<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.campaign</groupId>
		<artifactId>campaign</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<artifactId>campaign-external</artifactId>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<repositories>
		<repository>
			<id>jitpack.io</id>
			<url>https://jitpack.io</url>
		</repository>
	</repositories>

	<dependencies>
		<dependency>
			<groupId>com.campaign</groupId>
			<artifactId>campaign-dao</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.apache.commons/commons-lang3 -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.7</version><!--$NO-MVN-MAN-VER$ -->
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.apache.commons/commons-collections4 -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-collections4</artifactId>
			<version>4.1</version>
		</dependency>
		<!-- http://repo1.maven.org/maven2/com/googlecode/libphonenumber/libphonenumber/8.9.6/ -->
		<dependency>
			<groupId>com.googlecode.libphonenumber</groupId>
			<artifactId>libphonenumber</artifactId>
			<version>8.12.10</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-s3</artifactId>
			<version>1.11.969</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-core</artifactId>
			<version>1.11.969</version>
		</dependency>
		<dependency>
			<groupId>io.searchbox</groupId>
			<artifactId>jest</artifactId>
			<version>5.3.3</version>
		</dependency>
		<!-- <dependency> <groupId>org.elasticsearch</groupId> <artifactId>elasticsearch</artifactId> 
			<version>6.8.10</version> </dependency> -->
		<dependency>
			<groupId>org.elasticsearch.client</groupId>
			<artifactId>elasticsearch-rest-high-level-client</artifactId>
			<version>7.1.1</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.amazonaws/aws-java-sdk-sts -->
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-sts</artifactId>
			<version>1.11.969</version>
		</dependency>
		<dependency>
			<groupId>com.github.pieroxy</groupId>
			<artifactId>java-user-agent-detection</artifactId>
			<version>2017.09.24.17.00.GMT</version>
		</dependency>
		<dependency>
			<groupId>com.aerospike</groupId>
			<artifactId>spring-data-aerospike</artifactId>
			<version>1.0.2.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.aerospike</groupId>
			<artifactId>aerospike-client</artifactId>
			<version>4.4.1</version>
		</dependency>
		<dependency>
			<groupId>com.aerospike</groupId>
			<artifactId>aerospike-helper-java</artifactId>
			<version>1.2</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-secretsmanager</artifactId>
			<version>1.11.430</version>
		</dependency>
	</dependencies>
</project>
