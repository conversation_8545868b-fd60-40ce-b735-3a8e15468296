package com.birdeye.campaign.response.external;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CheckinIdDetails implements Serializable {
	
	/**
	 * 
	 */
	private static final long		serialVersionUID	= 4484233212152247886L;
	
	private Integer					checkinId;
	private Integer					customerId;
	private Integer					businessId;
	private Integer					accountId;
	private Integer					checkinSourceId;
	private String					checkinSource;
	private List<AssistedByDetails>	assistedBy;
	
	public static class AssistedByDetails implements Serializable {
		
		/**
		 * 
		 */
		private static final long	serialVersionUID	= -3262443230051027337L;
		
		private Integer				id;
		private String				name;
		private String				email;
		
		public Integer getId() {
			return id;
		}
		
		public void setId(Integer id) {
			this.id = id;
		}
		
		public String getName() {
			return name;
		}
		
		public void setName(String name) {
			this.name = name;
		}
		
		public String getEmail() {
			return email;
		}
		
		public void setEmail(String email) {
			this.email = email;
		}
		
		@Override
		public String toString() {
			ReflectionToStringBuilder sb = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
			return sb.toString();
		}
		
	}
	
	public Integer getCheckinId() {
		return checkinId;
	}
	
	public void setCheckinId(Integer checkinId) {
		this.checkinId = checkinId;
	}
	
	public Integer getCustomerId() {
		return customerId;
	}
	
	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}
	
	public Integer getBusinessId() {
		return businessId;
	}
	
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	public Integer getAccountId() {
		return accountId;
	}
	
	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}
	
	public Integer getCheckinSourceId() {
		return checkinSourceId;
	}
	
	public void setCheckinSourceId(Integer checkinSourceId) {
		this.checkinSourceId = checkinSourceId;
	}
	
	public String getCheckinSource() {
		return checkinSource;
	}
	
	public void setCheckinSource(String checkinSource) {
		this.checkinSource = checkinSource;
	}
	
	public List<AssistedByDetails> getAssistedBy() {
		return assistedBy;
	}
	
	public void setAssistedBy(List<AssistedByDetails> assistedBy) {
		this.assistedBy = assistedBy;
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder sb = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return sb.toString();
	}
	
}
