package com.birdeye.campaign.response.external;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_NULL)
public class BulkUserDetailsResponse implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1372801273889412618L;
	
	private List<UserDetails>	users;
	
	@JsonIgnoreProperties(ignoreUnknown = true)
	@JsonInclude(value = Include.NON_NULL)
	public static class UserDetails implements Serializable {
		
		/**
		 * 
		 */
		private static final long	serialVersionUID	= 1706228453093225231L;
		
		private Integer				id;
		
		private String				name;
		
		private String				emailId;
		
		private String				phone;
		
		private Boolean				enterpriseUser;
		
		private String				role;
		
		/**
		 * @return the id
		 */
		public Integer getId() {
			return id;
		}
		
		/**
		 * @param id
		 *            the id to set
		 */
		public void setId(Integer id) {
			this.id = id;
		}
		
		/**
		 * @return the name
		 */
		public String getName() {
			return name;
		}
		
		/**
		 * @param name
		 *            the name to set
		 */
		public void setName(String name) {
			this.name = name;
		}
		
		/**
		 * @return the emailId
		 */
		public String getEmailId() {
			return emailId;
		}
		
		/**
		 * @param emailId
		 *            the emailId to set
		 */
		public void setEmailId(String emailId) {
			this.emailId = emailId;
		}
		
		/**
		 * @return the phone
		 */
		public String getPhone() {
			return phone;
		}
		
		/**
		 * @param phone
		 *            the phone to set
		 */
		public void setPhone(String phone) {
			this.phone = phone;
		}
		
		/**
		 * @return the enterpriseUser
		 */
		public Boolean getEnterpriseUser() {
			return enterpriseUser;
		}
		
		/**
		 * @param enterpriseUser
		 *            the enterpriseUser to set
		 */
		public void setEnterpriseUser(Boolean enterpriseUser) {
			this.enterpriseUser = enterpriseUser;
		}
		
		/**
		 * @return the role
		 */
		public String getRole() {
			return role;
		}
		
		/**
		 * @param role
		 *            the role to set
		 */
		public void setRole(String role) {
			this.role = role;
		}
		
		@Override
		public String toString() {
			ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
			return b.toString();
		}
	}
	
	/**
	 * @return the users
	 */
	public List<UserDetails> getUsers() {
		return users;
	}
	
	/**
	 * @param users
	 *            the users to set
	 */
	public void setUsers(List<UserDetails> users) {
		this.users = users;
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return b.toString();
	}
}