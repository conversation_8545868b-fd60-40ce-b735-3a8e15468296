package com.birdeye.campaign.response.external;

import java.io.Serializable;

public class CustomerByEcidResponse implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1419383981306030358L;

	private Integer cid;

	public Integer getCid() {
		return cid;
	}

	public void setCid(Integer cid) {
		this.cid = cid;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("CustomerByECIDResponse [cid=");
		builder.append(cid);
		builder.append("]");
		return builder.toString();
	}

}
