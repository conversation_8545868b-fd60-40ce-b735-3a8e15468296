/**
 * @file_name CustomerExistsResponse.java
 * @created_date 13 Aug 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.response.external;

import java.io.Serializable;

/**
 * @file_name CustomerExistsResponse.java
 * @created_date 13 Aug 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public class CustomerExistsResponse implements Serializable {
	
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 239268291896146114L;

	private boolean customerPresent;

	/**
	 * @return the customerPresent
	 */
	public boolean isCustomerPresent() {
		return customerPresent;
	}

	/**
	 * @param customerPresent the customerPresent to set
	 */
	public void setCustomerPresent(boolean customerPresent) {
		this.customerPresent = customerPresent;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("CustomerExistsResponse [customerPresent=");
		builder.append(customerPresent);
		builder.append("]");
		return builder.toString();
	}

	/**
	 * @param customerPresent
	 */
	public CustomerExistsResponse(boolean customerPresent) {
		super();
		this.customerPresent = customerPresent;
	}

	/**
	 * 
	 */
	public CustomerExistsResponse() {
		super();
	}
	
	
	
	
}
