package com.birdeye.campaign.response.external;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AccountPirvacyInfoResponse implements Serializable {
	
	private static final long	serialVersionUID	= 4617934858199346398L;
	
	private Integer				businessId;
	
	private String				privacyUrl;
	
	private String				termsServiceUrl;
	
	public AccountPirvacyInfoResponse() {
		super();
	}
	
	/**
	 * @return the businessId
	 */
	public Integer getBusinessId() {
		return businessId;
	}
	
	/**
	 * @param businessId
	 *            the businessId to set
	 */
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	/**
	 * @return the privacyUrl
	 */
	public String getPrivacyUrl() {
		return privacyUrl;
	}
	
	/**
	 * @param privacyUrl
	 *            the privacyUrl to set
	 */
	public void setPrivacyUrl(String privacyUrl) {
		this.privacyUrl = privacyUrl;
	}
	
	/**
	 * @return the termsServiceUrl
	 */
	public String getTermsServiceUrl() {
		return termsServiceUrl;
	}
	
	/**
	 * @param termsServiceUrl
	 *            the termsServiceUrl to set
	 */
	public void setTermsServiceUrl(String termsServiceUrl) {
		this.termsServiceUrl = termsServiceUrl;
	}
}
