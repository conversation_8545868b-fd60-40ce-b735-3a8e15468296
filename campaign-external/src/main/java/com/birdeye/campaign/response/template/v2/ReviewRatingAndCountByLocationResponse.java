/**
 *
 */
package com.birdeye.campaign.response.template.v2;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * 
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_NULL)
public class ReviewRatingAndCountByLocationResponse {
	
	private Double									totalAvgRating	= 0.0;
	private Integer									totalCount		= 0;
	private List<ReviewRatingAndCountByLocation>	perLocationInfos;
	
	@JsonIgnoreProperties(ignoreUnknown = true)
	@JsonInclude(value = Include.NON_NULL)
	public static class ReviewRatingAndCountByLocation {
		
		private Integer	businessId;
		private Integer	count;
		private Double	avgRating;
		
		/**
		 * @return the businessId
		 */
		public Integer getBusinessId() {
			return businessId;
		}
		
		/**
		 * @param businessId
		 *            the businessId to set
		 */
		public void setBusinessId(Integer businessId) {
			this.businessId = businessId;
		}
		
		/**
		 * @return the count
		 */
		public Integer getCount() {
			return count;
		}
		
		/**
		 * @param count
		 *            the count to set
		 */
		public void setCount(Integer count) {
			this.count = count;
		}
		
		/**
		 * @return the avgRating
		 */
		public Double getAvgRating() {
			return avgRating;
		}
		
		/**
		 * @param avgRating
		 *            the avgRating to set
		 */
		public void setAvgRating(Double avgRating) {
			this.avgRating = avgRating;
		}
		
	}
	
	/**
	 * @return the totalAvgRating
	 */
	public Double getTotalAvgRating() {
		return totalAvgRating;
	}
	
	/**
	 * @param totalAvgRating
	 *            the totalAvgRating to set
	 */
	public void setTotalAvgRating(Double totalAvgRating) {
		this.totalAvgRating = totalAvgRating;
	}
	
	/**
	 * @return the totalCount
	 */
	public Integer getTotalCount() {
		return totalCount;
	}
	
	/**
	 * @param totalCount
	 *            the totalCount to set
	 */
	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}
	
	/**
	 * @return the perLocationInfos
	 */
	public List<ReviewRatingAndCountByLocation> getPerLocationInfos() {
		return perLocationInfos;
	}
	
	/**
	 * @param perLocationInfos
	 *            the perLocationInfos to set
	 */
	public void setPerLocationInfos(List<ReviewRatingAndCountByLocation> perLocationInfos) {
		this.perLocationInfos = perLocationInfos;
	}
	
}
