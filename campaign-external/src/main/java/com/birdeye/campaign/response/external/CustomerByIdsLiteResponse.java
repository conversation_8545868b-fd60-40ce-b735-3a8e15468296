package com.birdeye.campaign.response.external;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.utils.CoreUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerByIdsLiteResponse implements Serializable {
	
	private static final long	serialVersionUID	= -3912401111649056404L;
	
	private Integer				id;
	
	private String				displayName;
	
	private String				phone;
	
	private String				email;
	
	private String				locationName;
	
	private Integer				ecid;
	
	private Integer				businessId;
	
	private String				firstName;
	
	private String				lastName;
	
	private String				referralCode;
	
	private Integer				emailEngaged;
	
	public String getDisplayName() {
		return displayName;
	}
	
	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}
	
	public String getPhone() {
		return phone;
	}
	
	public void setPhone(String phone) {
		this.phone = phone;
	}
	
	public String getEmail() {
		return email;
	}
	
	public void setEmail(String email) {
		this.email = email;
	}
	
	public String getLocationName() {
		return locationName;
	}
	
	public void setLocationName(String locationName) {
		this.locationName = locationName;
	}
	
	public Integer getId() {
		return id;
	}
	
	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getEcid() {
		return ecid;
	}
	
	public void setEcid(Integer ecid) {
		this.ecid = ecid;
	}
	
	public Integer getBusinessId() {
		return businessId;
	}
	
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	public String getCustomerName() {
		return CoreUtils.getCustomerName(firstName, lastName, email, phone);
	}
	
	public String getFirstName() {
		return firstName;
	}
	
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}
	
	public String getLastName() {
		return lastName;
	}
	
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}
	
	public String getReferralCode() {
		return referralCode;
	}
	
	public void setReferralCode(String referralCode) {
		this.referralCode = referralCode;
	}
	
	public Integer getEmailEngaged() {
		return emailEngaged;
	}
	
	public void setEmailEngaged(Integer emailEngaged) {
		this.emailEngaged = emailEngaged;
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder sb = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return sb.toString();
	}
	
}
