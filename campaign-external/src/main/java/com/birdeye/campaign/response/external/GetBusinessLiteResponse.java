package com.birdeye.campaign.response.external;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonIgnoreProperties(ignoreUnknown = true)
public class GetBusinessLiteResponse implements Serializable {
	
	/**
	 * 
	 */
	private static final long		serialVersionUID	= -6600008340181842702L;
	
	private Integer					businessId;
	
	private Long					businessNumber;
	
	private Integer					enterpriseId;
	
	private Integer					resellerId;
	
	private String					businessName;
	
	private String					activationStatus;
	
	private Integer					locationId;
	
	private LocationLiteResponse	location;
	
	private String					timeZone;
	
	private String                  timeZoneId;
	
	private String					type;
	
	private String					accountType;
	
	private String					phone;
	
	// 0 for false 1 for true
	private Integer					serviceAreaProvider;
	
	private CustomData				customData;
	
	private String					websiteUrl;
	
	public Integer getBusinessId() {
		return businessId;
	}
	
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	public Long getBusinessNumber() {
		return businessNumber;
	}
	
	public void setBusinessNumber(Long businessNumber) {
		this.businessNumber = businessNumber;
	}
	
	public Integer getEnterpriseId() {
		return enterpriseId;
	}
	
	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	
	public Integer getResellerId() {
		return resellerId;
	}
	
	public void setResellerId(Integer resellerId) {
		this.resellerId = resellerId;
	}
	
	public String getBusinessName() {
		return businessName;
	}
	
	public void setBusinessName(String businessName) {
		this.businessName = businessName;
	}
	
	public String getActivationStatus() {
		return activationStatus;
	}
	
	public void setActivationStatus(String activationStatus) {
		this.activationStatus = activationStatus;
	}
	
	public Integer getLocationId() {
		return locationId;
	}
	
	public void setLocationId(Integer locationId) {
		this.locationId = locationId;
	}
	
	public LocationLiteResponse getLocation() {
		return location;
	}
	
	public void setLocation(LocationLiteResponse location) {
		this.location = location;
	}
	
	public String getTimeZone() {
		return timeZone;
	}
	
	public void setTimeZone(String timeZone) {
		this.timeZone = timeZone;
	}
	
	public String getTimeZoneId() {
		return timeZoneId;
	}

	public void setTimeZoneId(String timeZoneId) {
		this.timeZoneId = timeZoneId;
	}

	public String getType() {
		return type;
	}
	
	public void setType(String type) {
		this.type = type;
	}
	
	public String getAccountType() {
		return accountType;
	}
	
	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}
	
	public Integer getServiceAreaProvider() {
		return serviceAreaProvider;
	}
	
	public void setServiceAreaProvider(Integer serviceAreaProvider) {
		this.serviceAreaProvider = serviceAreaProvider;
	}
	
	public String getPhone() {
		return phone;
	}
	
	public void setPhone(String phone) {
		this.phone = phone;
	}
	
	public CustomData getCustomData() {
		return customData;
	}
	
	public void setCustomData(CustomData customData) {
		this.customData = customData;
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public class LocationLiteResponse implements Serializable {
		
		/**
		 * 
		 */
		private static final long	serialVersionUID	= -2971551421198998574L;
		
		private Integer				id;
		private String				address1;
		private String				address2;
		private String				city;
		private String				state;
		private String				zip;
		private String				countryName;
		private String				countryCode;
		private String				latitude;
		private String				longitude;
		
		public Integer getId() {
			return id;
		}
		
		public void setId(Integer id) {
			this.id = id;
		}
		
		public String getAddress1() {
			return address1;
		}
		
		public void setAddress1(String address1) {
			this.address1 = address1;
		}
		
		public String getAddress2() {
			return address2;
		}
		
		public void setAddress2(String address2) {
			this.address2 = address2;
		}
		
		public String getCity() {
			return city;
		}
		
		public void setCity(String city) {
			this.city = city;
		}
		
		public String getState() {
			return state;
		}
		
		public void setState(String state) {
			this.state = state;
		}
		
		public String getZip() {
			return zip;
		}
		
		public void setZip(String zip) {
			this.zip = zip;
		}
		
		public String getCountryName() {
			return countryName;
		}
		
		public void setCountryName(String countryName) {
			this.countryName = countryName;
		}
		
		public String getCountryCode() {
			return countryCode;
		}
		
		public void setCountryCode(String countryCode) {
			this.countryCode = countryCode;
		}
		
		public String getLatitude() {
			return latitude;
		}
		
		public void setLatitude(String latitude) {
			this.latitude = latitude;
		}
		
		public String getLongitude() {
			return longitude;
		}
		
		public void setLongitude(String longitude) {
			this.longitude = longitude;
		}
		
		@Override
		public String toString() {
			ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
			return b.toString();
		}
	}
	
	public static class CustomData implements Serializable {
		
		/**
		 * 
		 */
		private static final long	serialVersionUID	= 8195878327162716966L;
		
		private String				groupType;									// need field for appointment
		private String				accountSpecialist;							// need field for appointment for specialist ex. doctor
		private String				accountCustomer;							// need field for appointment for customer ex. patient
		
		public String getGroupType() {
			return groupType;
		}
		
		public void setGroupType(String groupType) {
			this.groupType = groupType;
		}
		
		public String getAccountSpecialist() {
			return accountSpecialist;
		}
		
		public void setAccountSpecialist(String accountSpecialist) {
			this.accountSpecialist = accountSpecialist;
		}
		
		public String getAccountCustomer() {
			return accountCustomer;
		}
		
		public void setAccountCustomer(String accountCustomer) {
			this.accountCustomer = accountCustomer;
		}
		
		@Override
		public String toString() {
			ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
			return b.toString();
			
		}
	}
	
	/**
	 * @return the websiteUrl
	 */
	public String getWebsiteUrl() {
		return websiteUrl;
	}

	/**
	 * @param websiteUrl the websiteUrl to set
	 */
	public void setWebsiteUrl(String websiteUrl) {
		this.websiteUrl = websiteUrl;
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return b.toString();
	}

}
