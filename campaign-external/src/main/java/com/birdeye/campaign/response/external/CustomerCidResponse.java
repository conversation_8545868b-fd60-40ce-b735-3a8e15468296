package com.birdeye.campaign.response.external;

import java.io.Serializable;
import java.util.List;

public class CustomerCidResponse implements Serializable {

	private static final long serialVersionUID = 733464969139446241L;

	private List<Integer> cid;

	private Integer totalCount;

	private String scrollId;

	public List<Integer> getCid() {
		return cid;
	}

	public void setCid(List<Integer> cid) {
		this.cid = cid;
	}

	public Integer getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}

	public String getScrollId() {
		return scrollId;
	}

	public void setScrollId(String scrollId) {
		this.scrollId = scrollId;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("CustomerCidResponse [cid=");
		builder.append(cid);
		builder.append(", totalCount=");
		builder.append(totalCount);
		builder.append(", scrollId=");
		builder.append(scrollId);
		builder.append("]");
		return builder.toString();
	}

}
