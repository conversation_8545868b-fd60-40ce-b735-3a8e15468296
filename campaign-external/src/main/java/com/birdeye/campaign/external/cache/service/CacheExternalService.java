package com.birdeye.campaign.external.cache.service;

import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.response.external.QRConfigSlugResponse;
import com.birdeye.campaign.response.kontacto.KontactoDTO;

public interface CacheExternalService {
	
	KontactoDTO getRRCustomerCached(Long requestId, Integer customerId);
	
	BusinessEnterpriseEntity getBusinessById(Integer businessId);

	QRConfigSlugResponse getQRCofigDetailsFromSlug(String qrCodeSlug, String businessNumber);

	KontactoDTO getRRCustomerCachedWithReviewSource(Long requestId, Integer customerId);
	
}
