package com.birdeye.campaign.external.service.impl;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;

import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.external.service.SocialExternalService;
import com.birdeye.campaign.response.external.SocialBusinessPageInfo;
import com.birdeye.campaign.response.external.SocialPlaceIdResponse;

@Service("SocialExternalService")
public class SocialExternalServiceImpl implements SocialExternalService {

	@Autowired
	@Qualifier("campaignRestTemplate")
	private RestTemplate		restTemplate;
	
	@Autowired
	private Environment			environment;
	
	private static final Logger	LOG	= LoggerFactory.getLogger(SocialExternalServiceImpl.class);
	
	@Override
	public List<SocialPlaceIdResponse> getPlaceIdForGoogle(List<Integer> businessIds) {
		String socialServiceBaseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("social-service-url", environment.getProperty("social.service.endpoint"));
		String googlePlaceURL = StringUtils.join(socialServiceBaseUrl, "bam/businessAggregation/place-id/get");
		HttpHeaders headers = getDefaultHttpHeaders();
		LOG.info("Calling social service endpoint to get place id from url : {}", googlePlaceURL);
		ResponseEntity<SocialPlaceIdResponse[]> response = null;
	try {
		response = restTemplate.exchange(googlePlaceURL, HttpMethod.POST, new HttpEntity<>(businessIds, headers), SocialPlaceIdResponse[].class);
		if (response != null && response.hasBody()) {
			SocialPlaceIdResponse [] socialPlaceIdResponse=response.getBody();
			return Arrays.stream(socialPlaceIdResponse).collect(Collectors.toList());
			 
		}
	} catch (RestClientResponseException e) {
		LOG.error("RestClientResponseException while getting survey data for request {} is {}", googlePlaceURL, ExceptionUtils.getStackTrace(e));
	} catch (Exception e) {
		LOG.error("Exception while getting survey data for request {} is {}",googlePlaceURL, ExceptionUtils.getStackTrace(e));
	}
		return null;
	}
	private HttpHeaders getDefaultHttpHeaders() {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		return headers;
	}
	
	@Override
	public SocialBusinessPageInfo getBusinessPageDetails(String channel, Integer businessId) {
		String socialServiceBaseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("social-service-url",
				environment.getProperty("social.service.endpoint"));
		String url = StringUtils.join(socialServiceBaseUrl, "/social/page/filters?channel=", channel, "&businessId=", businessId);
		ResponseEntity<SocialBusinessPageInfo> response = null;
		try {
			response = restTemplate.exchange(url, HttpMethod.GET, null, SocialBusinessPageInfo.class);
			if (response.getStatusCode() == HttpStatus.OK) {
				return response.getBody();
			}
		} catch (RestClientResponseException e) {
			LOG.error("RestClientResponseException while fetching social business page data by businessId : {}, Error : {}", businessId, ExceptionUtils.getStackTrace(e));
		} catch (Exception e) {
			LOG.error("Exception while fetching social business page data by businessId {}, Error : {}", businessId, ExceptionUtils.getStackTrace(e));
		}
		return null;
	}
	
}

//http://localhost:8080/bam/businessAggregation/sourceurl-profileid/<short_business_id>?sourceId=2