package com.birdeye.campaign.external.utils;

import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

import com.birdeye.campaign.dto.BusinessProfileResponse;
import com.birdeye.campaign.response.LocationCustomField;

public class BusinessExternalUtils {
	
	private BusinessExternalUtils() {
		
	}
	
	public static Map<String, String> getCustomFieldsMap(BusinessProfileResponse businessProfile) {
		if (businessProfile == null || CollectionUtils.isEmpty(businessProfile.getCustomFields())) {
			return Collections.emptyMap();
		}
		return businessProfile.getCustomFields().stream()
				.collect(Collectors.toMap(LocationCustomField::getFieldName, kdto -> kdto.getFieldValue() == null ? "" : kdto.getFieldValue(), (first, second) -> second));
	}
	
	public static Map<String, LocationCustomField> getNameToCustomFieldMap(BusinessProfileResponse businessProfile) {
		if (businessProfile == null || CollectionUtils.isEmpty(businessProfile.getCustomFields())) {
			return Collections.emptyMap();
		}
		return businessProfile.getCustomFields().stream().collect(Collectors.toMap(LocationCustomField::getFieldName, kdto -> kdto, (first, second) -> second));
	}

}
