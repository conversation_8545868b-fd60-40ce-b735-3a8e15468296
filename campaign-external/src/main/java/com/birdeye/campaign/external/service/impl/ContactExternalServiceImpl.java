package com.birdeye.campaign.external.service.impl;

import java.text.MessageFormat;
import java.time.Duration;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import com.birdeye.campaign.aspect.annotation.Profiled;
import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.dto.CreateLocationCustomerRequest;
import com.birdeye.campaign.dto.CustomerCustomFieldRequest;
import com.birdeye.campaign.exception.CampaignExternalServiceException;
import com.birdeye.campaign.external.service.IContactExternalService;
import com.birdeye.campaign.request.CampaignAudienceFilter;
import com.birdeye.campaign.request.external.CustomerExistsRequest;
import com.birdeye.campaign.request.external.LeadCreationRequest;
import com.birdeye.campaign.request.kontacto.ContactGetOrCreateRequest;
import com.birdeye.campaign.response.CreateCustomerCheckinResponse;
import com.birdeye.campaign.response.CustomerCustomFieldResponse;
import com.birdeye.campaign.response.external.CheckinIdDetails;
import com.birdeye.campaign.response.external.CustomerByEcidResponse;
import com.birdeye.campaign.response.external.CustomerByIdsLiteResponse;
import com.birdeye.campaign.response.external.CustomerCidResponse;
import com.birdeye.campaign.response.external.CustomerExistsResponse;
import com.birdeye.campaign.response.external.CustomerInfoResponse;
import com.birdeye.campaign.response.external.LeadCreationResponse;
import com.birdeye.campaign.response.kontacto.KontactoDTO;

@Service("contactExternalService")
public class ContactExternalServiceImpl implements IContactExternalService {
	
	private static final String	CONTACT_SERVICE_BASE_URL	= "contact_api_server_base_url";
	
	private static final String	CONTACT_API_ENDPOINT		= "contact.api.endpoint";
	
	private static final String	X_ENTERPRISE_ID				= "x-enterprise-id";
	
	private static final Logger	LOG							= LoggerFactory.getLogger(ContactExternalServiceImpl.class);
	
	@Autowired
	private Environment			environment;
	
	@Autowired
	@Qualifier("campaignRestTemplate")
	private RestTemplate		restTemplate;
	
	@Autowired
	@Qualifier("kontactoRestTemplate")
	private RestTemplate		kontactoRestTemplate;
	
	@Autowired
	@Qualifier("campaignWebClient")
	private WebClient			campaignWebClient;
	
	/**
	 * Return customer data without additional parameters like customField, referralCode, tags.
	 */
	@Override
	public KontactoDTO getCustomerById(Integer customerId) {
		if (customerId == null || customerId.equals(0)) {
			LOG.warn("Customer id can not be null or 0");
			return null;
		}
		return getCustomerDataById(customerId, false, false, false, false);
	}
	
	@Override
	public KontactoDTO getCustomerByIdWithCustomFieldAndReferralCodeAndTags(Integer customerId, boolean customField, boolean referralCode, boolean tags) {
		if (customerId == null || customerId.equals(0)) {
			LOG.warn("Customer id can not be null or 0.");
			return null;
		}
		return getCustomerDataById(customerId, customField, referralCode, tags, false);
	}
	
	@Override
	public KontactoDTO getCustomerByIdWithCustomFieldAndReferralCodeAndTagsAndReviewSource(Integer customerId, boolean customField, boolean referralCode, boolean tags, boolean reviewSource) {
		if (customerId == null || customerId.equals(0)) {
			LOG.warn("Customer id can not be null or 0.");
			return null;
		}
		return getCustomerDataById(customerId, customField, referralCode, tags, reviewSource);
	}
	
	@Override
	@Cacheable(key = "#customerId", value = "customerWithCustomFieldReferralCode", unless = "#result == null")
	public KontactoDTO getCustomerByIdWithCustomFieldAndReferralCodeCached(Integer customerId) {
		return getCustomerByIdWithCustomFieldAndReferralCodeAndTags(customerId, true, true, false);
	}
	
	/**
	 * 
	 * Get Customer By ID
	 * 
	 * 1. Kontacto API /customer/get/
	 * 2. On error - Fallback Enabled : true -> DB hit | Fallback Enabled : true -> Do Nothing (Proactive)
	 * 
	 * 3. GLOBAL SWITCH - to turn off kontacto version (Reactive)
	 * 
	 * Retry - https://www.baeldung.com/spring-webflux-retry
	 *  
	 * @param customerId
	 * @return
	 */
	private KontactoDTO getCustomerDataById(Integer customerId, boolean customField, boolean referralCode, boolean tags, boolean reviewSource) {
		if (customerId == null || customerId.equals(0)) {
			LOG.warn("Customer id can not be null or 0.");
			return null;
		}
//		LOG.info("Calling Kontacto service to get Customer data by Id : {}", customerId);
		String url = StringUtils.join(getContactServiceBaseURL(), GET_CUSTOMER_BYID, customerId, "?customField=", customField, "&isReferralCodeRequired=", referralCode, "&tagsRequired=", tags,
				"&reviewSource=", reviewSource);
//		long requestLevelTimeout = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("kontacto.getCustomerById.timeout", 10000).longValue();
//		Integer maxRetryAttempts = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("kontacto.getCustomerById.maxRetryAttempts", 3);
//		Integer fixedDelay = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("kontacto.getCustomerById.retry.fixedDelayInSeconds", 2);
		HttpHeaders headers = getDefaultHttpHeaders();
		ResponseEntity<KontactoDTO> response = null;
		try {
			response = kontactoRestTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(headers), KontactoDTO.class);
			if (response.getStatusCode() == HttpStatus.OK && response.hasBody()) {
				return response.getBody();
			}
//			dto = campaignWebClient.get().uri(url)
//					.retrieve().bodyToMono(KontactoDTO.class)
//					// currently retrying for all kind of exceptions. Can be changed later to retry for specific exceptions.
//					.retryWhen(Retry.fixedDelay(maxRetryAttempts, Duration.ofSeconds(fixedDelay)) /* .filter(this::isRetryableError) */)
//					.timeout(Duration.ofMillis(requestLevelTimeout))
//					.block();
		} catch (RestClientResponseException e) {
			LOG.error("RestClientResponseException while calling Kontacto getCustomerById for customer id {} :: {}", customerId, e.getLocalizedMessage());
		} catch (Exception e) {
			LOG.error("Exception while calling Kontacto getCustomerById for customer id {} :: {}", customerId, ExceptionUtils.getStackTrace(e));
		}
//		} catch (WebClientResponseException e) {
//			LOG.error("WebClientResponseException while calling Kontacto getCustomerById for customer id {} :: {}", customerId, ExceptionUtils.getStackTrace(e));
//		} catch (Exception e) {
//			LOG.error("Exception while calling Kontacto getCustomerById for customer id {} :: {}", customerId, ExceptionUtils.getStackTrace(e));
//		}
		return null;
	}
	
	private boolean isRetryableError(Throwable throwable) {
		LOG.error("Error received from Contact service to get customer data by id. {}", throwable.getLocalizedMessage());
		return true;
//		return (throwable instanceof WebClientResponseException && ((WebClientResponseException) throwable).getStatusCode().is5xxServerError()) || (throwable instanceof TimeoutException)
//				|| (throwable instanceof SSLException) || (throwable instanceof IOException) || (throwable instanceof IllegalStateException);
	}
	
	@Override
	@Profiled
	public List<CustomerInfoResponse> getCustomersInfo(List<Integer> customerIds) {
		try {
			return getCustomerInfoList(customerIds, false, false);
		} catch (Exception e) {
			LOG.info("Throwing Exception occurred while calling KONTAKTO's batch api to get customers info.", e);
			throw new CampaignExternalServiceException(ErrorCodes.EXTERNAL_SERVICE_UAVAILABLE, "No response from Kontacto Service.");
		}
	}
	
	@Override
	@Profiled
	public List<CustomerInfoResponse> getCustomersInfoWithCustomFieldsAndReferralCode(List<Integer> customerIds, boolean isCustomField, boolean isReferralCodeRequired) {
		try {
			return getCustomerInfoList(customerIds, isCustomField, isReferralCodeRequired);
		} catch (Exception e) {
			LOG.info("Consuming Exception occurred while calling KONTAKTO's batch api to get customers info.", e);
		}
		return Collections.emptyList();
	}
	
	private List<CustomerInfoResponse> getCustomerInfoList(List<Integer> customerIds, boolean isCustomField, boolean isReferralCodeRequired) {
		String contactServiceBaseUrl = getContactServiceBaseURL();
		String apiUrl = StringUtils.join(contactServiceBaseUrl, "customer/getByIds?customField=", isCustomField, "&isReferralCodeRequired=", isReferralCodeRequired);
		HttpHeaders headers = getDefaultHttpHeaders();
		ResponseEntity<List<CustomerInfoResponse>> response = null;
		try {
			LOG.info("calling api to get list of customer info for customer size : {} with url {}", customerIds.size(), apiUrl);
			response = restTemplate.exchange(apiUrl, HttpMethod.POST, new HttpEntity<>(customerIds, headers), new ParameterizedTypeReference<List<CustomerInfoResponse>>() {
			});
			if (response.getStatusCode() == HttpStatus.OK) {
				return response.getBody();
			}
		} catch (RestClientResponseException e) {
			LOG.error("RestClientResponseException while calling api to get customer info from KONTAKTO for request url{} is {}", apiUrl, e.getResponseBodyAsString());
			throw new CampaignExternalServiceException(ErrorCodes.EXTERNAL_SERVICE_UAVAILABLE, MessageFormat.format(ErrorCodes.EXTERNAL_SERVICE_UAVAILABLE.getMessage(), apiUrl));
		} catch (Exception e) {
			LOG.error("Exception while calling api to get customer info from KONTAKTO for request {} is {}", apiUrl, e);
			throw new CampaignExternalServiceException(ErrorCodes.EXTERNAL_SERVICE_UAVAILABLE, MessageFormat.format(ErrorCodes.EXTERNAL_SERVICE_UAVAILABLE.getMessage(), apiUrl));
		}
		return Collections.emptyList();
	}
	
	/**
	 * Customer batch info Lite API.
	 */
	@Override
	@Profiled
	public List<CustomerByIdsLiteResponse> getCustomersInfoInBatchLite(List<Integer> customerIds, boolean locationName, boolean referralCode) {
		String apiUrl = StringUtils.join(getContactServiceBaseURL(), "customer/getByIds/lite?locationName=", true, "&referralCode=", true);
		//String apiUrl = StringUtils.join(getContactServiceBaseURL(), "customer/getByIds?locationName=", locationName, "&referralCode=", referralCode);
		
		Map<String, List<Integer>> requestMap = new HashMap<>();
		requestMap.put("ids", customerIds);
		
		ResponseEntity<List<CustomerByIdsLiteResponse>> response = null;
		try {
			LOG.info("calling customers batch lite api for customer size : {} with url {}", customerIds.size(), apiUrl);
			response = restTemplate.exchange(apiUrl, HttpMethod.POST, new HttpEntity<>(requestMap, getDefaultHttpHeaders()), new ParameterizedTypeReference<List<CustomerByIdsLiteResponse>>() {
			});
			if (response.getStatusCode() == HttpStatus.OK) {
				return response.getBody();
			}
		} catch (RestClientResponseException e) {
			LOG.error("RestClientResponseException while calling customers batch lite api for request url {} is {}", apiUrl, ExceptionUtils.getStackTrace(e));
			//throw new CampaignExternalServiceException(ErrorCodes.EXTERNAL_SERVICE_UAVAILABLE, MessageFormat.format(ErrorCodes.EXTERNAL_SERVICE_UAVAILABLE.getMessage(), apiUrl));
		} catch (Exception e) {
			LOG.error("Exception while calling customers batch lite api for request {} is {}", apiUrl, ExceptionUtils.getStackTrace(e));
			//throw new CampaignExternalServiceException(ErrorCodes.EXTERNAL_SERVICE_UAVAILABLE, MessageFormat.format(ErrorCodes.EXTERNAL_SERVICE_UAVAILABLE.getMessage(), apiUrl));
		}
		return Collections.emptyList();
	}
	
	@Override
	public CustomerExistsResponse isCustomerPresent(Integer enterpriseId, String email, String phone) {
		String apiUrl = StringUtils.join(getContactServiceBaseURL(), "customer/checkCustomerExists");
		HttpHeaders headers = getDefaultHttpHeaders();
		headers.add(X_ENTERPRISE_ID, String.valueOf(enterpriseId));
		CustomerExistsRequest request = new CustomerExistsRequest(email, phone);
		ResponseEntity<CustomerExistsResponse> response = null;
		try {
			LOG.info("Calling contact service API to isCustomerPresent for business id  {} email {} phone {}", enterpriseId, email, phone);
			response = restTemplate.exchange(apiUrl, HttpMethod.POST, new HttpEntity<>(request, headers), CustomerExistsResponse.class);
			if (response.getStatusCode() == HttpStatus.OK) {
				return response.getBody();
			}
		} catch (RestClientResponseException e) {
			LOG.error("RestClientResponseException while calling contact service API {} and request {} is {}", apiUrl, request, e);
		} catch (Exception e) {
			LOG.error("Exception while calling contact service API {} and request {} is {}", apiUrl, request, e);
		}
		// Fallback in case of error, bypass
		return new CustomerExistsResponse(false);
	}
	
	/**
	 * @return
	 */
	private String getContactServiceBaseURL() {
		return CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(CONTACT_SERVICE_BASE_URL, environment.getProperty(CONTACT_API_ENDPOINT));
	}
	
	private static HttpHeaders getDefaultHttpHeaders() {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		return headers;
	}
	
	@Profiled
	@Override
	public CreateCustomerCheckinResponse createOrGetCustomerWithCheckin(CreateLocationCustomerRequest request) {
		String contactServiceBaseUrl = getContactServiceBaseURL();
		String apiUrl = StringUtils.join(getContactServiceBaseURL(), "checkin/sync?businessNumber=", request.getBusinessNumber());
		HttpHeaders headers = getDefaultHttpHeaders();
		headers.add(X_ENTERPRISE_ID, String.valueOf(request.getEnterpriseId()));
		ResponseEntity<CreateCustomerCheckinResponse> response = null;
		try {
			LOG.info("calling api to create customer/checkin info for businessId: {} with url {}", request.getBusinessId(), apiUrl);
			response = restTemplate.exchange(apiUrl, HttpMethod.POST, new HttpEntity<>(request, headers), CreateCustomerCheckinResponse.class);
			if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.ACCEPTED) {
				return response.getBody();
			}
		} catch (RestClientResponseException e) {
			LOG.error("RestClientResponseException while calling api to create customer/checkin from KONTAKTO for request url{} is {}", apiUrl, ExceptionUtils.getStackTrace(e));
			// handle duplicate customer
			if (e.getResponseBodyAsString().contains("1022")) {
				throw new CampaignExternalServiceException(ErrorCodes.DUPLICATE_CUSTOMER_CREATE_ERROR, ErrorCodes.DUPLICATE_CUSTOMER_CREATE_ERROR.getMessage());
			}
			handleErrorReponseForExternalService(e.getResponseBodyAsString(), contactServiceBaseUrl, apiUrl);
		} catch (ResourceAccessException e) {
			LOG.error("ResourceAccessException while calling api to create customer/checkin from KONTAKTO for request {} is {}", apiUrl, ExceptionUtils.getStackTrace(e));
			throw new CampaignExternalServiceException(ErrorCodes.EXTERNAL_SERVICE_CONNECTION_REFUSED,
					MessageFormat.format(ErrorCodes.EXTERNAL_SERVICE_CONNECTION_REFUSED.getMessage(), contactServiceBaseUrl));
		} catch (Exception e) {
			LOG.error("Exception while calling api to create customer/checkin from KONTAKTO for request {} is {}", apiUrl, ExceptionUtils.getStackTrace(e));
		}
		return null;
	}
	
	@Override
	public LeadCreationResponse createLead(Integer enterpriseOrSMBId, LeadCreationRequest request) {
		String apiUrl = StringUtils.join(getContactServiceBaseURL(), CREATE_CUSTOMER);
		HttpHeaders headers = getDefaultHttpHeaders();
		headers.add(X_ENTERPRISE_ID, String.valueOf(enterpriseOrSMBId));
		ResponseEntity<LeadCreationResponse> response = null;
		try {
			LOG.info("calling api to create lead for enterprise id : {} with url {} and request {}", enterpriseOrSMBId, apiUrl, request);
			response = restTemplate.exchange(apiUrl, HttpMethod.POST, new HttpEntity<>(request, headers), LeadCreationResponse.class);
			if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.ACCEPTED) {
				return response.getBody();
			}
		} catch (RestClientResponseException e) {
			LOG.error("RestClientResponseException while calling api to create lead from KONTAKTO for request {} is {}", request, ExceptionUtils.getStackTrace(e));
			return null;
		} catch (ResourceAccessException e) {
			LOG.error("ResourceAccessException while calling api to create lead from KONTAKTO for request {} is {}", request, ExceptionUtils.getStackTrace(e));
			return null;
		} catch (Exception e) {
			LOG.error("Exception while calling api to create lead from KONTAKTO for request {} is {}", request, ExceptionUtils.getStackTrace(e));
			return null;
		}
		return null;
	}
	
	private void handleErrorReponseForExternalService(String errorResponse, String baseUrl, String resourceUrl) {
		if (errorResponse.contains("404")) {
			throw new CampaignExternalServiceException(ErrorCodes.EXTERNAL_SERVICE_URL_NOT_FOUND, MessageFormat.format(ErrorCodes.EXTERNAL_SERVICE_URL_NOT_FOUND.getMessage(), resourceUrl));
		}
		if (errorResponse.contains("500") || errorResponse.contains("503")) {
			throw new CampaignExternalServiceException(ErrorCodes.EXTERNAL_SERVICE_UAVAILABLE, MessageFormat.format(ErrorCodes.EXTERNAL_SERVICE_UAVAILABLE.getMessage(), baseUrl));
		}
	}
	
	@Override
	@Cacheable(key = "#customerId", value = "getCustomerByIdCache", unless = "#result == null")
	public KontactoDTO getCustomerByIdCached(Integer customerId) {
		return getCustomerById(customerId);
	}
	
	/**
	 * API to return customer basic info if present, else create and return customer info. No Updates.
	 * This API may not return all fields of KontactoDTO. Below mentioned response will come for sure if present. [17-10-2020]
	 * private Integer id;
	 * private Integer ecid;
	 * private Integer businessId;
	 * private String firstName;
	 * private String lastName;
	 * private String emailId;
	 * private String phone;
	 * private String subscriptionStatus;
	 * 
	 * @param request
	 * @param accountId
	 * @return
	 */
	@Override
	public KontactoDTO getOrCreateCustomer(ContactGetOrCreateRequest request, Integer accountId) {
		String url = StringUtils.join(getContactServiceBaseURL(), GET_OR_CREATE_CUSTOMER);
		
		KontactoDTO dto = null;
		try {
			dto = campaignWebClient.post().uri(url)
					.header(X_ENTERPRISE_ID, String.valueOf(accountId))
					.body(BodyInserters.fromValue(request))
					.retrieve().bodyToMono(KontactoDTO.class)
					.timeout(Duration.ofMillis(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("kontacto.getOrCreateCustomer.timeout", 10000).longValue()))
					.block();
		} catch (WebClientResponseException e) {
			LOG.error("WebClientResponseException while calling Kontacto getOrCreateCustomer for :: {} :: {}", request, ExceptionUtils.getStackTrace(e));
			// handle customer mapped to different location.
			/**
			 * if (StringUtils.isNotBlank(e.getResponseBodyAsString()) &&
			 * e.getResponseBodyAsString().contains(String.valueOf(ErrorCodes.CUSTOMER_ALREADY_MAPPED.getValue()))) {
			 * throw new CampaignExternalServiceException(ErrorCodes.CUSTOMER_ALREADY_MAPPED, ErrorCodes.CUSTOMER_ALREADY_MAPPED.getMessage());
			 * }
			 **/
		} catch (Exception e) {
			LOG.error("Exception while calling Kontacto getOrCreateCustomer for :: {} :: {}", request, ExceptionUtils.getStackTrace(e));
		}
		return dto;
	}
	
	@Override
	public CustomerByEcidResponse getCustomerIdByLocationAndEcid(Integer businessId, Integer ecid) {
		String apiUrl = StringUtils.join(getContactServiceBaseURL(), "customer/map/", ecid, "/", businessId);
		HttpHeaders headers = getDefaultHttpHeaders();
		ResponseEntity<CustomerByEcidResponse> response = null;
		try {
			LOG.info("Calling contact service API to getCustomerIdByLocationAndEcid for business id  {} and ecid  {}", businessId, ecid);
			response = restTemplate.exchange(apiUrl, HttpMethod.GET, new HttpEntity<>(headers), CustomerByEcidResponse.class);
			if (response.getStatusCode() == HttpStatus.OK) {
				return response.getBody();
			}
		} catch (RestClientResponseException e) {
			LOG.error("RestClientResponseException while calling contact service API {} is {}", apiUrl, e);
		} catch (Exception e) {
			LOG.error("Exception while calling contact service API {} is {}", apiUrl, e);
		}
		// Fallback in case of error, bypass
		return null;
	}
	
	@Override
	public CustomerCidResponse getCidsFromKontacto(CampaignAudienceFilter campaignAudienceFilter, Integer page, Integer size, String sortby, String order) {
		StringBuilder apiUrl = new StringBuilder();
		apiUrl.append(getContactServiceBaseURL()).append("customer/get/cids").append("?").append("page=").append(page).append("&size=").append(size).append("&sortBy=").append(sortby).append("&order=")
				.append(order);
		HttpHeaders headers = getDefaultHttpHeaders();
		ResponseEntity<CustomerCidResponse> response = null;
		try {
			LOG.info("Calling contact service Pagination API {} to get Cids with filter {}", apiUrl, campaignAudienceFilter);
			response = restTemplate.exchange(apiUrl.toString(), HttpMethod.POST, new HttpEntity<>(campaignAudienceFilter, headers), CustomerCidResponse.class);
			if (response.getStatusCode() == HttpStatus.OK) {
				return response.getBody();
			}
		} catch (RestClientResponseException e) {
			LOG.error("RestClientResponseException while calling contact service API {} and request {} is {}", apiUrl, campaignAudienceFilter, e);
		} catch (Exception e) {
			LOG.error("Exception while calling contact service API {} and request {} is {}", apiUrl, campaignAudienceFilter, e);
		}
		return null;
	}

	@Profiled
	@Override
	public CustomerCustomFieldResponse getCustomerCustomFields(Integer enterpriseId) {
		String contactServiceBaseUrl = getContactServiceBaseURL();
		String apiUrl = StringUtils.join(contactServiceBaseUrl, "customField/");
		HttpHeaders headers = getDefaultHttpHeaders();
		headers.add(X_ENTERPRISE_ID, String.valueOf(enterpriseId));
		ResponseEntity<CustomerCustomFieldResponse> response = null;
		try {
			LOG.info("calling api to create customer/customField info for enterpriseId: {} with url {}", enterpriseId, apiUrl);
			response = restTemplate.exchange(apiUrl, HttpMethod.POST, new HttpEntity<>(new CustomerCustomFieldRequest(), headers), CustomerCustomFieldResponse.class);
			if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.ACCEPTED) {
				return response.getBody();
			}
		} catch (RestClientResponseException e) {
			LOG.error("RestClientResponseException while calling api to create customer/customField from KONTAKTO for request url{} is {}", apiUrl, ExceptionUtils.getStackTrace(e));
			// handle duplicate customer
			if (e.getResponseBodyAsString() != null) {
				handleErrorReponseForExternalService(e.getResponseBodyAsString(), contactServiceBaseUrl, apiUrl);
			}
		} catch (ResourceAccessException e) {
			LOG.error("ResourceAccessException while calling api to create customer/customField from KONTAKTO for request {} is {}", apiUrl, ExceptionUtils.getStackTrace(e));
			throw new CampaignExternalServiceException(ErrorCodes.EXTERNAL_SERVICE_CONNECTION_REFUSED,
					MessageFormat.format(ErrorCodes.EXTERNAL_SERVICE_CONNECTION_REFUSED.getMessage(), contactServiceBaseUrl));
		} catch (Exception e) {
			LOG.error("Exception while calling api to create customer/customField from KONTAKTO for request {} is {}", apiUrl, ExceptionUtils.getStackTrace(e));
		}
		return null;
	}
	
	@Override
	public CustomerCustomFieldResponse getCustomerCustomFieldsData(Integer enterpriseId) {
		CustomerCustomFieldResponse customFieldResponse = getCustomerCustomFields(enterpriseId);
		if (customFieldResponse == null || customFieldResponse.getCount() == 0 || CollectionUtils.isEmpty(customFieldResponse.getCustomFields())) {
			LOG.error("For Enterprise {} No data found for custom fields in Kontacto", enterpriseId);
			return null;
		}
		return customFieldResponse;
	}
	
	// curl -X POST "http://dev-paid-kontacto-api-2.birdeye.internal:8080/checkin/details?assistedBy=true&extraParams=true" -H "accept: */*" -H
	// "Content-Type: application/json" -d "[ 721570,721569,721567]"
	@Override
	@Profiled
	public List<CheckinIdDetails> getCustomersCheckInDetails(List<Integer> checkinIds, boolean assistedBy, boolean extraParams) {
		String apiUrl = StringUtils.join(getContactServiceBaseURL(), "checkin/details?assistedBy=", assistedBy, "&extraParams=", extraParams);
		
		ResponseEntity<List<CheckinIdDetails>> response = null;
		try {
//			LOG.info("calling customers check in details api for customer size : {} with url {}", checkinIds.size(), apiUrl);
			response = restTemplate.exchange(apiUrl, HttpMethod.POST, new HttpEntity<>(checkinIds, getDefaultHttpHeaders()), new ParameterizedTypeReference<List<CheckinIdDetails>>() {
			});
			if (response.getStatusCode() == HttpStatus.OK) {
				return response.getBody();
			}
		} catch (RestClientResponseException e) {
			LOG.error("RestClientResponseException while calling ustomers check in details api for request url {} is {}", apiUrl, e.getLocalizedMessage());
		} catch (Exception e) {
			LOG.error("Exception while calling customers check in details api for request {} is {}", apiUrl, ExceptionUtils.getStackTrace(e));
		}
		return null;
	}
	
	// kontactoapi.birdeye.com/checkin/latest/{customerId}
	
	@Profiled
	 @Override
	 public CheckinIdDetails getLatestCheckinDetails(Integer customerId) {
	 String apiUrl = StringUtils.join(getContactServiceBaseURL(), "checkin/latest/", customerId);
	 HttpHeaders headers = getDefaultHttpHeaders();
	 ResponseEntity<CheckinIdDetails> response = null;
	 try {
	 LOG.info("Calling contact service API to get latest checkin details for customer id {} ", customerId);
	 response = restTemplate.exchange(apiUrl, HttpMethod.GET, new HttpEntity<>(headers), CheckinIdDetails.class);
	 if (response.getStatusCode() == HttpStatus.OK) {
	 return response.getBody();
	 }
	 } catch (RestClientResponseException e) {
	 LOG.error("RestClientResponseException while calling contact service latest checkin details API {} is {}", apiUrl, ExceptionUtils.getStackTrace(e));
	 } catch (Exception e) {
	 LOG.error("Exception while calling contact service latest checkin details API {} is {}", apiUrl, ExceptionUtils.getStackTrace(e));
	 }
	 return null;
	 }
	
}
