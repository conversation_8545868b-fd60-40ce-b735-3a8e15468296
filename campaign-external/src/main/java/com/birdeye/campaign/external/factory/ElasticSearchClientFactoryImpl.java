package com.birdeye.campaign.external.factory;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.elasticsearch.service.ElasticsearchHighLevelClientService;
import com.birdeye.campaign.exception.CampaignException;

@Service
public class ElasticSearchClientFactoryImpl implements ElasticSearchClientFactory {
	
	@Autowired
	@Qualifier("elasticHighLevelClientService")
	private ElasticsearchHighLevelClientService	elasticsearchHighLevelClient;
	
	@Autowired
	@Qualifier("elasticHighLevelC3ClientService")
	private ElasticsearchHighLevelClientService	elasticsearchHighLevelC3Client;
	
	@Override
	public ElasticsearchHighLevelClientService getElasticSearchHighClientService(String index) {
		if (StringUtils.equalsAnyIgnoreCase(index, Constants.REFERRAL_REPORT_INDEX, Constants.APPOINTMENT_REMINDER_REPORT_INDEX, Constants.APPOINTMENT_RECALL_REPORT_INDEX,
				Constants.REFERRAL_LEADS_INDEX, Constants.PROMOTION_REPORT_INDEX, Constants.RR_REPORT_INDEX, Constants.CX_REPORT_INDEX, Constants.SURVEY_REPORT_INDEX,
				Constants.APPOINTMENT_FORM_REPORT_INDEX)) {
			return elasticsearchHighLevelC3Client;
		}
		throw new CampaignException(ErrorCodes.INVALID_ES_INDEX, "Unsupported ES index for high level client");
	}
	
}
