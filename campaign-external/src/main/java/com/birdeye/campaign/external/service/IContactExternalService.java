package com.birdeye.campaign.external.service;

import java.util.List;

import com.birdeye.campaign.dto.CreateLocationCustomerRequest;
import com.birdeye.campaign.request.CampaignAudienceFilter;
import com.birdeye.campaign.request.external.LeadCreationRequest;
import com.birdeye.campaign.request.kontacto.ContactGetOrCreateRequest;
import com.birdeye.campaign.response.CreateCustomerCheckinResponse;
import com.birdeye.campaign.response.CustomerCustomFieldResponse;
import com.birdeye.campaign.response.external.CheckinIdDetails;
import com.birdeye.campaign.response.external.CustomerByEcidResponse;
import com.birdeye.campaign.response.external.CustomerByIdsLiteResponse;
import com.birdeye.campaign.response.external.CustomerCidResponse;
import com.birdeye.campaign.response.external.CustomerExistsResponse;
import com.birdeye.campaign.response.external.CustomerInfoResponse;
import com.birdeye.campaign.response.external.LeadCreationResponse;
import com.birdeye.campaign.response.kontacto.KontactoDTO;

public interface IContactExternalService {
	
	public static final String	CREATE_CUSTOMER			= "/customer/create/mapping";
	
	public static final String	GET_OR_CREATE_CUSTOMER	= "/customer/get-or-create";
	
	// "/customer/get/{id}"
	public static final String GET_CUSTOMER_BYID = "/customer/get/";
	
	public List<CustomerInfoResponse> getCustomersInfo(List<Integer> customerIds);
	
	public List<CustomerInfoResponse> getCustomersInfoWithCustomFieldsAndReferralCode(List<Integer> customerIds, boolean isCustomField, boolean isReferralCodeRequired);
	
	/**
	 * @param enterpriseId
	 * @param email
	 * @param phone
	 * @return
	 */
	CustomerExistsResponse isCustomerPresent(Integer enterpriseId, String email, String phone);
	
	CreateCustomerCheckinResponse createOrGetCustomerWithCheckin(CreateLocationCustomerRequest request);
	
	/**
	 * @param enterpriseOrSMBId
	 * @param request
	 * @return
	 */
	LeadCreationResponse createLead(Integer enterpriseOrSMBId, LeadCreationRequest request);
	
	/**
	 * 
	 */
	KontactoDTO getCustomerById(Integer customerId);
	
	CustomerByEcidResponse getCustomerIdByLocationAndEcid(Integer businessId, Integer ecid);
	
	CustomerCidResponse getCidsFromKontacto(CampaignAudienceFilter campaignAudienceFilter, Integer page, Integer size, String sortby, String order);
	
	KontactoDTO getOrCreateCustomer(ContactGetOrCreateRequest request, Integer accountId);
	
	/**
	 * @param customerId
	 * @return
	 */
	KontactoDTO getCustomerByIdCached(Integer customerId);
	
	public CustomerCustomFieldResponse getCustomerCustomFields(Integer enterpriseId);
	
	CustomerCustomFieldResponse getCustomerCustomFieldsData(Integer enterpriseId);
	
	KontactoDTO getCustomerByIdWithCustomFieldAndReferralCodeAndTags(Integer customerId, boolean customField, boolean referralCode, boolean tags);
	
	public List<CustomerByIdsLiteResponse> getCustomersInfoInBatchLite(List<Integer> customerIds, boolean locationName, boolean referralCode);
	
	KontactoDTO getCustomerByIdWithCustomFieldAndReferralCodeCached(Integer customerId);

	List<CheckinIdDetails> getCustomersCheckInDetails(List<Integer> customerIds, boolean assistedBy, boolean extraParams);

	CheckinIdDetails getLatestCheckinDetails(Integer customerId);

	KontactoDTO getCustomerByIdWithCustomFieldAndReferralCodeAndTagsAndReviewSource(Integer customerId, boolean customField, boolean referralCode, boolean tags, boolean reviewSource);
	
}
