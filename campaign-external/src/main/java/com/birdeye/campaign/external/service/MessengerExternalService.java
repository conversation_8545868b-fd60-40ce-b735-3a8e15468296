package com.birdeye.campaign.external.service;

import com.birdeye.campaign.dto.MessengerSmsEntity;

public interface MessengerExternalService {
	
	public void createPuslseSurveyContext(MessengerSmsEntity smsEntity);

	String fetchCustomerMCID(Integer cid, Integer businessId);

	public void postCampaignEventToMessengerRedis(MessengerSmsEntity request);

	void pushAppointmentReminderEventToMessenger(MessengerSmsEntity request);
	
}
