package com.birdeye.campaign.external.service;

import java.util.Map;

import com.birdeye.campaign.communication.message.SurveyDataMessage;
import com.birdeye.campaign.response.template.v2.SurveyCountResponse;
import com.birdeye.campaign.response.template.v2.SurveyEmbeddedResponse;

public interface SurveyExternalService {
	
	public String getSurveyNameById(Integer surveyId);
	
	public SurveyCountResponse getSurveyCountForEnterprise(Integer enterpriseId, Integer userId);
	
	String getSurveyDataById(Integer surveyId);
	
	Map<String, Object> getSurveyDataByIdAsMap(Integer surveyId);
	
	SurveyEmbeddedResponse getSurveyEmbeddedFieldCached(Integer surveyId, Long reviewRequestId, Long businessNumber);
	
	SurveyDataMessage getSurveyById(Integer surveyId);
	
}
