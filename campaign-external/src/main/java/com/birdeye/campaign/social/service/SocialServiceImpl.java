package com.birdeye.campaign.social.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.external.service.SocialExternalService;
import com.birdeye.campaign.response.external.SocialPlaceIdResponse;
import com.birdeye.campaign.utils.BatchUtils;

@Service("socialService")
public class SocialServiceImpl implements SocialService {
	
	@Autowired
	private SocialExternalService		socialExternalService;
	
	private static final Logger	logger	= LoggerFactory.getLogger(SocialServiceImpl.class);
	
	@Override
	public String getGooglePlaceIdforABusiness(Integer businessId) {
		if(businessId==null) {
			return null;
		}
		logger.info("fetching google place id for a business {}", businessId);
		List<Integer> businessIds = Collections.singletonList(businessId);
		List<SocialPlaceIdResponse> responseList = socialExternalService.getPlaceIdForGoogle(businessIds);
		
		if (CollectionUtils.isEmpty(responseList)) {
			return null;
		}
		
		for (SocialPlaceIdResponse response : responseList) {
			if (businessId == response.getBusinessId() && response.getProfileId() != null) {
				return response.getProfileId();
			}
		}
		
		return null;
		
	}
	
	@Override
	public List<SocialPlaceIdResponse> getGooglePlaceIdforBusinesses(List<Integer> businessIds) {
		if(CollectionUtils.isEmpty(businessIds)) {
			return Collections.emptyList();
		}
		logger.info("fetching google place ids for business ids with size {}", CollectionUtils.size(businessIds));
		List<SocialPlaceIdResponse> responseList = new ArrayList<>();
		try {
		BatchUtils.doInBatch(new ArrayList<>(businessIds), 500, batch -> responseList.addAll(socialExternalService.getPlaceIdForGoogle(batch)));
		}
		catch(Exception e) {
			logger.info("Error while getting palce ids for google {}", ExceptionUtils.getStackTrace(e));
		}
		return responseList;
	}
	
}