package com.birdeye.campaign.communication;

/**
 * Enum for sendgrid events to message
 * <AUTHOR>
 *
 */
public enum SendGridEventToMessageEnum {

	UNSUBSCRIBE("unsubscribe" , "Unsubscribed"),
	SPAM_REPORT("spamreport", "Reported spam"),
	DROPPED("dropped", "Dropped"),
	BOUNCE("bounce", "Email bounced");
	
	private String event;
	
	private String eventMessage;
	
	private SendGridEventToMessageEnum(String event , String eventMessage) {
		this.event = event;
		this.eventMessage = eventMessage;
	}

	/**
	 * @return the event
	 */
	public String getEvent() {
		return event;
	}

	/**
	 * @param event the event to set
	 */
	public void setEvent(String event) {
		this.event = event;
	}

	/**
	 * @return the eventMessage
	 */
	public String getEventMessage() {
		return eventMessage;
	}

	/**
	 * @param eventMessage the eventMessage to set
	 */
	public void setEventMessage(String eventMessage) {
		this.eventMessage = eventMessage;
	}
	
	public static SendGridEventToMessageEnum getEvent(String event) {
		SendGridEventToMessageEnum eventObj = null;
		for(SendGridEventToMessageEnum eventToMessageEnum : SendGridEventToMessageEnum.values()) {
			if(eventToMessageEnum.getEvent().equalsIgnoreCase(event)) {
				return eventToMessageEnum;
			}
		}
		return eventObj;
	}
}
