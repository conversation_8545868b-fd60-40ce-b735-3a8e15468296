package com.birdeye.campaign.communication.message;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RequestIdDTO implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -5094668024554034552L;
	
	private List<Long>			rrRequestIds;
	private List<Long>			cxRequestIds;
	private List<Long>			referralRequestIds;
	private List<Long>			promotionRequestIds;
	private List<Long>      	appointmentReminderIds;
	private List<Long>			appointmentRecallRequestIds;
	private List<Long>			appointmentFormRequestIds;
	
	public RequestIdDTO() {
		super();
	}
	
	public RequestIdDTO(List<Long> rrRequestIds, List<Long> cxRequestIds, List<Long> referralRequestIds, List<Long> promotionRequestIds, List<Long> appointmentReminderIds, List<Long> appointmentRecallRequestIds, List<Long> appointmentFormRequestIds) {
		super();
		this.rrRequestIds = rrRequestIds;
		this.cxRequestIds = cxRequestIds;
		this.referralRequestIds = referralRequestIds;
		this.promotionRequestIds = promotionRequestIds;
		this.appointmentReminderIds = appointmentReminderIds;
		this.appointmentRecallRequestIds = appointmentRecallRequestIds;
		this.appointmentFormRequestIds = appointmentFormRequestIds;
	}

	public List<Long> getRrRequestIds() {
		return rrRequestIds;
	}
	
	public void setRrRequestIds(List<Long> rrRequestIds) {
		this.rrRequestIds = rrRequestIds;
	}
	
	public List<Long> getCxRequestIds() {
		return cxRequestIds;
	}
	
	public void setCxRequestIds(List<Long> cxRequestIds) {
		this.cxRequestIds = cxRequestIds;
	}
	
	public List<Long> getReferralRequestIds() {
		return referralRequestIds;
	}
	
	public void setReferralRequestIds(List<Long> referralRequestIds) {
		this.referralRequestIds = referralRequestIds;
	}
	
	public List<Long> getPromotionRequestIds() {
		return promotionRequestIds;
	}
	
	public void setPromotionRequestIds(List<Long> promotionRequestIds) {
		this.promotionRequestIds = promotionRequestIds;
	}
	
	public List<Long> getAppointmentReminderIds() {
		return appointmentReminderIds;
	}

	public void setAppointmentReminderIds(List<Long> appointmentReminderIds) {
		this.appointmentReminderIds = appointmentReminderIds;
	}
	
	public List<Long> getAppointmentRecallRequestIds() {
		return appointmentRecallRequestIds;
	}

	public void setAppointmentRecallRequestIds(List<Long> appointmentRecallRequestIds) {
		this.appointmentRecallRequestIds = appointmentRecallRequestIds;
	}
	
	/**
	 * @return the appointmentFormRequestIds
	 */
	public List<Long> getAppointmentFormRequestIds() {
		return appointmentFormRequestIds;
	}

	/**
	 * @param appointmentFormRequestIds the appointmentFormRequestIds to set
	 */
	public void setAppointmentFormRequestIds(List<Long> appointmentFormRequestIds) {
		this.appointmentFormRequestIds = appointmentFormRequestIds;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
