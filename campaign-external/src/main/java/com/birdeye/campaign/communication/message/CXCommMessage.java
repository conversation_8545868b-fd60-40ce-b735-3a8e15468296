package com.birdeye.campaign.communication.message;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;

/**
 * Communication object of Logging cx comm data in ES
 * <AUTHOR>
 */
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CXCommMessage implements Serializable {

	private static final long serialVersionUID = -5750425866465799903L;
	
	private Integer businessId;
	private String businessType;
	private String businessName;
	private String businessAlias;
	private CommLocation location;
	private Integer enterpriseId;
	private Long requestId;
	private String cxType;
	private Long updatedAt = new Date().getTime();
	private Integer reminderCount;
	private String clickTime ;
	private String reviewSourceClickTime;
	private Integer clicked;
	private Integer clickCount;
	private Integer opened;
	private Integer rating;
	
	@SerializedName("open_web")
	@JsonProperty("open_web")
	private Integer openWeb;
	
	@SerializedName("open_tab")
	@JsonProperty("open_tab")
	private Integer openTab;
	
	@SerializedName("open_mobile")
	@JsonProperty("open_mobile")
	private Integer openMobile;
	
	private Integer customerId;
	private String customerName;
	
	private Integer status = 0;
	private String src;
	private String requestDate;
	private Integer checkinId;
	private Integer campaignId;
	private Integer reviewSourceClicked;
	private Integer reviewSourceClickCount;
	
	@SerializedName("clicked_sources")
	@JsonProperty("clicked_sources")
	private Integer[] clickedSources;
	
	private Integer reviewSourceId;
	private Integer reviewId;
	private Float reviewRating;
	private Integer sourceId;
	private Integer recommended;
	private Integer requestCategory;
	private Integer templateId;
	private String failureReason;
	private Integer assistedById;
	private String assistedByName;
	private Long requestDateNew; // new field added to keep date in Epoch
	
	public CXCommMessage() {

	}
	
	public CXCommMessage(Long requestId,Integer enterpriseId) {
		this.requestId = requestId;
		this.enterpriseId = enterpriseId;
	}

	/**
	 * @return the businessId
	 */
	public Integer getBusinessId() {
		return businessId;
	}

	/**
	 * @param businessId the businessId to set
	 */
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}

	/**
	 * @return the businessType
	 */
	public String getBusinessType() {
		return businessType;
	}

	/**
	 * @param businessType the businessType to set
	 */
	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	/**
	 * @return the businessName
	 */
	public String getBusinessName() {
		return businessName;
	}

	/**
	 * @param businessName the businessName to set
	 */
	public void setBusinessName(String businessName) {
		this.businessName = businessName;
	}

	/**
	 * @return the location
	 */
	public CommLocation getLocation() {
		return location;
	}

	/**
	 * @param location the location to set
	 */
	public void setLocation(CommLocation location) {
		this.location = location;
	}

	/**
	 * @return the enterpriseId
	 */
	public Integer getEnterpriseId() {
		return enterpriseId;
	}

	/**
	 * @param enterpriseId the enterpriseId to set
	 */
	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	/**
	 * @return the requestId
	 */
	public Long getRequestId() {
		return requestId;
	}

	/**
	 * @param requestId the requestId to set
	 */
	public void setRequestId(Long requestId) {
		this.requestId = requestId;
	}

	/**
	 * @return the cxType
	 */
	public String getCxType() {
		return cxType;
	}

	/**
	 * @param cxType the cxType to set
	 */
	public void setCxType(String cxType) {
		this.cxType = cxType;
	}

	/**
	 * @return the updatedAt
	 */
	public Long getUpdatedAt() {
		return updatedAt;
	}

	/**
	 * @param updatedAt the updatedAt to set
	 */
	public void setUpdatedAt(Long updatedAt) {
		this.updatedAt = updatedAt;
	}

	/**
	 * @return the reminderCount
	 */
	public Integer getReminderCount() {
		return reminderCount;
	}

	/**
	 * @param reminderCount the reminderCount to set
	 */
	public void setReminderCount(Integer reminderCount) {
		this.reminderCount = reminderCount;
	}

	/**
	 * @return the clickTime
	 */
	public String getClickTime() {
		return clickTime;
	}

	/**
	 * @param clickTime the clickTime to set
	 */
	public void setClickTime(String clickTime) {
		this.clickTime = clickTime;
	}
	
	/**
	 * @return the reviewSourceClickTime
	 */
	public String getReviewSourceClickTime() {
		return reviewSourceClickTime;
	}

	/**
	 * @param reviewSourceClickTime the reviewSourceClickTime to set
	 */
	public void setReviewSourceClickTime(String reviewSourceClickTime) {
		this.reviewSourceClickTime = reviewSourceClickTime;
	}

	/**
	 * @return the clicked
	 */
	public Integer getClicked() {
		return clicked;
	}

	/**
	 * @param clicked the clicked to set
	 */
	public void setClicked(Integer clicked) {
		this.clicked = clicked;
	}

	/**
	 * @return the clickCount
	 */
	public Integer getClickCount() {
		return clickCount;
	}

	/**
	 * @param clickCount the clickCount to set
	 */
	public void setClickCount(Integer clickCount) {
		this.clickCount = clickCount;
	}

	/**
	 * @return the opened
	 */
	public Integer getOpened() {
		return opened;
	}

	/**
	 * @param opened the opened to set
	 */
	public void setOpened(Integer opened) {
		this.opened = opened;
	}

	/**
	 * @return the rating
	 */
	public Integer getRating() {
		return rating;
	}

	/**
	 * @param rating the rating to set
	 */
	public void setRating(Integer rating) {
		this.rating = rating;
	}
	
	/**
	 * @return the businessAlias
	 */
	public String getBusinessAlias() {
		return businessAlias;
	}

	/**
	 * @param businessAlias the businessAlias to set
	 */
	public void setBusinessAlias(String businessAlias) {
		this.businessAlias = businessAlias;
	}

	/**
	 * @return the customerName
	 */
	public String getCustomerName() {
		return customerName;
	}

	/**
	 * @param customerName the customerName to set
	 */
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	/**
	 * @return the customerId
	 */
	public Integer getCustomerId() {
		return customerId;
	}

	/**
	 * @param customerId the customerId to set
	 */
	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	/**
	 * @return the status
	 */
	public Integer getStatus() {
		return status;
	}

	/**
	 * @param status the status to set
	 */
	public void setStatus(Integer status) {
		this.status = status;
	}

	/**
	 * @return the src
	 */
	public String getSrc() {
		return src;
	}

	/**
	 * @param src the src to set
	 */
	public void setSrc(String src) {
		this.src = src;
	}

	/**
	 * @return the requestDate
	 */
	public String getRequestDate() {
		return requestDate;
	}

	/**
	 * @param requestDate the requestDate to set
	 */
	public void setRequestDate(String requestDate) {
		this.requestDate = requestDate;
	}

	/**
	 * @return the checkinId
	 */
	public Integer getCheckinId() {
		return checkinId;
	}

	/**
	 * @param checkinId the checkinId to set
	 */
	public void setCheckinId(Integer checkinId) {
		this.checkinId = checkinId;
	}

	/**
	 * @return the campaignId
	 */
	public Integer getCampaignId() {
		return campaignId;
	}

	/**
	 * @param campaignId the campaignId to set
	 */
	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}


	/**
	 * @return the reviewSourceClicked
	 */
	public Integer getReviewSourceClicked() {
		return reviewSourceClicked;
	}

	/**
	 * @param reviewSourceClicked the reviewSourceClicked to set
	 */
	public void setReviewSourceClicked(Integer reviewSourceClicked) {
		this.reviewSourceClicked = reviewSourceClicked;
	}

	/**
	 * @return the reviewSourceClickCount
	 */
	public Integer getReviewSourceClickCount() {
		return reviewSourceClickCount;
	}

	/**
	 * @param reviewSourceClickCount the reviewSourceClickCount to set
	 */
	public void setReviewSourceClickCount(Integer reviewSourceClickCount) {
		this.reviewSourceClickCount = reviewSourceClickCount;
	}

	/**
	 * @return the reviewSourceId
	 */
	public Integer getReviewSourceId() {
		return reviewSourceId;
	}

	/**
	 * @param reviewSourceId the reviewSourceId to set
	 */
	public void setReviewSourceId(Integer reviewSourceId) {
		this.reviewSourceId = reviewSourceId;
	}

	/**
	 * @return the reviewId
	 */
	public Integer getReviewId() {
		return reviewId;
	}

	/**
	 * @param reviewId the reviewId to set
	 */
	public void setReviewId(Integer reviewId) {
		this.reviewId = reviewId;
	}

	/**
	 * @return the reviewRating
	 */
	public Float getReviewRating() {
		return reviewRating;
	}

	/**
	 * @param reviewRating the reviewRating to set
	 */
	public void setReviewRating(Float reviewRating) {
		this.reviewRating = reviewRating;
	}

	/**
	 * @return the sourceId
	 */
	public Integer getSourceId() {
		return sourceId;
	}

	/**
	 * @param sourceId the sourceId to set
	 */
	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}
	
	/**
	 * @return the recommended
	 */
	public Integer getRecommended() {
		return recommended;
	}

	/**
	 * @param recommended the recommended to set
	 */
	public void setRecommended(Integer recommended) {
		this.recommended = recommended;
	}


	/**
	 * @return the requestCategory
	 */
	public Integer getRequestCategory() {
		return requestCategory;
	}

	/**
	 * @param requestCategory the requestCategory to set
	 */
	public void setRequestCategory(Integer requestCategory) {
		this.requestCategory = requestCategory;
	}


	/**
	 * @return the templateId
	 */
	public Integer getTemplateId() {
		return templateId;
	}

	/**
	 * @param templateId the templateId to set
	 */
	public void setTemplateId(Integer templateId) {
		this.templateId = templateId;
	}

	/**
	 * @return the openWeb
	 */
	public Integer getOpenWeb() {
		return openWeb;
	}

	/**
	 * @param openWeb the openWeb to set
	 */
	public void setOpenWeb(Integer openWeb) {
		this.openWeb = openWeb;
	}

	/**
	 * @return the openTab
	 */
	public Integer getOpenTab() {
		return openTab;
	}

	/**
	 * @param openTab the openTab to set
	 */
	public void setOpenTab(Integer openTab) {
		this.openTab = openTab;
	}

	/**
	 * @return the openMobile
	 */
	public Integer getOpenMobile() {
		return openMobile;
	}

	/**
	 * @param openMobile the openMobile to set
	 */
	public void setOpenMobile(Integer openMobile) {
		this.openMobile = openMobile;
	}

	/**
	 * @return the clickedSources
	 */
	public Integer[] getClickedSources() {
		return clickedSources;
	}

	/**
	 * @param clickedSources the clickedSources to set
	 */
	public void setClickedSources(Integer[] clickedSources) {
		this.clickedSources = clickedSources;
	}

	/**
	 * @return the failureReason
	 */
	public String getFailureReason() {
		return failureReason;
	}

	/**
	 * @param failureReason the failureReason to set
	 */
	public void setFailureReason(String failureReason) {
		this.failureReason = failureReason;
	}

	/**
	 * @return the assistedById
	 */
	public Integer getAssistedById() {
		return assistedById;
	}

	/**
	 * @param assistedById the assistedById to set
	 */
	public void setAssistedById(Integer assistedById) {
		this.assistedById = assistedById;
	}

	/**
	 * @return the assistedByName
	 */
	public String getAssistedByName() {
		return assistedByName;
	}

	/**
	 * @param assistedByName the assistedByName to set
	 */
	public void setAssistedByName(String assistedByName) {
		this.assistedByName = assistedByName;
	}
	
	public Long getRequestDateNew() {
		return requestDateNew;
	}

	public void setRequestDateNew(Long requestDateNew) {
		this.requestDateNew = requestDateNew;
	}


	public static class Builder {
		private Integer businessId;
		private String businessType;
		private String businessName;
		private String businessAlias;
		private CommLocation location;
		private Integer enterpriseId;
		private Long requestId;
		private String cxType;
		private Integer reminderCount;
		private String clickTime;
		private String reviewSourceClickTime ;
		private Integer clicked;
		private Integer clickCount;
		private Integer opened;
		private Integer rating;
		private Integer openWeb;
		private Integer openTab;
		private Integer openMobile;
		private Integer customerId;
		private String customerName;
		private Integer status;
		private String src;
		private String requestDate;
		private Integer checkinId;
		private Integer campaignId;
		private Integer[] clickedSources;
		private Integer reviewSourceClicked;
		private Integer reviewSourceClickCount;
		private Integer reviewSourceId;
		private Integer reviewId;
		private Float reviewRating;
		private Integer sourceId;
		private Integer recommended;
		private Integer requestCategory;
		private Integer templateId;
		private String failureReason;
		private Integer assistedById;
		private String assistedByName;

		public Builder businessId(Integer businessId) {
			this.businessId = businessId;
			return this;
		}

		public Builder businessType(String businessType) {
			this.businessType = businessType;
			return this;
		}

		public Builder businessName(String businessName) {
			this.businessName = businessName;
			return this;
		}

		public Builder location(CommLocation location) {
			this.location = location;
			return this;
		}

		public Builder enterpriseId(Integer enterpriseId) {
			this.enterpriseId = enterpriseId;
			return this;
		}

		public Builder requestId(Long requestId) {
			this.requestId = requestId;
			return this;
		}

		public Builder cxType(String cxType) {
			this.cxType = cxType;
			return this;
		}

		public Builder reminderCount(Integer reminderCount) {
			this.reminderCount = reminderCount;
			return this;
		}

		public Builder clickTime(String clickTime) {
			this.clickTime = clickTime;
			return this;
		}
		
		public Builder reviewSourceClickTime(String clickTime) {
			this.reviewSourceClickTime = clickTime;
			return this;
		}

		public Builder clicked(Integer clicked) {
			this.clicked = clicked;
			return this;
		}

		public Builder clickCount(Integer clickCount) {
			this.clickCount = clickCount;
			return this;
		}

		public Builder opened(Integer opened) {
			this.opened = opened;
			return this;
		}

		public Builder rating(Integer rating) {
			this.rating = rating;
			return this;
		}

		public Builder openEeb(Integer openWeb) {
			this.openWeb = openWeb;
			return this;
		}

		public Builder openTab(Integer openTab) {
			this.openTab = openTab;
			return this;
		}

		public Builder openMobile(Integer openMobile) {
			this.openMobile = openMobile;
			return this;
		}

		public Builder customerId(Integer customerId) {
			this.customerId = customerId;
			return this;
		}
		
		public Builder customerName(String customerName) {
			this.customerName = customerName;
			return this;
		}
		
		public Builder businessAlias(String businessAlias) {
			this.businessAlias = businessAlias;
			return this;
		}

		public Builder status(Integer status) {
			this.status = status;
			return this;
		}

		public Builder src(String src) {
			this.src = src;
			return this;
		}

		public Builder requestDate(String requestDate) {
			this.requestDate = requestDate;
			return this;
		}

		public Builder checkinId(Integer checkinId) {
			this.checkinId = checkinId;
			return this;
		}

		public Builder campaignId(Integer campaignId) {
			this.campaignId = campaignId;
			return this;
		}

		public Builder clickedSources(Integer[] clickedSources) {
			this.clickedSources = clickedSources;
			return this;
		}

		public Builder reviewSourceId(Integer reviewSourceId) {
			this.reviewSourceId = reviewSourceId;
			return this;
		}

		public Builder reviewId(Integer reviewId) {
			this.reviewId = reviewId;
			return this;
		}

		public Builder reviewRating(Float reviewRating) {
			this.reviewRating = reviewRating;
			return this;
		}
		
		public Builder reviewSourceClicked(Integer clicked) {
			this.reviewSourceClicked = clicked;
			return this;
		}

		public Builder reviewSourceClickCount(Integer clickCount) {
			this.reviewSourceClickCount = clickCount;
			return this;
		}
		
		public Builder sourceId(Integer sourceId) {
			this.sourceId = sourceId;
			return this;
		}
		
		public Builder recommended(Integer recommended) {
			this.recommended = recommended;
			return this;
		}
		
		public Builder requestCategory(Integer requestCategory) {
			this.requestCategory = requestCategory;
			return this;
		}
		
		public Builder templateId(Integer templateId) {
			this.templateId = templateId;
			return this;
		}
		
		public Builder failureReason(String failureReason) {
			this.failureReason = failureReason;
			return this;
		}
		
		public Builder assistedById(Integer assistedById) {
			this.assistedById = assistedById;
			return this;
		}
		
		public Builder assistedByName(String assistedByName) {
			this.assistedByName = assistedByName;
			return this;
		}

		public CXCommMessage build() {
			return new CXCommMessage(this);
		}
	}

	private CXCommMessage(Builder builder) {
		this.businessId = builder.businessId;
		this.businessType = builder.businessType;
		this.businessName = builder.businessName;
		this.businessAlias = builder.businessAlias;
		this.location = builder.location;
		this.enterpriseId = builder.enterpriseId;
		this.requestId = builder.requestId;
		this.cxType = builder.cxType;
		this.updatedAt = new Date().getTime();
		this.reminderCount = builder.reminderCount;
		this.clickTime = builder.clickTime;
		this.reviewSourceClickTime	 = builder.reviewSourceClickTime;
		this.clicked = builder.clicked;
		this.clickCount = builder.clickCount;
		this.opened = builder.opened;
		this.rating = builder.rating;
		this.openWeb = builder.openWeb;
		this.openTab = builder.openTab;
		this.openMobile = builder.openMobile;
		this.customerId = builder.customerId;
		this.customerName = builder.customerName;
		this.status = builder.status;
		this.src = builder.src;
		this.requestDate = builder.requestDate;
		this.checkinId = builder.checkinId;
		this.campaignId = builder.campaignId;
		this.clickedSources = builder.clickedSources;
		this.reviewSourceId = builder.reviewSourceId;
		this.reviewId = builder.reviewId;
		this.reviewRating = builder.reviewRating;
		this.sourceId = builder.sourceId;
		this.reviewSourceClicked = builder.reviewSourceClicked;
		this.reviewSourceClickCount = builder.reviewSourceClickCount;
		this.recommended = builder.recommended;
		this.requestCategory = builder.requestCategory;
		this.templateId = builder.templateId;
		this.failureReason = builder.failureReason;
		this.assistedById = builder.assistedById;
		this.assistedByName = builder.assistedByName;
	}

	@Override
	public String toString() {
		return "CXCommMessage [businessId=" + businessId + ", businessType=" + businessType + ", businessName="
				+ businessName + ", businessAlias=" + businessAlias + ", location=" + location + ", enterpriseId="
				+ enterpriseId + ", requestId=" + requestId + ", cxType=" + cxType + ", updatedAt=" + updatedAt
				+ ", reminderCount=" + reminderCount + ", clickTime=" + clickTime + ", reviewSourceClickTime="
				+ reviewSourceClickTime + ", clicked=" + clicked + ", clickCount=" + clickCount + ", opened=" + opened
				+ ", rating=" + rating + ", openWeb=" + openWeb + ", openTab=" + openTab + ", openMobile=" + openMobile
				+ ", customerId=" + customerId + ", customerName=" + customerName + ", status=" + status + ", src="
				+ src + ", requestDate=" + requestDate + ", checkinId=" + checkinId + ", campaignId=" + campaignId
				+ ", reviewSourceClicked=" + reviewSourceClicked + ", reviewSourceClickCount=" + reviewSourceClickCount
				+ ", clickedSources=" + Arrays.toString(clickedSources) + ", reviewSourceId=" + reviewSourceId
				+ ", reviewId=" + reviewId + ", reviewRating=" + reviewRating + ", sourceId=" + sourceId
				+ ", recommended=" + recommended + ", requestCategory=" + requestCategory + ", templateId=" + templateId
				+ ", failureReason=" + failureReason + ", assistedById=" + assistedById + ", assistedByName="
				+ assistedByName + "]";
	}

	
	
}
