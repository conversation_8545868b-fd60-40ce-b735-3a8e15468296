package com.birdeye.campaign.communication.message;

import java.io.Serializable;
import java.util.Map;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.response.external.CheckinIdDetails;

public class ReviewRequstToCheckInDetailsMapResponse implements Serializable {
	
	/**
	 * 
	 */
	private static final long			serialVersionUID	= -5143578942454506685L;
	
	private Map<Long, CheckinIdDetails>	reviewRequestToCheckInDetailsMap;
	
	public Map<Long, CheckinIdDetails> getReviewRequestToCheckInDetailsMap() {
		return reviewRequestToCheckInDetailsMap;
	}
	
	public void setReviewRequestToCheckInDetailsMap(Map<Long, CheckinIdDetails> reviewRequestToCheckInDetailsMap) {
		this.reviewRequestToCheckInDetailsMap = reviewRequestToCheckInDetailsMap;
	}
	
	public ReviewRequstToCheckInDetailsMapResponse() {
		
	}
	
	public ReviewRequstToCheckInDetailsMapResponse(Map<Long, CheckinIdDetails> reviewRequestToCheckInDetailsMap) {
		super();
		this.reviewRequestToCheckInDetailsMap = reviewRequestToCheckInDetailsMap;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}