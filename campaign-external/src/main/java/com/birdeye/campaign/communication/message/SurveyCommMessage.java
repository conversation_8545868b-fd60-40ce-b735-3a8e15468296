package com.birdeye.campaign.communication.message;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;

/**
 * Communication object for Survey data in ES
 * <AUTHOR>
 *
 */
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SurveyCommMessage implements Serializable{

	private static final long serialVersionUID = -7856583781771114055L;
	
	@SerializedName("b_id")
	@JsonProperty("b_id")
	private Integer businessId;
	
	@SerializedName("req_id")
	@JsonProperty("req_id")
    private Long requestId;
	
	@SerializedName("e_id")
	@JsonProperty("e_id")
    private Integer enterpriseId;
	
	@SerializedName("customer_id")
	@JsonProperty("customer_id")
	private Integer customerId;
	
	@SerializedName("reminder_cnt")
	@JsonProperty("reminder_cnt")
	private Integer reminderCount;
	
	private Integer status = 0;
	private String src;
	
	@SerializedName("checkin_id")
	@JsonProperty("checkin_id")
	private Integer checkinId;
	
	@SerializedName("survey_id")
	@JsonProperty("survey_id")
	private Integer surveyId;
	
	@SerializedName("campaign_id")
	@JsonProperty("campaign_id")
	private Integer campaignId;
	
	@SerializedName("req_date")
	@JsonProperty("req_date")
	private String requestDate;
	
	private CommOpen open;
	
	@SerializedName("open_cnt")
	@JsonProperty("open_cnt")
    private Integer openCount;
	
	@SerializedName("clicked")
	@JsonProperty("clicked")
	private Integer clicked;
	
	@SerializedName("click_time")
	@JsonProperty("click_time")
    private Long clickTime ;
	
	@SerializedName("template_id")
	@JsonProperty("template_id")
	private Integer templateId;
	
	@SerializedName("c_time")
	@JsonProperty("c_time")
    private Long cTime = System.currentTimeMillis(); //Represents time of create/update of message
	
	private String failureReason;
	
	private String businessAlias;
	
	private String customerName;
	
	private Integer isSurveyCompleted; 

	private Integer isSurveyStarted;
	
	private Long requestDateNew; // new field added to keep date in Epoch
	
	public SurveyCommMessage() {
		
	}
	
	public SurveyCommMessage(CommMessage commMessage) {
		this.enterpriseId = commMessage.getEnterpriseId();
		this.requestId = commMessage.getRequestId();
		this.businessId = commMessage.getBusinessId();
		this.customerId = commMessage.getCustomerId();
		this.reminderCount = commMessage.getReminderCount();
		this.status = commMessage.getStatus();
		this.src = commMessage.getSrc();
		this.checkinId = commMessage.getCheckinId();
		this.surveyId = commMessage.getSurveyId();
		this.campaignId = commMessage.getCampaignId();
		this.requestDate = commMessage.getRequestDate();
		this.open = commMessage.getOpen();
		this.openCount = commMessage.getOpenCount();
		this.templateId = commMessage.getTemplateId();
		this.cTime = commMessage.getcTime();
		this.failureReason= commMessage.getFailureReason();
		this.businessAlias = commMessage.getBusinessAlias();
		this.customerName = commMessage.getCustomerName();
		this.requestDateNew = commMessage.getRequestDateNew();
	}

	/**
	 * @return the businessId
	 */
	public Integer getBusinessId() {
		return businessId;
	}

	/**
	 * @param businessId the businessId to set
	 */
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}

	/**
	 * @return the requestId
	 */
	public Long getRequestId() {
		return requestId;
	}

	/**
	 * @param requestId the requestId to set
	 */
	public void setRequestId(Long requestId) {
		this.requestId = requestId;
	}

	/**
	 * @return the enterpriseId
	 */
	public Integer getEnterpriseId() {
		return enterpriseId;
	}

	/**
	 * @param enterpriseId the enterpriseId to set
	 */
	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	/**
	 * @return the customerId
	 */
	public Integer getCustomerId() {
		return customerId;
	}

	/**
	 * @param customerId the customerId to set
	 */
	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	/**
	 * @return the reminderCount
	 */
	public Integer getReminderCount() {
		return reminderCount;
	}

	/**
	 * @param reminderCount the reminderCount to set
	 */
	public void setReminderCount(Integer reminderCount) {
		this.reminderCount = reminderCount;
	}

	/**
	 * @return the status
	 */
	public Integer getStatus() {
		return status;
	}

	/**
	 * @param status the status to set
	 */
	public void setStatus(Integer status) {
		this.status = status;
	}

	/**
	 * @return the src
	 */
	public String getSrc() {
		return src;
	}

	/**
	 * @param src the src to set
	 */
	public void setSrc(String src) {
		this.src = src;
	}

	/**
	 * @return the checkinId
	 */
	public Integer getCheckinId() {
		return checkinId;
	}

	/**
	 * @param checkinId the checkinId to set
	 */
	public void setCheckinId(Integer checkinId) {
		this.checkinId = checkinId;
	}

	/**
	 * @return the surveyId
	 */
	public Integer getSurveyId() {
		return surveyId;
	}

	/**
	 * @param surveyId the surveyId to set
	 */
	public void setSurveyId(Integer surveyId) {
		this.surveyId = surveyId;
	}

	/**
	 * @return the campaignId
	 */
	public Integer getCampaignId() {
		return campaignId;
	}

	/**
	 * @param campaignId the campaignId to set
	 */
	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}

	/**
	 * @return the requestDate
	 */
	public String getRequestDate() {
		return requestDate;
	}

	/**
	 * @param requestDate the requestDate to set
	 */
	public void setRequestDate(String requestDate) {
		this.requestDate = requestDate;
	}

	/**
	 * @return the open
	 */
	public CommOpen getOpen() {
		return open;
	}

	/**
	 * @param open the open to set
	 */
	public void setOpen(CommOpen open) {
		this.open = open;
	}

	/**
	 * @return the openCount
	 */
	public Integer getOpenCount() {
		return openCount;
	}

	/**
	 * @param openCount the openCount to set
	 */
	public void setOpenCount(Integer openCount) {
		this.openCount = openCount;
	}

	/**
	 * @return the clicked
	 */
	public Integer getClicked() {
		return clicked;
	}

	/**
	 * @param clicked the clicked to set
	 */
	public void setClicked(Integer clicked) {
		this.clicked = clicked;
	}

	/**
	 * @return the clickTime
	 */
	public Long getClickTime() {
		return clickTime;
	}

	/**
	 * @param clickTime the clickTime to set
	 */
	public void setClickTime(Long clickTime) {
		this.clickTime = clickTime;
	}

	/**
	 * @return the templateId
	 */
	public Integer getTemplateId() {
		return templateId;
	}

	/**
	 * @param templateId the templateId to set
	 */
	public void setTemplateId(Integer templateId) {
		this.templateId = templateId;
	}

	/**
	 * @return the cTime
	 */
	public Long getcTime() {
		return cTime;
	}

	/**
	 * @param cTime the cTime to set
	 */
	public void setcTime(Long cTime) {
		this.cTime = cTime;
	}

	/**
	 * @return the failureReason
	 */
	public String getFailureReason() {
		return failureReason;
	}

	/**
	 * @param failureReason the failureReason to set
	 */
	public void setFailureReason(String failureReason) {
		this.failureReason = failureReason;
	}

	/**
	 * @return the businessAlias
	 */
	public String getBusinessAlias() {
		return businessAlias;
	}

	/**
	 * @param businessAlias the businessAlias to set
	 */
	public void setBusinessAlias(String businessAlias) {
		this.businessAlias = businessAlias;
	}

	/**
	 * @return the customerName
	 */
	public String getCustomerName() {
		return customerName;
	}

	/**
	 * @param customerName the customerName to set
	 */
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	/**
	 * @return the isSurveyCompleted
	 */
	public Integer getIsSurveyCompleted() {
		return isSurveyCompleted;
	}

	/**
	 * @param isSurveyCompleted the isSurveyCompleted to set
	 */
	public void setIsSurveyCompleted(Integer isSurveyCompleted) {
		this.isSurveyCompleted = isSurveyCompleted;
	}

	/**
	 * @return the isSurveyStarted
	 */
	public Integer getIsSurveyStarted() {
		return isSurveyStarted;
	}

	/**
	 * @param isSurveyStarted the isSurveyStarted to set
	 */
	public void setIsSurveyStarted(Integer isSurveyStarted) {
		this.isSurveyStarted = isSurveyStarted;
	}

	public Long getRequestDateNew() {
		return requestDateNew;
	}

	public void setRequestDateNew(Long requestDateNew) {
		this.requestDateNew = requestDateNew;
	}
	
}
