package com.birdeye.campaign.communication.message;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;

/**
 * Wrapper object for Landing page event click
 * <AUTHOR>
 *
 */
public class CommLandingPageClick implements Serializable{
	
    private static final long serialVersionUID = -4237227233957697162L;
    
    @SerializedName("evnt_id")
    @JsonProperty("evnt_id")
    private String eventId;
    
    private Integer rtng;
    private Integer rcmd;
    private String time;

    public CommLandingPageClick() {
    }

    public CommLandingPageClick(String eventId,Integer rtng, Integer rcmd, String time) {
        this.eventId = eventId;
        this.rtng = rtng;
        this.rcmd = rcmd;
        this.time = time;
    }
    
    public CommLandingPageClick(String eventId,Integer rtng, Integer rcmd, Date time) {
        this.eventId = eventId;
        this.rtng = rtng;
        this.rcmd = rcmd;
        this.time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(time);
    }

	public String getEventId() {
		return eventId;
	}

	public void setEventId(String eventId) {
		this.eventId = eventId;
	}

	public Integer getRcmd() {
        return rcmd;
    }

    public void setRcmd(Integer rcmd) {
        this.rcmd = rcmd;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public Integer getRtng() {
            return rtng;
    }

    public void setRtng(Integer rtng) {
            this.rtng = rtng;
    }

	@Override
	public String toString() {
		return "CommLandingPageClick [eventId=" + eventId + ", rtng=" + rtng + ", rcmd=" + rcmd + ", time=" + time
				+ "]";
	}

}