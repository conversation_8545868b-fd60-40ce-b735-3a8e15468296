package com.birdeye.campaign.communication.message;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * <AUTHOR>
 */
@JsonInclude(Include.NON_NULL)
public class CommLocation implements Serializable {

	private static final long serialVersionUID = 5148766057671506845L;

	private String city;

	private String state;

	public CommLocation() {
		
	}
	
	public String getCity() {
		return city;
	}

	public String getState() {
		return state;
	}

	public static class Builder {
		private String city;
		private String state;

		public Builder city(String city) {
			this.city = city;
			return this;
		}

		public Builder state(String state) {
			this.state = state;
			return this;
		}

		public CommLocation build() {
			return new CommLocation(this);
		}
	}

	private CommLocation(Builder builder) {
		this.city = builder.city;
		this.state = builder.state;
	}

	@Override
	public String toString() {
		return "CommLocation [city=" + city + ", state=" + state + "]";
	}
	
}
