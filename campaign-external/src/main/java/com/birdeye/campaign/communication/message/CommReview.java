
package com.birdeye.campaign.communication.message;

import java.io.Serializable;

/**
 * <AUTHOR>
 */

public class CommReview implements Serializable{
    
    private static final long serialVersionUID = -1598780124711140725L;
    private Integer r_id;
    private Float rtng;
    private Integer rev_src_id;

    public CommReview() {
    }

    public CommReview(Integer r_id, Float rtng, Integer rev_src_id) {
        this.r_id = r_id;
        this.rtng = rtng;
        this.rev_src_id = rev_src_id;
    }

    public Integer getR_id() {
        return r_id;
    }

    public void setR_id(Integer r_id) {
        this.r_id = r_id;
    }

    public Float getRtng() {
        return rtng;
    }

    public void setRtng(Float rtng) {
        this.rtng = rtng;
    }

    public Integer getRev_src_id() {
        return rev_src_id;
    }

    public void setRev_src_id(Integer rev_src_id) {
        this.rev_src_id = rev_src_id;
    }

	@Override
	public String toString() {
		return "CommReview [r_id=" + r_id + ", rtng=" + rtng + ", rev_src_id=" + rev_src_id + "]";
	}
    
}
