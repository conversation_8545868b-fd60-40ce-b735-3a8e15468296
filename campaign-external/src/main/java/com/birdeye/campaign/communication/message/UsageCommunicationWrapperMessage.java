package com.birdeye.campaign.communication.message;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * Wrapper message for communication from platform
 * <AUTHOR>
 *
 */
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UsageCommunicationWrapperMessage implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -3626173348404005120L;
	private String requestType; //review_request/cx_request/appointment_reminder
	private UsageCommunicationMessage communicationMessage;
	
	
	/**
	 * 
	 */
	public UsageCommunicationWrapperMessage() {
		super();
	}
	public UsageCommunicationWrapperMessage(String requestType) {
		this.requestType = requestType;
	}
	public String getRequestType() {
		return requestType;
	}
	public void setRequestType(String requestType) {
		this.requestType = requestType;
	}
	
	public UsageCommunicationMessage getCommunicationMessage() {
		return communicationMessage;
	}
	public void setCommunicationMessage(UsageCommunicationMessage communicationMessage) {
		this.communicationMessage = communicationMessage;
	}
	
	@Override
	public String toString() {
        ReflectionToStringBuilder b = new ReflectionToStringBuilder(this,ToStringStyle.DEFAULT_STYLE);
        return b.toString();        
	}
	
}
