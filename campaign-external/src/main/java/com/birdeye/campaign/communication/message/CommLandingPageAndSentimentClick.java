package com.birdeye.campaign.communication.message;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;

/**
 *
 * <AUTHOR>
 */
public class CommLandingPageAndSentimentClick implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -9087097121403168205L;

	@SerializedName("evnt_id")
	@JsonProperty("evnt_id")
	private String eventId;
	
	private Integer rtng;
	
	private Integer rcmd;
	
	private String time;
	
	@SerializedName("s_id")
	@JsonProperty("s_id")
	private Integer sid;
	
	@SerializedName("s_name")
	@JsonProperty("s_name")
	private String sname;

	public CommLandingPageAndSentimentClick() {
	}

	public CommLandingPageAndSentimentClick(String eventId, Integer rtng, Integer rcmd, String time, Integer sid, String sname) {
		super();
		this.eventId = eventId;
		this.rtng = rtng;
		this.rcmd = rcmd;
		this.time = time;
		this.sid = sid;
		this.sname = sname;
	}

	public CommLandingPageAndSentimentClick(Integer s_id, String s_name, String time) {
		this.sid = s_id;
		this.sname = s_name;
		this.time = time;
	}
	
	public CommLandingPageAndSentimentClick(String eventId, Integer rtng, Integer rcmd, Date date) {
		super();
		this.eventId = eventId;
		this.rtng = rtng;
		this.rcmd = rcmd;
		this.time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
	}

	/**
	 * @return the eventId
	 */
	public String getEventId() {
		return eventId;
	}

	/**
	 * @param eventId
	 *            the eventId to set
	 */
	public void setEventId(String eventId) {
		this.eventId = eventId;
	}

	public Integer getRcmd() {
		return rcmd;
	}

	public void setRcmd(Integer rcmd) {
		this.rcmd = rcmd;
	}

	public String getTime() {
		return time;
	}

	public void setTime(String time) {
		this.time = time;
	}

	public Integer getRtng() {
		return rtng;
	}

	public void setRtng(Integer rtng) {
		this.rtng = rtng;
	}

	/**
	 * @return the sid
	 */
	public Integer getSid() {
		return sid;
	}

	/**
	 * @param sid
	 *            the sid to set
	 */
	public void setSid(Integer sid) {
		this.sid = sid;
	}

	/**
	 * @return the sname
	 */
	public String getSname() {
		return sname;
	}

	/**
	 * @param sname
	 *            the sname to set
	 */
	public void setSname(String sname) {
		this.sname = sname;
	}
}