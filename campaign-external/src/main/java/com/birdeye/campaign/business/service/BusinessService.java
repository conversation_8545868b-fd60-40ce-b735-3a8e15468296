package com.birdeye.campaign.business.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.data.domain.Pageable;

import com.birdeye.campaign.dto.BusinessCustomHierarchyLocMappingDetails;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.BusinessMessage;
import com.birdeye.campaign.dto.BusinessBrandingWithResellerInfo;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.dto.LocationInfo;
import com.birdeye.campaign.platform.entity.HtmlTemplates;
import com.birdeye.campaign.platform.readonly.repository.BusinessReadOnlyRepo.BusinessProjection;
import com.birdeye.campaign.request.core.BusinessDataBatchGetRequest;
import com.birdeye.campaign.response.BusinessTokenResponse;
import com.birdeye.campaign.response.LocationCustomFieldResponse;
import com.birdeye.campaign.response.external.AccountPirvacyInfoResponse;
import com.birdeye.campaign.response.external.BizAppsSmsSegmentLimitResponse;
import com.birdeye.campaign.response.external.BulkUserDetailsResponse;
import com.birdeye.campaign.response.external.BusinessFeaturesResponse;
import com.birdeye.campaign.response.external.GetBusinessLiteResponse;
import com.birdeye.campaign.response.external.LocationInfoResponse;
import com.birdeye.campaign.response.external.UserLocationAccessResponse;

/**
 * Service for handling business related requirements
 * 
 * <AUTHOR>
 *
 */
public interface BusinessService {
	
	/**
	 * @param bId
	 * @return
	 */
	BusinessEnterpriseEntity getBusinessById(String bId);
	
	/**
	 * @param type
	 * @return
	 */
	HtmlTemplates getCustomHtmlTemplate(String type);
	
	/**
	 * @param type
	 * @param business
	 * @return
	 */
	String getHtmlTemplate(String type, BusinessEnterpriseEntity business);
	
	/**
	 * @param businessId
	 * @return
	 */
	BusinessEnterpriseEntity getBusinessById(Integer businessId);
	
	public Map<Integer, BusinessEnterpriseEntity> getIdToEnterpriseMapForIds(Set<Integer> businessIds);
	
	public BusinessEnterpriseEntity getWhitelabelReseller(BusinessEnterpriseEntity business);
	
	/**
	 * @param businessNumber
	 * @return
	 */
	BusinessEnterpriseEntity getBusinessByBusinessNumber(Long businessNumber);
	
	
	List<Integer> getLoactionsForEnterprise(Integer enterpriseId) ;

	String getBusinessTimezone(Integer businessId);

	BizAppsSmsSegmentLimitResponse getSmsSegmentLimitForBusiness(Long businessNumber);

	Long getEnterpriseId(Integer businessId);

	List<BusinessEnterpriseEntity> getByBusinessIdsAndOrderByAlias(List<Integer> businessIds);

	BusinessEnterpriseEntity getEnterpriseorSmb(BusinessEnterpriseEntity locationOrSmb);
	
	Set<Integer> getValidBusinessIds(List<Long> destinationEnterpriseIds);

	String getBusinessTimezoneId(Integer businessId);

	List<LocationInfo> getLocationsInfo(Integer enterpriseId) throws Exception;
	
	List<BusinessMessage>  getPaginatedAndSortedByNameActiveAndPaidLocationByEnterpriseId(Integer businessId, Pageable page);

	List<Integer> getActiveLocationCountforEnterpriseId(Integer enterpriseId);

	GetBusinessLiteResponse getBusinessLiteDetails(Integer businessId, Boolean allowCustomData);

	AccountPirvacyInfoResponse getPrivacyPolicyInfo(Integer businessId);

	LocationInfoResponse getLocationInfo(Integer locationId);

	BusinessEnterpriseEntity getValidBusinessById(Integer businessId);
	/**
	 * 
	 * @param userId
	 * @param enterpriseId
	 * @param account
	 * @return
	 * fetching accessible locations to an user for an enterprise
	 * will not work for a SMB
	 */
	CachedCollectionWrapper<Integer> getAccessedLocationsOfAnEnterpriseForUser(Integer userId, Integer enterpriseId, BusinessEnterpriseEntity account);
	
	BusinessCustomHierarchyLocMappingDetails getBusinessHeirarchyData(Integer accountId, String levelAlias);

	List<GetBusinessLiteResponse> getBusinessDataInBatch(BusinessDataBatchGetRequest request, boolean location);

	List<Integer> getAccountIdsAboveOffset(Integer offsetId, Integer batchSize);
	
	LocationCustomFieldResponse getLocationCustomFieldsByEnterpriseId(Integer enterpriseId);
	
	BusinessTokenResponse getStaticLocationCustomFieldsByBusinessId(Integer accountId, Integer businessId);

	UserLocationAccessResponse getUserInfoWithLocationAccess(Integer userId, Integer accountId);

	/***
	 * 
	 * @param userId
	 * @param accountId
	 * @return
	 * fetch bulk user details for enterprise 
	 * will not work for SMB
	 */
	UserLocationAccessResponse getBulkUserInfoWithLocationAccess(List<Integer> userId, Integer accountId);
	
	/**
	 * 
	 * API to fetch user details for user list and given account id.
	 * Note : The limit on the number of user ids passed in the request to the API below is 100.
	 * @param userIdList,
	 *            accountId
	 * 
	 */
	BulkUserDetailsResponse getUserDetailsForUserListAndAccountId(List<Integer> userIdList, Integer accountId);

	Map<Integer, BusinessProjection> getIdToBusinessDetailsMapForIds(Set<Integer> businessIds);

	/***
	 * 
	 * @param businessId
	 * @return
	 * API to fetch business branding along with reseller info
	 * works for both business id and enterprise id
	 * 
	 */
	public BusinessBrandingWithResellerInfo getBusinessBrandingWithResellerInfo(Integer businessId);
	
	BusinessFeaturesResponse getBusinessFeatures(Integer accountId, Long accountNumber, boolean traverseHierarchy);

	Map<Integer, BusinessEnterpriseEntity> getIdToEnterpriseMapForIds(List<Integer> businessIds, Pageable page);
	
}

