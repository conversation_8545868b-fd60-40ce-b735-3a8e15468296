package com.birdeye.campaign.business.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.business.service.BusinessService;
import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.ParametersCache;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.dto.BusinessBrandingWithResellerInfo;
import com.birdeye.campaign.dto.BusinessCustomHierarchyLocMappingDetails;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.BusinessMessage;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.dto.LocationInfo;
import com.birdeye.campaign.external.service.BizAppsExternalService;
import com.birdeye.campaign.external.service.BusinessExternalService;
import com.birdeye.campaign.platform.entity.HtmlTemplates;
import com.birdeye.campaign.platform.readonly.repository.BusinessReadOnlyRepo;
import com.birdeye.campaign.platform.readonly.repository.BusinessReadOnlyRepo.BusinessProjection;
import com.birdeye.campaign.platform.repository.HtmlTemplatesRepo;
import com.birdeye.campaign.request.core.BusinessDataBatchGetRequest;
import com.birdeye.campaign.response.BusinessTokenResponse;
import com.birdeye.campaign.response.LocationCustomFieldResponse;
import com.birdeye.campaign.response.external.AccountPirvacyInfoResponse;
import com.birdeye.campaign.response.external.BizAppsSmsSegmentLimitResponse;
import com.birdeye.campaign.response.external.BulkUserDetailsResponse;
import com.birdeye.campaign.response.external.BusinessFeaturesResponse;
import com.birdeye.campaign.response.external.BusinessOptionsResponse;
import com.birdeye.campaign.response.external.GetBusinessLiteResponse;
import com.birdeye.campaign.response.external.LocationInfoResponse;
import com.birdeye.campaign.response.external.UserLocationAccessResponse;
import com.birdeye.campaign.response.external.UserLocationAccessResponse.UserLocationInfo;
import com.birdeye.campaign.utils.BusinessUtils;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.DateTimeUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;

@Service("businessService")
public class BusinessServiceImpl implements BusinessService {

	private static final Logger			logger	= LoggerFactory.getLogger(BusinessServiceImpl.class);
	
	private static final int		BUSINESS_DATA_GET_BATCH_SIZE	= 50;
	
	private static final String	    AMERICA_LOS_ANGELES	= "America/Los_Angeles";
	
	@Autowired
	private BusinessReadOnlyRepo				businessReadOnlyRepo;
	
	@Autowired
	private HtmlTemplatesRepo			htmlTemplatesRepo;
	
	@Autowired
	private BusinessExternalService businessExternalService;
	
	@Autowired
	private BizAppsExternalService	bizAppsExternalService;
	
	@Qualifier("campaignObjectMapper")
	@Autowired
	private ObjectMapper			campaignObjectMapper;
	
	/**
	 * Get business by ID
	 * 
	 * @param bId
	 * @return
	 */
	@Override
	public BusinessEnterpriseEntity getBusinessById(String bId) {
		if (StringUtils.isBlank(bId)) {
			logger.error("RequestMetadataFilter :: businessId is null");
			return null;
		}
		Integer businessId = null;
		try {
			businessId = Integer.parseInt(bId);
		} catch (NumberFormatException e) {
			logger.error("RequestMetadataFilter :: Exception while parsing businessId for bId {}", bId);
			return null;
		}
		return getBusinessById(businessId);
	}

	/**
	 * @param bId
	 * @param businessId
	 * @return
	 */
	@Override
	public BusinessEnterpriseEntity getBusinessById(Integer businessId) {
		BusinessEnterpriseEntity business = null;
		
		try {
			business = businessReadOnlyRepo.getBusinessLocationByBid(businessId);
		} catch (Exception e) {
			logger.error("RequestMetadataFilter :: Exception while fetching business for bId {}", businessId);
			return null;
		}
		return business;
	}
	
	@Override
	public BusinessEnterpriseEntity getBusinessByBusinessNumber(Long businessNumber) {
		BusinessEnterpriseEntity business = businessReadOnlyRepo.getBusinessByBusinessLongId(businessNumber);
		if (business == null) {
			logger.error("RequestMetadataFilter :: Exception while fetching business for businessNumber {}", businessNumber);
			return null;
		}
		return business;
	}
	
	private BusinessEnterpriseEntity getParentOrEnterpriseOrReseller(BusinessEnterpriseEntity business) {
		if (business.getBusinessParentId() != null) {
			business = businessReadOnlyRepo.getBusinessByBid(business.getBusinessParentId());
		} else if (business.getEnterpriseId() != null) {
			business = businessReadOnlyRepo.getBusinessByBid(business.getEnterpriseId());
		} else if (business.getResellerId() != null) {
			business = businessReadOnlyRepo.getBusinessByBid(business.getResellerId());
		}else {
			business = null;
		}
		return business;
	}
	
	
	@Override
	public HtmlTemplates getCustomHtmlTemplate(String type) {
		HtmlTemplates htmlTemplate = null;
		// look for default custom html template for given type
		List<HtmlTemplates> htmlTemplates = htmlTemplatesRepo.findByTypeDefaultCustomHtml(type, PageRequest.of(0, 1));
		
		if (CollectionUtils.isNotEmpty(htmlTemplates)) {
			htmlTemplate = htmlTemplates.get(0);
		}
		// fall back to default if not found
		if (htmlTemplate == null) {
			logger.info("default custom template not found for type : {}. falling back to default", type);
			htmlTemplates = htmlTemplatesRepo.findbyTypeDefaultNonCustomHtml(type, PageRequest.of(0, 1));
			if (CollectionUtils.isNotEmpty(htmlTemplates)) {
				htmlTemplate = htmlTemplates.get(0);
			}
		}
		return htmlTemplate;
	}
	
	@Override
	@Cacheable(key = "#type.toString().concat('-').concat(#business.id.toString())"  ,value = "businessHtmlTemplateCache1", unless = "#result == null")
	public String getHtmlTemplate(String type, BusinessEnterpriseEntity business) {
		HtmlTemplates htmlTemplate = null;
		List<HtmlTemplates> htmlTemplates = null;
		int i = 0;
		while (business != null && i < Constants.MAX_RESELLER_HIERARCHY_DEPTH) {
			htmlTemplates = htmlTemplatesRepo.findByTypeAndBusinessId(type, business.getId());
			if (CollectionUtils.isNotEmpty(htmlTemplates)) {
				htmlTemplate = htmlTemplates.get(0);
				break;
			}
			business = getParentOrEnterpriseOrReseller(business);
			i++;
		}
		if (htmlTemplate == null) {
			logger.info("No business specific template found for type : {}. picking default html template", type);
			htmlTemplates = htmlTemplatesRepo.findbyTypeDefaultNonCustomHtml(type, PageRequest.of(0, 1));
			if (CollectionUtils.isNotEmpty(htmlTemplates)) {
				htmlTemplate = htmlTemplates.get(0);
			}
		}
		if(htmlTemplate == null) {
			logger.info("No template found for type : {}. no default found, returning null", type);
			return null;
		}
		return htmlTemplate.getContent();
	}
	
	@Override
	public Map<Integer, BusinessEnterpriseEntity> getIdToEnterpriseMapForIds(Set<Integer> businessIds) {
		Map<Integer, BusinessEnterpriseEntity> idToBusinessMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(businessIds)) {
			List<BusinessEnterpriseEntity> enterpriseEntities = businessReadOnlyRepo.getBusinessesByBids(businessIds);
			if (CollectionUtils.isNotEmpty(enterpriseEntities)) {
				for (BusinessEnterpriseEntity enterpriseEntity : enterpriseEntities) {
					idToBusinessMap.put(enterpriseEntity.getId(), enterpriseEntity);
				}
			}
		}
		return idToBusinessMap;
	}
	
	@Override
	public Map<Integer, BusinessEnterpriseEntity> getIdToEnterpriseMapForIds(List<Integer> businessIds, Pageable page) {
		
		Map<Integer, BusinessEnterpriseEntity> idToBusinessMap = new HashMap<>();
		
		if (CollectionUtils.isNotEmpty(businessIds)) {
			Page<BusinessEnterpriseEntity> enterpriseEntities = businessReadOnlyRepo.getPaginatedBusinessesByBids(businessIds, page);
			if (CollectionUtils.isNotEmpty(enterpriseEntities.getContent())) {
				for (BusinessEnterpriseEntity enterpriseEntity : enterpriseEntities) {
					idToBusinessMap.put(enterpriseEntity.getId(), enterpriseEntity);
				}
			}
		}
		
		return idToBusinessMap;
	}
	
	@Override
	public Map<Integer, BusinessProjection> getIdToBusinessDetailsMapForIds(Set<Integer> businessIds) {
		Map<Integer, BusinessProjection> idToBusinessMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(businessIds)) {
			List<BusinessProjection> enterpriseEntities = businessReadOnlyRepo.getBusinessDetails(businessIds);
			if (CollectionUtils.isNotEmpty(enterpriseEntities)) {
				for (BusinessProjection enterpriseEntity : enterpriseEntities) {
					idToBusinessMap.put(enterpriseEntity.getID(), enterpriseEntity);
				}
			}
		}
		return idToBusinessMap;
	}
	
	@Override
	public BusinessEnterpriseEntity getWhitelabelReseller(BusinessEnterpriseEntity business) {
		BusinessEnterpriseEntity whitelabelReseller = null;
		String whiteLabelGroupParameter = CacheManager.getInstance().getCache(ParametersCache.class).getProperty("Whitelabel Partners Group ID");
		if (whiteLabelGroupParameter != null) {
			Integer whiteLabelGroupId = Integer.parseInt(whiteLabelGroupParameter);
			int i = 0;
			while (business != null && i < Constants.MAX_RESELLER_HIERARCHY_DEPTH) {
				if (business.getResellerId() != null && whiteLabelGroupId.equals(business.getResellerId())) {
					whitelabelReseller = business;
					break;
				}
				if (business.getEnterpriseId() != null) {
					business = businessReadOnlyRepo.getBusinessByBid(business.getEnterpriseId());
				} else if (business.getResellerId() != null) {
					business = businessReadOnlyRepo.getBusinessByBid(business.getResellerId());
				}
				i++;
			}
		}
		return whitelabelReseller;
	}

	@Override
	public List<Integer> getLoactionsForEnterprise(Integer enterpriseId) {
		List<Integer> locationIds = businessReadOnlyRepo.findBusinessIdByBusinessIdOrEnterpriseId(enterpriseId);
		// Remove the account id from result
		locationIds.remove(enterpriseId);
		return locationIds;
	}
	
	// Can be removed
	@Override
	@Cacheable(key = "#businessId.toString()", value = "businessTimezoneCache", unless = "#result == null")
	public String getBusinessTimezone(Integer businessId) {
		logger.info("getBusinessTimezone : fetching business timezone from business service for business id {}", businessId);
		GetBusinessLiteResponse businessInfo = businessExternalService.getBusinessLite(businessId, false);
		if (businessInfo == null) {
			logger.error("getBusinessTimezone : business info response empty for business id {}, taking timezone as PST", businessId);
			return null;
		}
		
		// returning PST here only coz returning null would prevent result from being cached, and for most of the cases - timezone returned will be null as
		// null is defaulted as PST
		return DateTimeUtils.convertToProperTimeZone(StringUtils.isNotBlank(businessInfo.getTimeZone()) ? businessInfo.getTimeZone() : Constants.TIMEZONE_PST);
	}
	
	@Override
	@Cacheable(key = "#businessId.toString()", value = "businessTimezoneIdCache", unless = "#result == null")
	public String getBusinessTimezoneId(Integer businessId) {
		logger.info("getBusinessTimezone : fetching business timezone Id from business service for business id {}", businessId);
		GetBusinessLiteResponse businessInfo = businessExternalService.getBusinessLite(businessId, false);
		if (businessInfo == null) {
			logger.error("getBusinessTimezoneId : business info response empty for business id {}, taking timezone as PST", businessId);
			return AMERICA_LOS_ANGELES;
		}
		logger.info("getBusinessTimezone : TimezoneId for business Id {} is {}", businessId, businessInfo.getTimeZoneId());
		// returning PST here only coz returning null would prevent result from being cached, and for most of the cases - timezone returned will be null as
		// null is defaulted as PST
		return (StringUtils.isNotBlank(businessInfo.getTimeZoneId()) && !StringUtils.equalsIgnoreCase(businessInfo.getTimeZoneId(), "PST")) ? businessInfo.getTimeZoneId() : AMERICA_LOS_ANGELES;
		
	}
	
	@Override
	public BizAppsSmsSegmentLimitResponse getSmsSegmentLimitForBusiness(Long businessNumber) {
		return bizAppsExternalService.getSegmentLimitByBusinessId(businessNumber);
	}
	
	@Override
	@Cacheable(key = "#businessId.toString()", value = "businessEnterpriseIdCache", unless = "#result == null")
	public Long getEnterpriseId(Integer businessId) {
		GetBusinessLiteResponse businessInfo = businessExternalService.getBusinessLite(businessId, false);
		if (businessInfo == null) {
			logger.error("getEnterpriseId : business info response empty for business id {}", businessId);
			return null;
		}
		return businessInfo.getEnterpriseId() != null ? businessInfo.getEnterpriseId().longValue() : null;
	}

	@Override
	public List<BusinessEnterpriseEntity> getByBusinessIdsAndOrderByAlias(List<Integer> businessIds) {
		List<BusinessEnterpriseEntity> businesses = businessReadOnlyRepo.getBusinessesByBIdsOrderByAlias(businessIds);
		if (businesses == null) {
			logger.error("no business dound for businessIds {}", businessIds);
			return Collections.emptyList();
		}
		return businesses;
	}
	
	@Override
	public BusinessEnterpriseEntity getEnterpriseorSmb(BusinessEnterpriseEntity locationOrSmb) {
		if(locationOrSmb.getEnterpriseId()!=null) {
			return getBusinessById(locationOrSmb.getEnterpriseId());
		}		
		return locationOrSmb;
	}
		
	
	/**
	 * filter out invalid business where business is not enterprise or smb
	 * 
	 * @param destinationEnterpriseIds
	 * @return
	 */
	@Override
	public Set<Integer> getValidBusinessIds(List<Long> destinationEnterpriseIds) {
		List<List<Long>> businessIdsBatch = Lists.partition(destinationEnterpriseIds, BUSINESS_DATA_GET_BATCH_SIZE);
		BusinessDataBatchGetRequest request = new BusinessDataBatchGetRequest();
		Set<Integer> validBusinessIds = new HashSet<>();
		for (List<Long> businessNumbers : businessIdsBatch) {
			request.setBusinessNumbers(businessNumbers);
			Map<Long, GetBusinessLiteResponse> response = businessExternalService.getBusinessDataInBatch(request, false);
			if (MapUtils.isEmpty(response))
				continue;
			for (Map.Entry<Long, GetBusinessLiteResponse> responseMap : response.entrySet()) {
				GetBusinessLiteResponse data = campaignObjectMapper.convertValue(responseMap.getValue(), GetBusinessLiteResponse.class);
				if (BusinessUtils.isValidEnterprise(data.getType(), data.getAccountType(), 0) || BusinessUtils.isValidSMBBusiness(data.getType(), data.getAccountType(), 0, data.getEnterpriseId()))
					validBusinessIds.add(data.getBusinessId());
			}
		}
		return validBusinessIds;
	}
	
	@Override
	public List<LocationInfo> getLocationsInfo(Integer enterpriseId) throws Exception{
		if(enterpriseId==null) {
			new ArrayList<LocationInfo>();
		}
		try {
			return businessExternalService.getLocationDetailsForAnEnterprise(enterpriseId);
		} catch (Exception e) {
			logger.error("Exception while fetching loction details for enterprise id {} : {}", enterpriseId, ExceptionUtils.getStackTrace(e));
			throw new Exception("Exception while getting location info for an enterprise");
		}
	}

	@Override
	public List<BusinessMessage> getPaginatedAndSortedByNameActiveAndPaidLocationByEnterpriseId(Integer enterpriseId, Pageable page) {
	
		if(enterpriseId==null || page ==null) {
			return Collections.emptyList();
		}
		try {
		return businessReadOnlyRepo.findPaginatedAndSortedByNameActiveAndPaidLocationIdsByEnterpriseId(enterpriseId, page).getContent();
		}catch(Exception e){
			logger.error("Exception while fetching business ids for enterprise id {} : {}", enterpriseId, ExceptionUtils.getStackTrace(e));
		}
		return Collections.emptyList();
	}
	
	@Override
	public List<Integer> getActiveLocationCountforEnterpriseId(Integer enterpriseId) {
	
		if(enterpriseId==null ) {
			return Collections.emptyList();
		}
		try {
		return businessReadOnlyRepo.getActiveBusinessCountforEnterpriseId(enterpriseId);
		}catch(Exception e){
			logger.error("Exception while fetching business count for enterprise id {} :: {}", enterpriseId, ExceptionUtils.getStackTrace(e));
		}
		return Collections.emptyList();
	}

	@Override
	@Cacheable(key = "#businessId.toString().concat('-').concat(#allowCustomData.toString())", value = "businessLiteCacheWithCustomData", unless = "#result == null")
	public GetBusinessLiteResponse getBusinessLiteDetails(Integer businessId, Boolean allowCustomData) {
		GetBusinessLiteResponse businessInfo = businessExternalService.getBusinessLite(businessId, allowCustomData);
		if (businessInfo == null) {
			logger.error("business info response empty for business id {}", businessId);
			return null;
		}
		return businessInfo;
	}
	
	@Override
	@Cacheable(value = "accountPrivacyPolicyCache", key = "#businessId", unless = "#result==null")
	public AccountPirvacyInfoResponse getPrivacyPolicyInfo(Integer businessId) {
		return getPrivacyPolicyInfo(businessExternalService.getBusinessOptions(businessId, true));
	}
	
	private AccountPirvacyInfoResponse getPrivacyPolicyInfo(BusinessOptionsResponse businessOptions) {
		if (businessOptions == null)
			return null;
		AccountPirvacyInfoResponse response = new AccountPirvacyInfoResponse();
		response.setBusinessId(businessOptions.getBusinessId());
		response.setPrivacyUrl(businessOptions.getPrivacyUrl());
		response.setTermsServiceUrl(businessOptions.getTermsUrl());
		return response;
	}
	
	@Override
	@Cacheable(value = "locationInfoCache", key = "#locationId", unless = "#result == null")
	public LocationInfoResponse getLocationInfo(Integer locationId) {
		LocationInfoResponse locationInfo = businessExternalService.getLocationInfo(locationId);
		if (locationInfo == null) {
			logger.error("location info response empty for location id {}", locationInfo);
			return null;
		}
		return locationInfo;
	}
	
	@Override
	public BusinessEnterpriseEntity getValidBusinessById(Integer businessId) {
		return businessReadOnlyRepo.getValidBusinessByBid(businessId);
	}
	
	@Cacheable(value="userLocationsAccessForEnterprise", key = "#enterpriseId.toString().concat('-').concat(#userId.toString())", unless="#result == null")
	@Override
	public CachedCollectionWrapper<Integer> getAccessedLocationsOfAnEnterpriseForUser(Integer userId, Integer enterpriseId, BusinessEnterpriseEntity account) {
		if (userId == null || account == null) {
			logger.info("invalid request for enterpriseId {} and userId {}", enterpriseId, userId);
			return null;
		}
		if (BusinessUtils.isValidSMBBusiness(account)) {
			return new CachedCollectionWrapper<Integer>(Collections.singletonList(enterpriseId));
		}
		logger.info("fetching user accessed location for user id {} and enterprise id {}", userId, enterpriseId);
		UserLocationAccessResponse usersDetails = businessExternalService.getUserAccessedLocationForAnEnterprise(Collections.singletonList(userId) , enterpriseId);
		logger.info("received response for user access location for account id {} user id {}", enterpriseId, userId);
		if (usersDetails != null && CollectionUtils.isNotEmpty(usersDetails.getUsers())) {
			UserLocationInfo user = usersDetails.getUsers().get(0);
			return new CachedCollectionWrapper<Integer>(user.getLocationIds());
		}
		return null;
	}
	
	@Override
	public UserLocationAccessResponse getUserInfoWithLocationAccess(Integer userId, Integer accountId) {
		if (userId == null || accountId == null) {
			logger.error("getUserDetails :: Invalid request received to fetch user info with location access for account id {} and user id {}", accountId, userId);
			return null;
		}
		logger.info("getUserInfoWithLocationAccess :: fetching user info with location access for user id {} and account id {}", userId, accountId);
		UserLocationAccessResponse usersDetails = businessExternalService.getUserAccessedLocationForAnEnterprise(Collections.singletonList(userId), accountId);
		return (usersDetails == null || CollectionUtils.isEmpty(usersDetails.getUsers())) ? null : usersDetails;
	}
	
	@Override
	public UserLocationAccessResponse getBulkUserInfoWithLocationAccess(List<Integer> userIds, Integer accountId) {
 
		if (CollectionUtils.isEmpty(userIds) || accountId == null) {
			logger.error("getUserDetails :: Invalid request received to fetch user info with location access for account id {} and user id {}", accountId, userIds);
			return null;
		}
		logger.info("getUserInfoWithLocationAccess :: fetching user info with location access for user ids {} and account id {}", userIds, accountId);
		return businessExternalService.getUserAccessedLocationForAnEnterprise(userIds, accountId);
	}

//	@Cacheable(value="businessHeirarchyData", key = "#accountId.toString().concat('-').concat(levelAlias)", unless="#result == null")
	@Override
	public BusinessCustomHierarchyLocMappingDetails getBusinessHeirarchyData(Integer accountId, String levelAlias) {
		return businessExternalService.getBusinessHeirarchyData(accountId, levelAlias);
	
	}

	@Override
	public List<GetBusinessLiteResponse> getBusinessDataInBatch(BusinessDataBatchGetRequest request, boolean location) {
		
		return businessExternalService.getBusinessDataInBatch(request, location).values().stream().collect(Collectors.toList());
	}
	
	@Override
	public List<Integer> getAccountIdsAboveOffset(Integer offsetId, Integer batchSize) {
		return businessReadOnlyRepo.getAccountIdsAboveOffset(offsetId, PageRequest.of(0, batchSize, Sort.by("id").ascending()));
	}
	
	@Override
	public LocationCustomFieldResponse getLocationCustomFieldsByEnterpriseId(Integer enterpriseId) {
		return businessExternalService.getLocationCustomFieldsByAccountId(enterpriseId);
	}
	
	@Override
	@Cacheable(value = "businessStaticToken", key = "#businessId", unless = "#result == null")
	public BusinessTokenResponse getStaticLocationCustomFieldsByBusinessId(Integer accountId, Integer businessId) {
		logger.info("fetching static business token for business {}", businessId);
		
		BusinessTokenResponse response = null;
		try {
			response = businessExternalService.getLocationStaticCustomFields(accountId, businessId);
			
			logger.info("static business token for business {}: {}", businessId, response);
		} catch (Exception e) {
			logger.error("received error in fetching static business token for business {}: {}", businessId, ExceptionUtils.getStackTrace(e));
		}
		return response;
	}

	@Override
	public BulkUserDetailsResponse getUserDetailsForUserListAndAccountId(List<Integer> userIdList, Integer accountId) {
		if (CollectionUtils.isEmpty(userIdList) || accountId == null) {
			logger.error("getUserDetails :: Invalid request received to fetch user info with location access for account id {} and user id {}", accountId, userIdList);
			return null;
		}
		logger.info("getUserInfoWithLocationAccess :: fetching user info with location access for user ids {} and account id {}", userIdList, accountId);
		return businessExternalService.getUserDetailsForUserListAndAccountId(userIdList, accountId);
	}

	@Override
	@Cacheable(value = "businessBrandingWithResellerInfoCache", key = "#businessId", unless = "#result == null")
	public BusinessBrandingWithResellerInfo getBusinessBrandingWithResellerInfo(Integer businessId) {
		if(BooleanUtils.isFalse(CoreUtils.isTrueForInteger(businessId))) {
			return null;
		}
		return businessExternalService.getBusinessBrandingWithResellerInfo(businessId);
	}
	
	@Override
	public BusinessFeaturesResponse getBusinessFeatures(Integer accountId, Long accountNumber, boolean traverseHierarchy) {
		if (accountId == null && accountNumber == null) {
			logger.error("getBusinessFeatures :: Invalid request received to fetch business features as account id and account number can't be null");
			return null;
		}
		logger.info("getBusinessFeatures :: fetching business features for account id {} and account number {}", accountId, accountNumber);
		return businessExternalService.getBusinessFeatures(accountId, accountNumber, traverseHierarchy);
	}
}
