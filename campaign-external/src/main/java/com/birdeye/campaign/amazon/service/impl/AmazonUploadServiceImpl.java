package com.birdeye.campaign.amazon.service.impl;

import java.io.FileNotFoundException;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Service;

import com.amazonaws.services.s3.AmazonS3;
import com.birdeye.campaign.amazon.service.AWSService;
import com.birdeye.campaign.amazon.service.AmazonUploadService;
import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.ParametersCache;
import com.birdeye.campaign.utils.CampaignUtils;

@Service("awsuploadService")
public class AmazonUploadServiceImpl implements AmazonUploadService {

	private static final Logger logger = LoggerFactory.getLogger(AmazonUploadServiceImpl.class);
	
	@Autowired
	private AmazonS3	amazonS3;
	
	@Autowired
	private AWSService awsService;
	
	@Override
	public String uploadFileAndGetDownloadLink(String filePath ,String fileName ,  String extension) {
		if(StringUtils.isBlank(filePath) || StringUtils.isBlank(fileName)) {
			throw new IllegalArgumentException("FilePath cannot be null while performing upload");
		}
		fileName = CampaignUtils.removeSpecialCharacters(fileName);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		String dateNow = sdf.format(new Date());
		String bucket = CacheManager.getInstance().getCache(ParametersCache.class).getProperty("cdn_bucket", "bzimages-prod");
		StringBuilder s3ExcelUrl = new StringBuilder();
		String cdnBaseUrl = CacheManager.getInstance().getCache(ParametersCache.class).getProperty("cdn_image_base_url", "https://ddjkm7nmu27lx.cloudfront.net");
		if(StringUtils.isNotBlank(cdnBaseUrl)) {
			s3ExcelUrl.append(cdnBaseUrl).append("/");
		}else {
			s3ExcelUrl.append("https://").append(bucket).append(".s3.amazonaws.com/");
		}
		s3ExcelUrl.append("fileUpload/").append(dateNow).append("/");
		try {
			String hash = performUpload(bucket, null, StringUtils.join("fileUpload/", dateNow, "/"), filePath, fileName , extension);
			s3ExcelUrl.append(hash).append(extension);
		} catch (FileNotFoundException e) {
			logger.error("Exception occured while perfoming upload of file : {} at path : {}",fileName , filePath);
		}
		return s3ExcelUrl.toString();
	}

	private String performUpload(String bucket,String subBucket, String keyPrefix, String uploadFilePath ,String fileName , String extension) throws FileNotFoundException {
		String contentType = "application/vnd.ms-excel";
		// check if the bucket exists. if not then create the bucket
		if (!amazonS3.doesBucketExistV2(bucket)) {
			logger.info("bucket already exists {}", bucket);
			amazonS3.createBucket(bucket);
		}
		if(StringUtils.isBlank(subBucket)) {
			subBucket = keyPrefix;
		}else {
			subBucket += "/" + keyPrefix;
		}
		String keyName = subBucket + fileName + extension;
		// upload and delete local file - async operation
		awsService.upload(bucket, keyName, uploadFilePath, false, contentType);

		return fileName;
	}
}
