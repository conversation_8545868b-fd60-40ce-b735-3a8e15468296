package com.birdeye.campaign.request.external;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.sro.ReviewSourceSRO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class QrReviewSourcesResponse implements Serializable {
	
	private static final long		serialVersionUID	= -662771811915158869L;
	private Integer					templateId;
	private List<ReviewSourceSRO>	selectedSources;
	private List<ReviewSourceSRO>	availableSources;
	
	public Integer getTemplateId() {
		return templateId;
	}
	
	public void setTemplateId(Integer templateId) {
		this.templateId = templateId;
	}
	
	public List<ReviewSourceSRO> getSelectedSources() {
		return selectedSources;
	}
	
	public void setSelectedSources(List<ReviewSourceSRO> selectedSources) {
		this.selectedSources = selectedSources;
	}
	
	public List<ReviewSourceSRO> getAvailableSources() {
		return availableSources;
	}
	
	public void setAvailableSources(List<ReviewSourceSRO> availableSources) {
		this.availableSources = availableSources;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
