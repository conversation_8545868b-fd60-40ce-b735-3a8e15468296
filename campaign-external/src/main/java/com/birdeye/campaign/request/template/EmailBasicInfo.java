package com.birdeye.campaign.request.template;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
public class EmailBasicInfo implements Serializable{
	private static final long serialVersionUID = 214737159290741387L;
	private String email;
	private String name;
	
	public EmailBasicInfo(String email, String name) {
		this.email=email;
		this.name=name;
	}
	
	public EmailBasicInfo(String email) {
		this.email=email;
	}
	
	public String getEmail() {
		return email;
	}
	
	public void setEmail(String email) {
		this.email = email;
	}
	
	public String getName() {
		return name;
	}
	
	public void setName(String name) {
		this.name = name;
	}
	
	public String toString() {
        ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
        return b.toString();
    }
}
