/**
 * @file_name CustomerExistsRequest.java
 * @created_date 13 Aug 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.request.external;

import java.io.Serializable;

/**
 * @file_name CustomerExistsRequest.java
 * @created_date 13 Aug 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public class CustomerExistsRequest implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 4221911125510121075L;
	
	private String				email;
	private String				phone;
	
	/**
	 * 
	 */
	public CustomerExistsRequest() {
		super();
	}

	/**
	 * @param email
	 * @param phone
	 */
	public CustomerExistsRequest(String email, String phone) {
		super();
		this.email = email;
		this.phone = phone;
	}

	/**
	 * @return the email
	 */
	public String getEmail() {
		return email;
	}
	
	/**
	 * @param email
	 *            the email to set
	 */
	public void setEmail(String email) {
		this.email = email;
	}
	
	/**
	 * @return the phone
	 */
	public String getPhone() {
		return phone;
	}
	
	/**
	 * @param phone
	 *            the phone to set
	 */
	public void setPhone(String phone) {
		this.phone = phone;
	}
	
	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("CustomerExistsRequest [email=");
		builder.append(email);
		builder.append(", phone=");
		builder.append(phone);
		builder.append("]");
		return builder.toString();
	}
	
}
