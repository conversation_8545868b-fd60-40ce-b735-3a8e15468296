package com.birdeye.campaign.request.template;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class LocationReviewgenRequisitesRequest implements Serializable {

	private static final long serialVersionUID = -3976139233268618616L;
	
	private Set<Integer> supportedSourceIds = new HashSet<>();
	private Set<Integer> nonSupportedSourceIds = new HashSet<>();

	public Set<Integer> getSupportedSourceIds() {
		return supportedSourceIds;
	}

	public void setSupportedSourceIds(Set<Integer> supportedSourceIds) {
		this.supportedSourceIds.addAll(supportedSourceIds);
	}

	public Set<Integer> getNonSupportedSourceIds() {
		return nonSupportedSourceIds;
	}

	public void setNonSupportedSourceIds(Set<Integer> nonSupportedSourceIds) {
		this.nonSupportedSourceIds.addAll(nonSupportedSourceIds);
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
