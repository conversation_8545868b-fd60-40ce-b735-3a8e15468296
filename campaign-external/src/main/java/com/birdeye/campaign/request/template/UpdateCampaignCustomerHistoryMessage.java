package com.birdeye.campaign.request.template;

import java.io.Serializable;

/**
 * Wrapper message to update customer history in platform
 * <AUTHOR>
 */
public class UpdateCampaignCustomerHistoryMessage implements Serializable{

	private static final long serialVersionUID = 7240736630923956273L;

	private Long requestId;
	
	private Integer customerId;
	
	private String activityType;
	
	private String eventName;
	
	private String eventDesc;
	
	private Boolean promotionRequest = false;


	public Long getRequestId() {
		return requestId;
	}

	public void setRequestId(Long requestId) {
		this.requestId = requestId;
	}

	public String getActivityType() {
		return activityType;
	}

	public void setActivityType(String activityType) {
		this.activityType = activityType;
	}

	public String getEventDesc() {
		return eventDesc;
	}

	public void setEventDesc(String eventDesc) {
		this.eventDesc = eventDesc;
	}
	
	public Boolean getPromotionRequest() {
		return promotionRequest;
	}

	public void setPromotionRequest(Boolean promotionRequest) {
		this.promotionRequest = promotionRequest;
	}

	public String getEventName() {
		return eventName;
	}

	public void setEventName(String eventName) {
		this.eventName = eventName;
	}
	
	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	@Override
	public String toString() {
		return "UpdateCampaignCustomerHistoryMessage [requestId=" + requestId + ", customerId=" + customerId
				+ ", activityType=" + activityType + ", eventName=" + eventName + ", eventDesc=" + eventDesc
				+ ", isPromotionRequest=" + promotionRequest + "]";
	}

}
