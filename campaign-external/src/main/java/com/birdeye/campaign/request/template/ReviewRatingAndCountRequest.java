/**
 *
 */
package com.birdeye.campaign.request.template;

import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReviewRatingAndCountRequest {
	
	private List<Integer>	locationIds;
	private long			startDate;
	private long			endDate;
	
	public ReviewRatingAndCountRequest() {
	}
	
	public ReviewRatingAndCountRequest(List<Integer> businessIds, long fromDate, long toDate) {
		this.locationIds = businessIds;
		this.startDate = fromDate;
		this.endDate = toDate;
	}
	
	public List<Integer> getLocationIds() {
		return locationIds;
	}
	
	public void setLocationIds(List<Integer> locationIds) {
		this.locationIds = locationIds;
	}
	
	public long getStartDate() {
		return startDate;
	}
	
	public void setStartDate(long startDate) {
		this.startDate = startDate;
	}
	
	public long getEndDate() {
		return endDate;
	}
	
	public void setEndDate(long endDate) {
		this.endDate = endDate;
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder sb = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return sb.toString();
	}
}
