package com.birdeye.campaign.request.template;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> Request Object to Update Campaign Customer Fields Via Platform API
 *
 */
public class UpdateCampaignCustomerRequest implements Serializable {
	
	private static final long	serialVersionUID	= -8926333714193038322L;
	
	private Date				rrEmailOn;
	
	private Date				rrSmsOn;
	
	private Date				cxEmailOn;
	
	private Date				cxSmsOn;
	
	private Date				promotionEmailOn;
	
	private Date				referralEmailOn;
	
	private Date				referralSmsOn;
	
	private Date				surveyEmailOn;
	
	private Date				surveySmsOn;
	
	private Integer			customerId;
	
	private Date				promotionSmsOn;
	
	private String				source;
	
	public Date getRrEmailOn() {
		return rrEmailOn;
	}
	
	public void setRrEmailOn(Date rrEmailOn) {
		this.rrEmailOn = rrEmailOn;
	}
	
	public Date getRrSmsOn() {
		return rrSmsOn;
	}
	
	public void setRrSmsOn(Date rrSmsOn) {
		this.rrSmsOn = rrSmsOn;
	}
	
	public Date getCxEmailOn() {
		return cxEmailOn;
	}
	
	public void setCxEmailOn(Date cxEmailOn) {
		this.cxEmailOn = cxEmailOn;
	}
	
	public Date getCxSmsOn() {
		return cxSmsOn;
	}
	
	public void setCxSmsOn(Date cxSmsOn) {
		this.cxSmsOn = cxSmsOn;
	}
	
	public Date getPromotionEmailOn() {
		return promotionEmailOn;
	}
	
	public void setPromotionEmailOn(Date promotionEmailOn) {
		this.promotionEmailOn = promotionEmailOn;
	}
	
	public Integer getCustomerId() {
		return customerId;
	}
	
	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}
	
	/**
	 * @return the referralEmailOn
	 */
	public Date getReferralEmailOn() {
		return referralEmailOn;
	}
	
	/**
	 * @param referralEmailOn
	 *            the referralEmailOn to set
	 */
	public void setReferralEmailOn(Date referralEmailOn) {
		this.referralEmailOn = referralEmailOn;
	}
	
	/**
	 * @return the referralSmsOn
	 */
	public Date getReferralSmsOn() {
		return referralSmsOn;
	}
	
	/**
	 * @param referralSmsOn
	 *            the referralSmsOn to set
	 */
	public void setReferralSmsOn(Date referralSmsOn) {
		this.referralSmsOn = referralSmsOn;
	}
	
	/**
	 * @return the surveyEmailOn
	 */
	public Date getSurveyEmailOn() {
		return surveyEmailOn;
	}

	/**
	 * @param surveyEmailOn the surveyEmailOn to set
	 */
	public void setSurveyEmailOn(Date surveyEmailOn) {
		this.surveyEmailOn = surveyEmailOn;
	}

	/**
	 * @return the surveySmsOn
	 */
	public Date getSurveySmsOn() {
		return surveySmsOn;
	}

	/**
	 * @param surveySmsOn the surveySmsOn to set
	 */
	public void setSurveySmsOn(Date surveySmsOn) {
		this.surveySmsOn = surveySmsOn;
	}
	
	public Date getPromotionSmsOn() {
		return promotionSmsOn;
	}

	public void setPromotionSmsOn(Date promotionSmsOn) {
		this.promotionSmsOn = promotionSmsOn;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("UpdateCampaignCustomerRequest [rrEmailOn=");
		builder.append(rrEmailOn);
		builder.append(", rrSmsOn=");
		builder.append(rrSmsOn);
		builder.append(", cxEmailOn=");
		builder.append(cxEmailOn);
		builder.append(", cxSmsOn=");
		builder.append(cxSmsOn);
		builder.append(", promotionEmailOn=");
		builder.append(promotionEmailOn);
		builder.append(", referralEmailOn=");
		builder.append(referralEmailOn);
		builder.append(", referralSmsOn=");
		builder.append(referralSmsOn);
		builder.append(", surveyEmailOn=");
		builder.append(surveyEmailOn);
		builder.append(", surveySmsOn=");
		builder.append(surveySmsOn);
		builder.append(", customerId=");
		builder.append(customerId);
		builder.append(", promotionSmsOn=");
		builder.append(promotionSmsOn);
		builder.append("]");
		return builder.toString();
	}

}
