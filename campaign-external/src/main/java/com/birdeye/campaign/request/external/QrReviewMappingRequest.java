package com.birdeye.campaign.request.external;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.sro.ReviewSourceSRO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_NULL)
public class QrReviewMappingRequest implements Serializable {
	
	private static final long		serialVersionUID	= -9035250389226048711L;
	
	private Integer 				templateId;
	private Integer					accountId;
	private List<ReviewSourceSRO>	selectedSources;
	
	
	public QrReviewMappingRequest() {
		super();
	}
	
	public QrReviewMappingRequest(Integer accountId, List<ReviewSourceSRO> selectedSources) {
		super();
		this.accountId = accountId;
		this.selectedSources = selectedSources;
	}

	public Integer getTemplateId() {
		return templateId;
	}

	public void setTemplateId(Integer templateId) {
		this.templateId = templateId;
	}

	public Integer getAccountId() {
		return accountId;
	}
	
	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}
	
	public List<ReviewSourceSRO> getSelectedSources() {
		return selectedSources;
	}
	
	public void setSelectedSources(List<ReviewSourceSRO> selectedSources) {
		this.selectedSources = selectedSources;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
