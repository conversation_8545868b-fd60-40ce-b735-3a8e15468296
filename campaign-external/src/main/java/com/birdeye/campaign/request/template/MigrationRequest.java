package com.birdeye.campaign.request.template;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class MigrationRequest implements Serializable {
	
	private static final long	serialVersionUID	= -6072923075956079018L;
	
	private Long				startId;
	
	private Long				endId;
	
	public Long getStartId() {
		return startId;
	}
	
	public void setStartId(Long startId) {
		this.startId = startId;
	}
	
	public Long getEndId() {
		return endId;
	}
	
	public void setEndId(Long endId) {
		this.endId = endId;
	}
	
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("RequestDateMigrationRequest [startId=");
		builder.append(startId);
		builder.append(", endId=");
		builder.append(endId);
		builder.append("]");
		return builder.toString();
	}
	
	public MigrationRequest(Long startId, Long endId) {
		super();
		this.startId = startId;
		this.endId = endId;
	}
	
	public MigrationRequest() {
		
	}
	
}
