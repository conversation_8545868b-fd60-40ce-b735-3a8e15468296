package com.birdeye.campaign.request.template;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


public class NexusEmailInputMessage<T> implements Serializable {
	
	private static final long		serialVersionUID	= 1L;
	
	private NexusEmailGenericData	emailGenericData;
	
	private String					emailBody;
	
	private T		dataObject;
	
	private NexusEmailMetaData		emailMetaData;
	// emailBody is mandatory when ampEmailBody is provided
	private String					ampEmailBody;
	
	private boolean					freeTrialProductEmail;
	
	public static class NexusEmailMetaData implements Serializable {
		
		private static final long	serialVersionUID	= 1L;
		
		private String				emailType;
		
		private String				emailSubType;
		
		private String	ampEmailSubType;
		
		private String				externalUid;
		
		private Integer				businessId;
		
		private String				htmlTemplateType;
		
		private boolean				applyBranding;
		
		private boolean				isCustomTemplate;
		
		private String				requestId;
		
		private String				recipientType;				// to identify the sender's email and name. Permissible values - customer, business_user, birdeye_internal, others
		
		private Integer				customerId; 
		
		private String				emailCategory;
		
		private Integer				isCustomerEmail;
		
		public String getEmailType() {
			return emailType;
		}
		
		public void setEmailType(String type) {
			this.emailType = type;
		}
		
		public String getEmailSubType() {
			return emailSubType;
		}
		
		public void setEmailSubType(String subType) {
			this.emailSubType = subType;
		}
		
		public Integer getBusinessId() {
			return businessId;
		}
		
		public void setBusinessId(Integer businessId) {
			this.businessId = businessId;
		}
		
		public String getExternalUid() {
			return externalUid;
		}
		
		public void setExternalUid(String externalUid) {
			this.externalUid = externalUid;
		}
		
		public String getHtmlTemplateType() {
			return htmlTemplateType;
		}
		
		public void setHtmlTemplateType(String htmlTemplateType) {
			this.htmlTemplateType = htmlTemplateType;
		}
		
		public boolean isApplyBranding() {
			return applyBranding;
		}
		
		public void setApplyBranding(boolean applyBranding) {
			this.applyBranding = applyBranding;
		}
		
		public boolean isCustomTemplate() {
			return isCustomTemplate;
		}
		
		public void setCustomTemplate(boolean isCustomTemplate) {
			this.isCustomTemplate = isCustomTemplate;
		}
		
		public String getRequestId() {
			return requestId;
		}
		
		public void setRequestId(String requestId) {
			this.requestId = requestId;
		}

		/**
		 * @return the ampEmailSubType
		 */
		public String getAmpEmailSubType() {
			return ampEmailSubType;
		}

		/**
		 * @param ampEmailSubType the ampEmailSubType to set
		 */
		public void setAmpEmailSubType(String ampEmailSubType) {
			this.ampEmailSubType = ampEmailSubType;
		}

		public String getRecipientType() {
			return recipientType;
		}

		public void setRecipientType(String recipientType) {
			this.recipientType = recipientType;
		}

		
		public Integer getCustomerId() {
			return customerId;
		}

		public void setCustomerId(Integer customerId) {
			this.customerId = customerId;
		}

		/**
		 * @return the emailCategory
		 */
		public String getEmailCategory() {
			return emailCategory;
		}

		/**
		 * @param emailCategory the emailCategory to set
		 */
		public void setEmailCategory(String emailCategory) {
			this.emailCategory = emailCategory;
		}

		/**
		 * @return the isCustomerEmail
		 */
		public Integer getIsCustomerEmail() {
			return isCustomerEmail;
		}

		/**
		 * @param isCustomerEmail the isCustomerEmail to set
		 */
		public void setIsCustomerEmail(Integer isCustomerEmail) {
			this.isCustomerEmail = isCustomerEmail;
		}

		@Override
		public String toString() {
			return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
		}
		
	}
	
	public static class NexusEmailGenericData implements Serializable {
		
		private static final long	serialVersionUID	= 1L;
		private List<String>		to					= new ArrayList<>();
		private List<String>		bcc					= new ArrayList<>();
		private Map<String, File>	attachments			= new HashMap<>();
		private String				subject;
		private String				from;
		private String				replyTo;
		private String				fromName;
		private String				body;
		private String				replyToName;
		private List<EmailBasicInfo>  replyToList;
		// scheduled time in epoch
		private long				scheduleTime;
		
		private List<EmailFileAttachementData> fileAttachementData;
		
		public enum TEXT_TYPE {
			HTML, TEXT, AMP
		};
		
		private TEXT_TYPE textType = TEXT_TYPE.HTML;
		
		public List<String> getTo() {
			return to;
		}
		
		public void setTo(List<String> to) {
			this.to = to;
		}
		
		public List<String> getBcc() {
			return bcc;
		}
		
		public void setBcc(List<String> bcc) {
			this.bcc = bcc;
		}
		
		public Map<String, File> getAttachments() {
			return attachments;
		}
		
		public void setAttachments(Map<String, File> attachments) {
			this.attachments = attachments;
		}
		
		public String getSubject() {
			return subject;
		}
		
		public void setSubject(String subject) {
			this.subject = subject;
		}
		
		public String getFrom() {
			return from;
		}
		
		public void setFrom(String from) {
			this.from = from;
		}
		
		public String getReplyTo() {
			return replyTo;
		}
		
		public void setReplyTo(String replyTo) {
			this.replyTo = replyTo;
		}
		
		public String getFromName() {
			return fromName;
		}
		
		public void setFromName(String fromName) {
			this.fromName = fromName;
		}
		
		public void clearAndAddTo(String email) {
			this.to = new ArrayList<>();
			this.to.add(email);
		}
		
		public void addTo(String to) {
			this.to.add(to);
		}
		
		public long getScheduleTime() {
			return scheduleTime;
		}
		
		public void setScheduleTime(long scheduleTime) {
			this.scheduleTime = scheduleTime;
		}
		
		public String getBody() {
			return body;
		}
		
		public void setBody(String body) {
			this.body = body;
		}
		
		public List<EmailBasicInfo> getReplyToList(){
			return replyToList;
		}
		
		public void setReplyToList(List<EmailBasicInfo> replyToList) {
			this.replyToList = replyToList;
		}
		
		public String getReplyToName() {
			return replyToName;
		}
		
		public void setReplyToName(String replyToName) {
			this.replyToName = replyToName;
		}
		
		/**
		 * @return the textType
		 */
		public TEXT_TYPE getTextType() {
			return textType;
		}

		/**
		 * @param textType the textType to set
		 */
		public void setTextType(TEXT_TYPE textType) {
			this.textType = textType;
		}

		public List<EmailFileAttachementData> getFileAttachementData() {
			return fileAttachementData;
		}

		public void setFileAttachementData(List<EmailFileAttachementData> fileAttachementData) {
			this.fileAttachementData = fileAttachementData;
		}

		/* (non-Javadoc)
		 * @see java.lang.Object#toString()
		 */
		@Override
	    public String toString() {
	        ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
	        return b.toString();
	    }

	}
	
	
	public NexusEmailGenericData getEmailGenericData() {
		return emailGenericData;
	}
	
	public void setEmailGenericData(NexusEmailGenericData emailGenericData) {
		this.emailGenericData = emailGenericData;
	}
	
	public String getEmailBody() {
		return emailBody;
	}
	
	public void setEmailBody(String emailBody) {
		this.emailBody = emailBody;
	}
	
	public T getDataObject() {
		return dataObject;
	}
	
	public void setDataObject(T dataObject) {
		this.dataObject = dataObject;
	}
	
	public NexusEmailMetaData getEmailMetaData() {
		return emailMetaData;
	}
	
	public void setEmailMetaData(NexusEmailMetaData emailMetaData) {
		this.emailMetaData = emailMetaData;
	}
	
	/**
	 * @return the ampEmailBody
	 */
	public String getAmpEmailBody() {
		return ampEmailBody;
	}

	/**
	 * @param ampEmailBody the ampEmailBody to set
	 */
	public void setAmpEmailBody(String ampEmailBody) {
		this.ampEmailBody = ampEmailBody;
	}

	/**
	 * @return the freeTrialProductEmail
	 */
	public boolean isFreeTrialProductEmail() {
		return freeTrialProductEmail;
	}

	/**
	 * @param freeTrialProductEmail the freeTrialProductEmail to set
	 */
	public void setFreeTrialProductEmail(boolean freeTrialProductEmail) {
		this.freeTrialProductEmail = freeTrialProductEmail;
	}
	

	

}