package com.birdeye.campaign.request.external;

import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AppointmentBulkRequest {
	
	private List<Integer> appointmentIds;
	
	public AppointmentBulkRequest(List<Integer> appointmentIds) {
		this.appointmentIds = appointmentIds;
	}
	
	public List<Integer> getAppointmentIds() {
		return appointmentIds;
	}
	
	public void setAppointmentIds(List<Integer> appointmentIds) {
		this.appointmentIds = appointmentIds;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
