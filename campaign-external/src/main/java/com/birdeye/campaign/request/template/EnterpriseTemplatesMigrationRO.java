package com.birdeye.campaign.request.template;

/**
 * Request object for enterprise templates migration to new campaign flow.
 * 
 * <AUTHOR>
 *
 */
public class EnterpriseTemplatesMigrationRO extends EnterpriseTemplatesMigrationBasicRO {
	
	private static final long serialVersionUID = -6644239204645956596L;

	private Integer				ongoingEmailTemplateId;
	
	private Integer				ongoingSmsTemplateId;
	
	private Integer				campaignStatus;
	
	private Integer				enableUnsubscribeText;
	
	private Integer				surveyId;
	
	private Integer				contactUsEnabled;
	
	private Integer				enableLocationBranding;
	
	private Integer				enableMms;
	
	private Integer				noReplyEnabled;
	
	public Integer getCampaignStatus() {
		return campaignStatus;
	}

	public void setCampaignStatus(Integer campaignStatus) {
		this.campaignStatus = campaignStatus;
	}

	public Integer getOngoingEmailTemplateId() {
		return ongoingEmailTemplateId;
	}

	public void setOngoingEmailTemplateId(Integer ongoingEmailTemplateId) {
		this.ongoingEmailTemplateId = ongoingEmailTemplateId;
	}

	public Integer getOngoingSmsTemplateId() {
		return ongoingSmsTemplateId;
	}

	public void setOngoingSmsTemplateId(Integer ongoingSmsTemplateId) {
		this.ongoingSmsTemplateId = ongoingSmsTemplateId;
	}

	public Integer getSurveyId() {
		return surveyId;
	}

	public void setSurveyId(Integer surveyId) {
		this.surveyId = surveyId;
	}

	public Integer getContactUsEnabled() {
		return contactUsEnabled;
	}

	public void setContactUsEnabled(Integer contactUsEnabled) {
		this.contactUsEnabled = contactUsEnabled;
	}

	public Integer getEnableUnsubscribeText() {
		return enableUnsubscribeText;
	}

	public void setEnableUnsubscribeText(Integer enableUnsubscribeText) {
		this.enableUnsubscribeText = enableUnsubscribeText;
	}

	public Integer getEnableLocationBranding() {
		return enableLocationBranding;
	}

	public void setEnableLocationBranding(Integer enableLocationBranding) {
		this.enableLocationBranding = enableLocationBranding;
	}

	public Integer getEnableMms() {
		return enableMms;
	}

	public void setEnableMms(Integer enableMms) {
		this.enableMms = enableMms;
	}

	public Integer getNoReplyEnabled() {
		return noReplyEnabled;
	}

	public void setNoReplyEnabled(Integer noReplyEnabled) {
		this.noReplyEnabled = noReplyEnabled;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("EnterpriseTemplatesMigrationRO [ongoingEmailTemplateId=");
		builder.append(ongoingEmailTemplateId);
		builder.append(", ongoingSmsTemplateId=");
		builder.append(ongoingSmsTemplateId);
		builder.append(", campaignStatus=");
		builder.append(campaignStatus);
		builder.append(", enableUnsubscribeText=");
		builder.append(enableUnsubscribeText);
		builder.append(", surveyId=");
		builder.append(surveyId);
		builder.append(", contactUsEnabled=");
		builder.append(contactUsEnabled);
		builder.append(", enableLocationBranding=");
		builder.append(enableLocationBranding);
		builder.append(", enableMms=");
		builder.append(enableMms);
		builder.append(", noReplyEnabled=");
		builder.append(noReplyEnabled);
		builder.append("]");
		return builder.toString();
	}

}
