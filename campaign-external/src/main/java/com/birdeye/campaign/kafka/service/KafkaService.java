/**
 * 
 */
package com.birdeye.campaign.kafka.service;

import java.util.List;

import com.birdeye.campaign.constant.KafkaTopicTypeEnum;
import com.birdeye.campaign.dto.KafkaMessage;

/**
 * <AUTHOR>
 *
 */
public interface KafkaService {
	
	/**
	 * Push message to <PERSON><PERSON><PERSON> for the given topic.
	 * 
	 * @param kafkaConfigType
	 * @param message
	 * @return
	 */
	public void pushMessageToKafka(KafkaTopicTypeEnum kafkaConfigType, KafkaMessage message);

	/**
	 * Push messages list to <PERSON><PERSON>ka for the given topic.
	 * 
	 * @param kafkaConfigType
	 * @param kafkaMessages
	 * @return
	 */
	public boolean pushMessagesListToKafka(KafkaTopicTypeEnum kafkaConfigType, List<KafkaMessage> kafkaMessages);

	/**
	 * 
	 * Sends message to Kafka topic and give an acknowledgement
	 * @param <T>
	 * 
	 * @param kafkaTopic
	 * @param message
	 * @return
	 */
	<T> boolean pushMessageToKafkaAcknowledged(String kafkaTopic,String key, T message);

	/**
	 * @param kafkaConfigType
	 * @param key
	 * @param message
	 * @return
	 */
	<T> boolean pushMessageToKafkaAcknowledged(KafkaTopicTypeEnum kafkaConfigType, String key, T message);

	void pushMessageToKafkaWithKey(KafkaTopicTypeEnum kafkaConfigType, KafkaMessage message);

	/**
	 * Push messages list to Kafka for the given topic.
	 * 
	 * @param kafkaConfigType
	 * @param kafkaMessages
	 * @return
	 */
	boolean pushMessagesListToKafka(String kafkaConfigType, List<KafkaMessage> kafkaMessages);

	void pushMessageToKafkaWithKey(String kafkaTopic, KafkaMessage message);
	
}
