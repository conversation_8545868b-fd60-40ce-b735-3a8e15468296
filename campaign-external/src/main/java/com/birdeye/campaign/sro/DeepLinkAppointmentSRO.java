package com.birdeye.campaign.sro;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.dto.CalendarInfo;
import com.birdeye.campaign.dto.MapPoints;

public class DeepLinkAppointmentSRO implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1324632027887275793L;
	
	private Integer				appointmentId;
	private String				appointmentExtId;
	private String				appointmentStartTime;						// date and time both
	private String				specialistName;
	private String				specialistLabel;
	private Boolean				showConfirmButton;
	private Boolean				showLocationInfo;
	private String				serviceName;
	private CalendarInfo		calendarDetails;
	private MapPoints			map;
	private String				patientName;
	private String				confirmButtonLink;
	
	private String				appointmentDetailsUrl;
	private String				appointmentStatus;
	
	public Integer getAppointmentId() {
		return appointmentId;
	}
	
	public void setAppointmentId(Integer appointmentId) {
		this.appointmentId = appointmentId;
	}
	
	/**
	 * @return the appointmentExtId
	 */
	public String getAppointmentExtId() {
		return appointmentExtId;
	}

	/**
	 * @param appointmentExtId the appointmentExtId to set
	 */
	public void setAppointmentExtId(String appointmentExtId) {
		this.appointmentExtId = appointmentExtId;
	}

	public String getAppointmentStartTime() {
		return appointmentStartTime;
	}
	
	public void setAppointmentStartTime(String appointmentStartTime) {
		this.appointmentStartTime = appointmentStartTime;
	}
	
	public String getSpecialistName() {
		return specialistName;
	}
	
	public void setSpecialistName(String specialistName) {
		this.specialistName = specialistName;
	}
	
	public String getSpecialistLabel() {
		return specialistLabel;
	}
	
	public void setSpecialistLabel(String specialistLabel) {
		this.specialistLabel = specialistLabel;
	}
	
	public Boolean getShowConfirmButton() {
		return showConfirmButton;
	}
	
	public void setShowConfirmButton(Boolean showConfirmButton) {
		this.showConfirmButton = showConfirmButton;
	}
	
	public Boolean getShowLocationInfo() {
		return showLocationInfo;
	}

	public void setShowLocationInfo(Boolean showLocationInfo) {
		this.showLocationInfo = showLocationInfo;
	}

	public String getServiceName() {
		return serviceName;
	}
	
	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}
	
	public CalendarInfo getCalendarDetails() {
		return calendarDetails;
	}
	
	public void setCalendarDetails(CalendarInfo calendarDetails) {
		this.calendarDetails = calendarDetails;
	}
	
	public MapPoints getMap() {
		return map;
	}
	
	public void setMap(MapPoints map) {
		this.map = map;
	}
	
	public String getPatientName() {
		return patientName;
	}
	
	public void setPatientName(String patientName) {
		this.patientName = patientName;
	}
	
	 public String getConfirmButtonLink() {
	 return confirmButtonLink;
	 }
	
	 public void setConfirmButtonLink(String confirmButtonLink) {
	 this.confirmButtonLink = confirmButtonLink;
	 }
	
	public String getAppointmentDetailsUrl() {
		return appointmentDetailsUrl;
	}
	
	public void setAppointmentDetailsUrl(String appointmentDetailsUrl) {
		this.appointmentDetailsUrl = appointmentDetailsUrl;
	}
	
	public String getAppointmentStatus() {
		return appointmentStatus;
	}
	
	public void setAppointmentStatus(String appointmentStatus) {
		this.appointmentStatus = appointmentStatus;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
