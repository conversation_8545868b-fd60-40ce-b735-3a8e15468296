package com.birdeye.campaign.sro;

import java.util.List;

public class AllReviewSourcesSRO {
	private List<ReviewSourceSRO> selectedSources;

	private List<ReviewSourceSRO> selectedAndroidSources;

	private List<ReviewSourceSRO> availableSources;

	public AllReviewSourcesSRO(List<ReviewSourceSRO> selectedSources, List<ReviewSourceSRO> selectedAndroidSources,
			List<ReviewSourceSRO> availableSources) {
		super();
		this.selectedSources = selectedSources;
		this.selectedAndroidSources = selectedAndroidSources;
		this.availableSources = availableSources;
	}

	public AllReviewSourcesSRO() {
		// TODO Auto-generated constructor stub
	}

	public List<ReviewSourceSRO> getSelectedSources() {
		return selectedSources;
	}

	public void setSelectedSources(List<ReviewSourceSRO> selectedSources) {
		this.selectedSources = selectedSources;
	}

	public List<ReviewSourceSRO> getSelectedAndroidSources() {
		return selectedAndroidSources;
	}

	public void setSelectedAndroidSources(List<ReviewSourceSRO> selectedAndroidSources) {
		this.selectedAndroidSources = selectedAndroidSources;
	}

	public List<ReviewSourceSRO> getAvailableSources() {
		return availableSources;
	}

	public void setAvailableSources(List<ReviewSourceSRO> availableSources) {
		this.availableSources = availableSources;
	}
	
}
