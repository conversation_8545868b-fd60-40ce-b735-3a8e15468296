package com.birdeye.campaign.sro;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.dto.BrandingInfo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties({ "tokenMap" })
public class DeeplinkInfoSRO implements Serializable {
	
	private static final long				serialVersionUID		= 5130595855698855179L;
	
	private String							feedbackMessage;
	
	private Integer							feedbackCallbackEnabled;
	
	private Integer							feedbackCheckboxEnabled;
	
	private String							feedbackCallbackMessage;
	
	private String							thankYouHeading;
	
	private String							thankYouMessage;
	
	private Integer							reviewEnabled;
	
	private String							reviewHeading;
	
	private String							reviewMessage;
	
	private String							referralMessage;
	
	private String							reviewSiteButtonColor;
	
	private String							reviewSiteButtonTextColor;
	
	private String							surveyButtonColor;
	
	private String							surveyButtonTextColor;
	
	private Integer							contactUsEnabled;
	
	private String							contactUsMessage;
	
	private String							contactUsButtonText;
	
	private String							contactUsCustomUrl;
	
	private String							contactUsButtonColor;
	
	private String							contactUsButtonTextColor;
	
	private Integer							locationBrandingEnabled;
	
	private List<ReviewSourceSRO>			selectedSources;
	
	private List<ReferralSourceSRO>			selectedReferralSources;
	
	private CXDataByTypeSRO					cxDataByType;
	
	private Integer							isDisclaimerEnabled		= 0;
	
	private DisclaimerInfoSRO				disclaimerInfo;
	
	private HeaderInfoSRO					headerInfo				= new HeaderInfoSRO();
	
	private BrandingInfo					brandingInfo			= new BrandingInfo();
	
	private Integer							positiveButtonThreshold;
	
	private String							customerFirstName;
	
	private String							customerLastName;
	
	private String							customerEmailId;
	
	private String							emailSubject;
	
	private String							signature;
	
	private Integer							isDGR;
	
	private String							referralImage;
	
	private Map<String, String>				tokenMap;
	
	private DeeplinkReferralInfoSRO			referralInfo			= new DeeplinkReferralInfoSRO();		// referral specific data
	
	private DeeplinkAccountInfoSRO			accountInfo;													// account details (business info - to be used only for referrals)
	
	private DeepLinkAppointmentSRO			appointmentInfo					= new DeepLinkAppointmentSRO();			// appointment specific details
	
	private DeepLinkAppointmentRecallSRO	appointmentRecallInfo			= new DeepLinkAppointmentRecallSRO();	// appointment recall specific details
	// 0 for false and 1 for true
	private Integer							serviceAreaProvider;
	
	private boolean							appointmentSchedulingEnabled	= false;
	
	private boolean							appointmentRemindersEnabled		= false;
	
	private boolean							confirmButtonEnabled;
	
	private boolean							rescheduleButtonEnabled;
	
	private boolean							cancelButtonEnabled;
	
	private boolean							hideCancelOption;
	
	private boolean							hideRescheduleOption;
	
	private boolean							bookAppointmentButtonEnabled;
	
	private String							feedbackTextLabel;
	
	private String							submitButtonText;
	
	private String							formUrl;
	
	private String							formButtonText;
	
	private String							formButtonColor;
	
	private String							formButtonTextColor;
	
	private String							feedbackNamePlaceholder;
	
	private String							feedbackEmailPlaceholder;
	
	private String							directFeedbackHeading;
	
	private String							feedbackMessagePositiveSentiment;
	
	private List<String>					starsSubText;
	
	private String							directFeedbackNextButtonText;
	
	private String							referralLinkLabel;
	
	private String							referralFirstNamePlaceholder;
	
	private String							referralLastNamePlaceholder;
	
	private String							referralEmailPlaceholder;
	
	private String							referralPhonePlaceholder;
	
	private String							feedbackAssistedByPlaceholder;
	// customer phone Number field
	
	public String getFeedbackTextLabel() {
		return feedbackTextLabel;
	}

	public void setFeedbackTextLabel(String feedbackTextLabel) {
		this.feedbackTextLabel = feedbackTextLabel;
	}

	public String getSubmitButtonText() {
		return submitButtonText;
	}

	public void setSubmitButtonText(String submitButtonText) {
		this.submitButtonText = submitButtonText;
	}

	public String getFeedbackMessage() {
		return feedbackMessage;
	}
	
	public void setFeedbackMessage(String feedbackMessage) {
		this.feedbackMessage = feedbackMessage;
	}
	
	public Integer getFeedbackCallbackEnabled() {
		return feedbackCallbackEnabled;
	}
	
	public void setFeedbackCallbackEnabled(Integer feedbackCallbackEnabled) {
		this.feedbackCallbackEnabled = feedbackCallbackEnabled;
	}
	
	public Integer getFeedbackCheckboxEnabled() {
		return feedbackCheckboxEnabled;
	}
	
	public void setFeedbackCheckboxEnabled(Integer feedbackCheckboxEnabled) {
		this.feedbackCheckboxEnabled = feedbackCheckboxEnabled;
	}
	
	public String getFeedbackCallbackMessage() {
		return feedbackCallbackMessage;
	}
	
	public void setFeedbackCallbackMessage(String feedbackCallbackMessage) {
		this.feedbackCallbackMessage = feedbackCallbackMessage;
	}
	
	public String getThankYouHeading() {
		return thankYouHeading;
	}
	
	public void setThankYouHeading(String thankYouHeading) {
		this.thankYouHeading = thankYouHeading;
	}
	
	public String getThankYouMessage() {
		return thankYouMessage;
	}
	
	public void setThankYouMessage(String thankYouMessage) {
		this.thankYouMessage = thankYouMessage;
	}
	
	public Integer getReviewEnabled() {
		return reviewEnabled;
	}
	
	public void setReviewEnabled(Integer reviewEnabled) {
		this.reviewEnabled = reviewEnabled;
	}
	
	public String getReviewHeading() {
		return reviewHeading;
	}
	
	public void setReviewHeading(String reviewHeading) {
		this.reviewHeading = reviewHeading;
	}
	
	public String getReviewMessage() {
		return reviewMessage;
	}
	
	public void setReviewMessage(String reviewMessage) {
		this.reviewMessage = reviewMessage;
	}
	
	public String getReviewSiteButtonColor() {
		return reviewSiteButtonColor;
	}
	
	public void setReviewSiteButtonColor(String reviewSiteButtonColor) {
		this.reviewSiteButtonColor = reviewSiteButtonColor;
	}
	
	public String getReviewSiteButtonTextColor() {
		return reviewSiteButtonTextColor;
	}
	
	public void setReviewSiteButtonTextColor(String reviewSiteButtonTextColor) {
		this.reviewSiteButtonTextColor = reviewSiteButtonTextColor;
	}
	
	public Integer getContactUsEnabled() {
		return contactUsEnabled;
	}
	
	public void setContactUsEnabled(Integer contactUsEnabled) {
		this.contactUsEnabled = contactUsEnabled;
	}
	
	public String getContactUsMessage() {
		return contactUsMessage;
	}
	
	public void setContactUsMessage(String contactUsMessage) {
		this.contactUsMessage = contactUsMessage;
	}
	
	public String getContactUsButtonText() {
		return contactUsButtonText;
	}
	
	public void setContactUsButtonText(String contactUsButtonText) {
		this.contactUsButtonText = contactUsButtonText;
	}
	
	public String getContactUsCustomUrl() {
		return contactUsCustomUrl;
	}
	
	public void setContactUsCustomUrl(String contactUsCustomUrl) {
		this.contactUsCustomUrl = contactUsCustomUrl;
	}
	
	public String getContactUsButtonColor() {
		return contactUsButtonColor;
	}
	
	public void setContactUsButtonColor(String contactUsButtonColor) {
		this.contactUsButtonColor = contactUsButtonColor;
	}
	
	public String getContactUsButtonTextColor() {
		return contactUsButtonTextColor;
	}
	
	public void setContactUsButtonTextColor(String contactUsButtonTextColor) {
		this.contactUsButtonTextColor = contactUsButtonTextColor;
	}
	
	public Integer getLocationBrandingEnabled() {
		return locationBrandingEnabled;
	}
	
	public void setLocationBrandingEnabled(Integer locationBrandingEnabled) {
		this.locationBrandingEnabled = locationBrandingEnabled;
	}
	
	public List<ReviewSourceSRO> getSelectedSources() {
		return selectedSources;
	}
	
	public void setSelectedSources(List<ReviewSourceSRO> selectedSources) {
		this.selectedSources = selectedSources;
	}
	
	public CXDataByTypeSRO getCxDataByType() {
		return cxDataByType;
	}
	
	public void setCxDataByType(CXDataByTypeSRO cxDataByType) {
		this.cxDataByType = cxDataByType;
	}
	
	public Integer getIsDisclaimerEnabled() {
		return isDisclaimerEnabled;
	}
	
	public void setIsDisclaimerEnabled(Integer isDisclaimerEnabled) {
		this.isDisclaimerEnabled = isDisclaimerEnabled;
	}
	
	public DisclaimerInfoSRO getDisclaimerInfo() {
		return disclaimerInfo;
	}
	
	public void setDisclaimerInfo(DisclaimerInfoSRO disclaimerInfo) {
		this.disclaimerInfo = disclaimerInfo;
	}
	
	public HeaderInfoSRO getHeaderInfo() {
		return headerInfo;
	}
	
	public void setHeaderInfo(HeaderInfoSRO headerInfo) {
		this.headerInfo = headerInfo;
	}
	
	public BrandingInfo getBrandingInfo() {
		return brandingInfo;
	}
	
	public void setBrandingInfo(BrandingInfo brandingInfo) {
		this.brandingInfo = brandingInfo;
	}
	
	/**
	 * @return the positiveButtonThreshold
	 */
	public Integer getPositiveButtonThreshold() {
		return positiveButtonThreshold;
	}
	
	/**
	 * @param positiveButtonThreshold
	 *            the positiveButtonThreshold to set
	 */
	public void setPositiveButtonThreshold(Integer positiveButtonThreshold) {
		this.positiveButtonThreshold = positiveButtonThreshold;
	}
	
	/**
	 * @return the customerFirstName
	 */
	public String getCustomerFirstName() {
		return customerFirstName;
	}
	
	/**
	 * @param customerFirstName
	 *            the customerFirstName to set
	 */
	public void setCustomerFirstName(String customerFirstName) {
		this.customerFirstName = customerFirstName;
	}
	
	/**
	 * @return the customerLastName
	 */
	public String getCustomerLastName() {
		return customerLastName;
	}
	
	/**
	 * @param customerLastName
	 *            the customerLastName to set
	 */
	public void setCustomerLastName(String customerLastName) {
		this.customerLastName = customerLastName;
	}
	
	/**
	 * @return the customerEmailId
	 */
	public String getCustomerEmailId() {
		return customerEmailId;
	}
	
	/**
	 * @param customerEmailId
	 *            the customerEmailId to set
	 */
	public void setCustomerEmailId(String customerEmailId) {
		this.customerEmailId = customerEmailId;
	}
	
	public String getEmailSubject() {
		return emailSubject;
	}
	
	public void setEmailSubject(String emailSubject) {
		this.emailSubject = emailSubject;
	}
	
	public String getSignature() {
		return signature;
	}
	
	public void setSignature(String signature) {
		this.signature = signature;
	}
	
	public Integer getIsDGR() {
		return isDGR;
	}
	
	public void setIsDGR(Integer isDGR) {
		this.isDGR = isDGR;
	}
	
	public String getSurveyButtonColor() {
		return surveyButtonColor;
	}
	
	public void setSurveyButtonColor(String surveyButtonColor) {
		this.surveyButtonColor = surveyButtonColor;
	}
	
	public String getSurveyButtonTextColor() {
		return surveyButtonTextColor;
	}
	
	public void setSurveyButtonTextColor(String surveyButtonTextColor) {
		this.surveyButtonTextColor = surveyButtonTextColor;
	}
	
	public List<ReferralSourceSRO> getSelectedReferralSources() {
		return selectedReferralSources;
	}
	
	public void setSelectedReferralSources(List<ReferralSourceSRO> selectedReferralSources) {
		this.selectedReferralSources = selectedReferralSources;
	}
	
	public String getReferralMessage() {
		return referralMessage;
	}
	
	public void setReferralMessage(String referralMessage) {
		this.referralMessage = referralMessage;
	}
	
	public String getReferralImage() {
		return referralImage;
	}
	
	public void setReferralImage(String referralImage) {
		this.referralImage = referralImage;
	}
	
	public Map<String, String> getTokenMap() {
		return tokenMap;
	}
	
	public void setTokenMap(Map<String, String> tokenMap) {
		this.tokenMap = tokenMap;
	}
	
	/**
	 * @return the referralInfo
	 */
	public DeeplinkReferralInfoSRO getReferralInfo() {
		return referralInfo;
	}
	
	/**
	 * @param referralInfo
	 *            the referralInfo to set
	 */
	public void setReferralInfo(DeeplinkReferralInfoSRO referralInfo) {
		this.referralInfo = referralInfo;
	}
	
	public DeeplinkAccountInfoSRO getAccountInfo() {
		return accountInfo;
	}
	
	public void setAccountInfo(DeeplinkAccountInfoSRO accountInfo) {
		this.accountInfo = accountInfo;
	}
	
	public Integer getServiceAreaProvider() {
		return serviceAreaProvider;
	}
	
	public void setServiceAreaProvider(Integer serviceAreaProvider) {
		this.serviceAreaProvider = serviceAreaProvider;
	}
	
	public DeepLinkAppointmentSRO getAppointmentInfo() {
		return appointmentInfo;
	}
	
	public void setAppointmentInfo(DeepLinkAppointmentSRO appointmentInfo) {
		this.appointmentInfo = appointmentInfo;
	}

	public DeepLinkAppointmentRecallSRO getAppointmentRecallInfo() {
		return appointmentRecallInfo;
	}
	
	public void setAppointmentRecallInfo(DeepLinkAppointmentRecallSRO appointmentRecallInfo) {
		this.appointmentRecallInfo = appointmentRecallInfo;
	}

	public boolean getAppointmentSchedulingEnabled() {
		return appointmentSchedulingEnabled;
	}

	public void setAppointmentSchedulingEnabled(boolean appointmentSchedulingEnabled) {
		this.appointmentSchedulingEnabled = appointmentSchedulingEnabled;
	}

	public boolean isAppointmentRemindersEnabled() {
		return appointmentRemindersEnabled;
	}

	public void setAppointmentRemindersEnabled(boolean appointmentRemindersEnabled) {
		this.appointmentRemindersEnabled = appointmentRemindersEnabled;
	}

	public boolean getConfirmButtonEnabled() {
		return confirmButtonEnabled;
	}

	public void setConfirmButtonEnabled(boolean confirmButtonEnabled) {
		this.confirmButtonEnabled = confirmButtonEnabled;
	}

	public boolean getRescheduleButtonEnabled() {
		return rescheduleButtonEnabled;
	}

	public void setRescheduleButtonEnabled(boolean rescheduleButtonEnabled) {
		this.rescheduleButtonEnabled = rescheduleButtonEnabled;
	}

	public boolean getCancelButtonEnabled() {
		return cancelButtonEnabled;
	}

	public void setCancelButtonEnabled(boolean cancelButtonEnabled) {
		this.cancelButtonEnabled = cancelButtonEnabled;
	}

	public boolean getHideCancelOption() {
		return hideCancelOption;
	}

	public void setHideCancelOption(boolean hideCancelOption) {
		this.hideCancelOption = hideCancelOption;
	}

	public boolean getHideRescheduleOption() {
		return hideRescheduleOption;
	}

	public void setHideRescheduleOption(boolean hideRescheduleOption) {
		this.hideRescheduleOption = hideRescheduleOption;
	}

	public boolean getBookAppointmentButtonEnabled() {
		return bookAppointmentButtonEnabled;
	}

	public void setBookAppointmentButtonEnabled(boolean bookAppointmentButtonEnabled) {
		this.bookAppointmentButtonEnabled = bookAppointmentButtonEnabled;
	}

	/**
	 * @return the formUrl
	 */
	public String getFormUrl() {
		return formUrl;
	}

	/**
	 * @param formUrl the formUrl to set
	 */
	public void setFormUrl(String formUrl) {
		this.formUrl = formUrl;
	}

	/**
	 * @return the formButtonText
	 */
	public String getFormButtonText() {
		return formButtonText;
	}

	/**
	 * @param formButtonText the formButtonText to set
	 */
	public void setFormButtonText(String formButtonText) {
		this.formButtonText = formButtonText;
	}

	/**
	 * @return the formButtonColor
	 */
	public String getFormButtonColor() {
		return formButtonColor;
	}

	/**
	 * @param formButtonColor the formButtonColor to set
	 */
	public void setFormButtonColor(String formButtonColor) {
		this.formButtonColor = formButtonColor;
	}

	/**
	 * @return the formButtonTextColor
	 */
	public String getFormButtonTextColor() {
		return formButtonTextColor;
	}

	/**
	 * @param formButtonTextColor the formButtonTextColor to set
	 */
	public void setFormButtonTextColor(String formButtonTextColor) {
		this.formButtonTextColor = formButtonTextColor;
	}

	/**
	 * @return the feedbackNamePlaceholder
	 */
	public String getFeedbackNamePlaceholder() {
		return feedbackNamePlaceholder;
	}

	/**
	 * @param feedbackNamePlaceholder the feedbackNamePlaceholder to set
	 */
	public void setFeedbackNamePlaceholder(String feedbackNamePlaceholder) {
		this.feedbackNamePlaceholder = feedbackNamePlaceholder;
	}

	/**
	 * @return the feedbackEmailPlaceholder
	 */
	public String getFeedbackEmailPlaceholder() {
		return feedbackEmailPlaceholder;
	}

	/**
	 * @param feedbackEmailPlaceholder the feedbackEmailPlaceholder to set
	 */
	public void setFeedbackEmailPlaceholder(String feedbackEmailPlaceholder) {
		this.feedbackEmailPlaceholder = feedbackEmailPlaceholder;
	}

	/**
	 * @return the directFeedbackHeading
	 */
	public String getDirectFeedbackHeading() {
		return directFeedbackHeading;
	}

	/**
	 * @param directFeedbackHeading the directFeedbackHeading to set
	 */
	public void setDirectFeedbackHeading(String directFeedbackHeading) {
		this.directFeedbackHeading = directFeedbackHeading;
	}

	/**
	 * @return the feedbackMessagePositiveSentiment
	 */
	public String getFeedbackMessagePositiveSentiment() {
		return feedbackMessagePositiveSentiment;
	}

	/**
	 * @param feedbackMessagePositiveSentiment the feedbackMessagePositiveSentiment to set
	 */
	public void setFeedbackMessagePositiveSentiment(String feedbackMessagePositiveSentiment) {
		this.feedbackMessagePositiveSentiment = feedbackMessagePositiveSentiment;
	}

	/**
	 * @return the starsSubText
	 */
	public List<String> getStarsSubText() {
		return starsSubText;
	}

	/**
	 * @param starsSubText the starsSubText to set
	 */
	public void setStarsSubText(List<String> starsSubText) {
		this.starsSubText = starsSubText;
	}

	/**
	 * @return the directFeedbackNextButtonText
	 */
	public String getDirectFeedbackNextButtonText() {
		return directFeedbackNextButtonText;
	}

	/**
	 * @param directFeedbackNextButtonText the directFeedbackNextButtonText to set
	 */
	public void setDirectFeedbackNextButtonText(String directFeedbackNextButtonText) {
		this.directFeedbackNextButtonText = directFeedbackNextButtonText;
	}

	/**
	 * @return the referralLinkLabel
	 */
	public String getReferralLinkLabel() {
		return referralLinkLabel;
	}

	/**
	 * @param referralLinkLabel the referralLinkLabel to set
	 */
	public void setReferralLinkLabel(String referralLinkLabel) {
		this.referralLinkLabel = referralLinkLabel;
	}

	/**
	 * @return the referralFirstNamePlaceholder
	 */
	public String getReferralFirstNamePlaceholder() {
		return referralFirstNamePlaceholder;
	}

	/**
	 * @param referralFirstNamePlaceholder the referralFirstNamePlaceholder to set
	 */
	public void setReferralFirstNamePlaceholder(String referralFirstNamePlaceholder) {
		this.referralFirstNamePlaceholder = referralFirstNamePlaceholder;
	}

	/**
	 * @return the referralLastNamePlaceholder
	 */
	public String getReferralLastNamePlaceholder() {
		return referralLastNamePlaceholder;
	}

	/**
	 * @param referralLastNamePlaceholder the referralLastNamePlaceholder to set
	 */
	public void setReferralLastNamePlaceholder(String referralLastNamePlaceholder) {
		this.referralLastNamePlaceholder = referralLastNamePlaceholder;
	}

	/**
	 * @return the referralEmailPlaceholder
	 */
	public String getReferralEmailPlaceholder() {
		return referralEmailPlaceholder;
	}

	/**
	 * @param referralEmailPlaceholder the referralEmailPlaceholder to set
	 */
	public void setReferralEmailPlaceholder(String referralEmailPlaceholder) {
		this.referralEmailPlaceholder = referralEmailPlaceholder;
	}

	/**
	 * @return the referralPhonePlaceholder
	 */
	public String getReferralPhonePlaceholder() {
		return referralPhonePlaceholder;
	}

	/**
	 * @param referralPhonePlaceholder the referralPhonePlaceholder to set
	 */
	public void setReferralPhonePlaceholder(String referralPhonePlaceholder) {
		this.referralPhonePlaceholder = referralPhonePlaceholder;
	}

	/**
	 * @return the assistedByPlaceholder
	 */
	public String getFeedbackAssistedByPlaceholder() {
		return feedbackAssistedByPlaceholder;
	}

	/**
	 * @param assistedByPlaceholder the assistedByPlaceholder to set
	 */
	public void setFeedbackAssistedByPlaceholder(String assistedByPlaceholder) {
		this.feedbackAssistedByPlaceholder = assistedByPlaceholder;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}

}
