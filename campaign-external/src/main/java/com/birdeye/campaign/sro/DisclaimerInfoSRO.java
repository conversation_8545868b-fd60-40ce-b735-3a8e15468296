/**
 * 
 */
package com.birdeye.campaign.sro;

/**
 * <AUTHOR>
 *
 */
public class DisclaimerInfoSRO {
	
	private String content;
    private String agreeToTermsContent;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getAgreeToTermsContent() {
        return agreeToTermsContent;
    }

    public void setAgreeToTermsContent(String agreeToTermsContent) {
        this.agreeToTermsContent = agreeToTermsContent;
    }

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("DisclaimerSRO [content=");
		builder.append(content);
		builder.append(", agreeToTermsContent=");
		builder.append(agreeToTermsContent);
		builder.append("]");
		return builder.toString();
	}
    
}
