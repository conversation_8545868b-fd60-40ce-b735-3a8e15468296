/**
 * @file_name ReferralSourceWrapperSRO.java
 * @created_date 8 Jul 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.sro;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * @file_name ReferralSourceWrapperSRO.java
 * @created_date 8 Jul 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_NULL)
public class ReferralSourceWrapperSRO implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -7001889259218916959L;
	private List<ReferralSourceSRO> sources;
	
	/**
	 * 
	 */
	public ReferralSourceWrapperSRO() {
		super();
	}

	/**
	 * @param sources
	 */
	public ReferralSourceWrapperSRO(List<ReferralSourceSRO> sources) {
		super();
		this.sources = sources;
	}

	/**
	 * @return the sources
	 */
	public List<ReferralSourceSRO> getSources() {
		return sources;
	}
	
	/**
	 * @param sources
	 *            the sources to set
	 */
	public void setSources(List<ReferralSourceSRO> sources) {
		this.sources = sources;
	}
	
	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("ReferralSourceWrapperSRO [sources=");
		builder.append(sources);
		builder.append("]");
		return builder.toString();
	}
	
}
