/**
 * @file_name ReferralSourceSRO.java
 * @created_date 8 Jul 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.sro;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * @file_name ReferralSourceSRO.java
 * @created_date 8 Jul 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_NULL)
public class ReferralSourceSRO implements Serializable {
	
	/**
	 * 
	 */
	private static final long		serialVersionUID	= -6568527151241808828L;
	
	private Integer					sourceId;
	private String					sourceName;
	private String					sourceAlias;
	private Integer					priority;
	private String					logoUrl;
	private String					buttonColor;
	private String					buttonTextColor;
	private String					sourceUrl;
	
	private DeeplinkUrlByDeviceType	urlByDevice = new DeeplinkUrlByDeviceType();;
	// Additional URLs by opening devices, (only used in referrals for now) -
	// https://docs.google.com/presentation/d/14ZwLpWRu2T1AU9I_qX1lX4V6FTDRb9HMN81Ws2ipfik/edit#slide=id.ga5de0c3f06_0_78
	
	/**
	 * @return the sourceId
	 */
	public Integer getSourceId() {
		return sourceId;
	}
	
	/**
	 * @param sourceId
	 *            the sourceId to set
	 */
	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}
	
	/**
	 * @return the sourceName
	 */
	public String getSourceName() {
		return sourceName;
	}
	
	/**
	 * @param sourceName
	 *            the sourceName to set
	 */
	public void setSourceName(String sourceName) {
		this.sourceName = sourceName;
	}
	
	/**
	 * @return the priority
	 */
	public Integer getPriority() {
		return priority;
	}
	
	/**
	 * @param priority
	 *            the priority to set
	 */
	public void setPriority(Integer priority) {
		this.priority = priority;
	}
	
	/**
	 * @return the logoUrl
	 */
	public String getLogoUrl() {
		return logoUrl;
	}
	
	/**
	 * @param logoUrl
	 *            the logoUrl to set
	 */
	public void setLogoUrl(String logoUrl) {
		this.logoUrl = logoUrl;
	}
	
	/**
	 * @return the buttonColor
	 */
	public String getButtonColor() {
		return buttonColor;
	}
	
	/**
	 * @param buttonColor
	 *            the buttonColor to set
	 */
	public void setButtonColor(String buttonColor) {
		this.buttonColor = buttonColor;
	}
	
	/**
	 * @return the buttonTextColor
	 */
	public String getButtonTextColor() {
		return buttonTextColor;
	}
	
	/**
	 * @param buttonTextColor
	 *            the buttonTextColor to set
	 */
	public void setButtonTextColor(String buttonTextColor) {
		this.buttonTextColor = buttonTextColor;
	}
	
	public String getSourceUrl() {
		return sourceUrl;
	}
	
	public void setSourceUrl(String sourceUrl) {
		this.sourceUrl = sourceUrl;
	}
	
	public String getSourceAlias() {
		return sourceAlias;
	}
	
	public void setSourceAlias(String sourceAlias) {
		this.sourceAlias = sourceAlias;
	}
	
	
	public DeeplinkUrlByDeviceType getUrlByDevice() {
		return urlByDevice;
	}

	public void setUrlByDevice(DeeplinkUrlByDeviceType urlByDevice) {
		this.urlByDevice = urlByDevice;
	}

	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return b.toString();
	}
	
}
