/**
 * @file_name AllReferralSourcesSRO.java
 * @created_date 10 Jul 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.sro;

import java.io.Serializable;
import java.util.List;

import org.codehaus.jackson.annotate.JsonIgnoreProperties;

/**
 * @file_name AllReferralSourcesSRO.java
 * @created_date 10 Jul 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public class AllReferralSourcesSRO implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 3587213297127998912L;
	private List<ReferralSourceSRO>	selectedReferralSources;
	private List<ReferralSourceSRO>	availableReferralSources;
	
	/**
	 * @return the selectedReferralSources
	 */
	public List<ReferralSourceSRO> getSelectedReferralSources() {
		return selectedReferralSources;
	}
	
	/**
	 * @param selectedReferralSources
	 *            the selectedReferralSources to set
	 */
	public void setSelectedReferralSources(List<ReferralSourceSRO> selectedReferralSources) {
		this.selectedReferralSources = selectedReferralSources;
	}
	
	/**
	 * @return the availableReferralSources
	 */
	public List<ReferralSourceSRO> getAvailableReferralSources() {
		return availableReferralSources;
	}
	
	/**
	 * @param availableReferralSources
	 *            the availableReferralSources to set
	 */
	public void setAvailableReferralSources(List<ReferralSourceSRO> availableReferralSources) {
		this.availableReferralSources = availableReferralSources;
	}
	
	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("AllReferralSourcesSRO [selectedReferralSources=");
		builder.append(selectedReferralSources);
		builder.append(", availableReferralSources=");
		builder.append(availableReferralSources);
		builder.append("]");
		return builder.toString();
	}
	
}
