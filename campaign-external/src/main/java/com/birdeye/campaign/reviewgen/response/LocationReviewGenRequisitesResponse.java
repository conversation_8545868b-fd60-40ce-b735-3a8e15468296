package com.birdeye.campaign.reviewgen.response;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.reviewgen.ReviewGenSourceRequisite;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class LocationReviewGenRequisitesResponse implements Serializable {

	private static final long serialVersionUID = 7801901505226341663L;
	
	private String businessName;
	private Integer businessId;
	private List<ReviewGenSourceRequisite> reviewGenRequisites;

	public String getBusinessName() {
		return businessName;
	}

	public void setBusinessName(String businessName) {
		this.businessName = businessName;
	}

	public Integer getBusinessId() {
		return businessId;
	}

	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}

	public List<ReviewGenSourceRequisite> getReviewGenRequisites() {
		return reviewGenRequisites;
	}

	public void setReviewGenRequisites(List<ReviewGenSourceRequisite> reviewGenRequisites) {
		this.reviewGenRequisites = reviewGenRequisites;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
