package com.birdeye.campaign.reviewgen.response;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.dto.reviewgen.ReviewGenRequisite;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * 
 * <AUTHOR>
 *
 */
@JsonInclude(value = Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SourceReviewGenRequisitesResponse implements Serializable {
	
	private static final long			serialVersionUID	= -8061646772043259987L;
	
	private Integer						totalCount;
	private Integer						emptyReviewGenURLLocationsCount;
	private String						sourceName;
	private String						sourceDomain;
	private Integer						supported;
	private Integer						sourceId;
	private List<ReviewGenRequisite>	reviewGenRequisites;
	
	public SourceReviewGenRequisitesResponse() {
		super();
	}
	
	public SourceReviewGenRequisitesResponse(Integer totalCount, Integer emptyReviewGenURLLocationsCount, String sourceName, String sourceDomain, Integer supported, Integer sourceId,
			List<ReviewGenRequisite> reviewGenRequisites) {
		super();
		this.totalCount = totalCount;
		this.emptyReviewGenURLLocationsCount = emptyReviewGenURLLocationsCount;
		this.sourceName = sourceName;
		this.sourceDomain = sourceDomain;
		this.supported = supported;
		this.sourceId = sourceId;
		this.reviewGenRequisites = reviewGenRequisites;
	}
	
	public List<ReviewGenRequisite> getReviewGenRequisites() {
		return reviewGenRequisites;
	}
	
	public void setReviewGenRequisites(List<ReviewGenRequisite> reviewGenRequisites) {
		this.reviewGenRequisites = reviewGenRequisites;
	}
	
	public Integer getTotalCount() {
		return totalCount;
	}
	
	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}
	
	public String getSourceName() {
		return sourceName;
	}
	
	public void setSourceName(String sourceName) {
		this.sourceName = sourceName;
	}
	
	public String getSourceDomain() {
		return sourceDomain;
	}
	
	public void setSourceDomain(String sourceDomain) {
		this.sourceDomain = sourceDomain;
	}
	
	public Integer getSupported() {
		return supported;
	}
	
	public void setSupported(Integer supported) {
		this.supported = supported;
	}
	
	public Integer getSourceId() {
		return sourceId;
	}
	
	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}
	
	public Integer getEmptyReviewGenURLLocationsCount() {
		return emptyReviewGenURLLocationsCount;
	}
	
	public void setEmptyReviewGenURLLocationsCount(Integer emptyReviewGenURLLocationsCount) {
		this.emptyReviewGenURLLocationsCount = emptyReviewGenURLLocationsCount;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
