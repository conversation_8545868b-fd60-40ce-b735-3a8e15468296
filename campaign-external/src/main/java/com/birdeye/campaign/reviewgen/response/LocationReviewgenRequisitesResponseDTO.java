package com.birdeye.campaign.reviewgen.response;

import java.io.Serializable;
import java.util.List;

import com.birdeye.campaign.reviewgen.ReviewGenSourceRequisite;

public class LocationReviewgenRequisitesResponseDTO implements Serializable {
	/**
	 * 
	 */
	private static final long				serialVersionUID	= 418761366814750823L;
	private String							businessName;
	private Integer							businessId;
	private List<ReviewGenSourceRequisite>	reviewGenRequisites;
	
	public String getBusinessName() {
		return businessName;
	}
	
	public void setBusinessName(String businessName) {
		this.businessName = businessName;
	}
	
	public Integer getBusinessId() {
		return businessId;
	}
	
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	public List<ReviewGenSourceRequisite> getReviewGenRequisites() {
		return reviewGenRequisites;
	}
	
	public void setReviewGenRequisites(List<ReviewGenSourceRequisite> reviewGenRequisites) {
		this.reviewGenRequisites = reviewGenRequisites;
	}
	
}
