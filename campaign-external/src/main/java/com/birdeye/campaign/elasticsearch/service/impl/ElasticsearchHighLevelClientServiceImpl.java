package com.birdeye.campaign.elasticsearch.service.impl;

import java.io.IOException;
import java.util.Collections;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.LoggingDeprecationHandler;
import org.elasticsearch.common.xcontent.NamedXContentRegistry;
import org.elasticsearch.common.xcontent.XContentFactory;
import org.elasticsearch.common.xcontent.XContentParser;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.SearchModule;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.aspect.annotation.Profiled;
import com.birdeye.campaign.elasticsearch.request.ElasticSearchBaseRequest;
import com.birdeye.campaign.elasticsearch.request.ElasticSearchDeleteByIdsRequest;
import com.birdeye.campaign.elasticsearch.request.ElasticSearchSelectRequest;
import com.birdeye.campaign.elasticsearch.request.ElasticSearchUpdateRequest;
import com.birdeye.campaign.elasticsearch.service.ElasticsearchHighLevelClientService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service("elasticHighLevelClientService")
public class ElasticsearchHighLevelClientServiceImpl implements ElasticsearchHighLevelClientService {
	
	private static final Logger	LOG	= LoggerFactory.getLogger(ElasticsearchHighLevelClientServiceImpl.class);
	
	@Autowired
	@Qualifier("esHighLevelClient")
	private RestHighLevelClient	client;
	
	private static final ObjectMapper	OBJECT_MAPPER	= new ObjectMapper();
	
	static {
		OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
	}
	
	@Override
	public SearchResponse execute(ElasticSearchBaseRequest request) {
		
		LOG.debug("Received ES request execute :: {}", request);
		if (request == null || StringUtils.isBlank(request.getIndex()) || StringUtils.isBlank(request.getType()) || StringUtils.isBlank(request.getQuery())) {
			LOG.error("Invalid ES execute request :: {}", request);
			return null;
		}
		
		SearchResponse searchResponse = null;
		try {
			SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
			SearchModule searchModule = new SearchModule(Settings.EMPTY, false, Collections.emptyList());
			
			XContentParser parser = XContentFactory.xContent(XContentType.JSON).createParser(new NamedXContentRegistry(searchModule.getNamedXContents()),
					LoggingDeprecationHandler.INSTANCE, request.getQuery());
			searchSourceBuilder.parseXContent(parser);
			searchSourceBuilder.trackTotalHits(true);
			
			SearchRequest searchRequest = new SearchRequest(request.getIndex()).source(searchSourceBuilder);
			if (request.getRoutingIdentifier() != null) {
				searchRequest.routing(String.valueOf(request.getRoutingIdentifier()));
			}
			
			searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
			if (searchResponse == null || searchResponse.status().getStatus() != 200) {
				LOG.error("ES hit failed while executing for request {} since response is null.", request);
				return null;
			}
		} catch (IOException e) {
			LOG.error("IO Exception while executing ES request {} :: {}", request, e);
		} catch (Exception e) {
			LOG.error("Exception while executing ES request {} :: {}", request, e);
		}
		return searchResponse;
	}
	
	@Override
	public SearchResponse executeWithoutHitsTracking(ElasticSearchBaseRequest request) {
		LOG.debug("Received ES request execute :: {}", request);
		if (request == null || StringUtils.isBlank(request.getIndex()) || StringUtils.isBlank(request.getType()) || StringUtils.isBlank(request.getQuery())) {
			LOG.error("Invalid ES execute request :: {}", request);
			return null;
		}
		
		SearchResponse searchResponse = null;
		try {
			SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
			SearchModule searchModule = new SearchModule(Settings.EMPTY, false, Collections.emptyList());
			
			XContentParser parser = XContentFactory.xContent(XContentType.JSON).createParser(new NamedXContentRegistry(searchModule.getNamedXContents()),
					LoggingDeprecationHandler.INSTANCE, request.getQuery());
			searchSourceBuilder.parseXContent(parser);
			searchSourceBuilder.trackTotalHits(false);
			
			SearchRequest searchRequest = new SearchRequest(request.getIndex()).source(searchSourceBuilder);
			if (request.getRoutingIdentifier() != null) {
				searchRequest.routing(String.valueOf(request.getRoutingIdentifier()));
			}
			
			searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
			if (searchResponse == null || searchResponse.status().getStatus() != 200) {
				LOG.error("ES hit failed while executing for request {} since response is null.", request);
				return null;
			}
		} catch (IOException e) {
			LOG.error("IO Exception while executing ES request {} :: {}", request, e);
		} catch (Exception e) {
			LOG.error("Exception while executing ES request {} :: {}", request, e);
		}
		return searchResponse;
	}
	
	@Override
	public <T extends Aggregation> T executeAndFetchAggregations(ElasticSearchBaseRequest request, String aggregationName) {
		LOG.debug("Received ES request executeAndFetchAggregations for aggregationName {} :: {}", aggregationName, request);
		
		SearchResponse response = execute(request);
		if (response == null || response.getAggregations() == null) {
			LOG.warn("ES aggregations are null for request {}", request);
			return null;
		}
		T aggregation = null;
		try {
			aggregation = response.getAggregations().get(aggregationName);
		} catch (Exception e) {
			LOG.error("Exception while fetching aggregation for aggregationName {} and request {} is {}", aggregationName, request, e);
			return null;
		}
		return aggregation;
		
	}
	
	/**
	 * Refer https://www.elastic.co/guide/en/elasticsearch/client/java-rest/master/java-rest-high-document-get.html
	 */
	@Override
	@Profiled
	public <T> T select(ElasticSearchSelectRequest<T> request) {
		LOG.debug("Received ES get request :: {}", request);
		if (request == null || StringUtils.isBlank(request.getIndex()) || request.getId() == null) {
			LOG.error("Invalid ES get request :: {}", request);
			return null;
		}
		GetRequest getRequest = new GetRequest(request.getIndex()).id(request.getId());
		if (request.getRoutingIdentifier() != null) {
			getRequest.routing(String.valueOf(request.getRoutingIdentifier()));
		}
		GetResponse response = null;
		
		try {
			response = client.get(getRequest, RequestOptions.DEFAULT);
			
		} catch (IOException e) {
			LOG.error("Exception while executing ES get request {} :: {}", request, e);
		} catch (ElasticsearchException e) {
			if (e.status() == RestStatus.NOT_FOUND) {
				LOG.error("Index not found Exception while executing ES get request {} :: {}", request, e);
			}
		}
		if (response == null) {
			LOG.error("No response found for ES gett request {}", request);
			return null;
		}
		
		return OBJECT_MAPPER.convertValue(response.getSourceAsMap(), request.getTypeParameterClass());
	}
	
	/**
	 * Refer - https://www.elastic.co/guide/en/elasticsearch/client/java-rest/7.x/java-rest-high-document-update.html
	 * 
	 */
	@Override
	@Profiled
	@SuppressWarnings("unchecked")
	public <T> T update(ElasticSearchUpdateRequest<T> request) {
		LOG.debug("Received ES update request :: {}", request);
		if (request == null || StringUtils.isBlank(request.getIndex()) || StringUtils.isBlank(request.getType()) || request.getDocument() == null) {
			LOG.error("Invalid ES update request :: {}", request);
			return null;
		}
		
		UpdateRequest updateRequest = new UpdateRequest(request.getIndex(), request.getId());
		if (request.getRoutingIdentifier() != null) {
			updateRequest.routing(String.valueOf(request.getRoutingIdentifier()));
		}
		updateRequest.doc(OBJECT_MAPPER.convertValue(request.getDocument(), Map.class));
		updateRequest.retryOnConflict(5);
		updateRequest.fetchSource(true); // Enable source retrieval, disabled by default

		UpdateResponse response = null;
		try {
			response = client.update(updateRequest, RequestOptions.DEFAULT);
		} catch (IOException e) {
			LOG.error("Exception while executing ES update request {} :: {}", request, e);
		} catch (ElasticsearchException e) {
			if (e.status() == RestStatus.NOT_FOUND) {
				LOG.error("Index not found Exception while executing ES update request {} :: {}", request, e);
			}
		}
		if (response == null || response.getGetResult() == null) {
			LOG.error("No response found for ES update request {}", request);
			return null;
		}
		
		return OBJECT_MAPPER.convertValue(response.getGetResult().sourceAsMap(), request.getTypeParameterClass());
	}
	
	/**
	 * https://www.elastic.co/guide/en/elasticsearch/client/java-rest/7.x/java-rest-high-document-delete-by-query.html
	 */
	@Override
	public String deleteByQuery(ElasticSearchDeleteByIdsRequest esRequest) {
		
		LOG.debug("Received ES request to delete by ids :: {}", esRequest);
		if (esRequest == null || StringUtils.isBlank(esRequest.getIndex()) || StringUtils.isBlank(esRequest.getType())) {
			LOG.error("Invalid ES delete by ids request :: {}", esRequest);
			return null;
		}
		
		DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest(esRequest.getIndex());
		if (esRequest.getRoutingIdentifier() != null) {
			deleteByQueryRequest.setRouting(String.valueOf(esRequest.getRoutingIdentifier()));
		}
		deleteByQueryRequest.setQuery(new TermsQueryBuilder(esRequest.getSearchField(), esRequest.getIds()));
		
		BulkByScrollResponse deleteByQueryResponse = null;
		try {
			deleteByQueryResponse = client.deleteByQuery(deleteByQueryRequest, RequestOptions.DEFAULT);
		} catch (IOException e) {
			LOG.error("Exception while executing ES delete by ids request {} :: {}", esRequest, e);
		}
		if (deleteByQueryResponse == null) {
			LOG.error("ES hit failed while executing delete by ids request {} since response is null.", esRequest);
			return null;
		}
		return deleteByQueryResponse.toString();
	}

	// This verion of high is not being used now
	@Override
	@Deprecated
	public String executeESQueryV1(String esQuery, String index, String type, Object routingIdentifier) throws JsonProcessingException {
		return StringUtils.EMPTY;
	}

	// This verion of high is not being used now
	@Override
	@Deprecated
	public String executeESQueryV1(ElasticSearchBaseRequest elasticSearchBaseRequest) {
		return StringUtils.EMPTY;
	}

	@Override
	public CreateIndexResponse createIndex(CreateIndexRequest indexCreationRequest) {
		// TODO Auto-generated method stub
		return null;
	}

	
}
