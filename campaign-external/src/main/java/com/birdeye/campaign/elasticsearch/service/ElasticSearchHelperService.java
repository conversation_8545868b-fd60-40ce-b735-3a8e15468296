package com.birdeye.campaign.elasticsearch.service;

import java.util.Map;

import org.apache.poi.ss.formula.functions.T;

import com.birdeye.campaign.elasticsearch.request.ElasticSearchBaseRequest;
import com.birdeye.campaign.elasticsearch.request.ElasticSearchSelectRequest;

/**
 * <AUTHOR>
 *
 */
public interface ElasticSearchHelperService {
	
	/**
	 * prepares elastic search request for given query , token data, index and type.
	 *
	 * @param routingIdentifier
	 * @param tokenData
	 * @param queryName
	 * @param index
	 * @param type
	 * @return
	 * @throws BEAdminException
	 */
	ElasticSearchBaseRequest prepareElasticSearchRequest(Object routingIdentifier, Map<String, Object> tokenData, String queryName, String index, String type);




	/**
	 * @param routingIdentifier
	 * @param index
	 * @param type
	 * @param id
	 * @param classType
	 * @return
	 */
	<T> ElasticSearchSelectRequest<T> prepareElasticSearchSelectRequest(Object routingIdentifier, String index, String type, String id, Class<T> classType);

	
}
