/**
 * @file_name ElasticSearchSelectRequest.java
 * @created_date 24 Jul 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.elasticsearch.request;

import java.io.Serializable;

/**
 * @file_name ElasticSearchSelectRequest.java
 * @created_date 24 Jul 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public class ElasticSearchSelectRequest<T> implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 9014133734306159787L;
	private Object				routingIdentifier;
	private String				index;
	private String				type;
	private String				id;
	private final Class<T>		typeParameterClass;
	
	/**
	 * @param typeParameterClass
	 */
	public ElasticSearchSelectRequest(Class<T> typeParameterClass) {
		this.typeParameterClass = typeParameterClass;
	}
	
	/**
	 * @return the routingIdentifier
	 */
	public Object getRoutingIdentifier() {
		return routingIdentifier;
	}
	
	/**
	 * @param routingIdentifier
	 *            the routingIdentifier to set
	 */
	public void setRoutingIdentifier(Object routingIdentifier) {
		this.routingIdentifier = routingIdentifier;
	}
	
	/**
	 * @return the index
	 */
	public String getIndex() {
		return index;
	}
	
	/**
	 * @param index
	 *            the index to set
	 */
	public void setIndex(String index) {
		this.index = index;
	}
	
	/**
	 * @return the type
	 */
	public String getType() {
		return type;
	}
	
	/**
	 * @param type
	 *            the type to set
	 */
	public void setType(String type) {
		this.type = type;
	}


	/**
	 * @return the id
	 */
	public String getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * @return the typeParameterClass
	 */
	public Class<T> getTypeParameterClass() {
		return typeParameterClass;
	}
	
}
