/**
 * @file_name ElasticSearchInsertRequest.java
 * @created_date 24 Jul 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.elasticsearch.request;

import java.io.Serializable;

/**
 * @file_name ElasticSearchInsertRequest.java
 * @created_date 24 Jul 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 * @param <T>
 */
public class ElasticSearchInsertRequest<T> implements Serializable {
	
	//NOTE: Please use class based constructor to initialize this object
	//Refer https://stackoverflow.com/questions/3437897/how-to-get-a-class-instance-of-generics-type-t
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -1861504329492811843L;
	private Object				routingIdentifier;
	private String				index;
	private String				type;
	private T					document;
	private final Class<T>		typeParameterClass;
	private String				id;
	
	/**
	 * @param typeParameterClass
	 */
	public ElasticSearchInsertRequest(Class<T> typeParameterClass) {
		super();
		this.typeParameterClass = typeParameterClass;
	}

	/**
	 * @return the routingIdentifier
	 */
	public Object getRoutingIdentifier() {
		return routingIdentifier;
	}
	
	/**
	 * @param routingIdentifier
	 *            the routingIdentifier to set
	 */
	public void setRoutingIdentifier(Object routingIdentifier) {
		this.routingIdentifier = routingIdentifier;
	}
	
	/**
	 * @return the index
	 */
	public String getIndex() {
		return index;
	}
	
	/**
	 * @param index
	 *            the index to set
	 */
	public void setIndex(String index) {
		this.index = index;
	}
	
	/**
	 * @return the type
	 */
	public String getType() {
		return type;
	}
	
	/**
	 * @param type
	 *            the type to set
	 */
	public void setType(String type) {
		this.type = type;
	}
	
	/**
	 * @return the document
	 */
	public T getDocument() {
		return document;
	}
	
	/**
	 * @param document
	 *            the document to set
	 */
	public void setDocument(T document) {
		this.document = document;
	}
	
	
	
	/**
	 * @return the typeParameterClass
	 */
	public Class<T> getTypeParameterClass() {
		return typeParameterClass;
	}

	/**
	 * @return the id
	 */
	public String getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(String id) {
		this.id = id;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("ElasticSearchInsertRequest [routingIdentifier=");
		builder.append(routingIdentifier);
		builder.append(", index=");
		builder.append(index);
		builder.append(", type=");
		builder.append(type);
		builder.append(", document=");
		builder.append(document);
		builder.append(", typeParameterClass=");
		builder.append(typeParameterClass);
		builder.append(", id=");
		builder.append(id);
		builder.append("]");
		return builder.toString();
	}
	
}
