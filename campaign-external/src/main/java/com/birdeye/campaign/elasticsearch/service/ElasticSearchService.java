/**
 *
 */
package com.birdeye.campaign.elasticsearch.service;

import com.birdeye.campaign.elasticsearch.request.ElasticSearchBaseRequest;
import com.birdeye.campaign.elasticsearch.request.ElasticSearchInsertRequest;
import com.birdeye.campaign.elasticsearch.request.ElasticSearchSelectRequest;
import com.birdeye.campaign.elasticsearch.request.ElasticSearchUpdateRequest;

import io.searchbox.core.SearchResult;
import io.searchbox.core.search.aggregation.Aggregation;

/**
 ** File:         ElasticSearchService.java
 ** Created:      11 Feb 2019
 ** Author:       sahilarora
 **
 ** This code is copyright (c) BirdEye Software India Pvt. Ltd.
 **/
public interface ElasticSearchService {
	
	/**
	 * Transport service for ES
	 *
	 * Returns same json in the same heirarchy as resulted in ES Kibana Console
	 *
	 * @param request
	 * @return
	 */
	SearchResult execute(ElasticSearchBaseRequest request);

	/**
	 * Transport service for ES
	 *
	 * Returns object mapped with json defined in aggs{} in the ES query search result
	 *
	 * @param request
	 * @param aggregationName
	 * @param aggregationType
	 * @return
	 */
	<T extends Aggregation> T executeAndFetchAggregations(ElasticSearchBaseRequest request, String aggregationName, Class<T> aggregationType);

	/**
	 * @param request
	 * @return
	 */
	 <T> T insert(ElasticSearchInsertRequest<T> request);
	
	/**
	 * @param request
	 * @return
	 */
	<T> T update(ElasticSearchUpdateRequest<T> request);

	/**
	 * @param request
	 * @return
	 */
	<T> T select(ElasticSearchSelectRequest<T> request);

	String executeESQuery(ElasticSearchBaseRequest request, String reportIndexUrl);
	
}
