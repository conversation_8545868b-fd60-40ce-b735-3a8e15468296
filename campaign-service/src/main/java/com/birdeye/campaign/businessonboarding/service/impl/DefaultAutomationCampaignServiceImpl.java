package com.birdeye.campaign.businessonboarding.service.impl;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.appointment.service.AppointmentService;
import com.birdeye.campaign.businessonboarding.service.DefaultAutomationCampaignService;
import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.CampaignTriggerTypeCache;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.dto.AppointmentRecallTypesDTO;
import com.birdeye.campaign.dto.AppointmentScheduleInfo;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.dto.RecallDueDateInfo;
import com.birdeye.campaign.dto.RecallDueDateInfo.DueDateInfo;
import com.birdeye.campaign.dto.RecallDueDateInfo.FallbackDateInfo;
import com.birdeye.campaign.dto.RuleCondition;
import com.birdeye.campaign.dto.RuleExpression;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.CampaignCondition;
import com.birdeye.campaign.entity.CampaignTriggerType;
import com.birdeye.campaign.enums.AppointmentStatusEnum;
import com.birdeye.campaign.enums.CampaignRunTypeEnum;
import com.birdeye.campaign.enums.GroupByTimeEnum;
import com.birdeye.campaign.enums.ScheduleAtEnum;
import com.birdeye.campaign.platform.constant.CampaignModificationUserActionEnum;
import com.birdeye.campaign.platform.constant.CampaignStatusEnum;
import com.birdeye.campaign.platform.constant.CampaignTriggerTypeEnum;
import com.birdeye.campaign.platform.constant.CampaignTypeEnum;
import com.birdeye.campaign.rule.utils.RuleEngine;
import com.birdeye.campaign.service.CampaignModificationAuditService;
import com.birdeye.campaign.service.dao.AutomationCampaignDao;
import com.birdeye.campaign.utils.AppointmentRecallUtils;
import com.birdeye.campaign.utils.CampaignModificationAuditUtils;
import com.birdeye.campaign.utils.CoreUtils;

@Service("defaultAutomationCampaignService")
public class DefaultAutomationCampaignServiceImpl implements DefaultAutomationCampaignService {
	
	private static final Logger		logger				= LoggerFactory.getLogger(DefaultAutomationCampaignServiceImpl.class);
	
	@Autowired
	private AutomationCampaignDao	automationCampaignDao;
	
	@Autowired
	private AppointmentService		appointmentService;
	
	@Autowired
	private CampaignModificationAuditService	campaignModificationAuditService;
	
	private static final String		scheduleType		= "before";
	
	private static final String		sendOrderConstant	= "after";
	
	private static final Integer	scheduledConstant	= 6;
	
	
	
	private Map<String, String> getMvelParamsFromConditions(Map<String, RuleCondition> conditions) {
		Map<String, String> paramsAndTypes = new HashMap<>();
		for (RuleCondition condition : conditions.values()) {
			paramsAndTypes.put(CoreUtils.convertToValidIdentifier(condition.getOperand()), condition.getType());
		}
		return paramsAndTypes;
	}
	
	private String getTriggerTypeByEvent(String event) {
		if (StringUtils.isEmpty(event) || "Contact is added to Birdeye".equalsIgnoreCase(event))
			event = CampaignTriggerTypeEnum.CONTACT_ADDED.getType();
		
		CampaignTriggerTypeCache triggerTypeCache = CacheManager.getInstance().getCache(CampaignTriggerTypeCache.class);
		CampaignTriggerType triggerType = triggerTypeCache.getCampaignTriggerTypeByName(event);
		if (triggerType != null)
			return triggerType.getTriggerName();
		return null;
	}
	
	/**
	 * Prepare Default Campaign Data
	 * 
	 * @param enterpriseId,
	 *            userId, emailTemplateId, smsTemplateId, campaignName
	 */
	private Campaign prepareDefaultCampaignData(Integer enterpriseId, Integer userId, Integer emailTemplateId, Integer smsTemplateId, String campaignName) {
		Campaign campaign = new Campaign();
		campaign.setEnterpriseId(enterpriseId);
		campaign.setName(campaignName);
		campaign.setSmsTemplateId(smsTemplateId);
		campaign.setTemplateId(emailTemplateId);
		campaign.setIsDefault(1);
		campaign.setCreatedBy(userId);
		return campaign;
	}
	
	/**
	 * Prepare Default Campaign Condition Data
	 * 
	 * @param enterpriseId,
	 *            userId, campaignId
	 */
	private CampaignCondition prepareDefaultCampaignConditionData(Integer enterpriseId, Integer userId, Integer campaignId) {
		CampaignCondition campaignCondition = new CampaignCondition();
		campaignCondition.setEnterpriseId(enterpriseId);
		campaignCondition.setCampaignId(campaignId);
		campaignCondition.setUpdatedBy(userId);
		List<String> defaultContactSources = new ArrayList<>(Arrays.asList("dashboard", "sftp", "integration", "api"));
		campaignCondition.setContactSources(defaultContactSources);
		return campaignCondition;
	}
	
	/**
	 * Set Appointment specific conditions and save campaign
	 * 
	 * @param CampaignCondition,
	 *            status, priority, enterpriseId,type,triggerType
	 */
	private Campaign defaultOnGoingCampaignForAppointment(Campaign campaign, CampaignStatusEnum status, String priority, Integer enterpriseId, String type, String triggerType) {
		campaign.setType(type);
		campaign.setRunType(CampaignRunTypeEnum.ONGOING.getRunType());
		campaign.setStatus(status.getStatus());
		campaign.setPriorityOrder(status.getPriorityOrder());
		campaign.setSendReminder(1);
		campaign.setReminderCount(3);
		campaign.setPriority(priority);
		campaign.setTriggerType(getTriggerTypeByEvent(triggerType));
		return automationCampaignDao.saveDefaultAutomationCampaign(campaign, enterpriseId);
	}

	/**
	 * Set Trigger Conditions for Ongoing Default Appointments Campaign
	 * 
	 * @param CampaignCondition
	 */
	private void setTriggerConditionsForDefaultAppointmentReminderCampaign(CampaignCondition ongoingCampaignCondition) {
		
		List<String> value = Arrays.asList(AppointmentStatusEnum.CONFIRMED.getType(), AppointmentStatusEnum.BOOKED.getType());
		RuleCondition ruleCondition = new RuleCondition(7, Constants.APPOINTMENT_STATUS_FILTER_NAME, "IS_ANY_OF", "text_multi", value);
		Map<String, RuleCondition> conditions = new HashMap<>();
		conditions.put("1", ruleCondition);
		RuleExpression triggerExpression = new RuleExpression();
		triggerExpression.setExpression(Constants.DEFAULT_APPOINTMENT_REMINDER_TRIGGER_EXPRESSION);
		triggerExpression.setConditions(conditions);
		ongoingCampaignCondition.setTriggerRuleExpression(triggerExpression);
		ongoingCampaignCondition.setTriggerMvelExpression(RuleEngine.buildRuleExpression(triggerExpression));
		ongoingCampaignCondition.setTriggerMvelParamsAndTypes(getMvelParamsFromConditions(triggerExpression.getConditions()));
	}
	
	/**
	 * Set Appointment Schedule for Ongoing Default Appointments Reminders Campaign
	 * 
	 * @param CampaignCondition
	 */
	private void setAppointmentScheduleForDefaultAppointmentReminderCampaign(CampaignCondition campaignCondition) {
		AppointmentScheduleInfo appointmentScheduleInfoInDays = new AppointmentScheduleInfo(3, GroupByTimeEnum.DAYS.getGroupBy(), StringUtils.EMPTY, ScheduleAtEnum.APPOINTMENT_TIME.getScheduleAt(),Constants.DAYS_OF_WEEK,  StringUtils.EMPTY);
		AppointmentScheduleInfo appointmentScheduleInfoInHours = new AppointmentScheduleInfo(3, GroupByTimeEnum.HOURS.getGroupBy(), StringUtils.EMPTY, ScheduleAtEnum.APPOINTMENT_TIME.getScheduleAt(), Collections.emptyList(),  StringUtils.EMPTY);
		AppointmentScheduleInfo appointmentScheduleInfoInWeeks = new AppointmentScheduleInfo(3, GroupByTimeEnum.WEEKS.getGroupBy(), StringUtils.EMPTY, ScheduleAtEnum.APPOINTMENT_TIME.getScheduleAt(),Constants.DAYS_OF_WEEK,  StringUtils.EMPTY);
		List<AppointmentScheduleInfo> appointmentScheduleInfoList = new ArrayList<>(Arrays.asList(appointmentScheduleInfoInDays, appointmentScheduleInfoInHours, appointmentScheduleInfoInWeeks));
		campaignCondition.setAppointmentScheduleInfo(appointmentScheduleInfoList);
		campaignCondition.setScheduleinHours(null);
	}
	
	/**
	 * Prepare recall name list from recall response received from appointment service
	 * 
	 * @param appointmentlist,
	 *            enterpriseId
	 */
	private List<String> getRecallNamesFromRecallTypes(CachedCollectionWrapper<AppointmentRecallTypesDTO> response, Integer enterpriseId) {
		if (response == null) {
			logger.info("Empty response recieved from Appointment Api for enterpriseId {}", enterpriseId);
			return new ArrayList<>();
		}
		List<AppointmentRecallTypesDTO> appointmentList = response.getElementsList();
		if (CollectionUtils.isEmpty(appointmentList)) {
			logger.info("Empty recall types list recieved from Appointment Api for enterpriseId {}", enterpriseId);
			return new ArrayList<>();
		}
		return appointmentList.stream().map(e -> e.getName()).collect(Collectors.toList());
	}
	
	/**
	 * Prepare executionDateInfo for Default Appointment Recall Automation
	 * 
	 * @param enterpriseId
	 */
	private RecallDueDateInfo prepareExecutionDateInfo(Integer enterpriseId) {
		CachedCollectionWrapper<AppointmentRecallTypesDTO> response = appointmentService.getAppointmentRecallTypesByAccountId(enterpriseId);
		List<String> appointmentRecallTypesList = getRecallNamesFromRecallTypes(response, enterpriseId);
		DueDateInfo dueDateInfo = new DueDateInfo();
		dueDateInfo.setRecallTypes(appointmentRecallTypesList);
		FallbackDateInfo fallbackDateInfo = new FallbackDateInfo();
		fallbackDateInfo.setScheduled(scheduledConstant);
		fallbackDateInfo.setScheduledBy(GroupByTimeEnum.MONTHS.getGroupBy());
		fallbackDateInfo.setSendOrder(sendOrderConstant);
		RecallDueDateInfo recallDueDateInfo = new RecallDueDateInfo();
		recallDueDateInfo.setDueDateInfo(dueDateInfo);
		recallDueDateInfo.setFallbackInfo(fallbackDateInfo);
		return recallDueDateInfo;
	}
	
	/**
	 * Set Appointment Schedule and Due Date Info for Ongoing Default Appointments Recall Campaign
	 * 
	 * @param CampaignCondition
	 */
	private void setAppointmentScheduleForDefaultAppointmentRecallCampaign(CampaignCondition campaignCondition) {
		AppointmentScheduleInfo appointmentScheduleInfoInWeeks = new AppointmentScheduleInfo(2, GroupByTimeEnum.WEEKS.getGroupBy(), scheduleType);
		List<AppointmentScheduleInfo> appointmentScheduleInfoList = new ArrayList<>(Arrays.asList(appointmentScheduleInfoInWeeks));
		campaignCondition.setAppointmentScheduleInfo(appointmentScheduleInfoList);
		campaignCondition.setScheduleinHours(AppointmentRecallUtils.prepareRecallScheduleInfo(appointmentScheduleInfoList));
		campaignCondition.setExecutionDateInfo(prepareExecutionDateInfo(campaignCondition.getEnterpriseId()));
	}
	
	/**
	 * Create Default Automation Campaign for a Business
	 * 
	 * Create a Appointment Reminder Campaign
	 * 
	 * @param enterpriseId,
	 *            userId, emailTemplateId, smsTemplateId, status, priority
	 */
	@Override
	public Integer createDefaultOnGoingCampaignForAutomationReminder(Integer enterpriseId, Integer inputUserId, Integer emailTemplateId, Integer smsTemplateId, CampaignStatusEnum status,
			String priority) {
		logger.info("Create Deafult Ongoing Campaign for Automation Reminder for business Id {}", enterpriseId);
		Integer userId = inputUserId != null ? inputUserId : 12;
		Campaign campaign = defaultOnGoingCampaignForAppointment(
				prepareDefaultCampaignData(enterpriseId, userId, emailTemplateId, smsTemplateId, Constants.DEFAULT_APPOINTMENT_REMINDER_AUTOMATION_NAME), status, priority, enterpriseId,
				CampaignTypeEnum.APPOINTMENT_REMINDER.name(), CampaignTriggerTypeEnum.BEFORE_APPOINTMENT_DATE.getType());
		CampaignCondition campaignCondition = prepareDefaultCampaignConditionData(enterpriseId, userId, campaign.getId());
		campaignCondition.setLvlAlias("loc");
		campaignCondition.setEvent(getTriggerTypeByEvent(CampaignTriggerTypeEnum.BEFORE_APPOINTMENT_DATE.getType()));
		setTriggerConditionsForDefaultAppointmentReminderCampaign(campaignCondition);
		setAppointmentScheduleForDefaultAppointmentReminderCampaign(campaignCondition);
		automationCampaignDao.saveDefaultAutomationCampaignCondition(campaignCondition, enterpriseId);
		
		// BIRD-72687 - Maintain audit of campaign modification(Async call)
		// Future case: For any change in the campaign/templates entities, consider its handling in auditing flow.
		campaignModificationAuditService.prepareAndPublishCampaignModificationEvent(CampaignModificationAuditUtils.prepareAllCampaignDataDTO(null, null, null),
				CampaignModificationAuditUtils.prepareAllCampaignDataDTO(campaign, campaignCondition, null), userId, CampaignModificationUserActionEnum.CREATE.getUserActionType(),
				Instant.now().toEpochMilli(), enterpriseId, CoreUtils.getBooleanValueFromInteger(campaign.getIsSplitCampaign()));
		
		return campaignCondition.getId();
	}
	
	/**
	 * Create Default Automation Campaign for a Business
	 * 
	 * Create a Appointment Recall Campaign
	 * 
	 * @param enterpriseId,
	 *            userId, emailTemplateId, smsTemplateId, status, priority
	 */
	@Override
	public Integer createDefaultOnGoingCampaignForAutomationRecall(Integer enterpriseId, Integer inputUserId, Integer emailTemplateId, Integer smsTemplateId, CampaignStatusEnum status,
			String priority) {
		logger.info("Create Deafult Ongoing Campaign for Automation Recall for business Id {}", enterpriseId);
		Integer userId = inputUserId != null ? inputUserId : 12;
		Campaign campaign = defaultOnGoingCampaignForAppointment(
				prepareDefaultCampaignData(enterpriseId, userId, emailTemplateId, smsTemplateId, Constants.DEFAULT_APPOINTMENT_RECALL_AUTOMATION_NAME), status, priority, enterpriseId,
				CampaignTypeEnum.APPOINTMENT_RECALL.name(), CampaignTriggerTypeEnum.APPOINTMENT_RECALL.getType());
		CampaignCondition campaignCondition = prepareDefaultCampaignConditionData(enterpriseId, userId, campaign.getId());
		campaignCondition.setLvlAlias("loc");
		campaignCondition.setEvent(getTriggerTypeByEvent(CampaignTriggerTypeEnum.APPOINTMENT_RECALL.getType()));
		setAppointmentScheduleForDefaultAppointmentRecallCampaign(campaignCondition);
		setConditionsForDefaultAppointmentRecallCampaign(campaignCondition);
		automationCampaignDao.saveDefaultAutomationCampaignCondition(campaignCondition, enterpriseId);
		
		// BIRD-72687 - Maintain audit of campaign modification(Async call)
		// Future case: For any change in the campaign/templates entities, consider its handling in auditing flow.
		campaignModificationAuditService.prepareAndPublishCampaignModificationEvent(CampaignModificationAuditUtils.prepareAllCampaignDataDTO(null, null, null),
				CampaignModificationAuditUtils.prepareAllCampaignDataDTO(campaign, campaignCondition, null), userId, CampaignModificationUserActionEnum.CREATE.getUserActionType(),
				Instant.now().toEpochMilli(), enterpriseId, CoreUtils.getBooleanValueFromInteger(campaign.getIsSplitCampaign()));
		
		return campaignCondition.getId();
	}
	
	/**
	 * Sets the conditions for the default appointment recall campaign by creating the default rule expression.
	 * The appointment recall communication is sent for all appointment statuses, so the default completed status is added.
	 *
	 * @param campaignCondition The CampaignCondition object to which the rule expression will be set.
	 */
	private void setConditionsForDefaultAppointmentRecallCampaign(CampaignCondition campaignCondition) {
		RuleExpression ruleExpression = createDefaultAppointmentRecallRuleExpression();
		campaignCondition.setTriggerRuleExpression(ruleExpression);
		campaignCondition.setTriggerMvelExpression(RuleEngine.buildRuleExpression(ruleExpression));
		campaignCondition.setTriggerMvelParamsAndTypes(getMvelParamsFromConditions(ruleExpression.getConditions()));
	
	}
	
	public static RuleExpression createDefaultAppointmentRecallRuleExpression() {
        RuleExpression ruleExpression = new RuleExpression();
        ruleExpression.setExpression("1");

        // Create RuleCondition
        RuleCondition condition = new RuleCondition();
        condition.setId(7);
        condition.setOperand("Appointment status");
        condition.setOperator("IS_ANY_OF");
        condition.setType("text_multi");
        condition.setValue(new String[]{"completed"});
        condition.setCustomFieldSource(null);

        // Add condition to conditions map
        HashMap<String, RuleCondition> conditions = new HashMap<>();
        conditions.put("1", condition);

        // Set conditions to RuleExpression
        ruleExpression.setConditions(conditions);

        return ruleExpression;
    }
}
