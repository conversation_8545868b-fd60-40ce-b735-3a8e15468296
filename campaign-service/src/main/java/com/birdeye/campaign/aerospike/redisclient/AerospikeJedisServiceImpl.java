package com.birdeye.campaign.aerospike.redisclient;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aerospike.client.AerospikeClient;
import com.aerospike.client.AerospikeException;
import com.aerospike.client.Bin;
import com.aerospike.client.Key;
import com.aerospike.client.Record;
import com.aerospike.client.ResultCode;
import com.aerospike.client.Value;
import com.aerospike.client.policy.Policy;
import com.aerospike.client.policy.ReadModeSC;
import com.aerospike.client.policy.RecordExistsAction;
import com.aerospike.client.policy.WritePolicy;

@Service("aerospikeJedisService")
public class AerospikeJedisServiceImpl implements AerospikeJedisService {

	@Autowired
	private AerospikeClient asClient;
	
	private WritePolicy writePolicy;
	
	private Policy readPolicy;

	private static final String NAMESPACE = "campaigns";
	private static final String KEY_BIN = "key-bin";
	private static final String VALUE = "value";
	
	public static final int DEFAULT_ACQUIRE_TIMEOUT_MILLIS = 10000;// 10 sec
	public static final int DEFAULT_WAIT_TIMEOUT_MILLIS = 1000;// 1 sec


	private static final Logger LOGGER = LoggerFactory.getLogger(AerospikeJedisServiceImpl.class);

	@PostConstruct
	public void init()
	{
		writePolicy = new WritePolicy();
		writePolicy.socketTimeout=10000;
		writePolicy.maxRetries = 0;
		writePolicy.recordExistsAction=RecordExistsAction.REPLACE;
		
		readPolicy  = new Policy();
		readPolicy.readModeSC=ReadModeSC.SESSION; // 
		readPolicy.maxRetries=0; //DB Call will be hit, so opting for no retry
		readPolicy.socketTimeout=5000;
		writePolicy.sendKey=true;
	}
	
	@Override
	public String set(Object key, Object value, String set,int expiration) {
		return set(null, key, value, set,expiration);
	}

	private String set(WritePolicy wp, Object key, Object value, String set,int expiration) {
		Key asKey = new Key(NAMESPACE, set, Value.get(key));
		Bin keyBin = new Bin(KEY_BIN, key);
		Bin valueBin = new Bin(VALUE, Value.get(value));
		WritePolicy wPolicy=wp==null?this.writePolicy:wp; 
		wPolicy.expiration=expiration;
		this.asClient.put(wPolicy, asKey, keyBin, valueBin);
		return "OK";
	}

	@Override
	public long setIfNotExists(Object key, Object value, String set,int expiration) {
		try {
			WritePolicy wp = new WritePolicy();
			wp.recordExistsAction = RecordExistsAction.CREATE_ONLY;
			set(wp, key, value, set,expiration);
			return 1;
		} catch (AerospikeException e) {
			LOGGER.error("While setting the key {}, in set {} error :: {}", key, set, e.getMessage());
			if (e.getResultCode() == ResultCode.KEY_EXISTS_ERROR) {
				return 0;
			} else {
				throw e;
			}
		}
	}

	@Override
	public long setxx(Object key, Object value, String set,int expiration) {
		try {
			WritePolicy wp = new WritePolicy();
			wp.recordExistsAction = RecordExistsAction.REPLACE_ONLY;
			set(wp, key, value, set,expiration);
			return 1;
		} catch (AerospikeException e) {
			if (e.getResultCode() == ResultCode.KEY_NOT_FOUND_ERROR)
				return 0;
			else
				throw e;
		}
	}

	@Override
	public boolean exists(Object key, String set) {
		Key asKey = new Key(NAMESPACE, set, Value.get(key));
		return this.asClient.exists(readPolicy, asKey);
	}

	@Override
	public long delete(Object key, String set) {
		Key asKey = new Key(NAMESPACE, set, Value.get(key));
		this.asClient.delete(null, asKey);
		return 1;
	}

	@Override
	public String get(Object key, String set) {
		Key asKey = new Key(NAMESPACE, set, Value.get(key));
		Record record = this.asClient.get(readPolicy, asKey, VALUE);
		if (record == null)
			return null;
		return String.valueOf(record.getValue(VALUE));
	}

	@Override
	public List<String> multipleGet(String set, Object[] keys) {
		Key[] asKeys = new Key[keys.length];
		for (int i = 0; i < keys.length; i++) {
			asKeys[i] = new Key(NAMESPACE, set, Value.get(keys[i]));
		}
		Record[] records = this.asClient.get(null, asKeys, VALUE);
		List<String> result = new ArrayList<>();
		for (Record record : records) {
			result.add((record == null) ? null : (String) record.getValue(VALUE));
		}
		return result;
	}

	@Override
	public String multipleSet(final Map<String, String> keysvalues, String set,int expiration) {
		if (keysvalues == null || keysvalues.isEmpty())
			return "Keys and Values mismatch";

		for (Entry<String, String> keyvalue : keysvalues.entrySet()) {
			set(null, keyvalue.getKey(), Value.get(keyvalue.getValue()), set,expiration);
		}
		return "OK";
	}

	@Override
	public long multipleSetIfNotExists(final Map<String, String> keysvalues, String set,int expiration) {
		if (keysvalues == null || keysvalues.isEmpty())
			return 0L;
		long retVal = 0L;
		WritePolicy wp = new WritePolicy();
		wp.totalTimeout = asClient.writePolicyDefault.totalTimeout;
		wp.recordExistsAction = RecordExistsAction.CREATE_ONLY;
		try {
			for (Entry<String, String> keyvalue : keysvalues.entrySet()) {
				set(wp, keyvalue.getKey(), Value.get(keyvalue.getValue()), set,expiration);
				retVal++;
			}
		} catch (AerospikeException e) {
			LOGGER.error("while setting key values {} error:: {} ", keysvalues, e);
			if (e.getResultCode() != ResultCode.KEY_EXISTS_ERROR)
				throw e;
		}
		return retVal;
	}

	@Override
	public String setIfExists(Object key, Object value, String set,int expiration) {
		WritePolicy wp = new WritePolicy();
		wp.expiration = expiration;
		set(wp, key, Value.get(value), set,expiration);
		return "OK";
	}

	public long expire(Object key, long expiration, String set) {
		try {
			Key asKey = new Key(NAMESPACE, set, Value.get(key));
			WritePolicy wp = new WritePolicy();
			wp.expiration = (int) expiration;
			wp.recordExistsAction = RecordExistsAction.UPDATE_ONLY;
			wp.totalTimeout = asClient.writePolicyDefault.totalTimeout;
			this.asClient.touch(wp, asKey);
			return 1;
		} catch (AerospikeException e) {
			LOGGER.error("while updating the TTL for key {} error :: {} occurs",key,e);
			if (e.getResultCode() == ResultCode.KEY_NOT_FOUND_ERROR) {
				return 0;
			} else {
				throw e;
			}
		}
	}

	public long pexpire(Object key, long expiration,String set) {
		return expire(key, expiration / 1000,set);
	}

	public long expireAt(Object key, long unixTime, String set) {
		try {
			long now = System.currentTimeMillis();
			Key asKey = new Key(NAMESPACE, set, Value.get(key));
			WritePolicy wp = new WritePolicy();
			wp.recordExistsAction = RecordExistsAction.UPDATE_ONLY;
			wp.expiration = (int) ((unixTime - now) / 1000);
			this.asClient.touch(wp, asKey);
			return 1;
		} catch (AerospikeException e) {
			LOGGER.error("while updating the TTL for key {} error :: {} occurs",key,e);
			if (e.getResultCode() == ResultCode.KEY_NOT_FOUND_ERROR) {
				return 0;
			} else
				throw e;
		}
	}
	
	@Override
	public String getSet(Object key, Object value,String set) {
		Key asKey = new Key(NAMESPACE, set, Value.get(key));
		return (String)this.asClient.execute(this.writePolicy, asKey, "redis", "GETSET", Value.get(VALUE), Value.get(value));
	}
	
	@Override
	public boolean tryLock(String key, String set, int expiration) throws InterruptedException {
		String value = String.valueOf(System.currentTimeMillis() + expiration * 1000);
		int timeToWait = DEFAULT_ACQUIRE_TIMEOUT_MILLIS;
		while (timeToWait >= 0) {
			LOGGER.info("for thread {} and key {} trying to take lock",Thread.currentThread(),key);
			if (setIfNotExists(key,value, set, expiration)!=1) {
				timeToWait -= DEFAULT_WAIT_TIMEOUT_MILLIS;
				LOGGER.info("For thread {} and key {} trying to take lock and waiting time is {}",Thread.currentThread(),key,timeToWait);
				Thread.sleep(DEFAULT_WAIT_TIMEOUT_MILLIS);
			} else {
				LOGGER.info("for thread {} and key {} lock got acquired",Thread.currentThread(),key);
				return true;
			}
		}
		LOGGER.info("For thread {} and key {} lock did not acquired",Thread.currentThread(),key);
		return false;
	}
	@Override
	public void releaseLock(String key, String set) {
		LOGGER.info("Releasing the lock for key {}",key);
		Key asKey = new Key(NAMESPACE, set, Value.get(key));
		this.asClient.delete(null, asKey);
	}
	
	@Override
	public Long getUniqueBatchId(String set, String key, String bin, long currentEpocTime, long batchGapTimeInMs) {
		try {
			Key asKey = new Key(NAMESPACE, set, key);
			Object result = this.asClient.execute(this.writePolicy, asKey, "campaign-udf-v1", "getUniqueBatchId", Value.get(bin), Value.get(currentEpocTime), Value.get(batchGapTimeInMs));
			return ((Long) result).longValue();
		} catch (Exception exe) {
			LOGGER.error("Error while fetching unique Batch Id for key {} error :: {} occurs", key, exe);
		}
		// if any error in aerospike the
		return currentEpocTime;
	}
}