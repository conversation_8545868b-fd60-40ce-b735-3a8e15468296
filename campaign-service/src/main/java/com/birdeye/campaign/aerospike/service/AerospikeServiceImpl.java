package com.birdeye.campaign.aerospike.service;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aerospike.client.AerospikeClient;
import com.aerospike.client.Key;
import com.aerospike.client.Record;

@Service("aerospikeServiceImpl")
public class AerospikeServiceImpl implements AerospikeService {
	
	private static final Logger	LOGGER				= LoggerFactory.getLogger(AerospikeServiceImpl.class);
	
	private static final String	SUCCESS				= "SUCCESS";
	private static final String	FAILURE				= "FAILURE";
	private static final String	KEY_NOT_FOUND		= "Key does not exist in cache";
	private static final String	NULL_VALUE_FOUND	= "Cache exist but value is null";
	
	@Autowired
	AerospikeClient				aerospikeClient;
	
	@Override
	public Object getEntry(String namespace, String set, String key) {
		// We can always check cache names which are visible to
		// aerospikeCacheManager
		Key asKey = new Key(namespace, set, key);
		// Default write policy.
		Record record = aerospikeClient.get(null, asKey);
		if (record == null) {
			LOGGER.info(KEY_NOT_FOUND);
			return null;
		} else {
			Map<String, Object> values = null;
			if (record.bins != null && !record.bins.entrySet().isEmpty()) {
				values = new HashMap<>();
				for (Map.Entry<String, Object> entry : record.bins.entrySet()) {
					values.put(entry.getKey(), entry.getValue());
				}
			}
			Object value = values.get("value");
			if (value == null)
				LOGGER.info(NULL_VALUE_FOUND);
			return value;
		}
	}
	
	@Override
	public String deleteEntry(String namespace, String set, String key) {
		boolean result = false;
		Key asKey = new Key(namespace, set, key);
		// Default write policy.
		result = aerospikeClient.delete(null, asKey);
		return result ? SUCCESS : FAILURE;
	}

	/**
	 * Method to delete an entire set in aerospike cache.
	 * 
	 * @param namespace
	 * @param set
	 * @return
	 */
	@Override
	public String deleteSet(String namespace, String set) {
		try {
			aerospikeClient.truncate(null, namespace, set, null);
			return SUCCESS;
		} catch (Exception e) {
			LOGGER.info("Exception occurred while truncating the set: {}, namespace:{}. Exception: {}", set, namespace, ExceptionUtils.getStackTrace(e));
			return FAILURE;
		}
		
	}
	
}
