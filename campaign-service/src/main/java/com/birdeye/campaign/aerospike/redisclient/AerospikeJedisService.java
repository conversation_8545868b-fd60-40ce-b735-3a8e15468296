package com.birdeye.campaign.aerospike.redisclient;

import java.util.List;
import java.util.Map;

public interface AerospikeJedisService {

	public String set(Object key, Object value, String set, int expiration);

	public long setIfNotExists(Object key, Object value, String set, int expiration);

	public boolean exists(Object key, String set);

	public long delete(Object key, String set);

	public long setxx(Object key, Object value, String set, int expiration);

	public String get(Object key, String set);

	public List<String> multipleGet(String set, Object[] keys);

	public long multipleSetIfNotExists(Map<String, String> keysvalues, String set, int expiration);

	public String setIfExists(Object key, Object value, String set,int expiration);

	public String multipleSet(Map<String, String> keysvalues, String set, int expiration);

	String getSet(Object key, Object value, String set);
	
	public boolean tryLock(String key, String set, int ttl) throws InterruptedException;
	
	public void releaseLock(String key, String set);

	Long getUniqueBatchId(String set, String key, String bin, long currentEpocTime, long batchGapTimeInMs);

}