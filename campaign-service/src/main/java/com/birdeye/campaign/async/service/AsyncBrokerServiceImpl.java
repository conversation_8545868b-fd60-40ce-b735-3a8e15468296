/**
 * @file_name Asy.java
 * @created_date 13 Jun 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.async.service;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.cache.service.CacheManagementService;
import com.birdeye.campaign.response.template.v2.EmailTemplateResponse;
import com.birdeye.campaign.service.impl.ReviewSourceWrapperSRO;
import com.birdeye.campaign.sro.ReviewSourceSRO;

/**
 * @file_name Asy.java
 * @created_date 13 Jun 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@Service("asyncBrokerService")
public class AsyncBrokerServiceImpl implements AsyncBrokerService {
	
	@Autowired
	private CacheManagementService		cacheManagementService;
	
	private static final Logger			logger	= LoggerFactory.getLogger(AsyncBrokerServiceImpl.class);
	/**
	 * @param templateId
	 * @param requestType
	 * @param response
	 */
	@Async
	@Override
	public void evictAndUpdateEmailTemplateResponseCache(Integer templateId, String requestType, EmailTemplateResponse response) {
		cacheManagementService.evictEmailTemplateResponseCache(templateId, requestType);
		cacheManagementService.updateEmailTemplateResponceCache(templateId, requestType, response);
	}
	
	/**
	 * @param templateId
	 * @param response
	 */
	@Async
	@Override
	public void evictAndUpdateSelectedSourcesCache(Integer businessId, Integer templateId, String deviceType, List<ReviewSourceSRO> sources) {
		cacheManagementService.evictSelectedReviewSourcesCache(businessId, templateId, deviceType);
		cacheManagementService.updateSelectedReviewSourcesCache(businessId, templateId, deviceType, new ReviewSourceWrapperSRO(sources));
	}
	
	@Async
	@Override
	public void evictEmailTemplatesListCache(Integer enterpriseId) {
		cacheManagementService.evictEmailTemplatesListCache(enterpriseId);
	}
	
	@Async
	@Override
	public void evictSmsTemplatesListCache(Integer enterpriseId) {
		cacheManagementService.evictSmsTemplatesListCache(enterpriseId);
		logger.info("evicted templates for enterpriseId {}", enterpriseId);
	}
	
	@Async
	@Override
	public void evictGlobalTemplatesCaches(Integer enterpriseId, String source) {
		cacheManagementService.evictGlobalTemplatesMappingCache(enterpriseId);
		cacheManagementService.evictGlobalTemplatesCache(enterpriseId, source);
		logger.info("evicted global templates caches for enterpriseId {} and source {}", enterpriseId, source);
	}
	
	@Async
	@Override
	public void evictDefaultCampaign(Integer enterpriseId) {
		cacheManagementService.evictDefaultCampaignCache(enterpriseId);
	}
	
	@Async
	@Override
	public void evictTemplateLocationMappingCache(Integer enterpriseId, Integer userId) {
		cacheManagementService.evictTemplateLocationMappingCache(enterpriseId, userId);
		logger.info("evicting template location mapping for enterpriseId {} and userId {}", enterpriseId, userId);
	}
	
	@Async
	@Override
	public void evictLocationLevelTemplateForAnEnterpriseCache(Integer enterpriseId) {
		cacheManagementService.evictLocationLevelTemplateForAnEnterpriseCache(enterpriseId);
		logger.info("evicted location level templates for enterpriseId {}", enterpriseId);
	}

	@Async
	@Override
	public void evictAccountTemplatesCache(Integer accountId, String templateType, String communicationCategory) {
		cacheManagementService.evictAccountTemplatesCache(accountId, templateType, communicationCategory);
		logger.info("evicted account templates cache for accountId: {}, templateType: {}, communicationCategory: {}", +accountId, templateType, communicationCategory);
	}
	
	@Async
	@Override
	public void evictEmailTemplateCategoryCache(Integer templateId) {
		cacheManagementService.evictEmailTemplateCategoryCache(templateId);
	}
	
	@Async
	@Override
	public void evictSMSTemplateCategoryCache(Integer templateId) {
		cacheManagementService.evictSMSTemplateCategoryCache(templateId);
	}
	
}
