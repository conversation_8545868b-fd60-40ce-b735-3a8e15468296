/**
 * @file_name CacheManagementServiceImpl.java
 * @created_date 13 Jun 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.cache.service;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.response.template.v2.EmailTemplateResponse;
import com.birdeye.campaign.service.impl.ReviewSourceWrapperSRO;

/**
 * @file_name CacheManagementServiceImpl.java
 * @created_date 13 Jun 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@Service("cacheManagementService")
public class CacheManagementServiceImpl implements CacheManagementService {
	
	@Override
	@CacheEvict(key = "#templateId.toString().concat('-').concat(#requestType.toString())", value = "emailTemplateCache1")
	public void evictEmailTemplateResponseCache(Integer templateId, String requestType) {
		// method to evictEmailTemplateResponseCache
	}
	
	@Override
	@CacheEvict(key = "#businessId.toString().concat('-').concat(#templateId.toString()).concat('-').concat(#deviceType.toString())", value = "selectedReviewSourcesCache1")
	public void evictSelectedReviewSourcesCache(Integer businessId, Integer templateId, String deviceType) {
		// method to evictSelectedReviewSourcesCache
	}
	
	@Override
	@Cacheable(key = "#businessId.toString().concat('-').concat(#templateId.toString()).concat('-').concat(#deviceType.toString())", value = "selectedReviewSourcesCache1", unless = "#result == null")
	public ReviewSourceWrapperSRO updateSelectedReviewSourcesCache(Integer businessId, Integer templateId, String deviceType, ReviewSourceWrapperSRO response) {
		return response;
	}
	
	@Override
	@Cacheable(key = "#templateId.toString().concat('-').concat(#requestType.toString())", value = "emailTemplateCache1", unless = "#result == null")
	public EmailTemplateResponse updateEmailTemplateResponceCache(Integer templateId, String requestType, EmailTemplateResponse response) {
		return response;
	}
	
	@Override
	@CacheEvict(key = "#enterpriseId.toString()", value = "emailTemplatesListCache")
	public void evictEmailTemplatesListCache(Integer enterpriseId) {
		// method to evictEmailTemplatesListCache
	}
	
	@Override
	@CacheEvict(key = "#enterpriseId.toString()", value = "smsTemplatesListCache")
	public void evictSmsTemplatesListCache(Integer enterpriseId) {
		// method to evictSmsTemplatesListCache
	}
	
	@Override
	@CacheEvict(key = "#enterpriseId.toString()", value = "globalTemplateMappingCache")
	public void evictGlobalTemplatesMappingCache(Integer enterpriseId) {
		// method to evictSmsTemplatesListCache
	}
	
	@Override
	@CacheEvict(key = "#enterpriseId.toString().concat('-').concat(#source)", value = "globalTemplatesCache")
	public void evictGlobalTemplatesCache(Integer enterpriseId, String source) {
		// method to evictSmsTemplatesListCache
	}

	@Override
	@CacheEvict(key="#enterpriseId.toString()", value ="defaultOngoingCampaign")
	public void evictDefaultCampaignCache(Integer enterpriseId) {
		// method to evict defaultCampaign
	}

	/**
	 * 
	 * @param enterpriseId
	 * @param userId
	 * 
	 * this cache contains template and location mapping for inbox
	 * need to evict in case of inbox location template creation, update and deletion
	 * if not done properly new/update template will not be visible
	 * key enterpriseId-userId
	 */
	@Override
	@CacheEvict(key = "#enterpriseId.toString().concat('-').concat(#userId.toString())", value = "enterpriseUserLocationTemplateMapping")
	public void evictTemplateLocationMappingCache(Integer enterpriseId, Integer userId) {
		// method to evictenterpriseUSerLocationAccessCache
	}
	
	/***
	 * @param enterpriseId
	 * cache contains location level template for an enterprise
	 * cache is on account level
	 * evict on create, update and delete location level template flows
	 */
	@Override
	@CacheEvict(key = "#enterpriseId.toString()", value = "smsLocationTemplatesListCache")
	public void evictLocationLevelTemplateForAnEnterpriseCache(Integer enterpriseId) {
		// method to smsLocationTemplatesListCache
	}
	
	@Override
	@CacheEvict(value = "accountTemplatesCache", key = "T(String).valueOf(#accountId) + '-' + T(String).valueOf(#templateType) + '-' + T(String).valueOf(#communicationCategory)")
	public void evictAccountTemplatesCache(Integer accountId, String templateType, String communicationCategory) {
		// method to evict accountsTemplatesCache
	}

	@Override
	@CacheEvict(key = "#templateId.toString()", value = "smsTemplateCategoryCache")
	public void evictSMSTemplateCategoryCache(Integer templateId) {
		// method to evictSMSTemplateCategoryCache 
		
	}

	@Override
	@CacheEvict(key = "#templateId.toString()", value = "emailTemplateCategoryCache")
	public void evictEmailTemplateCategoryCache(Integer templateId) {
		// method to evictEmailTemplateCategoryCache
		
	}
}
