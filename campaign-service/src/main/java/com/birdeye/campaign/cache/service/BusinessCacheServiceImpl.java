package com.birdeye.campaign.cache.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.business.service.BusinessService;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.external.service.BusinessExternalService;
import com.birdeye.campaign.request.ProductFeatureRequest;
import com.birdeye.campaign.response.external.BusinessOptionsResponse;
import com.birdeye.campaign.service.BusinessOptionService;

@Service("businessCacheService")
public class BusinessCacheServiceImpl implements BusinessCacheService {
	
	@Autowired
	private BusinessExternalService	businessExternalService;
	
	@Autowired
	private BusinessOptionService	businessOptionService;
	
	@Autowired
	private BusinessService			businessService;
	
	@Override
	@Cacheable(value = "productFeatureCache", unless = "#result == null", key = "#accountId")
	public ProductFeatureRequest getProductFeatureForBusiness(Integer accountId) {
		BusinessOptionsResponse response = businessExternalService.getBusinessOptions(accountId, true);
		boolean isSurveyEnabledForBusiness = businessOptionService.isSurveyEnabledForBusiness(accountId);
		ProductFeatureRequest request = new ProductFeatureRequest();
		request.setIsSurveyEnabled(isSurveyEnabledForBusiness ? 1 : 0);
		request.setEnableReferral(response.getEnableReferral());
		request.setIsSuspectSupportOn(response.getIsSuspectSupportOn());
		request.setAppointmentRemindersEnabled(response.getAppointmentRemindersEnabled());
		request.setAppointmentRecallEnabled(response.getAppointmentRecallEnabled());
		request.setAppointmentSchedulingEnabled(response.getAppointmentSchedulingEnabled());
		request.setAppointmentFormEnabled(response.getAppointmentFormsEnabled() != null ? response.getAppointmentFormsEnabled() : 0);
		request.setReviewGenEnabled(response.getReviewGenEnabled());
		request.setBulkCustomSms(response.getBulkCustomSms());
		request.setCampaignLocationUserAccess(response.getCampaignLocationUserAccess());
		request.setLocationLevelTemplateEnabled(response.getLocationTemplateEnabled() != null ? response.getLocationTemplateEnabled() : 0);
		return request;
	}
	
	@Cacheable(value = "validBusinessCache", unless = "#result == null", key = "#businessId")
	public BusinessEnterpriseEntity getValidBusinessById(Integer businessId) {
		return businessService.getValidBusinessById(businessId);
	}
	
}
