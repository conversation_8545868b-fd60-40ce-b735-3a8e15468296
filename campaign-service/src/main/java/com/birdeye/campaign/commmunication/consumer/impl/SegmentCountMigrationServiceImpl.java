package com.birdeye.campaign.commmunication.consumer.impl;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.commmunication.consumer.SegmentCountMigrationService;
import com.birdeye.campaign.communication.message.PromotionCommMessage;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.entity.BusinessSmsTemplate;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.MessengerCampaign;
import com.birdeye.campaign.entity.Promotion;
import com.birdeye.campaign.exception.InputValidationException;
import com.birdeye.campaign.executor.services.CampaignCallable;
import com.birdeye.campaign.executor.services.CampaignExecutorService;
import com.birdeye.campaign.executor.services.ExecutorCommonService;
import com.birdeye.campaign.external.service.IContactExternalService;
import com.birdeye.campaign.kafka.service.KafkaService;
import com.birdeye.campaign.repository.CampaignRepo;
import com.birdeye.campaign.repository.PromotionRepo;
import com.birdeye.campaign.response.kontacto.KontactoDTO;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.CampaignSmsService;
import com.birdeye.campaign.service.TemplateHelperService;
import com.birdeye.campaign.service.dao.BusinessSmsTemplateDao;
import com.birdeye.campaign.utils.BatchUtils;
import com.birdeye.campaign.utils.TwilioAPIUtils;
import com.google.common.collect.Lists;
import com.google.gson.Gson;

@Service("segmentCountMigrationServiceImpl")
public class SegmentCountMigrationServiceImpl implements SegmentCountMigrationService {
	
	private static final Logger		logger									= LoggerFactory.getLogger(SegmentCountMigrationServiceImpl.class);
	
	private static final String		SEGMENT_COUNT_MIGRATION_REQUESTS_BATCH	= "100";
	
	@Autowired
	@Qualifier(Constants.CAMPAIGN_UI_COMPLETABLE_FUTURE_TASK_EXECUTOR)
	private ThreadPoolTaskExecutor	threadPoolTaskExecutor;
	
	@Autowired
	private KafkaService			kafkaService;
	
	@Autowired
	private CacheService			cacheService;
	
	@Autowired
	private ExecutorCommonService	executorCommonService;
	
	@Autowired
	private BusinessSmsTemplateDao			businessSmsTemplateDao;
	
	@Autowired
	private CampaignSmsService		campaignSmsService;
	
	@Autowired
	private PromotionRepo			promotionRepo;
	
	@Autowired
	private CampaignRepo			campaignRepo;
	
	@Autowired
	private TemplateHelperService	templateHelperService;
	
	@Autowired
	private IContactExternalService contactExternalService;
	
	@Async
	@Override
	public void migratePromotionCommSegmentCountInBatch(List<Promotion> requests) {
		if (CollectionUtils.isEmpty(requests)) {
			logger.error("[migratePromotionCommSegmentCountInBatch] : no request ids");
			throw new InputValidationException(ErrorCodes.NO_REQUEST_ID_TO_MIGRATE.getMessage());
		}
		
		logger.info("[migratePromotionCommSegmentCountInBatch] : starting promotion comm segment count batch migration between ids {} and {}", requests.get(0).getId(),
				requests.get(requests.size() - 1).getId());
		
		Integer batchSize = Integer.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(SEGMENT_COUNT_MIGRATION_REQUESTS_BATCH, "100"));
		// distribute requestIDs and create worker thread
		List<List<Promotion>> promotionRequestsBatch = Lists.partition(requests, batchSize);
		if (CollectionUtils.isNotEmpty(promotionRequestsBatch)) {
			int totalBatches = promotionRequestsBatch.size();
			logger.info("[migratePromotionCommSegmentCountInBatch] : Number of batches for Promotion Comm Segment Count Migration = {}", totalBatches);
			// thread pool for executor
			CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
			
			BatchUtils.doInBatch(requests, batchSize, data -> executorService.submit(prepareSegmentCountMigrationMessageAndMigrate(data)));
			try {
				executorCommonService.executeTasks(executorService);
			} catch (Exception exe) {
				logger.error("Error while executing task error {}", exe);
			}
		}
		logger.info("[migratePromotionCommSegmentCountInBatch] : finished promotion comm segment count batch migration between ids {} and {}", requests.get(0).getId(),
				requests.get(requests.size() - 1).getId());
	}
	
	//For using below method in the commented lines use SmsDto instead of Sms, and make a call to messenger service to fetch sms data
	private CampaignCallable<Boolean> prepareSegmentCountMigrationMessageAndMigrate(List<Promotion> promotionRequests) {
		return new CampaignCallable<Boolean>("Segment_Count_Migration_Task") {
			@Override
			public Boolean doCall() throws IOException, JSONException {
				int batchSize = promotionRequests.size();
				long minBatchRequestId = promotionRequests.get(0).getId();
				long maxBatchRequestId = promotionRequests.get(batchSize - 1).getId();
				logger.info("Started worker thread SegmentCountMigrationTask for Segment count migration of size : {} for minBatchRequestId : {} and  maxBatchRequestId : {}", batchSize,
						minBatchRequestId, maxBatchRequestId);
				List<Long> promotionRequestIds = promotionRequests.stream().map(Promotion::getId).collect(Collectors.toList());
//				List<Sms> smsRequests = smsRepo.findSmsByReviewRequestIds(promotionRequestIds);
//				Map<Long, List<Sms>> promotionRequestIdToSmsMap = getPromotionIdToSmsMap(smsRequests);
//				List<Sms> uniqueSmsRequests = new ArrayList<>();
//				List<Long> duplicatePromotionRequestIds = new ArrayList<>();
//				for (Entry<Long, List<Sms>> entry : promotionRequestIdToSmsMap.entrySet()) {
//					if (entry.getValue().size() == 1) {
//						uniqueSmsRequests.add(entry.getValue().get(0));
//					} else {
//						duplicatePromotionRequestIds.add(entry.getKey());
//					}
//				}
//				List<Promotion> duplicatePromotionRequests = promotionRepo.findPromotionByIds(duplicatePromotionRequestIds.isEmpty() ? null : duplicatePromotionRequestIds);
//				if (CollectionUtils.isNotEmpty(uniqueSmsRequests) || CollectionUtils.isNotEmpty(duplicatePromotionRequests)) {
//					StringBuilder bulkRequestBody = new StringBuilder();
//					//migrateSegmentCountForUniqueRequests(uniqueSmsRequests, bulkRequestBody);
//					migrateSegmentCountForDuplicateRequest(bulkRequestBody, duplicatePromotionRequests);
//					// send to kafka
//					// String bulkUploadTopic ="promotion-comm-migration-data"
//					if (bulkRequestBody.length() > 0) {
//						kafkaService.pushMessageToKafkaAcknowledged(KafkaTopicTypeEnum.PROMOTION_COMM_MIGRATION_DATA, null, bulkRequestBody.toString());
//					}
//					logger.info("Ended worker thread SegmentCountMigrationTask for Segment count Migration of size {} for minBatchRequestId = {} and  maxBatchRequestId = {}", batchSize,
//							minBatchRequestId, maxBatchRequestId);
//				}
				return true;
			}
			
		};
	}
	
//	private Map<Long, List<Sms>> getPromotionIdToSmsMap(List<Sms> smsRequests) {
//		Map<Long, List<Sms>> promotionRequestIdToSmsMap = new HashMap<>();
//		for (Sms smsRequest : smsRequests) {
//			if (!promotionRequestIdToSmsMap.containsKey(smsRequest.getReviewRequestId())) {
//				promotionRequestIdToSmsMap.put(smsRequest.getReviewRequestId(), new ArrayList<>());
//			}
//			promotionRequestIdToSmsMap.get(smsRequest.getReviewRequestId()).add(smsRequest);
//		}
//		return promotionRequestIdToSmsMap;
//	}
	
//	private void migrateSegmentCountForUniqueRequests(List<Sms> uniqueSmsRequests, StringBuilder bulkRequestBody) throws JSONException {
//		if (CollectionUtils.isNotEmpty(uniqueSmsRequests)) {
//			for (Sms sms : uniqueSmsRequests) {
//				PromotionCommMessage message = new PromotionCommMessage();
//				// setting default values as null so as not to update them
//				message.setStatus(null);
//				message.setUpdatedAt(null);
//				message.setClickTime(null);
//				message.setSegmentCount(TwilioAPIUtils.calculateSMSSegments(sms.getMessageBody()));
//				updateSegmentCountFieldForPromotion(null, sms.getReviewRequestId(), message.getSegmentCount());
//				JSONObject updateJson = new JSONObject();
//				JSONObject actionJson = new JSONObject();
//				actionJson.put("_id", sms.getReviewRequestId());
//				actionJson.put("_routing", getEnterpriseId(sms.getBusinessId()));
//				updateJson.put("update", actionJson);
//				bulkRequestBody.append(updateJson);
//				bulkRequestBody.append("\n");
//				
//				JSONObject docJson = new JSONObject();
//				Gson gson = new Gson();
//				docJson.put("doc", new JSONObject(gson.toJson(message)));
//				// docJson.put("doc_as_upsert", Boolean.TRUE);
//				bulkRequestBody.append(docJson);
//				bulkRequestBody.append("\n");
//			}
//		}
//	}
	
	private void updateSegmentCountFieldForPromotion(Promotion promotion, Long reviewRequestId, Long segmentCount) {
		if (promotion == null) {
			promotion = promotionRepo.findFirstById(reviewRequestId);
		}
		promotion.setSegmentCount(segmentCount);
		promotionRepo.saveAndFlush(promotion);
	}
	
	private void migrateSegmentCountForDuplicateRequest(StringBuilder bulkRequestBody, List<Promotion> duplicatePromotionRequests) throws JSONException {
		if (CollectionUtils.isNotEmpty(duplicatePromotionRequests)) {
			for (Promotion promotionRequest : duplicatePromotionRequests) {
				PromotionCommMessage message = new PromotionCommMessage();
				
				BusinessEnterpriseEntity business = cacheService.getBusinessById(promotionRequest.getBusinessId());
				if (business == null) {
					logger.error("Invalid migration Request no business found for businessId {}", promotionRequest.getBusinessId());
					continue;
				}
				BusinessSmsTemplate smsTemplate = businessSmsTemplateDao.getSMSTemplateByEnterpriseIdAndTemplateId(
						business.getEnterpriseId() != null ? business.getEnterpriseId() : business.getId(), promotionRequest.getTemplateId());
				KontactoDTO customer = contactExternalService.getCustomerByIdWithCustomFieldAndReferralCodeAndTags(promotionRequest.getCustId(), true, true, false);
				Campaign campaign = campaignRepo.getById(promotionRequest.getCampaignId());
				String smsBody = getPromotionSmsBody(promotionRequest, business, smsTemplate, customer, campaign);
				
				// setting default values as null so as not to update them
				message.setStatus(null);
				message.setUpdatedAt(null);
				message.setClickTime(null);
				message.setSegmentCount(TwilioAPIUtils.calculateSMSSegments(smsBody));
				updateSegmentCountFieldForPromotion(promotionRequest, promotionRequest.getId(), message.getSegmentCount());
				JSONObject updateJson = new JSONObject();
				JSONObject actionJson = new JSONObject();
				actionJson.put("_id", promotionRequest.getId());
				actionJson.put("_routing", business.getEnterpriseId() != null ? business.getEnterpriseId() : business.getId());
				updateJson.put("update", actionJson);
				bulkRequestBody.append(updateJson);
				bulkRequestBody.append("\n");
				
				JSONObject docJson = new JSONObject();
				Gson gson = new Gson();
				docJson.put("doc", new JSONObject(gson.toJson(message)));
				bulkRequestBody.append(docJson);
				bulkRequestBody.append("\n");
			}
		}
		
	}

	private String getPromotionSmsBody(Promotion promotionRequest, BusinessEnterpriseEntity business, BusinessSmsTemplate smsTemplate, KontactoDTO customer, Campaign campaign) {
		String smsBody;
		if (campaign != null && campaign.getIsMessengerCampaign() != null && campaign.getIsMessengerCampaign().intValue() == 1) {
			BusinessSmsTemplate freeTextSmsTemplate = templateHelperService.getFreeTextSmsTemplateId();
			if (freeTextSmsTemplate != null && promotionRequest.getTemplateId().equals(freeTextSmsTemplate.getId())) {
				smsTemplate = freeTextSmsTemplate;
			}
			
			if (smsTemplate == null) {
				logger.error("No SMS template found");
				return null;
			}
			MessengerCampaign messengerCampaign = campaignSmsService.validateMessengerCampaign(promotionRequest);
			smsBody = campaignSmsService.getPromotionSmsMessengerCampaignMessage(business, promotionRequest, customer, messengerCampaign, smsTemplate);
		} else {
			if (smsTemplate == null) {
				logger.error("No SMS template configured for businessId {} and templateId {}", promotionRequest.getBusinessId(), promotionRequest.getTemplateId());
				return null;
			}
			smsBody = campaignSmsService.getSmsMessage(smsTemplate, customer, business, null, promotionRequest, StringUtils.EMPTY, null, false);
		}
		return smsBody;
	}
	
	private Integer getEnterpriseId(Integer businessId) {
		BusinessEnterpriseEntity business = cacheService.getBusinessById(businessId);
		if (business == null)
			return businessId;
		
		return business.getEnterpriseId() != null ? business.getEnterpriseId() : business.getId();
	}
	
}
