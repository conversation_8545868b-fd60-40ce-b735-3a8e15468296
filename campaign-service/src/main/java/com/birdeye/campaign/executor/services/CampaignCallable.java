package com.birdeye.campaign.executor.services;

import java.util.Map;
import java.util.concurrent.Callable;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

public abstract class CampaignCallable<T> implements Callable<T> {

	private static final Logger LOG = LoggerFactory.getLogger(CampaignCallable.class);

	private long submissionTime;

	private String name;

	private String callerThread;

	private Map<String, String> mdc;

	public CampaignCallable(String name) {
		this.name = name;
	}

	public String getName() {
		return name;
	}

	public void setSubmissionTime(long submissionTime) {
		this.submissionTime = submissionTime;
	}

	public String getCallerThread() {
		return callerThread == null ? Thread.currentThread().getName() : callerThread;
	}

	public void setCallerThread(String callerThread) {
		this.callerThread = callerThread;
	}

	public Map<?, ?> getMdc() {
		return mdc;
	}

	public void setMdc(Map<String, String> mdc) {
		this.mdc = mdc;
	}

	// Abstract method for actual call body
	public abstract T doCall() throws Exception;

	@Override
	public T call() throws Exception {
		Map<String, String> previous = MDC.getCopyOfContextMap();
		setContext(mdc);
		logWaitTime();
		long startTime = System.currentTimeMillis();
		try {
			return doCall();
		} catch (Exception exe) {
			// Log error details. Should we rethrow it?
			LOG.error("Error in executing {}", details(), exe);
		} finally {
			long timeTaken = System.currentTimeMillis() - startTime;
			if (timeTaken > 20) {
				LOG.info("Time taken by  {} : {}", name, System.currentTimeMillis() - startTime);
			}
			setContext(previous);
		}
		return null;
	}

	private void logWaitTime() {
		long time = System.currentTimeMillis() - submissionTime;
		// Log time in case tasks are waiting more than 20 ms. It can be configurable
		if (time > 20) {
			LOG.info("{} waiting time in queue in ms: {}", details(), time);
		}
	}

	public String details() {
		StringBuilder builder = new StringBuilder();
		builder.append("[Task: ").append(name);
		builder.append(", PT: ").append(getCallerThread()).append("]");
		return builder.toString();
	}

	private static void setContext(Map<String, String> storedContext) {
		if (storedContext == null) {
			MDC.clear();
		} else {
			MDC.setContextMap(storedContext);
		}
	}

}
