package com.birdeye.campaign.qr.service;

import java.util.List;

import com.birdeye.campaign.request.external.QrReviewMappingRequest;
import com.birdeye.campaign.request.external.QrReviewSourcesResponse;
import com.birdeye.campaign.response.external.QRMostClickedSourceResponse;
import com.birdeye.campaign.response.template.v2.EditTemplateResponse;
import com.birdeye.campaign.response.template.v2.EmailTemplateResponse;
import com.birdeye.campaign.sro.ReviewSourceSRO;

public interface QRService {
	
	
	/**
	 * create new qr review source config mapping
	 * fetch default email template data
	 * create new email template & business email template for given business in db
	 * create mapping between qr config & review sources in db
	 */
	List<ReviewSourceSRO> createQRConfigReviewSourceMapping(Integer qrConfigId, QrReviewMappingRequest qrReviewMappingRequest);
	
	/**
	 * update qr review source config mapping
	 * fetch current mappings for given qr config id from db
	 * update review source mappings whose priorities are changed in incoming request
	 * delete review source mappings which aren't present in incoming request
	 * create review source mappings which aren't present in db but are present in incoming request
	 */
	List<ReviewSourceSRO> updateQRConfigReviewSourceMapping(Integer qrConfigId, QrReviewMappingRequest qrReviewMappingRequest);
	
	/**
	 * get qr review source config mapping
	 * if qr config id is 0 then return default deeplink sources
	 * else return review sources from table qr_config_review_source_mapping
	 */
	QrReviewSourcesResponse getQRReviewSources(Integer qrConfigId, Integer accountId);
	
	/**
	 * delete review source mappings for the given qr config id from db
	 * @param accountId 
	 */
	Boolean deleteQRConfigReviewSourceMapping(Integer qrConfigId, Integer accountId);

	/**
	 * Create default QR Config mapping for a business
	 * Consume event from core with qr config id and account id
	 * fetch the most used clicked sources for an account
	 * create default QR template in email template and business email template
	 * create entry in qr code mapping with template id and source id
	 * 
	 */
	QRMostClickedSourceResponse createDefaultQRConfigTemplate(Integer qrConfigId, Integer accountId, Integer defaultSources);

	/**
	 * 
	 * @param qrConfigId
	 * @param accountId
	 * @param request
	 * @return
	 * update qr template 
	 */

	EditTemplateResponse createOrUpdateBusinessEmailTemplate(Integer qrConfigId, Integer accountId, EmailTemplateResponse request);
	/**
	 * 
	 * @param qrConfigId
	 * @param accountId
	 * @return
	 * 
	 * get qr template details
	 */
	EmailTemplateResponse getQRTemplateDetails(Integer qrConfigId, Integer accountId);
	
}
