package com.birdeye.campaign.ongoing.ruleengine.rule;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.PostConstruct;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.constant.KafkaTopicTypeEnum;
import com.birdeye.campaign.dto.BusinessHierarchicalData;
import com.birdeye.campaign.dto.SplitCampaignMappingDTO;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.CampaignCondition;
import com.birdeye.campaign.enums.OngoingCampaignStatusEnum;
import com.birdeye.campaign.kafka.service.KafkaService;
import com.birdeye.campaign.ongoing.ruleengine.OngoingCampaignComponent;
import com.birdeye.campaign.ongoing.ruleengine.eventdata.OngoingCampaignEvent;
import com.birdeye.campaign.ongoing.ruleengine.exception.OngoingCampaignException;
import com.birdeye.campaign.platform.constant.CampaignStatusEnum;
import com.birdeye.campaign.platform.constant.CampaignTriggerTypeEnum;
import com.birdeye.campaign.platform.repository.BusinessLevelValueMappingRepo;
import com.birdeye.campaign.request.CampaignUpdateStatusRequest;
import com.birdeye.campaign.request.OngoingCampaignEventRequest;
import com.birdeye.campaign.request.ProductFeatureRequest;
import com.birdeye.campaign.request.Tag;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.CampaignSetupCachingService;
import com.birdeye.campaign.service.SplitCampaignHelperService;
import com.birdeye.campaign.service.dao.SplitCampaignDao;
import com.birdeye.campaign.utils.AppointmentRecallUtils;
import com.birdeye.campaign.utils.CampaignUtils;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.MvelEvaluationUtils;
import com.birdeye.campaign.utils.SplitCampaignUtils;

@Service("ongoingCampaignEvalRule")
public class OngoingCampaignEvalRule extends OngoingCampaignRule {

	@Autowired
	private ApplicationContext applicationContext;

	@Autowired
	private CampaignSetupCachingService campaignSetupCachingService;

	@Autowired
	private BusinessLevelValueMappingRepo businessLevelValueMappingRepo;
	
	@Autowired
	private SplitCampaignDao				splitCampaignDao;
	
	@Autowired
	private SplitCampaignHelperService		splitCampaignHelperService;
	
	@Autowired
	private CacheService					cacheService;
	
	@Autowired
	private KafkaService                    kafkaService;

	private static final Logger logger = LoggerFactory.getLogger(OngoingCampaignEvalRule.class);

	@PostConstruct
	public void init() {
		setNegativeOutcomeStep((OngoingCampaignComponent) applicationContext.getBean("eventRejectionAction"));
		setPositiveOutcomeStep((OngoingCampaignComponent) applicationContext.getBean("eventApprovedAction"));
	}

	@Override
	protected boolean evaluateDecision(OngoingCampaignEvent eventData) throws OngoingCampaignException {
		Integer enterpriseId = eventData.getEventRequest().getEnterpriseId();
		// check if campaignId present then give priority to that.
		if (eventData.getEventRequest().getCampaignId() != null) {
			return processEventForCampaignId(eventData);
		} else {
			// else go with fetching all the campaign for enterprise Id
			return processEventForwithOutCampaignId(eventData, enterpriseId);
		}
	}
	
	/**
	 *
	 * This function does the following :
	 * 1. If trigger type is null, before_appointment_date or appointment_recall, don't fetch split automation
	 * 2. Fetch SplitCampaignMapping for status and accountId,if empty, return empty map.
	 * 3. Else, Select sub campaignId to be used to rule match and send RR.
	 * 
	 * @param accountId,
	 *            status
	 * 
	 */
	private Map<Integer, Integer> fetchValidSplitCampaignIds(Integer accountId, List<Integer> status, String triggerType) {
//		logger.info("fetchValidSplitCampaignIds :: Request receievd to fetch valid split campaign ids for account id {} and status list {}", accountId, status);
		try {
			CampaignTriggerTypeEnum campaignTriggerTypeEnum = CampaignTriggerTypeEnum.getCampaignTriggerTypeEnum(triggerType);	
			if (campaignTriggerTypeEnum == null
					|| StringUtils.equalsAnyIgnoreCase(triggerType, CampaignTriggerTypeEnum.BEFORE_APPOINTMENT_DATE.getType(), CampaignTriggerTypeEnum.APPOINTMENT_RECALL.getType())) {
				logger.info("fetchValidSplitCampaignIds :: Invalid or Non Applicable trigger type to fetch valid split campaign ids for account id {} and status list {}", accountId, status);
				return new HashMap<>();
			}
			List<SplitCampaignMappingDTO> splitMappingList = splitCampaignDao.getMappingDataByAccountIdAndStatus(accountId, status);
			if (CollectionUtils.isEmpty(splitMappingList)) {
				return new HashMap<>();
			}
			logger.info("fetchValidSplitCampaignIds :: mapping list size fetched for account id {} and status list {} is {}", accountId, status, CollectionUtils.size(splitMappingList));
			return SplitCampaignUtils.getValidCampaignIdToSplitCampaignIdFromList(splitMappingList, accountId);
		} catch (Exception e) {
			logger.error("fetchValidSplitCampaignIds :: Exception occurred while fetching valid split campaign ids for account id {} and status list {} is {}", accountId, status,
					ExceptionUtils.getStackTrace(e));
		}
		return new HashMap<>();
	}
	
	/**
	 * 
	 * Prepare and dispatch campaign pause event
	 * 
	 * @param enterpriseId,
	 *            campaigns, filteredCampaigns
	 * 
	 * 
	 */
	private void prepareAndDispatchCampaignPauseEvent(Integer accountId, List<Campaign> campaigns, List<Campaign> filteredCampaigns) {
		try {
			if (CollectionUtils.size(campaigns) == CollectionUtils.size(filteredCampaigns)) {
				return;
			}
			logger.info("prepareAndDispatchCampaignPauseEvent :: Request received to send event for pausing campaigns for account id {}", accountId);
			List<Integer> filteredOutCampaignIdsList = CampaignUtils.fetchFilteredCampaignIdList(accountId, campaigns, filteredCampaigns);
			if (CollectionUtils.isNotEmpty(filteredOutCampaignIdsList)) {
				filteredOutCampaignIdsList.stream().forEach(e -> {
					logger.info("prepareAndDispatchCampaignPauseEvent :: Pushing campaign id {} for pause event", e);
					Boolean isEventDispatched = kafkaService.pushMessageToKafkaAcknowledged(KafkaTopicTypeEnum.CAMPAIGN_STATUS_UPDATE.getType(), String.valueOf(e),
							new CampaignUpdateStatusRequest(e, CampaignStatusEnum.PAUSED.getStatus()));
					logger.info("prepareAndDispatchCampaignPauseEvent :: For campaign id {}, pause event dispatched is {}", e, isEventDispatched);
				});
			} else {
				logger.error("prepareAndDispatchCampaignPauseEvent :: Error campaigns list is empty after filtering for account id {}", accountId);
			}
		} catch (Exception e) {
			logger.error("prepareAndDispatchCampaignPauseEvent :: Exception occurred while sending pause campaign events based upon feature flag for account id {} :: {}", accountId,
					ExceptionUtils.getStackTrace(e));
		}
	}
	
	/**
	 * 
	 * Fetch Product feature request and Filter Campaigns based upon feature flag enabled/disabled
	 * 
	 * @param enterpriseId,
	 *            campaigns
	 * 
	 * @return List<Campaign>
	 * 
	 */
	private List<Campaign> filterCampaignBasedUponFeatureFlag(Integer enterpriseId, List<Campaign> campaigns) {
		try {
			if (CollectionUtils.isEmpty(campaigns)) {
				logger.info("filterCampaignBasedUponFeatureFlag :: Empty campaign list provided for account id {}", enterpriseId);
				return campaigns;
			}
			ProductFeatureRequest featureRequest = cacheService.getProductFeatureForBusiness(enterpriseId);
			List<Campaign> filteredCampaigns = CampaignUtils.filterCampaignListBasedUponFeatureFlag(featureRequest, campaigns);
			prepareAndDispatchCampaignPauseEvent(enterpriseId, campaigns, filteredCampaigns);
			return filteredCampaigns;
		} catch (Exception e) {
			logger.error("filterCampaignBasedUponFeatureFlag :: Exception occurred while filtering campaign based upon feature flag for enterprise id {} :: {}", enterpriseId,
					ExceptionUtils.getStackTrace(e));
			return campaigns;
		}
	}
	
	/**
	 * 
	 * Fetch Product feature request and Filter Single Campaign based upon feature flag enabled/disabled
	 * 
	 * @param accountId,
	 *            campaign
	 * 
	 * @return Campaign
	 * 
	 */
	private Campaign filterSingleCampaignBasedUponFeatureFlag(Integer accountId, Campaign campaign) {
		if (campaign == null || BooleanUtils.isTrue(CoreUtils.getBooleanValueFromInteger(campaign.getIsSplitCampaign()))) {
			logger.warn("filterSingleCampaignBasedUponFeatureFlag :: Either Campaign provided is null or campaign belongs to a split campaign for account id {}", accountId);
			return campaign;
		}
		List<Campaign> filteredCampaign = filterCampaignBasedUponFeatureFlag(accountId, new ArrayList<>(Collections.singletonList(campaign)));
		if (CollectionUtils.isEmpty(filteredCampaign)) {
			logger.warn("filterSingleCampaignBasedUponFeatureFlag :: Campaign with id {} and account id {} filtered after feature flag check", campaign.getId(), accountId);
			return null;
		}
		return campaign;
	}

	private boolean processEventForwithOutCampaignId(OngoingCampaignEvent eventData, Integer enterpriseId) {
		//logger.info("For eid {} campaign Id {} is not present in the event request so proceding without campaign Id only", enterpriseId, eventData.getEventRequest().getCampaignId());
		// 1. get the campaign for an enterprise
		// 2. check the condition for given campaign
		// 3. evaluate the condition and populate the campaignIdWithKafkaStatus with value as false.
		// 4. if any of the campaign exists the proceeds further else go for rejection
		// go for fetching campaign for EID
		List<Integer> applicableCampaignStatus = getApplicableCampaignStatus(eventData.getEventRequest());
		List<Campaign> campaigns = campaignSetupCachingService
				.getOngoingCampaignByEnterprise(enterpriseId, applicableCampaignStatus, prepareCacheKey(enterpriseId, applicableCampaignStatus)).getElementsList();

		// Filter campaign based upon feature flag enabled/disabled and send event to pause the concerned campaigns
		campaigns = filterCampaignBasedUponFeatureFlag(enterpriseId, campaigns);
		
		// Split Automation Handling
		Map<Integer, Integer> campaignIdToSplitCampaignIdMap = fetchValidSplitCampaignIds(enterpriseId, applicableCampaignStatus,
				eventData.getEventRequest().getTriggerType());
		List<Integer> splitSubCampaignIdList = SplitCampaignUtils.getListOfKeysFromMap(campaignIdToSplitCampaignIdMap);
		
		if (CollectionUtils.isEmpty(campaigns) && CollectionUtils.isEmpty(splitSubCampaignIdList)) {
			// handle rejection action
			eventData.setEventStatus(OngoingCampaignStatusEnum.NO_CAMPAIGN_FOUND.getStatus());
			return false;
		} else {
			Map<Integer, Boolean> campaignIdWithKafkaStatus = new HashMap<>();
			// get list of all campaign Ids
			List<Integer> campaignIds = SplitCampaignUtils.mergeNormalAndSplitAutomationCampaignIds(campaigns, splitSubCampaignIdList);
			// fetching the Campaign Condition for campaign Ids
			//campaignIds.add(17538)
			List<CampaignCondition> campaignConditions = campaignSetupCachingService.getCampaignConditionsByCampaign(campaignIds, enterpriseId);
			if (CollectionUtils.isNotEmpty(campaignConditions)) {
				Map<Integer, List<String>> businessHierarchicalData = getHierarchyLevelDataForBusiness(eventData.getEventRequest().getBusinessId());
				for (CampaignCondition campaignCondition : campaignConditions) {
					logger.info("Matching rule for campaign condition : {}", campaignCondition);
					if (campaignCondition != null && isRuleMatchForGivenEvent(campaignCondition, eventData.getEventRequest(), businessHierarchicalData)) {
						campaignIdWithKafkaStatus.put(campaignCondition.getCampaignId(), false);
					}
				}
			}
			// if no rule match then failure
			if (MapUtils.isEmpty(campaignIdWithKafkaStatus)) {
				eventData.setEventStatus(OngoingCampaignStatusEnum.NO_MATCH_CONDITION_FOUND.getStatus());
				return false;
			}
			eventData.setCampaignIdWithKafkaStatus(campaignIdWithKafkaStatus);
		}
		return true;
	}

	private String prepareCacheKey(Integer enterpriseId, List<Integer> applicableCampaignStatus) {
		StringBuilder cacheKey = new StringBuilder();
		cacheKey.append(enterpriseId);
		for (Integer status : applicableCampaignStatus) {
			cacheKey.append("-").append(status);
		}
		return cacheKey.toString();
	}

	/**
	 * @param eventRequest
	 * @return status [1,2] for appointment reminder and recall otherwise return [1]
	 * BIRDEYE-115746
	 * 
	 * NOTE - Automations list query is cached based on campagin status. If anyone adds any other status to this method, 
	 * Please handle the automation list cache eviction for the new status. - CampaignSetupCachingService.evictOngoingCampaignByEnterprise  
	 */
	private List<Integer> getApplicableCampaignStatus(OngoingCampaignEventRequest eventRequest) {
		return (eventRequest.getAppointmentId() != null || eventRequest.getAppointmentRecallId() != null) ? Arrays.asList(1, 2) : Arrays.asList(1);
	}
	
	/**
	 *
	 * This function does the following :
	 * 1. Validate if campaign id given is a part of split automation and has trigger type as contact_event
	 * 2. If yes fetch split campaign mapping details and select campaign id to be executed.
	 * 3. Else just return the given campaign 's id.
	 * 
	 * @param campaign
	 * 
	 */
	private Integer handleReccurringReminderEventSplitAutomation(Campaign campaign) {
		if (BooleanUtils.isTrue(CoreUtils.getBooleanValueFromInteger(campaign.getIsSplitCampaign()))
				&& StringUtils.equalsIgnoreCase(campaign.getTriggerType(), CampaignTriggerTypeEnum.CONTACT_EVENT.getType())) {
			logger.info("handleReccurringReminderEventSplitAutomation :: Fetching mapping data and selecting campaign id to be executed based upon split perecent for given sub campaign id {}",
					campaign.getId());
			Map<Integer, Integer> campaignIdToSplitCampaignIdMap = SplitCampaignUtils.getValidCampaignIdToSplitCampaignIdFromList(
					splitCampaignHelperService.validateAndFetchSplitCampaignIdAndMappings(campaign.getId(), campaign.getEnterpriseId()), campaign.getEnterpriseId());
			if (campaignIdToSplitCampaignIdMap == null || CollectionUtils.sizeIsEmpty(campaignIdToSplitCampaignIdMap)) {
				logger.error("handleReccurringReminderEventSplitAutomation :: Error occurred while fetching campaign id based upon split percentage for sub campaign id {}", campaign.getId());
				return null;
			}
			return SplitCampaignUtils.getListOfKeysFromMap(campaignIdToSplitCampaignIdMap).get(0);
		}
		
		return campaign.getId();
	}
	
	private boolean processEventForCampaignId(OngoingCampaignEvent eventData) {
		logger.info("For eid {} campaign Id {} is present in the event request so proceding with campaign Id only", eventData.getEventRequest().getEnterpriseId(),
				eventData.getEventRequest().getCampaignId());
		Campaign campaign = campaignSetupCachingService.getOngoingCampaignByEnterpriseAndCampaignId(eventData.getEventRequest().getEnterpriseId(), eventData.getEventRequest().getCampaignId());
		// Filter campaign based upon feature flag enabled/disabled and send event to pause and delete the concerned campaign
		campaign = filterSingleCampaignBasedUponFeatureFlag(eventData.getEventRequest().getEnterpriseId(), campaign);
		if (campaign == null) {
			// handle rejection action
			eventData.setEventStatus(OngoingCampaignStatusEnum.NO_CAMPAIGN_FOUND.getStatus());
			return false;
		} else {
			Map<Integer, Boolean> campaignIdWithKafkaStatus = new HashMap<>();
			
			Integer campaignId = handleReccurringReminderEventSplitAutomation(campaign);
			if (campaignId == null) {
				eventData.setEventStatus(OngoingCampaignStatusEnum.NO_CAMPAIGN_FOUND.getStatus());
				return false;
			}
			CampaignCondition campaignCondition = campaignSetupCachingService.getCampaignConditionByCampaign(campaignId, eventData.getEventRequest().getEnterpriseId());
			if (campaignCondition != null) {
				Map<Integer, List<String>> businessHierarchicalData = getHierarchyLevelDataForBusiness(eventData.getEventRequest().getBusinessId());
				logger.info("Matching rule for campaign condition : {}", campaignCondition);
				if (isRuleMatchForGivenEvent(campaignCondition, eventData.getEventRequest(), businessHierarchicalData)) {
					campaignIdWithKafkaStatus.put(campaignId, false);
				}
			}
			if (MapUtils.isEmpty(campaignIdWithKafkaStatus)) {
				eventData.setEventStatus(OngoingCampaignStatusEnum.NO_MATCH_CONDITION_FOUND.getStatus());
				return false;
			}
			eventData.setCampaignIdWithKafkaStatus(campaignIdWithKafkaStatus);
			
			return true;
		}
	}

	private boolean isRuleMatchForGivenEvent(CampaignCondition campaignCondition, OngoingCampaignEventRequest eventRequest, Map<Integer, List<String>> businessHierarchicalData) {
		// 1. location or hierarchy match is done. then go for tag matching
		if (checkForLocationMatchForEvent(campaignCondition, eventRequest) || checkForHierarchyLevelMatchForEvent(campaignCondition, eventRequest, businessHierarchicalData)) {
			// handling for old and new flow
			if (CacheManager.getInstance().getCache(SystemPropertiesCache.class).getBooleanProperty("automation_campaign_new_flow", false)) {
				return evaluateAutomationTriggerEvent(campaignCondition,eventRequest);
			} else {
				return evaluateTags(campaignCondition, eventRequest) && evaluateContactSources(campaignCondition, eventRequest);
			}
		}
		return false;
	}
	
	private boolean evaluateAutomationTriggerEvent(CampaignCondition campaignCondition, OngoingCampaignEventRequest eventRequest) {
		CampaignTriggerTypeEnum campaignTriggerTypeEnum = CampaignTriggerTypeEnum.getCampaignTriggerTypeEnum(eventRequest.getTriggerType());
		
		if (campaignTriggerTypeEnum == null || !eventRequest.getTriggerType().equalsIgnoreCase(campaignCondition.getEvent()))
			return false;
		
		boolean pmsRecallRequest = AppointmentRecallUtils.isPMSRecallRequest(eventRequest.getAppointmentRecallId());
		
		if (StringUtils.equalsAnyIgnoreCase(campaignCondition.getEvent(), CampaignTriggerTypeEnum.CONTACT_ADDED.getType(), "Contact is added to BirdEye")) {
			// now contact source will not be part of conditions
			return MvelEvaluationUtils.evaluateMvelExpression(campaignCondition.getMvelExpression(), campaignCondition.getMvelParamsAndTypes(), eventRequest.getCustomFields(), eventRequest.getTags(),
					eventRequest.getReviewSourceIds()) && evaluateContactSources(campaignCondition, eventRequest);
		} else {
			return MvelEvaluationUtils.evaluateMvelExpression(campaignCondition.getMvelExpression(), campaignCondition.getMvelParamsAndTypes(), eventRequest.getCustomFields(), eventRequest.getTags(),
					eventRequest.getReviewSourceIds())
					&& MvelEvaluationUtils.evaluateTriggerMvelExpression(campaignCondition.getTriggerMvelExpression(), campaignCondition.getTriggerMvelParamsAndTypes(),
							eventRequest.getTriggerFilters(), pmsRecallRequest, campaignCondition.getTriggerRuleExpression());
		}
	}

	

	private boolean evaluateTags(CampaignCondition campaignCondition, OngoingCampaignEventRequest eventRequest) {
		if (campaignCondition.getNoTagFilter() != null && campaignCondition.getNoTagFilter() == 1) {
			return CollectionUtils.isEmpty(eventRequest.getTags());
		}
		if (campaignCondition.getAnyTagFilter() != null && campaignCondition.getAnyTagFilter() == 1) {
			return CollectionUtils.isNotEmpty(eventRequest.getTags());
		}
		if ((CollectionUtils.isNotEmpty(campaignCondition.getTags()) || CollectionUtils.isNotEmpty(campaignCondition.getExclusionTags())) && CollectionUtils.isNotEmpty(eventRequest.getTags())) {
			List<Tag> inclusionTags = convertJsonToTagList(campaignCondition.getTags());
			List<Tag> exclusionTags = convertJsonToTagList(campaignCondition.getExclusionTags());
			Collection<Tag> inclusionIntersectionTags = getIntersection(inclusionTags, eventRequest.getTags());
			Collection<Tag> exclusionIntersectionTags = getIntersection(exclusionTags, eventRequest.getTags());
			if (CollectionUtils.isEmpty(campaignCondition.getTags()))
				return CollectionUtils.isEmpty(exclusionIntersectionTags);
			else if (CollectionUtils.isEmpty(campaignCondition.getExclusionTags()))
				return CollectionUtils.isNotEmpty(inclusionIntersectionTags);
			else
				return (CollectionUtils.isNotEmpty(inclusionIntersectionTags) && CollectionUtils.isEmpty(exclusionIntersectionTags));
		} else {
			// this means the location or hierarchy is valid for given campaign
			// only return true for empty tag campaign
			return CollectionUtils.isEmpty(campaignCondition.getTags());
		}
	}

	private boolean evaluateContactSources(CampaignCondition campaignCondition, OngoingCampaignEventRequest eventRequest) {
		if (CollectionUtils.isNotEmpty(campaignCondition.getContactSources()) && StringUtils.isNotEmpty(eventRequest.getSource())) {
			Collection<String> intersectionSources = getIntersection(campaignCondition.getContactSources(), Collections.singletonList(eventRequest.getSource()));
			return CollectionUtils.isNotEmpty(intersectionSources);

		} else if (CollectionUtils.isEmpty(campaignCondition.getContactSources()) && StringUtils.isNotEmpty(eventRequest.getSource())) {
			List<String> defaultContactSources = new ArrayList<>();
			defaultContactSources.add("dashboard");
			defaultContactSources.add("sftp");
			defaultContactSources.add("integration");
			defaultContactSources.add("api");
			Collection<String> intersectionSources = getIntersection(defaultContactSources, Collections.singletonList(eventRequest.getSource()));
			return CollectionUtils.isNotEmpty(intersectionSources);
		} else {
			return CollectionUtils.isEmpty(campaignCondition.getContactSources());
		}
	}

	@SuppressWarnings("rawtypes")
	private List<Tag> convertJsonToTagList(List<Tag> inputTags) {
		if (CollectionUtils.isEmpty(inputTags)) {
			return Collections.emptyList();
		}
		List<Tag> tags = new ArrayList<>();
		for (int i = 0; i < inputTags.size(); i++) {
			Map entry = (Map) inputTags.get(i);
			Tag tag = new Tag((int) entry.get("id"), entry.get("name").toString(), entry.get("value").toString());
			tags.add(tag);
		}
		return tags;
	}

	public static <T> Collection<T> getIntersection(Collection<T> coll1, Collection<T> coll2) {
		return Stream.concat(coll1.stream(), coll2.stream()).filter(coll1::contains).filter(coll2::contains).collect(Collectors.toSet());
	}

	/**
	 * Checking for Hierarchy level data
	 * 
	 * @param campaignCondition
	 * @param eventRequest
	 * @param businessHierarchicalData2 
	 * @return
	 */
	private boolean checkForHierarchyLevelMatchForEvent(CampaignCondition campaignCondition, OngoingCampaignEventRequest eventRequest,
			Map<Integer, List<String>> businessHierarchicalData) {
		logger.info("For business {} the Hierarchy Level data is {} and campaign condition levelId {} and ids is {}", eventRequest.getBusinessId(), businessHierarchicalData,
				campaignCondition.getLvlAliasId(), campaignCondition.getLvlIds());
		// 2. if both condition have level data as well as the business have level data
		if (campaignCondition.getLvlAliasId() != null && CollectionUtils.isNotEmpty(campaignCondition.getLvlIds()) && MapUtils.isNotEmpty(businessHierarchicalData)
				&& businessHierarchicalData.get(Integer.valueOf(campaignCondition.getLvlAliasId())) != null) {
			List<String> conditionIds = campaignCondition.getLvlIds();
			List<String> businessLevelIds = businessHierarchicalData.get(Integer.valueOf(campaignCondition.getLvlAliasId()));
			Collection<String> intersectionIds = getIntersection(conditionIds, businessLevelIds);
			return CollectionUtils.isNotEmpty(intersectionIds);
		}
		// else everything is false
		return false;
	}

	private Map<Integer, List<String>> getHierarchyLevelDataForBusiness(Integer businessId) {
		List<BusinessHierarchicalData> hierarchicalData = businessLevelValueMappingRepo.getBusinessHierarchicalData(businessId);
		if (CollectionUtils.isEmpty(hierarchicalData)) {
			return Collections.emptyMap();
		}
		return hierarchicalData.stream()
				.collect(Collectors.groupingBy(BusinessHierarchicalData::getLvlAliasId, Collectors.mapping(BusinessHierarchicalData::getLvlId, Collectors.toList())));
	}
	

	/**
	 * Checking for location matching
	 * 
	 * @param campaignCondition
	 * @param eventRequest
	 * @return
	 */
	private boolean checkForLocationMatchForEvent(CampaignCondition campaignCondition, OngoingCampaignEventRequest eventRequest) {
		// 1 if for all location
		// 2. for selected location
		return ("LOC".equalsIgnoreCase(campaignCondition.getLvlAlias()) && (CollectionUtils.isEmpty(campaignCondition.getLvlIds())
				|| (CollectionUtils.isNotEmpty(campaignCondition.getLvlIds()) && campaignCondition.getLvlIds().contains(eventRequest.getBusinessId().toString()))));
	}
	
}
