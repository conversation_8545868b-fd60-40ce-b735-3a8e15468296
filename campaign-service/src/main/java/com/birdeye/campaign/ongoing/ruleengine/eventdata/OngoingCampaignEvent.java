package com.birdeye.campaign.ongoing.ruleengine.eventdata;

import java.io.Serializable;
import java.util.Map;

import com.birdeye.campaign.request.OngoingCampaignEventRequest;
/**
 * 
 * <AUTHOR>
 *
 */
public class OngoingCampaignEvent implements Serializable{
	
	private static final long serialVersionUID = 8525841247602309125L;

	private OngoingCampaignEventRequest eventRequest;
	
	private Long eventId;
	
	private String eventStatus;
	
	private Map<Integer,Boolean> campaignIdWithKafkaStatus;

	/**
	 * @return the eventRequest
	 */
	public OngoingCampaignEventRequest getEventRequest() {
		return eventRequest;
	}

	/**
	 * @param eventRequest the eventRequest to set
	 */
	public void setEventRequest(OngoingCampaignEventRequest eventRequest) {
		this.eventRequest = eventRequest;
	}

	/**
	 * @return the eventId
	 */
	public Long getEventId() {
		return eventId;
	}

	/**
	 * @param eventId the eventId to set
	 */
	public void setEventId(Long eventId) {
		this.eventId = eventId;
	}

	/**
	 * @return the eventStatus
	 */
	public String getEventStatus() {
		return eventStatus;
	}

	/**
	 * @param eventStatus the eventStatus to set
	 */
	public void setEventStatus(String eventStatus) {
		this.eventStatus = eventStatus;
	}

	/**
	 * @return the campaignIdWithKafkaStatus
	 */
	public Map<Integer, Boolean> getCampaignIdWithKafkaStatus() {
		return campaignIdWithKafkaStatus;
	}

	/**
	 * @param campaignIdWithKafkaStatus the campaignIdWithKafkaStatus to set
	 */
	public void setCampaignIdWithKafkaStatus(Map<Integer, Boolean> campaignIdWithKafkaStatus) {
		this.campaignIdWithKafkaStatus = campaignIdWithKafkaStatus;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("OngoingCampaignEvent [eventRequest=");
		builder.append(eventRequest);
		builder.append(", eventId=");
		builder.append(eventId);
		builder.append(", eventStatus=");
		builder.append(eventStatus);
		builder.append(", campaignIdWithKafkaStatus=");
		builder.append(campaignIdWithKafkaStatus);
		builder.append("]");
		return builder.toString();
	}

}
