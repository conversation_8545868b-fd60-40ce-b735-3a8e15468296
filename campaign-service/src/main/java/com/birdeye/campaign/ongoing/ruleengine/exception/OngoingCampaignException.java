package com.birdeye.campaign.ongoing.ruleengine.exception;

import com.birdeye.campaign.constant.ErrorCodes;
/**
 * 
 * <AUTHOR>
 *
 */
public class OngoingCampaignException extends Exception {
	
	private static final long	serialVersionUID	= 554840459459431473L;
	private final ErrorCodes			code;
	

	public ErrorCodes getCode() {
		return code;
	}

	public OngoingCampaignException(ErrorCodes code, String message) {
		super(message);
		this.code = code;
	}
	
	public OngoingCampaignException(ErrorCodes code) 
	{
		this.code = code;
	}

}
