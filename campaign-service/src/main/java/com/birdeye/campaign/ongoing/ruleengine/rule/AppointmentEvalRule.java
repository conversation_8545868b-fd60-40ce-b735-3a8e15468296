package com.birdeye.campaign.ongoing.ruleengine.rule;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.appointment.service.AppointmentService;
import com.birdeye.campaign.business.service.BusinessService;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.TriggerFilter;
import com.birdeye.campaign.ongoing.ruleengine.OngoingCampaignComponent;
import com.birdeye.campaign.ongoing.ruleengine.eventdata.OngoingCampaignEvent;
import com.birdeye.campaign.ongoing.ruleengine.exception.OngoingCampaignException;
import com.birdeye.campaign.platform.constant.CampaignTriggerTypeEnum;
import com.birdeye.campaign.request.OngoingCampaignEventRequest;
import com.birdeye.campaign.response.external.AppointmentDetailsResponse;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.utils.AppointmentRecallUtils;
import com.birdeye.campaign.utils.DateTimeUtils;

/**
 * 
 * <AUTHOR>
 *
 */
@Service("appointmentEvalRule")
public class AppointmentEvalRule extends OngoingCampaignRule {
	
	@Autowired
	private ApplicationContext	applicationContext;
	
	@Autowired
	private BusinessService		businessService;
	
	@Autowired
	private AppointmentService	appointmentService;
	
	@Autowired
	private CacheService		cacheService;
	
	@PostConstruct
	public void init() {
		setNegativeOutcomeStep((OngoingCampaignComponent) applicationContext.getBean("eventRejectionAction"));
		setPositiveOutcomeStep((OngoingCampaignComponent) applicationContext.getBean("ongoingCampaignEvalRule"));
	}
	
	@Override
	protected boolean evaluateDecision(OngoingCampaignEvent eventData) throws OngoingCampaignException {
		switch (CampaignTriggerTypeEnum.getCampaignTriggerTypeEnum(eventData.getEventRequest().getTriggerType())) {
			
			case BEFORE_APPOINTMENT_DATE:
			case APPOINTMENT_RECALL:
				// Change Appointment time from epoch to 24 hour integer for rule matching
				changeAppointmentTimeInOngoingCampaignEventRequest(eventData.getEventRequest(), eventData.getEventRequest().getBusinessId());
				
			case APPOINTMENT_CANCELED:
			case APPOINTMENT_MISSED:
			case APPOINTMENT_COMPLETED:
				addDayOfAppointmentInOngoingCampaignEventRequest(eventData.getEventRequest());
				break;
			default:
				break;
		}
		
		return true;
	}
	
	private void changeAppointmentTimeInOngoingCampaignEventRequest(OngoingCampaignEventRequest ongoingCampaignEventRequest, Integer businessId) {
		String timeZoneId = businessService.getBusinessTimezoneId(businessId);
		List<TriggerFilter> triggerFilters = ongoingCampaignEventRequest.getTriggerFilters();
		for(TriggerFilter triggerFilter : triggerFilters) {
			if (triggerFilter.getName().equalsIgnoreCase("Appointment time") && triggerFilter.getValue() != null) {
				String value = triggerFilter.getValue().toString();
				value = value.substring(1, value.length() - 1);
				triggerFilter.setValue(epochTo24HourTime(Long.parseLong(value), timeZoneId));
			}
		}
		ongoingCampaignEventRequest.setTriggerFilters(triggerFilters);
	}
	
	private static Object epochTo24HourTime(long epochTime, String timeZone) {
		LocalDateTime dateTime = Instant.ofEpochMilli(epochTime).atZone(ZoneId.of(timeZone)).toLocalDateTime();
		String formattedTime = dateTime.format(DateTimeFormatter.ofPattern("HHmm"));
		int intTime = Integer.parseInt(formattedTime);
		ArrayList<Integer> list = new ArrayList<>();
		list.add(intTime);
		return list;
	}
	
	/**
	 * Adds the day of the week as a trigger filter value based on the "Day of the week" filter type
	 * in the ongoing campaign event request.
	 *
	 * @param ongoingCampaignEventRequest
	 *            The ongoing campaign event request containing trigger filters.
	 */
	private void addDayOfAppointmentInOngoingCampaignEventRequest(OngoingCampaignEventRequest ongoingCampaignEventRequest) {
		// Get the list of trigger filters from the ongoing campaign event request.
		List<TriggerFilter> triggerFilters = ongoingCampaignEventRequest.getTriggerFilters();
		// Iterate through the trigger filters to find the "Day of the week" filter type.
		for (TriggerFilter triggerFilter : triggerFilters) {
			if (triggerFilter.getName().equalsIgnoreCase("Day of the week")) {
				// Retrieve appointment details using the provided appointment ID.
				AppointmentDetailsResponse appointmentDetails = appointmentService.getAppointmentById(ongoingCampaignEventRequest.getAppointmentId().toString(),
						ongoingCampaignEventRequest.getEnterpriseId(), false, false);
				BusinessEnterpriseEntity business = cacheService.getBusinessById(appointmentDetails.getBusinessId());
				ZoneId zoneId = DateTimeUtils.getZoneIdFromTimeZoneId(business.getTimezoneId());
				
				// If appointment details are available, proceed to extract the day of the week.
				if (appointmentDetails != null) {
					triggerFilter.setValue(Collections.singletonList(AppointmentRecallUtils.getDayOfTheWeekByMillisecond(appointmentDetails.getStartTime(), zoneId)));
				}
				
			}
		}
		ongoingCampaignEventRequest.setTriggerFilters(triggerFilters);
	}

}
