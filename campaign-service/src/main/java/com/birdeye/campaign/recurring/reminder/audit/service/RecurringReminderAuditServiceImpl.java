package com.birdeye.campaign.recurring.reminder.audit.service;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.entity.RecurringReminderEventsAudit;
import com.birdeye.campaign.repository.RecurringReminderEventsAuditRepo;

@Service("recurringReminderAuditService")
public class RecurringReminderAuditServiceImpl implements RecurringReminderAuditService {
	
	@Autowired
	private RecurringReminderEventsAuditRepo recurringReminderEventsAuditRepo;
	
	@Override
	public void createRecurringReminderAudit(Integer reminderEventId, Integer campaignId) {
		RecurringReminderEventsAudit reminderAudit = new RecurringReminderEventsAudit();
		reminderAudit.setReminderEventsId(reminderEventId);
		reminderAudit.setStatus("init");
		reminderAudit.setCampaignId(campaignId);
		recurringReminderEventsAuditRepo.save(reminderAudit);
	}
	
	@Override
	public void saveRecurringReminderEventsAudit(RecurringReminderEventsAudit reminderAudit) {
		recurringReminderEventsAuditRepo.save(reminderAudit);
	}
	
	@Override
	public List<RecurringReminderEventsAudit> findByReminderEventId(Integer recurringReminderEventId) {
		return recurringReminderEventsAuditRepo.findByReminderEventsId(recurringReminderEventId);
	}
	
	
	@Override
	public void updateRecurringReminderEventAudit(List<Integer> recurringReminderEventId, String status, String failureReason) {
		List<RecurringReminderEventsAudit> reminderAuditEvents = recurringReminderEventsAuditRepo.findByReminderEventsIdIn(recurringReminderEventId);
		
		recurringReminderEventsAuditRepo.saveAll(reminderAuditEvents.stream().map(event -> updateAuditEvent(event, status, failureReason)).collect(Collectors.toList()));
	}
	
	private RecurringReminderEventsAudit updateAuditEvent(RecurringReminderEventsAudit event, String status, String failureReason) {
		event.setStatus(status);
		event.setFailureReason(failureReason);
		return event;
	}
}
