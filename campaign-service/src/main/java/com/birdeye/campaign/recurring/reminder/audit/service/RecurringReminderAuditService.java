package com.birdeye.campaign.recurring.reminder.audit.service;

import java.util.List;

import com.birdeye.campaign.entity.RecurringReminderEventsAudit;

public interface RecurringReminderAuditService {

	void createRecurringReminderAudit(Integer reminderEventId, Integer campaignId);

	void updateRecurringReminderEventAudit(List<Integer> recurringReminderEventId, String status, String failureReason);

	List<RecurringReminderEventsAudit> findByReminderEventId(Integer recurringReminderEventId);

	void saveRecurringReminderEventsAudit(RecurringReminderEventsAudit reminderAudit);

	
}
