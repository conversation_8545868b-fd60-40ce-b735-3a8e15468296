/**
 * @file_name DripCampaignPollingActivity.java
 * @created_date 25 Mar 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.workflow.activity.email;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.entity.CampaignEvent;
import com.birdeye.campaign.repository.CampaignEventRepo;
import com.birdeye.campaign.workflow.activity.BaseActivity;
import com.birdeye.campaign.workflow.context.ProcessContext;
import com.birdeye.campaign.workflow.enums.ActivityStatus;
import com.birdeye.campaign.workflow.errorhandler.PollingErrorHandler;
/**
 * 
 * <AUTHOR>
 *
 */
@Service("campaignCustomerBatchPollingActivity")
public class CampaignCustomerBatchPollingActivity extends BaseActivity{
	
	private static final Logger			logger	= LoggerFactory.getLogger(CampaignCustomerBatchPollingActivity.class);
	
	@Autowired
	private CampaignEventRepo			campaignEventRepo;
	
	@PostConstruct
	public void init() {
		this.setActivityName("CampaignCustomerBatchPollingActivity");
		this.setErrorHandler(new PollingErrorHandler());
	}
	
	@Override
	public ProcessContext execute(ProcessContext context) throws Exception {
		Map<String, Object> map = context.getSeedData();
		
		String type = (String) map.get("type");
		Integer size = (Integer) map.get("size");
		List<CampaignEvent> events = campaignEventRepo.findByCampaignTypeAndStatusAndScheduledTimeBefore(type, ActivityStatus.INIT.getStatus(), new Date(),
				PageRequest.of(0, size, Sort.by(Sort.Direction.ASC, "scheduledTime")));
		
		logger.info("CampaignCustomerBatchPollingActivity :: Number of campaign events fetched are {}", CollectionUtils.size(events));
		if (CollectionUtils.isNotEmpty(events)) {
			updateRecordsToInProcessStatus(events);
			map.put("events", events);
		}
		return context;
	}

	private void updateRecordsToInProcessStatus(List<CampaignEvent> events) {
		logger.info("CampaignCustomerBatchPollingActivity :: updating campaign events to PROCESSING status size {}", CollectionUtils.size(events));
		events.stream().forEach(event -> event.setStatus(ActivityStatus.PROCESSING.getStatus()));
		campaignEventRepo.saveAll(events); // batch update, batch size 100, refer application.properties
		logger.info("CampaignCustomerBatchPollingActivity :: updated campaign events to PROCESSING status size {}", CollectionUtils.size(events));
	}

	
}
