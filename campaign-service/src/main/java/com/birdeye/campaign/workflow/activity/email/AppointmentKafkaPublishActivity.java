package com.birdeye.campaign.workflow.activity.email;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.KafkaTopicCache;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.KafkaTopicTypeEnum;
import com.birdeye.campaign.entity.CampaignEvent;
import com.birdeye.campaign.event.service.CampaignEventService;
import com.birdeye.campaign.kafka.service.KafkaService;
import com.birdeye.campaign.utils.CampaignUtils;
import com.birdeye.campaign.workflow.activity.BaseActivity;
import com.birdeye.campaign.workflow.context.ProcessContext;
import com.birdeye.campaign.workflow.enums.ActivityStatus;
import com.birdeye.campaign.workflow.errorhandler.PollingErrorHandler;

/**
 * 
 * <AUTHOR>
 *
 */
@Service("appointmentKafkaPublishActivity")
public class AppointmentKafkaPublishActivity extends BaseActivity {
	
	private static final Logger		logger	= LoggerFactory.getLogger(AppointmentKafkaPublishActivity.class);
	
	@Autowired
	private KafkaService			kafkaService;
	
	@Autowired
	private CampaignEventService	campaignEventService;
	
	@PostConstruct
	public void init() {
		this.setActivityName("appointmentKafkaPublishActivity");
		this.setErrorHandler(new PollingErrorHandler());
	}
	
	@Override
	public ProcessContext execute(ProcessContext context) throws Exception {
		logger.info("appointmentKafkaPublishActivity : pushing appointment events to Kafka");
		Object eventsObj = context.getSeedData().get("events");
		if (eventsObj == null) {
			logger.info("No events found to push to Kafka");
			return null;
		}
		List<CampaignEvent> events = cast(eventsObj);
		if (CollectionUtils.isEmpty(events)) {
			logger.info("No events found to push to Kafka");
			return null;
		}
		
		events.parallelStream().filter(event -> StringUtils.equalsIgnoreCase(event.getCampaignType(), Constants.MANUAL_CAMPAIGN_TYPE))
				.forEach(event -> isEventPushed(event, KafkaTopicTypeEnum.CAMPAIGN_SCHEDULED_MAUNAL_EVENT.getType()));
		
		List<CampaignEvent> emailEvents = events.stream().filter(e -> !StringUtils.equalsIgnoreCase(e.getCampaignType(), Constants.MANUAL_CAMPAIGN_TYPE)
				&& StringUtils.equalsIgnoreCase(e.getCategory(), "email") && BooleanUtils.isTrue(e.getIsReminder().intValue() == 0) && e.getReviewRequestId() != null).collect(Collectors.toList());
		String emailEventsTopic = CacheManager.getInstance().getCache(KafkaTopicCache.class).getKafkaTopic(KafkaTopicTypeEnum.CAMPAIGN_EMAIL_EVENT.getType());
		emailEvents.parallelStream().forEach(event -> isEventPushed(event, emailEventsTopic, false));
		
		List<CampaignEvent> smsEvents = events.stream().filter(e -> !StringUtils.equalsIgnoreCase(e.getCampaignType(), Constants.MANUAL_CAMPAIGN_TYPE)
				&& StringUtils.equalsIgnoreCase(e.getCategory(), "sms") && BooleanUtils.isTrue(e.getIsReminder().intValue() == 0) && e.getReviewRequestId() != null).collect(Collectors.toList());
		String smsEventsTopic = CacheManager.getInstance().getCache(KafkaTopicCache.class).getKafkaTopic(KafkaTopicTypeEnum.CAMPAIGN_SMS_EVENT.getType());
		smsEvents.parallelStream().forEach(event -> isEventPushed(event, smsEventsTopic, false));
		
		List<CampaignEvent> reminderEvents = events.stream().filter(
				e -> !StringUtils.equalsIgnoreCase(e.getCampaignType(), Constants.MANUAL_CAMPAIGN_TYPE) && BooleanUtils.isTrue(e.getIsReminder().intValue() == 1) && e.getReviewRequestId() != null)
				.collect(Collectors.toList());
		String reminderEventsTopic = KafkaTopicTypeEnum.CAMPAIGN_REMINDER_EVENT.getType();
		reminderEvents.parallelStream().forEach(event -> isEventPushed(event, reminderEventsTopic, true));
		
		return context;
	}
	
	private boolean isEventPushed(CampaignEvent event, String topicName, boolean isReminder) {
		Map<String, Object> dataMap = isReminder ? getReminderData(event.getReviewRequestId()) : getSMSAndEmailData(event.getReviewRequestId(), event.getType());
        topicName = CampaignUtils.appendCampaignTypeToTopicName(event.getType(), topicName);
		boolean isEventPushed = kafkaService.pushMessageToKafkaAcknowledged(topicName, null, dataMap);
		if (isEventPushed) {
			event.setNumAttempts(1);
			event.setStatus(ActivityStatus.SUCCESS.getStatus());
		} else {
			event.setStatus(ActivityStatus.FAILED.getStatus());
			event.setFailureReason("Push to Kafka Failed.");
			event.setNumAttempts(1);
		}
		campaignEventService.saveCampaignEventAsync(event);
		return isEventPushed;
	}
	
	@SuppressWarnings("unchecked")
	public static <T extends List<?>> T cast(Object obj) {
		return (T) obj;
	}
	
	private Map<String, Object> getSMSAndEmailData(Long reviewRequestId, String requestType) {
		Map<String, Object> data = new LinkedHashMap<>();
		data.put("reviewRequestId", reviewRequestId);
		data.put("requestType", requestType);
		return data;
	}
	
	private Map<String, Object> getReminderData(Long requestId) {
		Map<String, Object> data = new LinkedHashMap<>();
		data.put("requestId", String.valueOf(requestId));
		return data;
	}
	
	private  boolean isEventPushed(CampaignEvent event, String topicName) {
		 boolean isEventPushed = kafkaService.pushMessageToKafkaAcknowledged(topicName, null, getCampaignData(event.getCampaignId()));
		 if(isEventPushed) {
			 event.setNumAttempts(1);
			 event.setStatus(ActivityStatus.SUCCESS.getStatus());
		 }else {
			 event.setStatus(ActivityStatus.FAILED.getStatus());
			 event.setFailureReason("Push to Kafka Failed.");
			 event.setNumAttempts(1);
		 }
		 campaignEventService.saveCampaignEventAsync(event);
		 return isEventPushed;		 
	}
	
	private Map<String, Integer> getCampaignData(Integer campaignId){
		Map<String, Integer> data = new LinkedHashMap<>();
		data.put("campaignId", campaignId);
		return data;
	}
}
