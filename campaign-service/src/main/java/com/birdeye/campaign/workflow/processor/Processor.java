package com.birdeye.campaign.workflow.processor;

import java.util.List;

import com.birdeye.campaign.workflow.activity.Activity;
import com.birdeye.campaign.workflow.errorhandler.ErrorHandler;

public interface Processor {

	public boolean supports(Activity activity);

	public void doActivities();

	public void setActivities(List<Activity> activities);

	public void setDefaultErrorHandler(ErrorHandler defaultErrorHandler);
}
