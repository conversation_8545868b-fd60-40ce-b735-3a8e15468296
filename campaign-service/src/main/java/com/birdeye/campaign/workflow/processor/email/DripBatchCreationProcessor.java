package com.birdeye.campaign.workflow.processor.email;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.workflow.activity.Activity;
import com.birdeye.campaign.workflow.activity.BaseActivity;
import com.birdeye.campaign.workflow.activity.email.DripBatchCreationKafkaPublishActivity;
import com.birdeye.campaign.workflow.activity.email.DripBatchCreationPollingActivity;
import com.birdeye.campaign.workflow.context.PollingProcessContext;
import com.birdeye.campaign.workflow.context.ProcessContext;
import com.birdeye.campaign.workflow.errorhandler.ErrorHandler;
import com.birdeye.campaign.workflow.processor.BaseProcessor;

@Service("dripBatchCreationProcessor")
public class DripBatchCreationProcessor extends BaseProcessor {
	
	private static final Logger						logger	= LoggerFactory.getLogger(DripBatchCreationProcessor.class);
	
	@Autowired
	private DripBatchCreationPollingActivity		dripBatchCreationPollingActivity;
	
	@Autowired
	private DripBatchCreationKafkaPublishActivity	dripBatchCreationKafkaPublishActivity;
	
	public boolean supports(Activity activity) {
		return (activity instanceof BaseActivity);
	}
	
	public void doActivities() {
		// retrieve injected by Spring
		
		List<Activity> activities = getActivities();
		// retrieve a new instance of the Workflow ProcessContext
		ProcessContext context = createContext();
		
		if (MapUtils.isEmpty(context.getSeedData())) {
			logger.info("Exiting DRIP CAMPAIGN BATCH CREATION Processor Task. No context found");
			return;
		}
		for (Activity activity : activities) {
			logger.debug("running activity:{} using arguments: {} ", activity.getActivityName(), context);
			try {
				context = activity.execute(context);
			} catch (Exception th) {
				ErrorHandler errorHandler = activity.getErrorHandler();
				if (errorHandler == null) {
					logger.info("no error handler for this action, run default error handler and abort processing ");
					getDefaultErrorHandler().handleError(context, th);
					break;
				} else {
					logger.info("run error handler and continue");
					errorHandler.handleError(context, th);
				}
			}
			// ensure its ok to continue the process
			if (processShouldStop(context, activity))
				break;
		}
		
	}
	
	public List<Activity> getActivities() {
		logger.info("Fetching list of activities for Drip Campaign Batch Creation Processor Task");
		List<Activity> activities = new ArrayList<>();
		activities.add(dripBatchCreationPollingActivity);
		activities.add(dripBatchCreationKafkaPublishActivity);
		// activities.add(auditActivity);
		return activities;
	}
	
	private boolean processShouldStop(ProcessContext context, Activity activity) {
		if (context != null && context.stopProcess()) {
			logger.info("Interrupted workflow as requested by {}", activity.getActivityName());
			return true;
		}
		return false;
	}
	
	private ProcessContext createContext() {
		Integer pollerBatchSize = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("drip.batch.creation.poller.batch.size", 20);
		ProcessContext processContext = new PollingProcessContext();
		Map<String, Object> seedData = new HashMap<>();
		seedData.put("type", Constants.POLLER_DRIP_CAMPAIGN_BATCH_CREATE_JOB);
		seedData.put("size", pollerBatchSize);
		processContext.setSeedData(seedData);
		return processContext;
	}
}
