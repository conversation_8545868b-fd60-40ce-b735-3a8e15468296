package com.birdeye.campaign.workflow.activity.sms;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.PostConstruct;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.KafkaTopicCache;
import com.birdeye.campaign.constant.KafkaTopicTypeEnum;
import com.birdeye.campaign.entity.CampaignEvent;
import com.birdeye.campaign.event.service.CampaignEventService;
import com.birdeye.campaign.kafka.service.KafkaService;
import com.birdeye.campaign.utils.CampaignUtils;
import com.birdeye.campaign.workflow.activity.BaseActivity;
import com.birdeye.campaign.workflow.context.ProcessContext;
import com.birdeye.campaign.workflow.enums.ActivityStatus;
import com.birdeye.campaign.workflow.errorhandler.PollingErrorHandler;

/**
 * 
 * <AUTHOR>
 *
 */
@Service("dndKafkaPublishActivity")
public class DNDKafkaPublishActivity extends BaseActivity {
	
	private static final Logger		logger	= LoggerFactory.getLogger(DNDKafkaPublishActivity.class);
	
	@Autowired
	private KafkaService			kafkaService;
	
	@Autowired
	private CampaignEventService	campaignEventService;
	
	@PostConstruct
	public void init() {
		this.setActivityName("dndKafkaPublishActivity");
		this.setErrorHandler(new PollingErrorHandler());
	}
	
	@Override
	public ProcessContext execute(ProcessContext context) throws Exception {
		Map<Integer, Boolean> eventIdToStatusMap = new ConcurrentHashMap<>();
		Object eventsObj = context.getSeedData().get("events");
		if (eventsObj == null) {
			logger.info("No events found to push to Kafka");
			return null;
		}
		List<CampaignEvent> events = cast(eventsObj);
		if (CollectionUtils.isEmpty(events)) {
			logger.info("No events found to push to Kafka");
			return null;
		}
		String topicName = CacheManager.getInstance().getCache(KafkaTopicCache.class).getKafkaTopic(KafkaTopicTypeEnum.CAMPAIGN_SMS_EVENT.getType());
		events.parallelStream().forEach(event -> isEventPushed(event, eventIdToStatusMap, topicName));
		
		return context;
	}
	
	private boolean isEventPushed(CampaignEvent event, Map<Integer, Boolean> eventIdToStatusMap, String topicName) {
		topicName = CampaignUtils.appendCampaignTypeToTopicName(event.getType(), topicName);
		boolean isEventPushed = kafkaService.pushMessageToKafkaAcknowledged(topicName, null, getData(event.getReviewRequestId(),event.getType()));
		if (isEventPushed) {
			event.setNumAttempts(1);
			event.setStatus(ActivityStatus.SUCCESS.getStatus());
		} else {
			event.setStatus(ActivityStatus.FAILED.getStatus());
			event.setFailureReason("Push to Kafka Failed");
			event.setNumAttempts(1);
		}
		campaignEventService.saveCampaignEventAsync(event);
		return isEventPushed;
		// eventIdToStatusMap.put(event.getReviewRequestId(), isEventPushed);
		
	}
	
	@SuppressWarnings("unchecked")
	public static <T extends List<?>> T cast(Object obj) {
		return (T) obj;
	}
	
	private Map<String, Object> getData(Long reviewRequestId,String requestType){
		Map<String, Object> data = new LinkedHashMap<>();
		data.put("reviewRequestId", reviewRequestId);
		data.put("requestType", requestType);
		return data;
	}
}
