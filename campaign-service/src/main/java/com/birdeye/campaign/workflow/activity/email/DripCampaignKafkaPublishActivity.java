package com.birdeye.campaign.workflow.activity.email;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.PostConstruct;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.constant.KafkaTopicTypeEnum;
import com.birdeye.campaign.entity.CampaignEvent;
import com.birdeye.campaign.event.service.CampaignEventService;
import com.birdeye.campaign.kafka.service.KafkaService;
import com.birdeye.campaign.workflow.activity.BaseActivity;
import com.birdeye.campaign.workflow.context.ProcessContext;
import com.birdeye.campaign.workflow.enums.ActivityStatus;
import com.birdeye.campaign.workflow.errorhandler.PollingErrorHandler;

@Service("dripCampaignKafkaPublishActivity")
public class DripCampaignKafkaPublishActivity extends BaseActivity {
	
	private static final Logger			logger	= LoggerFactory.getLogger(DripCampaignKafkaPublishActivity.class); 

	@Autowired
	private KafkaService kafkaService;
	
	@Autowired
	private CampaignEventService campaignEventService;
	
	@PostConstruct
	public void init() {
		this.setActivityName("dripCampaignKafkaPublishActivity");
		this.setErrorHandler(new PollingErrorHandler());
	}

	@Override
	public ProcessContext execute(ProcessContext context) throws Exception {
		logger.info("dripCampaignKafkaPublishActivity : pushing email events to Kafka");
		Map<Integer, Boolean> eventIdToStatusMap = new ConcurrentHashMap<>();
		Object eventsObj = context.getSeedData().get("events");
		if(eventsObj == null) {
			logger.info("No events found to push to Kafka");
			return null;
		}
		List<CampaignEvent> events = cast(eventsObj);
		if(CollectionUtils.isEmpty(events)) {
			logger.info("No events found to push to Kafka");
			return null;
		}
		events.parallelStream().forEach(event -> isEventPushed(event, eventIdToStatusMap));
		
		return context;
	}
	
	private  boolean isEventPushed(CampaignEvent event, Map<Integer, Boolean> eventIdToStatusMap) {
		 boolean isEventPushed = kafkaService.pushMessageToKafkaAcknowledged(KafkaTopicTypeEnum.CAMPAIGN_DRIP_EVENT.getType(),null, getData(event.getCampaignId()));
		 if(isEventPushed) {
			 event.setNumAttempts(1);
			 event.setStatus(ActivityStatus.SUCCESS.getStatus());
		 }else {
			 event.setStatus(ActivityStatus.FAILED.getStatus());
			 event.setFailureReason("Push to Kafka Failed.");
			 event.setNumAttempts(1);
		 }
		 campaignEventService.saveCampaignEventAsync(event);
		 return isEventPushed;
		 //eventIdToStatusMap.put(event.getReviewRequestId(), isEventPushed);
		 
	}

	@SuppressWarnings("unchecked")
	public static <T extends List<?>> T cast(Object obj) {
	    return (T) obj;
	}
	
	
	private Map<String, Integer> getData(Integer campaignId){
		Map<String, Integer> data = new LinkedHashMap<>();
		data.put("campaignId", campaignId);
		return data;
	}
}
