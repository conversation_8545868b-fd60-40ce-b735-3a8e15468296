package com.birdeye.campaign.template.ai.dao.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.entity.TemplateAICustomFieldAssociation;
import com.birdeye.campaign.enums.CustomFieldSourceEnum;
import com.birdeye.campaign.repository.TemplateAICustomFieldAssociationRepo;
import com.birdeye.campaign.template.ai.dao.service.ITemplateAICustomFieldAssociationDao;

@Service("templateAICustomFieldAssociationDao")
public class TemplateAICustomFieldAssociationDaoImpl implements ITemplateAICustomFieldAssociationDao {
	
	@Autowired
	private TemplateAICustomFieldAssociationRepo customFieldRepo;
	
	@Override
	public int deleteCustomField(Integer associatedObjId, String assoicatedObjType, CustomFieldSourceEnum enumObject) {
		return customFieldRepo.deleteCustomField(associatedObjId, assoicatedObjType, enumObject);
	}
	
	@Override
	public void saveCustomFieldList(List<TemplateAICustomFieldAssociation> customFieldAssociationList) {
		customFieldRepo.saveAll(customFieldAssociationList);
	}

	/**
	 * Retrieves active custom fields associated with a specific object ID and type.
	 *
	 * @param associatedObjectId the ID of the associated object
	 * @param associatedObjectType the type of the associated object
	 * @param customFieldSourceEnum the source enum for custom fields
	 * @return a list of active custom field associations
	 */
	@Override
	public List<TemplateAICustomFieldAssociation> getActiveCustomFields(Integer associatedObjectId, String associatedObjectType, CustomFieldSourceEnum customFieldSourceEnum) {
		return customFieldRepo.findActiveCustomFieldByObjectIdAndTypeAndSourceEnum(associatedObjectId,associatedObjectType,customFieldSourceEnum);
	}
}
