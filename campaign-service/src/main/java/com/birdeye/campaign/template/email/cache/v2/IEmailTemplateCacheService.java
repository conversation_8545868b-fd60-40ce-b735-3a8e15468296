/**
 * @file_name IEmailTemplateCacheService.java
 * @created_date 25 Feb 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.template.email.cache.v2;

import com.birdeye.campaign.entity.EmailTemplate;
import com.birdeye.campaign.response.template.v2.ReviewRatingAndCountResponse;
import com.birdeye.dto.reviewgen.ReviewSourceWrapper;

/**
 * @file_name IEmailTemplateCacheService.java
 * @created_date 25 Feb 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public interface IEmailTemplateCacheService {

	/**
	 * @param type
	 * @return
	 */
	EmailTemplate getDefaultEmailTemplateByType(String type);

	/**
	 * @param businessId
	 * @return
	 */
	String getDefaultReplyToEmailId(Integer businessId);

	/**
	 * @param type
	 * @return
	 */
	String getDefaultCustomHtmlContentByType(String type);

	/**
	 * @param templateId
	 * @param deviceType
	 * @return
	 */
	ReviewSourceWrapper getSelectedDeeplinksBySource(Integer templateId, String deviceType);

	/**
	 * @param businessNumber
	 * @return
	 * @throws Exception
	 */
	ReviewRatingAndCountResponse getAverageRatingAndReviewsCount(Long businessNumber) throws Exception;

}
