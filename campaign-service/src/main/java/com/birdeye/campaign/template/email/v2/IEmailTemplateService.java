/**
 * @file_name IEmailTemplateService.java
 * @created_date 13 Feb 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.template.email.v2;

import java.util.List;

import com.birdeye.campaign.entity.BusinessEmailTemplate;
import com.birdeye.campaign.request.GenAIPhishingDetectionRequest;
import com.birdeye.campaign.response.template.v2.EditTemplateResponse;
import com.birdeye.campaign.response.template.v2.EmailTemplateResponse;

/**
 * @file_name IEmailTemplateService.java
 * @created_date 13 Feb 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public interface IEmailTemplateService {

	/**
	 * @param templateId
	 * @param businessId
	 * @param type
	 * @param request
	 * @return
	 */
	EditTemplateResponse createOrUpdateBusinessEmailTemplate(Integer templateId, Integer businessId, String type, EmailTemplateResponse request);

	/**
	 * @param templateId
	 * @param requestType
	 * @return
	 */
	EmailTemplateResponse getEmailTemplateResponseById(Integer templateId, String requestType);

	/**
	 * @param businessId
	 * @return
	 */
	EditTemplateResponse createDefaultBusinessEmailTemplate(Integer businessId);
	
	EditTemplateResponse createDefaultBusinessEmailTemplateWithRS(Integer businessId);

	/**
	 * @param businessId
	 * @param templateType
	 * @return
	 */
	EditTemplateResponse createBusinessEmailTemplate(Integer businessId, String templateType);
	
	/**
	 * Get template data for deeplink api.
	 * 
	 * @param templateId
	 * @param requestType
	 * @return
	 */
	EmailTemplateResponse getEmailTemplateResponseForDeeplink(Integer templateId, String requestType);

	EditTemplateResponse createDefaultEmailTemplateWithGoogleFb(Integer businessId);

	EmailTemplateResponse getEmailResellerLevelTemplateResponseById(Integer enterpriseId, String requestType, Integer resellerId);

	EditTemplateResponse createOrUpdateResellerEmailTemplate(String type, EmailTemplateResponse request, Integer resellerId);

	Integer checkIfResellerTemplateExists(Integer templateId, Integer enterpriseId, String requestType);

	EditTemplateResponse createDefaultResellerEmailTemplate(Integer businessId, Integer resellerId);

	void validateResellerAndType(Integer resellerId, String type);

	EditTemplateResponse createDefaultEmailTemplateForAppointment(Integer businessId, String type);

	BusinessEmailTemplate getBusinessEmailTemplateByEmailTemplateIdAndEnterpriseId(Integer enterpriseId, Integer emailTemplateId);

	EditTemplateResponse createDefaultPromotionalEmailTemplateOutOfTheBox(Integer businessId, String name, String subject, String heading, String message, String customHtmlEnabled, String customHtml);

	EditTemplateResponse createDefaultEmailCXTemplateWithGoogleFb(Integer businessId);

	EmailTemplateResponse getDefaultEmailTemplateResponseForDeeplink(String requestType, Integer enterpriseId);
	
	void detectPhishingEmailTemplate(Integer templateId);

	void bulkDetectPhishingTemplates(List<Integer> emailTemplateIds);

	GenAIPhishingDetectionRequest getGenAIPayload(Integer templateId);

	Integer createDefaultResellerEmailTemplate(Integer enterpriseId, Integer resellerId, String templateType);

	EmailTemplateResponse getFreeTrialEmailTemplate(Integer enterpriseId, String requestType);
	
}
