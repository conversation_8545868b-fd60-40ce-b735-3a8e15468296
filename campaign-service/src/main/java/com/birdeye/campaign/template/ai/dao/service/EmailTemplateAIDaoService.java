package com.birdeye.campaign.template.ai.dao.service;

import java.util.List;

import com.birdeye.campaign.dto.BusinessAITemplateEntity;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.entity.EmailTemplateAI;

public interface EmailTemplateAIDaoService {
	
    EmailTemplateAI getGlobalDefaultEmailTemplateAIByType(String rrTemplateName);

    EmailTemplateAI getEmailTemplateById(Integer aiTemplateId);

	EmailTemplateAI saveTemplate(EmailTemplateAI templateObject);

	Long getSameNameCountForTemplate(String name, Integer accountId);

	Long getNameCountForTemplateUpdate(String baseName, String oldName, Integer accountId);

	CachedCollectionWrapper<BusinessAITemplateEntity> getAccountTemplatesListByAccountId(Integer accountId);

	List<BusinessAITemplateEntity> getAIEmailTemplatesByType(Integer accountId, List<String> templateType);
}
