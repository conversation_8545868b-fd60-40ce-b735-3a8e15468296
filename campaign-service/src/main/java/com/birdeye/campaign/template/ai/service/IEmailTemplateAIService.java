package com.birdeye.campaign.template.ai.service;

import java.util.List;

import com.birdeye.campaign.ai.request.AITemplateFilters;
import com.birdeye.campaign.ai.response.GetEmailTemplateAIResponse;
import com.birdeye.campaign.dto.BusinessAITemplateEntity;
import com.birdeye.campaign.dto.BusinessAITemplateMessage;
import com.birdeye.campaign.dto.BusinessAllAITemplatesMessage;
import com.birdeye.email.templates.ai.request.EmailTemplateAISaveRequest;
import com.birdeye.email.templates.ai.response.EmailTemplateAISaveResponse;

public interface IEmailTemplateAIService {

    GetEmailTemplateAIResponse getEmailTemplate(String type, Integer templateId, Integer userId, Integer businessId);
	
	EmailTemplateAISaveResponse saveTemplate(Integer userId, Integer accountId, String type, Integer templateId, EmailTemplateAISaveRequest request) throws Exception;

    BusinessAllAITemplatesMessage getAllEmailTemplatesByFilters(Integer accountId, AITemplateFilters templateFilters);
    
    List<BusinessAITemplateMessage> getAIEmailTemplateMessages(List<BusinessAITemplateEntity> aiTemplateEntities, Integer accountId);

	List<BusinessAITemplateMessage> getAIEmailTemplatesByType(Integer accountId, List<String> templateTypes);
	
	Boolean deleteAIEmailTemplate(Integer aiTemplateId, Integer accountId, Integer loggedInUserId);
    
}
