/**
 * @file_name EmailTemplateServiceImpl.java
 * @created_date 13 Feb 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.template.email.v2.impl;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.aspect.annotation.Profiled;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.constant.KafkaTopicTypeEnum;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.BusinessTemplateEntity;
import com.birdeye.campaign.dto.KafkaMessage;
import com.birdeye.campaign.entity.BusinessEmailTemplate;
import com.birdeye.campaign.entity.CustomHTMLTemplatesScannedAudit;
import com.birdeye.campaign.enums.EmailTemplateTypes;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.external.service.impl.GenAIExternalService;
import com.birdeye.campaign.external.utils.GenAIPhishingDetectionUtils;
import com.birdeye.campaign.kafka.service.KafkaService;
import com.birdeye.campaign.platform.constant.FileFormat;
import com.birdeye.campaign.platform.constant.TemplateTypeEnum;
import com.birdeye.campaign.platform.readonly.repository.BusinessReadOnlyRepo;
import com.birdeye.campaign.repository.BusinessEmailTemplateRepo;
import com.birdeye.campaign.request.GenAIPhishingDetectionRequest;
import com.birdeye.campaign.request.TemplateBaseEvent;
import com.birdeye.campaign.response.external.GenAIPhishingDetectionResponse;
import com.birdeye.campaign.response.template.v2.EditTemplateResponse;
import com.birdeye.campaign.response.template.v2.EmailTemplateResponse;
import com.birdeye.campaign.service.ContentScanningService;
import com.birdeye.campaign.service.IReviewSourcesService;
import com.birdeye.campaign.service.dao.BusinessEmailTemplateDao;
import com.birdeye.campaign.service.dao.CustomHTMLTemplateScannedAuditDao;
import com.birdeye.campaign.sro.ReviewSourceSRO;
import com.birdeye.campaign.template.email.v2.IEmailTemplateService;
import com.birdeye.campaign.utils.BusinessUtils;
import com.birdeye.campaign.utils.CampaignUtils;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.PublicMediaUrlUtils;
import com.google.common.collect.Lists;

/**
 * @file_name EmailTemplateServiceImpl.java
 * @created_date 13 Feb 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@Service("emailTemplateServiceV2")
public class EmailTemplateServiceImpl implements IEmailTemplateService {
	
	@Value("${spring.profiles.active}")
	private String								activeProfile;
	
	private static final Logger											LOGGER	= LoggerFactory.getLogger(EmailTemplateServiceImpl.class);
	
	@Autowired
	private BusinessEmailTemplateRepo									businessEmailTemplateRepo;
	
	@Autowired
	private IReviewSourcesService										reviewSourcesService;
	
	@Autowired
	private BusinessReadOnlyRepo								businessReadOnlyRepo;
	
	@Autowired
	private BusinessEmailTemplateDao businessEmailTemplateDao;
	
	@Autowired
	private KafkaService				kafkaService;
	
	@Autowired
	private GenAIExternalService		genAIExternalService;
	
	@Autowired
	private CustomHTMLTemplateScannedAuditDao customHTMLTemplateScannedAuditDao;
	
	@Autowired
	private ContentScanningService				contentScanningService;

	
	/**
	 * Create Default Email Template for a Business
	 * 
	 * Create a RR Template
	 * 
	 * @param businessId
	 */
	@Override
	public EditTemplateResponse createDefaultBusinessEmailTemplate(Integer businessId) {
		String defaultTemplateType = StringUtils.lowerCase(EmailTemplateTypes.REVIEW_REQUEST_NEW.name());
		EmailTemplateResponse defaultTemplate = getEmailTemplateResponseById(0, defaultTemplateType);
		return createOrUpdateBusinessEmailTemplate(0, businessId, defaultTemplateType, defaultTemplate);
	}
	
	/**
	 * Create Default Email Template for a Business
	 * 
	 * Create a RR Template
	 * 
	 * @param businessId
	 */
	@Override
	public EditTemplateResponse createDefaultEmailTemplateWithGoogleFb(Integer businessId) {
		String defaultTemplateType = StringUtils.lowerCase(EmailTemplateTypes.REVIEW_REQUEST_NEW.name());
		EmailTemplateResponse defaultTemplate = getEmailTemplateResponseById(0, defaultTemplateType);
		
		// BIRDEYE-80354 | Adding Google/FB as selected source in default template.
		defaultTemplate.setSelectedSources(reviewSourcesService.getDefaultReviewSourcesForOnboarding(businessId));
		return createOrUpdateBusinessEmailTemplate(0, businessId, defaultTemplateType, defaultTemplate);
	}
	
	/**
	 * Create Default Email Template for a Business
	 * 
	 * Create a CX Template
	 * 
	 * @param businessId
	 */
	@Override
	public EditTemplateResponse createDefaultEmailCXTemplateWithGoogleFb(Integer businessId) {
		String defaultTemplateType = StringUtils.lowerCase(TemplateTypeEnum.CUSTOMER_EXPERIENCE.getName());
		EmailTemplateResponse defaultTemplate = getEmailTemplateResponseById(0, defaultTemplateType);
		
		// BIRDEYE-80354 | Adding Google/FB as selected source in default template.
		defaultTemplate.setSelectedSources(reviewSourcesService.getDefaultReviewSourcesForOnboarding(businessId));
		return createOrUpdateBusinessEmailTemplate(0, businessId, defaultTemplateType, defaultTemplate);
	}
	
	/**
	 * Create Default Email Template for a Business
	 * 
	 * Create a Appointment Template
	 * 
	 * @param businessId,
	 *            type
	 */
	@Override
	public EditTemplateResponse createDefaultEmailTemplateForAppointment(Integer businessId, String type) {
		String defaultTemplateType = StringUtils.lowerCase(type);
		EmailTemplateResponse defaultTemplate = getEmailTemplateResponseById(0, defaultTemplateType);
		return createOrUpdateBusinessEmailTemplate(0, businessId, defaultTemplateType, defaultTemplate);
	}
	
	@Override
	public EditTemplateResponse createDefaultPromotionalEmailTemplateOutOfTheBox(Integer businessId, String name, String subject, String heading, String message,
			String customHtmlEnabled, String customHtml) {
		String defaultTemplateType = StringUtils.lowerCase(EmailTemplateTypes.PROMOTION.name());
		EmailTemplateResponse defaultTemplate = getEmailTemplateResponseById(0, defaultTemplateType);
		defaultTemplate.setName(name);
		defaultTemplate.setSubject(subject);
		defaultTemplate.setReviewHeading(heading);
		defaultTemplate.setReviewMessage(message);
		defaultTemplate.setCustomHtmlEnabled(CoreUtils.getNullSafeInteger(customHtmlEnabled));
		defaultTemplate.setCustomHtml(customHtml);
		return createOrUpdateBusinessEmailTemplate(0, businessId, defaultTemplateType, defaultTemplate);
	}
	
	@Override
	public EditTemplateResponse createDefaultResellerEmailTemplate(Integer businessId, Integer resellerId) {
		String defaultTemplateType = StringUtils.lowerCase(EmailTemplateTypes.REVIEW_REQUEST_NEW.name());
		EmailTemplateResponse defaultTemplate = getEmailResellerLevelTemplateResponseById(businessId, defaultTemplateType, resellerId);
		if (defaultTemplate == null) {
			return createDefaultEmailTemplateWithGoogleFb(businessId);
		}
		return createOrUpdateBusinessEmailTemplate(0, businessId, defaultTemplateType, defaultTemplate);
	}
	
	@Override
	public Integer createDefaultResellerEmailTemplate(Integer enterpriseId, Integer resellerId, String templateType) {
		
		LOGGER.info("Creating default reseller email template for enterprise: {}, reseller: {} and type: {}", enterpriseId, resellerId, templateType);
		
		EmailTemplateResponse defaultTemplate = getEmailResellerLevelTemplateResponseById(enterpriseId, templateType, resellerId);
		
		EditTemplateResponse templateResponse = createOrUpdateBusinessEmailTemplate(0, enterpriseId, templateType, defaultTemplate);
		
		return templateResponse.getId();
	}
	
	/**
	 * Create Default Email Template for a Business.
	 * This method is only for enterprise migration. This does not set google and fb as default review sources. Rather pick the existing unique sources from templates on all locations.
	 * If unique sources count > 3 then google and fb are set.
	 * Create a RR Template
	 * 
	 * @param businessId
	 */
	@Override
	public EditTemplateResponse createDefaultBusinessEmailTemplateWithRS(Integer businessId) {
		String defaultTemplateType = StringUtils.lowerCase(EmailTemplateTypes.REVIEW_REQUEST_NEW.name());
		EmailTemplateResponse defaultTemplate = getEmailTemplateResponseById(0, defaultTemplateType);
		
		List<ReviewSourceSRO> existingUniquePriorities = getExistingUniquePriorities(businessId);
		if (CollectionUtils.isNotEmpty(existingUniquePriorities)) {
			defaultTemplate.setSelectedSources(existingUniquePriorities);
		}
		return createOrUpdateBusinessEmailTemplate(0, businessId, defaultTemplateType, defaultTemplate);
	}
	
	private List<ReviewSourceSRO> getExistingUniquePriorities(Integer businessId) {
		List<Integer> enterpriseLocations = businessReadOnlyRepo.findEnterpriseLocations(businessId);
		if(CollectionUtils.isEmpty(enterpriseLocations)) {
			LOGGER.warn("No valid locations found! Default review sources will be created for business: {}", businessId);
			return Collections.emptyList();
		}
		//TODO: Filter default templates only
		List<BusinessTemplateEntity> emailTemplates = businessEmailTemplateRepo.getRRTemplatesByLocationIds(enterpriseLocations);
		if(CollectionUtils.isEmpty(emailTemplates)) {
			LOGGER.warn("No valid email templates found! Default review sources will be created for business: {}", businessId);
		}
		List<Integer> emailTemplateIds = emailTemplates.stream().map(BusinessTemplateEntity::getTemplateId).collect(Collectors.toList());
		
		return reviewSourcesService.getExistingUniquePrioritiesForEnterprise(emailTemplateIds, "web", businessId, enterpriseLocations);
	}

	/**
	 * Create Default Email Template for a Business
	 *
	 *
	 * @param businessId
	 * @param templateType
	 */
	@Override
	public EditTemplateResponse createBusinessEmailTemplate(Integer businessId, String templateType) {
		EmailTemplateResponse defaultTemplate = getEmailTemplateResponseById(0, templateType);
		return createOrUpdateBusinessEmailTemplate(0, businessId, templateType, defaultTemplate);
	}
	
	/**
	 * Create or update a email template
	 * 
	 * Create and update have similar workflows except name generation strategy
	 * 
	 * @param templateId
	 * @param type
	 * @param request
	 */
	@Override
	public EditTemplateResponse createOrUpdateBusinessEmailTemplate(Integer templateId, Integer businessId, String type, EmailTemplateResponse request) {
		LOGGER.info("Received create or update business email template API for template id {} business id {} type {} :: {}", templateId, businessId, type, request);
		if (type == null || request == null) {
			LOGGER.error("createOrUpdateBusinessEmailTemplate - Request is invalid for template id {} business id {} type {} :: request {}", templateId, businessId, type, request);
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "Invalid Request");
		}
		validateMediaUrls(type, request);
		boolean suspiciousTemplate = contentScanningService.isSuspiciousEmailTemplate(request, templateId, businessId, type);
		if (suspiciousTemplate) {
			return new EditTemplateResponse(templateId, request.getName());
		}
		
		return businessEmailTemplateDao.getEditTemplateResponse(templateId, businessId, type, request);
	}
	
	@Override
	public EditTemplateResponse createOrUpdateResellerEmailTemplate(String type, EmailTemplateResponse request, Integer resellerId) {
		LOGGER.info("Received create or update request for Reseller email template API for reseller {} type {} :: {}", resellerId, type, request);
		if (StringUtils.isBlank(type) || request == null || resellerId == null) {
			LOGGER.error("createOrUpdateResellerLevelEmailTemplate - Request is invalid for type {} :: request {}", type, request);
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "Invalid Request");
		}
		return businessEmailTemplateDao.getEditResellerLevelTemplateResponse(type, request, resellerId);
	}

	private BusinessEnterpriseEntity getParentOrEnterpriseOrReseller(BusinessEnterpriseEntity business) {
		if (business.getBusinessParentId() != null) {
			business = businessReadOnlyRepo.getBusinessByBid(business.getBusinessParentId());
		} else if (business.getEnterpriseId() != null) {
			business = businessReadOnlyRepo.getBusinessByBid(business.getEnterpriseId());
		} else if (business.getResellerId() != null) {
			business = businessReadOnlyRepo.getBusinessByBid(business.getResellerId());
		} else {
			business = null;
		}
		return business;
	}
	
	@Override
	public Integer checkIfResellerTemplateExists(Integer templateId, Integer enterpriseId, String requestType) {
		if (enterpriseId == null || StringUtils.isBlank(requestType)) {
			throw new CampaignException(ErrorCodes.ERROR_WHILE_FETCHING_RESELLER_TEMPLATE, "enterpriseId or requestType can't be null while fetching templates by id");
		}
		boolean isNewTemplate = StringUtils.equalsAnyIgnoreCase(String.valueOf(templateId), "0", "null");
		if (isNewTemplate) {
			int i = 0;
			BusinessEnterpriseEntity business = businessReadOnlyRepo.getValidBusinessByBid(enterpriseId);
			while (business != null && i < Constants.MAX_RESELLER_HIERARCHY_DEPTH && BooleanUtils.isFalse(BusinessUtils.isBirdeyeReseller(business.getId()))) {
				BusinessEmailTemplate businessEmailTemplate = businessEmailTemplateRepo.getByResellerIdAndTypeAndIsDeleted(business.getId(), requestType, 0);
				if (businessEmailTemplate != null) {
					return business.getId();
				}
				business = getParentOrEnterpriseOrReseller(business);
				i++;
			}
		}
		return null;
	}
	
	@Override
	public void validateResellerAndType(Integer resellerId, String type) {
		if (!EmailTemplateTypes.PROMOTION.name().equalsIgnoreCase(type) && !EmailTemplateTypes.REVIEW_REQUEST_NEW.name().equalsIgnoreCase(type)
				&& !EmailTemplateTypes.SURVEY_REQUEST.name().equalsIgnoreCase(type) && !EmailTemplateTypes.CUSTOMER_EXPERIENCE.name().equalsIgnoreCase(type)
				&& !EmailTemplateTypes.REFERRAL.name().equalsIgnoreCase(type)) {
			throw new CampaignException(ErrorCodes.INVALID_TYPE, ErrorCodes.INVALID_TYPE.getMessage());
		}
		BusinessEnterpriseEntity business = businessReadOnlyRepo.getValidBusinessByBid(resellerId);
		if (BusinessUtils.isResellerAndNotBirdeyeReseller(business)) {
			return;
		} else {
			throw new CampaignException(ErrorCodes.ERROR_NOT_A_RESELLER_ACCOUNT, "Error, Not a reseller account");
		}
	}
	
	
	@Override
	@Profiled
	public EmailTemplateResponse getEmailTemplateResponseById(Integer templateId, String requestType) {
		EmailTemplateTypes type = EmailTemplateTypes.valueOf(StringUtils.upperCase(requestType));
		switch (type) {
			case REVIEW_REQUEST_NEW:
				return businessEmailTemplateDao.getRRTemplate(templateId, requestType);
			case CUSTOMER_EXPERIENCE:
				return businessEmailTemplateDao.getCXTemplate(templateId, requestType);
			case SURVEY_REQUEST:
				return businessEmailTemplateDao.getSurveyTemplate(templateId, requestType);
			case PROMOTION:
				return businessEmailTemplateDao.getPromotionTemplate(templateId, requestType);
			case REFERRAL:
				return businessEmailTemplateDao.getReferralTemplate(templateId, requestType);
			case APPOINTMENT_REMINDER:
				return businessEmailTemplateDao.getAppointmentReminderTemplate(templateId, requestType);
			case APPOINTMENT_RECALL:
				return businessEmailTemplateDao.getAppointmentRecallTemplate(templateId, requestType);
			case APPOINTMENT_FORM:
				return businessEmailTemplateDao.getAppointmentFormTemplate(templateId, requestType);
			case QR_REVIEW:
				return businessEmailTemplateDao.getQRTemplate(templateId, requestType);
			default:
				return null;
		}
	}
	
	@Override
	@Profiled
	public EmailTemplateResponse getEmailResellerLevelTemplateResponseById(Integer enterpriseId, String requestType, Integer resellerId) {
		BusinessEmailTemplate businessEmailTemplate = businessEmailTemplateRepo.getByResellerIdAndTypeAndIsDeleted(resellerId, requestType, 0);
		if (businessEmailTemplate == null) {
			LOGGER.info("No reseller template found for the reseller id {} and type {} ", resellerId, requestType);
			return null;
		}
		businessEmailTemplate.setEnterpriseId(enterpriseId);
		return businessEmailTemplateDao.getExistingResellerTemplate(businessEmailTemplate, requestType, resellerId);
	}
	
	/**
	 *
	 * Fetch default email template response for deeplink
	 * 
	 * 
	 * @param requestType, enterpriseId
	 */
	@Override
	public EmailTemplateResponse getDefaultEmailTemplateResponseForDeeplink(String requestType, Integer enterpriseId) {
		EmailTemplateTypes type = EmailTemplateTypes.valueOf(StringUtils.upperCase(requestType));
		switch (type) {
			case REVIEW_REQUEST_NEW:
				return businessEmailTemplateDao.getRRTemplateNewForDeeplink(requestType, enterpriseId);
			default:
				return null;
		}
	}
	
	/**
	 * Get template data for deeplink api.
	 * 
	 * @param templateId
	 * @param requestType
	 * @return
	 */
	@Override
	@Profiled
	public EmailTemplateResponse getEmailTemplateResponseForDeeplink(Integer templateId, String requestType) {
		EmailTemplateTypes type = EmailTemplateTypes.valueOf(StringUtils.upperCase(requestType));
		switch (type) {
			case REVIEW_REQUEST_NEW:
				return businessEmailTemplateDao.existingRRTemplateData(null, templateId);
			case CUSTOMER_EXPERIENCE:
				return businessEmailTemplateDao.existingCXTemplateData(null,templateId);
			case SURVEY_REQUEST:
				return businessEmailTemplateDao.existingSurveyTemplateData(null,templateId);
			case PROMOTION:
				return businessEmailTemplateDao.existingPromotionTemplateData(null,templateId);
			case REFERRAL:
				return businessEmailTemplateDao.existingReferralTemplateData(null,templateId);
			case APPOINTMENT_REMINDER:
				return businessEmailTemplateDao.existingAppointmentTemplateData(null, templateId);
			case APPOINTMENT_RECALL:
				return businessEmailTemplateDao.existingAppointmentTemplateData(null, templateId);
			case APPOINTMENT_FORM:
				return businessEmailTemplateDao.existingAppointmentTemplateData(null, templateId);
			case QR_REVIEW:
				return businessEmailTemplateDao.existingRRTemplateData(null,templateId); 
			default:
				return null;
		}
	}
	
	@Override
	public BusinessEmailTemplate getBusinessEmailTemplateByEmailTemplateIdAndEnterpriseId(Integer enterpriseId, Integer emailTemplateId) {
		return businessEmailTemplateDao.getBusinessEmailTemplateByEmailTemplateIdAndEnterpriseId(enterpriseId, emailTemplateId);
	}
	
	/**
	 * Detect suspicious or phishing content in custom html enabled templates
	 */
	@Override
	public void detectPhishingEmailTemplate(Integer templateId) {
		BusinessTemplateEntity businessTemplateEntity = businessEmailTemplateDao.getBusinessTemplateEntityByTemplateId(templateId);
		// only templates where custom HTML is enabled are scanned, if need to scan all types then add improvement
		if (businessTemplateEntity == null || !CoreUtils.getBooleanValueFromInteger(businessTemplateEntity.getCustomHtmlEnabled())) {
			LOGGER.error("no valid custom html enabled template found with id {}", templateId);
			return;
		}
		CustomHTMLTemplatesScannedAudit customHTMLTemplatesScannedAudit = new CustomHTMLTemplatesScannedAudit(businessTemplateEntity);
		customHTMLTemplatesScannedAudit.setIsSuspiciousSubject(CoreUtils.getIntegerValueFromBoolean(suspiciousContentFound(businessTemplateEntity.getSubject(), templateId)));
		customHTMLTemplatesScannedAudit.setIsPhishingTemplateGenAI(CoreUtils.getIntegerValueFromBoolean(isPhishingHTMLTemplate(businessTemplateEntity)));
		// saving derived result in table, value=1 if either subject or HTML is spam
		customHTMLTemplatesScannedAudit.setIsSpamTemplate(Math.max(customHTMLTemplatesScannedAudit.getIsSuspiciousSubject(), customHTMLTemplatesScannedAudit.getIsPhishingTemplateGenAI()));
		customHTMLTemplateScannedAuditDao.saveScannedcustomHTMLTemplateEntry(customHTMLTemplatesScannedAudit);
	}
	
	private boolean suspiciousContentFound(String emailContent, Integer templateId) {
		try {
			if (CampaignUtils.containsAnyBlocklistedKeyword(emailContent)) {
				LOGGER.info("Received suspicious content for templateId {}", templateId);
				return true;
			}
		} catch (Exception e) {
			LOGGER.error("getting error while checking email content for template id {} : {}", templateId, ExceptionUtils.getStackTrace(e));
			return false;
		}
		LOGGER.info("no suspicious keyword present in template id {}", templateId);
		return false;
	}
	
	private boolean isPhishingHTMLTemplate(BusinessTemplateEntity businessTemplateEntity) {
		LOGGER.info("isPhishingHTMLTemplate : scanning content via genai phishing detection api for template id {}", businessTemplateEntity.getTemplateId());
		GenAIPhishingDetectionResponse response = genAIExternalService.phishingDetection(GenAIPhishingDetectionUtils.getGenAITemplatePhishingDetectionRequest(businessTemplateEntity));
		if (response != null) {
			LOGGER.info("genai response for template id {} , response {}", businessTemplateEntity.getTemplateId(), response);
		}
		
		return response != null && response.getStatusCode() != null && response.getStatusCode() == 200 && BooleanUtils.isTrue(response.getPhishing());
	}
	
	@Override
	public void bulkDetectPhishingTemplates(List<Integer> emailTemplateIds) {
		LOGGER.info("bulkDetectPhishingTemplates : for email template ids size {}", CollectionUtils.size(emailTemplateIds));
		List<List<Integer>> templateIdsBatch = Lists.partition(emailTemplateIds, 200);
		templateIdsBatch.stream().map(templateIds -> templateIds.stream().map(tId -> {
			KafkaMessage kafkaMessage = new KafkaMessage(new TemplateBaseEvent(tId, Constants.TEMPLATE_BASE_TYPE_EMAIL));
			LOGGER.info("Created KafkaMessage for template ID {} ", tId);
			return kafkaMessage;
		}).collect(Collectors.toList())).forEach(kafkaMessages -> {
			kafkaService.pushMessagesListToKafka(KafkaTopicTypeEnum.DETECT_PHISHING_TEMPLATE, kafkaMessages);
			LOGGER.info("Pushed KafkaMessages to Kafka for batch size: {} ", kafkaMessages.size());
		});
		LOGGER.info("bulkDetectPhishingTemplates : published all events size {}", CollectionUtils.size(emailTemplateIds));
	}

	@Override
	public GenAIPhishingDetectionRequest getGenAIPayload(Integer templateId) {
		BusinessTemplateEntity businessTemplateEntity = businessEmailTemplateDao.getBusinessTemplateEntityByTemplateId(templateId);
		// only templates where custom HTML is enabled are scanned, if need to scan all types then add improvement
		if (businessTemplateEntity == null || !CoreUtils.getBooleanValueFromInteger(businessTemplateEntity.getCustomHtmlEnabled())) {
			LOGGER.error("no valid custom html enabled template found with id {}", templateId);
			return null;
		}
		LOGGER.info("preparing genai payload for template id {}", templateId);
		return GenAIPhishingDetectionUtils.getGenAITemplatePhishingDetectionRequest(businessTemplateEntity);
	}
	
	private boolean validateMediaUrls(String type, EmailTemplateResponse request) {
	    if (!EmailTemplateTypes.PROMOTION.name().equalsIgnoreCase(type)) {
	        return true;
	    }

	    List<String> mediaUrls = request.getMediaUrls();
	    if (CollectionUtils.isEmpty(mediaUrls)) {
	        return true;
	    }

	    if (mediaUrls.size() > 1) {
	        throw new CampaignException(ErrorCodes.INVALID_REQUEST, "Only one attachment is allowed per email template.");
	    }

	    String fileType = PublicMediaUrlUtils.getFileExtensionFromUrl(mediaUrls.get(0));
	    if (!FileFormat.isValidFormat(fileType)) {
	        throw new CampaignException(ErrorCodes.INVALID_REQUEST, "Only PDF, JPEG, JPG, and PNG files are allowed.");
	    }

	    return true;
	}

	/**
	 * Gets the email template for free trial accounts
	 *
	 * @param enterpriseId The enterprise ID
	 * @param requestType The type of email template request
	 * @return EmailTemplateResponse containing the template data
	 */
	@Override
	public EmailTemplateResponse getFreeTrialEmailTemplate(Integer enterpriseId, String requestType) {
		EmailTemplateTypes type = EmailTemplateTypes.valueOf(StringUtils.upperCase(requestType));
		Integer emailTemplateId = businessEmailTemplateDao.findByEnterpriseIdAndTypeAndIsDefaultTemplate(enterpriseId, requestType, 1);
		
		switch (type) {
			case REVIEW_REQUEST_NEW:
				// Use template ID 0 for default template when no custom template exists
				return businessEmailTemplateDao.getRRTemplate(emailTemplateId != null ? emailTemplateId : 0, requestType);
			default:
				break;
		}
		
		LOGGER.info("No default template found for enterpriseId {} and type {}", enterpriseId, requestType);
		return null;
	}
	
}
