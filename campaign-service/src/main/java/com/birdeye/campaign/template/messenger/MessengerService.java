/**
 * 
 */
package com.birdeye.campaign.template.messenger;

import java.util.List;
import java.util.Map;

import com.birdeye.campaign.dto.BusinessTokenMap;
import com.birdeye.campaign.response.messenger.MessengerTemplatesResponse;

/**
 * <AUTHOR>
 *
 */
public interface MessengerService {
	
	/**
	 * Messenger api to fetch all sms templates for a given business and customer.
	 * 
	 * @param businessId
	 * @param customerId
	 * @return
	 */
	public Map<String, List<MessengerTemplatesResponse>> getTemplates(Integer businessId, Integer customerId);
	

	/**
	 * Fetch tokens for templates to be used in messenger
	 * 
	 * @param businessId
	 * @param customerId
	 * @param userId
	 * @return
	 */
	BusinessTokenMap getTokensMapForMessenger(Integer businessId, Integer customerId, Integer userId);
	
}
