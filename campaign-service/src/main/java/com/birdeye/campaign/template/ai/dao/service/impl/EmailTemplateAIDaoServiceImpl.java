package com.birdeye.campaign.template.ai.dao.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.dto.BusinessAITemplateEntity;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.entity.EmailTemplateAI;
import com.birdeye.campaign.repository.EmailTemplateAIRepo;
import com.birdeye.campaign.template.ai.dao.service.EmailTemplateAIDaoService;

@Service
public class EmailTemplateAIDaoServiceImpl implements EmailTemplateAIDaoService {
    
	@Autowired
	private EmailTemplateAIRepo	emailTemplateAIRepo;
	
	private static final Logger	LOGGER	= LoggerFactory.getLogger(EmailTemplateAIDaoServiceImpl.class);
    
    /**
     * This method is used to get the default email template AI by type.
     *
     * @param rrTemplateName the name of the template
     * @return GetEmailTemplateAIResponse containing the default email template AI
     */
    @Override
    public EmailTemplateAI getGlobalDefaultEmailTemplateAIByType(String type) {
        List<EmailTemplateAI> emailTemplateAIList = emailTemplateAIRepo.findGlobalDefaultTemplateByType(1, type);
        if (CollectionUtils.isNotEmpty(emailTemplateAIList)) {
            return emailTemplateAIList.get(0); // Return the first default template found
        }
        return null; // Return null if no default template is found
    }

    /**
     * This method is used to get the email template AI by its ID.
     *
     * @param aiTemplateId the ID of the email template AI
     * @return EmailTemplateAI object corresponding to the provided ID
     */
    @Override
    public EmailTemplateAI getEmailTemplateById(Integer aiTemplateId) {
        Optional<EmailTemplateAI> emailTemplateAI = emailTemplateAIRepo.findById(aiTemplateId);
        return emailTemplateAI.orElse(null);

    }
	
	@Override
	public EmailTemplateAI saveTemplate(EmailTemplateAI templateObject) {
		return emailTemplateAIRepo.saveAndFlush(templateObject);
	}
	
	@Override
	public Long getSameNameCountForTemplate(String name, Integer accountId) {
		return emailTemplateAIRepo.countByNameStartingWithAndAccountIdAndIsDeleted(name, accountId, 0);
	}
	
	@Override
	public Long getNameCountForTemplateUpdate(String baseName, String oldName, Integer accountId) {
		return emailTemplateAIRepo.countByNameStartingWithAndNameNotEqualsAndEnterpriseIdAndIsDeleted(baseName, oldName, accountId, 0);
	}
	
	/**
	 * This method is used to get the list of account templates(location templates excluded) by account ID.
	 *
	 * @param accountId
	 *            the ID of the account
	 * @return CachedCollectionWrapper containing BusinessAITemplateEntity objects
	 */
	@Override
//	@Cacheable(key = "#accountId?.toString()", value = "emailTemplatesAIListCache", condition = "#accountId != null", unless = "#result == null || #result.elementsList == null || #result.elementsList.isEmpty()")
	public CachedCollectionWrapper<BusinessAITemplateEntity> getAccountTemplatesListByAccountId(Integer accountId) {
		if (Objects.isNull(accountId)) {
			LOGGER.warn("Received null value for accountId");
			return new CachedCollectionWrapper<BusinessAITemplateEntity>(new ArrayList<BusinessAITemplateEntity>());
		}
		
		List<BusinessAITemplateEntity> emailTemplates = emailTemplateAIRepo.findAllActiveAccountTemplatesByAccountId(accountId);
		if (CollectionUtils.isEmpty(emailTemplates)) {
			emailTemplates = new ArrayList<BusinessAITemplateEntity>();
		}
		return new CachedCollectionWrapper<BusinessAITemplateEntity>(emailTemplates);
	}
	
	@Override
	public List<BusinessAITemplateEntity> getAIEmailTemplatesByType(Integer accountId, List<String> templateType) {
		if (Objects.isNull(accountId)) {
			LOGGER.warn("Received null value for accountId");
			return new ArrayList<BusinessAITemplateEntity>();
		}
		return emailTemplateAIRepo.findAllActiveAccountTemplatesByAccountIdAndType(accountId, templateType);
		
	}
}
