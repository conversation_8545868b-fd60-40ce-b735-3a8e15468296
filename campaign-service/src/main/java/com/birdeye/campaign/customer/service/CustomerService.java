package com.birdeye.campaign.customer.service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.request.CampaignAudienceFilter;
import com.birdeye.campaign.response.external.CheckinIdDetails;
import com.birdeye.campaign.response.external.CustomerInfoResponse;
import com.birdeye.campaign.response.kontacto.KontactoDTO;

/**
 * Service methods handling requests regarding @Customer
 * 
 * <AUTHOR>
 *
 */
public interface CustomerService {

	public void updateCustomerLastCommunicationTimeToCache(Integer customerId, String commType, String rrType, Date date,int ttl);
	public void updateCampaignLastCommunicationTimeToCaches(Integer customerId, String commType, String rrType, Date date, int ttl, KontactoDTO customer);
	public boolean validateCustomerCommunication(Integer customerId, String commType, String rrType, Integer mailResendFrequency,String businessTimeZone);
	public boolean isCustomerCommunicationRequestSent(Integer customerId, String commType, String rrType, Integer mailResendFrequency, String businessTimeZoneId, String campaignLevelRestriction, Boolean isAccountLevel, KontactoDTO customer);
	public void deleteCustomerLastCommunicationTimeCache(Integer customerId, String commType, String rrType);

	/**
	 * @param customerIds
	 * @return
	 */
	Map<Integer, CustomerInfoResponse> getCustomersMap(Set<Integer> customerIds);

	public KontactoDTO getCustomerById(Integer customerId);

	List<Integer> getCampaignAudienceCidsInBatch(CampaignAudienceFilter campaignAudienceFilter);

	public List<Integer> getCampaignAudienceCidsSyncScroll(CampaignAudienceFilter audienceFilterCampaign);

	Integer getCustomerLatestCheckinId(Integer customerId);

	List<CheckinIdDetails> getCustomersCheckInDetails(List<Integer> customerIds, boolean assistedBy, boolean extraParams);

	boolean validateDuplicateAppointmentCommunication(Integer customerId, String commType, String rrType, Integer appointmentId);

	public boolean validateDuplicateAppointmentCommunicationForPatient(Integer customerId, String commType, String requestType, Integer enterpriseId, Long requestId, Date scheduledDate, BusinessEnterpriseEntity business);

	boolean isCustomerSurveyCommunicationRequestSent(Integer customerId, String commType, String rrType, Integer mailResendFrequency, String businessTimeZoneId,
			String campaignLevelRestriction, Boolean isAccountLevel, KontactoDTO customer, Boolean isPerSurveyRequest, Integer surveyId, Integer overrideGlobalCommRestriction, Integer surveyRestrictionScope);
	
	void updateSurveyCampaignLastCommunicationTimeToCaches(String commType, String rrType, Date date, int ttl, KontactoDTO customer, Boolean isPerSurveyRequest,
			Integer surveyId);
}
