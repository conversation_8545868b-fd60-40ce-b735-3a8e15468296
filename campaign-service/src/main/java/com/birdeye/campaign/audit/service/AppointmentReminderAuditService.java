package com.birdeye.campaign.audit.service;

import java.util.List;

import com.birdeye.campaign.entity.AppointmentRRMapping;

public interface AppointmentReminderAuditService {
	
	void saveAppointmentRequestInfo(Long reviewRequestId, Integer appointmentId, Integer recallId, String requestType);
	
	AppointmentRRMapping getReminderRequestAuditByReviewRequestId(Long reviewRequestId);
	
	List<AppointmentRRMapping> getAppointmentRequestByRecallIdAndRequestType(Integer reviewRequestId, String requestType);
	
	List<AppointmentRRMapping> getAppointmentRequestByReviewRequestIds(List<Long> reviewRequestId);
	
	List<AppointmentRRMapping> getAppointmentRequestByAppointmentIds(List<Integer> appointmentIds);
}
