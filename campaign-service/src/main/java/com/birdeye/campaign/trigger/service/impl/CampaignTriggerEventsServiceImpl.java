package com.birdeye.campaign.trigger.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.appointment.service.AppointmentService;
import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.CampaignTriggerFiltersCache;
import com.birdeye.campaign.cache.CampaignTriggerTypeCache;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.constant.KafkaTopicTypeEnum;
import com.birdeye.campaign.dto.AppointmentInfoLiteDTO;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.CustomField;
import com.birdeye.campaign.dto.TriggerFilter;
import com.birdeye.campaign.entity.CampaignTriggerFilters;
import com.birdeye.campaign.entity.CampaignTriggerType;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.external.service.IContactExternalService;
import com.birdeye.campaign.kafka.service.KafkaService;
import com.birdeye.campaign.platform.constant.CampaignTriggerTypeEnum;
import com.birdeye.campaign.request.OngoingCampaignEventRequest;
import com.birdeye.campaign.request.Tag;
import com.birdeye.campaign.response.kontacto.KontactoCustomFieldDTO;
import com.birdeye.campaign.response.kontacto.KontactoDTO;
import com.birdeye.campaign.response.kontacto.KontactoTagDTO;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.trigger.service.CampaignTriggerEventsService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.PathNotFoundException;

@Service("campaignTriggerEventsService")
public class CampaignTriggerEventsServiceImpl implements CampaignTriggerEventsService {
	
	private static final Logger logger = LoggerFactory.getLogger(CampaignTriggerEventsServiceImpl.class);
	
	@Autowired
	@Qualifier("kafkaServiceV2")
	private KafkaService		kafkaService;
	
	@Autowired
	private IContactExternalService			contactExternalService;
	
	@Autowired
	private CacheService cacheService;
	
	@Value("${spring.profiles.active}")
	private String activeProfile;
	
	@Autowired
	private AppointmentService appointmentService;
	
	@Override
	public /* <T> */ void transformTriggerEventForExecution(Map<String, Object> requestBody) throws JsonProcessingException {
		
		logger.info("Transforming Trigger Event for execution : {}", requestBody);
		
		String triggerType = (String) requestBody.get("campaignTriggerType");
		
		OngoingCampaignEventRequest eventRequest = new OngoingCampaignEventRequest();
		eventRequest.setCustomerId((Integer) requestBody.get("customerId"));
		eventRequest.setTriggerType(triggerType);
		eventRequest.setTriggerFilters(new ArrayList<>());
		eventRequest.setCampaignId((Integer) requestBody.get("campaignId"));
		validateAndSetFieldsForQuickSendTriggerEvent(requestBody, eventRequest);
		if (requestBody.containsKey("appointmentId")) {
			eventRequest.setAppointmentId((Integer) requestBody.get("appointmentId"));
		}
		if (requestBody.containsKey("appointmentRecallId")) {
			eventRequest.setAppointmentRecallId((Integer) requestBody.get("appointmentRecallId"));
		}
		try {
			// adding custom fields to handle custom fields condition.
			setCustomFieldsAndTagsInEventRequest(eventRequest);
			
			setEnterpriseIdInEventRequest(eventRequest);
			
			setTriggersInEventRequest(getTriggerFiltersByTriggerType(triggerType), eventRequest, requestBody);
		} catch (CampaignException exp) {
			logger.error("campaignException while transforming event for appointmentId : {}, customerId : {}, triggerType : {} And Error : {}", eventRequest.getAppointmentId(),
					eventRequest.getCustomerId(), eventRequest.getTriggerType(), exp.getLocalizedMessage());
			if (ErrorCodes.INVALID_REQUEST.equals(exp.getCode())) {
				return;
			}
		}
		
		logger.info("Ongoing Event message transformed from trigger event : {}", eventRequest);
		if (StringUtils.equalsAnyIgnoreCase(triggerType, CampaignTriggerTypeEnum.BEFORE_APPOINTMENT_DATE.getType(), CampaignTriggerTypeEnum.APPOINTMENT_RECALL.getType(),
				CampaignTriggerTypeEnum.APPOINTMENT_BOOKED.getType(), CampaignTriggerTypeEnum.APPOINTMENT_CANCELED.getType(), CampaignTriggerTypeEnum.APPOINTMENT_MISSED.getType(),
				CampaignTriggerTypeEnum.APPOINTMENT_COMPLETED.getType())) {
			kafkaService.pushMessageToKafkaAcknowledged(KafkaTopicTypeEnum.CAMPAIGN_TRIGGER_EVENT_EXECUTION_APPOINTMENTS, null, eventRequest);
		} else {
			kafkaService.pushMessageToKafkaAcknowledged(KafkaTopicTypeEnum.CAMPAIGN_TRIGGER_EVENT_EXECUTION, null, eventRequest);
		}
		
	}

	private void validateAndSetFieldsForQuickSendTriggerEvent(Map<String, Object> requestBody, OngoingCampaignEventRequest eventRequest) {
		String source = StringUtils.EMPTY;
		if (requestBody.containsKey("source")) {
			source = ((String) requestBody.get("source"));
		}
		if (StringUtils.isBlank(source) || BooleanUtils.isFalse(StringUtils.equals(source, Constants.QUICK_SEND))) {
			return;
		}
		eventRequest.setSource(source);
		if (requestBody.containsKey("checkinId")) {
			eventRequest.setCheckinId((Integer) requestBody.get("checkinId"));
		}
		//checkin id check
		eventRequest.setRestrictReviewRequest(false);
		eventRequest.setBlocked(false);
	}

	// getting trigger filters from in-memory cache.
	private List<CampaignTriggerFilters> getTriggerFiltersByTriggerType(String triggerType) {
		
		CampaignTriggerTypeCache triggerTypeCache = CacheManager.getInstance().getCache(CampaignTriggerTypeCache.class);
		
		CampaignTriggerType campaignTriggerType = triggerTypeCache.getCampaignTriggerTypeByName(triggerType);
		
		CampaignTriggerFiltersCache triggerFiltersCache = CacheManager.getInstance().getCache(CampaignTriggerFiltersCache.class);
		
		return triggerFiltersCache.getCampaignTriggerFiltersByTriggerId(campaignTriggerType.getId());
	}

	@SuppressWarnings("unchecked")
	private void setTriggersInEventRequest(List<CampaignTriggerFilters> triggerFiltersByTriggerId, OngoingCampaignEventRequest eventRequest, Map<String, Object> requestBody)
			throws JsonProcessingException {
		
		String triggerType = (String) requestBody.get("campaignTriggerType");
		if(requestBody.get("appointment") == null && StringUtils.equalsIgnoreCase(triggerType, CampaignTriggerTypeEnum.APPOINTMENT_RECALL.getType())) {
			Integer accountId = (requestBody.get("accountId") != null) ? Integer.parseInt(requestBody.get("accountId").toString()) : null;
			AppointmentInfoLiteDTO appointmentInfo = appointmentService.getAppointmentLiteById(requestBody.get("appointmentId").toString(), accountId, true, false);
			if(appointmentInfo == null) {
				logger.info("[Trigger event transform] No response from appointment for appointmentId : {}", eventRequest.getAppointmentId());
				throw new CampaignException(ErrorCodes.INVALID_REQUEST, "No appointment response for " + eventRequest.getAppointmentId());
			}
			Object appointment = Configuration.defaultConfiguration().jsonProvider().parse(appointmentInfo.toString());
			
			requestBody.put("appointment", appointment);
		}
		
		String jsonStr = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).writeValueAsString(requestBody);
		// https://github.com/json-path/JsonPath#readme
		Object jsonDocument = Configuration.defaultConfiguration().jsonProvider().parse(jsonStr);
		
		for (CampaignTriggerFilters campaignTriggerFilter : triggerFiltersByTriggerId) {
			
			TriggerFilter triggerFilter = new TriggerFilter();
			triggerFilter.setName(campaignTriggerFilter.getFilterName());
			triggerFilter.setType(campaignTriggerFilter.getDataType());
			Object valueObj = null;
			try {
				valueObj = JsonPath.read(jsonDocument, campaignTriggerFilter.getFilterJsonpathExp());
			} catch (PathNotFoundException e) {
				logger.debug("PathNotFoundException occurred while reading filter {} from json ,exception {}", triggerFilter.getName(), ExceptionUtils.getMessage(e));
			} catch (Exception e) {
				logger.warn("Exception occurred while reading filter {} from json ,exception {}", triggerFilter.getName(), ExceptionUtils.getStackTrace(e));
			}
			triggerFilter.setValue(valueObj != null ? Arrays.asList(valueObj) : null);
			
			eventRequest.getTriggerFilters().add(triggerFilter);
			
		}
		List<Map<String, Object>> appointmentCustomFields = null;
		
		// Check if the trigger type is for appointment recall.
		if(StringUtils.equalsIgnoreCase(triggerType, CampaignTriggerTypeEnum.APPOINTMENT_RECALL.getType())) {
			Map<String, Object> appointment = (Map<String, Object>) requestBody.get("appointment");
			appointmentCustomFields = (appointment != null) ? (List<Map<String, Object>>) appointment.get("customFields") : 
				(List<Map<String, Object>>) requestBody.get("customFields");
			
		} else {
			// If the trigger type is not for appointment recall, simply retrieve the custom fields.
			appointmentCustomFields = (List<Map<String, Object>>) requestBody.get("customFields");
		}
		
		if (CollectionUtils.isEmpty(appointmentCustomFields)) return;
		
		for (Map<String, Object> appointmentCustomField : appointmentCustomFields) {
			TriggerFilter triggerFilter = new TriggerFilter();
			triggerFilter.setName((String) appointmentCustomField.get("fieldName"));
			triggerFilter.setType((String) appointmentCustomField.get("type"));
			triggerFilter.setValue(Arrays.asList(appointmentCustomField.get("value")));
			
			eventRequest.getTriggerFilters().add(triggerFilter);
		}
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<TriggerFilter> extractAndPopulateTriggerFilterValues(Map<String, Object> requestBody, String triggerType) throws JsonProcessingException {
		List<CampaignTriggerFilters> triggerFiltersByTriggerId = getTriggerFiltersByTriggerType(triggerType);
		List<TriggerFilter> triggerFilters = new ArrayList<>();
		String jsonStr = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).writeValueAsString(requestBody);
		// https://github.com/json-path/JsonPath#readme
		Object jsonDocument = Configuration.defaultConfiguration().jsonProvider().parse(jsonStr);
		
		for (CampaignTriggerFilters campaignTriggerFilter : triggerFiltersByTriggerId) {
			
			TriggerFilter triggerFilter = new TriggerFilter();
			triggerFilter.setName(campaignTriggerFilter.getFilterName());
			triggerFilter.setType(campaignTriggerFilter.getDataType());
			
			Object valueObj = JsonPath.read(jsonDocument, campaignTriggerFilter.getFilterJsonpathExp());
			
			// Class<T> referenceClassFromFilterDataType = getReferenceClassFromFilterDataType(campaignTriggerFilter.getDataType());
			
			triggerFilter.setValue(Arrays.asList(valueObj));
			triggerFilters.add(triggerFilter);
			
		}
		List<Map<String, Object>> appointmentCustomFields = null;
		
		if(StringUtils.equalsIgnoreCase(triggerType, CampaignTriggerTypeEnum.APPOINTMENT_RECALL.getType())) {
			Map<String, Object> appointment = (Map<String, Object>) requestBody.get("appointment");
			appointmentCustomFields = (appointment != null) ? (List<Map<String, Object>>) appointment.get("customFields") : 
				(List<Map<String, Object>>) requestBody.get("customFields");
			
		} else {
			// If the trigger type is not for appointment recall, simply retrieve the custom fields.
			appointmentCustomFields = (List<Map<String, Object>>) requestBody.get("customFields");
		}
		if (CollectionUtils.isNotEmpty(appointmentCustomFields)) {
			for (Map<String, Object> appointmentCustomField : appointmentCustomFields) {
				TriggerFilter triggerFilter = new TriggerFilter();
				triggerFilter.setName((String) appointmentCustomField.get("fieldName"));
				triggerFilter.setType((String) appointmentCustomField.get("type"));
				triggerFilter.setValue(Arrays.asList(appointmentCustomField.get("value")));
				
				triggerFilters.add(triggerFilter);
			}
		}
		return triggerFilters;
	}
	
	// adding custom fields to handle custom fields condition.
	private void setCustomFieldsAndTagsInEventRequest(OngoingCampaignEventRequest eventRequest) {
		
		KontactoDTO kontactoDTO = contactExternalService.getCustomerByIdWithCustomFieldAndReferralCodeAndTagsAndReviewSource(eventRequest.getCustomerId(), true, true, true, true);
		
		if (kontactoDTO == null) {
			logger.info("[Trigger event transform] No valid customer found for id : {}", eventRequest.getCustomerId());
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "No valid customer found for " + eventRequest.getCustomerId());
		}
		eventRequest.setBusinessId(kontactoDTO.getBusinessId());
		if (CollectionUtils.isNotEmpty(kontactoDTO.getCustomFieldValues())) {
			eventRequest.setCustomFields(new ArrayList<>());
			for (KontactoCustomFieldDTO customField : kontactoDTO.getCustomFieldValues()) {
				
				// BIRDEYE-98104 | Added handling since Contact service is sending currency value without '$' in check-in event, but with '$' in get customer API.
				if ("Currency".equalsIgnoreCase(customField.getType()) && StringUtils.startsWithAny(customField.getFieldValue(), "$")) {
					customField.setFieldValue(StringUtils.substringAfter(customField.getFieldValue(), "$"));
				}
				
				// BIRDEYE-98206 | Added handling since Contact service is sending long date in Checkin event but MM/dd/yyyy in get customer API.
				if ("Date".equalsIgnoreCase(customField.getType())) {
					try {
						SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
						Date date = sdf.parse(customField.getFieldValue());
						
						customField.setFieldValue(String.valueOf(date.getTime()));
					} catch (ParseException e) {
						logger.error("Date parse exception occured while transforming trigger event.");
						return;
					}
				}
				
				eventRequest.getCustomFields()
						.add(new CustomField(customField.getId(), customField.getFieldName(), Arrays.asList(customField.getFieldValue()), customField.getType()));
			}
		}
		if (CollectionUtils.isNotEmpty(kontactoDTO.getTags())) {
			eventRequest.setTags(new ArrayList<>());
			for (KontactoTagDTO kontactoTag : kontactoDTO.getTags()) {
				eventRequest.getTags().add(new Tag(kontactoTag.getId(), kontactoTag.getName(), kontactoTag.getValue()));
			}
		}
		
		// BIRD-32475 | Fetch and Add Review Written Sources For Given Customer, which will be used ahead in rule matching, if applicable
		logger.info("setCustomFieldsAndTagsInEventRequest :: Review source ids received from Kontacto service for customer id {} are {}", eventRequest.getCustomerId(), kontactoDTO.getReviewSourceIds());
		eventRequest.setReviewSourceIds(kontactoDTO.getReviewSourceIds());
		
	}
	
	private void setEnterpriseIdInEventRequest(OngoingCampaignEventRequest eventRequest) {
		BusinessEnterpriseEntity business = cacheService.getBusinessById(eventRequest.getBusinessId());
		if (business == null) {
			logger.warn("[Trigger event transform] No valid business found for id : {}", eventRequest.getCustomerId());
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "No valid business found for " + eventRequest.getBusinessId());
		}
		
		eventRequest.setEnterpriseId(business.getEnterpriseId() != null ? business.getEnterpriseId() : business.getId());
	}

//	private <T> Class<T> getReferenceClassFromFilterDataType(String dataType) {
//	
//	if ("text".equalsIgnoreCase(dataType)) {
//		return (Class<T>) String.class;
//	} else if ("number".equalsIgnoreCase(dataType)) {
//		return (Class<T>) Integer.class;
//	}
//	return (Class<T>) Object.class;
//}
//
//public static <T> T convertInstanceOfObject(Object o, Class<T> clazz) {
//	try {
//		return clazz.cast(o);
//	} catch (ClassCastException e) {
//		return null;
//	}
//}
	
	@Override
	public void testMethodToPushEventsToKafka(Map<String, Object> requestBody, String kafkaTopic, Integer count) {
		
		if (StringUtils.equalsAnyIgnoreCase(activeProfile, "demo", "qa")) {
			for (int i = 0; i < count; i++) {
				kafkaService.pushMessageToKafkaAcknowledged(kafkaTopic, null, requestBody);
			}
		}
	}
	
}
