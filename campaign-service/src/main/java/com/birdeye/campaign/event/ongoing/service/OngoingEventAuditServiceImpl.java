package com.birdeye.campaign.event.ongoing.service;

import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.constant.KafkaTopicTypeEnum;
import com.birdeye.campaign.dto.KafkaMessage;
import com.birdeye.campaign.entity.OngoingCampaignAudit;
import com.birdeye.campaign.enums.OngoingCampaignStatusEnum;
import com.birdeye.campaign.kafka.service.KafkaService;
import com.birdeye.campaign.ongoing.ruleengine.eventdata.OngoingCampaignEvent;
import com.birdeye.campaign.repository.OngoingCampaignAuditRepo;
import com.birdeye.campaign.request.OngoingCampaignEventRequest;

@Service("OngoingEventAuditService")
public class OngoingEventAuditServiceImpl implements OngoingEventAuditService {

	private static final Logger		logger	= LoggerFactory.getLogger(OngoingEventAuditServiceImpl.class);
	
	@Autowired
	private OngoingCampaignAuditRepo	ongoingCampaignAuditRepo;
	
	@Autowired
	private KafkaService				kafkaService;
	
	@Override
	public void createEventAudit(OngoingCampaignEvent eventData) {
//		logger.info("creating the ongoing event audit for data {}", eventData);
		OngoingCampaignAudit audit = prepareOngoingCampaignAudit(eventData.getEventRequest(), eventData.getEventStatus());
		ongoingCampaignAuditRepo.saveAndFlush(audit);
//		logger.info("event saved against event id {}", audit != null ? audit.getId() : null);
		if (audit != null) {
			eventData.setEventId(audit.getId());
		}
	}

	@Override
	public void updateEventAudit(OngoingCampaignEvent eventData) {
//		logger.info("updating the ongoing event audit for eventId {} and status :: {}", eventData.getEventId(), eventData.getEventStatus());
		Optional<OngoingCampaignAudit> auditOptional = ongoingCampaignAuditRepo.findById(eventData.getEventId());
		if (auditOptional.isPresent()) {
			OngoingCampaignAudit existingAudit = auditOptional.get();
			existingAudit.setStatus(eventData.getEventStatus());
			if (OngoingCampaignStatusEnum.COMPLETE.getStatus().equalsIgnoreCase(eventData.getEventStatus())) {
				existingAudit.setEvalCampaigns(eventData.getCampaignIdWithKafkaStatus());
			} else {
				existingAudit.setFailureReason(OngoingCampaignStatusEnum.getEnum(eventData.getEventStatus()).getLabel());
			}
			ongoingCampaignAuditRepo.save(existingAudit);
		} else {
			logger.error("For event id {} not audit data present", eventData.getEventId());
		}
	}
	
	private OngoingCampaignAudit prepareOngoingCampaignAudit(OngoingCampaignEventRequest eventRequest, String status) {
		if (eventRequest == null) {
			logger.error("event request {} can't be null", eventRequest);
			return null;
		}
		OngoingCampaignAudit audit = new OngoingCampaignAudit();
		audit.setStatus(status);
		audit.setCheckinId(eventRequest.getCheckinId());
		audit.setRestrictReviewRequest(isRestrictedReviewRequest(eventRequest.getRestrictReviewRequest()));
		// Utilizing existing RestrictReviewRequest flag.
		if (audit.getRestrictReviewRequest().equals(0)) {
			audit.setRestrictReviewRequest(isBlockedReviewRequest(eventRequest.getBlocked())); // BIRDEYE-83335
		}
		audit.setBusinessId(eventRequest.getBusinessId());
		audit.setEnterpriseId(eventRequest.getEnterpriseId());
		audit.setCustomerId(eventRequest.getCustomerId());
		audit.setCampaignId(eventRequest.getCampaignId());
		audit.setContactSource(eventRequest.getSource());
		if (CollectionUtils.isNotEmpty(eventRequest.getTags())) {
			audit.setTags(eventRequest.getTags());
		}
		if (CollectionUtils.isNotEmpty(eventRequest.getCustomFields())) {
			audit.setCustomFields(eventRequest.getCustomFields());
		}
		audit.setTriggerType(eventRequest.getTriggerType());
		audit.setTriggerFilters(eventRequest.getTriggerFilters());
		return audit;
	}

	private Integer isRestrictedReviewRequest(Boolean restrictedComm) {
		return (restrictedComm != null && restrictedComm) ? 1 : 0;
	}
	
	private Integer isBlockedReviewRequest(Boolean blockedComm) {
		return (blockedComm != null && blockedComm) ? 1 : 0;
	}

	/**
	 * Publish Ongoing Campaign Audit Events To Kafka.
	 * Asynchronously updates 'ongoing_campaign_audit'.
	 * 
	 * @param eventData
	 */
	@Override
	public void asyncUpdateEventAudit(OngoingCampaignEvent eventData) {
		if(Objects.isNull(eventData) || StringUtils.isBlank(eventData.getEventStatus())) {
			logger.warn("publishOngoingCampaignAuditEvent::OngoingEventAuditServiceImpl - Received blank event for ongoing campaign audit!");
			return;
		}
		
		kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.ONGOING_CAMPAIGN_AUDIT_ASYNC_UPDATE, new KafkaMessage(eventData));
	}

}
