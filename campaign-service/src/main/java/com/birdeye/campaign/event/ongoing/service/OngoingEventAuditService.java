package com.birdeye.campaign.event.ongoing.service;

import com.birdeye.campaign.ongoing.ruleengine.eventdata.OngoingCampaignEvent;

public interface OngoingEventAuditService {
	
	public void createEventAudit(OngoingCampaignEvent eventData);
	
	public void updateEventAudit(OngoingCampaignEvent eventData);
	
	/**
	 * Publish Ongoing Campaign Audit Events To Kafka.
	 * Asynchronously updates 'ongoing_campaign_audit'.
	 * 
	 * @param eventData
	 */
	public void asyncUpdateEventAudit(OngoingCampaignEvent eventData);
	
}
