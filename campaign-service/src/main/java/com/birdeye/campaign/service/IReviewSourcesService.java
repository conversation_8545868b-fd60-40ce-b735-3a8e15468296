/**
 * @file_name IReviewSourcesService.java
 * @created_date 21 Feb 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.service;

import java.util.List;

import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.service.impl.ReviewSourceWrapperSRO;
import com.birdeye.campaign.sro.AllReferralSourcesSRO;
import com.birdeye.campaign.sro.AllReviewSourcesSRO;
import com.birdeye.campaign.sro.ReviewSourceSRO;

/**
 * @file_name IReviewSourcesService.java
 * @created_date 21 Feb 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public interface IReviewSourcesService {

	/**
	 * @param businessId
	 * @param templateId
	 * @param deviceType
	 * @param allSources
	 * @return
	 */
	List<ReviewSourceSRO> getSelectedDeeplinkSourcesForATemplate(Integer businessId, Integer templateId, String deviceType, List<ReviewSourceSRO> allSources);

	/**
	 * @param businessId
	 * @param allSources
	 * @return
	 */
	List<ReviewSourceSRO> getDefaultDeeplinkSource(Integer businessId, List<ReviewSourceSRO> allSources);
	
	ReviewSourceSRO getDefaultDeeplinkSource(BusinessEnterpriseEntity business);

	AllReviewSourcesSRO getAllReviewSourcesSRO(Integer businessId, Integer templateId, String type);
	
	ReviewSourceWrapperSRO getSelectedDistinctReviewSources(BusinessEnterpriseEntity business, Integer templateId, String deviceType);

	List<ReviewSourceSRO> getAllReviewSources(Integer enterpriseId);

	/**
	 * @param businessId
	 * @param templateId
	 * @param type
	 * @return
	 */
	AllReferralSourcesSRO getAllReferraSources(Integer businessId, Integer templateId, String type);

	public List<ReviewSourceSRO> getExistingUniquePrioritiesForEnterprise(List<Integer> templateIds, String deviceType, Integer businessId, List<Integer> locationIds);

	/**
	 * Get Google/Facebook/Birdeye(Reseller) as default selected review source for templates created at business onboarding.
	 * 
	 * @param businessId
	 * @return
	 */
	List<ReviewSourceSRO> getDefaultReviewSourcesForOnboarding(Integer businessId);

	AllReviewSourcesSRO getAllReviewSourcesForResellerTemplate(Integer businessId, Integer templateId, String type, Integer resellerId);

//	ReviewSourceWrapperSRO getSelectedDistinctReviewSourcesForQR(BusinessEnterpriseEntity business, Integer templateId, String deviceType);

	ReviewSourceWrapperSRO getSelectedDistinctReviewSourcesForQR(BusinessEnterpriseEntity business, Integer templateId, String deviceType, Integer accountId);

	List<ReviewSourceSRO> getSelectedReviewSourcesForQRTemplate(Integer businessId, Integer templateId, String deviceType, List<ReviewSourceSRO> allSources);
	
}
