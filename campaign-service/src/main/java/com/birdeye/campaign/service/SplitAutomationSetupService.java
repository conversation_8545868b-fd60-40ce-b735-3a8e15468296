package com.birdeye.campaign.service;

import com.birdeye.campaign.dto.SplitCampaignAllListingResponse;
import com.birdeye.campaign.request.CampaignFilterRequest;
import com.birdeye.campaign.request.SplitAutomationRequest;
import com.birdeye.campaign.request.SplitAutomationViewDetailsRequest;
import com.birdeye.campaign.response.CreateCampaignResponse;
import com.birdeye.campaign.response.SplitAutomationEditResponse;
import com.birdeye.campaign.response.SplitAutomationViewDetailsResponse;

public interface SplitAutomationSetupService {
	
	/**
	 *
	 * Validate and fetch paginated split campaign list based upon filters.
	 *
	 * 
	 * @param accountId,
	 *            CampaignFilterRequest, userId
	 */
	SplitCampaignAllListingResponse getAllSplitAutomationForEnterprise(Integer accountId, CampaignFilterRequest campaignFilterRequest, Integer userId);
	
	CreateCampaignResponse saveOrUpdateSplitAutomation(SplitAutomationRequest request, Integer enterpriseId, Integer splitCampaignId, String userId) throws Exception;
	
	/**
	 * Method to prepare edit split automation request's data.
	 * 
	 * @param enterpriseId
	 * @param splitCampaignId
	 */
	public SplitAutomationEditResponse getEditSplitAutomation(Integer enterpriseId, Integer splitCampaignId, Integer userId);
	
	/**
	 * Method to prepare split automation view details request's response data
	 * 
	 * @param SplitAutomationViewDetailsRequest
	 *            - enterpriseId, splitCampaignId, campaignId
	 */
	public SplitAutomationViewDetailsResponse getSplitAutomation(SplitAutomationViewDetailsRequest request, Integer userId);
	
	/**
	 * Method to update the delete flag for a split campaign.
	 * 
	 * @param splitCampaignId
	 * @param deleteFlag
	 * @param enterpriseId 
	 * @param userId 
	 * @return
	 */
	public Boolean deleteSplitCampaign(Integer splitCampaignId, Integer deleteFlag, Integer enterpriseId, String userId);

	/**
	 *
	 * Update status for split Automation
	 *
	 * 
	 * @param splitCampaignId,
	 *            status
	 */
	Boolean updateStatusForSplitAutomation(Integer splitCampaignId, Integer status, String userId) throws Exception;

}
