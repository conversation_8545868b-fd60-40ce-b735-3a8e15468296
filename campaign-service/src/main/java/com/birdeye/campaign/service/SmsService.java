package com.birdeye.campaign.service;

import java.io.Serializable;
import java.util.Map;

import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.CampaignSMSProperties;
import com.birdeye.campaign.dto.SmsDto;

public interface SmsService {

	void sendSMS(SmsDto sms, Map<String, Serializable> params, boolean applyDnd, BusinessEnterpriseEntity business, boolean quickSend, boolean freeTrialProductSMS);

	void sendSMS(SmsDto sms, CampaignSMSProperties smsProperties);
	
}
