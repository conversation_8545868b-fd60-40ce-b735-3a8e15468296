package com.birdeye.campaign.service.dao.impl;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.entity.CampaignEntitiesChangeLog;
import com.birdeye.campaign.readonly.repository.CampaignEntitiesChangeLogReadOnlyRepo;
import com.birdeye.campaign.repository.CampaignEntitiesChangeLogRepo;
import com.birdeye.campaign.service.dao.CampaignEntitiesChangeLogDao;

@Service("campaignEntitiesChangeLogDao")
public class CampaignEntitiesChangeLogDaoImpl implements CampaignEntitiesChangeLogDao {
	
	private static final Logger				LOG	= LoggerFactory.getLogger(CampaignEntitiesChangeLogDaoImpl.class);
	
	@Autowired
	private CampaignEntitiesChangeLogRepo			changeLogRepo;
	
	@Autowired
	private CampaignEntitiesChangeLogReadOnlyRepo	changeLogReadOnlyRepo;
	
	/**
	 * Method to persist campaign modification user activity record.
	 * 
	 * @param campaignModificationUserActivity
	 */
	@Override
	public void saveCampaignEntitiesChangeLog(CampaignEntitiesChangeLog campaignModificationUserActivity) {
		if (Objects.isNull(campaignModificationUserActivity) || campaignModificationUserActivity.getEntityId() == null || StringUtils.isBlank(campaignModificationUserActivity.getEntityType())
				|| StringUtils.isBlank(campaignModificationUserActivity.getEvent())) {
			LOG.warn("saveCampaignEntitiesChangeLog - Blank values received for fields having not null constraint!");
			return;
		}
		
		changeLogRepo.save(campaignModificationUserActivity);
	}

	@Override
	@Cacheable(key = "T(String).valueOf(#entityId) + '-' + T(String).valueOf(#entityType) + T(String).valueOf(#category)", value = "modificationAuditCache", unless = "#result == null")
	public List<CampaignEntitiesChangeLog> findChangeLogsByIdAndType(Integer accountId, Integer entityId, String entityType, String category) {
		if (Objects.isNull(accountId) || Objects.isNull(entityId) || StringUtils.isBlank(entityType)) {
			LOG.warn("findChangeLogsByIdAndType - Received blank values for accountId or entityId or entityType");
			return null;
		}
		 List<CampaignEntitiesChangeLog> response = changeLogReadOnlyRepo.findByAccountIdAndEntityIdAndEntityTypeOrderByEventTimeDesc(accountId, entityId,
				StringUtils.isNotBlank(category) ? StringUtils.join(category, entityType) : entityType);
		 
		 return CollectionUtils.isEmpty(response)? null: response;
	}

	@Override
	public CampaignEntitiesChangeLog findLatestChangeLogByIdAndType(Integer accountId, Integer entityId, String entityType, String category) {
		if (Objects.isNull(accountId) || Objects.isNull(entityId) || StringUtils.isBlank(entityType)) {
			LOG.warn("findChangeLogsByIdAndType - Received blank values for accountId or entityId or entityType");
			return null;
		}
		Optional<CampaignEntitiesChangeLog> entityChangeLog = changeLogReadOnlyRepo.findFirstByAccountIdAndEntityIdAndEntityTypeOrderByEventTimeDesc(accountId, entityId,
				StringUtils.isNotBlank(category) ? StringUtils.join(category, entityType) : entityType);
		if (entityChangeLog.isPresent()) {
			return entityChangeLog.get();
		}
		return null;
	}
}
