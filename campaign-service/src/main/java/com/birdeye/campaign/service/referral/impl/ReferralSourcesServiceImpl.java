/**
 * @file_name ReferralSourcesServiceImpl.java
 * @created_date 8 Jul 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.service.referral.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.ReferralSourceCache;
import com.birdeye.campaign.cache.ReferralSourceMetadataCache;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.dto.TemplateSourceDto;
import com.birdeye.campaign.entity.BusinessReferralSourcePriority;
import com.birdeye.campaign.entity.ReferralSource;
import com.birdeye.campaign.entity.ReferralSourceMetadata;
import com.birdeye.campaign.repository.BusinessReferralSourcePriorityRepo;
import com.birdeye.campaign.service.referral.IReferralSourcesService;
import com.birdeye.campaign.sro.ReferralSourceExternalSRO;
import com.birdeye.campaign.sro.ReferralSourceExternalWrapperSRO;
import com.birdeye.campaign.sro.ReferralSourceSRO;
import com.birdeye.campaign.sro.ReferralSourceWrapperSRO;
import com.birdeye.campaign.utils.CampaignModificationAuditUtils;
import com.birdeye.campaign.utils.CoreUtils;

/**
 * @file_name ReferralSourcesServiceImpl.java
 * @created_date 8 Jul 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@Service("referralSourcesServiceImpl")
public class ReferralSourcesServiceImpl implements IReferralSourcesService {
	
	private static final Logger					logger	= LoggerFactory.getLogger(ReferralSourcesServiceImpl.class);
	
	@Autowired
	private BusinessReferralSourcePriorityRepo	brspRepo;
	
	@Override
	public ReferralSourceWrapperSRO getAllEnabledReferralSourcesByChannel(String channel) {
		ReferralSourceWrapperSRO wrapper = new ReferralSourceWrapperSRO();
		List<ReferralSourceSRO> sources = getAllEnabledReferralSourcesListByChannel(channel);
		wrapper.setSources(sources);
		return wrapper;
	}
	
	/**
	 * @param channel
	 * @return
	 */
	private List<ReferralSourceSRO> getAllEnabledReferralSourcesListByChannel(String channel) {
		Comparator<ReferralSourceMetadata> sortByDefaultPriority = getComparatorByDefaultPriorityAsc();
		return CacheManager.getInstance().getCache(ReferralSourceMetadataCache.class).getAllSources().stream()
				.filter(s -> StringUtils.equalsIgnoreCase(s.getSource(), channel)).sorted(sortByDefaultPriority).map(this::getReferralSourceSRO).collect(Collectors.toList());
	}
	
	@Override
	public ReferralSourceWrapperSRO getAllEnabledAndDefaultReferralSourcesByChannel(String channel) {
		ReferralSourceWrapperSRO wrapper = new ReferralSourceWrapperSRO();
		List<ReferralSourceSRO> sources = getAllEnabledAndDefaultReferralSourcesListByChannel(channel);
		wrapper.setSources(sources);
		return wrapper;
	}
	
	/**
	 * @param channel
	 * @return
	 */
	private List<ReferralSourceSRO> getAllEnabledAndDefaultReferralSourcesListByChannel(String channel) {
		Comparator<ReferralSourceMetadata> sortByDefaultPriority = getComparatorByDefaultPriorityAsc();
		return CacheManager.getInstance().getCache(ReferralSourceMetadataCache.class).getAllSources().stream()
				.filter(s -> StringUtils.equalsIgnoreCase(s.getSource(), channel)).filter(r -> CoreUtils.isTrueForInteger(r.getIsDefault())).sorted(sortByDefaultPriority)
				.map(this::getReferralSourceSRO).collect(Collectors.toList());
	}
	
	/**
	 * @return
	 */
	private Comparator<ReferralSourceMetadata> getComparatorByDefaultPriorityAsc() {
		return Comparator.comparing(ReferralSourceMetadata::getDefaultPriority);
	}
	
	/**
	 * @return
	 */
	private Comparator<BusinessReferralSourcePriority> getBusinessRefferalComparatorByPriorityAsc() {
		return Comparator.comparing(BusinessReferralSourcePriority::getPriorityOrder);
	}
	
	
	private ReferralSourceSRO getReferralSourceSRO(ReferralSourceMetadata source) {
		if (source == null) {
			return null;
		}
		ReferralSourceSRO rsSRO = new ReferralSourceSRO();
		rsSRO.setSourceId(source.getReferralSourceId());
		rsSRO.setSourceName(getReferralSourceName(source.getReferralSourceId()));
		rsSRO.setSourceAlias(source.getSourceAlias());
		rsSRO.setPriority(source.getDefaultPriority());
		rsSRO.setLogoUrl(source.getLogoUrl());
		rsSRO.setButtonColor(source.getButtonColor());
		rsSRO.setButtonTextColor(source.getButtonTextColor());
		rsSRO.setSourceUrl(source.getReferralSourceUrl());
		return rsSRO;
	}

	/**
	 * @param source
	 * @return
	 */
	private String getReferralSourceName(Integer sourceId) {
		return CacheManager.getInstance().getCache(ReferralSourceCache.class).getReferralSource(sourceId).getName();
	}
	
	@Override
	public List<ReferralSourceSRO> getSelectedReferralSourcesForATemplate(Integer businessId, Integer templateId, String deviceType) {
		logger.info("Getting selected referral sources for business id {} template {} and deviceType {}", businessId, templateId, deviceType);
		List<ReferralSourceSRO> allSources = getAllEnabledReferralSourcesListByChannel(deviceType);
		List<Integer> enabledSourceIds = allSources.stream().map(ReferralSourceSRO::getSourceId).collect(Collectors.toList());
		List<BusinessReferralSourcePriority> selectedSources = getSelectedBusinessReferralSources(businessId, templateId, deviceType);
		if (CollectionUtils.isEmpty(selectedSources)) {
			return getAllEnabledAndDefaultReferralSourcesListByChannel(deviceType);
		}
		return selectedSources.stream().filter(s -> isSourceEnabled(s.getSourceId(), enabledSourceIds)).sorted(getBusinessRefferalComparatorByPriorityAsc()).map(p -> getReferralSourceSRO(p))
				.collect(Collectors.toList());
	}

	/**
	 * @param businessId
	 * @param templateId
	 * @param deviceType
	 * @return
	 */
	private List<BusinessReferralSourcePriority> getSelectedBusinessReferralSources(Integer businessId, Integer templateId, String deviceType) {
		return brspRepo.findByBusinessIdAndTemplateIdAndDeviceType(businessId, templateId, deviceType);
	}
	
	private boolean isSourceEnabled(Integer sourceId, List<Integer> enabledSourceIds) {
		return enabledSourceIds.stream().anyMatch(e -> e.intValue() == sourceId.intValue());
	}
	
	private ReferralSourceSRO getReferralSourceSRO(BusinessReferralSourcePriority priority) {
		if (priority == null) {
			return null;
		}
		ReferralSourceSRO referralSourceSRO = new ReferralSourceSRO();
		referralSourceSRO.setSourceId(priority.getSourceId());
		referralSourceSRO.setSourceName(getReferralSourceName(priority.getSourceId()));
		referralSourceSRO.setPriority(priority.getPriorityOrder());
		Optional<ReferralSourceMetadata> optionalMeta = CacheManager.getInstance().getCache(ReferralSourceMetadataCache.class).getAllSources().stream()
				.filter(rSM -> rSM.getSource().equalsIgnoreCase(priority.getDeviceType()) && rSM.getReferralSourceId().equals(priority.getSourceId())).findFirst();
		if (optionalMeta.isPresent()) {
			ReferralSourceMetadata referralSourceMetadata = optionalMeta.get();
			referralSourceSRO.setSourceUrl(referralSourceMetadata.getReferralSourceUrl());
			referralSourceSRO.setLogoUrl(referralSourceMetadata.getLogoUrl());
			referralSourceSRO.setButtonColor(referralSourceMetadata.getButtonColor());
			referralSourceSRO.setButtonTextColor(referralSourceMetadata.getButtonTextColor());
			referralSourceSRO.setSourceAlias(StringUtils.isBlank(priority.getSourceAlias())? referralSourceMetadata.getSourceAlias(): priority.getSourceAlias());
		}
		return referralSourceSRO;
	}
	
	@Override
	public Map<String, List<TemplateSourceDto>> saveReferralSourcesForATemplate(Integer businessId, Integer templateId, String deviceType, List<ReferralSourceSRO> selectedSources) {
		List<ReferralSourceSRO> selectedSortedDeeplinkSource = selectedSources.stream().filter(source -> source.getPriority() != null).sorted(Comparator.comparing(ReferralSourceSRO::getPriority))
				.collect(Collectors.toList());
		List<BusinessReferralSourcePriority> oldDeeplinkSources = getSelectedBusinessReferralSources(businessId, templateId, deviceType);
		Map<Integer, BusinessReferralSourcePriority> priorityByPriorityOrder = oldDeeplinkSources.stream()
				.collect(Collectors.toMap(BusinessReferralSourcePriority::getPriorityOrder, priority -> priority, (first, second) -> second));
		
		Map<String, List<TemplateSourceDto>> oldNewDeeplinksMap = new HashMap<>();
		List<BusinessReferralSourcePriority> newBrspList = new ArrayList<>();
		oldNewDeeplinksMap.put(CampaignModificationAuditUtils.OLD_SELECTED_SOURCES, CampaignModificationAuditUtils.prepareDtoFromReferralSourcePriority(priorityByPriorityOrder));
		
		for (ReferralSourceSRO reviewSource : selectedSortedDeeplinkSource) {
			BusinessReferralSourcePriority priority = priorityByPriorityOrder.remove(reviewSource.getPriority());
			if (priority == null) {
				priority = new BusinessReferralSourcePriority();
				priority.setBusinessId(businessId);
				priority.setTemplateId(templateId);
				priority.setPriorityOrder(reviewSource.getPriority());
				priority.setDeviceType(deviceType);
				priority.setSourceId(reviewSource.getSourceId());
				priority.setSourceAlias(StringUtils.isBlank(reviewSource.getSourceAlias()) ? null : reviewSource.getSourceAlias());
			} else {
				priority.setSourceId(reviewSource.getSourceId());
				priority.setPriorityOrder(reviewSource.getPriority());
				priority.setDeviceType(deviceType);
				priority.setSourceAlias(StringUtils.isBlank(reviewSource.getSourceAlias()) ? null : reviewSource.getSourceAlias());
			}
			newBrspList.add(priority);
			brspRepo.saveAndFlush(priority);
			
		}
		if (MapUtils.isNotEmpty(priorityByPriorityOrder)) {
			for (BusinessReferralSourcePriority priority : priorityByPriorityOrder.values()) {
				brspRepo.delete(priority);
			}
		}
		oldNewDeeplinksMap.put(CampaignModificationAuditUtils.NEW_SELECTED_SOURCES, CampaignModificationAuditUtils.prepareDtoFromReferralSourcePriority(newBrspList));
		return oldNewDeeplinksMap;
	}
	
	@Override
	public void saveReferralSourcesForAResellerTemplate(Integer businessId, Integer templateId, String deviceType, List<ReferralSourceSRO> selectedSources) {
		List<ReferralSourceSRO> selectedSortedDeeplinkSource;
		if (CollectionUtils.isNotEmpty(selectedSources)) {
			selectedSortedDeeplinkSource = selectedSources.stream().filter(source -> source.getPriority() != null).sorted(Comparator.comparing(ReferralSourceSRO::getPriority))
					.collect(Collectors.toList());
		} else {
			selectedSortedDeeplinkSource = new ArrayList<>();
			ReferralSourceSRO referralSourceObject1 = new ReferralSourceSRO();
			referralSourceObject1.setPriority(Integer.parseInt(Constants.EMAIL_REFERRAL_SOURCE_PRIORITY));
			referralSourceObject1.setSourceId(Integer.parseInt(Constants.EMAIL_REFERRAL_SOURCE_ID));
			ReferralSourceSRO referralSourceObject2 = new ReferralSourceSRO();
			referralSourceObject2.setPriority(Integer.parseInt(Constants.TEXT_REFERRAL_SOURCE_PRIORITY));
			referralSourceObject2.setSourceId(Integer.parseInt(Constants.TEXT_REFERRAL_SOURCE_ID));
			ReferralSourceSRO referralSourceObject3 = new ReferralSourceSRO();
			referralSourceObject3.setPriority(Integer.parseInt(Constants.FACEBOOK_REFERRAL_SOURCE_PRIORITY));
			referralSourceObject3.setSourceId(Integer.parseInt(Constants.FACEBOOK_REFERRAL_SOURCE_ID));
			selectedSortedDeeplinkSource.add(referralSourceObject1);
			selectedSortedDeeplinkSource.add(referralSourceObject2);
			selectedSortedDeeplinkSource.add(referralSourceObject3);
		}
		
		for (ReferralSourceSRO reviewSource : selectedSortedDeeplinkSource) {
			BusinessReferralSourcePriority priority = new BusinessReferralSourcePriority();
			priority.setBusinessId(businessId);
			priority.setTemplateId(templateId);
			priority.setPriorityOrder(reviewSource.getPriority());
			priority.setDeviceType(deviceType);
			priority.setSourceId(reviewSource.getSourceId());
			priority.setSourceAlias(StringUtils.isBlank(reviewSource.getSourceAlias()) ? null : reviewSource.getSourceAlias());
			brspRepo.saveAndFlush(priority);
		}
	}

	@Override
	public Map<Integer, String> getAllReferralSources(Integer businessId) {
		return CacheManager.getInstance().getCache(ReferralSourceCache.class).getAllSources().stream()
				.collect(Collectors.toMap(ReferralSource::getId, ReferralSource::getName));
	}
	
	@Override
	public ReferralSourceExternalWrapperSRO getAllSources() {
		ReferralSourceExternalWrapperSRO wrapper = new ReferralSourceExternalWrapperSRO();
		List<ReferralSource> sources =  CacheManager.getInstance().getCache(ReferralSourceCache.class).getAllSources();
		List<ReferralSourceExternalSRO> referralSources = sources.stream().map(this::getReferralSourceResponse).collect(Collectors.toList());
		wrapper.setSources(referralSources);
		return wrapper;
	}
	
	private ReferralSourceExternalSRO getReferralSourceResponse(ReferralSource source) {
		ReferralSourceExternalSRO sro = new ReferralSourceExternalSRO();
		sro.setSourceId(source.getId());
		sro.setSourceName(source.getName());
		return sro;
	}
	
}
