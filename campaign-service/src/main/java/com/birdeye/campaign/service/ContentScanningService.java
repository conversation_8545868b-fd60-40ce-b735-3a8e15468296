/**
 * 
 */
package com.birdeye.campaign.service;

import com.birdeye.campaign.request.CreateMessengerCampaignRequest;
import com.birdeye.campaign.response.template.v2.EmailTemplateResponse;
import com.birdeye.campaign.sro.CampaignSmsTemplateSRO;

/**
 * <AUTHOR>
 *
 */
public interface ContentScanningService {

	/**
	 * 
	 * scan the SMS template and perform post suspicious detection actions
	 * 
	 * @param enterpriseId
	 * @param userId
	 * @param templateId
	 * @param campaignSmsTemplateSRO
	 * @return
	 */
	boolean isSuspiciousSMSTemplate(Integer enterpriseId, Integer userId, Integer templateId, CampaignSmsTemplateSRO campaignSmsTemplateSRO);

	/**
	 * 
	 * scan the Email template and perform post suspicious detection actions
	 * 
	 * @param request
	 * @param templateId
	 * @param businessId
	 * @param type
	 * @return
	 */
	boolean isSuspiciousEmailTemplate(EmailTemplateResponse request, Integer templateId, Integer businessId, String type);

	/***
	 * check free text template created via messenger campaign or campaign created from appointments tab
	 * mark suspicious if phishing content found
	 * only template_flagged_audit will be created
	 * support for BE-Assist is not available for these campaigns
	 */
	boolean isSuspiciousMessengerCampaignContent(Integer enterpriseId, Integer userId, Integer templateId, CreateMessengerCampaignRequest messengerRequest);
	
}
