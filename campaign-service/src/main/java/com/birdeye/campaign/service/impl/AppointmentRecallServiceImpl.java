package com.birdeye.campaign.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.appointment.service.AppointmentService;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.dto.AppointmentInfoLiteDTO;
import com.birdeye.campaign.dto.AppointmentRecallInfoLiteDTO;
import com.birdeye.campaign.dto.RecallDueDateInfo;
import com.birdeye.campaign.dto.RuleExpression;
import com.birdeye.campaign.dto.TriggerFilter;
import com.birdeye.campaign.entity.CampaignCondition;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.platform.constant.CampaignTriggerTypeEnum;
import com.birdeye.campaign.service.AppointmentRecallService;
import com.birdeye.campaign.service.CampaignExecutionService;
import com.birdeye.campaign.service.CampaignSetupCachingService;
import com.birdeye.campaign.trigger.service.CampaignTriggerEventsService;
import com.birdeye.campaign.utils.AppointmentRecallUtils;
import com.birdeye.campaign.utils.MvelEvaluationUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service("appointmentRecallService")
public class AppointmentRecallServiceImpl implements AppointmentRecallService, ApplicationContextAware, InitializingBean {
	
	public static final Logger			LOGGER			= LoggerFactory.getLogger(AppointmentRecallServiceImpl.class);
	
	@Autowired
	private AppointmentService			appointmentService;
	
	@Autowired
	private CampaignSetupCachingService	campaignSetupCachingService;
	
	private CampaignExecutionService	campaignExecutionService;
	
	private ApplicationContext			applicationContext;
	
	@Autowired
	private CampaignTriggerEventsService campaignTriggerEventsService;
	
	private static final ObjectMapper	objectMapper	= new ObjectMapper();
	
	@Override
	public void isValidRecallExecutionRequest(Integer recallId, Integer campaignId, Integer enterpriseId, Integer appointmentId, String businessTimezone, String triggerType) {
		CampaignCondition campaignCondition = campaignSetupCachingService.getCampaignConditionByCampaign(campaignId, enterpriseId);
		campaignCondition = new CampaignCondition(campaignCondition);
		
		AppointmentRecallInfoLiteDTO recallInfo = appointmentService.getAppointmentRecallInfoLiteByAppointmentId(appointmentId, enterpriseId);
		if (recallInfo == null || campaignCondition.getExecutionDateInfo() == null)
			throw new CampaignException(ErrorCodes.NO_VALID_DUE_DATE_FOUND, ErrorCodes.NO_VALID_DUE_DATE_FOUND.getMessage());
		
		// Check if request is NON-PMS request and if PMS support for recall is enabled
		if(recallInfo.getAppointmentRecallId() == null && BooleanUtils.isTrue(recallInfo.getRecallSupportOnPMS())) {
			throw new CampaignException(ErrorCodes.PMS_SUPPORT_IS_ENABLED, ErrorCodes.PMS_SUPPORT_IS_ENABLED.getMessage());
		}
		
		if (recallInfo.isApptRecallDeleted()) {
			Map<String, Object> recallInfoMap = objectMapper.convertValue(recallInfo, new TypeReference<Map<String, Object>>() {
			});
			recallInfoMap.put("campaignTriggerType", CampaignTriggerTypeEnum.APPOINTMENT_RECALL.getType());
			try {
				campaignExecutionService.dropScheduledRequests(recallInfoMap, false);
			} catch (Exception e) {
				LOGGER.warn("Exception occurred while dropping scheduled request for recall id {} , campaignId {} , enterpriseId {}", recallId, campaignId, enterpriseId);
			}
			throw new CampaignException(ErrorCodes.APPOINTMENT_RECALL_DELETED, ErrorCodes.APPOINTMENT_RECALL_DELETED.getMessage());
		}
		
		if (recallInfo.isFutureBooking())
			throw new CampaignException(ErrorCodes.FUTURE_APPOINTMENT_BOOKED, ErrorCodes.FUTURE_APPOINTMENT_BOOKED.getMessage());
		
		Long dueDateEpoch = AppointmentRecallUtils.getAppointmentRecallDueDate(campaignCondition.getExecutionDateInfo(), recallInfo);
		if (dueDateEpoch == null)
			throw new CampaignException(ErrorCodes.NO_VALID_DUE_DATE_FOUND, ErrorCodes.NO_VALID_DUE_DATE_FOUND.getMessage());
		
		AppointmentInfoLiteDTO appointmentInfo = appointmentService.getAppointmentLiteById(appointmentId.toString(), enterpriseId, true, false);
		if (appointmentInfo == null) {
			throw new CampaignException(ErrorCodes.NO_VALID_APOINTMENT_FOUND, ErrorCodes.NO_VALID_APOINTMENT_FOUND.getMessage());
		}
		recallInfo.setAppointment(appointmentInfo);
		
		if(!AppointmentRecallUtils.isValidAppointmentStatusForRecallExecution(appointmentInfo.getAppointmentStatus())) {
			LOGGER.info("Invalid status found for execution for recall {}, campaign {}", appointmentId, campaignId);
			throw new CampaignException(ErrorCodes.INVALID_APPOINTMENT_STATUS, ErrorCodes.INVALID_APPOINTMENT_STATUS.getMessage());
		}
		
		List<TriggerFilter> triggerFilters = getTriggerFilters(recallInfo, CampaignTriggerTypeEnum.APPOINTMENT_RECALL.getType());
		boolean pmsRecallRequest = AppointmentRecallUtils.isPMSRecallRequest(recallId);
		if (!MvelEvaluationUtils.evaluateTriggerMvelExpression(campaignCondition.getTriggerMvelExpression(), campaignCondition.getTriggerMvelParamsAndTypes(), triggerFilters, pmsRecallRequest, campaignCondition.getTriggerRuleExpression())) {
			throw new CampaignException(ErrorCodes.NO_VALID_CAMPAIGN_CONDITION, ErrorCodes.NO_VALID_CAMPAIGN_CONDITION.getMessage());
		}
		
		// check if current time is valid to execute request
		if (BooleanUtils.isFalse(AppointmentRecallUtils.isValidExecutionTimeRequest(dueDateEpoch, campaignId, campaignCondition.getAppointmentScheduleInfo(), businessTimezone, triggerType))) {
			throw new CampaignException(ErrorCodes.CAMPAIGN_SCHEDULING_CONFIG_NOT_MATCHED, ErrorCodes.CAMPAIGN_SCHEDULING_CONFIG_NOT_MATCHED.getMessage());
		}
	}
	
	@Override
	public Long getDueDateTimeForAppointmentRecall(RecallDueDateInfo recallDueDateInfo, Integer appointmentId, Integer enterpriseId) {		
		AppointmentRecallInfoLiteDTO recallInfo = appointmentService.getAppointmentRecallInfoLiteByAppointmentId(appointmentId, enterpriseId);
		if (recallInfo == null || recallDueDateInfo == null)
			return null;
		
		Long dueDate = AppointmentRecallUtils.getAppointmentRecallDueDate(recallDueDateInfo, recallInfo);
		return dueDate;
	}
	
	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		this.applicationContext = applicationContext;
	}
	
	@Override
	public void afterPropertiesSet() throws Exception {
		campaignExecutionService = applicationContext.getBean(CampaignExecutionService.class);
	}
	
	@SuppressWarnings("unchecked")
	public List<TriggerFilter> getTriggerFilters(AppointmentRecallInfoLiteDTO recallInfo, String triggerType) {
		List<TriggerFilter> triggerFilters = new ArrayList<>();
		try {
			triggerFilters = campaignTriggerEventsService.extractAndPopulateTriggerFilterValues(objectMapper.convertValue(recallInfo, Map.class),
					triggerType);
		} catch (JsonProcessingException e) {
			throw new CampaignException(ErrorCodes.NO_VALID_CAMPAIGN_CONDITION, ErrorCodes.NO_VALID_CAMPAIGN_CONDITION.getMessage());
		}
		return triggerFilters;
	}
	
}
