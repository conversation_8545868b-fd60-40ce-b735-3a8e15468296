package com.birdeye.campaign.service.impl;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.dto.AppointmentFormUsageReportResponse;
import com.birdeye.campaign.dto.AppointmentRecallUsageReportResponse;
import com.birdeye.campaign.dto.AppointmentReminderUsageReportResponse;
import com.birdeye.campaign.dto.CXUsageReportResponse;
import com.birdeye.campaign.dto.CommunicationUsageStatsMessage;
import com.birdeye.campaign.dto.CustomUsageReportResponse;
import com.birdeye.campaign.dto.RRUsageReportResponse;
import com.birdeye.campaign.dto.ReferralUsageReportResponse;
import com.birdeye.campaign.dto.UsageFunnelReportResponse;
import com.birdeye.campaign.executor.services.CampaignCallable;
import com.birdeye.campaign.executor.services.CampaignExecutorService;
import com.birdeye.campaign.executor.services.ExecutorCommonService;
import com.birdeye.campaign.platform.constant.CampaignTypeEnum;
import com.birdeye.campaign.report.ReportService;
import com.birdeye.campaign.report.UsageReportsService;
import com.birdeye.campaign.request.CampaignUsageInfo;
import com.birdeye.campaign.request.CampaignUsageInfo.AppointmentUsage;
import com.birdeye.campaign.request.CampaignsFilterCriteria;
import com.birdeye.campaign.service.CampaignUsageService;

@Service("campaignUsageService")
public class CampaignUsageServiceImpl implements CampaignUsageService {
	
	@Autowired
	private ReportService			reportService;
	
	@Autowired
	private UsageReportsService		usageReportsService;
	
	@Autowired
	@Qualifier(Constants.CAMPAIGN_REPORTING_TASK_EXECUTOR)
	private ThreadPoolTaskExecutor	threadPoolTaskExecutor;
	
	@Autowired
	private ExecutorCommonService	executorCommonService;
	
	private static final Logger		logger	= LoggerFactory.getLogger(CampaignUsageServiceImpl.class);
	
	@Override
	public void prepareCampaignUsageReport(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo, String campaignType) {
		
		if (campaignType.equalsIgnoreCase(CampaignTypeEnum.CX_REQUEST.getType())) {
			prepareCXViewDetailData(filterCriteria, usageInfo);
		}
		
		else if (campaignType.equalsIgnoreCase(CampaignTypeEnum.REVIEW_REQUEST.getType())) {
			prepareRRViewDetailData(filterCriteria, usageInfo);
		}
		
		else if (campaignType.equalsIgnoreCase(CampaignTypeEnum.REFERRAL.getType())) {
			prepareReferralViewDetailData(filterCriteria, usageInfo);
		}
		
		else if (campaignType.equalsIgnoreCase(CampaignTypeEnum.SURVEY_REQUEST.getType())) {
			prepareSurveyViewDetailData(filterCriteria, usageInfo);
		}
		
		else if (campaignType.equalsIgnoreCase(CampaignTypeEnum.PROMOTIONAL.getType())) {
			preparePromotionViewDetailData(filterCriteria, usageInfo);
		}
		
		else if (campaignType.equalsIgnoreCase(CampaignTypeEnum.APPOINTMENT_REMINDER.getType())) {
			prepareReminderViewDetailData(filterCriteria, usageInfo);
		}
		
		else if (campaignType.equalsIgnoreCase(CampaignTypeEnum.APPOINTMENT_RECALL.getType())) {
			prepareRecallViewDetailData(filterCriteria, usageInfo);
		}
		
		else if (campaignType.equalsIgnoreCase(CampaignTypeEnum.APPOINTMENT_FORM.getType())) {
			prepareFormViewDetailData(filterCriteria, usageInfo);
		}
	}
	
	private void prepareFormViewDetailData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
		executorService.submit(prepareFormUsageInfoData(filterCriteria, usageInfo));
		executorService.submit(prepareFormUniqueRecipientsAndEmailDeliverabilityData(filterCriteria, usageInfo));
		
		try {
			executorCommonService.executeTasks(executorService);
		} catch (Exception exception) {
			logger.error("Exception occurred while executing Appointment-Form campaign usage info and unique recipients, email-deliverability stats fetch tasks for enterprise : {} and reason : {}",
					filterCriteria.getEnterpriseId(), exception);
		}
	}
	
	private CampaignCallable<Boolean> prepareFormUsageInfoData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		return new CampaignCallable<Boolean>("prepareFormUsageInfoTask") {
			@Override
			public Boolean doCall() {
				UsageFunnelReportResponse usageFunnelReportResponse = usageReportsService.getAppointmentFormUsageGraphReport(filterCriteria);
				@SuppressWarnings("unchecked")
				List<AppointmentFormUsageReportResponse> usageResponseList = (List<AppointmentFormUsageReportResponse>) usageFunnelReportResponse.getUsageReportResponses();
				setAppointmentFormCampaignUsageStats(usageResponseList.get(0), usageInfo);
				return true;
			}
		};
	}
	
	private CampaignCallable<Boolean> prepareFormUniqueRecipientsAndEmailDeliverabilityData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		return new CampaignCallable<Boolean>("prepareFormUniqueRecipientsTask") {
			@Override
			public Boolean doCall() {
				usageReportsService.getAppointmentFormRecipientUsageAndDeliveryStats(filterCriteria, usageInfo);
				return true;
			}
		};
	}
	
	private void prepareRecallViewDetailData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
		executorService.submit(prepareRecallUsageInfoData(filterCriteria, usageInfo));
		executorService.submit(prepareRecallUniqueRecipientsAndEmailDeliverabilityData(filterCriteria, usageInfo));
		
		try {
			executorCommonService.executeTasks(executorService);
		} catch (Exception exception) {
			logger.error("Exception occurred while executing Appointment-Recall campaign usage info and unique recipients, email deliverability stats fetch tasks for enterprise : {} and reason : {}",
					filterCriteria.getEnterpriseId(), exception);
		}
	}
	
	private CampaignCallable<Boolean> prepareRecallUniqueRecipientsAndEmailDeliverabilityData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		return new CampaignCallable<Boolean>("prepareRecallUniqueRecipientsTask") {
			@Override
			public Boolean doCall() {
				usageReportsService.getAppointmentRecallRecipientUsageAndDeliveryStats(filterCriteria, usageInfo);
				return true;
			}
		};
	}
	
	private CampaignCallable<Boolean> prepareRecallUsageInfoData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		return new CampaignCallable<Boolean>("prepareRecallUsageInfoTask") {
			@Override
			public Boolean doCall() {
				UsageFunnelReportResponse usageFunnelReportResponse = usageReportsService.getAppointmentRecallUsageGraphReport(filterCriteria);
				@SuppressWarnings("unchecked")
				List<AppointmentRecallUsageReportResponse> usageResponseList = (List<AppointmentRecallUsageReportResponse>) usageFunnelReportResponse.getUsageReportResponses();
				setAppointmentRecallCampaignUsageStats(usageResponseList.get(0), usageInfo);
				return true;
			}
		};
	}
	
	private void prepareReminderViewDetailData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
		executorService.submit(prepareReminderUsageInfoData(filterCriteria, usageInfo));
		executorService.submit(prepareReminderUniqueRecipientsAndEmailDeliverabilityData(filterCriteria, usageInfo));
		
		try {
			executorCommonService.executeTasks(executorService);
		} catch (Exception exception) {
			logger.error("Exception occurred while executing Appointment-Reminder campaign usage info and unique recipients, email-deliverability stats fetch tasks for enterprise : {} and reason : {}",
					filterCriteria.getEnterpriseId(), exception);
		}
	}
	
	private CampaignCallable<Boolean> prepareReminderUniqueRecipientsAndEmailDeliverabilityData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		return new CampaignCallable<Boolean>("prepareReminderUniqueRecipientsTask") {
			@Override
			public Boolean doCall() {
				usageReportsService.getAppointmentReminderRecipientUsageAndDeliveryStats(filterCriteria, usageInfo);
				return true;
			}
		};
	}
	
	private CampaignCallable<Boolean> prepareReminderUsageInfoData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		return new CampaignCallable<Boolean>("prepareReminderUsageInfoTask") {
			@Override
			public Boolean doCall() {
				UsageFunnelReportResponse usageFunnelReportResponse = usageReportsService.getAppointmentReminderUsageGraphReport(filterCriteria);
				@SuppressWarnings("unchecked")
				List<AppointmentReminderUsageReportResponse> usageResponseList = (List<AppointmentReminderUsageReportResponse>) usageFunnelReportResponse.getUsageReportResponses();
				setAppointmentCampaignUsageStats(usageResponseList.get(0), usageInfo);
				return true;
			}
		};
	}
	
	private void preparePromotionViewDetailData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
		executorService.submit(preparePromotionUsageInfoData(filterCriteria, usageInfo));
		executorService.submit(preparePromotionUniqueRecipientsAndEmailDeliverabilityData(filterCriteria, usageInfo));
		
		try {
			executorCommonService.executeTasks(executorService);
		} catch (Exception exception) {
			logger.error("Exception occurred while executing Promotion campaign usage info and unique recipients, email-deliverability stats fetch tasks for enterprise : {} and reason : {}",
					filterCriteria.getEnterpriseId(), exception);
		}
	}
	
	private CampaignCallable<Boolean> preparePromotionUsageInfoData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		return new CampaignCallable<Boolean>("preparePromotionUsageInfoTask") {
			@Override
			public Boolean doCall() {
				UsageFunnelReportResponse usageFunnelReportResponse = usageReportsService.getCustomUsageGraphReport(filterCriteria);
				@SuppressWarnings("unchecked")
				List<CustomUsageReportResponse> usageResponseList = (List<CustomUsageReportResponse>) usageFunnelReportResponse.getUsageReportResponses();
				setPromotionalCampaignUsageStats(usageResponseList.get(0), usageInfo);
				return true;
			}
		};
	}
	
	private CampaignCallable<Boolean> preparePromotionUniqueRecipientsAndEmailDeliverabilityData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		return new CampaignCallable<Boolean>("preparePromotionUniqueRecipientsTask") {
			@Override
			public Boolean doCall() {
				usageReportsService.getCustomRecipientUsageAndDeliveryStats(filterCriteria, usageInfo);
				return true;
			}
		};
	}
	
	private void prepareSurveyViewDetailData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
		executorService.submit(prepareSurveyUsageInfoData(filterCriteria, usageInfo));
		executorService.submit(prepareSurveyUniqueRecipientsAndEmailDeliverabilityData(filterCriteria, usageInfo));
		
		try {
			executorCommonService.executeTasks(executorService);
		} catch (Exception exception) {
			logger.error("Exception occurred while executing Survey campaign usage info and unique recipients, email-deliverablity stats fetch tasks for enterprise : {} and reason : {}",
					filterCriteria.getEnterpriseId(), exception);
		}
	}
	
	private CampaignCallable<Boolean> prepareSurveyUniqueRecipientsAndEmailDeliverabilityData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		return new CampaignCallable<Boolean>("prepareSurveyUniqueRecipientsTask") {
			@Override
			public Boolean doCall() {
				reportService.getSurveyRecipientUsageAndDeliveryStats(filterCriteria.getEnterpriseId(), Arrays.asList(filterCriteria.getCampaignId()), usageInfo);
				return true;
			}
		};
	}
	
	private CampaignCallable<Boolean> prepareSurveyUsageInfoData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		return new CampaignCallable<Boolean>("prepareSurveyUsageInfoTask") {
			@Override
			public Boolean doCall() {
				Map<Integer, CommunicationUsageStatsMessage> campaignIdToUsageMap = new HashMap<>();
				reportService.getSurveyCampaignUsage(filterCriteria.getEnterpriseId(), Arrays.asList(filterCriteria.getCampaignId()), campaignIdToUsageMap);
				if (campaignIdToUsageMap.containsKey(filterCriteria.getCampaignId())) {
					setSurveyCampaignUsageStats(campaignIdToUsageMap.get(filterCriteria.getCampaignId()), usageInfo);
				}
				return true;
			}
		};
	}
	
	private void prepareReferralViewDetailData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
		executorService.submit(prepareReferralUsageInfoData(filterCriteria, usageInfo));
		executorService.submit(prepareReferralUniqueRecipientsAndEmailDeliverabilityData(filterCriteria, usageInfo));
		
		try {
			executorCommonService.executeTasks(executorService);
		} catch (Exception exception) {
			logger.error("Exception occurred while executing Referral campaign usage info and unique recipients, email-deliverability stats fetch tasks for enterprise : {} and reason : {}",
					filterCriteria.getEnterpriseId(), exception);
		}
	}
	
	private CampaignCallable<Boolean> prepareReferralUniqueRecipientsAndEmailDeliverabilityData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		return new CampaignCallable<Boolean>("prepareReferralUniqueRecipientsTask") {
			@Override
			public Boolean doCall() {
				usageReportsService.getReferralRecipientUsageAndDeliveryStats(filterCriteria, usageInfo);
				return true;
			}
		};
	}
	
	private CampaignCallable<Boolean> prepareReferralUsageInfoData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		return new CampaignCallable<Boolean>("prepareReferralUsageInfoTask") {
			@Override
			public Boolean doCall() {
				UsageFunnelReportResponse usageFunnelReportResponse = usageReportsService.getReferralUsageGraphReport(filterCriteria);
				@SuppressWarnings("unchecked")
				List<ReferralUsageReportResponse> usageResponseList = (List<ReferralUsageReportResponse>) usageFunnelReportResponse.getUsageReportResponses();
				setReferralCampaignUsageStats(usageResponseList.get(0), usageInfo);
				return true;
			}
		};
	}
	
	private void prepareRRViewDetailData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
	    executorService.submit(prepareRRUsageInfoData(filterCriteria, usageInfo));
	    executorService.submit(prepareRRUniqueRecipients(filterCriteria, usageInfo));
	    executorService.submit(prepareRRDeliverabilityStats(filterCriteria, usageInfo));
	    
	    try {
	      executorCommonService.executeTasks(executorService);
	    } catch (Exception exception) {
			logger.error("Exception occurred while executing RR campaign usage info and unique recipients, email-deliverability stats fetch tasks for enterprise : {} and reason : {}",
					filterCriteria.getEnterpriseId(), exception);
	    }
	}

	private void prepareCXViewDetailData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
		executorService.submit(prepareCXUsageInfoData(filterCriteria, usageInfo));
		executorService.submit(prepareCXUniqueRecipientsAndEmailDeliverabilityData(filterCriteria, usageInfo));
		
		try {
			executorCommonService.executeTasks(executorService);
		} catch (Exception exception) {
			logger.error("Exception occurred while executing CX campaign usage info and unique recipients, email-deliverability stats fetch tasks for enterprise : {} and reason : {}",
					filterCriteria.getEnterpriseId(), exception);
		}
	}
	
	private CampaignCallable<Boolean> prepareRRDeliverabilityStats(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		return new CampaignCallable<Boolean>("prepareRRDeliverabilityStatsTask") {
			@Override
			public Boolean doCall() {
				usageReportsService.getRRDeliverabilityStats(filterCriteria, usageInfo);
				return true;
			}
		};
	}
	
	private CampaignCallable<Boolean> prepareRRUniqueRecipients(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		return new CampaignCallable<Boolean>("prepareRRUniqueRecipientsTask") {
			@Override
			public Boolean doCall() {
				usageReportsService.getRRUniqueRecipients(filterCriteria, usageInfo);
				return true;
			}
		};
		
	}
	
	private CampaignCallable<Boolean> prepareRRUsageInfoData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		return new CampaignCallable<Boolean>("prepareRRUsageInfoTask") {
			@Override
			public Boolean doCall() {
				UsageFunnelReportResponse usageFunnelReportResponse = usageReportsService.getRRCampaignReportForViewDetail(filterCriteria);
				@SuppressWarnings("unchecked")
				List<RRUsageReportResponse> usageResponseList = (List<RRUsageReportResponse>) usageFunnelReportResponse.getUsageReportResponses();
				setRRCampaignUsageStats(usageResponseList.get(0), usageInfo);
				return true;
			}
		};
		
	}
	
	private CampaignCallable<Boolean> prepareCXUniqueRecipientsAndEmailDeliverabilityData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		return new CampaignCallable<Boolean>("prepareCXUniqueRecipientsTask") {
			@Override
			public Boolean doCall() {
				usageReportsService.getCXRecipientUsageAndDeliveryStats(filterCriteria, usageInfo);
				return true;
			}
		};
		
	}
	
	private CampaignCallable<Boolean> prepareCXUsageInfoData(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo) {
		return new CampaignCallable<Boolean>("prepareCXUsageInfoTask") {
			@Override
			public Boolean doCall() {
				UsageFunnelReportResponse usageFunnelReportResponse = usageReportsService.getCXUsageGraphReport(filterCriteria);
				@SuppressWarnings("unchecked")
				List<CXUsageReportResponse> usageResponseList = (List<CXUsageReportResponse>) usageFunnelReportResponse.getUsageReportResponses();
				setCxCampaignUsageStats(usageResponseList.get(0), usageInfo);
				return true;
			}
		};
	}
	
	private void setSurveyCampaignUsageStats(CommunicationUsageStatsMessage communicationUsageStatsMessage, CampaignUsageInfo usageInfo) {
		usageInfo.setSent(communicationUsageStatsMessage.getSentSuccess());
		usageInfo.setDelivered(communicationUsageStatsMessage.getDelivered());
		usageInfo.setOpened(communicationUsageStatsMessage.getOpenStartedCount());
		usageInfo.setClicked(communicationUsageStatsMessage.getClickedCompletedCount());
	}
	
	private void setReferralCampaignUsageStats(ReferralUsageReportResponse referralUsageReportResponse, CampaignUsageInfo usageInfo) {
		if (referralUsageReportResponse != null) {
			Long sent = referralUsageReportResponse.getSentInfo() != null ? referralUsageReportResponse.getSentInfo().getTotal() : 0;
			usageInfo.setSent(sent);
			
			Long delivered = referralUsageReportResponse.getDeliveredInfo() != null ? referralUsageReportResponse.getDeliveredInfo().getTotal() : 0;
			usageInfo.setDelivered(delivered);
			
			Long opened = referralUsageReportResponse.getOpenInfo() != null ? referralUsageReportResponse.getOpenInfo().getTotal() : 0;
			usageInfo.setOpened(opened);
			
			Long clicked = referralUsageReportResponse.getSharedInfo() != null ? referralUsageReportResponse.getSharedInfo().getTotal() : 0;
			usageInfo.setClicked(clicked);
			
			double leads = referralUsageReportResponse.getReferralInfo() != null ? referralUsageReportResponse.getReferralInfo().getTotal() : 0;
			usageInfo.setLeads(leads);
		}
	}
	
	private void setCxCampaignUsageStats(CXUsageReportResponse cxUsageReportResponse, CampaignUsageInfo usageInfo) {
		if (cxUsageReportResponse != null) {
			Long sent = cxUsageReportResponse.getSentInfo() != null ? cxUsageReportResponse.getSentInfo().getTotal() : 0;
			usageInfo.setSent(sent);
			
			Long delivered = cxUsageReportResponse.getDeliveredInfo() != null ? cxUsageReportResponse.getDeliveredInfo().getTotal() : 0;
			usageInfo.setDelivered(delivered);
			
			Long opened = cxUsageReportResponse.getOpenInfo() != null ? cxUsageReportResponse.getOpenInfo().getTotal() : 0;
			usageInfo.setOpened(opened);
			
			Long clicked = cxUsageReportResponse.getSentimentClickInfo() != null ? cxUsageReportResponse.getSentimentClickInfo().getTotal() : 0;
			usageInfo.setClicked(clicked);
			
			Long reviewClicks = cxUsageReportResponse.getClickInfo() != null ? cxUsageReportResponse.getClickInfo().getTotal() : 0;
			usageInfo.setReviewClicks(reviewClicks);
		}
		
	}
	
	private void setPromotionalCampaignUsageStats(CustomUsageReportResponse customUsageReportResponse, CampaignUsageInfo usageInfo) {
		if (customUsageReportResponse != null) {
			Long totalSent = customUsageReportResponse.getSentInfo() != null ? customUsageReportResponse.getSentInfo().getTotal() : 0;
			usageInfo.setSent(totalSent);
			
			Long delivered = customUsageReportResponse.getDeliveredInfo() != null ? customUsageReportResponse.getDeliveredInfo().getTotal() : 0;
			usageInfo.setDelivered(delivered);
			
			Long opened = customUsageReportResponse.getOpenInfo() != null ? customUsageReportResponse.getOpenInfo().getTotal() : 0;
			usageInfo.setOpened(opened);
		}
	}
	
	private void setRRCampaignUsageStats(RRUsageReportResponse rrUsageReportResponse, CampaignUsageInfo usageInfo) {
		if (rrUsageReportResponse != null) {
			Long sent = rrUsageReportResponse.getSentInfo() != null ? rrUsageReportResponse.getSentInfo().getTotal() : 0;
			usageInfo.setSent(sent);
			
			Long delivered = rrUsageReportResponse.getDeliveredInfo() != null ? rrUsageReportResponse.getDeliveredInfo().getTotal() : 0;
			usageInfo.setDelivered(delivered);
			
			Long opened = rrUsageReportResponse.getOpenInfo() != null ? rrUsageReportResponse.getOpenInfo().getTotal() : 0;
			usageInfo.setOpened(opened);
			
			Long clicked = rrUsageReportResponse.getClickInfo() != null ? rrUsageReportResponse.getClickInfo().getTotal() : 0;
			usageInfo.setClicked(clicked);
		}
	}
	
	private void setAppointmentCampaignUsageStats(AppointmentReminderUsageReportResponse appointmentReminderUsageReportResponse, CampaignUsageInfo usageInfo) {
		if (appointmentReminderUsageReportResponse != null) {
			AppointmentUsage aappointmentUsageInfo = new AppointmentUsage();
			Long sent = appointmentReminderUsageReportResponse.getSentInfo() != null ? appointmentReminderUsageReportResponse.getSentInfo().getTotal() : 0;
			usageInfo.setSent(sent);
			
			Long delivered = appointmentReminderUsageReportResponse.getDeliveredInfo() != null ? appointmentReminderUsageReportResponse.getDeliveredInfo().getTotal() : 0;
			usageInfo.setDelivered(delivered);
			
			Long opened = appointmentReminderUsageReportResponse.getOpenInfo() != null ? appointmentReminderUsageReportResponse.getOpenInfo().getTotal() : 0;
			usageInfo.setOpened(opened);
			
			Long rescheduleClicks = appointmentReminderUsageReportResponse.getRescheduleClickInfo() != null
					? (long) appointmentReminderUsageReportResponse.getRescheduleClickInfo().getTotal()
					: 0l;
			aappointmentUsageInfo.setRescheduleClicks(rescheduleClicks);
			
			Long confirmedClicks = appointmentReminderUsageReportResponse.getConfirmationClickInfo() != null
					? (long) appointmentReminderUsageReportResponse.getConfirmationClickInfo().getTotal()
					: 0l;
			aappointmentUsageInfo.setConfirmedClicks(confirmedClicks);
			
			Long canceledClicks = appointmentReminderUsageReportResponse.getCancelClickInfo() != null
					? (long) appointmentReminderUsageReportResponse.getCancelClickInfo().getTotal()
					: 0l;
			aappointmentUsageInfo.setCancelledClicks(canceledClicks);
			
			usageInfo.setAppointmentUsage(aappointmentUsageInfo);
		}
	}
	
	private void setAppointmentRecallCampaignUsageStats(AppointmentRecallUsageReportResponse appointmentRecallUsageReportResponse, CampaignUsageInfo usageInfo) {
		if (appointmentRecallUsageReportResponse != null) {
			Long sent = appointmentRecallUsageReportResponse.getSentInfo() != null ? appointmentRecallUsageReportResponse.getSentInfo().getTotal() : 0;
			usageInfo.setSent(sent);
			
			Long delivered = appointmentRecallUsageReportResponse.getDeliveredInfo() != null ? appointmentRecallUsageReportResponse.getDeliveredInfo().getTotal() : 0;
			usageInfo.setDelivered(delivered);
			
			Long opened = appointmentRecallUsageReportResponse.getOpenInfo() != null ? appointmentRecallUsageReportResponse.getOpenInfo().getTotal() : 0;
			usageInfo.setOpened(opened);
			
			Long clicked = appointmentRecallUsageReportResponse.getClickInfo() != null ? appointmentRecallUsageReportResponse.getClickInfo().getTotal() : 0;
			usageInfo.setClicked(clicked);
			
			Long booked = appointmentRecallUsageReportResponse.getBookedInfo() != null ? appointmentRecallUsageReportResponse.getBookedInfo().getTotal() : 0;
			usageInfo.setBooked(booked);
		}
	}
	
	private void setAppointmentFormCampaignUsageStats(AppointmentFormUsageReportResponse appointmentFormUsageReportResponse, CampaignUsageInfo usageInfo) {
		if (appointmentFormUsageReportResponse != null) {
			Long sent = appointmentFormUsageReportResponse.getSentInfo() != null ? appointmentFormUsageReportResponse.getSentInfo().getTotal() : 0;
			usageInfo.setSent(sent);
			
			Long delivered = appointmentFormUsageReportResponse.getDeliveredInfo() != null ? appointmentFormUsageReportResponse.getDeliveredInfo().getTotal() : 0;
			usageInfo.setDelivered(delivered);
			
			Long opened = appointmentFormUsageReportResponse.getOpenInfo() != null ? appointmentFormUsageReportResponse.getOpenInfo().getTotal() : 0;
			usageInfo.setOpened(opened);
			
			Long clicked = appointmentFormUsageReportResponse.getClickInfo() != null ? appointmentFormUsageReportResponse.getClickInfo().getTotal() : 0;
			usageInfo.setClicked(clicked);
			
			Long filled = appointmentFormUsageReportResponse.getFormFilledInfo() != null ? appointmentFormUsageReportResponse.getFormFilledInfo().getTotal() : 0;
			usageInfo.setFormsFilled(filled);
		}
	}
	
}
