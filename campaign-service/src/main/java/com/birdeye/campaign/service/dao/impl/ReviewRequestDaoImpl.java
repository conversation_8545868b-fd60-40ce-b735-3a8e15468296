package com.birdeye.campaign.service.dao.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.transaction.Transactional;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.aspect.annotation.Profiled;
import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.communication.message.CommunicationInput;
import com.birdeye.campaign.dto.ReviewRequestDto;
import com.birdeye.campaign.dto.ReviewRequestListDTO;
import com.birdeye.campaign.dto.ReviewRequestLiteResponse;
import com.birdeye.campaign.entity.ReviewRequest;
import com.birdeye.campaign.entity.ReviewRequestsLog;
import com.birdeye.campaign.readonly.repository.ReviewRequestReadOnlyRepo;
import com.birdeye.campaign.repository.ReviewRequestRepo;
import com.birdeye.campaign.service.dao.ReviewRequestDao;
import com.birdeye.campaign.service.dao.ReviewRequestLogDao;

/**
 * <AUTHOR>
 */

@Service
public class ReviewRequestDaoImpl implements ReviewRequestDao {
	
	@Autowired
	private ReviewRequestRepo			reviewRequestRepo;
	
	@Autowired
	private ReviewRequestReadOnlyRepo	reviewRequestReadOnlyRepo;
	
	@Autowired
	private ReviewRequestLogDao  reviewRequestLogDao;
	
	private static final Logger			logger	= LoggerFactory.getLogger(ReviewRequestDaoImpl.class);
	
	@Override
	@Transactional
	public ReviewRequest getReviewRequestById(Long reviewRequestId) {
		return reviewRequestRepo.getById(reviewRequestId);
	}
	
	@Override
	@Transactional
	public ReviewRequest saveReviewRequest(ReviewRequest reviewRequest) {
		if (reviewRequest == null) {
			return null;
		}
		return reviewRequestRepo.saveAndFlush(reviewRequest);
	}
	
	@Override
	public List<Long> getReviewRequestIdForBusiness(Integer businessId){
		return reviewRequestReadOnlyRepo.findRRIdsByBusinessIdForMigration(businessId);
	}
	
//	@Override
//	@Transactional
//	public void setMessage(List<Integer> businessIds, long startTime) {
//		
//		businessIds.parallelStream().forEach(businessId -> {
//			try {
//				Collection<CommunicationInput> messages = null;
//				
//				List<Long> parentRequestIds = reviewRequestReadOnlyRepo.findRRIdsByBusinessIdForMigration(businessId);
//				logger.info("review size: {}", parentRequestIds.size());
//				
//				if (CollectionUtils.isNotEmpty(parentRequestIds)) {
//					List<List<Long>> parentRequestIdsBatches = ListUtils.partition(parentRequestIds,
//							CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("rr.requests.migration.batch.size", 5000));
//					
//					for (List<Long> parentRequestsBatch : parentRequestIdsBatches) {
//						List<ReviewRequest> requests = reviewRequestReadOnlyRepo.findRequestAndRemindersByRequestIds(parentRequestsBatch);
//						logger.info("review_request size: {}", requests.size());
//						messages = prepareUsagesReportForReviewRequestMessages(requests, null, 0);
//						logger.info("messages size : {}", messages.size());
//						
//						String topicName = CacheManager.getInstance().getCache(KafkaTopicCache.class).getKafkaTopic(KafkaTopicTypeEnum.COMM_DATA_MIGRATION.getType());
//						if (CollectionUtils.isNotEmpty(messages)) {
//							for (CommunicationInput message : messages) {
//								HashMap<String, Object> messageWrapper = new HashMap<>();
//								messageWrapper.put("doc", message);
//								messageWrapper.put("doc_as_upsert", Boolean.TRUE);
//								
//								kafkaService.pushMessageToKafkaAcknowledged(topicName, message.getReq_id().toString(), messageWrapper);
//								logger.info("kafka Service Pushed {}", message);
//							}
//							
//						}
//					}
//				}
//				
//				long endTime = System.currentTimeMillis();
//				logger.info("[ RR Migration] Total Migration Time ::{} ms ", (startTime - endTime));
//				
//			} catch (Exception exe) {
//				logger.error("Error while processing the request for businessId {} and error: {}", businessId, exe);
//			}
//		});
//	}
	
//	@Override
//	@Transactional
//	public Collection<CommunicationInput> prepareUsagesReportForReviewRequestMessages(List<ReviewRequest> requests, EventMigrationRequest migrationRequest,
//			Integer migrateUsagesReportDataToES) {
//		if (migrateUsagesReportDataToES == 1) {
//			logger.info("Fetching requests and reminders by request ids, count {}", migrationRequest.getReviewRequestIds().size());
//			requests = reviewRequestReadOnlyRepo.findRequestAndRemindersByRequestIds(migrationRequest.getReviewRequestIds());
//		}
//		long startTime = System.currentTimeMillis();
//		logger.info("[Review Request Query]  Start Time {}:: ", startTime);
//		
//		if (CollectionUtils.isEmpty(requests)) {
//			return Collections.emptyList();
//		}
//		Map<Long, CommunicationInput> messageById = new HashMap<>();
//		Map<Long, Integer> parentRequestCheckins = new HashMap<>();
//		Map<Long, Long> parentRequestIdByChildId = new HashMap<>();
//		
//		// refactored
//		Map<Long, ReviewRequest> idByRequestsMap = requests.stream().collect(Collectors.toMap(ReviewRequest::getId, field -> field, (x, y) -> x));
//		List<Integer> distinctCustomerIds = requests.stream().map(ReviewRequest::getCustId).collect(Collectors.toSet()).stream().collect(Collectors.toList());
//		
//		List<List<Integer>> customerIdBatches = ListUtils.partition(distinctCustomerIds,
//				CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("rr.migration.customer.batch.size", 2500));
//		
//		List<CustomerByIdsLiteResponse> customersInfo = new ArrayList<>();
//		for (List<Integer> customerBatch : customerIdBatches) {
//			logger.info("Fetching customer batch data. first customer id of batch : {}", customerBatch.get(0));
//			customersInfo.addAll(contactExternalService.getCustomersInfoInBatchLite(customerBatch, false, false));
//		}
//		Map<Integer, CustomerByIdsLiteResponse> idToCustomerMap = customersInfo.stream().collect(Collectors.toMap(CustomerByIdsLiteResponse::getId, field -> field, (x, y) -> x));
//		
//		List<Integer> distinctBusinessIds = requests.stream().map(ReviewRequest::getBusinessId).collect(Collectors.toSet()).stream().collect(Collectors.toList());
//		Map<Integer, BusinessEnterpriseEntity> businessEntitiesMap = new HashMap<>();
//		for (Integer businessId : distinctBusinessIds) {
//			logger.info("Fetching business details for id : {}", businessId);
//			BusinessEnterpriseEntity businessById = cacheService.getBusinessById(businessId);
//			if (businessById != null) {
//				businessEntitiesMap.put(businessById.getId(), businessById);
//			}
//		}
//		for (ReviewRequest request : requests) {
//			
//			if (request.getParentRequestId() == null) {
//				logger.info("[RR Migration] Executing for request id : {}", request.getId());
//				CustomerByIdsLiteResponse requestCustomer = idToCustomerMap.get(request.getCustId());
//				BusinessEnterpriseEntity requestBusiness = businessEntitiesMap.get(request.getBusinessId());
//				if (requestCustomer == null || requestBusiness == null) {
//					logger.warn("{} is null due to api call failure.", requestCustomer == null ? "Customer" : "Business");
//					continue;
//				}
//				
//				CommunicationInput communicationInput = new CommunicationInput(request, requestCustomer);
//				communicationInput.setE_id(requestBusiness.getEnterpriseId() == null ? request.getBusinessId() : requestBusiness.getEnterpriseId());
//				communicationInput.setBusinessAlias(requestBusiness.getBusinessAlias());
//				requestDateMigrationService.updateRequestDateFieldsForUsageReportData(request, communicationInput);
//				messageById.put(request.getId(), communicationInput);
//				if (request.getCheckinId() != null) {
//					parentRequestCheckins.put(request.getId(), request.getCheckinId());
//				}
//			}
//			
//			// for non reminder requests, set request id as parent id.
//			parentRequestIdByChildId.put(request.getId(), request.getParentRequestId() != null ? request.getParentRequestId() : request.getId());
//			
//		}
		
//		for (ReviewRequest request : requests) {
//			ReviewRequest parentRequest;
//			if (request.getParentRequestId() != null) {
//				parentRequest = reviewRequestRepo.getById(request.getParentRequestId());
//				logger.info("parentReuestId = {}", request.getParentRequestId());
//			} else {
//				/**
//				 * It's Original Request then preparing messageById Map with Parent Request Id
//				 * Value @link CommunicationInput Object
//				 * Building CommunicationInput Object (Reminder and Click/Open) with Parent Request Id
//				 */
//				
//				KontactoDTO customer = contactExternalService.getCustomerById(request.getCustId());
//				logger.info("req_date ={}", request.getSentOn());
//				BusinessEnterpriseEntity business = cacheService.getBusinessById(request.getBusinessId());
//				
//				CommunicationInput communicationInput = new CommunicationInput(request, customer);
//				communicationInput.setE_id(business.getEnterpriseId() == null ? request.getBusinessId() : business.getEnterpriseId());
//				communicationInput.setBusinessAlias(business.getBusinessAlias());
//				requestDateMigrationService.updateRequestDateFieldsForUsageReportData(request, communicationInput);
//				messageById.put(request.getId(), communicationInput);
//				if (request.getCheckinId() != null) {
//					parentRequestCheckins.put(request.getId(), request.getCheckinId());
//				}
//				parentRequest = request;
//			}
//			/**
//			 * Preparing parentRequestIdByChildId Map
//			 * Key : Request Id (if original request then Parent request Id key and if reminder request then Reminder Request Id)
//			 * Value : if request Id is related to reminder request then Parent Request Id value and
//			 * if request Id is related to parent Request then id of parent request
//			 */
//			parentRequestIdByChildId.put(request.getId(), parentRequest.getId());
//		}
		
//		long endTime = System.currentTimeMillis();
//		logger.info("[Review Request Query] End Time :: {} ms ", (endTime - startTime));
//		
//		startTime = System.currentTimeMillis();
//		logger.info("[Review Query]  Start Time :: {}", startTime);
//		/**
//		 * find out Review Id for Corresponding Review Request Id (Parent + Reminder Id)
//		 */
//		
//		endTime = System.currentTimeMillis();
//		logger.info("[Review Query] End Time ::{} ms ", (endTime - startTime));
//		
//		startTime = System.currentTimeMillis();
//		logger.info("[Review Request Log]  Start Time :: {} ", startTime);
//		reviewRequestLogDao.updateEventInfo(messageById, parentRequestIdByChildId, idByRequestsMap, businessEntitiesMap);
//		
//		if (!parentRequestCheckins.isEmpty()) {
//			updateEmployeeInfo(messageById, parentRequestCheckins);
//		}
//		
//		endTime = System.currentTimeMillis();
//		logger.info("[Review Request Log] End Time :: {} ms ", (endTime - startTime));
//		
//		updatePromoterDetractor(messageById, parentRequestIdByChildId);
//		
//		return messageById.values();
//	}
//	
	/**
	 * @param messageById
	 *            (Map Key : Parent Request Id , Value : {@link}CommunicationInput)
	 * @param results
	 *            (Result Set of Review for Corresponding Input Review Request Id)
	 * @param parentRequestIdByChildId
	 *            (Map Key : Request ids (Parent) , Value : Check-in Id)
	 */
//	private void updateEmployeeInfo(Map<Long, CommunicationInput> messageById, Map<Long, Integer> parentRequestCheckins) {
//		Map<Integer, CheckinIdDetails> employeeInfoDetails = new HashMap<>();
//		
//		List<CheckinIdDetails> employeeInfos = customerService.getCustomersCheckInDetails(new ArrayList<>(parentRequestCheckins.values()), true, false);
//		
//		if (CollectionUtils.isNotEmpty(employeeInfos)) {
//			for (CheckinIdDetails employeeInfo : employeeInfos) {
//				Integer checkinId = employeeInfo.getCheckinId();
//				employeeInfoDetails.put(checkinId, employeeInfo);
//			}
//		}
//		
//		if (CollectionUtils.isNotEmpty(messageById.keySet())) {
//			for (Long requestId : messageById.keySet()) {
//				if (parentRequestCheckins.containsKey(requestId)) {
//					CommunicationInput message = messageById.get(requestId);
//					if (message != null && parentRequestCheckins.get(requestId) != null && employeeInfoDetails.containsKey(parentRequestCheckins.get(requestId))) {
//						message.setCheckin_id(parentRequestCheckins.get(requestId));
//						updateEmployeeInfo(message, employeeInfoDetails.get(parentRequestCheckins.get(requestId)));
//					}
//				}
//			}
//		}
//	}
	
//	private void updateEmployeeInfo(CommunicationInput message, CheckinIdDetails employeeInfo) {
//		List<CommEmp> employees = message.getEmp();
//		if (employees == null) {
//			employees = new ArrayList<>();
//			message.setEmp(employees);
//		}
//
//		String userName = null;
//		if (CollectionUtils.isNotEmpty(employeeInfo.getAssistedBy())  && employeeInfo.getAssistedBy().get(0).getName() !=null) {
//			userName = employeeInfo.getAssistedBy().get(0).getName();
//		}
//		
//		String emailId = null;
//		if (CollectionUtils.isNotEmpty(employeeInfo.getAssistedBy()) && employeeInfo.getAssistedBy().get(0).getEmail() !=null) {
//			emailId = employeeInfo.getAssistedBy().get(0).getEmail();
//		}
//		
//		Integer employeeId = null;
//		if (CollectionUtils.isNotEmpty(employeeInfo.getAssistedBy()) && employeeInfo.getAssistedBy().get(0).getId() !=null) {
//			employeeId = employeeInfo.getAssistedBy().get(0).getId();
//		}
//		
//		employees.add(new CommEmp(employeeId, CoreUtils.getUserName(userName, emailId)));
//	}
	
	/**
	 * This method used by new Communication Migration Index in ES for find open/click for parent request Id
	 *
	 * @param messageById
	 *            (Map Key : Parent Request Id , Value : {@link}CommunicationInput)
	 * @param parentRequestIdByChildId
	 *            (Map Key : Request ids(Parent + Reminder) , Value : Parent Request Id)
	 */
	@Override
	@SuppressWarnings("rawtypes")
	public void updatePromoterDetractor(Map<Long, CommunicationInput> messageById, Map<Long, Long> parentRequestIdByChildId) {
		
		// native queries can be safely removed post validation of else flow where we are fetching only from review_requests_log table
		if (CacheManager.getInstance().getCache(SystemPropertiesCache.class).getBooleanProperty("fetch-rrl-promoter-detractor-via-native-query")) {
			logger.info("updatePromoterDetractor : disabled flow promotor/detractor information via native query");
		} else {
			
			logger.info("updatePromoterDetractor : Fetching promotor/detractor information from review_requests_log table");
			Map<Long, Integer> recommendedByReqId = new HashMap<>();
			Map<Long, Integer> sourceIdByReqId = new HashMap<>();
			List<Long> reqIdsHavingClicks = new ArrayList<>();
			
			prepareReviewRequestsLogDataMapForClickedEventsWithoutSource(recommendedByReqId, parentRequestIdByChildId);
			
			prepareReviewRequestsLogDataMapForClickedEventsWithSources(reqIdsHavingClicks, recommendedByReqId, sourceIdByReqId, parentRequestIdByChildId);
			
			updateCommunicationInputMessage(messageById, parentRequestIdByChildId, recommendedByReqId, sourceIdByReqId, reqIdsHavingClicks);
			
		}
	}

	private void updateCommunicationInputMessage(Map<Long, CommunicationInput> messageById, Map<Long, Long> parentRequestIdByChildId, Map<Long, Integer> recommendedByReqId,
			Map<Long, Integer> sourceIdByReqId, List<Long> reqIdsHavingClicks) {
		
		for (Long requestId : messageById.keySet()) {
			Long parentRequestId = parentRequestIdByChildId.get(requestId);
			CommunicationInput message = messageById.get(requestId);
			if (message != null) {
				Integer recommended = null;
				Integer sourceId = null;
				if (parentRequestId != null) {
					recommended = recommendedByReqId.get(parentRequestId);
					sourceId = sourceIdByReqId.get(parentRequestId);
					message.setS_id(sourceId);
				}
				if (recommended == null) {
					if (!reqIdsHavingClicks.contains(parentRequestId)) {
						message.setIs_promoter(null);
					}
				}
				if (recommended != null) {
					message.setIs_promoter(recommended);
				}
			}
		}
	}
	
	private void prepareReviewRequestsLogDataMapForClickedEventsWithSources(List<Long> reqIdsHavingClicks, Map<Long, Integer> recommendedByReqId, Map<Long, Integer> sourceIdByReqId,
			Map<Long, Long> parentRequestIdByChildId) {
		
		List<ReviewRequestsLog> list = reviewRequestLogDao.getByReviewRequestIdAndEventAndSourceIdNotNull(parentRequestIdByChildId.keySet(), "click");
		
		for (ReviewRequestsLog reviewRequestsLog : list) {
			Long parentRequestId = parentRequestIdByChildId.get(reviewRequestsLog.getReviewRequestId());
			if (parentRequestId == null) {
				continue;
			}
			
			reqIdsHavingClicks.add(parentRequestId);
			sourceIdByReqId.put(parentRequestId, reviewRequestsLog.getSourceId());
			if (reviewRequestsLog.getSourceId().intValue() == 100 && reviewRequestsLog.getRecommended() != null) {
				recommendedByReqId.put(parentRequestId, reviewRequestsLog.getRecommended());
			}
		}
		
	}

	private void prepareReviewRequestsLogDataMapForClickedEventsWithoutSource(Map<Long, Integer> recommendedByReqId, Map<Long, Long> parentRequestIdByChildId) {
		List<ReviewRequestsLog> list = reviewRequestLogDao.getByReviewRequestIdAndEventAndClickType(parentRequestIdByChildId.keySet(), "click", 3);
		
		for (ReviewRequestsLog reviewRequestsLog : list) {
			Long parentRequestId = parentRequestIdByChildId.get(reviewRequestsLog.getReviewRequestId());
			if (parentRequestId == null) {
				continue;
			}
			
			if (reviewRequestsLog.getRecommended() != null) {
				recommendedByReqId.put(parentRequestId, reviewRequestsLog.getRecommended());
			}
		}
		
	}

	@Override
	public List<ReviewRequest> getByCheckinIdCustomerIdAndStatus(Integer checkinId, Integer customerId, String status) {
		
		return reviewRequestRepo.findAllByCheckinIdAndCustIdAndDeliveryStatus(checkinId, customerId, status);
	}

	@Override
	public void deleteRequestsByIds(List<Long> rrIds) {
		int deletedCount = reviewRequestRepo.deleteByIds(rrIds);
		logger.info("Deleted review requests count : {}", deletedCount);
	}
	
	@Override
	public List<ReviewRequestLiteResponse> getReviewRequestCheckInIds(List<Long> reviewRequestIds ) {
		return reviewRequestReadOnlyRepo.getCheckinIdFromReviewRequestId(reviewRequestIds);	
	}
	
	@Override
	public List<ReviewRequest> getReviewRequestsFromIds(List<Long> requestIds){
		return reviewRequestReadOnlyRepo.findRequestAndRemindersByRequestIds(requestIds);
	}
	
	@Override
	@Profiled
	public Integer updateDeliveryStatusAndFailureReasonByCampaignId(ReviewRequestListDTO request, String initialStatus, String finalStatus, String failureReason) {
		logger.info("Starting process of updating delivery status of review requests for initial status :: {} to final status :: {}, failure reason :: {}", initialStatus,
				finalStatus, failureReason);
		try {
			return reviewRequestRepo.updateDeliveryStatusAndFailureReason(request.getReviewRequestIds(), initialStatus, finalStatus, failureReason);
		} catch (Exception e) {
			logger.info("Exception occurred while executing rr drop operation for batch number :: {}, batch size :: {} and exception is :: {}", request.getBatchNumber(),
					request.getReviewRequestIds().size(), ExceptionUtils.getStackTrace(e));
			return -1;
		}
	}

	@Override
	public List<ReviewRequestDto> getByCustomerIdsAndDeliveryStatus(List<Integer> customerIds, List<String> status, Pageable page) {
		return reviewRequestReadOnlyRepo.findByCustomerIdInAndDeliveryStatusIn(customerIds,status,page);
	}

	@Override
	public List<ReviewRequestDto> getByCustomerIds(List<Integer> customerIds, Pageable page) {
		return reviewRequestReadOnlyRepo.findByCustomerIdIn(customerIds,page);
	}
	
	@Override
	public List<Long> findReviewRequestIdsByCustomerAndTypeAndDeliveryStatus(Integer customerId, String requestType, String deliveryStatus, String source){
		return reviewRequestRepo.findReviewRequestIdsByCustomerAndTypeAndDeliveryStatus(customerId, requestType, deliveryStatus, source);
	}
	
}
