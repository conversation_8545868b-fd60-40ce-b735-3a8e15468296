/**
 * @file_name ViewRecipientsUtils.java
 * @created_date 17 Aug 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.service.viewrecipients.impl;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.WordUtils;
import org.apache.commons.lang3.StringUtils;

import com.birdeye.campaign.dto.SplitCampaignViewRecipientsResponse;
import com.birdeye.campaign.dto.ViewRecipientsCampaignMetadata;
import com.birdeye.campaign.dto.ViewRecipientsResponse;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.platform.constant.CampaignTypeEnum;
import com.birdeye.campaign.report.ReportServiceUtils;
import com.birdeye.campaign.request.viewrecipients.ViewRecipientsFilter;
import com.birdeye.campaign.request.viewrecipients.ViewRecipientsRequest;
import com.birdeye.campaign.utils.CampaignUtils;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.SplitCampaignUtils;

/**
 * @file_name ViewRecipientsUtils.java
 * @created_date 17 Aug 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public class ViewRecipientsUtils {
	
	private static final String			CAMPAIGN_AUDIENCE_DATE_FORMAT_PDT		= "dd MMM yyyy";
	private static final String			CAMPAIGN_AUDIENCE_DATE_TIME_FORMAT_PDT	= "dd MMM yyyy hh:mm a z";
	
	/**
	 * Checks if View Recipients Request is Valid
	 * 
	 * @param request
	 * @return
	 */
	public static boolean isViewRecipientsRequestInvalid(ViewRecipientsRequest request) {
		return request.getCampaignId() == null || request.getEnterpriseId() == null || CollectionUtils.isEmpty(request.getBusinessIds());
	}
	
	/**
	 * Checks if View Recipients Request For Split Campaigns is Valid
	 * 
	 * @param request
	 * @param requestParams
	 * @return
	 */
	public static boolean isViewRecipientsRequestInvalidForSplitCampaign(ViewRecipientsRequest request, Map<String, Object> requestParams) {
		return request.getEnterpriseId() == null || CollectionUtils.isEmpty(request.getBusinessIds()) || requestParams.get(SplitCampaignUtils.SPLIT_CAMPAIGN_ID) == null;
	}
	
	
	/**
	 * Returns Campaign Metadata For Listing
	 * 
	 * @param campaign
	 * @return
	 */
	public static ViewRecipientsCampaignMetadata getCampaignMetadata(Campaign campaign, String emailTemplateCategory, String smsTemplateCategory) {
		ViewRecipientsCampaignMetadata meta = new ViewRecipientsCampaignMetadata();
		meta.setCampaignName(campaign.getName());
		meta.setCampaignPriorityType(campaign.getPriority());
		meta.setCampaignType(campaign.getCampaignType());
		boolean surveyCommRestrictionEnable = StringUtils.equalsIgnoreCase(CampaignTypeEnum.SURVEY_REQUEST.getType(), campaign.getType()) && CoreUtils.isTrueForInteger(campaign.getBypassCommRestriction()) && campaign.getSurveyCommFrequency() != null && campaign.getSurveyCommFrequency() != 0;
		meta.setSurveyCommRestrictionEnable(surveyCommRestrictionEnable);
		meta.setEmailTemplateCategory(emailTemplateCategory);
		meta.setSmsTemplateCategory(smsTemplateCategory);
		return meta;
	}
	
	/**
	 * Returns Campaign Metadata For Download
	 * 
	 * @param campaign
	 * @return
	 */
	public static Map<String, String> getCampaignMetadataForDownload(Campaign campaign) {
		Map<String, String> meta = new HashMap<>();
		meta.put("campaignName", campaign.getName());
		meta.put("campaignId", String.valueOf(campaign.getId()));
		return meta;
	}
	
	/**
	 * 
	 * Generates ES Query Tokens for View Recipients Summary and Listing
	 * 
	 * @param request
	 * @param requestParams
	 * @return
	 */
	public static Map<String, Object> getViewRecipientsQueryTokens(ViewRecipientsRequest request, Map<String, Object> requestParams, CampaignTypeEnum campaignType) {
		Map<String, Object> tokenData = new HashMap<>();
		tokenData.put("campaignId", String.valueOf(request.getCampaignId()));
		tokenData.put("enterpriseId", String.valueOf(request.getEnterpriseId()));
		tokenData.put("businessIds", request.getBusinessIds().toString());
		requestParams = decorateViewRecipientsRequestMap(requestParams); //NOSONAR
		request = decorateViewRecipientsRequest(request); //NOSONAR
		addSortSearchAndFilterParams(requestParams, tokenData, request.getFilter(), campaignType);
		return tokenData;
	}
	
	private static Map<String, Object> decorateViewRecipientsRequestMap(Map<String, Object> requestParams) {
		if (!requestParams.containsKey(ReportServiceUtils.RESULT_ORDER)) {
			requestParams.put(ReportServiceUtils.RESULT_ORDER, "desc");
		}
		return requestParams;
	}

	private static ViewRecipientsRequest decorateViewRecipientsRequest(ViewRecipientsRequest request) {
		ViewRecipientsFilter filter = request.getFilter();
		List<String> sources = filter.getSources();
		if (CollectionUtils.isEmpty(sources)) {
			//Handle list empty case - default ALL
			sources = Arrays.asList("email", "sms");
			filter.setSources(sources);
			request.setFilter(filter);
		}
		return request;
	}
	
	private static void addSortSearchAndFilterParams(Map<String, Object> requestParams, Map<String, Object> tokenData, ViewRecipientsFilter filter, CampaignTypeEnum campaignType) {
		Integer resultSize = 25;
		if (requestParams.containsKey(ReportServiceUtils.RESULT_SIZE)) {
			resultSize = (Integer) requestParams.get(ReportServiceUtils.RESULT_SIZE);
		}
		Integer resultPage = 0;
		if (requestParams.containsKey(ReportServiceUtils.RESULT_PAGE)) {
			resultPage = (Integer) requestParams.get(ReportServiceUtils.RESULT_PAGE);
		}
		String resultOrder = "desc";
		if (requestParams.containsKey(ReportServiceUtils.RESULT_ORDER)) {
			resultOrder = (String) requestParams.get(ReportServiceUtils.RESULT_ORDER);
		}
		String sortBy = null;
		if (requestParams.containsKey(ReportServiceUtils.SORT_BY)) {
			sortBy = (String) requestParams.get(ReportServiceUtils.SORT_BY);
		}
		String searchBy = null;
		if (requestParams.containsKey(ReportServiceUtils.SEARCH_BY)) {
			searchBy = (String) requestParams.get(ReportServiceUtils.SEARCH_BY);
		}
		if (StringUtils.isNotBlank(searchBy)) {
			tokenData.put(ReportServiceUtils.SEARCH_BY, searchBy);
			tokenData.put(ReportServiceUtils.SEARCH_BY_WILDCARD, WordUtils.capitalize(searchBy));
			
		}
		tokenData.put("size", String.valueOf(resultSize));
		tokenData.put("from", String.valueOf(resultPage * resultSize));
		tokenData.put("sortOrder", resultOrder);
		tokenData.put(ReportServiceUtils.SORT_BY, ReportServiceUtils.getSortingAttributeForViewRecipientsListing(sortBy, campaignType.getType()));
		tokenData.put(ReportServiceUtils.STATUS, filter.getStatus()); //sent,delivered,opened,clicked,failed,influenced
		tokenData.put(ReportServiceUtils.SOURCES, CampaignUtils.getESQueryableStringFromList(filter.getSources())); //can never be empty, already handled
	}
	
	
	
	/**
	 * @param message
	 * @return
	 */
	public static String getLastActivityDatePDT(Long lastActivityTime) {
		return CoreUtils.prepareFormattedDatePST(new Date(lastActivityTime), CAMPAIGN_AUDIENCE_DATE_FORMAT_PDT);
	}
	
	/**
	 * @param message
	 * @return
	 */
	public static String getLastActivityDateTimePDT(Long lastActivityTime) {
		return CoreUtils.prepareFormattedDatePST(new Date(lastActivityTime), CAMPAIGN_AUDIENCE_DATE_TIME_FORMAT_PDT);
	}
	
	/**
	 * @param source
	 * @return
	 */
	public static String getDisplaySource(String source) {
		if(StringUtils.isBlank(source)) {
			return null;
		}
		if(StringUtils.equalsAnyIgnoreCase(source, "sms", "text")) {
			return "Text";
		}
		if(StringUtils.equalsIgnoreCase(source, "email")) {
			return "Email";
		}
		return source;
	}
	
	public static Map<String, Object> getExternalViewRecipientsQueryTokens(ViewRecipientsRequest request, Map<String, Object> requestParams, CampaignTypeEnum campaignType) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Map<String, Object> tokenData = new HashMap<>();
		tokenData.put("campaignId", String.valueOf(request.getCampaignId()));
		tokenData.put("enterpriseId", String.valueOf(request.getEnterpriseId()));
		tokenData.put("businessIds", request.getBusinessIds().toString());
		if (request.getFromDate() != null && request.getToDate() != null) {
			tokenData.put("startDate", sdf.format(request.getFromDate()));
			tokenData.put("endDate", sdf.format(request.getToDate()));
		}
		requestParams = decorateViewRecipientsRequestMap(requestParams); //NOSONAR
		request = decorateViewRecipientsRequest(request); //NOSONAR
		addSortSearchAndFilterParams(requestParams, tokenData, request.getFilter(), campaignType);
		return tokenData;
	}
	
	public static boolean isValidExternalViewRecipientsRequest(ViewRecipientsRequest request, Map<String, Object> requestParams) {
		if(request.getCampaignId() == null || request.getEnterpriseId() == null || CollectionUtils.isEmpty(request.getBusinessIds())) {
			return false;
		}
		if(!isValidExternalViewRecipientRequestParam(requestParams)) {
			return false;
		}
		return true;
	}
	
	/**
	 * This method validates and processes the external view recipient request parameters.
	 * It ensures that the requested page size does not exceed the maximum allowable size and
	 * that the start index is within the acceptable range, which is less than 10000.
	 * If these conditions are satisfied, the method updates the start index and the new page size
	 * in the request parameters.
	 *
	 * @param requestParams A map containing the request parameters, including "resultPage" and "resultSize".
	 * @return true if the request parameters are valid and have been updated; false otherwise.
	 */
	private static boolean isValidExternalViewRecipientRequestParam(Map<String, Object> requestParams) {
	    final int MAX = 10000;
	    
	    if (!requestParams.containsKey("resultPage") || !requestParams.containsKey("resultSize")) {
	        return false;
	    }

	    int startIndex;
	    int pageSize;
	    try {
	        startIndex = (int) requestParams.get("resultPage");
	        pageSize = (int) requestParams.get("resultSize");
	    } catch (ClassCastException e) {
	        return false;
	    }

	    int newStartIndex = startIndex * pageSize;

	    // Validate the range and calculate newSize
	    if (newStartIndex >= MAX) {
	        return false;
	    }

	    int endPage = Math.min(newStartIndex + pageSize, MAX);
	    int newSize = endPage - newStartIndex;
	    // If the new size is positive, update the request parameters
		if (newSize > 0) {
			requestParams.put(ReportServiceUtils.RESULT_SIZE, newSize);
			requestParams.put(ReportServiceUtils.RESULT_PAGE, newStartIndex);
		}
	    return newSize > 0;
	}
	
	public static ViewRecipientsResponse prepareEmptyResponseWithUserPermissions(List<String> userPermissions) {
		ViewRecipientsResponse emptyResponse = new ViewRecipientsResponse();
		emptyResponse.setMeta(new ViewRecipientsCampaignMetadata());
		emptyResponse.getMeta().setUserPermissions(userPermissions);
		return emptyResponse;
	}
	
	public static SplitCampaignViewRecipientsResponse prepareEmptyResponseWithUserPermissionsForSplit(List<String> userPermissions) {
		SplitCampaignViewRecipientsResponse emptyResponse = new SplitCampaignViewRecipientsResponse();
		emptyResponse.setMeta(new ViewRecipientsCampaignMetadata());
		emptyResponse.getMeta().setUserPermissions(userPermissions);
		return emptyResponse;
	}

}
