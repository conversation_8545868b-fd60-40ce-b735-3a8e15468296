package com.birdeye.campaign.service.dao.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.entity.SplitCampaignModificationAudit;
import com.birdeye.campaign.repository.SplitCampaignModificationAuditRepo;
import com.birdeye.campaign.service.dao.SplitCampaignModificationAuditDao;

@Service("SplitCampaignModificationAuditDao")
public class SplitCampaignModificationAuditDaoImpl implements SplitCampaignModificationAuditDao {
	
	@Autowired
	private SplitCampaignModificationAuditRepo modifyAudit;
	
	/**
	 *
	 * Save and return split campaign modification history entry in db
	 *
	 * 
	 * @param SplitCampaignModificationAudit
	 * 
	 */
	@Override
	public SplitCampaignModificationAudit saveEntryInDb(SplitCampaignModificationAudit data) {
		return modifyAudit.saveAndFlush(data);
	}
	
}
