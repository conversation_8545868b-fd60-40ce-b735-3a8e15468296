package com.birdeye.campaign.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.birdeye.campaign.appointment.backfill.service.AppointmentBackfillService;
import com.birdeye.campaign.business.service.BusinessService;
import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.CampaignTriggerTypeCache;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.customer.service.CustomerService;
import com.birdeye.campaign.dto.BusinessAllCampaignsMessage;
import com.birdeye.campaign.dto.BusinessCampaignMessage;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.dto.CampaignBulkLocDataDto;
import com.birdeye.campaign.dto.CampaignContactInfo;
import com.birdeye.campaign.dto.CampaignCountMessage;
import com.birdeye.campaign.dto.CampaignInfoDTO;
import com.birdeye.campaign.dto.CampaignLocationInfoDto;
import com.birdeye.campaign.dto.CampaignMetadata;
import com.birdeye.campaign.dto.CampaignUserAccessDTO;
import com.birdeye.campaign.dto.CommunicationUsageStatsMessage;
import com.birdeye.campaign.dto.GlobalTemplateValidationDTO;
import com.birdeye.campaign.entity.AppointmentBackfillEvent;
import com.birdeye.campaign.entity.BusinessEmailTemplate;
import com.birdeye.campaign.entity.BusinessSmsTemplate;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.CampaignAccountSettings;
import com.birdeye.campaign.entity.CampaignCondition;
import com.birdeye.campaign.entity.CampaignEntitiesChangeLog;
import com.birdeye.campaign.entity.CampaignTriggerType;
import com.birdeye.campaign.entity.CustomCampaignUrl;
import com.birdeye.campaign.entity.DripCampaignCondition;
import com.birdeye.campaign.entity.MessengerCampaign;
import com.birdeye.campaign.enums.AppointmentBackfillEventStatusEnum;
import com.birdeye.campaign.enums.CampaignPriorityEnum;
import com.birdeye.campaign.enums.CampaignRunTypeEnum;
import com.birdeye.campaign.enums.DayOfWeekEnum;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.exception.CampaignHTTPException;
import com.birdeye.campaign.executor.services.CampaignCallable;
import com.birdeye.campaign.executor.services.CampaignExecutorService;
import com.birdeye.campaign.executor.services.ExecutorCommonService;
import com.birdeye.campaign.external.service.IContactExternalService;
import com.birdeye.campaign.external.service.SurveyExternalService;
import com.birdeye.campaign.global.templates.service.GlobalTemplateHandlerService;
import com.birdeye.campaign.platform.constant.CampaignCommRestrictionTypeEnum;
import com.birdeye.campaign.platform.constant.CampaignModificationUserActionEnum;
import com.birdeye.campaign.platform.constant.CampaignSchedulingTypeEnum;
import com.birdeye.campaign.platform.constant.CampaignStatusEnum;
import com.birdeye.campaign.platform.constant.CampaignTriggerTypeEnum;
import com.birdeye.campaign.platform.constant.CampaignTypeEnum;
import com.birdeye.campaign.platform.constant.SurveyRestrictionScopeTypeEnum;
import com.birdeye.campaign.platform.entity.User;
import com.birdeye.campaign.platform.readonly.repository.BusinessReadOnlyRepo;
import com.birdeye.campaign.platform.readonly.repository.UserReadOnlyRepo;
import com.birdeye.campaign.referral.service.AppointmentService;
import com.birdeye.campaign.report.ReportService;
import com.birdeye.campaign.repository.BusinessEmailTemplateRepo;
import com.birdeye.campaign.repository.BusinessSMSTemplateRepo;
import com.birdeye.campaign.repository.CampaignAccountSettingsRepo;
import com.birdeye.campaign.repository.CampaignConditionRepo;
import com.birdeye.campaign.repository.CampaignCustomFieldAssociationRepo;
import com.birdeye.campaign.repository.CampaignRepo;
import com.birdeye.campaign.repository.DripCampaignConditionRepo;
import com.birdeye.campaign.repository.EmailTemplateRepo;
import com.birdeye.campaign.repository.ReviewRequestRepo;
import com.birdeye.campaign.request.AutomationCampaignRequest;
import com.birdeye.campaign.request.CampaignBasicRequest;
import com.birdeye.campaign.request.CampaignEditInfo;
import com.birdeye.campaign.request.CampaignFilterRequest;
import com.birdeye.campaign.request.CampaignReminderTypeMessage;
import com.birdeye.campaign.request.CampaignUpdateStatusRequest;
import com.birdeye.campaign.request.CampaignUsageInfo;
import com.birdeye.campaign.request.CampaignUserAccessRequest;
import com.birdeye.campaign.request.CampaignsFilterCriteria;
import com.birdeye.campaign.request.CreateManualCampaignRequest;
import com.birdeye.campaign.request.CreateMessengerCampaignRequest;
import com.birdeye.campaign.request.CustomerDeleteEventRequest;
import com.birdeye.campaign.request.DripCampaignSchedulingDetails;
import com.birdeye.campaign.request.MessengerMediaInfo;
import com.birdeye.campaign.request.ProductFeatureRequest;
import com.birdeye.campaign.response.CampaignAccountSettingsResponse;
import com.birdeye.campaign.response.CampaignBasicResponse;
import com.birdeye.campaign.response.CampaignCountsResponse;
import com.birdeye.campaign.response.CreateCampaignResponse;
import com.birdeye.campaign.response.EditInfoResponse;
import com.birdeye.campaign.response.ManualCampaignResponse;
import com.birdeye.campaign.response.ManualEditCampaignResponse;
import com.birdeye.campaign.response.OngoingEditCampaignResponse;
import com.birdeye.campaign.response.ReviewRequestResponse;
import com.birdeye.campaign.response.ReviewRequestResponseWrapper;
import com.birdeye.campaign.response.external.AppointmentDetailsResponse;
import com.birdeye.campaign.response.external.CustomerInfoResponse;
import com.birdeye.campaign.service.AppointmentFormService;
import com.birdeye.campaign.service.AutomationCampaignSetupService;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.CampaignExecutionService;
import com.birdeye.campaign.service.CampaignModificationAuditService;
import com.birdeye.campaign.service.CampaignSetupCachingService;
import com.birdeye.campaign.service.CampaignSetupService;
import com.birdeye.campaign.service.CampaignUsageService;
import com.birdeye.campaign.service.ContentScanningService;
import com.birdeye.campaign.service.CustomCampaignCachedService;
import com.birdeye.campaign.service.CustomCampaignService;
import com.birdeye.campaign.service.DripCampaignCachedService;
import com.birdeye.campaign.service.DripCampaignService;
import com.birdeye.campaign.service.FilterCriteriaService;
import com.birdeye.campaign.service.MessengerCampaignService;
import com.birdeye.campaign.service.TemplateHelperService;
import com.birdeye.campaign.service.dao.CampaignEntitiesChangeLogDao;
import com.birdeye.campaign.template.service.TemplateConfigService;
import com.birdeye.campaign.user.access.settings.service.CampaignUserAccessSettingsService;
import com.birdeye.campaign.utils.AppointmentFormUtils;
import com.birdeye.campaign.utils.BusinessUtils;
import com.birdeye.campaign.utils.CampaignModificationAuditUtils;
import com.birdeye.campaign.utils.CampaignUserAccessUtils;
import com.birdeye.campaign.utils.CampaignUtils;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.DateTimeUtils;
import com.birdeye.campaign.utils.DripCampaignUtils;
import com.birdeye.template.dto.TemplateEnterpriseConfigUpdateMessage;
import com.birdeye.template.exception.TemplateConfigException;

@Service("campaignSetupService")
public class CampaignSetupServiceImpl implements CampaignSetupService {

	private static final Logger logger = LoggerFactory.getLogger(CampaignSetupServiceImpl.class);

	private static final String TIME_FORMAT_1 = "hh:mm a";

	@Autowired
	private CampaignRepo campaignRepo;

	@Autowired
	private ReportService reportService;

	@Autowired
	private CampaignConditionRepo campaignConditionRepo;

	@Autowired
	private UserReadOnlyRepo userRepo;

	@Autowired
	private EmailTemplateRepo emailTemplateRepo;

	@Autowired
	private BusinessSMSTemplateRepo smsTemplateRepo;

	@Autowired
	@Qualifier(Constants.CAMPAIGN_UI_COMPLETABLE_FUTURE_TASK_EXECUTOR)
	private ThreadPoolTaskExecutor threadPoolTaskExecutor;

	@Autowired
	private ExecutorCommonService executorCommonService;

	@Autowired
	private CampaignExecutionService campaignExecutionService;

	@Autowired
	private CampaignSetupCachingService campaignCachingService;

	@Autowired
	private SurveyExternalService surveyExternalService;

	@Autowired
	private BusinessReadOnlyRepo businessReadOnlyRepo;

	@Autowired
	private BusinessEmailTemplateRepo businessEmailTemplateRepo;

	@Autowired
	private BusinessSMSTemplateRepo businessSMSTemplateRepo;

	@Autowired
	private ReviewRequestRepo reviewRequestRepo;

	@Autowired
	private MessengerCampaignService messengerCampaignService;

	@Autowired
	private TemplateHelperService templateHelperService;

	@Autowired
	private CustomCampaignCachedService customCampaignCachedService;

	@Autowired
	private CustomCampaignService customCampaignService;

	@Autowired
	private DripCampaignConditionRepo dripCampaignConditionRepo;

	@Autowired
	private DripCampaignCachedService dripCampaignCachedService;

	@Autowired
	private CacheService cacheService;

	@Autowired
	private DripCampaignService dripCampaignService;

	@Autowired
	private CustomerService customerService;
	
	@Autowired
	@Qualifier(Constants.CAMPAIGN_UI_COMPLETABLE_FUTURE_TASK_EXECUTOR)
	private ThreadPoolTaskExecutor					taskExecutor;

	@Autowired
	private CampaignUsageService campaignUsageService;

	@Autowired
	private FilterCriteriaService filterCriteriaService;

	@Autowired
	private CampaignCustomFieldAssociationRepo customFieldAssociationRepo;
	
	@Autowired
	private IContactExternalService contactExternalService;
	
	@Autowired
	private AutomationCampaignSetupService automationCampaignService;

	@Autowired
	private BusinessService businessService;
	
	@Autowired
	private AppointmentFormService appointmentFormService;
	
	@Autowired
	private AppointmentBackfillService			appointmentBackfillService;
	
	@Autowired
	private AppointmentService 					appointmentService;
	
	@Autowired
	private GlobalTemplateHandlerService		globalTemplatesService;
	
	@Autowired
	private TemplateConfigService 				templateConfigService;
	
	@Autowired
	CampaignAccountSettingsRepo					campaignAccountSettingsRepo;
	
	@Autowired
	private CampaignUserAccessSettingsService	userAccessSettingsService;
	
	@Autowired
	private CampaignModificationAuditService	campaignModificationAuditService;
	
	@Autowired
	private ContentScanningService				contentScanningService;
	
	@Autowired
	private CampaignEntitiesChangeLogDao		campaignEntitiesChangeLogDao;

	@Override
	public BusinessAllCampaignsMessage getAllCampaignForEnterprise(Integer enterpriseId, Integer userId, CampaignFilterRequest campaignFilterRequest) {
		logger.info("Request received to getAllCampaignForEnterprise, enterpriseId : {} and request : {}", enterpriseId, campaignFilterRequest);
		if (enterpriseId == null || StringUtils.isEmpty(campaignFilterRequest.getRunType())) {
			throw new CampaignHTTPException(HttpStatus.BAD_REQUEST, "Enterprise Id or Campaign run type can not be null.");
		}
		BusinessAllCampaignsMessage response = new BusinessAllCampaignsMessage();
		Boolean isLocationFilteringApplicable = automationCampaignService.isLocationFilteringApplicable(enterpriseId, campaignFilterRequest);
		CampaignBulkLocDataDto campaignData = validateAndFetchLocCampaignDataAndTotalCount(enterpriseId, campaignFilterRequest, isLocationFilteringApplicable);
		response.setTotalCount(campaignData != null ? campaignData.getTotalCount() : 0);
		if (response.getTotalCount() == 0 || CollectionUtils.isEmpty(campaignFilterRequest.getCampaignType()) || CollectionUtils.isEmpty(campaignFilterRequest.getCampaignStatus())) {
			logger.info("No campaign found for enterpriseId {} and request{}", enterpriseId, campaignFilterRequest);
			return response;
		}
		
		Page<Campaign> pageOfCampaigns = fetchFilteredCampaigns(enterpriseId, campaignFilterRequest, CampaignUtils.getPageRequestForCampaignListing(campaignFilterRequest),
				CampaignUtils.getCampaignStatus(campaignFilterRequest.getCampaignStatus()), campaignData, isLocationFilteringApplicable);
		
		List<Campaign> campaigns = pageOfCampaigns.getContent();
		List<BusinessCampaignMessage> campaignsMessages = null;
		Integer freeTextSmsTemplateId = getFreeTextSmsTemplateId();
		if (CollectionUtils.isNotEmpty(campaigns)) {
			campaignsMessages = new ArrayList<>();
			BusinessCampaignMessage campaignMessage = null;
			Map<Integer, CommunicationUsageStatsMessage> campaignIdToUsageMap = reportService.getCampaignsCommunicationUsageData(enterpriseId, campaigns);
			for (Campaign campaign : campaigns) {
				campaignMessage = new BusinessCampaignMessage(campaign, isFreeTextCampaign(campaign, freeTextSmsTemplateId));
				// this is to populate correct scheduled date for new drip campaigns - campaign will remain scheduled (when yet to begin or resumed after being
				// paused), until a batch runs
				if (CampaignSchedulingTypeEnum.DRIP.getType().equalsIgnoreCase(campaign.getSchedulingType()) && dripCampaignService.isNewDripCampaign(campaign)
						&& CampaignStatusEnum.SCHEDULED.getStatus() == campaign.getStatus()) {
					Date runDate = dripCampaignService.getLatestScheduledBatchForCampaign(campaign.getId());
					if (runDate != null) {
						campaignMessage.setScheduledDt(prepareDateAsPerTimeZone(runDate, "MMM dd, yyyy hh:mm a", campaign.getEnterpriseId()));
					}
				}
				
				if (campaignMessage.getScheduledDt() == null && campaign.getStartAt() != null) {
					campaignMessage.setScheduledDt(prepareDateAsPerTimeZone(campaign.getStartAt(), "MMM dd, yyyy hh:mm a", campaign.getEnterpriseId()));
				}
				CommunicationUsageStatsMessage statsMessage = null;
				if (campaignIdToUsageMap.containsKey(campaign.getId())) {
					statsMessage = campaignIdToUsageMap.get(campaign.getId());
					campaignMessage.setUsageStats(statsMessage);
				}
				campaignsMessages.add(campaignMessage);
			}
			response.setData(campaignsMessages);
		}
		response.setFilteredCount(Integer.valueOf(String.valueOf(pageOfCampaigns.getTotalElements())));
		
		// BIRD-50563 | User Access Settings Handling in Listing API
		addAccessSettingsInCampaignListingResponse(enterpriseId, userId, response, campaignFilterRequest.getRunType());
		return response;
	}
	
	/**
	 * 
	 * This method fetches the access status for the current user for list of split campaigns
	 * 1. If the user is enterprise user , By default user would have edit access to all campaigns.
	 * 2. Else the campaign access will be given to the user. Default access is no-access.
	 * 
	 * @param accountId,
	 *            userId, response
	 * 
	 */
	private void addAccessSettingsInCampaignListingResponse(Integer accountId, Integer userId, BusinessAllCampaignsMessage response, String campaignRunType) {
		if (CampaignRunTypeEnum.MANUAL.getRunType().equalsIgnoreCase(campaignRunType)) {
			return;
		}
		
		if (CollectionUtils.isEmpty(response.getData())) {
			logger.info("Access Settings not applicable as campaign list is empty for accountId {} and userId {}", accountId, userId);
			return;
		}
		
		if (userId == null || BooleanUtils.isFalse(userAccessSettingsService.isAccessSettingApplicable(accountId))) {
			return;
		}
		
		logger.info("addAccessSettingsInCampaignListingResponse: fetching Access Settings for accountId {} and userId {}", accountId, userId);		
		CachedCollectionWrapper<CampaignUserAccessDTO> userCampaignAccessWrapper = userAccessSettingsService
				.fetchUserCampaignsAccess(new CampaignUserAccessRequest(accountId, userId, Constants.USER_ACCESS_CAMPAIGN));
		if (BooleanUtils.isTrue(CampaignUserAccessUtils.isAccessSettingsListForEnterpriseUser(userCampaignAccessWrapper))) {
			response.getData().stream().forEach(e -> e.setUserPermissions(new ArrayList<>(Collections.singletonList(userCampaignAccessWrapper.getElementsList().get(0).getAccess()))));
			return;
		}
		Map<Integer, CampaignUserAccessDTO> campaignIdToCampaignAccess = CampaignUserAccessUtils.prepareIdToCampaignAccessMap(userCampaignAccessWrapper, Constants.USER_ACCESS_CAMPAIGN);
		response.getData().stream().forEach(e -> e.setUserPermissions(CampaignUserAccessUtils.fetchAccessSettingsForCampaignId(campaignIdToCampaignAccess, e.getId())));
	}
	
	/**
	 * BIRD-50559 | If Location Filtering Applicable, Get Campaign List with Location Data, Else Size of Applicable Campaigns
	 *
	 * @param accountId, campaignFilterRequest, isLocationFilterApplicable
	 * 
	 */
	private CampaignBulkLocDataDto validateAndFetchLocCampaignDataAndTotalCount(Integer accountId, CampaignFilterRequest campaignFilterRequest, Boolean isLocationFilterApplicable) {
		if (BooleanUtils.isTrue(isLocationFilterApplicable)) {
			List<CampaignLocationInfoDto> campaignLocationData = campaignCachingService.getCampaignLocInfo(accountId).getElementsList();
			return new CampaignBulkLocDataDto(campaignLocationData, CollectionUtils.size(campaignLocationData));
		}
		return new CampaignBulkLocDataDto(new ArrayList<>(), campaignRepo.findCampaignCountByEnterpriseIdAndRunType(accountId, campaignFilterRequest.getRunType()));
	}
	
	/**
	 * 
	 * BIRD-50559 | Fetch Filtered Campaigns Based by including/excluding Location Hierarchy Filters
	 *
	 * @param accountId,
	 *            campaignFilterRequest, pageable, campaignStatus, campaignData, isLocationFilterApplicable
	 * 			
	 */
	private Page<Campaign> fetchFilteredCampaigns(Integer accountId, CampaignFilterRequest campaignFilterRequest, Pageable pageable, List<Integer> campaignStatus, CampaignBulkLocDataDto campaignData,
			Boolean isLocationFilterApplicable) {
		if (BooleanUtils.isTrue(isLocationFilterApplicable)) {
			List<Integer> campaignIds = CampaignUtils.filterCampaignBasedUponLocHierarchy(campaignFilterRequest, campaignData, false);
			return (CollectionUtils.isNotEmpty(campaignIds)) ? fetchFilteredCampaignsPaginated(accountId, campaignFilterRequest, pageable, campaignStatus, campaignIds)
					: new PageImpl<>(Collections.emptyList(), pageable, 0);
		}
		return fetchFilteredCampaignsPaginated(accountId, campaignFilterRequest, pageable, campaignStatus);
	}
	
	/**
	 * 
	 * BIRD-50559 | Fetch Filtered Campaigns Based by including list of campaign ids
	 *
	 * @param accountId,
	 *            campaignFilterRequest, pageable, campaignStatus, campaignIds
	 * 			
	 */
	private Page<Campaign> fetchFilteredCampaignsPaginated(Integer accountId, CampaignFilterRequest campaignFilterRequest, Pageable pageable, List<Integer> campaignStatus,
			List<Integer> campaignIds) {
		Page<Campaign> pageOfCampaigns = null;
		filterCampaignTypes(accountId, campaignFilterRequest);
		if (CampaignRunTypeEnum.ONGOING.getRunType().equalsIgnoreCase(campaignFilterRequest.getRunType())) {
			if (CollectionUtils.isEmpty(campaignFilterRequest.getTriggerType())) {
				campaignFilterRequest.setTriggerType(
						CacheManager.getInstance().getCache(CampaignTriggerTypeCache.class).getAllCampaignTriggerType().stream().map(CampaignTriggerType::getTriggerName).collect(Collectors.toList()));
			}
			if (StringUtils.isEmpty(campaignFilterRequest.getSearchStr())) {
				pageOfCampaigns = campaignRepo.findCampaignByFilterCriteriaAndTriggerTypeAndCampaignIds(accountId, campaignStatus, campaignFilterRequest.getCampaignType(), campaignFilterRequest.getRunType(),
						campaignFilterRequest.getTriggerType(), campaignIds, pageable);
			} else {
				pageOfCampaigns = campaignRepo.findCampaignByFilterCriteriaAndNameContainingAndTriggerTypeAndCampaignIds(accountId, campaignStatus, campaignFilterRequest.getSearchStr(),
						campaignFilterRequest.getCampaignType(), campaignFilterRequest.getRunType(), campaignFilterRequest.getTriggerType(), campaignIds, pageable);
			}
		}
		return pageOfCampaigns;
	}

	private Page<Campaign> fetchFilteredCampaignsPaginated(Integer enterpriseId, CampaignFilterRequest campaignFilterRequest, Pageable pageable, List<Integer> campaignStatus) {
		
		Page<Campaign> pageOfCampaigns = null;
		filterCampaignTypes(enterpriseId, campaignFilterRequest);
		
		if (CampaignRunTypeEnum.MANUAL.getRunType().equalsIgnoreCase(campaignFilterRequest.getRunType())) {
			if (StringUtils.isEmpty(campaignFilterRequest.getSearchStr())) {
				pageOfCampaigns = campaignRepo.findCampaignByFilterCriteria(enterpriseId, campaignStatus, campaignFilterRequest.getCampaignType(),
						campaignFilterRequest.getRunType(), pageable);
			} else {
				pageOfCampaigns = campaignRepo.findCampaignByFilterCriteriaAndNameContaining(enterpriseId, campaignStatus, campaignFilterRequest.getSearchStr(),
						campaignFilterRequest.getCampaignType(), campaignFilterRequest.getRunType(), pageable);
			}
		} else if (CampaignRunTypeEnum.ONGOING.getRunType().equalsIgnoreCase(campaignFilterRequest.getRunType())) {
			if (CollectionUtils.isEmpty(campaignFilterRequest.getTriggerType())) {
				campaignFilterRequest.setTriggerType(CacheManager.getInstance().getCache(CampaignTriggerTypeCache.class).getAllCampaignTriggerType().stream()
						.map(CampaignTriggerType::getTriggerName).collect(Collectors.toList()));
			}
			if (StringUtils.isEmpty(campaignFilterRequest.getSearchStr())) {
				pageOfCampaigns = campaignRepo.findCampaignByFilterCriteriaAndTriggerType(enterpriseId, campaignStatus, campaignFilterRequest.getCampaignType(),
						campaignFilterRequest.getRunType(), campaignFilterRequest.getTriggerType(), pageable);
			} else {
				pageOfCampaigns = campaignRepo.findCampaignByFilterCriteriaAndNameContainingAndTriggerType(enterpriseId, campaignStatus, campaignFilterRequest.getSearchStr(),
						campaignFilterRequest.getCampaignType(), campaignFilterRequest.getRunType(), campaignFilterRequest.getTriggerType(), pageable);
			}
		}
		
		return pageOfCampaigns;
	}

	/**
	 * @param enterpriseId
	 * @param campaignFilterRequest
	 * This method filters the campaign types based on business options purchased by the business.
	 */
	private void filterCampaignTypes(Integer enterpriseId, CampaignFilterRequest campaignFilterRequest) {
		logger.info("Filtering campaign types based on business options for request: {}", campaignFilterRequest);
		ProductFeatureRequest featureRequest = cacheService.getProductFeatureForBusiness(enterpriseId);
		List<String> filteredCampaignTypes = new ArrayList<>();
		campaignFilterRequest.getCampaignType().stream().forEach(campaignType -> {
			if ((StringUtils.equalsIgnoreCase(campaignType, CampaignTypeEnum.SURVEY_REQUEST.getType()) && !Objects.equals(featureRequest.getIsSurveyEnabled(), 1))
					|| (StringUtils.equalsIgnoreCase(campaignType, CampaignTypeEnum.REFERRAL.getType()) && !Objects.equals(featureRequest.getEnableReferral(), 1))
					|| (StringUtils.equalsIgnoreCase(campaignType, CampaignTypeEnum.APPOINTMENT_REMINDER.getType()) && !Objects.equals(featureRequest.getAppointmentRemindersEnabled(), 1))
					|| (StringUtils.equalsIgnoreCase(campaignType, CampaignTypeEnum.APPOINTMENT_RECALL.getType()) && !Objects.equals(featureRequest.getAppointmentRecallEnabled(), 1))
					|| (StringUtils.equalsIgnoreCase(campaignType, CampaignTypeEnum.APPOINTMENT_FORM.getType()) && !Objects.equals(featureRequest.getAppointmentFormEnabled(), 1))
					|| (StringUtils.equalsIgnoreCase(campaignType, CampaignTypeEnum.REVIEW_REQUEST.getType())) && !Objects.equals(featureRequest.getReviewGenEnabled(), 1)
					|| (StringUtils.equalsIgnoreCase(campaignType, CampaignTypeEnum.CX_REQUEST.getType())) && !Objects.equals(featureRequest.getReviewGenEnabled(), 1)) {
				return;
			}
			filteredCampaignTypes.add(campaignType);
		});
		campaignFilterRequest.setCampaignType(filteredCampaignTypes);
	}

	private Integer getFreeTextSmsTemplateId() {
		BusinessSmsTemplate freeTextSmsTemplate = templateHelperService.getFreeTextSmsTemplateId();
		return freeTextSmsTemplate != null ? freeTextSmsTemplate.getId() : null;
	}

	@Override
	public CreateCampaignResponse createOrUpdateManualCampaign(Integer campaignId, CreateManualCampaignRequest campaignRequest, Integer enterpriseId, String userId) {
		
		// Handling Global Template Save
		GlobalTemplateValidationDTO validationDTO = globalTemplatesService.validateAndCreateBusinessSmsTemplate(campaignRequest.getIsGlobalTemplate(),
				campaignRequest.getSmsTemplateId(), enterpriseId, userId);
		campaignRequest.setSmsTemplateId((validationDTO != null) ? validationDTO.getTemplateId() : campaignRequest.getSmsTemplateId());
		
		// update campaign path only draft campaigns
		Campaign ongoingCampaign = null, oldCampaign = null;
		//boolean isBatchApplicable = isApplicableForBatch(campaignRequest);
		validatePriority(campaignRequest.getPriority());
		if (campaignId != null && campaignId != 0) {
			ongoingCampaign = campaignCachingService.getCampaignById(campaignId);
			if (ongoingCampaign == null || CampaignStatusEnum.DRAFT.getStatus() != ongoingCampaign.getStatus()) {
				StringBuilder errorMsg = new StringBuilder("CampaignId").append(campaignId).append("For enterprise ").append(enterpriseId).append("cannot be edited");
				throw new CampaignHTTPException(HttpStatus.NOT_FOUND, errorMsg.toString());
			}
			oldCampaign = new Campaign(ongoingCampaign);
			prepareGenericManualCampaignData(campaignRequest, enterpriseId, ongoingCampaign, false);
			ongoingCampaign.setName(getCampaignName(enterpriseId, campaignRequest.getCampaignName(), ongoingCampaign.getName()));
			ongoingCampaign.setEditedBy(prepareUserName(getUserId(userId)));
			ongoingCampaign.setEditedOn(new Date());
			campaignCachingService.evictCampaignById(campaignId);

		} else {
			ongoingCampaign = prepareGenericManualCampaignData(campaignRequest, enterpriseId, null, false);
			ongoingCampaign.setName(getCampaignName(enterpriseId, campaignRequest.getCampaignName()));
			ongoingCampaign.setCreatedBy(getUserId(userId));
		}
		validateDripCampaignCondition(campaignRequest);
		campaignRepo.saveAndFlush(ongoingCampaign);
		DripCampaignCondition[] oldNewDripCondition = prepareDripCampaignConditionEntity(campaignRequest, ongoingCampaign, getUserId(userId));
		
		/*
		 * if (BooleanUtils.isFalse(isBatchApplicable)) {
		 * } else {
		 * // persist audience using kontakto scroll api and push campaign to execution - async
		 * populateCampaignCidsAsync(campaignRequest, campaign);
		 * }
		 */
		
			if (campaignRequest.getIsDraft() == null || campaignRequest.getIsDraft() == 0) {
				campaignExecutionService.pushCampaignToKafkaExecution(ongoingCampaign.getId());
			}
		
			// BIRD-72687 - Maintain audit of campaign modification(Async call)
			// Future case: For any change in the campaign/templates entities, consider its handling in auditing flow.
			campaignModificationAuditService.prepareAndPublishCampaignModificationEvent(
					CampaignModificationAuditUtils.prepareAllCampaignDataDTO(oldCampaign, null, oldNewDripCondition[1]),
					CampaignModificationAuditUtils.prepareAllCampaignDataDTO(ongoingCampaign, null, oldNewDripCondition[0]), getUserId(userId),
					(campaignId != null && campaignId != 0) ? CampaignModificationUserActionEnum.EDIT.getUserActionType()
							: CampaignModificationUserActionEnum.CREATE.getUserActionType(),
					Instant.now().toEpochMilli(), enterpriseId, false);
			
		return new CreateCampaignResponse(ongoingCampaign.getId(), ongoingCampaign.getName(), ongoingCampaign.getStatus());
	}
	
	/**
	 * Audience Filter : contacts > 5000
	 * 
	 * @param campaignRequest
	 * @return
	 */
	private static boolean isApplicableForBatch(CreateManualCampaignRequest campaignRequest) {
		if (campaignRequest.getAudienceFilterCampaign() == null) {
			// audience limited - no need to auto drip
			return false;
		}
		Integer customerCount = campaignRequest.getAudienceFilterCampaign().getTotalCount(); //UI - count
		Integer batchCustomerThreshold = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("customer.batch.threshold", 5000);
		return customerCount >= batchCustomerThreshold;
	}

	private void validatePriority(String priority) {
		if (StringUtils.isBlank(priority)) {
			logger.error("validatePriority : received blank priority {}", priority);
			throw new CampaignException(ErrorCodes.INVALID_CAMPAIGN, "Invalid campaign priority received");
		}
	}

	private void validateDripCampaignCondition(CreateManualCampaignRequest campaignRequest) {
		if (StringUtils.isEmpty(campaignRequest.getCmpStartAt()) || StringUtils.isEmpty(campaignRequest.getCmpEndAt())
				|| (CollectionUtils.isEmpty(campaignRequest.getExclusionDates()) && CollectionUtils.isEmpty(campaignRequest.getAllowedDays()) && StringUtils.isEmpty(campaignRequest.getSendTime()))) {
			return;
		}
		validateInputTime(campaignRequest);
		DripCampaignUtils.validateExclusionDates(campaignRequest.getExclusionDates());
	}

	private DripCampaignCondition[] prepareDripCampaignConditionEntity(CreateManualCampaignRequest campaignRequest, Campaign campaign, Integer userId) {
		if (StringUtils.isEmpty(campaignRequest.getCmpStartAt()) || StringUtils.isEmpty(campaignRequest.getCmpEndAt())
				|| (CollectionUtils.isEmpty(campaignRequest.getExclusionDates()) && CollectionUtils.isEmpty(campaignRequest.getAllowedDays()) && StringUtils.isEmpty(campaignRequest.getSendTime()))) {
			return new DripCampaignCondition[2];
		}
		DripCampaignCondition oldNewDripCondition[] = new DripCampaignCondition[2];
		DripCampaignCondition dripCampaignCondition = dripCampaignCachedService.getDripCampaignCondition(campaign.getId());
		//Save old drip condition
		if(dripCampaignCondition != null) {
			oldNewDripCondition[1] = new DripCampaignCondition(dripCampaignCondition);
		}
		
		if (dripCampaignCondition == null) {
			dripCampaignCondition = new DripCampaignCondition();
		}
		
		dripCampaignCondition.setCampaignId(campaign.getId());
		dripCampaignCondition.setEnterpriseId(campaign.getEnterpriseId());
		dripCampaignCondition.setAllowedDays(campaignRequest.getAllowedDays());
		dripCampaignCondition.setExclusionDates(campaignRequest.getExclusionDates());
		dripCampaignCondition.setSendTime(campaignRequest.getSendTime());
		dripCampaignCondition.setUpdatedBy(userId);
		dripCampaignConditionRepo.saveAndFlush(dripCampaignCondition);
		dripCampaignCachedService.evictDripCampaignConditionFromCache(campaign.getId());
		//Save current drip condition
		oldNewDripCondition[0] = dripCampaignCondition;
		return oldNewDripCondition;
	}

	private void validateInputTime(CreateManualCampaignRequest campaignRequest) {
		if (DripCampaignUtils.validateInputTime(campaignRequest.getSendTime()) == null) {
			logger.error("validateInputTime : error while parsing send time {}", campaignRequest.getSendTime());
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "Invalid campaign send time received");
		}
	}

	@Override
	public CreateCampaignResponse createMessengerCampaign(CreateMessengerCampaignRequest campaignRequest, Integer enterpriseId, String userId) {

		boolean suspiciousTemplate = contentScanningService.isSuspiciousMessengerCampaignContent(enterpriseId, Integer.valueOf(userId), getFreeTextSmsTemplateId(), campaignRequest);
		if (suspiciousTemplate) {
			logger.warn("Received suspicious content for messenger campaign for enterprise id {} user id {} and request {}", enterpriseId, userId, campaignRequest);
			return new CreateCampaignResponse(null, campaignRequest.getCampaignName());
		}
		// Handling Global Template Save
		GlobalTemplateValidationDTO validationDTO = globalTemplatesService.validateAndCreateBusinessSmsTemplate(campaignRequest.getIsGlobalTemplate(),
				campaignRequest.getSmsTemplateId(), enterpriseId, userId);
		campaignRequest.setSmsTemplateId((validationDTO != null) ? validationDTO.getTemplateId() : campaignRequest.getSmsTemplateId());
		
		Campaign campaign = prepareGenericManualCampaignData(campaignRequest, enterpriseId, null, false);
		campaign.setName(getCampaignName(enterpriseId, campaignRequest.getCampaignName()));
		campaign.setCreatedBy(getUserId(userId));
		campaign.setIsMessengerCampaign(1);
		//Handling for sending free text messages
		setTemplateIdForMessengerCampaign(campaignRequest, campaign);
		campaignRepo.saveAndFlush(campaign);
		try {
			createMessengerCampaignEntry(campaignRequest, campaign);
		} catch (Exception exception) {
			// required to delete entry in campaign table if error in creating messenger_campaign
			logger.error("exception occured while creating messenger campaign for enterpriseId {}", enterpriseId);
			campaignRepo.deleteById(campaign.getId());
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "Invalid request");
		}
		campaignExecutionService.pushCampaignToKafkaExecution(campaign.getId());
		
		// BIRD-72687 - Maintain audit of campaign modification(Async call)
		// Future case: For any change in the campaign/templates entities, consider its handling in auditing flow.
		campaignModificationAuditService.prepareAndPublishCampaignModificationEvent(CampaignModificationAuditUtils.prepareAllCampaignDataDTO(null, null, null),
				CampaignModificationAuditUtils.prepareAllCampaignDataDTO(campaign, null, null), getUserId(userId), CampaignModificationUserActionEnum.CREATE.getUserActionType(),
				Instant.now().toEpochMilli(), enterpriseId, false);
		
		return new CreateCampaignResponse(campaign.getId(), campaign.getName());
	}

	private Campaign prepareGenericManualCampaignData(CreateManualCampaignRequest campaignRequest, Integer enterpriseId, Campaign manualCampaign, boolean fetchRecipientsFromAppointments) {
		Campaign campaign = manualCampaign == null ? new Campaign() : manualCampaign;
		campaign.setEnterpriseId(enterpriseId);
		campaign.setPriority(campaignRequest.getPriority());
		campaign.setRunType(CampaignRunTypeEnum.MANUAL.getRunType());
		campaign.setSmsTemplateId(campaignRequest.getSmsTemplateId());
		campaign.setTemplateId(campaignRequest.getEmailTemplateId());
		campaign.setSurveyId(CampaignUtils.isSurveyRequest(campaignRequest.getSurveyId(), campaignRequest.getCampaignType()) ? campaignRequest.getSurveyId() : null);
		campaign.setType(campaignRequest.getCampaignType());
		setCampaignScheduleAndStatus(campaignRequest, campaign);
		setReminderConfig(campaignRequest, campaign);
		setCampaignAudience(campaignRequest, campaign, fetchRecipientsFromAppointments);
		campaign.setBypassCommRestriction(campaignRequest.getOverrideCommRestriction());
		campaign.setSkipFutureAppointment(campaignRequest.getSkipFutureAppointment() != null ? campaignRequest.getSkipFutureAppointment() : 0);
		return campaign;
	}

	private void createMessengerCampaignEntry(CreateMessengerCampaignRequest campaignRequest, Campaign campaign) {
		messengerCampaignService.saveMessengerCampaignData(campaignRequest, campaign.getId());
		customCampaignService.saveFreeTextCampaignUrl(campaignRequest.getFreeTextCampaignUrlMap(), campaign.getEnterpriseId(), campaign.getId());
	}

	private void setTemplateIdForMessengerCampaign(CreateMessengerCampaignRequest campaignRequest, Campaign campaign) {
		if (StringUtils.equalsAnyIgnoreCase(campaignRequest.getPriority(), CampaignPriorityEnum.SMS.getType(), CampaignPriorityEnum.EMAIL_SMS.getType(),
				CampaignPriorityEnum.SMS_EMAIL.getType(), CampaignPriorityEnum.EMAIL_AND_SMS.getType()) && campaignRequest.getSmsTemplateId() == null) {
			Integer smsTemplateId = getFreeTextSmsTemplateId();
			/*
			 * if (smsTemplateId == null) { // done to handle case when default templates were created from platform - won't be needed now NOSONAR
			 * List<BusinessTemplateEntity> smsTemplate = businessSMSTemplateRepo.getByEnterpriseIdAndIsDeletedAndTypeV2(enterpriseId,
			 * Constants.FREE_TEXT_TEMPLATE_TYPE); if (CollectionUtils.isNotEmpty(smsTemplate)) { smsTemplateId = smsTemplate.get(0).getTemplateId();
			 * customCampaignCachedService.createFreeTextTemplateEntry(smsTemplateId, enterpriseId); } }
			 */
			if (smsTemplateId == null) {
				logger.error("setTemplateIdForMessengerCampaign - Free text template Id not found {}", smsTemplateId);
				throw new CampaignException(ErrorCodes.INVALID_CAMPAIGN);
			}
			campaign.setSmsTemplateId(smsTemplateId);
		}
	}

	private void setReminderConfig(CreateManualCampaignRequest campaignRequest, Campaign campaign) {
		campaign.setSendReminder(campaignRequest.isSendReminder() ? 1 : 0);
		if (campaignRequest.isSendReminder()) {
			campaign.setReminderCount(campaignRequest.getReminderCount());
			campaign.setReminderFrequency(campaignRequest.getReminderInterval());
			campaign.setReminderSubject(campaignRequest.getReminderSubject());
		}
	}

	private Integer getUserId(String userId) {
		Integer uid = null;
		try {
			uid = Integer.parseInt(userId);
		} catch (NumberFormatException e) {
			logger.warn("Not a valid user id : {}", userId);
		}
		return uid;
	}

	/**
	 * Get Campaign Name for old and new campaigns (both inserts or updates)
	 * 
	 * @param enterpriseId
	 * @param newCampaignName
	 * @param oldCampaignName
	 *            - set null for new campaigns
	 * @return
	 */
	private String getCampaignName(Integer enterpriseId, String newCampaignName, String oldCampaignName) {
		if (StringUtils.equalsIgnoreCase(newCampaignName, oldCampaignName)) {
			return oldCampaignName;
		}
		return getCampaignName(enterpriseId, newCampaignName);
	}

	/**
	 * Get Campaign Name for New Campaign - Only inserts
	 * 
	 * @param newCampaignName
	 * @param enterpriseId
	 * @return
	 */
	private String getCampaignName(Integer enterpriseId, String newCampaignName) {
		Integer count = campaignRepo.getCampaignSameNameCountByEnterpriseIdAndName(enterpriseId, newCampaignName);
		if (count == 0)
			return newCampaignName;
		return newCampaignName + "(" + count + ")";
	}

	private String getCampaignNameForMigration(String campaignName, Integer enterpriseId, String campaignNameSuffix) {
		if (StringUtils.isNotBlank(campaignNameSuffix)) {
			campaignName = campaignName + " - " + campaignNameSuffix;
		}
		Integer count = campaignRepo.getCampaignSameNameCountByEnterpriseIdAndName(enterpriseId, campaignName);
		if (count == 0)
			return campaignName;

		return campaignName + "(" + count + ")";
	}

	private void setCampaignScheduleAndStatus(CreateManualCampaignRequest campaignRequest, Campaign campaign) {
		if (StringUtils.isNotBlank(campaignRequest.getCmpStartAt()) && StringUtils.isNotBlank(campaignRequest.getCmpEndAt())) {
			// DRIP MANUAL
			try {
				// adding hours so that conversion to PST doesn't lead to a change in date
				campaign.setStartAt(addHoursToDate(new SimpleDateFormat("MM/dd/yyyy").parse(campaignRequest.getCmpStartAt()), 8));
				campaign.setEndAt(addHoursToDate(new SimpleDateFormat("MM/dd/yyyy").parse(campaignRequest.getCmpEndAt()), 8));
				campaign.setSchedulingType(CampaignSchedulingTypeEnum.DRIP.getType());
				campaign.setStatus(CampaignStatusEnum.SCHEDULED.getStatus()); // 0 for Scheduled state
				campaign.setPriorityOrder(CampaignStatusEnum.SCHEDULED.getPriorityOrder());
			} catch (ParseException e) {
				logger.error("error while parsing campaign schedule date : {}", e.getMessage());
			}
		} else if (campaignRequest.getScheduled() != null && campaignRequest.getScheduled().intValue() > 0) {
			// SCHEDULED MANUAL
			setSchedulingDetails(campaignRequest, campaign);
			campaign.setSchedulingType(CampaignSchedulingTypeEnum.SCHEDULED.getType()); // Scheduled for scheduled instant campaigns
			campaign.setStatus(CampaignStatusEnum.SCHEDULED.getStatus()); // Scheduled for scheduled instant campaign
			campaign.setPriorityOrder(CampaignStatusEnum.SCHEDULED.getPriorityOrder());
			campaign.setStartAt(addHoursToDate(new Date(), campaign.getSchedule()));
			campaign.setEndAt(addHoursToDate(new Date(), campaign.getSchedule()));
		} else {
			// INSTANT MANUAL
			// this is the default value in DB for these dates, if we dont explicitly set it, hibernate assigns today's date to it
			campaign.setStartAt(new Date());
			campaign.setEndAt(new Date());
			campaign.setSchedulingType(CampaignSchedulingTypeEnum.INSTANT.getType());
			campaign.setStatus(CampaignStatusEnum.ACTIVE.getStatus()); // 1 for instant campaign (Active state)
			campaign.setPriorityOrder(CampaignStatusEnum.ACTIVE.getPriorityOrder());
		}

		if (campaignRequest.getIsDraft() != null && campaignRequest.getIsDraft() == 1) {
			// override Campaign status for draft Campaign
			campaign.setStatus(CampaignStatusEnum.DRAFT.getStatus());
			campaign.setPriorityOrder(CampaignStatusEnum.DRAFT.getPriorityOrder());
		}
	}

	private void setSchedulingDetails(CampaignBasicRequest campaignRequest, Campaign campaign) {
		campaign.setSchedule(campaignRequest.getScheduled());
		campaign.setSchedulingInHours(campaignRequest.getSchedulingInHours());
		if (Objects.equals(campaignRequest.getSchedulingInHours(), 0)) {
			campaign.setSchedule(campaignRequest.getScheduled() * 24);
		}
	}

	private Date addHoursToDate(Date date, int hours) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.HOUR_OF_DAY, hours);
		return calendar.getTime();
	}

	private void setCampaignAudience(CreateManualCampaignRequest campaignRequest, Campaign campaign, boolean fetchRecipientsFromAppointments) {
		List<Integer> campaignAudience = getCampaignAudience(campaignRequest, campaign.getEnterpriseId(), fetchRecipientsFromAppointments);
	
		campaign.setCustomerIds(StringUtils.join(campaignAudience, ","));
		campaign.setCustomerCount(CollectionUtils.isNotEmpty(campaignAudience) ? campaignAudience.size() : 0);
		if(CollectionUtils.isNotEmpty(campaignRequest.getAppointmentAudience())) {
			campaign.setAppointmentIds(StringUtils.join(campaignRequest.getAppointmentAudience(), ","));
		}
	}

	@Override
	public Integer createDefaultOnGoingCampaign(Integer enterpriseId, String businessName, Integer userId, Integer emailTemplateId, Integer smsTemplateId, CampaignStatusEnum status, String priority) {
		// NOTE: No need for new default campaign flow as contact source are not part of conditions now
		/*
		 * if (CacheManager.getInstance().getCache(SystemPropertiesCache.class).getBooleanProperty("automation_campaign_new_flow", false)) { return
		 * automationCampaignSetupService.createDefaultAutomationCampaign(enterpriseId, businessName, userId, emailTemplateId, smsTemplateId, status,
		 * priority); } else {
		 */
		Campaign campaign = defaultOnGoingCampaign(prepareDefaultCampaignData(enterpriseId, userId, emailTemplateId, smsTemplateId, "Review request"), status, priority);
		CampaignCondition campaignCondition = prepareDefaultCampaignConditionData(enterpriseId, userId, campaign.getId());
		campaignCondition.setLvlAlias("loc");
		campaignCondition.setEvent(getTriggerTypeByEvent(CampaignTriggerTypeEnum.CONTACT_ADDED.getType()));
		campaignConditionRepo.saveAndFlush(campaignCondition);
		evictOngoingCampaignsListCacheForEnterprise(campaign.getEnterpriseId());
		
		// BIRD-72687 - Maintain audit of campaign modification(Async call)
		// Future case: For any change in the campaign/templates entities, consider its handling in auditing flow.
		campaignModificationAuditService.prepareAndPublishCampaignModificationEvent(CampaignModificationAuditUtils.prepareAllCampaignDataDTO(null, null, null),
				CampaignModificationAuditUtils.prepareAllCampaignDataDTO(campaign, campaignCondition, null), userId, CampaignModificationUserActionEnum.CREATE.getUserActionType(),
				Instant.now().toEpochMilli(), enterpriseId, CoreUtils.getBooleanValueFromInteger(campaign.getIsSplitCampaign()));
		
		return campaign.getId();
	}
	
	private AutomationCampaignRequest campaignConditionToAutomationCampaignRequest(Integer resellerId, String type, Integer emailTemplateId, Integer smsTemplateId, String priority) {
		List<Campaign> campaignList = campaignRepo.findByResellerIdAndTypeAndIsDeleted(resellerId, type, 0);
		if (CollectionUtils.isEmpty(campaignList)) {
			logger.info("No campaign Id found for resellerId {} and type {}", resellerId, type);
			return null;
		}
		Campaign campaign = campaignList.get(0);
		CampaignCondition campaignCondn = campaignConditionRepo.getByCampaignId(campaign.getId());
		if (campaignCondn == null) {
			logger.info("No entry found in campaign conditions table for campaignId {}", campaign.getId());
			return null;
		}
		AutomationCampaignRequest campaignRequest = new AutomationCampaignRequest();
		campaignRequest.setCampaignId(campaign.getId());
		campaignRequest.setCampaignType(campaign.getCampaignType());
		campaignRequest.setCampaignName(campaign.getName());
		campaignRequest.setEmailTemplateId(emailTemplateId);
		campaignRequest.setSmsTemplateId(smsTemplateId);
		// campaignRequest.setSelectAll(false);
		campaignRequest.setPriority(campaign.getPriority());
		// campaignRequest.setPriority(priority);
		campaignRequest.setSurveyId(campaign.getSurveyId());
		campaignRequest.setSendReminder(campaign.getSendReminder() >= 1 ? true : false);
		campaignRequest.setReminderSubject(campaign.getReminderSubject());
		campaignRequest.setReminderCount(campaign.getReminderCount());
		campaignRequest.setReminderInterval(campaign.getReminderFrequency());
		campaignRequest.setScheduled(campaign.getSchedule());
		campaignRequest.setSchedulingInHours(campaign.getSchedulingInHours());
		campaignRequest.setIsDraft(campaign.getStatus() == CampaignStatusEnum.DRAFT.getStatus() ? 1 : null);
		campaignRequest.setStatusId(campaign.getStatus());
		campaignRequest.setTriggerType(campaign.getTriggerType());
		campaignRequest.setLvlAlias(campaignCondn.getLvlAlias());
		campaignRequest.setLvlAliasId(campaignCondn.getLvlAliasId());
		campaignRequest.setLvlIds(campaignCondn.getLvlIds());
		campaignRequest.setSources(campaignCondn.getContactSources());
		// campaignRequest.setTriggerExpression(campaignCondn.getTriggerRuleExpression());
		campaignRequest.setExpression(campaignCondn.getRuleExpression());
		return campaignRequest;
		
	}
	
	private AutomationCampaignRequest defaultBirdeyeCampaignForReseller(Integer resellerId, String type) {
		AutomationCampaignRequest campaignRequest = new AutomationCampaignRequest();
		campaignRequest.setCampaignId(0);
		campaignRequest.setCampaignType(type);
		campaignRequest.setCampaignName("Default OnGoing Campaign For Reseller");
		campaignRequest.setSendReminder(false);
		campaignRequest.setIsDraft(0);
		campaignRequest.setTriggerType(getTriggerTypeByEvent(CampaignTriggerTypeEnum.CONTACT_ADDED.getType()));
		campaignRequest.setLvlAlias("loc");
		campaignRequest.setPriority("email_and_sms");
		campaignRequest.setStatusId(5);
		List<String> defaultContactSources = new ArrayList<>();
		defaultContactSources.add("dashboard");
		defaultContactSources.add("sftp");
		defaultContactSources.add("integration");
		defaultContactSources.add("api");
		campaignRequest.setSources(defaultContactSources);
		return campaignRequest;
	}
	
	private void validateResellerCampaignType(String type) {
		if (!"promotional".equalsIgnoreCase(type) && !"review_request".equalsIgnoreCase(type) && !"survey_request".equalsIgnoreCase(type) && !"cx_request".equalsIgnoreCase(type)
				&& !"referral".equalsIgnoreCase(type)) {
			throw new CampaignException(ErrorCodes.INVALID_TYPE, ErrorCodes.INVALID_TYPE.getMessage());
		}
	}
	
	@Override
	public AutomationCampaignRequest getDefaultCampaignForReseller(Integer resellerId, String type) {
		if (resellerId == null || StringUtils.isBlank(type)) {
			throw new CampaignException(ErrorCodes.ERROR_INVALID_REQUEST, "Invalid data passed to get reseller campaign");
		}
		validateResellerCampaignType(type);
		BusinessEnterpriseEntity business = businessReadOnlyRepo.getValidBusinessByBid(resellerId);
		if (BusinessUtils.isResellerAndNotBirdeyeReseller(business)) {
			AutomationCampaignRequest campaignRequest = campaignConditionToAutomationCampaignRequest(resellerId, type, null, null, null);
			if (campaignRequest == null) {
				logger.info("No campaign data found fo reseller id {} , default campaign data will be sent", resellerId);
				return defaultBirdeyeCampaignForReseller(resellerId, type);
			}
			return campaignRequest;
		} else {
			throw new CampaignException(ErrorCodes.ERROR_NOT_A_RESELLER_ACCOUNT, "Error, Not a reseller account");
		}
	}
	
	@Override
	public Integer createDefaultOnGoingCampaignForReseller(Integer enterpriseId, Integer userId, Integer emailTemplateId, Integer smsTemplateId, String priority, Integer resellerId) {
		String type = Constants.REVIEW_REQUEST_TEMPLATE_TYPE;
		AutomationCampaignRequest campaignRequest = campaignConditionToAutomationCampaignRequest(resellerId, type, emailTemplateId, smsTemplateId, priority);
		if (campaignRequest == null) {
			logger.info("No campaign data found fo reseller id {} , default on going campaign will be created", resellerId);
			return null;
		}
		campaignRequest.setStatusId(1);
		CreateCampaignResponse campaignResponse = automationCampaignService.createOrUpdateAutomationCampaign(campaignRequest, enterpriseId, 0, String.valueOf(userId));
		return campaignResponse.getId();
	}

	private Campaign defaultOnGoingCampaign(Campaign campaign, CampaignStatusEnum status, String priority) {
		campaign.setType(CampaignTypeEnum.REVIEW_REQUEST.name());
		campaign.setRunType(CampaignRunTypeEnum.ONGOING.getRunType());
		campaign.setStatus(status.getStatus());
		campaign.setPriorityOrder(status.getPriorityOrder());
		campaign.setSendReminder(0);
		campaign.setReminderCount(3);
		campaign.setReminderFrequency(2);
		campaign.setPriority(priority);
		campaign.setTriggerType(getTriggerTypeByEvent(CampaignTriggerTypeEnum.CONTACT_ADDED.getType()));
		return campaignRepo.saveAndFlush(campaign);
	}
	
	private String getTriggerTypeByEvent(String event) {
		if (StringUtils.isEmpty(event) || "Contact is added to Birdeye".equalsIgnoreCase(event))
			event = CampaignTriggerTypeEnum.CONTACT_ADDED.getType();
		
		CampaignTriggerTypeCache triggerTypeCache = CacheManager.getInstance().getCache(CampaignTriggerTypeCache.class);
		CampaignTriggerType triggerType = triggerTypeCache.getCampaignTriggerTypeByName(event);
		if (triggerType != null)
			return triggerType.getTriggerName();
		return null;
	}

	private CampaignCondition prepareDefaultCampaignConditionData(Integer enterpriseId, Integer userId, Integer campaignId) {
		CampaignCondition campaignCondition = new CampaignCondition();
		campaignCondition.setEnterpriseId(enterpriseId);
		campaignCondition.setCampaignId(campaignId);
		campaignCondition.setUpdatedBy(userId);
		List<String> defaultContactSources = new ArrayList<>();
		defaultContactSources.add("dashboard");
		defaultContactSources.add("sftp");
		defaultContactSources.add("integration");
		defaultContactSources.add("api");
		campaignCondition.setContactSources(defaultContactSources);
		return campaignCondition;
	}

	private Campaign prepareDefaultCampaignData(Integer enterpriseId, Integer userId, Integer emailTemplateId, Integer smsTemplateId, String campaignName) {
		Campaign campaign = new Campaign();
		campaign.setEnterpriseId(enterpriseId);
		campaign.setName(campaignName);
		campaign.setSmsTemplateId(smsTemplateId);
		campaign.setTemplateId(emailTemplateId);
		campaign.setIsDefault(1);
		campaign.setCreatedBy(userId);
		return campaign;
	}

	// TODO: Campaign Status life cycle
	@Override
	public void updateCampaignStatus(Integer status, Integer campaignId) {
		Campaign onGoingCampaign = campaignRepo.findFirstById(campaignId);
		if (onGoingCampaign == null) {
			logger.error("updateCampaignStatus : invalid campaign : {}", campaignId);
			throw new CampaignException(ErrorCodes.INVALID_CAMPAIGN);
		}
		Campaign oldCampaign = new Campaign(onGoingCampaign);

		if (!(CampaignStatusEnum.ACTIVE.getStatus() == status || CampaignStatusEnum.PAUSED.getStatus() == status)) {
			logger.error("updateCampaignStatus : invalid campaign status : {}", status);
			throw new CampaignException(ErrorCodes.INVALID_CAMPAIGN_STATUS);
		}

		if (status == onGoingCampaign.getStatus()) {
			logger.warn("updateCampaignStatus : Campaign status already is : {} , No change would be done.", status);
			return;
		}

		int campaignStatus = status;
		if (CampaignSchedulingTypeEnum.INSTANT.getType().equalsIgnoreCase(onGoingCampaign.getSchedulingType())) {
			if (CampaignStatusEnum.ACTIVE.getStatus() == status) {
				// no RESUME for instant campaigns
				logger.error("updateCampaignStatus : instant campaign {} can't be resumed", campaignId);
				throw new CampaignException(ErrorCodes.INVALID_CAMPAIGN_STATUS);
			}
			// PAUSE works as STOP for instant campaigns
			campaignStatus = CampaignStatusEnum.STOPPED.getStatus();
		}
		// For Drip/Scheduled Campaign, resume should always make campaign to SCHEDULED
		// status and poller triggered execution will set the status to RUNNING
		if ((CampaignSchedulingTypeEnum.DRIP.getType().equalsIgnoreCase(onGoingCampaign.getSchedulingType()) || CampaignSchedulingTypeEnum.SCHEDULED.getType().equalsIgnoreCase(onGoingCampaign.getSchedulingType())) && status == CampaignStatusEnum.ACTIVE.getStatus()) {
			campaignStatus = CampaignStatusEnum.SCHEDULED.getStatus();
		}
		CampaignCondition campaignCondition = campaignCachingService.getCampaignConditionByCampaign(campaignId, onGoingCampaign.getEnterpriseId());
		// back-fill appointment events in case of appointment reminder automation resume
		if (StringUtils.equalsAnyIgnoreCase(onGoingCampaign.getCampaignType(), CampaignTypeEnum.APPOINTMENT_REMINDER.getType(), CampaignTypeEnum.APPOINTMENT_RECALL.getType(), CampaignTypeEnum.APPOINTMENT_FORM.getType())
				&& (campaignStatus == CampaignStatusEnum.ACTIVE.getStatus() || campaignStatus == CampaignStatusEnum.SCHEDULED.getStatus()) 
				&& AppointmentFormUtils.isAppointmentFormBackFillApplicable(new Campaign[]{onGoingCampaign}, campaignCondition)) {
			
			appointmentBackfillService.appointmentBackfillEvent(new AppointmentBackfillEvent(onGoingCampaign.getEnterpriseId(), campaignId, 
					onGoingCampaign.getCampaignType(), AppointmentBackfillEventStatusEnum.INIT, 0, null));
		}
		
		onGoingCampaign.setStatus(campaignStatus);
		onGoingCampaign.setPriorityOrder(CampaignStatusEnum.getStatus(campaignStatus).getPriorityOrder());
		campaignRepo.saveAndFlush(onGoingCampaign);
		// evict cache for campaign Id
		campaignCachingService.evictCampaignById(campaignId);
		if (CampaignRunTypeEnum.ONGOING.getRunType().equalsIgnoreCase(onGoingCampaign.getRunType())) {
			evictOngoingCampaignsListCacheForEnterprise(onGoingCampaign.getEnterpriseId());
		}

		// Handle Pause/Resume cases for drip campaign.
		if (CampaignSchedulingTypeEnum.DRIP.getType().equalsIgnoreCase(onGoingCampaign.getSchedulingType())) {
			handleDripPauseResume(status, onGoingCampaign);
		}
		
		// BIRD-72687 - Maintain audit of campaign modification(Async call)
		// Future case: For any change in the campaign/templates entities, consider its handling in auditing flow.
		campaignModificationAuditService.prepareAndPublishCampaignModificationEvent(CampaignModificationAuditUtils.prepareAllCampaignDataDTO(oldCampaign, null, null),
				CampaignModificationAuditUtils.prepareAllCampaignDataDTO(onGoingCampaign, null, null), CoreUtils.getNullSafeInteger(MDC.get(Constants.USER_ID)),
				CampaignModificationAuditUtils.getCampaignRunningStatus(status), Instant.now().toEpochMilli(), onGoingCampaign.getEnterpriseId(),
				CoreUtils.getBooleanValueFromInteger(onGoingCampaign.getIsSplitCampaign()));
	}

	private void handleDripPauseResume(Integer status, Campaign campaign) {
		if (dripCampaignService.isNewDripCampaign(campaign)) { // if new drip campaign (part of Campaign 3.3), redirect to new flow
			if (CampaignStatusEnum.ACTIVE.getStatus() == status) {
				dripCampaignService.handleDripResumeAsync(campaign);
			} else {
				dripCampaignService.handleDripPauseAsync(campaign);
			}

			return;
		}
		// if campaign is resumed, we need to create new entry in poller for this campaign either for today (if no batch has run yet today) or tomorrow
		if (CampaignStatusEnum.ACTIVE.getStatus() == status) {
			campaignExecutionService.handleDripCampaignResume(campaign);
		}
		// if campaign is paused, we need to remove any entry in INIT state present in the poller for this campaign id as we'll always create new entry on
		// resume
		else {
			campaignExecutionService.handleDripCampaignPause(campaign);
		}
	}

	private void prepareEditCampaignConditionData(CampaignCondition campCondition, OngoingEditCampaignResponse response) {
		response.setEvent(campCondition.getEvent());
		response.setLvlAlias(campCondition.getLvlAlias());
		response.setLvlAliasId(campCondition.getLvlAliasId());
		response.setLvlIds(campCondition.getLvlIds());
		if (CollectionUtils.isEmpty(campCondition.getLvlIds()) && "LOC".equalsIgnoreCase(campCondition.getLvlAlias())) {
			response.setSelectAll(true);
		} else {
			response.setSelectAll(false);
			if ("LOC".equalsIgnoreCase(campCondition.getLvlAlias()) && CollectionUtils.isNotEmpty(campCondition.getLvlIds())) {
				response.setLvlIds(getValidBusinessIds(campCondition.getLvlIds()));
			}
		}
		response.setTags(campCondition.getTags());
		response.setExcludeTags(campCondition.getExclusionTags());
		response.setNoTagFilter(campCondition.getNoTagFilter());
		response.setAnyTagFilter(campCondition.getAnyTagFilter());
		response.setSources(campCondition.getContactSources());

	}
	
	private List<String> getValidBusinessIds(List<String> lvlids) {
		if (CollectionUtils.isEmpty(lvlids)) {
			return Collections.emptyList();
		}
		Set<Integer> businessIds = lvlids.stream().map(id -> Integer.parseInt(id)).collect(Collectors.toSet());
		List<Integer> validBusinessIds = businessReadOnlyRepo.getValidBusinessIdByBusinessIds(businessIds);
		if (CollectionUtils.isEmpty(validBusinessIds)) {
			return Collections.emptyList();
		}
		return validBusinessIds.stream().map(id -> String.valueOf(id)).collect(Collectors.toList());
	}
	
	private void setSchedulingDetails(Campaign campaign, CampaignBasicResponse response) {
		if (campaign.getSchedulingInHours() != null) {
			response.setScheduled(campaign.getSchedule());
			response.setSchedulingInHours(campaign.getSchedulingInHours());
			if (campaign.getSchedulingInHours().intValue() == 0) {
				response.setScheduled(campaign.getSchedule() / 24);
			}
		} else { // backward compatability before scheduling changes (previously handled from UI)

			if (campaign.getSchedule() != null && campaign.getSchedule() > 0) {
				if (campaign.getSchedule().intValue() > 24) {
					response.setSchedulingInHours(0);
					response.setScheduled(campaign.getSchedule() / 24);
				}

				else {
					response.setSchedulingInHours(1);
					response.setScheduled(campaign.getSchedule());
				}

			}
		}
	}

	private void prepareEditCampaignDetails(Campaign campaign, OngoingEditCampaignResponse response) {
		response.setId(campaign.getId());
		response.setCampaignName(campaign.getName());
		response.setCampaignType(campaign.getType());
		response.setPriority(campaign.getPriority());
		response.setSurveyId(campaign.getSurveyId());
		response.setStatusId(campaign.getStatus());
		response.setRunType(CampaignRunTypeEnum.getEnum(campaign.getRunType()).getLabel());
		if (CampaignTypeEnum.SURVEY_REQUEST.getType().equalsIgnoreCase(campaign.getCampaignType())) {
			response.setSurveyName(surveyExternalService.getSurveyNameById(campaign.getSurveyId()));
		}
		response.setSendReminder(campaign.getSendReminder() != null && campaign.getSendReminder() == 1);
		response.setReminderCount(campaign.getReminderCount());
		response.setReminderInterval(campaign.getReminderFrequency());
		response.setReminderSubject(campaign.getReminderSubject());
		response.setEmailTemplateId(campaign.getTemplateId());
		response.setSmsTemplateId(campaign.getSmsTemplateId());
		response.setEmailTemplate(prepareEmailTemplateName(campaign.getTemplateId()));
		response.setSmsTemplate(prepareSMSTemplate(campaign.getSmsTemplateId()));
		response.setIsDraft(campaign.getStatus() == CampaignStatusEnum.DRAFT.getStatus() ? 1 : 0);
		// for migration
		response.setCreatedBy(String.valueOf(campaign.getCreatedBy()));
		setSchedulingDetails(campaign, response);
	}

	private String prepareSMSTemplate(Integer templateId) {
		return smsTemplateRepo.getTemplateNameById(templateId);
	}

	private String prepareEmailTemplateName(Integer templateId) {
		return emailTemplateRepo.findTemplateNameById(templateId);
	}

	private String prepareUserName(Integer userId) {
		User user = userRepo.findFirstById(userId);
		return CoreUtils.formatUserName(user.getFirstName(), user.getLastName(), user.getEmailId());
	}

	@Override
	public ManualEditCampaignResponse getEditManualCampaign(Integer enterpriseId, Integer campaignId) {
		ManualEditCampaignResponse response = new ManualEditCampaignResponse();
		editManualCampaignDetailsTask(campaignId, response);
		return response;
	}

	private void editManualCampaignDetailsTask(Integer campaignId, ManualEditCampaignResponse response) {

		Campaign campaign = campaignCachingService.getCampaignById(campaignId);
		if (campaign != null) {
			prepareManualCampaignEditDetails(campaign, response);
		}

	}

	private void prepareManualCampaignEditDetails(Campaign campaign, ManualEditCampaignResponse response) {
		response.setId(campaign.getId());
		response.setCampaignName(campaign.getName());
		response.setCampaignType(campaign.getType());
		response.setRunType(CampaignRunTypeEnum.getEnum(campaign.getRunType()).getLabel());
		response.setStatusId(campaign.getStatus());
		response.setCreatedOn(CoreUtils.formatDateToMMMddYYYY(campaign.getCreatedAt()));
		response.setCreatedBy(prepareUserName(campaign.getCreatedBy()));
		response.setCmpEndDate(CoreUtils.formatDateToMMMddYYYY(campaign.getEndAt()));
		response.setSelectAll(false);
		response.setEmailTemplate(prepareEmailTemplateName(campaign.getTemplateId()));
		response.setSmsTemplate(prepareSMSTemplate(campaign.getSmsTemplateId()));
		response.setPriority(campaign.getPriority());
		response.setSurveyId(campaign.getSurveyId());
		response.setIsDraft(CampaignStatusEnum.DRAFT.getStatus() == campaign.getStatus() ? 1 : 0);
		if(!StringUtils.equalsAnyIgnoreCase(campaign.getType(), Constants.APPOINTMENT_FORM_TYPE, Constants.APPOINTMENT_REMINDER_TYPE)) {
			response.setAudience(StringUtils.isNotEmpty(campaign.getCustomerIds()) ? CoreUtils.getIntegerListFromCommaSeparatedString(campaign.getCustomerIds()) : null);
		}
		if (CampaignTypeEnum.SURVEY_REQUEST.getType().equalsIgnoreCase(campaign.getCampaignType())) {
			response.setSurveyName(surveyExternalService.getSurveyNameById(campaign.getSurveyId()));
		}
		// UI needs end date null for instant campaign
		if (CampaignSchedulingTypeEnum.DRIP.getType().equalsIgnoreCase(campaign.getSchedulingType())) {
			response.setCmpStartAt(CoreUtils.formatDateTommddyyyy(campaign.getStartAt()));
			response.setCmpEndAt(CoreUtils.formatDateTommddyyyy(campaign.getEndAt()));
			setDripCampaignCondition(campaign, response);
		}
		if (CampaignSchedulingTypeEnum.SCHEDULED.getType().equalsIgnoreCase(campaign.getSchedulingType())) {
			setSchedulingDetails(campaign, response);
		}
		response.setSendReminder(campaign.getSendReminder() != null && campaign.getSendReminder() == 1);
		response.setReminderCount(campaign.getReminderCount());
		response.setReminderInterval(campaign.getReminderFrequency());
		// for clone part
		if (!isEmailTemplateDeleted(campaign.getTemplateId())) {
			response.setEmailTemplateId(campaign.getTemplateId());
		}
		if (!isSmsTemplateDeleted(campaign.getSmsTemplateId())) {
			response.setSmsTemplateId(campaign.getSmsTemplateId());
		}
		response.setReminderSubject(campaign.getReminderSubject());
		response.setOverrideCommRestriction(campaign.getBypassCommRestriction());
		response.setSkipFutureAppointment(campaign.getSkipFutureAppointment());
	}

	private void setDripCampaignCondition(Campaign campaign, ManualEditCampaignResponse response) {
		DripCampaignCondition dripCampaignCondition = dripCampaignConditionRepo.findByCampaignId(campaign.getId());
		if (dripCampaignCondition != null) {
			response.setAllowedDays(dripCampaignCondition.getAllowedDays());
			response.setExclusionDates(dripCampaignCondition.getExclusionDates());
			response.setSendTime(dripCampaignCondition.getSendTime());
		}

	}

	@Override
	public OngoingEditCampaignResponse getEditOngoingCampaign(Integer enterpriseId, Integer campaignId) {
		OngoingEditCampaignResponse response = new OngoingEditCampaignResponse();
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
		executorService.submit(editCampaignDetailsTask(campaignId, response));
		executorService.submit(editCampaignConditionTask(enterpriseId, campaignId, response));
		try {
			executorCommonService.executeTasks(executorService, 5000);
		} catch (Exception exe) {
			logger.error("Error {} while executing the the get details for eid {} and campId {}", exe, enterpriseId, campaignId);
		}
		return response;
	}

	private Callable<Boolean> editCampaignDetailsTask(final Integer campaignId, final OngoingEditCampaignResponse response) {
		return new CampaignCallable<Boolean>("Edit Campaign details Task") {
			@Override
			public Boolean doCall() {
				Campaign campaign = campaignCachingService.getCampaignById(campaignId);
				if (campaign != null) {
					prepareEditCampaignDetails(campaign, response);
				}
				return true;
			}
		};
	}

	private Callable<Boolean> editCampaignConditionTask(final Integer enterpriseId, final Integer campaignId, final OngoingEditCampaignResponse response) {
		return new CampaignCallable<Boolean>("Edit Campaign Condition Task") {
			@Override
			public Boolean doCall() {
				CampaignCondition campaignCondition = campaignConditionRepo.getByCampaignIdAndEnterpriseId(campaignId, enterpriseId);
				if (campaignCondition != null) {
					prepareEditCampaignConditionData(campaignCondition, response);
				}
				return true;
			}
		};
	}

	/**
	 * View Details api for manual campaign.
	 * 
	 * @param enterpriseId
	 * @param campaignId
	 * @return
	 */
	@Override
	public ManualCampaignResponse getManualCampaign(Integer enterpriseId, Integer campaignId) {
		CampaignUtils.validateAccountId(enterpriseId);
		ManualCampaignResponse response = new ManualCampaignResponse();
		Campaign campaign = campaignCachingService.getCampaignById(campaignId);
		CampaignUtils.validateCampaignId(enterpriseId, campaign.getEnterpriseId());
		if (campaign != null) {
			CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
			executorService.submit(getManualGenericDetailsTask(campaign, response));
			executorService.submit(campaignAuditLogInfoTask(campaign, response));
			if (CampaignStatusEnum.DRAFT.getStatus() != campaign.getStatus()) {
				CampaignsFilterCriteria filterCriteria = filterCriteriaService.prepareCampaignsFilterCriteria(enterpriseId, campaign);
				executorService.submit(getCampaignUsageReportTask(filterCriteria, response));
			}
			try {
				executorCommonService.executeTasks(executorService, 5000);
			} catch (Exception exe) {
				logger.error("Error {} while executing the the get details for enterpriseId {} and campaignId {}", exe, enterpriseId, campaignId);
			}
			return response;
		}
		return response;
	}
	

	private Callable<Boolean> campaignAuditLogInfoTask(Campaign campaign, final ManualCampaignResponse response) {
		return new CampaignCallable<Boolean>("Campaign Audit Info Task") {
			@Override
			public Boolean doCall() {
				// Fetch latest audit log corresponding to entity
				CampaignEntitiesChangeLog changeLog = campaignEntitiesChangeLogDao.findLatestChangeLogByIdAndType(campaign.getEnterpriseId(), campaign.getId(),
						CampaignModificationAuditUtils.CAMPAIGN_ENTITY, StringUtils.EMPTY);
				String businessTimeZoneId = businessService.getBusinessTimezoneId(campaign.getEnterpriseId());
				if (Objects.isNull(changeLog)) {
					// Prepare creation audit log using the entity
					changeLog = new CampaignEntitiesChangeLog(campaign.getEnterpriseId(), campaign.getId(), campaign.getCreatedBy(),
							CampaignModificationUserActionEnum.CREATE.getUserActionType(), campaign.getCreatedAt());
				}
				prepareAuditLogInfo(changeLog, response, businessTimeZoneId);
				return true;
			}
			
			private void prepareAuditLogInfo(CampaignEntitiesChangeLog changeLog, ManualCampaignResponse response, String businessTimeZoneId) {
				EditInfoResponse auditInfoResponse = new EditInfoResponse(cacheService.getUserResponsibleForModification(changeLog.getUserId(), campaign.getEnterpriseId()),
						changeLog.getUserId(), changeLog.getEvent().toLowerCase(),
						DateTimeUtils.prepareFormattedDate(changeLog.getEventTime(), CampaignModificationAuditUtils.MODIFICATION_DATE_FORMAT, businessTimeZoneId),
						DateTimeUtils.prepareFormattedDate(changeLog.getEventTime(), CampaignModificationAuditUtils.MODIFICATION_TIME_FORMAT, businessTimeZoneId));
				response.setAuditLogInfo(auditInfoResponse);
			}
		};
	}
	
	/*
	 * private CampaignsFilterCriteria prepareCampaignsFilterCriteria(Integer enterpriseId, Campaign campaign) { CampaignsFilterCriteria filterCriteria =
	 * new CampaignsFilterCriteria(); List<Integer> locationIds = businessRepo.findBusinessIdByBusinessIdOrEnterpriseId(enterpriseId);
	 * filterCriteria.setBusinessIds(locationIds); filterCriteria.setCampaignId(campaign.getId()); if (StringUtils.isNotEmpty(campaign.getPriority())) {
	 * filterCriteria.setSources(Arrays.asList(campaign.getPriority().replace("_and_", "_").split("_"))); } filterCriteria.setCount(null);
	 * filterCriteria.setEnterpriseId(enterpriseId); filterCriteria.setIsDownload(false); try { SimpleDateFormat dateFormat = new
	 * SimpleDateFormat(CAMPAIGN_REPORT_DATE_FORMAT); Date fromDate = dateFormat.parse("1970-01-01 00:00:01"); filterCriteria.setFromDate(fromDate);
	 * filterCriteria.setToDate(new Date()); } catch (ParseException e) {
	 * logger.info("prepareCampaignsFilterCriteria : Exception occured while parsing Date"); } return filterCriteria; }
	 */

	private CampaignCallable<Boolean> getManualGenericDetailsTask(Campaign campaign, ManualCampaignResponse response) {
		return new CampaignCallable<Boolean>("getManualGenericDetailsTask") {
			@Override
			public Boolean doCall() {
				prepareCampaignDetails(campaign, response);
				return true;
			}
		};
	}

	private CampaignCallable<Boolean> getCampaignUsageReportTask(CampaignsFilterCriteria filterCriteria, ManualCampaignResponse response) {
		return new CampaignCallable<Boolean>("getCampaignUsageReportTask") {
			@Override
			public Boolean doCall() {
				CampaignUsageInfo usageInfo = new CampaignUsageInfo();
				campaignUsageService.prepareCampaignUsageReport(filterCriteria, usageInfo, response.getCampaignType());
				response.setCampaignUsageInfo(usageInfo);
				return true;
			}
		};
	}

	private void prepareCampaignDetails(Campaign campaign, ManualCampaignResponse response) {
		response.setId(campaign.getId());
		response.setCampaignName(campaign.getName());
		response.setCampaignType(campaign.getType());
		response.setRunType(CampaignRunTypeEnum.getEnum(campaign.getRunType()).getLabel());
		response.setStatusId(campaign.getStatus());
		response.setCreatedOn(prepareDateAsPerTimeZone(campaign.getCreatedAt(), "MMM dd, yyyy", campaign.getEnterpriseId()));
		response.setCreatedBy(prepareUserName(campaign.getCreatedBy()));
		response.setCmpEndDate(prepareDateAsPerTimeZone(campaign.getEndAt(), "MMM dd, yyyy", campaign.getEnterpriseId()));
		response.setAudience(campaign.getCustomerCount());
		response.setSelectAll(false);
		response.setEmailTemplate(prepareEmailTemplateName(campaign.getTemplateId()));
		response.setSmsTemplate(prepareSMSTemplate(campaign.getSmsTemplateId()));
		response.setPriority(campaign.getPriority());
		response.setSurveyId(campaign.getSurveyId());
		if (CampaignTypeEnum.SURVEY_REQUEST.getType().equalsIgnoreCase(campaign.getCampaignType())) {
			response.setSurveyName(surveyExternalService.getSurveyNameById(campaign.getSurveyId()));
		}
		// UI needs end date null for instant campaign
		if (CampaignSchedulingTypeEnum.DRIP.getType().equalsIgnoreCase(campaign.getSchedulingType())) {
			response.setCmpStartAt(prepareDateAsPerTimeZone(campaign.getStartAt(), "MM/dd/yyyy", campaign.getEnterpriseId()));
			response.setCmpEndAt(prepareDateAsPerTimeZone(campaign.getEndAt(), "MM/dd/yyyy", campaign.getEnterpriseId()));			prepareDripCampaignCondition(campaign, response);
			prepareDripCampaignCondition(campaign, response);
		}
		if (CampaignSchedulingTypeEnum.SCHEDULED.getType().equalsIgnoreCase(campaign.getSchedulingType())) {
			setSchedulingDetails(campaign, response);
		}
		response.setSendReminder(campaign.getSendReminder() != null && campaign.getSendReminder() == 1);
		response.setReminderCount(campaign.getReminderCount());
		response.setReminderInterval(campaign.getReminderFrequency());
		// for clone part
		if (!isEmailTemplateDeleted(campaign.getTemplateId())) {
			response.setEmailTemplateId(campaign.getTemplateId());
		}
		if (!isSmsTemplateDeleted(campaign.getSmsTemplateId())) {
			if (isFreeTextCampaign(campaign)) {
				response.setSmsTemplateId(null);
			} else {
				response.setSmsTemplateId(campaign.getSmsTemplateId());
			}
		}
		
		if (StringUtils.equalsIgnoreCase(campaign.getType(), CampaignTypeEnum.PROMOTIONAL.getType())) {
			if (CoreUtils.isTrueForInteger(campaign.getTemplateId())) {
				response.setEmailTemplateCategory(templateHelperService.prepareEmailTemplateCategory(campaign.getTemplateId()));
			}
			
			if (CoreUtils.isTrueForInteger(campaign.getSmsTemplateId())) {
				response.setSmsTemplateCategory(templateHelperService.prepareSmsTemplateCategory(campaign.getSmsTemplateId()));
			}
		}
		
		response.setReminderSubject(campaign.getReminderSubject());
		response.setIsMessengerCampaign(campaign.getIsMessengerCampaign() != null ? campaign.getIsMessengerCampaign() : 0);
		response.setIsAppointmentTabCampaign(campaign.getIsAppointmentTabCampaign() != null ? campaign.getIsAppointmentTabCampaign() : 0);
		if ((campaign.getIsMessengerCampaign() != null && campaign.getIsMessengerCampaign() == 1)
				|| (campaign.getIsAppointmentTabCampaign() != null && campaign.getIsAppointmentTabCampaign() == 1)) {
			prepareMessengerOrAppointmentTabCampaignDataInResponse(campaign, response);
		}
		if (StringUtils.isNotEmpty(campaign.getEditedBy()) && campaign.getEditedOn() != null) {
			response.setEditInfo(prepareCampaignEditDetails(campaign));
		}
		response.setOverrideCommRestriction(campaign.getBypassCommRestriction());
	}

	private String prepareDateAsPerTimeZone(Date date, String dateFormat, Integer businessId){
		String timeZoneId = businessService.getBusinessTimezoneId(businessId);
		return DateTimeUtils.prepareFormattedDate(date ,dateFormat, timeZoneId);
	}

	/*
	 * private void prepareCampaignUsageReport(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo, String campaignType) {
	 * 
	 * if (campaignType.equalsIgnoreCase(CampaignTypeEnum.CX_REQUEST.getType())) { UsageFunnelReportResponse usageFunnelReportResponse =
	 * usageReportsService.getCXUsageGraphReport(filterCriteria);
	 * 
	 * @SuppressWarnings("unchecked") List<CXUsageReportResponse> usageResponseList = (List<CXUsageReportResponse>)
	 * usageFunnelReportResponse.getUsageReportResponses(); setCxCampaignUsageStats(usageResponseList.get(0), usageInfo); }
	 * 
	 * else if (campaignType.equalsIgnoreCase(CampaignTypeEnum.REVIEW_REQUEST.getType())) { UsageFunnelReportResponse usageFunnelReportResponse =
	 * usageReportsService.getRRUsageGraphReport(filterCriteria);
	 * 
	 * @SuppressWarnings("unchecked") List<RRUsageReportResponse> usageResponseList = (List<RRUsageReportResponse>)
	 * usageFunnelReportResponse.getUsageReportResponses(); setRRCampaignUsageStats(usageResponseList.get(0), usageInfo); }
	 * 
	 * else if (campaignType.equalsIgnoreCase(CampaignTypeEnum.REFERRAL.getType())) { UsageFunnelReportResponse usageFunnelReportResponse =
	 * usageReportsService.getReferralUsageGraphReport(filterCriteria);
	 * 
	 * @SuppressWarnings("unchecked") List<ReferralUsageReportResponse> usageResponseList = (List<ReferralUsageReportResponse>)
	 * usageFunnelReportResponse.getUsageReportResponses(); setReferralCampaignUsageStats(usageResponseList.get(0), usageInfo); }
	 * 
	 * else if (campaignType.equalsIgnoreCase(CampaignTypeEnum.SURVEY_REQUEST.getType())) { Map<Integer, CommunicationUsageStatsMessage>
	 * campaignIdToUsageMap = new HashMap<>(); reportService.getSurveyCampaignUsage(filterCriteria.getEnterpriseId(),
	 * Arrays.asList(filterCriteria.getCampaignId()), campaignIdToUsageMap); if (campaignIdToUsageMap.containsKey(filterCriteria.getCampaignId())) {
	 * setSurveyCampaignUsageStats(campaignIdToUsageMap.get(filterCriteria.getCampaignId()), usageInfo); } }
	 * 
	 * else if (campaignType.equalsIgnoreCase(CampaignTypeEnum.PROMOTIONAL.getType())) { UsageFunnelReportResponse usageFunnelReportResponse =
	 * usageReportsService.getCustomUsageGraphReport(filterCriteria);
	 * 
	 * @SuppressWarnings("unchecked") List<CustomUsageReportResponse> usageResponseList = (List<CustomUsageReportResponse>)
	 * usageFunnelReportResponse.getUsageReportResponses(); setPromotionalCampaignUsageStats(usageResponseList.get(0), usageInfo); } }
	 */

	/*
	 * private void setSurveyCampaignUsageStats(CommunicationUsageStatsMessage communicationUsageStatsMessage,CampaignUsageInfo usageInfo) {
	 * usageInfo.setSent(communicationUsageStatsMessage.getSentSuccess()); usageInfo.setDelivered(communicationUsageStatsMessage.getDelivered());
	 * usageInfo.setOpened(communicationUsageStatsMessage.getOpenStartedCount());
	 * usageInfo.setClicked(communicationUsageStatsMessage.getClickedCompletedCount()); }
	 * 
	 * private void setReferralCampaignUsageStats(ReferralUsageReportResponse referralUsageReportResponse, CampaignUsageInfo usageInfo) { if
	 * (referralUsageReportResponse != null) { Long sent = referralUsageReportResponse.getSentInfo() != null ?
	 * referralUsageReportResponse.getSentInfo().getTotal() : 0; usageInfo.setSent(sent);
	 * 
	 * Long delivered = referralUsageReportResponse.getDeliveredInfo() != null ? referralUsageReportResponse.getDeliveredInfo().getTotal() : 0;
	 * usageInfo.setDelivered(delivered);
	 * 
	 * Long opened = referralUsageReportResponse.getOpenInfo() != null ? referralUsageReportResponse.getOpenInfo().getTotal() : 0;
	 * usageInfo.setOpened(opened);
	 * 
	 * Long clicked = referralUsageReportResponse.getSharedInfo() != null ? referralUsageReportResponse.getSharedInfo().getTotal() : 0;
	 * usageInfo.setClicked(clicked);
	 * 
	 * Long leads = referralUsageReportResponse.getReferralInfo() != null ? referralUsageReportResponse.getReferralInfo().getTotal() : 0;
	 * usageInfo.setLeads(leads); } }
	 * 
	 * private void setCxCampaignUsageStats(CXUsageReportResponse cxUsageReportResponse, CampaignUsageInfo usageInfo) { if (cxUsageReportResponse != null)
	 * { Long sent = cxUsageReportResponse.getSentInfo() != null ? cxUsageReportResponse.getSentInfo().getTotal() : 0; usageInfo.setSent(sent);
	 * 
	 * Long delivered = cxUsageReportResponse.getDeliveredInfo() != null ? cxUsageReportResponse.getDeliveredInfo().getTotal() : 0;
	 * usageInfo.setDelivered(delivered);
	 * 
	 * Long opened = cxUsageReportResponse.getOpenInfo() != null ? cxUsageReportResponse.getOpenInfo().getTotal() : 0; usageInfo.setOpened(opened);
	 * 
	 * Long clicked = cxUsageReportResponse.getSentimentClickInfo() != null ? cxUsageReportResponse.getSentimentClickInfo().getTotal() : 0;
	 * usageInfo.setClicked(clicked);
	 * 
	 * Long reviewClicks = cxUsageReportResponse.getClickInfo() != null ? cxUsageReportResponse.getClickInfo().getTotal() : 0;
	 * usageInfo.setReviewClicks(reviewClicks); }
	 * 
	 * }
	 * 
	 * private void setPromotionalCampaignUsageStats(CustomUsageReportResponse customUsageReportResponse, CampaignUsageInfo usageInfo) { if
	 * (customUsageReportResponse != null) { Long totalSent = customUsageReportResponse.getSentInfo() != null ?
	 * customUsageReportResponse.getSentInfo().getTotal() : 0; usageInfo.setSent(totalSent);
	 * 
	 * Long delivered = customUsageReportResponse.getDeliveredInfo() != null ? customUsageReportResponse.getDeliveredInfo().getTotal() : 0;
	 * usageInfo.setDelivered(delivered);
	 * 
	 * Long opened = customUsageReportResponse.getOpenInfo() != null ? customUsageReportResponse.getOpenInfo().getTotal() : 0;
	 * usageInfo.setOpened(opened); } }
	 * 
	 * private void setRRCampaignUsageStats(RRUsageReportResponse rrUsageReportResponse, CampaignUsageInfo usageInfo) { if (rrUsageReportResponse != null)
	 * { Long sent = rrUsageReportResponse.getSentInfo() != null ? rrUsageReportResponse.getSentInfo().getTotal() : 0; usageInfo.setSent(sent);
	 * 
	 * Long delivered = rrUsageReportResponse.getDeliveredInfo() != null ? rrUsageReportResponse.getDeliveredInfo().getTotal() : 0;
	 * usageInfo.setDelivered(delivered);
	 * 
	 * Long opened = rrUsageReportResponse.getOpenInfo() != null ? rrUsageReportResponse.getOpenInfo().getTotal() : 0; usageInfo.setOpened(opened);
	 * 
	 * Long clicked = rrUsageReportResponse.getClickInfo() != null ? rrUsageReportResponse.getClickInfo().getTotal() : 0; usageInfo.setClicked(clicked); }
	 * }
	 */

	private void prepareDripCampaignCondition(Campaign campaign, ManualCampaignResponse response) {
		DripCampaignCondition dripCampaignCondition = dripCampaignCachedService.getDripCampaignCondition(campaign.getId());
		DripCampaignSchedulingDetails dripCampaignSchedulingDetails = new DripCampaignSchedulingDetails();
		if (dripCampaignCondition == null) {
			dripCampaignCondition = prepareDefaultDripCampaignCondition(campaign);
		}
		dripCampaignSchedulingDetails.setAllowedDays(dripCampaignCondition.getAllowedDays());
		dripCampaignSchedulingDetails.setExclusionDates(dripCampaignCondition.getExclusionDates());
		dripCampaignSchedulingDetails.setSendTime(dripCampaignCondition.getSendTime());
		response.setSchedulingDetails(dripCampaignSchedulingDetails);
	}

	private DripCampaignCondition prepareDefaultDripCampaignCondition(Campaign campaign) {
		DripCampaignCondition dripCampaignCondition = new DripCampaignCondition();
		dripCampaignCondition.setAllowedDays(DayOfWeekEnum.getWeekDaysAsList());
		dripCampaignCondition.setExclusionDates(new ArrayList<String>());
		dripCampaignCondition.setSendTime(new SimpleDateFormat(TIME_FORMAT_1).format(DateTimeUtils.addMinutesToDate(campaign.getStartAt())));
		return dripCampaignCondition;
	}

	private CampaignEditInfo prepareCampaignEditDetails(Campaign campaign) {
		CampaignEditInfo editInfo = new CampaignEditInfo();
		editInfo.setEditedBy(campaign.getEditedBy());
		editInfo.setEditedOn(prepareDateAsPerTimeZone(campaign.getEditedOn(), "MMM dd, yyyy", campaign.getEnterpriseId()));
		return editInfo;
	}

	private void prepareMessengerOrAppointmentTabCampaignDataInResponse(Campaign campaign, ManualCampaignResponse response) {
		MessengerCampaign messengerCampaign = messengerCampaignService.getMessengerCampaignData(campaign.getId());
		if (messengerCampaign == null) {
			throw new CampaignException(ErrorCodes.INVALID_CAMPAIGN, "Invalid Messenger Campaign" + campaign.getId());
		}
		response.setFreeText(replaceUrlTokensInFreeText(campaign, messengerCampaign));
		response.setMessengerMediaInfo(getMediaForMessengerCampaign(messengerCampaign)); // single attachment is supported
	}

	private List<MessengerMediaInfo> getMediaForMessengerCampaign(MessengerCampaign messengerCampaign) {
		List<MessengerMediaInfo> messengerMediaInfos = CampaignUtils.convertJsonToMessengerMediaList(messengerCampaign.getMessengerMedia());
		if (!CollectionUtils.isEmpty(messengerMediaInfos)) {
			return new ArrayList<>(Arrays.asList(messengerMediaInfos.get(0)));
		}
		return messengerMediaInfos;
	}

	private String replaceUrlTokensInFreeText(Campaign campaign, MessengerCampaign messengerCampaign) {
		if (StringUtils.isNotEmpty(messengerCampaign.getFreeText()) && campaign.getType().equalsIgnoreCase(CampaignTypeEnum.PROMOTIONAL.getType())) {
			List<CustomCampaignUrl> customCampaignUrlList = customCampaignCachedService.getFreeTextCampaignUrl(campaign.getId());
			for (CustomCampaignUrl customCampaignUrl : customCampaignUrlList) {
				messengerCampaign.setFreeText(messengerCampaign.getFreeText().replace(customCampaignUrl.getUrlToken(), customCampaignUrl.getUrlValue()));
			}
		}
		return messengerCampaign.getFreeText();
	}

	private boolean isFreeTextCampaign(Campaign campaign) {
		if (campaign.getIsMessengerCampaign() != null && campaign.getIsMessengerCampaign() == 1 && campaign.getSmsTemplateId() != null) {
			Integer freeTextSmsTemplateId = getFreeTextSmsTemplateId();
			return campaign.getSmsTemplateId().equals(freeTextSmsTemplateId);
		}
		
		if (campaign.getIsAppointmentTabCampaign() != null && campaign.getIsAppointmentTabCampaign() == 1 && campaign.getSmsTemplateId() != null) {
			Integer freeTextSmsTemplateId = getFreeTextSmsTemplateId();
			return campaign.getSmsTemplateId().equals(freeTextSmsTemplateId);
		}
		
		return false;
	}

	private boolean isFreeTextCampaign(Campaign campaign, Integer freeTextSmsTemplateId) {
		if (campaign.getIsMessengerCampaign() != null && campaign.getIsMessengerCampaign() == 1 && campaign.getSmsTemplateId() != null) {
			return campaign.getSmsTemplateId().equals(freeTextSmsTemplateId);
		}
		
		if (campaign.getIsAppointmentTabCampaign() != null && campaign.getIsAppointmentTabCampaign() == 1 && campaign.getSmsTemplateId() != null) {
			return campaign.getSmsTemplateId().equals(freeTextSmsTemplateId);
		}
		
		return false;
	}

	private boolean isEmailTemplateDeleted(Integer templateId) {
		BusinessEmailTemplate businessEmailTemplate = businessEmailTemplateRepo.findFirstByEmailTemplateId(templateId);
		if (businessEmailTemplate != null) {
			return CoreUtils.isTrueForInteger(businessEmailTemplate.getIsDeleted());
		}
		return false;
	}

	private boolean isSmsTemplateDeleted(Integer templateId) {
		if (templateId != null) {
			BusinessSmsTemplate businessSmsTemplate = businessSMSTemplateRepo.findFirstById(templateId);
			if (businessSmsTemplate != null) {
				return CoreUtils.isTrueForInteger(businessSmsTemplate.getIsDeleted());
			}
		}
		return false;
	}

	@Override
	public CampaignCountsResponse getEnterpriseCampaignsCount(Integer enterpriseId) {
		CampaignCountsResponse response = new CampaignCountsResponse();
		List<CampaignCountMessage> campaignCounts = campaignRepo.getCampaignCountByEnterpriseId(enterpriseId);
		if (CollectionUtils.isNotEmpty(campaignCounts)) {
			for (CampaignCountMessage countmsg : campaignCounts) {
				if ("ongoing".equalsIgnoreCase(countmsg.getRunType())) {
					response.setOngoingCount(countmsg.getCount());
				} else {
					response.setManualCount(countmsg.getCount());
				}
			}
		}
		return response;
	}

	@Override
	public Boolean getDripCampaignFlag(Integer accountId) {
		BusinessEnterpriseEntity business = businessReadOnlyRepo.getBusinessByBid(accountId);
		if (business == null) {
			logger.error("getDripCampaignFlag : invalid business id {}", accountId);
			throw new CampaignException(ErrorCodes.INVALID_BUSINESS);
		}
		List<Integer> businessIds = new ArrayList<>();
		businessIds.addAll(businessReadOnlyRepo.findBusinessIdByBusinessIdOrEnterpriseId(accountId));

		Integer dripCampaignCount = campaignRepo.getDripCampaignCountByLocationIds(businessIds, new Date());
		logger.info("dripCampaignCount for account id {} is {}", accountId, dripCampaignCount);
		return (dripCampaignCount != null && dripCampaignCount > 0);
	}

	@Override
	public void updateContactUsEnabledFlagForTemplates(Integer emailTemplateId, Integer smsTemplateId, Integer contactUsEnabled) {
		businessEmailTemplateRepo.updateBusinessEmailTemplatesContactUsEnabled(emailTemplateId, contactUsEnabled);
		businessSMSTemplateRepo.updateBusinessSmsTemplatesContactUsEnabled(smsTemplateId, contactUsEnabled);
	}

	@Override
	public void updateUnsubscribeTextEnabledFlagForTemplates(Integer smsTemplateId, Integer enableUnsubscribeText) {
		businessSMSTemplateRepo.updateBusinessSmsTemplatesUnsubscribeTextEnabled(smsTemplateId, enableUnsubscribeText);
	}

	@Override
	public void updateLocationBrandingEnabledFlagForTemplates(Integer emailTemplateId, Integer smsTemplateId, Integer enableLocationBranding) {
		businessEmailTemplateRepo.updateBusinessEmailTemplatesLocationBrandingEnabled(emailTemplateId, enableLocationBranding);
		businessSMSTemplateRepo.updateBusinessSmsTemplatesLocationBrandingEnabled(smsTemplateId, enableLocationBranding);
	}

	@Override
	public void updateMmsEnabledFlagForTemplates(Integer smsTemplateId, Integer enableMms) {
		businessSMSTemplateRepo.updateBusinessSmsTemplatesMmsEnabled(smsTemplateId, enableMms);
	}

	@Override
	public void updateNoReplyEnabledFlagForTemplates(Integer emailTemplateId, Integer noReplyEnabled) {
		businessEmailTemplateRepo.updateBusinessEmailTemplatesNoReplyEnabled(emailTemplateId, noReplyEnabled);
	}

	@Override
	public void createOnGoingCampaignForMigration(Integer enterpriseId, Integer userId, Integer emailTemplateId, Integer smsTemplateId, CampaignStatusEnum status, String priority, String campaignType,
			Integer surveyId, List<String> lvlIds, String campaignNameSuffix) {
		if (!CampaignTypeEnum.SURVEY_REQUEST.getType().equalsIgnoreCase(campaignType)) {
			surveyId = null;
		} else if (CampaignTypeEnum.SURVEY_REQUEST.getType().equalsIgnoreCase(campaignType) && (surveyId == null || surveyId.equals(0))) {
			// no survey campaign is created if survey id is null.
			return;
		}
		Campaign campaign = onGoingCampaignForMigration(prepareOnGoingCampaignDataForMigration(enterpriseId, userId, emailTemplateId, smsTemplateId, campaignType, surveyId, campaignNameSuffix),
				status, priority, campaignType);
		CampaignCondition campaignCondition = prepareCampaignConditionForMigration(enterpriseId, userId, campaign.getId(), lvlIds);
		campaignCondition.setLvlAlias("loc");
		campaignCondition.setEvent(CampaignTriggerTypeEnum.CONTACT_ADDED.getType());
		campaignConditionRepo.saveAndFlush(campaignCondition);
		logger.info("Ongoign campaign : {}, created for emterprise {}, emailTemplate {}, smsTemplate {}, priority {}, campaignType {}, surveyId {}, lvlIds {}", campaign.getId(), enterpriseId,
				emailTemplateId, smsTemplateId, priority, campaignType, surveyId, lvlIds);
	}

	private Campaign prepareOnGoingCampaignDataForMigration(Integer enterpriseId, Integer userId, Integer emailTemplateId, Integer smsTemplateId, String campaignType, Integer surveyId,
			String campaignNameSuffix) {
		Campaign campaign = new Campaign();
		campaign.setEnterpriseId(enterpriseId);
		campaign.setName(getCampaignNameForMigration(CampaignUtils.getDefaultCampaignNameByType(campaignType), enterpriseId, campaignNameSuffix));
		campaign.setSmsTemplateId(smsTemplateId);
		campaign.setTemplateId(emailTemplateId);
		campaign.setSurveyId(surveyId);
		campaign.setIsDefault(0);
		campaign.setCreatedBy(userId);
		return campaign;
	}

	private Campaign onGoingCampaignForMigration(Campaign campaign, CampaignStatusEnum status, String priority, String campaignType) {
		campaign.setType(campaignType);
		campaign.setRunType(CampaignRunTypeEnum.ONGOING.getRunType());
		campaign.setStatus(status.getStatus());
		campaign.setPriorityOrder(status.getPriorityOrder());
		campaign.setSendReminder(0);
		campaign.setReminderCount(3);
		campaign.setReminderFrequency(2);
		campaign.setPriority(priority);
		return campaignRepo.saveAndFlush(campaign);
	}

	private CampaignCondition prepareCampaignConditionForMigration(Integer enterpriseId, Integer userId, Integer campaignId, List<String> lvlIds) {
		CampaignCondition campaignCondition = new CampaignCondition();
		campaignCondition.setEnterpriseId(enterpriseId);
		campaignCondition.setCampaignId(campaignId);
		campaignCondition.setUpdatedBy(userId);
		List<String> defaultContactSources = new ArrayList<>();
		defaultContactSources.add("dashboard");
		defaultContactSources.add("sftp");
		defaultContactSources.add("integration");
		defaultContactSources.add("api");
		campaignCondition.setContactSources(defaultContactSources);
		if (CollectionUtils.isNotEmpty(lvlIds)) {
			campaignCondition.setLvlIds(lvlIds);
		}
		return campaignCondition;
	}

	@Transactional(value = "campaignTransactionManager", readOnly = false)
	@Override
	public void updateReminderFlag(CampaignReminderTypeMessage campaignReminderTypeMessage) {
		if (CollectionUtils.isEmpty(campaignReminderTypeMessage.getCampaignIds())) {
			logger.error("updateReminderFlag : no campaignId found");
			throw new CampaignException(ErrorCodes.INVALID_REQUEST);
		}
		logger.info(" updateReminderFlag: the total Request Recieve for Campaign ids are :: {}", campaignReminderTypeMessage.getCampaignIds().size());
		int updatedRow = campaignRepo.updateCampaignReminderFlag(campaignReminderTypeMessage.getEnableReminder(), campaignReminderTypeMessage.getCampaignIds());
		logger.info(" updateReminderFlag: the total rows updated for Campaign are :: {}", updatedRow);
	}

	@Override
	public ReviewRequestResponseWrapper getCommunicationRestrictionReport(Integer businessId) {
		if (businessId == null) {
			logger.error("getCommunicationRestrictionReport : no businessId found");
			throw new CampaignException(ErrorCodes.INVALID_REQUEST);
		}
		List<ReviewRequestResponse> reviewRequestResponseList = reviewRequestRepo.findByBusinessIdAndFailure(businessId);
		
		List<Integer> customerIds = reviewRequestResponseList.stream().map(ReviewRequestResponse::getCutomerId).collect(Collectors.toList());
		
		List<CustomerInfoResponse> customerInfo = contactExternalService.getCustomersInfo(customerIds);
		
		mergeCustomerInfoAndRRInfo(reviewRequestResponseList, customerInfo);

		logger.info(" getCommunicationRestrictionReport: the total rows for communication restriction failure for businessId :: {} are :: {}", businessId, reviewRequestResponseList.size());
		ReviewRequestResponseWrapper reviewRequestResponseWrapper = new ReviewRequestResponseWrapper();

		reviewRequestResponseWrapper.setReviewRequestResponseList(reviewRequestResponseList);
		return reviewRequestResponseWrapper;
	}

	private void mergeCustomerInfoAndRRInfo(List<ReviewRequestResponse> reviewRequestResponseList, List<CustomerInfoResponse> customerInfo) {
		
		if (CollectionUtils.isEmpty(reviewRequestResponseList) || CollectionUtils.isEmpty(customerInfo)) {
			return;
		}
		Map<Integer, CustomerInfoResponse> customerMap = customerInfo.stream().collect(Collectors.toMap(CustomerInfoResponse::getId, c -> c));
		for (ReviewRequestResponse rr : reviewRequestResponseList) {
			CustomerInfoResponse customer = customerMap.get(rr.getCutomerId());
			if (customer == null)
				continue;
			rr.setEmail(customer.getEmail());
			rr.setPhone(customer.getPhone());
		}
	}

	@Transactional(value = "campaignTransactionManager", readOnly = false)
	@Override
	public Boolean updateDeleteStatus(Integer campaignId, Integer deleteFlag) {
		if (campaignId == null) {
			logger.error("updateDeleteFlag : no campaignId :: {} found", campaignId);
			throw new CampaignException(ErrorCodes.INVALID_REQUEST);
		}

		Campaign onGoingCampaign = campaignRepo.findFirstById(campaignId);
		if (onGoingCampaign == null) {
			logger.error("updateDeleteFlag : no campaign found with campaignId :: {}", campaignId);
			throw new CampaignException(ErrorCodes.INVALID_REQUEST);
		}
		if (onGoingCampaign.getStatus() == 0 || onGoingCampaign.getStatus() == 2 || onGoingCampaign.getStatus() == 3 || onGoingCampaign.getStatus() == 4 || onGoingCampaign.getStatus() == 5 || onGoingCampaign.getStatus() == 6) {
			Campaign oldCampaign = new Campaign(onGoingCampaign);
			Integer updatedRow = campaignRepo.updateCampaignDeleteFlag(deleteFlag, campaignId);
			logger.info("updateDeleteFlag: the total rows updated for CampaignId :: {} are :: {}", campaignId, updatedRow);
			if (CampaignRunTypeEnum.ONGOING.getRunType().equalsIgnoreCase(onGoingCampaign.getRunType())) {
				// deleting the mvel expression
				deleteMvelExpressionForCampaign(campaignId, onGoingCampaign.getEnterpriseId());
				evictOngoingCampaignsListCacheForEnterprise(onGoingCampaign.getEnterpriseId());
			}
			markDeleteCustomFieldAssociation(campaignId, "Campaign");
			// BIRD-50563 | User Access Setting Deletion Handling
			userAccessSettingsService.deleteCampaignAccessSetting(new CampaignUserAccessRequest(campaignId, null));
			
			// BIRD-72687 - Maintain audit of campaign modification(Async call)
			// Future case: For any change in the campaign/templates entities, consider its handling in auditing flow.
			if (updatedRow.intValue() > 0) {
				onGoingCampaign.setIsDeleted(deleteFlag);
				campaignModificationAuditService.prepareAndPublishCampaignModificationEvent(CampaignModificationAuditUtils.prepareAllCampaignDataDTO(oldCampaign, null, null),
						CampaignModificationAuditUtils.prepareAllCampaignDataDTO(onGoingCampaign, null, null), CoreUtils.getNullSafeInteger(MDC.get(Constants.USER_ID)),
						CampaignModificationUserActionEnum.DELETE.getUserActionType(), Instant.now().toEpochMilli(), onGoingCampaign.getEnterpriseId(),
						CoreUtils.getBooleanValueFromInteger(onGoingCampaign.getIsSplitCampaign()));
			}
			
			return updatedRow.intValue() > 0;
		} else {
			logger.info("updateDeleteFlag: can not be updated for Campaign status :: {} and campaignId :: {}", onGoingCampaign.getStatus(), campaignId);
			throw new CampaignException(ErrorCodes.CAMPAIGN_DELETE_ERROR, ErrorCodes.CAMPAIGN_DELETE_ERROR.getMessage());
		}

	}
	
	@Transactional(value = "campaignTransactionManager", readOnly = false)
	@Override
	public Boolean updateDeleteStatusForResellerCampaign(Integer resellerId, String type, Integer deleteFlag) {
		if (resellerId == null || StringUtils.isBlank(type)) {
			logger.error("Reseller id : {} or type : {} can't be null", resellerId, type);
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "Invalid Request");
		}
		
		List<Campaign> campaignList = campaignRepo.findByResellerIdAndTypeAndIsDeleted(resellerId, type, 0);
		Campaign onGoingCampaign=null;
		if (CollectionUtils.isEmpty(campaignList)) {
			logger.error("updateDeleteFlag : no campaign found with resellerId :: {} and type :: {}", resellerId, type);
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "Invalid Request");
		}
		onGoingCampaign=campaignList.get(0);
		if (onGoingCampaign.getStatus() == 0 || onGoingCampaign.getStatus() == 2 || onGoingCampaign.getStatus() == 3 || onGoingCampaign.getStatus() == 4 || onGoingCampaign.getStatus() == 5 || onGoingCampaign.getStatus() == 6) {
			Integer updatedRow = campaignRepo.updateCampaignDeleteFlag(deleteFlag, onGoingCampaign.getId());
			logger.info("updateDeleteFlag: the total rows updated for CampaignId :: {} are :: {}", onGoingCampaign.getId(), updatedRow);
			//return updatedRow.intValue() > 0;
			
			return true;
		} else {
			logger.info("updateDeleteFlag: can not be updated for Campaign status :: {} and campaignId :: {}", onGoingCampaign.getStatus(), onGoingCampaign.getId());
			throw new CampaignException(ErrorCodes.CAMPAIGN_DELETE_ERROR, ErrorCodes.CAMPAIGN_DELETE_ERROR.getMessage());
		}
		
	}

	@Transactional(value = "campaignTransactionManager", readOnly = false)
	@Override
	public void updateCampaignStatus() {
		LocalDate today = LocalDate.now();
		LocalDate beforeThreeDays = today.minusDays(3);
		Date dateBeforeThreeDays = java.sql.Date.valueOf(beforeThreeDays);
		int updatedRow = campaignRepo.updateCampaignStatus(3, 1, dateBeforeThreeDays);
		logger.info(" updateReminderFlag: the total rows updated for Campaign are :: {}", updatedRow);
	}
	
	@Override
	public void deleteCampaignCustomer(CustomerDeleteEventRequest request) {
		if (request == null || request.getcustomerId() == null || request.getbusinessId() == null) {
			logger.error("Invalid request received {}", request);
			return;
		}
		
		BusinessEnterpriseEntity business = cacheService.getBusinessById(request.getbusinessId());
		if (business == null) {
			logger.error("No valid business found");
			return;
		}
		List<CampaignContactInfo> campaignContactList = campaignRepo.getCustomerByBusinessIdAndStatus(request.getcustomerId(), CoreUtils.getSmbOrEnterpriseId(business),
				CampaignStatusEnum.DRAFT.getStatus());
		deleteCidsAndUpdateCampaign(campaignContactList, request.getcustomerId());
	}

	public void deleteCidsAndUpdateCampaign(List<CampaignContactInfo> campaignContactList, Integer cid) {
		for (CampaignContactInfo contactDetail : campaignContactList) {
			ArrayList<String> customerIds = new ArrayList<>(Arrays.asList(contactDetail.getCustomerIds().split(",")));
			customerIds.remove(Integer.toString(cid));
			campaignRepo.updateCustomerIds(contactDetail.getCampaignId(), StringUtils.join(customerIds, ",").isEmpty() ? null : StringUtils.join(customerIds, ","));
			campaignCachingService.evictCampaignById(contactDetail.getCampaignId());
			logger.info("customerId {} successfully deleted for campaignId {}", cid, contactDetail.getCampaignId());
		}
	}

	// Soft delete custom fields association when associatedObject is marked delete.
	private void markDeleteCustomFieldAssociation(Integer associatedObjectId, String associatedType) {
		int deleteCount = customFieldAssociationRepo.updateDeleteFlag(1, associatedObjectId, associatedType);
		logger.info("custom fields updateDeleteFlag: the total rows updated for associatedObjectId :: {}, associatedType :: {} are :: {}", associatedObjectId, associatedType, deleteCount);
	}

	private void deleteMvelExpressionForCampaign(Integer campaignId, Integer enterpriseId) {
		CampaignCondition condition = campaignConditionRepo.getByCampaignIdAndEnterpriseId(campaignId, enterpriseId);
		if (condition == null) {
			logger.info("no Condition found for campaign {} and ent id {}", campaignId, enterpriseId);
			return;
		}
		condition.setRuleExpression(null);
		condition.setMvelExpression(null);
		condition.setMvelParamsAndTypes(null);
		campaignConditionRepo.saveAndFlush(condition);
	}

	private List<Integer> getcustomerIdsFromAppointmentDetails(List<AppointmentDetailsResponse> appointmentDetails) {
		if(CollectionUtils.isEmpty(appointmentDetails)) {
			return Collections.emptyList();
		}
		return appointmentDetails.stream().map(AppointmentDetailsResponse::getCustomerId).filter(id -> id != null && id != 0).collect(Collectors.toList());
	}
	
	/**
	 * Gets the campaign audience based on the provided campaign request.
	 *
	 * @param campaignRequest The campaign request containing details about the campaign.
	 * @param fetchRecipientsFromAppointments TODO
	 * @return The list of customer IDs in the campaign audience.
	 */
	private List<Integer> getCampaignAudience(CreateManualCampaignRequest campaignRequest, Integer enterpriseId, boolean fetchRecipientsFromAppointments) {
		//Audience for Appointment forms manual campaign is derived from appointments list
		if(StringUtils.equalsAnyIgnoreCase(campaignRequest.getCampaignType(), Constants.APPOINTMENT_FORM_TYPE, Constants.APPOINTMENT_REMINDER_TYPE) || BooleanUtils.isTrue(fetchRecipientsFromAppointments)) {
			List<Integer> appointmentIds;
			if (campaignRequest.getAudienceFilterCampaign() == null) {
				appointmentIds = campaignRequest.getAppointmentAudience();
			} else {
				// Retrieve appointment IDs based on the audience filter
				appointmentIds = appointmentService.getCampaignAudienceAppointmentidsInBatch(campaignRequest.getAudienceFilterCampaign(), enterpriseId);
				campaignRequest.setAppointmentAudience(appointmentIds);
			}
			
			// Retrieve appointment details for the selected appointment IDs
			List<AppointmentDetailsResponse> appointmentDetails = appointmentFormService.getAppointmentDetailsInBatch(appointmentIds);
			// Extract customer IDs from the appointment details and return
			return getcustomerIdsFromAppointmentDetails(appointmentDetails);
			
		} else {
			// For non-appointment campaigns	
			return campaignRequest.getAudienceFilterCampaign() == null ? campaignRequest.getAudience()
					: customerService.getCampaignAudienceCidsInBatch(campaignRequest.getAudienceFilterCampaign());
		}
	}
	
	/**
	 * Retrieves campaign metadata for a given campaign ID.
	 * 
	 * @param campaignId The ID of the campaign for which metadata is to be retrieved.
	 * @return CampaignMetadata containing metadata information for the campaign.
	 * @throws CampaignException if the campaign ID is invalid or the campaign does not exist.
	 */
	@Override
	public CampaignMetadata getCampaignMetadataByCampaignId(Integer campaignId) {
		// Check if the campaign ID is null or zero
		if(campaignId == null || campaignId == 0) {
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "Invalid request");
		}
		// Retrieve the campaign object from the caching service based on the campaign ID
		Campaign campaign = campaignCachingService.getCampaignById(campaignId);
		// Check if the campaign object is null or type is not appointment form manual campaign, indicating an invalid campaign ID
		if(campaign == null || !appointmentFormService.isManualCampaignAppointmentFormRequest(campaign)) {
			throw new CampaignException(ErrorCodes.INVALID_CAMPAIGN, "Invalid campaignId");
		}
		// Check if a form URL is available for the campaign
		boolean formUrlPresent = appointmentFormService.isFormUrlAvailableForCampaign(campaign);
		
		CampaignMetadata metadata = new CampaignMetadata();
		metadata.setFormUrlPresent(formUrlPresent);
		return metadata;
	}

	/**
	 * Retrieves campaign details by campaign ID.
	 *
	 * @param campaignId The ID of the campaign to retrieve details for.
	 * @return CampaignInfoDTO containing the details of the campaign.
	 * @throws CampaignException if the campaign ID is null, zero, or if no valid campaign is found.
	 */
	@Override
	public CampaignInfoDTO getCampaignDetailsById(Integer campaignId) {
		// Check if the campaign ID is null or zero
		if (campaignId == null || campaignId == 0) {
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "Invalid request");
		}
		
		// Retrieve campaign from caching service
		Campaign campaign = campaignCachingService.getCampaignById(campaignId);
		if (campaign == null) {
			throw new CampaignException(ErrorCodes.INVALID_CAMPAIGN, "no valid campaign found");
		}
		
		// Return CampaignInfoDTO containing campaign details
		return new CampaignInfoDTO(campaign);
	}
	@Override
	public void updateCommRestrictionConfig(Integer enterpriseId, TemplateEnterpriseConfigUpdateMessage configUpdateMessage) {
		logger.info("Request received to update campaign account settings for enterprise : {} with Configuration : {}", enterpriseId,
				configUpdateMessage);
		if (configUpdateMessage == null) {
			return;
		}
		BusinessEnterpriseEntity enterprise = businessReadOnlyRepo.getValidBusinessByBid(enterpriseId);
		if (isValidEnterprise(enterprise)) {
			logger.error("Invalid enterpriseId received while updating template enterprise config");
			throw new TemplateConfigException(HttpStatus.BAD_REQUEST, ErrorCodes.INVALID_ENTERPRISE.getMessage());
		}
		List<CampaignAccountSettings> campaignAccountSettingsList = cacheService.getCampaignAccountSettings(enterpriseId);
		CampaignAccountSettings campaignAccountSettings;
		if (CollectionUtils.isNotEmpty(campaignAccountSettingsList)) {
			campaignAccountSettings = campaignAccountSettingsList.get(0);
		} else {
			CampaignAccountSettings defaultAccountSettings = templateConfigService.getCampaignAccountSettings(enterpriseId).get(0);
			campaignAccountSettings = new CampaignAccountSettings();
			campaignAccountSettings.setAccountId(enterpriseId);
			campaignAccountSettings.setMailResendFrequency(defaultAccountSettings.getMailResendFrequency());
			campaignAccountSettings.setIsDirectGoogleReviews(defaultAccountSettings.getIsDirectGoogleReviews());
		}
		if(StringUtils.equalsAnyIgnoreCase(configUpdateMessage.getRestrictionOnCampaignType(),CampaignCommRestrictionTypeEnum.SHARED_CAMPAIGN_TYPE.getRestrictionType(),
				CampaignCommRestrictionTypeEnum.IRRESPECTIVE_OF_CAMPAIGN_TYPE.getRestrictionType())){
			updateAllCampaignTypeValues(configUpdateMessage,campaignAccountSettings);
		}
		updateEnterpriseConfig(enterpriseId, configUpdateMessage, campaignAccountSettings);
	}

	@Override
	public CampaignAccountSettingsResponse getCommRestrictionConfigById(Integer enterpriseId) {
		logger.info("request for feteching comm restriction of enterprise {}", enterpriseId);
		BusinessEnterpriseEntity enterprise = businessReadOnlyRepo.getValidBusinessByBid(enterpriseId);

		if (isValidEnterprise(enterprise)) {
			logger.error("Invalid enterpriseId {} received while getting template enterprise config", enterpriseId);
			throw new TemplateConfigException(HttpStatus.BAD_REQUEST, ErrorCodes.INVALID_ENTERPRISE.getMessage());
		}

		List<CampaignAccountSettings> campaignAccountSettingsList = templateConfigService.getCampaignAccountSettings(enterpriseId);
		// if it is still empty then return error
		if (CollectionUtils.isEmpty(campaignAccountSettingsList)) {
			logger.error("For enterpriseId {} no config setting present", enterpriseId);
			throw new TemplateConfigException(HttpStatus.NOT_FOUND, ErrorCodes.NO_ENTERPRISE_CONFIG_SETTING.getMessage());
		}
		// getting the first entry
		CampaignAccountSettings campaignAccountSettings = campaignAccountSettingsList.get(0);
		return getCampaignAccountSettingReponse(campaignAccountSettings);
	}

	private CampaignAccountSettingsResponse getCampaignAccountSettingReponse(CampaignAccountSettings campaignAccountSetting){
		logger.info("Campaign Account settings data : {}",campaignAccountSetting);
		CampaignAccountSettingsResponse settingsResponse = new CampaignAccountSettingsResponse();
		settingsResponse.setEmailSmsLimit(campaignAccountSetting.getMailResendFrequency());
		settingsResponse.setCxCommFrequency(campaignAccountSetting.getCxCommFrequency());
		settingsResponse.setPromotionCommFrequency(campaignAccountSetting.getPromotionCommFrequency());
		settingsResponse.setSurveyCommFrequency(campaignAccountSetting.getSurveyCommFrequency());
		settingsResponse.setReferralCommFrequency(campaignAccountSetting.getReferralCommFrequency());
		settingsResponse.setReviewCommFrequency(campaignAccountSetting.getReviewCommFrequency());
		settingsResponse.setRestrictionAtAccountLevel(BooleanUtils.toBoolean(campaignAccountSetting.getRestrictionAtAccountLevel()));
		settingsResponse.setRestrictionOnCampaignType(campaignAccountSetting.getRestrictionOnCampaignType());
		settingsResponse.setIsCommRestrictionEnabled(BooleanUtils.toBoolean(campaignAccountSetting.getIsCommRestrictionEnabled()));
		settingsResponse.setSurveyRestrictionScope(SurveyRestrictionScopeTypeEnum.getSurveyScope(campaignAccountSetting.getSurveyRestrictionScope()));
		return settingsResponse;

	}

	private void updateAllCampaignTypeValues(TemplateEnterpriseConfigUpdateMessage configUpdateMessage, CampaignAccountSettings campaignAccountSettings){
		Integer frequency = configUpdateMessage.getEmailSmsLimit();
		configUpdateMessage.setCxCommFrequency(frequency);
		configUpdateMessage.setPromotionCommFrequency(frequency);
		configUpdateMessage.setReferralCommFrequency(frequency);
		configUpdateMessage.setReviewCommFrequency(frequency);
		configUpdateMessage.setSurveyCommFrequency(frequency);
	}

	private void updateEnterpriseConfig(Integer enterpriseId, TemplateEnterpriseConfigUpdateMessage configUpdateMessage, CampaignAccountSettings campaignAccountSettings) {
		logger.info("Updating campaign account settings for enterprise : {},existing mailResendFrequency : {} and updated mailResendFrequency : {} ", enterpriseId,
				campaignAccountSettings.getMailResendFrequency());

		campaignAccountSettings.setMailResendFrequency(configUpdateMessage.getEmailSmsLimit());
		campaignAccountSettings.setCxCommFrequency(configUpdateMessage.getCxCommFrequency());
		campaignAccountSettings.setPromotionCommFrequency(configUpdateMessage.getPromotionCommFrequency());
		campaignAccountSettings.setReferralCommFrequency(configUpdateMessage.getReferralCommFrequency());
		campaignAccountSettings.setReviewCommFrequency(configUpdateMessage.getReviewCommFrequency());
		campaignAccountSettings.setSurveyCommFrequency(configUpdateMessage.getSurveyCommFrequency());
		campaignAccountSettings.setIsCommRestrictionEnabled(BooleanUtils.toInteger(configUpdateMessage.getIsCommRestrictionEnabled()));
		campaignAccountSettings.setRestrictionAtAccountLevel(BooleanUtils.toInteger(configUpdateMessage.getRestrictionAtAccountLevel()));
		campaignAccountSettings.setRestrictionOnCampaignType(configUpdateMessage.getRestrictionOnCampaignType());
		campaignAccountSettings.setUpdatedBy(getUpdatedByStringForUser(CoreUtils.getNullSafeInteger(String.valueOf(MDC.get("uid")))));
		campaignAccountSettings.setSurveyRestrictionScope(SurveyRestrictionScopeTypeEnum.getSurveyScopeValue(configUpdateMessage.getSurveyRestrictionScope()));
		campaignAccountSettingsRepo.saveAndFlush(campaignAccountSettings);
		cacheService.evictCampaignAccountSettingsCache(enterpriseId);
	}

	private String getUpdatedByStringForUser(Integer userId) {
		StringBuilder builder = new StringBuilder();
		builder.append(userId);
		builder.append(";");
		builder.append(userId != null ? cacheService.getUserEmailById(userId) : null);
		return builder.toString();
	}

	private boolean isValidEnterprise(BusinessEnterpriseEntity enterprise) {
		return (enterprise == null || !(enterprise.isBusinessSMB() || enterprise.isEnterprise()));
	}
	
	/**
	 * 
	 * BIRD-53931 | API to pause and delete manual/automated campaigns.
	 * Used to mark those running campaigns as paused and deleted, whose feature flag for the given campaign type is disabled at account level.
	 * 
	 * @param request
	 * 
	 */
	@Override
	@Transactional(value = "chainedTransactionManager", rollbackFor = Exception.class)
	public void markCampaignPausedAndDeleted(CampaignUpdateStatusRequest request) {
		if (request == null || request.getCampaignId() == null || request.getStatus() == null) {
			logger.error("markCampaignPausedAndDeleted :: Invalid request recieved");
			return;
		}
		
		logger.info("markCampaignPausedAndDeleted :: Request received is {}", request);
		// 1.1 Fetch campaign
		Campaign campaign = campaignRepo.findFirstById(request.getCampaignId());
		if (campaign == null) {
			logger.error("markCampaignPausedAndDeleted :: Invalid campaign id {} received", request.getCampaignId());
			return;
		}
		
		// 1.3 Mark the campaign as deleted, if not deleted
		if (BooleanUtils.isTrue(CoreUtils.getBooleanValueFromInteger(campaign.getIsDeleted()))) {
			logger.info("markCampaignPausedAndDeleted :: Campaign {} is deleted", request.getCampaignId());
			return;
		}
		
		// 1.4 Update status and mark the campaign as deleted and update the entities related
		handlePauseAndDeleteCampaign(campaign, request.getStatus());
		
	}

	/**
	 * 
	 * BIRD-53931 | API to pause and delete manual/automated campaigns.
	 * This API 
	 * 1. Select valid pause/stopped status based upon campaign type
	 * 2. Update the object in db
	 * 3. Handle pause in case of drip campaign
	 * 4. Delete mvel expression in case of automation
	 * 5. Delete custom field association
	 * 6. Evict campaign caches
	 * 
	 * @param campaign, status
	 * 
	 */
	private void handlePauseAndDeleteCampaign(Campaign onGoingCampaign, Integer status) {
		
		Campaign oldCampaign = new Campaign(onGoingCampaign);
		// Set status in campaignStatus variable
		int campaignStatus = status;
		if (CampaignSchedulingTypeEnum.INSTANT.getType().equalsIgnoreCase(onGoingCampaign.getSchedulingType())) {
			// PAUSE works as STOP for instant campaigns
			campaignStatus = CampaignStatusEnum.STOPPED.getStatus();
		}
		
		// Update status, deleted flag in campaign object and update it in db
		onGoingCampaign.setStatus(campaignStatus);
		onGoingCampaign.setPriorityOrder(CampaignStatusEnum.getStatus(campaignStatus).getPriorityOrder());
		onGoingCampaign.setIsDeleted(1);
		campaignRepo.saveAndFlush(onGoingCampaign);
		
		// Handle Pause cases for drip campaign.
		if (CampaignSchedulingTypeEnum.DRIP.getType().equalsIgnoreCase(onGoingCampaign.getSchedulingType())) {
			handleDripPauseResume(campaignStatus, onGoingCampaign);
		}
		
		// Deleting the mvel expression
		if (CampaignRunTypeEnum.ONGOING.getRunType().equalsIgnoreCase(onGoingCampaign.getRunType())) {
			deleteMvelExpressionForCampaign(onGoingCampaign.getId(), onGoingCampaign.getEnterpriseId());
		}
		
		// Delete the custom fields associated with the campaign
		markDeleteCustomFieldAssociation(onGoingCampaign.getId(), "Campaign");
		
		// BIRD-50563 | User Access Setting Deletion Handling
		userAccessSettingsService.deleteCampaignAccessSetting(new CampaignUserAccessRequest(onGoingCampaign.getId(), null));
		
		// evict cache for campaign Id
		campaignCachingService.evictCampaignById(onGoingCampaign.getId());
		campaignCachingService.evictActiveOngoingCampaignCache(onGoingCampaign.getId());
		evictOngoingCampaignsListCacheForEnterprise(onGoingCampaign.getEnterpriseId());

		// BIRD-72687 - Maintain audit of campaign modification(Async call)
		// Future case: For any change in the campaign/templates entities, consider its handling in auditing flow.
		campaignModificationAuditService.prepareAndPublishCampaignModificationEvent(CampaignModificationAuditUtils.prepareAllCampaignDataDTO(oldCampaign, null, null),
				CampaignModificationAuditUtils.prepareAllCampaignDataDTO(onGoingCampaign, null, null), CoreUtils.getNullSafeInteger(MDC.get(Constants.USER_ID)),
				CampaignModificationUserActionEnum.DELETE.getUserActionType(), Instant.now().toEpochMilli(), onGoingCampaign.getEnterpriseId(),
				CoreUtils.getBooleanValueFromInteger(onGoingCampaign.getIsSplitCampaign()));
	}
	
	@Override
	public void evictOngoingCampaignsListCacheForEnterprise(Integer enterpriseId) {
		// BIRD-64796 | evict automation cache for both status 1(active) & 2(paused). keys : #accountId-1, #accountId-1-2
		campaignCachingService.evictOngoingCampaignByEnterprise(StringUtils.join(enterpriseId, "-", 1));
		campaignCachingService.evictOngoingCampaignByEnterprise(StringUtils.join(enterpriseId, "-", 1, "-", 2));
		campaignCachingService.evictCampaignListWithLocInfoCache(enterpriseId);
	}

	/**
	 * Method To Create Manual Campaign For Sending Bulk Messages To Appointments.
	 * Allowed communication types: promotion only
	 * Allowed priority types: ["email_sms", "sms_email", "sms", "email", "email_and_sms"]
	 * 
	 * @param createCampaignRequest
	 * @param enterpriseId
	 * @param userId
	 * @return
	 */
	@Override
	public CreateCampaignResponse createCampaignForBulkMessagesToAppointments(CreateMessengerCampaignRequest createCampaignRequest, Integer enterpriseId, String userId) {
		boolean suspiciousTemplate = contentScanningService.isSuspiciousMessengerCampaignContent(enterpriseId, Integer.valueOf(userId), getFreeTextSmsTemplateId(), createCampaignRequest);
		if (suspiciousTemplate) {
			logger.warn("Received suspicious content for appointment campaign for enterprise id {} user id {} and request {}", enterpriseId, userId, createCampaignRequest);
			return new CreateCampaignResponse(null, createCampaignRequest.getCampaignName());
		}
		validateAppointmentsTabCampaignCreationRequest(createCampaignRequest);
		// Handling Global Template Save
		GlobalTemplateValidationDTO validationDTO = globalTemplatesService.validateAndCreateBusinessSmsTemplate(createCampaignRequest.getIsGlobalTemplate(),
				createCampaignRequest.getSmsTemplateId(), enterpriseId, userId);
		createCampaignRequest.setSmsTemplateId(validationDTO != null ? validationDTO.getTemplateId() : createCampaignRequest.getSmsTemplateId());
		
		Campaign campaign = prepareGenericManualCampaignData(createCampaignRequest, enterpriseId, null, true);
		campaign.setName(getCampaignName(enterpriseId, createCampaignRequest.getCampaignName()));
		campaign.setCreatedBy(getUserId(userId));
		campaign.setIsAppointmentTabCampaign(1);
		//Handling for sending free text messages
		setTemplateIdForMessengerCampaign(createCampaignRequest, campaign);
		campaignRepo.saveAndFlush(campaign);
		try {
			if (!StringUtils.equalsAnyIgnoreCase(createCampaignRequest.getPriority(), CampaignPriorityEnum.EMAIL.getType())) {
				createMessengerCampaignEntry(createCampaignRequest, campaign);
			}
		} catch (Exception exception) {
			// required to delete entry in campaign table if error in creating messenger_campaign
			logger.error("exception occured while creating appointments tabs campaign for enterpriseId {}", enterpriseId);
			campaignRepo.deleteById(campaign.getId());
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "Invalid request");
		}
		campaignExecutionService.pushCampaignToKafkaExecution(campaign.getId());
		
		// BIRD-72687 - Maintain audit of campaign modification(Async call)
		// Future case: For any change in the campaign/templates entities, consider its handling in auditing flow.
		campaignModificationAuditService.prepareAndPublishCampaignModificationEvent(CampaignModificationAuditUtils.prepareAllCampaignDataDTO(null, null, null),
				CampaignModificationAuditUtils.prepareAllCampaignDataDTO(campaign, null, null), getUserId(userId), CampaignModificationUserActionEnum.CREATE.getUserActionType(),
				Instant.now().toEpochMilli(), enterpriseId, false);
		
		return new CreateCampaignResponse(campaign.getId(), campaign.getName());
	}
	
	/**
	 * Method to validate the campaign creation request for appointments tab campaign
	 * 
	 * @param createCampaignRequest
	 */
	private void validateAppointmentsTabCampaignCreationRequest(CreateMessengerCampaignRequest createCampaignRequest) {
		// For now only promotional type requests can be sent via appointments tab campaign
		if (BooleanUtils.isFalse(StringUtils.equalsAnyIgnoreCase(createCampaignRequest.getCampaignType(), CampaignTypeEnum.PROMOTIONAL.getType(), CampaignTypeEnum.APPOINTMENT_FORM.getType()))) {
			logger.warn("validateAppointmentsTabCampaignCreationRequest::CampaignSetupServiceImpl - Received request to create {} type appointments tab campaign!",
					createCampaignRequest.getCampaignType());
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "Provided campaign type not supported for appointments tab campaign!");
		}
		
		// No free text support provided for email requests
		if (StringUtils.equalsAnyIgnoreCase(createCampaignRequest.getPriority(), CampaignPriorityEnum.EMAIL.getType(), CampaignPriorityEnum.EMAIL_SMS.getType(),
				CampaignPriorityEnum.SMS_EMAIL.getType(), CampaignPriorityEnum.EMAIL_AND_SMS.getType()) && createCampaignRequest.getEmailTemplateId() == null) {
			logger.warn(
					"validateAppointmentsTabCampaignCreationRequest::CampaignSetupServiceImpl - Received NULL value received for email template id when request type in 'email'!");
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "For request type in email, email template id can't be null!");
		}
		
		if (StringUtils.equalsAnyIgnoreCase(createCampaignRequest.getPriority(), CampaignPriorityEnum.SMS.getType(), CampaignPriorityEnum.EMAIL_SMS.getType(),
				CampaignPriorityEnum.SMS_EMAIL.getType(), CampaignPriorityEnum.EMAIL_AND_SMS.getType()) && createCampaignRequest.getSmsTemplateId() == null
				&& StringUtils.isBlank(createCampaignRequest.getFreeText())) {
			logger.warn(
					"validateAppointmentsTabCampaignCreationRequest::CampaignSetupServiceImpl - Received blank values for both sms template id & free text when request type in 'sms'!");
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "For request type in sms, both sms template id & free text can't be null!");
		}
		
	}
}
