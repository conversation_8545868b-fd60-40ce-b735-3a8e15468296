package com.birdeye.campaign.service;

import java.io.IOException;
import java.util.Map;

import org.apache.commons.text.TextStringBuilder;

import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.BusinessProfileResponse;
import com.birdeye.campaign.dto.EmailTemplateMessage;
import com.birdeye.campaign.entity.BusinessEmailTemplate;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.Promotion;
import com.birdeye.campaign.entity.ReviewRequest;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.response.kontacto.KontactoDTO;

public interface CampaignEmailHtmlService {
	
	TextStringBuilder getHtmlDataMapForSurvey(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, BusinessEmailTemplate bizEmailTemplate, EmailTemplateMessage emailTemplate, TextStringBuilder content,
			String campaignType, Map<String, String> dataMap, BusinessProfileResponse businessInfo) throws Exception;
	
	void getHtmlDataMapForReviewRequest(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, boolean isReminder, BusinessEmailTemplate bizEmailTemplate,
			EmailTemplateMessage emailTemplate, TextStringBuilder content, String campaignType, Map<String, String> dataMap, BusinessProfileResponse businessInfo) throws IOException, CampaignException;
	
	void getHtmlDataMapForCXRequest(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, BusinessEmailTemplate bizEmailTemplate, EmailTemplateMessage emailTemplate,
			TextStringBuilder content, String campaignType, Map<String, String> dataMap, BusinessProfileResponse businessInfo) throws IOException;
	
	void getHtmlDataMapForPromotion(BusinessEnterpriseEntity business, Promotion promotionRequest, BusinessEmailTemplate bizEmailTemplate, EmailTemplateMessage emailTemplate,
			TextStringBuilder content, KontactoDTO customer, Map<String, String> dataMap, BusinessProfileResponse businessInfo) throws IOException;
	

	void getHtmlDataMapForReferral(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, BusinessEmailTemplate bizEmailTemplate, TextStringBuilder content, String campaignType,
			Map<String, String> dataMap, BusinessProfileResponse businessInfo) throws IOException;

	void getHTMLDataMapForAppointmentReminder(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, String campaignType, BusinessEmailTemplate bizEmailTemplate, TextStringBuilder content,
			Map<String, String> dataMap, BusinessProfileResponse businessInfo) throws IOException;

	void getHTMLDataMapForAppointmentRecall(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, String campaignType, BusinessEmailTemplate bizEmailTemplate, TextStringBuilder content,
			Map<String, String> htmlDataMap, BusinessProfileResponse businessInfo) throws IOException;
	
	void getHTMLDataMapForAppointmentForm(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, String campaignType, BusinessEmailTemplate bizEmailTemplate, TextStringBuilder content,
			Map<String, String> htmlDataMap, Campaign campaign, BusinessProfileResponse businessInfo) throws IOException;
	
}
