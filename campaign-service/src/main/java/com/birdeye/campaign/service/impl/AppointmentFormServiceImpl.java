package com.birdeye.campaign.service.impl;

import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.appointment.service.AppointmentService;
import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.dto.AppointmentInfoLiteDTO;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.TriggerFilter;
import com.birdeye.campaign.entity.BusinessEmailTemplate;
import com.birdeye.campaign.entity.BusinessSmsTemplate;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.CampaignCondition;
import com.birdeye.campaign.entity.ReviewRequest;
import com.birdeye.campaign.enums.AppointmentStatusEnum;
import com.birdeye.campaign.enums.CampaignRunTypeEnum;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.executor.services.CampaignCallable;
import com.birdeye.campaign.executor.services.CampaignExecutorService;
import com.birdeye.campaign.executor.services.ExecutorCommonService;
import com.birdeye.campaign.platform.constant.CampaignTriggerTypeEnum;
import com.birdeye.campaign.platform.constant.CampaignTypeEnum;
import com.birdeye.campaign.response.external.AppointmentDetailsResponse;
import com.birdeye.campaign.service.AppointmentFormService;
import com.birdeye.campaign.service.CampaignSetupCachingService;
import com.birdeye.campaign.service.TemplateHelperService;
import com.birdeye.campaign.service.dao.BusinessSmsTemplateDao;
import com.birdeye.campaign.template.email.v2.IEmailTemplateService;
import com.birdeye.campaign.trigger.service.CampaignTriggerEventsService;
import com.birdeye.campaign.utils.AppointmentReminderUtils;
import com.birdeye.campaign.utils.MvelEvaluationUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class AppointmentFormServiceImpl implements AppointmentFormService {
	
	private static final Logger				LOGGER	= LoggerFactory.getLogger(AppointmentFormServiceImpl.class);
	
	@Autowired
	private CampaignSetupCachingService		campaignSetupCachingService;
	
	@Autowired
	private CampaignTriggerEventsService	campaignTriggerEventsService;
	
	@Qualifier("campaignObjectMapper")
	@Autowired
	private ObjectMapper					objectMapper;
	
	@Autowired
	private AppointmentService				appointmentService;
	
	@Autowired
	private ExecutorCommonService		executorCommonService;
	
	@Autowired
	@Qualifier(Constants.CAMPAIGN_UI_COMPLETABLE_FUTURE_TASK_EXECUTOR)
	private ThreadPoolTaskExecutor		taskExecutor;

	@Autowired
	private IEmailTemplateService			emailTemplateService;
	
	@Autowired
	private BusinessSmsTemplateDao			businessSmsTemplateDao;
	
	@Autowired
	private TemplateHelperService			templateHelperService;
	
	@Override
	public void validateFormExecutionRequest(ReviewRequest reviewRequest, Integer appointmentId, Integer campaignId, Integer enterpriseId, BusinessEnterpriseEntity business) {
		AppointmentInfoLiteDTO appointmentInfo = appointmentService.getAppointmentLiteById(appointmentId.toString(), enterpriseId, true, false);
		if (appointmentInfo == null) {
			throw new CampaignException(ErrorCodes.NO_VALID_APOINTMENT_FOUND, ErrorCodes.NO_VALID_APOINTMENT_FOUND.getMessage());
		}
		
		if (StringUtils.equalsIgnoreCase(appointmentInfo.getFormFillStatus(), "complete")) {
			throw new CampaignException(ErrorCodes.FORM_ALREADY_FILLED, ErrorCodes.FORM_ALREADY_FILLED.getMessage());
		}
		
		// validate if form url is present in the template or not
		Campaign campaign = campaignSetupCachingService.getCampaignById(campaignId);
		String formUrl = getFormUrl(campaign, reviewRequest, enterpriseId);
		if (StringUtils.isBlank(formUrl)) {
			throw new CampaignException(ErrorCodes.FORM_URL_BLANK, ErrorCodes.FORM_URL_BLANK.getMessage());
		}
		
		// validate MVEL expression
		List<TriggerFilter> triggerFilters = getTriggerFilters(appointmentInfo, campaign.getTriggerType());
		CampaignCondition campaignCondition = campaignSetupCachingService.getCampaignConditionByCampaign(campaignId, enterpriseId);
		if (!MvelEvaluationUtils.evaluateTriggerMvelExpression(campaignCondition.getTriggerMvelExpression(), campaignCondition.getTriggerMvelParamsAndTypes(), triggerFilters, false, null)) {
			throw new CampaignException(ErrorCodes.NO_VALID_CAMPAIGN_CONDITION, ErrorCodes.NO_VALID_CAMPAIGN_CONDITION.getMessage());
		}
		
		// validate if appointment time is already passed for appointment_booked trigger
		if (StringUtils.equalsIgnoreCase(CampaignTriggerTypeEnum.APPOINTMENT_BOOKED.getType(), campaignCondition.getEvent())
				&& BooleanUtils.isFalse(isValidRequestExecutionTime(appointmentInfo.getStartTime()))) {
			throw new CampaignException(ErrorCodes.APPOINTMENT_TIME_PASSED, ErrorCodes.APPOINTMENT_TIME_PASSED.getMessage());
		}
		
		// check if current time is valid to execute request for before_appointment_date trigger
		if (StringUtils.equalsIgnoreCase(CampaignTriggerTypeEnum.BEFORE_APPOINTMENT_DATE.getType(), campaignCondition.getEvent())
				&& BooleanUtils.isFalse(AppointmentReminderUtils.isValidExecutionTimeRequest(appointmentInfo, business, campaignCondition, new ArrayList<>()))) {
			throw new CampaignException(ErrorCodes.CAMPAIGN_SCHEDULING_CONFIG_NOT_MATCHED, ErrorCodes.CAMPAIGN_SCHEDULING_CONFIG_NOT_MATCHED.getMessage());
		}
	}
	
	@SuppressWarnings("unchecked")
	private List<TriggerFilter> getTriggerFilters(AppointmentInfoLiteDTO appointmentInfo, String triggerType) {
		List<TriggerFilter> triggerFilters = new ArrayList<>();
		try {
			triggerFilters = campaignTriggerEventsService.extractAndPopulateTriggerFilterValues(objectMapper.convertValue(appointmentInfo, Map.class), triggerType);
		} catch (JsonProcessingException e) {
			throw new CampaignException(ErrorCodes.NO_VALID_CAMPAIGN_CONDITION, ErrorCodes.NO_VALID_CAMPAIGN_CONDITION.getMessage());
		}
		return triggerFilters;
	}
	
	@Override
	public List<AppointmentDetailsResponse> getAppointmentDetailsInBatch(List<Integer> appointmentIds) {
		if(CollectionUtils.isEmpty(appointmentIds)) {
			return new ArrayList<>();
		}
		List<AppointmentDetailsResponse> appointmentDetails = new ArrayList<>();
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(taskExecutor);
		int batchSize = Integer.parseInt(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("campaign.audience.appointment.batch.size", "5000"));
		List<List<Integer>> appointmentIdsBatch = ListUtils.partition(appointmentIds, batchSize);
		
		for (List<Integer> aIds : appointmentIdsBatch) {
			executorService.submit(populateAppointmentDetails(aIds, appointmentDetails));
		}
		try {
			executorCommonService.executeTasks(executorService);
		} catch (Exception exe) {
			LOGGER.error("getAppointmentAudienceCidsInBatch - Error while executing batch concurrent task {}", ExceptionUtils.getStackTrace(exe));
		}
		
		return appointmentDetails;
		
	}
	
	private CampaignCallable<Boolean> populateAppointmentDetails(List<Integer> appointmentIds, List<AppointmentDetailsResponse> appointmentDetails) {
		return new CampaignCallable<Boolean>("Appointment_Audience_Task") {
			@Override
			public Boolean doCall() throws IOException {
				Map<String, List<AppointmentDetailsResponse>> appointmentDetailsResponse = appointmentService.getAllAppointmentsById(appointmentIds, true, false);
				if (appointmentDetailsResponse == null || CollectionUtils.isEmpty(appointmentDetailsResponse.get("appointmentDetails"))) {
					LOGGER.error("populateAppointmentDetails : empty/no appointmentDeails response from Appoitment for appintmentIds {}", appointmentIds);
					return true;
				}
				appointmentDetails.addAll(appointmentDetailsResponse.get("appointmentDetails"));
				return true;
			}
		};
	}
	
	@Override
	public String getFormUrl(Campaign campaign, ReviewRequest reviewRequest, Integer enterpriseId) {
		if (Constants.TEMPLATE_BASE_TYPE_EMAIL.equalsIgnoreCase(reviewRequest.getSource())) {
			BusinessEmailTemplate bizEmailTemplate = emailTemplateService.getBusinessEmailTemplateByEmailTemplateIdAndEnterpriseId(enterpriseId, reviewRequest.getTemplateId());
			if (bizEmailTemplate == null) {
				LOGGER.info("Business Email Template not found for review request {}", reviewRequest.getId());
				throw new CampaignException(ErrorCodes.INVALID_TEMPLATE, "No Email template configured");
			}
			return bizEmailTemplate.getFormUrl();
		} else {
			BusinessSmsTemplate smsTemplate = businessSmsTemplateDao.getSMSTemplateByEnterpriseIdAndTemplateId(enterpriseId, reviewRequest.getTemplateId());
			
			Integer isAppointmentCampaign = (campaign != null && campaign.getIsAppointmentTabCampaign() != null) ? campaign.getIsAppointmentTabCampaign() : 0;
			if (smsTemplate == null && isAppointmentCampaign == 1) {
				BusinessSmsTemplate freeTextSmsTemplate = templateHelperService.getFreeTextSmsTemplateId();
				if (freeTextSmsTemplate != null && reviewRequest.getTemplateId().equals(freeTextSmsTemplate.getId())) {
					smsTemplate = freeTextSmsTemplate;
				}
			}
			
			if (smsTemplate == null) {
				throw new CampaignException(ErrorCodes.INVALID_TEMPLATE, "No SMS template configured");
			}
			return smsTemplate.getFormUrl();
		}
	}
	
	private boolean isValidRequestExecutionTime(Long appointmentStartTimeEpochMillis) {
		ZonedDateTime zonedAppointmentStartTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(appointmentStartTimeEpochMillis), ZoneId.of("UTC"));
		ZonedDateTime currentTime = ZonedDateTime.ofInstant(Instant.now(), ZoneId.of("UTC"));
		Duration durationBeforeAppointment = Duration.between(currentTime, zonedAppointmentStartTime);
		return !durationBeforeAppointment.isNegative();
	}
	
	@Override
	public void validateManualAppointmentFormExecutionRequest(ReviewRequest reviewRequest, Integer appointmentId, Campaign campaign) {
		AppointmentInfoLiteDTO appointmentInfo = appointmentService.getAppointmentLiteById(appointmentId.toString(), campaign.getEnterpriseId(), true, true);
		if (appointmentInfo == null) {
			throw new CampaignException(ErrorCodes.NO_VALID_APOINTMENT_FOUND, ErrorCodes.NO_VALID_APOINTMENT_FOUND.getMessage());
		}
		LOGGER.info("Received appointment details for appointment Id : {} and details : {}", appointmentId, appointmentInfo);
		// Check if communication should be skipped in case the campaign's "skip future appointment" feature is enabled
		// and there is a future appointment associated with the current appointment.
		// If enabled, then the communication should be skipped.
		if(campaign.getSkipFutureAppointment() == 1 && BooleanUtils.isTrue(appointmentInfo.getFutureAppointmentPresent())) {
			LOGGER.info("Received future flag : {} for appointment id : {}", appointmentInfo.getFutureAppointmentPresent(), appointmentId);
			throw new CampaignException(ErrorCodes.FUTURE_APPOINTMENT_BOOKED, ErrorCodes.FUTURE_APPOINTMENT_BOOKED.getMessage());
		}
		
		// validate if appointment form is already filled
		if (StringUtils.equalsIgnoreCase(appointmentInfo.getFormFillStatus(), "complete")) {
			throw new CampaignException(ErrorCodes.FORM_ALREADY_FILLED, ErrorCodes.FORM_ALREADY_FILLED.getMessage());
		}
		
		// validate if appointment status is booked or confirmed
		if (!StringUtils.equalsAnyIgnoreCase(appointmentInfo.getAppointmentStatus(), AppointmentStatusEnum.CONFIRMED.getType(), AppointmentStatusEnum.BOOKED.getType())) {
			throw new CampaignException(ErrorCodes.INVALID_APPOINTMENT_STATUS, ErrorCodes.INVALID_APPOINTMENT_STATUS.getMessage());
		}
		
		// validate if appointment time is already passed
		if (BooleanUtils.isFalse(isValidRequestExecutionTime(appointmentInfo.getStartTime()))) {
			throw new CampaignException(ErrorCodes.APPOINTMENT_TIME_PASSED, ErrorCodes.APPOINTMENT_TIME_PASSED.getMessage());
		}
		
		// validate if form url is present in the template or not
		String formUrl = getFormUrl(campaign, reviewRequest, campaign.getEnterpriseId());
		if (StringUtils.isBlank(formUrl)) {
			throw new CampaignException(ErrorCodes.FORM_URL_BLANK, ErrorCodes.FORM_URL_BLANK.getMessage());
		}
	}
	
	@Override
	public boolean validateManualCustomAppointmentFormExecutionRequest(ReviewRequest reviewRequest, Integer appointmentId, Campaign campaign) {
		AppointmentInfoLiteDTO appointmentInfo = appointmentService.getAppointmentLiteById(appointmentId.toString(), campaign.getEnterpriseId(), true, true);
		if (appointmentInfo == null) {
			throw new CampaignException(ErrorCodes.NO_VALID_APOINTMENT_FOUND, ErrorCodes.NO_VALID_APOINTMENT_FOUND.getMessage());
		}
		LOGGER.info("Received appointment details for appointment Id : {} and details : {}", appointmentId, appointmentInfo);
		
		// Check if communication should be skipped in case the campaign's "skip future appointment" feature is enabled
		// and there is a future appointment associated with the current appointment.
		// If enabled, then the communication should be skipped.
		if(campaign.getSkipFutureAppointment() == 1 && BooleanUtils.isTrue(appointmentInfo.getFutureAppointmentPresent())) {
			LOGGER.info("Received future flag : {} for appointment id : {}", appointmentInfo.getFutureAppointmentPresent(), appointmentId);
			throw new CampaignException(ErrorCodes.FUTURE_APPOINTMENT_BOOKED, ErrorCodes.FUTURE_APPOINTMENT_BOOKED.getMessage());
		}
		
//		// validate if appointment status is booked or confirmed
//		if (!StringUtils.equalsAnyIgnoreCase(appointmentInfo.getAppointmentStatus(), AppointmentStatusEnum.CONFIRMED.getType(), AppointmentStatusEnum.BOOKED.getType())) {
//			throw new CampaignException(ErrorCodes.INVALID_APPOINTMENT_STATUS, ErrorCodes.INVALID_APPOINTMENT_STATUS.getMessage());
//		}
//		
//		// validate if appointment time is already passed
//		if (BooleanUtils.isFalse(isValidRequestExecutionTime(appointmentInfo.getStartTime()))) {
//			throw new CampaignException(ErrorCodes.APPOINTMENT_TIME_PASSED, ErrorCodes.APPOINTMENT_TIME_PASSED.getMessage());
//		}
		return false;
	}
	
	@Override
	public boolean validateCustomFormExecutionRequest(ReviewRequest reviewRequest, Integer appointmentId, Integer campaignId, Integer enterpriseId, BusinessEnterpriseEntity business) {
		AppointmentInfoLiteDTO appointmentInfo = appointmentService.getAppointmentLiteById(appointmentId.toString(), enterpriseId, true, false);

		if (appointmentInfo == null) {
			throw new CampaignException(ErrorCodes.NO_VALID_APOINTMENT_FOUND, ErrorCodes.NO_VALID_APOINTMENT_FOUND.getMessage());
		}
		
		Campaign campaign = campaignSetupCachingService.getCampaignById(campaignId);
		
		// validate MVEL expression
		List<TriggerFilter> triggerFilters = getTriggerFilters(appointmentInfo, campaign.getTriggerType());
		CampaignCondition campaignCondition = campaignSetupCachingService.getCampaignConditionByCampaign(campaignId, enterpriseId);
		if (!MvelEvaluationUtils.evaluateTriggerMvelExpression(campaignCondition.getTriggerMvelExpression(), campaignCondition.getTriggerMvelParamsAndTypes(), triggerFilters, false, null)) {
			throw new CampaignException(ErrorCodes.NO_VALID_CAMPAIGN_CONDITION, ErrorCodes.NO_VALID_CAMPAIGN_CONDITION.getMessage());
		}
		
		// validate if appointment time is already passed for appointment_booked trigger
		if (StringUtils.equalsIgnoreCase(CampaignTriggerTypeEnum.APPOINTMENT_BOOKED.getType(), campaignCondition.getEvent())
				&& BooleanUtils.isFalse(isValidRequestExecutionTime(appointmentInfo.getStartTime()))) {
			throw new CampaignException(ErrorCodes.APPOINTMENT_TIME_PASSED, ErrorCodes.APPOINTMENT_TIME_PASSED.getMessage());
		}
		
		// check if current time is valid to execute request for before_appointment_date trigger
		if (StringUtils.equalsIgnoreCase(CampaignTriggerTypeEnum.BEFORE_APPOINTMENT_DATE.getType(), campaignCondition.getEvent())
				&& BooleanUtils.isFalse(AppointmentReminderUtils.isValidExecutionTimeRequest(appointmentInfo, business, campaignCondition, new ArrayList<>()))) {
			throw new CampaignException(ErrorCodes.CAMPAIGN_SCHEDULING_CONFIG_NOT_MATCHED, ErrorCodes.CAMPAIGN_SCHEDULING_CONFIG_NOT_MATCHED.getMessage());
		}
		return false;
	}
	
	/**
	 * Checks if an appointment request is a general one based on the provided inputs.
	 * @return True if the appointment request is a general one, false otherwise.
	 */
	@Override
	public boolean isManualCampaignCustomAppointmentRequest(Campaign campaign, ReviewRequest reviewRequest, Integer enterpriseId) {
		// Get the URL for the appointment form
		String formUrl = getFormUrl(campaign, reviewRequest, enterpriseId);
		// Check if the form URL is empty or null and type is appointmetn form, indicating a custom appointment request.
		return campaign != null && StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.APPOINTMENT_FORM.getType()) 
				&& StringUtils.equalsIgnoreCase(campaign.getRunType(), CampaignRunTypeEnum.MANUAL.getRunType()) && StringUtils.isEmpty(formUrl) ? true : false;
	}
	
	@Override
	public String getFormUrl(Integer templateId, String source, Integer enterpriseId, Integer isAppointmentCampaign) {
		if (Constants.TEMPLATE_BASE_TYPE_EMAIL.equalsIgnoreCase(source)) {
			// Fetch the business email template using the provided templateId and enterpriseId
			BusinessEmailTemplate bizEmailTemplate = emailTemplateService.getBusinessEmailTemplateByEmailTemplateIdAndEnterpriseId(enterpriseId, templateId);
			 // If no email template is found, log the error and return null
			if (bizEmailTemplate == null) {
				LOGGER.info("Business Email Template not found for templateId {}, enterpriseId {}", templateId, enterpriseId);
				return null;
			}
			return bizEmailTemplate.getFormUrl();
		} else {
			// Since the source is not an email template, assume it's an SMS template
	        // Fetch the business SMS template using the provided templateId and enterpriseId
			BusinessSmsTemplate smsTemplate = businessSmsTemplateDao.getSMSTemplateByEnterpriseIdAndTemplateId(enterpriseId, templateId);
			
			if (smsTemplate == null && (isAppointmentCampaign != null && isAppointmentCampaign == 1)) {
				BusinessSmsTemplate freeTextSmsTemplate = templateHelperService.getFreeTextSmsTemplateId();
				if (freeTextSmsTemplate != null && templateId.equals(freeTextSmsTemplate.getId())) {
					smsTemplate = freeTextSmsTemplate;
				}
					
			}
			
			// If no SMS template is found, throw a CampaignException
			if (smsTemplate == null) {
				LOGGER.info("Business SMS Template not found for templateId {}, enterpriseId {}", templateId, enterpriseId);
				return null;
			}
			return smsTemplate.getFormUrl();
		}
	}
	
	/**
	 * Checks if a form URL is available for a given campaign.
	 *
	 * @param campaign The campaign for which to check the form URL availability.
	 * @return True if at least one form URL (email or SMS) is available, otherwise false.
	 */
	@Override
	public boolean isFormUrlAvailableForCampaign(Campaign campaign) {
		if(campaign == null) {
			return false;
		}
		// Get the form URL for the email template associated with the campaign.
		String emailFormUrl = getFormUrl(campaign.getTemplateId(), Constants.TEMPLATE_BASE_TYPE_EMAIL, campaign.getEnterpriseId(), campaign.getIsAppointmentTabCampaign());
		// Get the form URL for the SMS template associated with the campaign.
	    String smsFormUrl = getFormUrl(campaign.getSmsTemplateId(), Constants.TEMPLATE_BASE_TYPE_SMS, campaign.getEnterpriseId(), campaign.getIsAppointmentTabCampaign());
	    // Check if either the email form URL or the SMS form URL is not empty.
	    return StringUtils.isNotEmpty(emailFormUrl) || StringUtils.isNotEmpty(smsFormUrl);
		
	}
	
	/**
	 * Checks given campaign is a manual appointment form request .
	 *
	 * @param campaign The campaign object to be checked.
	 * @return True if the campaign is a manual appointment form request, false otherwise.
	 */
	@Override
	public boolean isManualCampaignAppointmentFormRequest(Campaign campaign) {
		//return True if the campaign is a manual appointment form request, false otherwise.
		return campaign != null
	            && StringUtils.equalsIgnoreCase(campaign.getType(), CampaignTypeEnum.APPOINTMENT_FORM.getType())
	            && StringUtils.equalsIgnoreCase(campaign.getRunType(), CampaignRunTypeEnum.MANUAL.getRunType());
	}
	
	/**
	 * Checks whether the given review request qualifies for a quick send of a custom appointment request.
	 * A quick send is possible if the following conditions are met:
	 * 1. The campaign is null.
	 * 2. The review request type is an appointment form.
	 * 3. The campaign ID in the review request is null.
	 * 4. The appointment form URL is empty.
	 *
	 * @return True if the conditions for quick send of a custom appointment request are met, otherwise false.
	 */
	@Override
	public boolean isQuickSendCustomAppointmentRequest(Campaign campaign, ReviewRequest reviewRequest, Integer enterpriseId) {
		// Get the URL for the appointment form
		String formUrl = getFormUrl(campaign, reviewRequest, enterpriseId);
		
		return campaign == null && StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.APPOINTMENT_FORM.getType()) 
				 && reviewRequest.getCampaignId() == null && StringUtils.isEmpty(formUrl) ? true : false;
	}
	
}
