package com.birdeye.campaign.service;

import com.birdeye.campaign.request.CampaignUsageInfo;
import com.birdeye.campaign.request.CampaignsFilterCriteria;

/**
 * Service to handle campaign specific requests
 * 
 * <AUTHOR>
 *
 */
public interface CampaignUsageService {

	public void prepareCampaignUsageReport(CampaignsFilterCriteria filterCriteria, CampaignUsageInfo usageInfo, String campaignType);

}
