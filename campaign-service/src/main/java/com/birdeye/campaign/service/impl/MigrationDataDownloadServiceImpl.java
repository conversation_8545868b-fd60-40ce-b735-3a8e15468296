/**
 * @file_name MigrationDataDownloadServiceImpl.java
 * @created_date 13 Apr 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.service.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.sql.DataSource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.amazon.service.AmazonUploadService;
import com.birdeye.campaign.dto.FileDownloadResource;
import com.birdeye.campaign.entity.BusinessDeeplinkPriority;
import com.birdeye.campaign.entity.BusinessEmailTemplate;
import com.birdeye.campaign.entity.BusinessSmsTemplate;
import com.birdeye.campaign.entity.EmailTemplate;
import com.birdeye.campaign.excel.dto.CXEmailTemplateDTO;
import com.birdeye.campaign.excel.dto.CXSMSTemplateDTO;
import com.birdeye.campaign.excel.dto.PromotionEmailTemplateDTO;
import com.birdeye.campaign.excel.dto.RREmailTemplateDTO;
import com.birdeye.campaign.excel.dto.RRSMSTemplateDTO;
import com.birdeye.campaign.excel.dto.SurveyEmailTemplateDTO;
import com.birdeye.campaign.excel.dto.SurveySMSTemplateDTO;
import com.birdeye.campaign.platform.constant.TemplateTypeEnum;
import com.birdeye.campaign.platform.readonly.repository.BusinessReadOnlyRepo;
import com.birdeye.campaign.repository.BusinessDeeplinkPriorityRepo;
import com.birdeye.campaign.repository.BusinessEmailTemplateRepo;
import com.birdeye.campaign.repository.BusinessSMSTemplateRepo;
import com.birdeye.campaign.repository.EmailTemplateRepo;
import com.birdeye.campaign.workbook.additional.service.CustomExcelUtils;

/**
 * @file_name MigrationDataDownloadServiceImpl.java
 * @created_date 13 Apr 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@Service("migrationDataDownloadService")
public class MigrationDataDownloadServiceImpl implements MigrationDataDownloadService {
	
	@Autowired
	private BusinessReadOnlyRepo					businessReadOnlyRepo;
	
	@Autowired
	private BusinessEmailTemplateRepo		businessEmailTemplateRepo;
	
	@Autowired
	private BusinessSMSTemplateRepo			businessSMSTemplateRepo;
	
	@Autowired
	private EmailTemplateRepo				emailTemplateRepo;
	
	@Autowired
	private BusinessDeeplinkPriorityRepo	deeplinkPriorityRepo;
	
	@Autowired
	private AmazonUploadService				uploadService;
	
	@Override
	public FileDownloadResource getExcelSheet(Integer businessId) throws Exception {
		List<Integer> businessIdsList = businessReadOnlyRepo.findBusinessIdByBusinessIdOrEnterpriseId(businessId);
		if (CollectionUtils.isEmpty(businessIdsList)) {
			throw new Exception("Invalid business IDs");
		}
		businessIdsList.removeIf(Objects::isNull);
		Set<Integer> businessIds = Optional.ofNullable(businessIdsList).map(HashSet::new).orElse(null);
		List<BusinessEmailTemplate> rrEmailTemplates = businessEmailTemplateRepo.findByBusinessIdsAndType(businessIds, TemplateTypeEnum.REVIEW_REQUEST_NEW.getName());
		List<BusinessEmailTemplate> cxEmailTemplates = businessEmailTemplateRepo.findByBusinessIdsAndType(businessIds, TemplateTypeEnum.CUSTOMER_EXPERIENCE.getName());
		List<BusinessEmailTemplate> surveyEmailTemplates = businessEmailTemplateRepo.findByBusinessIdsAndType(businessIds, TemplateTypeEnum.SURVEY_REQUEST.getName());
		List<BusinessEmailTemplate> promotionEmailTemplates = businessEmailTemplateRepo.findByBusinessIdsAndType(businessIds, TemplateTypeEnum.PROMOTION.getName());
		
		List<BusinessSmsTemplate> rrSMSTemplates = businessSMSTemplateRepo.findByBusinessIdInAndType(businessIdsList, TemplateTypeEnum.REVIEW_REQUEST_SMS.getName());
		List<BusinessSmsTemplate> cxSMSTemplates = businessSMSTemplateRepo.findByBusinessIdInAndType(businessIdsList, TemplateTypeEnum.CUSTOMER_EXPERIENCE_SMS.getName());
		List<BusinessSmsTemplate> surveySMSTemplates = businessSMSTemplateRepo.findByBusinessIdInAndType(businessIdsList, TemplateTypeEnum.SURVEY_REQUEST_SMS.getName());
		
		List<RREmailTemplateDTO> rrEmails = rrEmailTemplates.stream().map(bet -> getRREmailTemplateDTO(bet)).collect(Collectors.toList());
		List<CXEmailTemplateDTO> cxEmails = cxEmailTemplates.stream().map(bet -> getCXEmailTemplateDTO(bet)).collect(Collectors.toList());
		List<SurveyEmailTemplateDTO> surveyEmails = surveyEmailTemplates.stream().map(bet -> getSurveyEmailTemplateDTO(bet)).collect(Collectors.toList());
		List<PromotionEmailTemplateDTO> promotionEmails = promotionEmailTemplates.stream().map(bet -> getPromotionEmailTemplateDTO(bet)).collect(Collectors.toList());
		
		List<RRSMSTemplateDTO> rrSMSs = rrSMSTemplates.stream().map(bst -> getRRSMSTemplateDTO(bst)).collect(Collectors.toList());
		List<CXSMSTemplateDTO> cxSMSs = cxSMSTemplates.stream().map(bst -> getCXSMSTemplateDTO(bst)).collect(Collectors.toList());
		List<SurveySMSTemplateDTO> surveySMSs = surveySMSTemplates.stream().map(bst -> getSurveySMSTemplateDTO(bst)).collect(Collectors.toList());
		
		String fName = StringUtils.join(businessId, "-", String.valueOf(System.currentTimeMillis()), ".xlsx");
		String fileName = StringUtils.join("/tmp/", fName);
		CustomExcelUtils.writeToExcelInMultiSheets(fileName, "RR Email Templates", rrEmails);
		CustomExcelUtils.writeToExcelInMultiSheets(fileName, "CX Email Templates", cxEmails);
		CustomExcelUtils.writeToExcelInMultiSheets(fileName, "Survey Email Templates", surveyEmails);
		CustomExcelUtils.writeToExcelInMultiSheets(fileName, "Promotion Email Templates", promotionEmails);
		
		CustomExcelUtils.writeToExcelInMultiSheets(fileName, "RR SMS Templates", rrSMSs);
		CustomExcelUtils.writeToExcelInMultiSheets(fileName, "CX SMS Templates", cxSMSs);
		CustomExcelUtils.writeToExcelInMultiSheets(fileName, "Survey SMS Templates", surveySMSs);
		return new FileDownloadResource(uploadService.uploadFileAndGetDownloadLink(fileName, fName, ".xlsx"));
		
	}
	
	private RREmailTemplateDTO getRREmailTemplateDTO(BusinessEmailTemplate bet) {
		EmailTemplate et = getEmailTemplate(bet);
		RREmailTemplateDTO dto = new RREmailTemplateDTO();
		dto.setLocationId(String.valueOf(bet.getBusinessId()));
		dto.setTemplateId(String.valueOf(bet.getEmailTemplateId()));
		dto.setEmailSubjectLine(String.valueOf(et.getSubject()));
		dto.setHeader(et.getEmailQuestion());
		dto.setMessage(et.getMessage());
		dto.setFeedbackMessage(et.getFeedbackMessage());
		dto.setShowContactUsOption(String.valueOf(bet.getEnableContactUs()));
		dto.setContactUsMessage(et.getContactUsMessage());
		dto.setContactUsCustomUrl(bet.getNonRecommendedUrl());
		dto.setSendReminderEmail(String.valueOf(bet.getSendReminder()));
		dto.setSendReminderSubject(et.getReminderSubject());
		dto.setSendReminderEmail(String.valueOf(bet.getSendReminder()));
		dto.setSendReminderAfter(String.valueOf(bet.getReminderFrequency()));
		dto.setLastUsed(String.valueOf(bet.getUpdatedAt()));
		List<String> urls = getDeeplinkUrls(bet.getBusinessId(), bet.getEmailTemplateId(), "web");
		dto.setReviewSite1(urls.get(0));
		dto.setReviewSite2(urls.get(1));
		dto.setReviewSite3(urls.get(2));
		return dto;
	}
	
	private CXEmailTemplateDTO getCXEmailTemplateDTO(BusinessEmailTemplate bet) {
		
		EmailTemplate et = getEmailTemplate(bet);
		CXEmailTemplateDTO dto = new CXEmailTemplateDTO();
		dto.setLocationId(String.valueOf(bet.getBusinessId()));
		dto.setTemplateId(String.valueOf(bet.getEmailTemplateId()));
		dto.setEmailSubjectLine(String.valueOf(et.getSubject()));
		dto.setCxHeader(et.getSubject());
		dto.setCxMessage(et.getSignature());
		dto.setFeedbackType(bet.getSentimentCheckType());
		dto.setRrShowPage(String.valueOf(bet.getReviewEnabled()));
		dto.setRrMessage(et.getReviewMessage());
		dto.setRrHeader(et.getReviewHeading());
		dto.setFeedbackMessage(et.getFeedbackMessage());
		dto.setShowContactUsOption(String.valueOf(bet.getEnableContactUs()));
		dto.setContactUsMessage(et.getContactUsMessage());
		dto.setContactUsCustomUrl(bet.getNonRecommendedUrl());
		dto.setSendReminderEmail(String.valueOf(bet.getSendReminder()));
		dto.setSendReminderSubject(et.getReminderSubject());
		dto.setSendReminderEmail(String.valueOf(bet.getSendReminder()));
		dto.setSendReminderAfter(String.valueOf(bet.getReminderFrequency()));
		dto.setLastUsed(String.valueOf(bet.getUpdatedAt()));
		List<String> urls = getDeeplinkUrls(bet.getBusinessId(), bet.getEmailTemplateId(), "web");
		dto.setReviewSite1(urls.get(0));
		dto.setReviewSite2(urls.get(1));
		dto.setReviewSite3(urls.get(2));
		return dto;
		
	}
	
	private SurveyEmailTemplateDTO getSurveyEmailTemplateDTO(BusinessEmailTemplate bet) {
		EmailTemplate et = getEmailTemplate(bet);
		SurveyEmailTemplateDTO dto = new SurveyEmailTemplateDTO();
		dto.setLocationId(String.valueOf(bet.getBusinessId()));
		dto.setTemplateId(String.valueOf(bet.getEmailTemplateId()));
		dto.setEmailSubjectLine(String.valueOf(et.getSubject()));
		dto.setHeader(et.getEmailQuestion());
		dto.setMessage(et.getMessage());
		dto.setLastUsed(String.valueOf(bet.getUpdatedAt()));
		dto.setSendReminderEmail(String.valueOf(bet.getSendReminder()));
		dto.setSendReminderAfter(String.valueOf(bet.getReminderFrequency()));
		dto.setNoOfReminder(String.valueOf(bet.getMaxReminderCount()));
		
		return dto;
		
	}
	
	private PromotionEmailTemplateDTO getPromotionEmailTemplateDTO(BusinessEmailTemplate bet) {
		EmailTemplate et = getEmailTemplate(bet);
		PromotionEmailTemplateDTO dto = new PromotionEmailTemplateDTO();
		dto.setLocationId(String.valueOf(bet.getBusinessId()));
		dto.setTemplateId(String.valueOf(bet.getEmailTemplateId()));
		dto.setEmailSubjectLine(String.valueOf(et.getSubject()));
		dto.setHeader(et.getEmailQuestion());
		dto.setMessage(et.getMessage());
		dto.setLastUsed(String.valueOf(bet.getUpdatedAt()));
		return dto;
	}
	
	/**
	 * @param bet
	 * @return
	 */
	private EmailTemplate getEmailTemplate(BusinessEmailTemplate bet) {
		return emailTemplateRepo.findFirstById(bet.getEmailTemplateId());
	}
	
	private RRSMSTemplateDTO getRRSMSTemplateDTO(BusinessSmsTemplate bst) {
		RRSMSTemplateDTO dto = new RRSMSTemplateDTO();
		dto.setLocationId(String.valueOf(bst.getBusinessId()));
		dto.setTemplateId(String.valueOf(bst.getId()));
		dto.setSmsTextMessage(bst.getMessageBody());
		dto.setIncludeUnsubscribe(String.valueOf(bst.getUnsubscribeTextEnabled()));
		dto.setSendImage(String.valueOf(bst.getMmsEnabled()));
		dto.setHeader(bst.getQuestion());
		dto.setMessage(bst.getMessageBody());
		dto.setFeedbackMessage(bst.getNonRecommendReviewPageMessage());
		dto.setShowContactUsOption(String.valueOf(bst.getEnableContactUs()));
		dto.setContactUsMessage(bst.getContactUsMessage());
		dto.setContactUsCustomUrl(bst.getNonRecommendedUrl());
		dto.setImageUrl(bst.getMediaUrl());
		dto.setLastUsed(String.valueOf(bst.getUpdatedAt()));
		List<String> urls = getDeeplinkUrls(bst.getBusinessId(), bst.getId(), "android");
		dto.setAndroidReviewSite1(urls.get(0));
		dto.setAndroidReviewSite2(urls.get(1));
		dto.setAndroidReviewSite3(urls.get(2));
		List<String> iurls = getDeeplinkUrls(bst.getBusinessId(), bst.getId(), "ios");
		dto.setIosReviewSite1(iurls.get(0));
		dto.setIosReviewSite2(iurls.get(1));
		dto.setIosReviewSite3(iurls.get(2));
		return dto;
		
	}
	
	private CXSMSTemplateDTO getCXSMSTemplateDTO(BusinessSmsTemplate bst) {
		CXSMSTemplateDTO dto = new CXSMSTemplateDTO();
		dto.setLocationId(String.valueOf(bst.getBusinessId()));
		dto.setTemplateId(String.valueOf(bst.getId()));
		dto.setSmsTextMessage(bst.getMessageBody());
		dto.setIncludeUnsubscribe(String.valueOf(bst.getUnsubscribeTextEnabled()));
		dto.setSendImage(String.valueOf(bst.getMmsEnabled()));
		dto.setCxHeader(bst.getQuestion());
		dto.setCxMessage(bst.getMessageBody());
		dto.setFeedbackType(bst.getSentimentCheckType());
		dto.setFeedbackMessage(bst.getNonRecommendReviewPageMessage());
		dto.setShowContactUsOption(String.valueOf(bst.getEnableContactUs()));
		dto.setContactUsMessage(bst.getContactUsMessage());
		dto.setContactUsCustomUrl(bst.getNonRecommendedUrl());
		dto.setImageUrl(bst.getMediaUrl());
		dto.setLastUsed(String.valueOf(bst.getUpdatedAt()));
		List<String> urls = getDeeplinkUrls(bst.getBusinessId(), bst.getId(), "android");
		dto.setAndroidReviewSite1(urls.get(0));
		dto.setAndroidReviewSite2(urls.get(1));
		dto.setAndroidReviewSite3(urls.get(2));
		List<String> iurls = getDeeplinkUrls(bst.getBusinessId(), bst.getId(), "ios");
		dto.setIosReviewSite1(iurls.get(0));
		dto.setIosReviewSite2(iurls.get(1));
		dto.setIosReviewSite3(iurls.get(2));
		return dto;
		
	}
	
	private SurveySMSTemplateDTO getSurveySMSTemplateDTO(BusinessSmsTemplate bst) {
		SurveySMSTemplateDTO dto = new SurveySMSTemplateDTO();
		dto.setLocationId(String.valueOf(bst.getBusinessId()));
		dto.setTemplateId(String.valueOf(bst.getId()));
		dto.setSmsTextMessage(bst.getMessageBody());
		dto.setIncludeUnsubscribe(String.valueOf(bst.getUnsubscribeTextEnabled()));
		dto.setSendImage(String.valueOf(bst.getMmsEnabled()));
		dto.setImageUrl(bst.getMediaUrl());
		dto.setLastUsed(String.valueOf(bst.getUpdatedAt()));
		return dto;
	}
	
	private List<String> getDeeplinkUrls(Integer businessId, Integer templateId, String deviceType) {
		List<BusinessDeeplinkPriority> deeplinkPriorities = deeplinkPriorityRepo.findByBusinessIdAndDeviceTypeAndTemplateId(businessId, deviceType, templateId);
		List<String> priorities = new ArrayList<>();
		Comparator<BusinessDeeplinkPriority> comparator = getComparator();
		if (CollectionUtils.isNotEmpty(deeplinkPriorities)) {
			
			deeplinkPriorities = deeplinkPriorities.stream().filter(b -> b.getPriorityOrder() != null).sorted(comparator).collect(Collectors.toList());
			priorities = deeplinkPriorities.stream().map(p -> p.getSourceUrl()).collect(Collectors.toList());
		}
		priorities.add(StringUtils.EMPTY);
		priorities.add(StringUtils.EMPTY);
		priorities.add(StringUtils.EMPTY);
		return priorities;
	}
	
	private  Comparator<BusinessDeeplinkPriority> getComparator() {
		Comparator<BusinessDeeplinkPriority> comparator = (h1, h2) -> h1.getPriorityOrder().compareTo(h2.getPriorityOrder());
		return comparator;
	}
	
	
private JdbcTemplate jdbcTemplate;
	
	@Autowired
	@Qualifier("platformDatasource")
	public void setupJdbcTemplate(DataSource dataSource){
		jdbcTemplate = new JdbcTemplate(dataSource);
	}

	private static final Logger logger = LoggerFactory.getLogger(MigrationDataDownloadServiceImpl.class);

	private static final String SMS_TEMPLATES_BUSINESS = "select * from business_sms_template where business_id"
			+ " in (select id from business where enterprise_id=%s)";
	
	private static final String SMS_TEMPLATES_PRIORITY = "select template_id,device_type,source_id,priority_order from business_deeplink_priority  where device_type"
			+ " in ('ios','android') and template_id in (%s)";
	
	private static final String EMAIL_TEMPLATES_BUSINESS = "select *  from  business_email_template  bet inner join email_template  et "
			+ "on bet.template_id=et.id  where et.type='review_request_new' and bet.business_id "
			+ "in (select id from business where enterprise_id=%s) ";
	
	private static final String EMAIL_TEMPLATES_PRIORITY = "select template_id,device_type,source_id,priority_order from business_deeplink_priority  where device_type"
			+ " = 'web' and template_id in (%s)";
	
	
	public void getTemplatesForEnterprise(Integer businessId) {
		logger.info("####################### EMAIL TEMPLATES #######################");
		getBusinessEmailTemplates(businessId);
		logger.info("####################### SMS TEMPLATES #######################");
		getBusinessSMSTemplates(businessId);
	}

	private void getBusinessEmailTemplates(Integer businessId) {

		List<Map<String, Object>> result = jdbcTemplate
				.queryForList(String.format(EMAIL_TEMPLATES_BUSINESS, businessId));
		logger.info("Email Templates size for business {} is : {}", businessId, result.size());

		Map<Object, List<Map<String, Object>>> byType = result.stream()
				.collect(Collectors.groupingBy(row -> row.get("type")));
		logger.info("Email Templates Types : {}", byType.keySet());

		for (Object type : byType.keySet()) {
			List<RREmailTemplateDTO> templatesByType = byType.get(type).stream().map(e -> toEmailTemplate(e))
					.collect(Collectors.toList());
			logger.info("Type: {} Email Templates are : {}", type, templatesByType.size());
			uniqueEmailTemplates(templatesByType);
		}
	}

	private void getBusinessSMSTemplates(Integer businessId) {

		List<Map<String, Object>> result = jdbcTemplate.queryForList(String.format(SMS_TEMPLATES_BUSINESS, businessId));
		logger.info("SMS Templates size for business {} is : {}", businessId, result.size());

		Map<Object, List<Map<String, Object>>> byType = result.stream()
				.collect(Collectors.groupingBy(row -> row.get("type")));
		logger.info("SMS Templates Types : {}", byType.keySet());

		for (Object type : byType.keySet()) {
			List<RRSMSTemplateDTO> templatesByType = byType.get(type).stream().map(e -> toSMSTemplate(e))
					.collect(Collectors.toList());
			logger.info("{} SMS Templates size : {}", type, templatesByType.size());
			uniqueSmsTemplates(templatesByType);
		}
	}

	private void uniqueSmsTemplates(List<RRSMSTemplateDTO> templatesByType) {
		String rrTemplateIds = templatesByType.stream().map(RRSMSTemplateDTO::getTemplateId)
				.collect(Collectors.joining(","));

		List<Map<String, Object>> reviewSites = jdbcTemplate
				.queryForList(String.format(SMS_TEMPLATES_PRIORITY, rrTemplateIds));
		logger.info("SMS Templates review sites are : {}", reviewSites.size());

		Map<Object, List<Map<String, Object>>> byTemplateIds = reviewSites.stream()
				.collect(Collectors.groupingBy(t -> t.get("template_id")));

		// Group by common fields
		Map<String, List<RRSMSTemplateDTO>> uniqueMap = new HashMap<>();
		templatesByType.stream().forEach(rrtemplate -> {
			List<Map<String, Object>> reviewSitesForTemplates = byTemplateIds.get(rrtemplate.getTemplateId());
			if (reviewSitesForTemplates != null) {
				setupReviewSites(rrtemplate, reviewSitesForTemplates);
			}
			String key = getKey(rrtemplate);
			// Compute if absent
			uniqueMap.computeIfAbsent(key, (k -> new ArrayList<>())).add(rrtemplate);
		});
		logger.info("RR Unique SMS Templates size : {}", uniqueMap.size());
		uniqueMap.entrySet().forEach(e -> {
			logger.info("Template Key {} with Value{} :", e.getKey(),
					e.getValue().stream().map(RRSMSTemplateDTO::getTemplateId).collect(Collectors.toList()));
		});
	}

	private String getKey(RRSMSTemplateDTO map) {
		StringBuilder builder = new StringBuilder("[");
		builder.append(map.getMessage());
		builder.append(map.getHeader());
		builder.append(map.getContactUsMessage());
		builder.append(map.getFeedbackMessage());
		builder.append("]");
		return builder.toString();
	}

	private void setupReviewSites(RRSMSTemplateDTO rrtemplate, List<Map<String, Object>> reviewSitesForTemplates) {
		reviewSitesForTemplates.stream().forEach(row -> {
			String deviceType = row.get("device_type").toString();
			String priorityOrder = row.get("priority_order").toString();
			String sourceId = row.get("source_id").toString();
			if ("ios".equals(deviceType)) {
				if ("1".equals(priorityOrder)) {
					rrtemplate.setIosReviewSite1(sourceId);
				} else if ("2".equals(priorityOrder)) {
					rrtemplate.setIosReviewSite2(sourceId);
				} else if ("3".equals(priorityOrder)) {
					rrtemplate.setIosReviewSite3(sourceId);
				}

			} else if ("android".equals(deviceType)) {
				if ("1".equals(priorityOrder)) {
					rrtemplate.setAndroidReviewSite1(sourceId);
				} else if ("2".equals(priorityOrder)) {
					rrtemplate.setAndroidReviewSite2(sourceId);
				} else if ("3".equals(priorityOrder)) {
					rrtemplate.setAndroidReviewSite3(sourceId);
				}
			} else {
				logger.info("Unsupported SMS review site type {} for template {} ", deviceType,
						rrtemplate.getTemplateId());
			}
		});

	}

	private void uniqueEmailTemplates(List<RREmailTemplateDTO> templatesByType) {
		String rrTemplateIds = templatesByType.stream().map(RREmailTemplateDTO::getTemplateId)
				.collect(Collectors.joining(","));

		List<Map<String, Object>> reviewSites = jdbcTemplate
				.queryForList(String.format(EMAIL_TEMPLATES_PRIORITY, rrTemplateIds));
		logger.info("Email Templates review sites are : {}", reviewSites.size());

		Map<Object, List<Map<String, Object>>> byTemplateIds = reviewSites.stream()
				.collect(Collectors.groupingBy(t -> t.get("template_id")));

		// Group by common fields
		Map<String, List<RREmailTemplateDTO>> uniqueMap = new HashMap<>();
		templatesByType.stream().forEach(rrtemplate -> {
			List<Map<String, Object>> reviewSitesForTemplates = byTemplateIds.get(rrtemplate.getTemplateId());
			if (reviewSitesForTemplates != null) {
				setupReviewSites(rrtemplate, reviewSitesForTemplates);
			}
			String key = getKey(rrtemplate);
			// Compute if absent
			uniqueMap.computeIfAbsent(key, (k -> new ArrayList<>())).add(rrtemplate);
		});
		logger.info("RR Unique SMS Templates size : {}", uniqueMap.size());
		uniqueMap.entrySet().forEach(e -> {
			logger.info("EMail Template Key {} with Value{} :", e.getKey(),
					e.getValue().stream().map(RREmailTemplateDTO::getTemplateId).collect(Collectors.toList()));
		});
	}

	private String getKey(RREmailTemplateDTO map) {
		StringBuilder builder = new StringBuilder("[");
		builder.append(map.getMessage());
		builder.append(map.getHeader());
		builder.append(map.getContactUsMessage());
		builder.append(map.getFeedbackMessage());
		builder.append("]");
		return builder.toString();
	}

	private void setupReviewSites(RREmailTemplateDTO rrtemplate, List<Map<String, Object>> reviewSitesForTemplates) {
		reviewSitesForTemplates.stream().forEach(row -> {
			String priorityOrder = row.get("priority_order").toString();
			String sourceId = row.get("source_id").toString();
			if ("1".equals(priorityOrder)) {
				rrtemplate.setReviewSite1(sourceId);
			} else if ("2".equals(priorityOrder)) {
				rrtemplate.setReviewSite2(sourceId);
			} else if ("3".equals(priorityOrder)) {
				rrtemplate.setReviewSite3(sourceId);
			}
		});
	}
	/*
	 * {id=7495, business_id=119522, type=review_request, message_from=David's
	 * Bridal, question=How was your experience?, message_body=David's Bridal:
	 * Hi [Customer Name], We’d love to hear about your experience. Tell us what
	 * you think:, positive_link_label=Great!, negative_link_label=Not Good,
	 * non_recommend_review_page_message=We apologize that your experience was
	 * not great. How can we improve?, unsubscribe_text=null,
	 * media_url=mms/57e304fc3355a8.95150788.jpg, media_type=custom,
	 * non_recommended_url=null, include_message_from=1, name=Review request SMS
	 * for all customers, mms_enabled=true, enable_sentiment_check=true,
	 * show_emoticon=true, nps_rating_min=null, star_rating_min=null,
	 * sentiment_check_type=null, recommend_page_heading=Where would you review
	 * us?, recommend_page_message=Thank you for choosing us! Please take a
	 * moment to leave us a review? Your responses will help us serve you better
	 * in the future., recommend_thank_page_message=We truly value your opinion
	 * and appreciate your feedback., recommend_thank_page_footer=Have a great
	 * day, The [Business Name] team., non_recommend_thank_page_message=We truly
	 * value your opinion and appreciate your feedback.,
	 * non_recommend_thank_page_footer=Have a great day, The [Business Name]
	 * team., unsubscribe_text_enabled=true, write_review_question=How did we
	 * do?, write_review_neg_text=Sorry to hear that! Please tell us about your
	 * experience below., write_review_pos_text=Please write a review in the box
	 * below., positive_rating_threshold=3, send_mail_after=0, salutation=null,
	 * enable_contact_us=1, contact_us_message=Your experience was not great?,
	 * contact_us_button_text=Contact us directly,
	 * contact_us_button_text_color=#ffffff, contact_us_button_color=#e53935,
	 * custom_image_url=null, neutral_link_label=null, exclude_neutral=false,
	 * star_heading=null, sentiment_heading=null, sentiment_message=null,
	 * star_message=null, is_deleted=false, enable_location_branding=false,
	 * enable_feedback_message=false, feedback_callback_message=null,
	 * enable_feedback_checkbox=false, thankyou_message=null,
	 * thankyou_heading=null, review_site_button_color=null,
	 * review_site_button_text_color=null, review_enable=false,
	 * enable_image_branding=false, enable_header_branding=false}
	 */

	static String[] fields = { "question", "message_body", "contact_us_message", "non_recommend_review_page_message" };

	// Template fields should be common
	@SuppressWarnings("rawtypes")
	private RRSMSTemplateDTO toSMSTemplate(Map map) {
		RRSMSTemplateDTO dto = new RRSMSTemplateDTO();
		dto.setTemplateId(map.get("id").toString());
		dto.setLocationId(map.get("business_id").toString());
		// Grouping fields.
		dto.setMessage(nullSafe(map.get("message_body")));
		dto.setHeader(nullSafe(map.get("question")));
		dto.setContactUsMessage(nullSafe(map.get("contact_us_message")));
		dto.setFeedbackMessage(nullSafe(map.get("non_recommend_review_page_message")));
		dto.setImageUrl(nullSafe(map.get("media_url")));
		return dto;
	}

	private static String nullSafe(Object obj) {
		return obj == null ? null : obj.toString();
	}

	// Template fields should be common
	@SuppressWarnings("rawtypes")
	private RREmailTemplateDTO toEmailTemplate(Map map) {
		RREmailTemplateDTO dto = new RREmailTemplateDTO();
		dto.setTemplateId(map.get("id").toString());
		dto.setLocationId(map.get("business_id").toString());
		// Grouping fields.
		dto.setMessage(nullSafe(map.get("message")));
		dto.setEmailSubjectLine(nullSafe(map.get("subject")));
		dto.setHeader(nullSafe(map.get("email_question")));
		dto.setContactUsMessage(nullSafe(map.get("contact_us_message")));
		dto.setFeedbackMessage(nullSafe(map.get("recommend_page_message")));
		dto.setFeedbackMessage(nullSafe(map.get("non_recommend_page_message")));
		return dto;
	}

}
