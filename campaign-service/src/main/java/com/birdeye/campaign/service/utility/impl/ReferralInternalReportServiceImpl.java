/**
 * @file_name ReferralInternalReportServiceImpl.java
 * @created_date 10 Nov 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.service.utility.impl;

import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.amazon.service.AmazonUploadService;
import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.ParametersCache;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.constant.KafkaTopicTypeEnum;
import com.birdeye.campaign.dto.KafkaMessage;
import com.birdeye.campaign.kafka.service.KafkaService;
import com.birdeye.campaign.platform.readonly.repository.BusinessReadOnlyRepo;
import com.birdeye.campaign.report.ReportServiceUtils;
import com.birdeye.campaign.repository.ReviewRequestLogRepo.RRLEvent;
import com.birdeye.campaign.request.template.NexusEmailInputMessage;
import com.birdeye.campaign.request.template.NexusEmailInputMessage.NexusEmailGenericData;
import com.birdeye.campaign.request.template.NexusEmailInputMessage.NexusEmailMetaData;
import com.birdeye.campaign.service.utility.ReferralInternalReportService;
import com.birdeye.campaign.utils.CampaignUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;

/**
 * @file_name ReferralInternalReportServiceImpl.java
 * @created_date 10 Nov 2020
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
@Service("referralInternalReportsService")
public class ReferralInternalReportServiceImpl implements ReferralInternalReportService {
	
	@Autowired
	private AmazonUploadService				uploadService;
	
	@Autowired
	private KafkaService					kafkaService;
	
	@Autowired
	private BusinessReadOnlyRepo					businessReadOnlyRepo;
	
	@Value("${spring.profiles.active}")
	private String activeProfile;
	
	private static final Logger				LOG				= LoggerFactory.getLogger(ReferralInternalReportServiceImpl.class);
	
	private static final String				ENTERPRISE_ID	= "enterpriseId";
	
	private static final String				REF_ID			= "Request ID";
	private static final String				BIZ_ID			= "Business ID";
	private static final String				ALIAS			= "Business Alias";
	private static final String				CMP_ID			= "Campaign ID";
	private static final String				CID				= "Customer ID";
	private static final String				CNAME			= "Customer Name";
	private static final String				CPHONE			= "Customer Phone";
	private static final String				CEMAIL			= "Customer Email";
	private static final String				CSTM_VNO		= "Vergent Customer ID";
	private static final String				CSTM_MDM		= "MDM ID";
	private static final String				CSTM_REFCODE	= "Referral Code";
	private static final String				SRC				= "Request Source";
	private static final String				REF_DATE		= "Request Sent Date (UTC)";
	
	private static final String				EVENT		= "Event";
	private static final String				EVENT_TIME		= "Event Date (UTC)";


	private static final String				SHARED_SOURCES	= "Shared Source";
	
	private static final SimpleDateFormat	sdf				= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	

	static final String[] columns = new String[] { REF_ID, BIZ_ID, ALIAS, CMP_ID, CID, CNAME, CPHONE, CEMAIL, SRC,REF_DATE, EVENT, EVENT_TIME,
			 SHARED_SOURCES, CSTM_MDM, CSTM_REFCODE, CSTM_VNO };

	
	@Override
	public void generateTMXReferralReport() {
		
		boolean tmxReferralReportDisabled = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getBooleanProperty("tmx-referral-report-disabled");
		if (tmxReferralReportDisabled) {
			LOG.info("generateTMXReferralReport : TMX report disabled");
			return;
		}
		
		long start = System.currentTimeMillis();
		
		Integer tmxEnterpriseId = getTMXEnterpriseID();
		List<Integer> tmxLocationIds = getTMXLocationIds(tmxEnterpriseId);
		Date prevDayStartInclusive = getStartDateInclusive();
		Date prevDayEndExclusive = getEndDateEnclusive();
		List<Integer> clickTypes = new ArrayList<>(Arrays.asList(1,2));
		
		List<RRLEvent> events = getOpenAndSharedEventsForTMX(tmxLocationIds, prevDayStartInclusive, prevDayEndExclusive, clickTypes);
		LOG.info("generateTMXReferralReport - total open and shared events is :: {}", CollectionUtils.size(events));
		if (CollectionUtils.isEmpty(events)) {
			return;
		}
		
		final List<Map<String, String>> rows = new ArrayList<>();
		events.forEach(message -> rows.add(processDoc(message)));
		LOG.info("Total requests: " + rows.size() + " and time taken in seconds: " + (System.currentTimeMillis() - start) / 1000);
		LOG.info("Total unique Shares: " + rows.stream().filter(r -> (r.get("Referral Sources") != null)).count());
		LOG.info("Total Shares: " + rows.stream().map(r -> getShareCount(r.get("Referral Sources"))).collect(Collectors.summingInt(Integer::intValue)));
		start = System.currentTimeMillis();
		
		List<String> customerIds = rows.stream().map(m -> m.get(CID)).collect(Collectors.toList());
		Map<String, Map<String, String>> kontactoData = getKontactoData(customerIds);
		
		// Merge data.
		rows.stream().forEach(r -> {
			String cid = r.get(CID);
			Map<String, String> customerInfo = kontactoData.get(cid);
			if (customerInfo != null) {
				r.putAll(customerInfo);
			}
		});
		start = System.currentTimeMillis();
		String fileName = CampaignUtils.removeSpecialCharacters(StringUtils.join("TMX_Referral_Reports_", System.currentTimeMillis()));
		String filePath = writeExcelFile(rows, fileName);
		LOG.info("Time taken in writting file in seconds: " + ((System.currentTimeMillis() - start) / 1000));
		
		
		
		String uploadUrl = uploadService.uploadFileAndGetDownloadLink(filePath, fileName, ".xlsx");
		
		NexusEmailInputMessage nexusEmailInputMessage = getTMXReferralReportNexusEmailMessage(uploadUrl);
		
		kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.NEXUS_EMAIL_SEND, new KafkaMessage(nexusEmailInputMessage));
		
	}

	private List<Integer> getTMXLocationIds(Integer tmxEnterpriseId) {
		return businessReadOnlyRepo.findActiveLocationIdsByEnterpriseId(tmxEnterpriseId);
	}
	
	/**
	 * @return
	 */
	private static String getYesterdayYYYYMMDD() {
		return DateTimeFormatter.ofPattern("yyyy-MM-dd").format(Instant.now().minus(1, ChronoUnit.DAYS).atZone(ZoneId.of("America/Los_Angeles")));
	}
	
	/**
	 * @return
	 */
	private static Date getEndDateEnclusive() {
		return Date.from(ZonedDateTime.now(ZoneId.of("America/Los_Angeles")).withHour(0).withMinute(0).withSecond(0).withNano(0).withZoneSameInstant(ZoneOffset.UTC).toInstant());
	}
	
	/**
	 * @return
	 */
	private static Date getStartDateInclusive() {
		return Date.from(ZonedDateTime.now(ZoneId.of("America/Los_Angeles")).plusDays(-1).withHour(0).withMinute(0).withSecond(0).withNano(0).withZoneSameInstant(ZoneOffset.UTC).toInstant());
	}
	
	/**
	 * @return
	 */
	private Integer getTMXEnterpriseID() {
		return CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("tmx.referral.report.account.id", 717879);
	}
	
	/*
	private List<ReferralCommMessage> getOpenDocumentsForTMX(Integer enterpriseId, Date startDateIncl, Date endDateExcl) {
		Map<String, Object> tokenData = getTokenDataForESQuery(enterpriseId, startDateIncl, endDateExcl);
		ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData, ElasticQueryTemplateEnum.TMX_REFERRAL_REPORT_INTERNAL_V1.getQueryName(),
				Constants.REFERRAL_REPORT_INDEX, Constants.REFERRAL_REPORT_TYPE);
		
		SearchResult result = elasticSearchService.execute(esRequest);
		if (result == null || BooleanUtils.isFalse(result.isSucceeded()) || result.getTotal() == null || result.getTotal() == 0l) {
			return Collections.emptyList();
		}
		List<ReferralCommMessage> messages = result.getSourceAsObjectList(ReferralCommMessage.class, false);
		if (CollectionUtils.isEmpty(messages)) {
			return Collections.emptyList();
		}
		return messages;
	}
	*/
	
	/**
	 * 
	 * OPEN AND SHARED EVENTS - NO LONGER ACTIVE, CALLED FROM DEPRECATED API
	 * 
	 * @param businessIds
	 * @param startDateInclusive
	 * @param endDateExclusive
	 * @param clickTypes
	 * @return
	 */
	private List<RRLEvent> getOpenAndSharedEventsForTMX(List<Integer> businessIds, Date startDateInclusive, Date endDateExclusive, List<Integer> clickTypes){
		/**
		 * 
		 * IF REQUIRED USE BELOW QUERY FOR OPEN AND SHARED EVENTS, create version without cross joins
		 * 
		 * @Query(value = "select rr.id as RRID, b.id as BID, b.alias1 as BNAME, rr.campaign_id as CAMPAIGNID, rr.customer_id as CUSTID, rr.source as REQUESTSOURCE, rr.sent_on AS REQUESTDATE, rrl.click_type as EVENT, rrl.clicked_at as EVENTDATE, rrl.source_id as SHAREDSOURCE 	from  review_requests_log rrl  join review_request rr on (rrl.review_request_id=rr.id) join business b on (rr.business_id=b.id) where rr.request_type='referral' and rrl.click_type in :clickTypes and rrl.clicked_at >= :startDateInclusive  and rrl.clicked_at < :endDateExclusive and rr.business_id in :businessIds", nativeQuery = true)
	public List<RRLEvent> findReferralRRLEvents(@Param("businessIds") List<Integer> businessIds, @Param("startDateInclusive") Date startDateInclusive, @Param("endDateExclusive") Date endDateExclusive,
			@Param("clickTypes") List<Integer> clickTypes);
	
		 */
//		List<RRLEvent> events = reviewRequestLogRepo.findReferralRRLEvents(businessIds, startDateInclusive, endDateExclusive, clickTypes);
//		if(CollectionUtils.isEmpty(events)) {
//			return Collections.emptyList();
//		}
		
		return Collections.emptyList();
	}
	
	private static int getShareCount(String sources) {
		if (sources != null && !sources.isEmpty()) {
			return sources.split(",").length;
		}
		return 0;
	}

	
	/**
	 * @param enterpriseId
	 * @param startDateIncl
	 * @param endDateExcl
	 * @return
	 */
	private Map<String, Object> getTokenDataForESQuery(Integer enterpriseId, Date startDateIncl, Date endDateExcl) {
		Map<String, Object> tokenData = new HashMap<>();
		tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
		tokenData.put(ReportServiceUtils.START_DATE, sdf.format(startDateIncl));
		tokenData.put(ReportServiceUtils.END_DATE, sdf.format(endDateExcl));
		return tokenData;
	}
	
	private static String getPSTDate(Long date) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		ZonedDateTime zonedDateTime = Instant.ofEpochMilli(date).atZone(ZoneId.of("America/Los_Angeles"));
		return zonedDateTime.format(formatter);
	}
	
	private static String getUTCDate(Long date) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		ZonedDateTime zonedDateTime = Instant.ofEpochMilli(date).atZone(ZoneOffset.UTC);
		return zonedDateTime.format(formatter);
	}
	
	/*
	//Input - 2020-11-04 23:12:00
	private static String getPSTDateFromUTCString(String date) {
		DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		Instant inputDate = LocalDateTime.from(inputFormatter.parse(date)).toInstant(ZoneOffset.UTC);
		
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/yyyy - HH:mm:ss");
		ZonedDateTime zonedDateTime = inputDate.atZone(ZoneId.of("America/Los_Angeles"));
		return zonedDateTime.format(formatter);
	}
	*/
	static Map<String, String> processDoc(RRLEvent event) {
		Map<String, String> row = new HashMap<>();
		row.put(REF_ID, asText(event.getRRID()));
		row.put(BIZ_ID, asText(event.getBID()));
		row.put(ALIAS, asText(event.getBNAME()));
		row.put(CMP_ID, asText(event.getCAMPAIGNID()));
		row.put(CID, asText(event.getCUSTID()));
		row.put(SRC, asText(event.getREQUESTSOURCE()));
		if (event.getREQUESTDATE() != null) {
			row.put(REF_DATE, getUTCDate(event.getREQUESTDATE().getTime()));
		}
		
		row.put(EVENT, asText(getReferralEventValue(event.getEVENT())));
		if (event.getEVENTDATE() != null) {
			row.put(EVENT_TIME, getUTCDate(event.getEVENTDATE().getTime()));
		}
		row.put(SHARED_SOURCES, asText(event.getSHAREDSOURCE()));
		return row;
	}
	
	private static String asText(Object obj) {
		return obj == null ? "" : obj.toString();
	}
	
	private static String getReferralEventValue(Integer clickType) {
		if(clickType == null) {
			return null;
		}
		switch(clickType) {
			case 1 :
				return "Open";
			case 2 :
				return "Shared";
			default:
				return null;
		}
	}
	
	static CloseableHttpClient httpClient = HttpClients.createDefault();

	private static Map<String, Map<String, String>> getKontactoData(List<String> customerIds) {
		int size = 5000;
		long start = System.currentTimeMillis();
		LOG.info("Calling Kontacto with batch size of :: " + size + " for total data :: " + customerIds.size());
		Map<String, Map<String, String>> output = new ConcurrentHashMap<>();
		Lists.partition(customerIds, size).forEach(l -> {
			getCustomerDetailsInBatch(l, output);
		});
		LOG.info("Total time taken from Kontacto in seconds :: " + (System.currentTimeMillis() - start) / 1000);
		return output;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	private static void getCustomerDetailsInBatch(List<?> customerIds, Map<String, Map<String, String>> result) {
		
		
		CloseableHttpResponse response = null;
		try {
			ObjectMapper om = new ObjectMapper();
			HttpPost request = new HttpPost(StringUtils.join(getContactServiceBaseURL(), "customer/getByIds?customField=true"));
			request.setHeader("Accept", "application/json");
			request.setHeader("Content-type", "application/json");
			request.setEntity(new StringEntity(om.writeValueAsString(customerIds)));
			response = httpClient.execute(request);
			HttpEntity entity = response.getEntity();
			if (entity != null) {
				// return it as a String
				String body = EntityUtils.toString(entity);
				List<?> listOfMaps = om.readValue(body, List.class);
				listOfMaps.forEach(element -> {
					if (element instanceof Map) {
						Map<String, String> data = new HashMap<>();
						Map<?, ?> map = (Map<?, ?>) element;
						result.put(asText(map.get("id")), data);
						// process customer data.
						data.put(CNAME, asText(map.get("displayName")));
						data.put(CPHONE, asText(map.get("phone")));
						data.put(CEMAIL, asText(map.get("email")));
						Object customFields = map.get("customFieldValues");
						if (customFields instanceof List) {
							((List) customFields).stream().forEach(e -> {
								if (e instanceof Map) {
									Map<?, ?> customField = (Map<?, ?>) e;
									if (CSTM_REFCODE.equals(customField.get("fieldName"))) {
										data.put(CSTM_REFCODE, asText(customField.get("fieldValue")));
									} else if (CSTM_MDM.equals(customField.get("fieldName"))) {
										data.put(CSTM_MDM, asText(customField.get("fieldValue")));
									} else if (CSTM_VNO.equals(customField.get("fieldName"))) {
										data.put(CSTM_VNO, asText(customField.get("fieldValue")));
									}
								}
							});
						}
					}
				});
			}
		} catch (Exception e) {
			LOG.error("Error getting data from Kontacto for CustomerIDs" + customerIds);
			LOG.error("Error getting data from Kontacto - exception - " + ExceptionUtils.getStackTrace(e));
		} finally {
			try {
				if (response != null) {
					response.close();
				}
			} catch (Exception e) {
			}
			LOG.info("Returning data for :: " + customerIds);
		}
	}

	// http://kontacto.birdeye.com/customer/get/126194657?customField=true
	static String writeExcelFile(List<Map<String, String>> rows, String fileName) {
		try {
			Workbook workbook = new XSSFWorkbook();

			Sheet sheet = workbook.createSheet("Referrals");
			 
			Row header = sheet.createRow(0);
			CellStyle headerStyle = getHeaderStyle(workbook);
			createHeaders(header, headerStyle);

			// Data
			CellStyle style = workbook.createCellStyle();
			style.setWrapText(true);
			for (int i = 0; i < rows.size(); i++) {
				Row excelRow = sheet.createRow(i + 1);
				createRow(excelRow, style, rows.get(i));
			}
			String rootDwnldPath = CacheManager.getInstance().getCache(ParametersCache.class).getProperty("downloadPath", "/tmp");
			File folder = new File(rootDwnldPath);
			if (!folder.exists()) {
				folder.mkdirs();
			}
			String fileLocation = folder.getAbsolutePath() + '/' + fileName + ".xlsx";
			
			FileOutputStream outputStream = new FileOutputStream(fileLocation);
			workbook.write(outputStream);
			workbook.close();
			return fileLocation;
		} catch (Exception e) {
			LOG.error("Error writting excel file :: {}", ExceptionUtils.getStackTrace(e));
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * @param workbook
	 * @return
	 */
	private static CellStyle getHeaderStyle(Workbook workbook) {
		CellStyle headerStyle = workbook.createCellStyle();
		 Font headerFont = workbook.createFont();
		 headerFont.setBold(true);
		 headerStyle.setFont(headerFont);
		return headerStyle;
	}
	
	private static void createRow(Row excelRow, CellStyle cellStyle, Map<String, String> data) {
		for (int i = 0; i < columns.length; i++) {
			Cell cell = excelRow.createCell(i);
			cell.setCellStyle(cellStyle);
			cell.setCellValue(data.get(columns[i]));
		}
	}

	private static void createHeaders(Row header, CellStyle headerStyle) {
		//headerStyle.setFillForegroundColor(IndexedColors.DARK_TEAL.getIndex());
		//headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		headerStyle.setWrapText(true);
		for (int i = 0; i < columns.length; i++) {
			Cell headerCell = header.createCell(i);
			headerCell.setCellStyle(headerStyle);
			headerCell.setCellValue(columns[i]);
		}
	}
	
	private NexusEmailInputMessage getTMXReferralReportNexusEmailMessage(String fileURL) {
		NexusEmailInputMessage nexusEmailInputMessage = new NexusEmailInputMessage<>();
		nexusEmailInputMessage.setDataObject(new HashMap<String, Object>());
		nexusEmailInputMessage.setEmailBody(getTMXReferralBody(fileURL));
		NexusEmailGenericData genericData = new NexusEmailGenericData();
		// the subject is fetched from subject field in html templates table, which is
		// this in prod
		genericData.setSubject(getEmailSubject());
		genericData.setFrom("<EMAIL>");
		genericData.setFromName("TitleMax Automation");
		genericData.setBody(getTMXReferralBody(fileURL));
		genericData.setTo(getTo());
		genericData.setBcc(getBcc());
		NexusEmailMetaData nexusEmailMetaData = new NexusEmailMetaData();
		nexusEmailMetaData.setApplyBranding(false);
		nexusEmailMetaData.setBusinessId(getTMXEnterpriseID());
		nexusEmailMetaData.setEmailSubType("tmx-referral-report");
		nexusEmailMetaData.setEmailType("tmx-referral-report");
		nexusEmailMetaData.setExternalUid(String.valueOf(System.currentTimeMillis()));
		nexusEmailMetaData.setRecipientType("custom"); // for business_user, sender's name will be either Birdeye or Reseller.
		nexusEmailInputMessage.setEmailMetaData(nexusEmailMetaData);
		nexusEmailInputMessage.setEmailGenericData(genericData);
		return nexusEmailInputMessage;
	}

	private List<String> getTo() {
		return CacheManager.getInstance().getCache(SystemPropertiesCache.class).getCommaSeparatedPropertiesList("tmx.referral.report.recipients", "<EMAIL>");
	}

	private List<String> getBcc() {
		return CacheManager.getInstance().getCache(SystemPropertiesCache.class).getCommaSeparatedPropertiesList("tmx.referral.report.bcc.recipients", "<EMAIL>,<EMAIL>");
	}
	
	private String getEmailSubject() {
		if (StringUtils.equalsAnyIgnoreCase(activeProfile, "demo", "qa")) {
			return "Referral Report : TitleMax - " + getYesterdayYYYYMMDD() + " - " + activeProfile;
		} else {
			return "Referral Report : TitleMax - " + getYesterdayYYYYMMDD();
		}
	}
	
	private static String getTMXReferralBody(String fileURL) {
		return StringUtils.join("TitleMax Referrals Report for ", getYesterdayYYYYMMDD(), " - ", fileURL + "<br/><br/><br/><br/><br/><br/><br/><br/>");
	}
	
	private static String getContactServiceBaseURL() {
		return CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("contact_api_server_base_url");
	}
}
