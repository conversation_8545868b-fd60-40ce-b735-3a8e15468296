package com.birdeye.campaign.service.dao.impl;

import javax.transaction.Transactional;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.CampaignCondition;
import com.birdeye.campaign.repository.CampaignConditionRepo;
import com.birdeye.campaign.repository.CampaignRepo;
import com.birdeye.campaign.service.dao.AutomationCampaignDao;

@Service("automationCampaignDaoImpl")
public class AutomationCampaignDaoImpl implements AutomationCampaignDao {
	
	private static final Logger		logger	= LoggerFactory.getLogger(AutomationCampaignDaoImpl.class);
	
	@Autowired
	private CampaignRepo			campaignRepo;
	
	@Autowired
	private CampaignConditionRepo	campaignConditionRepo;
	
	@Override
	@Transactional
	public Campaign saveDefaultAutomationCampaign(Campaign campaign, Integer enterpriseId) {
		logger.info("save Default Automation campaign for appointment and businessId {}", enterpriseId);
		if (campaign == null) {
			logger.info("Empty campaign object recieved while saving DefaultAutomationCampaign for appointment");
			return null;
		}
		try {
			return campaignRepo.saveAndFlush(campaign);
		} catch (Exception e) {
			logger.error("Exception occurred while saving default automation for appointment and Business id {}", enterpriseId, ExceptionUtils.getStackTrace(e));
		}
		return null;
	}
	
	@Override
	@Transactional
	public CampaignCondition saveDefaultAutomationCampaignCondition(CampaignCondition campaignCondition, Integer enterpriseId) {
		logger.info("save Default Automation campaign condition for appointment and businessId {}", enterpriseId);
		if (campaignCondition == null) {
			logger.info("Empty campaign condition object recieved while saving DefaultAutomationCampaignCondition for appointment and Business id {}", enterpriseId);
			return null;
		}
		try {
			return campaignConditionRepo.saveAndFlush(campaignCondition);
		} catch (Exception e) {
			logger.error("Exception occurred while saving default automation condition for appointment and Business id {}", enterpriseId, ExceptionUtils.getStackTrace(e));
		}
		return null;
	}
	
}
