/**
 * @file_name CampaignEmailEventsService.java
 * @created_date 6 Jan 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.service.emailevents;

import com.birdeye.campaign.request.EmailEventInfo;

/**
 * @file_name CampaignEmailEventsService.java
 * @created_date 6 Jan 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public interface CampaignEmailEventsService {

	/**
	 * @param request
	 */
	void processEmailEvent(EmailEventInfo request);
	
}
