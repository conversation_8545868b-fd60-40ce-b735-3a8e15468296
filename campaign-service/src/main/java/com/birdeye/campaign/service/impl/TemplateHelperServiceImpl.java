/**
 * 
 */
package com.birdeye.campaign.service.impl;

import static com.birdeye.campaign.constant.Constants.SHORTLINK_TOKEN;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.aspect.annotation.Profiled;
import com.birdeye.campaign.business.service.BusinessService;
import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.DefaultSmsTemplatesCache;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.TemplateStaticTokens;
import com.birdeye.campaign.dto.BrandingInfo;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.BusinessTemplateEntity;
import com.birdeye.campaign.dto.BusinessTemplateMessage;
import com.birdeye.campaign.dto.CampaignCustomEntity;
import com.birdeye.campaign.dto.TemplateCustomFieldSRO;
import com.birdeye.campaign.entity.BusinessEmailTemplate;
import com.birdeye.campaign.entity.BusinessSmsTemplate;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.CampaignCustomFieldAssociation;
import com.birdeye.campaign.entity.CustomCampaignUrl;
import com.birdeye.campaign.entity.LocationTemplateMapping;
import com.birdeye.campaign.enums.CampaignPriorityEnum;
import com.birdeye.campaign.enums.CustomFieldSourceEnum;
import com.birdeye.campaign.enums.SentimentCheckTypeEnum;
import com.birdeye.campaign.exception.CampaignHTTPException;
import com.birdeye.campaign.external.service.BusinessExternalService;
import com.birdeye.campaign.platform.constant.CampaignStatusEnum;
import com.birdeye.campaign.platform.constant.CommunicationCategoryEnum;
import com.birdeye.campaign.platform.constant.CustomFieldAssociatedObjectTypeEnum;
import com.birdeye.campaign.platform.constant.TemplateListingTypeEnum;
import com.birdeye.campaign.platform.constant.TemplateTypeEnum;
import com.birdeye.campaign.repository.BusinessEmailTemplateRepo;
import com.birdeye.campaign.repository.BusinessSMSTemplateRepo;
import com.birdeye.campaign.repository.CampaignCustomFieldAssociationRepo;
import com.birdeye.campaign.repository.CampaignRepo;
import com.birdeye.campaign.repository.CustomCampaignUrlRepo;
import com.birdeye.campaign.repository.EmailTemplateRepo;
import com.birdeye.campaign.request.ProductFeatureRequest;
import com.birdeye.campaign.response.external.BusinessFeaturesResponse;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.CampaignModificationAuditService;
import com.birdeye.campaign.service.CampaignSetupCachingService;
import com.birdeye.campaign.service.TemplateHelperService;
import com.birdeye.campaign.service.dao.LocationTemplateMappingDao;
import com.birdeye.campaign.utils.BusinessBrandingUtils;
import com.birdeye.campaign.utils.CampaignUtils;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.GlobalTemplatesUtils;
import com.birdeye.campaign.utils.TemplateUtils;
import com.birdeye.campaign.utils.TemplateValidationUtils;
import com.birdeye.campaign.utils.TwilioAPIUtils;
import com.birdeye.template.sms.dto.SmsTemplateMessage;

/**
 * <AUTHOR>
 *
 */
@Service("templateHelperService")
public class TemplateHelperServiceImpl implements TemplateHelperService {
	
	private static final Logger			logger		= LoggerFactory.getLogger(TemplateHelperServiceImpl.class);
	
	@Autowired
	private CampaignRepo				campaignRepo;
	
	@Autowired
	private BusinessEmailTemplateRepo	businessEmailTemplateRepo;
	
	@Autowired
	private BusinessSMSTemplateRepo		businessSMSTemplateRepo;
	
	@Autowired
	private BusinessExternalService		businessExternalService;
	
	@Autowired
	private CustomCampaignUrlRepo customCampaignUrlRepo;
	
	@Autowired
	private CampaignCustomFieldAssociationRepo customFieldAssociationRepo;
	
	@Autowired
	private CampaignSetupCachingService			campaignSetupCachingService;
	
	@Autowired
	private CacheService						cacheService;

	@Autowired
	private BusinessService						businessService;
	
	@Autowired
	private LocationTemplateMappingDao			locationTemplateMappingDao;
	
	@Autowired
	private CampaignModificationAuditService	campaignModificationAuditService;
	
	@Autowired
	private EmailTemplateRepo					emailTemplateRepo;
	
	static final long					DAY			= 24 * 60 * 60 * 1000;
	
	private static final String			SHORT_LINK	= "http://bit.ly/10JSQ1C";
	
	/**
	 * Populates email templates associated with given enterprise id to all templates list.
	 * 
	 * @param templates
	 * @param businessEmailTemplates
	 * @param enterpriseId
	 */
	@Override
	@Profiled
	public void populateEmailTemplates(List<BusinessTemplateMessage> templates, List<BusinessTemplateEntity> businessEmailTemplates, Integer enterpriseId,
			ProductFeatureRequest request, List<Integer> templateIdList, String listingEnumType) {
		logger.info("[TemplateHelperService] Entering populateEmailTemplates for enterpriseId : {}", enterpriseId);
		if (CollectionUtils.isEmpty(businessEmailTemplates)) {
			logger.info("No businessEmailTemplate found for enterpriseId : {}", enterpriseId);
			return;
		}
		Map<Integer, CampaignCustomEntity> customCampaignMap = (StringUtils.equalsIgnoreCase(listingEnumType, TemplateListingTypeEnum.SPLIT_CAMPAIGN_TAB.getType())) ? new HashMap<>()
				: getCustomCampaignEntityMapEmail(templateIdList);
		businessEmailTemplates.stream().forEach(template -> {
			// adding survey and referral templates only when enabled for business. Ideally they should not be in DB
			if ((template.getTemplateType().contains("survey") && !Objects.equals(request.getIsSurveyEnabled(), 1))
					|| (template.getTemplateType().contains("referral") && !Objects.equals(request.getEnableReferral(), 1))
					|| (template.getTemplateType().contains("appointment_reminder") && !Objects.equals(request.getAppointmentRemindersEnabled(), 1))
					|| (template.getTemplateType().contains("appointment_recall") && !Objects.equals(request.getAppointmentRecallEnabled(), 1))
					|| (template.getTemplateType().contains("appointment_form") && !Objects.equals(request.getAppointmentFormEnabled(), 1))
					|| (template.getTemplateType().contains("review_request")) && !Objects.equals(request.getReviewGenEnabled(), 1)
					|| (template.getTemplateType().contains("customer_experience")) && !Objects.equals(request.getReviewGenEnabled(), 1)) {
				return;
			} else {
				templates.add(getEmailTemplateMessageBasedUponListingEnumType(template, customCampaignMap, listingEnumType));
			}
		});
		logger.info("[TemplateHelperService] Exiting populateEmailTemplates for enterpriseId : {}", enterpriseId);
	}
	
	/**
	 * Prepare BusinessTemplateMessage based upon Listing call type
	 * 
	 * @param smsTemplate,customCampaignMap,
	 *            listingCallType
	 *
	 */
	private BusinessTemplateMessage getEmailTemplateMessageBasedUponListingEnumType(BusinessTemplateEntity businessEmailTemplate, Map<Integer, CampaignCustomEntity> customCampaignMap, String listingCallType) {
		if (StringUtils.equalsIgnoreCase(listingCallType, TemplateListingTypeEnum.SPLIT_CAMPAIGN_TAB.getType())) {
			return getTemplateMessageFromEmailTemplateEntity(businessEmailTemplate);
		}
		
		return getTemplateMessageFromEmailTemplateEntity(businessEmailTemplate, customCampaignMap);
	}
	
	private BusinessTemplateMessage getTemplateMessageFromEmailTemplateEntity(BusinessTemplateEntity businessEmailTemplate) {
		BusinessTemplateMessage templateMessage = new BusinessTemplateMessage();
		templateMessage.setId(businessEmailTemplate.getTemplateId());
		templateMessage.setName(businessEmailTemplate.getTemplateName());
		templateMessage.setTemplateType(businessEmailTemplate.getTemplateType());
		templateMessage.setType(Constants.TEMPLATE_BASE_TYPE_EMAIL);
		templateMessage.setCustomHtmlEnabled(businessEmailTemplate.getCustomHtmlEnabled());
		templateMessage.setUpdatedAt(businessEmailTemplate.getUpdatedAt());
		setDescriptionInEmailTemplateMessage(businessEmailTemplate, templateMessage);
		templateMessage.setTemplateCategory(
				TemplateUtils.getUnsubscriptionCategoryWithDefault(businessEmailTemplate.getEmailCategory(), businessEmailTemplate.getTemplateType()));
		
		return templateMessage;
	}

	private void setDescriptionInEmailTemplateMessage(BusinessTemplateEntity businessEmailTemplate, BusinessTemplateMessage templateMessage) {
		if (TemplateTypeEnum.CUSTOMER_EXPERIENCE.getName().equalsIgnoreCase(businessEmailTemplate.getTemplateType())) {
			templateMessage.setDescription(CoreUtils.replaceLineBreaks(getMessageBySentiment(businessEmailTemplate)));
		} else if (TemplateTypeEnum.REFERRAL.getName().equalsIgnoreCase(businessEmailTemplate.getTemplateType())) {
			templateMessage.setDescription(CoreUtils.replaceLineBreaks(businessEmailTemplate.getReviewMessage()));
		} else {
			templateMessage.setDescription(CoreUtils.replaceLineBreaks(businessEmailTemplate.getMessage()));
		}
	}
	
	private BusinessTemplateMessage getTemplateMessageFromEmailTemplateEntity(BusinessTemplateEntity businessEmailTemplate, Map<Integer, CampaignCustomEntity> customCampaignMap) {
		
		CampaignCustomEntity campaignCustomEntity = customCampaignMap.get(businessEmailTemplate.getTemplateId());
		
		BusinessTemplateMessage templateMessage = new BusinessTemplateMessage();
		templateMessage.setId(businessEmailTemplate.getTemplateId());
		templateMessage.setName(businessEmailTemplate.getTemplateName());
		templateMessage.setTemplateType(businessEmailTemplate.getTemplateType());
		templateMessage.setType(Constants.TEMPLATE_BASE_TYPE_EMAIL);
		templateMessage.setCanDelete(campaignCustomEntity != null ? campaignCustomEntity.getCampaignCount() : 0);
		templateMessage.setStatus(getTemplateStatus(businessEmailTemplate, campaignCustomEntity));
		templateMessage.setLabel(TemplateTypeEnum.valueOf(StringUtils.upperCase(templateMessage.getTemplateType())).getLabel());
		templateMessage.setUpdatedAt(businessEmailTemplate.getUpdatedAt());
		templateMessage.setAttachmentExists(CoreUtils.isTrueForInteger(businessEmailTemplate.getMmsEnabled()));
		templateMessage.setCustomHtmlEnabled(businessEmailTemplate.getCustomHtmlEnabled());
		templateMessage.setIsDefaultTemplate(businessEmailTemplate.getIsDefaultTemplate());	
		setDescriptionInEmailTemplateMessage(businessEmailTemplate, templateMessage);
		if (TemplateTypeEnum.APPOINTMENT_FORM.getName().equalsIgnoreCase(businessEmailTemplate.getTemplateType())) {
			if(StringUtils.isEmpty(businessEmailTemplate.getFormUrl())) {
				templateMessage.setFormUrlPresent(false);
			} else {
				templateMessage.setFormUrlPresent(true);
			}
		}
		templateMessage.setTemplateCategory(
				TemplateUtils.getUnsubscriptionCategoryWithDefault(businessEmailTemplate.getEmailCategory(), businessEmailTemplate.getTemplateType()));
		
		return templateMessage;
	}
	
	public static String getMessageBySentiment(BusinessTemplateEntity businessEmailOrSmsTemplate) {
		
		SentimentCheckTypeEnum sentimentCheckTypeEnum = SentimentCheckTypeEnum.findByKey(businessEmailOrSmsTemplate.getSentimentCheckType());
		if (sentimentCheckTypeEnum == null)
			return null;
		switch (sentimentCheckTypeEnum) {
			case YES_NO:
				return businessEmailOrSmsTemplate.getSentimentMessage();
			
			case SENTIMENT:
				return businessEmailOrSmsTemplate.getSentimentMessage();
			
			case STAR:
				return businessEmailOrSmsTemplate.getStarMessage();
			
			case NPS:
				return businessEmailOrSmsTemplate.getMessage();
			
			default:
				return null;
		}
	}
	
	private static String getCXSMSDescriptionMessage(BusinessTemplateEntity smsTemplate) {
		return smsTemplate.getMessageBody();
	}
	
	/**s
	 * 
	 * Get template progress status
	 *
	 * 1 : recently created and campaign count = 0
	 * 0 : in use
	 * -1 : not in use
	 * 
	 *
	 * 
	 * @param businessEmailTemplate
	 * @param campaignCustomEntity
	 * @return
	 */
	private Integer getTemplateStatus(BusinessTemplateEntity businessEmailTemplate, CampaignCustomEntity campaignCustomEntity) {
		if (businessEmailTemplate == null || GlobalTemplatesUtils.isGlobalSmsTemplate(businessEmailTemplate.getIsGlobalTemplate())) {
			return -1;
		}
		Integer recentlyCreatedPeriod = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("templates.listing.recently.created.period", 2);
		// If current day : CREATED and campaign count = 0
		if (businessEmailTemplate.getCreatedAt() != null && (businessEmailTemplate.getCreatedAt().getTime() > System.currentTimeMillis() - (recentlyCreatedPeriod * DAY))
				&& (campaignCustomEntity == null || campaignCustomEntity.getCampaignCount() == null || campaignCustomEntity.getCampaignCount().intValue() == 0)) {
			return 1;
		}
		// IN USE
		if (campaignCustomEntity != null && campaignCustomEntity.getCampaignCount() != null && campaignCustomEntity.getCampaignCount().intValue() > 0) {
			return 0;
		}
		// NOT IN USE
		if (campaignCustomEntity != null && (campaignCustomEntity.getCampaignCount() == null || campaignCustomEntity.getCampaignCount().intValue() == 0)) {
			return -1;
		}
		return -1;
	}

	/**
	 * Populates sms templates associated with given enterprise id to all templates list.
	 * 
	 * @param templates
	 * @param smsTemplates
	 * @param enterpriseId
	 * @param request
	 * @param customCampaignMap
	 */
	@Override
	@Profiled
	public void populateSmsTemplates(List<BusinessTemplateMessage> templates, List<BusinessTemplateEntity> smsTemplates, Integer enterpriseId, ProductFeatureRequest request,
			Map<Integer, CampaignCustomEntity> customCampaignMap, Map<Integer, List<Integer>> templateToBusinessMapping, String listingEnumType) {
		
		if (CollectionUtils.isNotEmpty(smsTemplates)) {
			smsTemplates.stream().forEach(smsTemplate -> {
				// adding survey templates only when survey enabled for business
				if ((smsTemplate.getTemplateType().contains("survey") && !Objects.equals(request.getIsSurveyEnabled(), 1))
						|| (smsTemplate.getTemplateType().contains("referral") && !Objects.equals(request.getEnableReferral(), 1))
						|| (smsTemplate.getTemplateType().contains("appointment_reminder") && !Objects.equals(request.getAppointmentRemindersEnabled(), 1))
						|| (smsTemplate.getTemplateType().contains("appointment_recall") && !Objects.equals(request.getAppointmentRecallEnabled(), 1))
						|| (smsTemplate.getTemplateType().contains("appointment_form") && !Objects.equals(request.getAppointmentFormEnabled(), 1))
						|| (smsTemplate.getTemplateType().contains("review_request") && !Objects.equals(request.getReviewGenEnabled(), 1))
						|| (smsTemplate.getTemplateType().contains("customer_experience") && !Objects.equals(request.getReviewGenEnabled(), 1))) {
					return;
				} else {
					templates.add(getSmsTemplateMessageBasedUponListingTypeEnum(smsTemplate, customCampaignMap,
							templateToBusinessMapping, listingEnumType,enterpriseId));
				}
			});
		}
	}
	
	/**
	 * Prepare BusinessTemplateMessage based upon Listing call type
	 * 
	 * @param smsTemplate,customCampaignMap,
	 *            templateToBusinessMapping, listingCallType
	 *
	 */
	private BusinessTemplateMessage getSmsTemplateMessageBasedUponListingTypeEnum(BusinessTemplateEntity smsTemplate,
			Map<Integer, CampaignCustomEntity> customCampaignMap, Map<Integer, List<Integer>> templateToBusinessMapping,
			String listingEnumType,Integer enterpriseId) {
		if (StringUtils.equalsIgnoreCase(listingEnumType, TemplateListingTypeEnum.SPLIT_CAMPAIGN_TAB.getType())) {
			return getTemplateMessageFromSmsTemplateEntity(smsTemplate,enterpriseId);
		}
		return getTemplateMessageFromSmsTemplateEntity(smsTemplate, customCampaignMap, templateToBusinessMapping,enterpriseId);
	}
	
	private BusinessTemplateMessage getTemplateMessageFromSmsTemplateEntity(BusinessTemplateEntity smsTemplate,Integer enterpriseId) {
		String unsubscribeText = StringUtils.EMPTY;
		BusinessTemplateMessage templateMessage = new BusinessTemplateMessage();
		templateMessage.setId(smsTemplate.getTemplateId());
		templateMessage.setName(smsTemplate.getTemplateName());
		templateMessage.setTemplateType(smsTemplate.getTemplateType() + "_sms");
		templateMessage.setType("sms");
		templateMessage.setUpdatedAt(smsTemplate.getUpdatedAt());
		templateMessage.setIsGlobalTemplate(smsTemplate.getIsGlobalTemplate());
		String description = getDescriptionByTypeForSMS(smsTemplate);
		templateMessage.setDescription(CoreUtils.replaceLineBreaks(description));
		templateMessage.setTemplateCategory(TemplateUtils.getUnsubscriptionCategoryWithDefault(smsTemplate.getSmsCategory(),smsTemplate.getTemplateType()));
		if (smsTemplate.getUnsubscribeTextEnabled() != null && smsTemplate.getUnsubscribeTextEnabled() == 1) {
			unsubscribeText = StringUtils.isBlank(smsTemplate.getUnsubscribeText()) ? "Txt STOP to unsub." : smsTemplate.getUnsubscribeText();
			if(extractTextCategoryEnabledFlag(enterpriseId)){
				unsubscribeText=prepareUnsubscribeText(templateMessage.getTemplateCategory());
			}
			templateMessage.setUnsubscribeText(unsubscribeText);
			templateMessage.setIncludeUnsubscribeText(smsTemplate.getUnsubscribeTextEnabled());
		}
		
		return templateMessage;
	}

    /**
     * 
	 * Get non editable field based upon whether unsubscribe text is blank or not.
	 * 
	 * @param unsubscribeText
	 */
	private String getNonEditableField(String unsubscribeText) {
		if (StringUtils.isBlank(unsubscribeText)) {
			return StringUtils.join(SHORT_LINK, StringUtils.SPACE);
		}
		return StringUtils.join(SHORT_LINK, CampaignUtils.getNewLineByCount(1), unsubscribeText);
	}

	private BusinessTemplateMessage getTemplateMessageFromSmsTemplateEntity(BusinessTemplateEntity smsTemplate,
			Map<Integer, CampaignCustomEntity> customCampaignMap,
			Map<Integer, List<Integer>> templateToBusinessMapping,Integer enterpriseId) {

		CampaignCustomEntity campaignCustomEntity = customCampaignMap.get(smsTemplate.getTemplateId());
		String unsubscribeText = StringUtils.EMPTY;
		
		BusinessTemplateMessage templateMessage = new BusinessTemplateMessage();
		templateMessage.setId(smsTemplate.getTemplateId());
		templateMessage.setName(smsTemplate.getTemplateName());
		templateMessage.setTemplateType(smsTemplate.getTemplateType() + "_sms");
		templateMessage.setType("sms");
		templateMessage.setCanDelete(campaignCustomEntity != null ? campaignCustomEntity.getCampaignCount() : 0);
		templateMessage.setStatus(getTemplateStatus(smsTemplate, campaignCustomEntity));
		templateMessage.setLabel(TemplateTypeEnum.valueOf(StringUtils.upperCase(templateMessage.getTemplateType())).getLabel());
		templateMessage.setUpdatedAt(smsTemplate.getUpdatedAt());
		templateMessage.setIsGlobalTemplate(smsTemplate.getIsGlobalTemplate());
		templateMessage.setIsDefaultTemplate(smsTemplate.getIsDefaultTemplate());
		templateMessage.setIncludeImageWithText(smsTemplate.getMmsEnabled() != null ? smsTemplate.getMmsEnabled() : 0);		
		String description = getDescriptionByTypeForSMS(smsTemplate);
		templateMessage.setDescription(CoreUtils.replaceLineBreaks(description));
		templateMessage.setTemplateCategory(TemplateUtils.getUnsubscriptionCategoryWithDefault(smsTemplate.getSmsCategory(),smsTemplate.getTemplateType()));
		if (smsTemplate.getUnsubscribeTextEnabled() != null && smsTemplate.getUnsubscribeTextEnabled() == 1) {
			unsubscribeText = StringUtils.isBlank(smsTemplate.getUnsubscribeText()) ? "Txt STOP to unsub." : smsTemplate.getUnsubscribeText();
			if (extractTextCategoryEnabledFlag(enterpriseId)) {
				unsubscribeText = prepareUnsubscribeText(templateMessage.getTemplateCategory());
			}
			templateMessage.setUnsubscribeText(unsubscribeText);
			templateMessage.setIncludeUnsubscribeText(smsTemplate.getUnsubscribeTextEnabled());
		}
		boolean highCharacterLimitFlag = extractHighCharacterLimitFlagFromResponse(enterpriseId);
		if (TemplateValidationUtils.validateSMSTemplateForHighCharacterLimit(smsTemplate, highCharacterLimitFlag)) {
			BusinessEnterpriseEntity business = cacheService.getBusinessById(enterpriseId);
			String customMessageWithUnsubscribeText = getCustomTextMessage(templateMessage, description,
					templateMessage.getUnsubscribeText());
			templateMessage.setSmsSegments(TwilioAPIUtils.getCustomSMSSegments(
					StringUtils.replace(customMessageWithUnsubscribeText, "<br />", "\n"), smsTemplate.getMediaUrls(), business.getCountryCode()));
		}
		if (StringUtils.equalsIgnoreCase(smsTemplate.getTemplateType(), TemplateTypeEnum.APPOINTMENT_FORM_SMS.getName())) {
			if(StringUtils.isEmpty(smsTemplate.getFormUrl())) {
				templateMessage.setFormUrlPresent(false);
			} else {
				templateMessage.setFormUrlPresent(true);
			}	
		}
		
		if (smsTemplate.getTemplateType().equalsIgnoreCase(TemplateTypeEnum.PROMOTION_SMS.getName()) || smsTemplate.getTemplateType().equalsIgnoreCase(TemplateTypeEnum.REFERRAL.getName())) {
			templateMessage.setAttachmentExists(!StringUtils.isBlank(smsTemplate.getMediaUrls()));
		} else {
			templateMessage.setAttachmentExists(CoreUtils.isTrueForInteger(smsTemplate.getMmsEnabled()));
		}
		//TODO: MESSENGER FLOW : MDC.get("businessId")) != null, REMOVE IT AND MAKE IT FLAG BASED
		
		if (isTemplatesFlowForMessenger()) {
			BusinessEnterpriseEntity business = getValidBusiness(CoreUtils.getNullSafeInteger(MDC.get("businessId")));
			BrandingInfo enterpriseBrandingInfo = null;
			if (GlobalTemplatesUtils.isNotGlobalSmsTemplate(smsTemplate.getIsGlobalTemplate())) {
				enterpriseBrandingInfo = businessExternalService.getBrandingInfoByEnterpriseIdCached(smsTemplate.getEnterpriseId());
			}
			if (smsTemplate.getTemplateType().equalsIgnoreCase(TemplateTypeEnum.PROMOTION_SMS.getName())) {
				templateMessage.setEditable(smsTemplate.getRecommendPageMessage());
				templateMessage.setMediaUrl(smsTemplate.getMediaUrls() != null ? smsTemplate.getMediaUrls().split(",")[0] : null);
				// setting selected custom fields as required in Inbox to edit custom sms template.
				templateMessage.setSelectedCustomFields(GlobalTemplatesUtils.isGlobalSmsTemplate(smsTemplate.getIsGlobalTemplate()) ? Collections.emptyList()
						: getTemplateCustomFieldsAssociation(smsTemplate.getTemplateId(), Constants.TEMPLATE_BASE_TYPE_SMS));
				//BIRDEYEV2-8491 location level template
				templateMessage.setSelectedLocations(getTemplateLocations(smsTemplate.getTemplateId(), templateToBusinessMapping));
				templateMessage.setLocationTemplate(smsTemplate.getLocationLevelTemplate());
				templateMessage.setLocationCustomFields(GlobalTemplatesUtils.isGlobalSmsTemplate(smsTemplate.getIsGlobalTemplate()) ? Collections.emptyList()
						: getTemplateLocationCustomFieldsAssociation(smsTemplate.getTemplateId(), Constants.TEMPLATE_BASE_TYPE_SMS));
			} else if ((smsTemplate.getTemplateType().equalsIgnoreCase(TemplateTypeEnum.REFERRAL.getName()))) {
				templateMessage.setEditable(StringUtils.replaceEach(smsTemplate.getMessageBody(), new String[] { TemplateStaticTokens.BUSINESS_NAME_TOKEN, SHORTLINK_TOKEN, TemplateStaticTokens.BUSINESS_LOCATION_ALIAS_TOKEN }, new String[] { business.getBusinessName(), "", business.getBusinessAlias() }));
				templateMessage.setMediaUrl(smsTemplate.getMediaUrls() != null ? smsTemplate.getMediaUrls().split(",")[0] : null);
				templateMessage.setNonEditable(getNonEditableField(unsubscribeText));
			} else if(smsTemplate.getTemplateType().equalsIgnoreCase(TemplateTypeEnum.APPOINTMENT_FORM_SMS.getName())) {
				templateMessage.setEditable(StringUtils.replaceEach(smsTemplate.getMessageBody(), new String[] { TemplateStaticTokens.BUSINESS_NAME_TOKEN, SHORTLINK_TOKEN, TemplateStaticTokens.BUSINESS_LOCATION_ALIAS_TOKEN }, new String[] { business.getBusinessName(), "", business.getBusinessAlias() }));
				templateMessage.setLocationBrandingEnabled(smsTemplate.getLocationBrandingEnabled());
				templateMessage.setMediaUrl(CampaignUtils.getFirstPropertyFromCommaSeparatedList(smsTemplate.getMediaUrls()));
				
			}
			else {
				templateMessage.setEditable(StringUtils.replaceEach(smsTemplate.getMessageBody(), new String[] { TemplateStaticTokens.BUSINESS_NAME_TOKEN, SHORTLINK_TOKEN, TemplateStaticTokens.BUSINESS_LOCATION_ALIAS_TOKEN }, new String[] { business.getBusinessName(), "", business.getBusinessAlias() }));
				templateMessage.setNonEditable(getNonEditableField(unsubscribeText));
				templateMessage.setLocationBrandingEnabled(smsTemplate.getLocationBrandingEnabled());
				templateMessage.setMediaUrl(getMediaUrl(business.getBusinessId(), enterpriseBrandingInfo, smsTemplate));
			}
			
			templateMessage.setRawDescription(getDescriptionByTypeForSMS(smsTemplate));
		}
		return templateMessage;
	}

	
	
	private List<Integer> getTemplateLocations(Integer templateId, Map<Integer, List<Integer>> templateToBusinessMapping) {
		if (MapUtils.isNotEmpty(templateToBusinessMapping)) {
			return templateToBusinessMapping.get(templateId);
		}
		return null;
	}

	private String getCustomTextMessage(BusinessTemplateMessage templateMessage, String description,String unsubscribeText) {
		if(StringUtils.isNotBlank(unsubscribeText)) {
			return StringUtils.join(description, " <br /> ", templateMessage.getUnsubscribeText());
		}
		return description;
	}

	/**
	 * @return
	 */
	private boolean isTemplatesFlowForMessenger() {
		return StringUtils.equalsIgnoreCase(MDC.get("isMessenger"), Boolean.TRUE.toString());
	}
	
	private String getDescriptionByTypeForSMS(BusinessTemplateEntity smsTemplate) {
		if (StringUtils.equalsAnyIgnoreCase(smsTemplate.getTemplateType(), TemplateTypeEnum.REVIEW_REQUEST_SMS.getName(), TemplateTypeEnum.SURVEY_REQUEST_SMS.getName(),
				TemplateTypeEnum.REFERRAL.getName(), TemplateTypeEnum.REFERRAL_SMS.getName(), TemplateTypeEnum.APPOINTMENT_REMINDER_SMS.getName(),
				TemplateTypeEnum.APPOINTMENT_RECALL_SMS.getName())) {
			return smsTemplate.getMessageBody();
		}
		if (StringUtils.equalsAnyIgnoreCase(smsTemplate.getTemplateType(), TemplateTypeEnum.PROMOTION_SMS.getName(), TemplateTypeEnum.APPOINTMENT_FORM_SMS.getName())) {
			return getPromotionDescriptionMessage(smsTemplate);
		}
		if (StringUtils.equalsIgnoreCase(smsTemplate.getTemplateType(), TemplateTypeEnum.CUSTOMER_EXPERIENCE.getName())) {
			return getCXSMSDescriptionMessage(smsTemplate);
		}
		
		return null;
	}
	
	private String getPromotionDescriptionMessage(BusinessTemplateEntity smsTemplateMessage) {
		if (GlobalTemplatesUtils.isGlobalSmsTemplate(smsTemplateMessage.getIsGlobalTemplate())) {
			return smsTemplateMessage.getMessageBody();
		}
		List<CustomCampaignUrl> customCampaignUrlList = customCampaignUrlRepo.findByTemplateId(smsTemplateMessage.getTemplateId());
		if (smsTemplateMessage.getMessageBody() != null) {
			for (CustomCampaignUrl customCampaignUrl : customCampaignUrlList) {
				smsTemplateMessage.setMessageBody(smsTemplateMessage.getMessageBody().replace(customCampaignUrl.getUrlToken(), customCampaignUrl.getUrlValue()));
			}
		}
		return smsTemplateMessage.getMessageBody();
	}

	private BusinessEnterpriseEntity getValidBusiness(Integer businessId) {
		logger.info("Validating business for business id : {}", businessId);
		if (businessId == null) {
			throw new CampaignHTTPException(HttpStatus.BAD_REQUEST, "Business Id can not be blank.");
		}
		BusinessEnterpriseEntity business = cacheService.getValidBusinessById(businessId);
		if (business == null) {
			throw new CampaignHTTPException(HttpStatus.BAD_REQUEST, "Invalid Business Id.");
		}
		return business;
	}
	
	private String getMediaUrl(Long businessId, BrandingInfo enterpriseBrandingInfo, BusinessTemplateEntity smsTemplate) {
		String mediaUrl = null;
		if (smsTemplate.getMmsEnabled() != null && smsTemplate.getMmsEnabled() == 1) {
			mediaUrl = getMediaUrlByMediaType(enterpriseBrandingInfo, businessId);
		}
		return mediaUrl;
	}
	
	private String getMediaUrlByMediaType(BrandingInfo brandingInfo, Long businessId) {
		String mediaUrl = null;
		if ("custom".equalsIgnoreCase(brandingInfo.getMediaType())) {
			mediaUrl = BusinessBrandingUtils.getAbsoluteCDNImageURLForBusiness(brandingInfo.getCustomImageUrl(), businessId);
		} else {
			mediaUrl = BusinessBrandingUtils.getAbsoluteImageURL(brandingInfo.getBrandingImage(), false);
		}
		return mediaUrl;
	}
	
	/**
	 * Soft delete email template from business_email_template.
	 * 
	 * @param businessEmailTemplates
	 */
	@Override
	public void deleteEmailTemplate(List<BusinessEmailTemplate> businessEmailTemplates) {
		BusinessEmailTemplate template = businessEmailTemplates.get(0);
		if (template == null) {
			logger.info("Email templates list is empty.");
			return;
		}
		logger.info("Deleting email template for templateId : {}", template.getEmailTemplateId());
		Integer activeCampaigns = campaignRepo.getActiveCampaignForEmailTemplateAndEnterprise(template.getEmailTemplateId(), template.getEnterpriseId());
		if (activeCampaigns == null || activeCampaigns == 0) {
			template.setIsDeleted(1);
			businessEmailTemplateRepo.saveAndFlush(template);
			// soft deleting custom fields association.
			customFieldAssociationRepo.updateDeleteFlag(1, template.getId(), CustomFieldAssociatedObjectTypeEnum.EMAIL_TEMPLATE.getObjectType());
			
			// BIRD-74509 - Publish modification event
			campaignModificationAuditService.prepareAndPublishTemplateDeleteEvent(template.getEmailTemplateId(), Constants.EMAIL_TYPE, CoreUtils.getNullSafeInteger(MDC.get(Constants.USER_ID)),
					template.getEnterpriseId(), Instant.now().toEpochMilli());
		} else {
			throw new CampaignHTTPException(HttpStatus.BAD_REQUEST, StringUtils.join("You cannot delete this template because it is being used in ", activeCampaigns, " campaign(s)."));
		}
	}
	
	/**
	 * Soft delete sms template from business_sms_template.
	 * 
	 * @param businessSmsTemplates
	 */
	@Override
	public void deleteSmsTemplate(List<BusinessSmsTemplate> businessSmsTemplates) {
		BusinessSmsTemplate template = businessSmsTemplates.get(0);
		if (template == null) {
			logger.info("SMS templates list is empty.");
			return;
		}
		logger.info("Deleting sms template for templateId : {}", template.getId());
		Integer activeCampaigns = campaignRepo.getActiveCampaignForSmsTemplateAndEnterprise(template.getId(), template.getEnterpriseId());
		if (activeCampaigns == null || activeCampaigns == 0) {
			template.setDeleted(1);
			businessSMSTemplateRepo.saveAndFlush(template);
			if (CoreUtils.getBooleanValueFromInteger(template.getLocationLevelTemplate())) {
				List<LocationTemplateMapping> locationTemplateMapping = locationTemplateMappingDao.getLocationTemplateMapping(template.getId());
				locationTemplateMappingDao.deleteTemplateBusinessMapping(locationTemplateMapping);
				
			}
			// soft deleting custom fields association.
			customFieldAssociationRepo.updateDeleteFlag(1, template.getId(), CustomFieldAssociatedObjectTypeEnum.SMS_TEMPLATE.getObjectType());
			
			// BIRD-74509 - Publish modification event
			campaignModificationAuditService.prepareAndPublishTemplateDeleteEvent(template.getId(), Constants.SMS_TYPE, CoreUtils.getNullSafeInteger(MDC.get(Constants.USER_ID)),
					template.getEnterpriseId(), Instant.now().toEpochMilli());
		} else {
			throw new CampaignHTTPException(HttpStatus.BAD_REQUEST, StringUtils.join("You cannot delete this template because it is being used in ", activeCampaigns, " campaign(s)."));
		}
	}
	
	public Map<Integer, CampaignCustomEntity> getCustomCampaignEntityMapEmail(List<Integer> templateIdsList) {
		
		List<CampaignCustomEntity> campaignCustomEntityList = campaignRepo.getCampaignListForEmailTemplateIds(templateIdsList);
		Map<Integer, CampaignCustomEntity> customCampaignMap = new HashMap<>();
		campaignCustomEntityList.stream().forEach(campaign -> {
			if (customCampaignMap.containsKey(campaign.getTemplateId())) {
				CampaignCustomEntity entity = customCampaignMap.get(campaign.getTemplateId());
				if (CampaignStatusEnum.SCHEDULED.getStatus() == campaign.getCampaignStatus() || CampaignStatusEnum.ACTIVE.getStatus() == campaign.getCampaignStatus()
						|| CampaignStatusEnum.PAUSED.getStatus() == campaign.getCampaignStatus()) {
					entity.setCampaignCount(entity.getCampaignCount() + 1);
				}
			} else {
				if (CampaignStatusEnum.SCHEDULED.getStatus() == campaign.getCampaignStatus() || CampaignStatusEnum.ACTIVE.getStatus() == campaign.getCampaignStatus()
						|| CampaignStatusEnum.PAUSED.getStatus() == campaign.getCampaignStatus()) {
					campaign.setCampaignCount(1);
				}
				customCampaignMap.put(campaign.getTemplateId(), campaign);
			}
		});
		
		return customCampaignMap;
	}
	
	@Override
	public Map<Integer, CampaignCustomEntity> getCustomCampaignEntityMapSms(List<Integer> templateIdsList) {
		
		List<CampaignCustomEntity> campaignCustomEntityList = campaignRepo.getCampaignListForSmsTemplateIds(templateIdsList);
		Map<Integer, CampaignCustomEntity> customCampaignMap = new HashMap<>();
		campaignCustomEntityList.stream().forEach(campaign -> {
			if (customCampaignMap.containsKey(campaign.getTemplateId())) {
				CampaignCustomEntity entity = customCampaignMap.get(campaign.getTemplateId());
				if (CampaignStatusEnum.SCHEDULED.getStatus() == campaign.getCampaignStatus() || CampaignStatusEnum.ACTIVE.getStatus() == campaign.getCampaignStatus()
						|| CampaignStatusEnum.PAUSED.getStatus() == campaign.getCampaignStatus()) {
					entity.setCampaignCount(entity.getCampaignCount() + 1);
				}
			} else {
				if (CampaignStatusEnum.SCHEDULED.getStatus() == campaign.getCampaignStatus() || CampaignStatusEnum.ACTIVE.getStatus() == campaign.getCampaignStatus()
						|| CampaignStatusEnum.PAUSED.getStatus() == campaign.getCampaignStatus()) {
					campaign.setCampaignCount(1);
				}
				customCampaignMap.put(campaign.getTemplateId(), campaign);
			}
		});
		
		return customCampaignMap;
	}
	
	@Override
	@Async
	public void saveTemplateCustomFieldsAssociation(Integer templateId, String templateType, List<TemplateCustomFieldSRO> customFieldsList, Integer userId,
			boolean deleteCustomFields) {
		// first delete all custom fields for that object id and type
		if (deleteCustomFields) {
			int count = customFieldAssociationRepo.deleteCampaignCustomField(templateId, CustomFieldAssociatedObjectTypeEnum.getObjectTypeByCommType(templateType),
					CustomFieldSourceEnum.CONTACT);
			logger.info("for object {} and type {} the fields {} has been deleted", templateId, CustomFieldAssociatedObjectTypeEnum.getObjectTypeByCommType(templateType), count);
		}
		if (CollectionUtils.isEmpty(customFieldsList)) {
			return;
		}
		List<CampaignCustomFieldAssociation> customFieldAssocnList = new ArrayList<>();
		for (TemplateCustomFieldSRO customField : customFieldsList) {
			CampaignCustomFieldAssociation fieldAssociation = new CampaignCustomFieldAssociation();
			fieldAssociation.setAssociatedObjectId(templateId);
			fieldAssociation.setAssociatedType(CustomFieldAssociatedObjectTypeEnum.getObjectTypeByCommType(templateType));
			fieldAssociation.setCustomFieldName(customField.getName());
			fieldAssociation.setCustomFieldId(customField.getId());
			fieldAssociation.setCustomFieldLabel(replaceSpaceWithUnderScore(customField.getName()));
			fieldAssociation.setUpdatedBy(userId);
			fieldAssociation.setCustomFieldSource(CustomFieldSourceEnum.CONTACT);
			customFieldAssocnList.add(fieldAssociation);
		}
		customFieldAssociationRepo.saveAll(customFieldAssocnList);
	}
	
	/**
	 * Fetch custom fields associations for a template.
	 */
	@Override
	public List<TemplateCustomFieldSRO> getTemplateCustomFieldsAssociation(Integer templateId, String templateType) {
		logger.info("Getting CampaignCustomFieldAssociation for templateId {} and type {}", templateId, templateType);
		List<CampaignCustomFieldAssociation> customFieldAssociation = customFieldAssociationRepo.getCampaignCustomFieldAssociationBySource(templateId,
				CustomFieldAssociatedObjectTypeEnum.getObjectTypeByCommType(templateType), CustomFieldSourceEnum.CONTACT);
//		customFieldAssociation = customFieldAssociation.stream()
//				.filter(field -> StringUtils.equalsIgnoreCase(field.getCustomFieldSource().getType(), CustomFieldSourceEnum.CONTACT.getType())).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(customFieldAssociation)) {
			return Collections.emptyList();
		}
		return customFieldAssociation.stream().map(c -> new TemplateCustomFieldSRO(c.getCustomFieldId(), c.getCustomFieldName())).collect(Collectors.toList());
	}
	
	private String replaceSpaceWithUnderScore(String operand) {
		if (operand.indexOf(' ') != -1) {
			return "$" + StringUtils.replace(operand, " ", "_");
		}
		return "$" + operand;
	}
	
	@Override
	public String getDistributionChartTypeForBusinessIds(List<Integer> businessIds, Integer campaignId) {
		String defaultDistributionChartType = "nps";
		logger.info("fetching distributionChartType for campaign id {} and businessIds {}", campaignId, businessIds);
		if (campaignId != null && campaignId != 0) {
			return getSentimentTypeForCXCampaignReport(campaignId);
		} else if (CollectionUtils.isNotEmpty(businessIds)) {
			int minBusinessId = Collections.min(businessIds);
			BusinessEnterpriseEntity enterpriseOrSMBBusiness = cacheService.getBusinessById(minBusinessId);
			enterpriseOrSMBBusiness = businessService.getEnterpriseorSmb(enterpriseOrSMBBusiness);
			logger.info("Enterprise/SMB ID while fetching sentiment type for usage report is {}", enterpriseOrSMBBusiness.getId());
			return getSentimentTypeByBusinessId(enterpriseOrSMBBusiness);
			
		}
		return defaultDistributionChartType;
	}
	
	private String getSentimentTypeByBusinessId(BusinessEnterpriseEntity business) {
		Integer parentEnterpriseOrSMBId = business.getId();
		List<Campaign> ongoingCampaigns = campaignRepo.getCampaignsByEnterpriseIdAndTypeAndRunType(parentEnterpriseOrSMBId, "cx_request", "ongoing");
		Campaign campaign = null;
		logger.info("Ongoing Campaigns count picked to fetch sentiment type for business {} is {}", parentEnterpriseOrSMBId, CollectionUtils.size(ongoingCampaigns));
		if (CollectionUtils.isNotEmpty(ongoingCampaigns)) {
			campaign = ongoingCampaigns.get(0);
		} else {
			List<Campaign> manualCampaigns = campaignRepo.getCampaignsByEnterpriseIdAndTypeAndRunType(parentEnterpriseOrSMBId, "cx_request", "manual");
			logger.info("Manual Campaigns count picked to fetch sentiment type for business {} is {}", parentEnterpriseOrSMBId, CollectionUtils.size(manualCampaigns));
			
			if (CollectionUtils.isNotEmpty(manualCampaigns)) {
				campaign = manualCampaigns.get(0);
			}
		}
		
		if (campaign == null) {
			return "nps";
		}
		logger.info("Campaign picked to fetch sentiment type for business {} is {}", parentEnterpriseOrSMBId, campaign.getId());
		
		return getdefaultSentimentTypeForCXCampaign(campaign);
	}
	
	public String getSentimentTypeForCXCampaignReport(Integer campaignId) {
		Campaign campaign = campaignSetupCachingService.getCampaignById(campaignId);
		return getdefaultSentimentTypeForCXCampaign(campaign);
	}
	
	private String getdefaultSentimentTypeForCXCampaign(Campaign campaign) {
		String defaultSentimentType = null;
		if (campaign != null) {
			CampaignPriorityEnum campaignPriority = CampaignPriorityEnum.getEnum(campaign.getPriority());
			switch (campaignPriority) {
				case EMAIL:
				case EMAIL_SMS:
				case EMAIL_AND_SMS:
					defaultSentimentType = businessEmailTemplateRepo.getSentimentCheckTypeForTemplateId(campaign.getTemplateId(), campaign.getEnterpriseId(), campaign.getEnterpriseId());
					break;
				case SMS:
				case SMS_EMAIL:
					defaultSentimentType = businessSMSTemplateRepo.getSentimentCheckTypeForTemplateId(campaign.getSmsTemplateId());
					break;
				default:
					break;
			}
			return defaultSentimentType;
		} else {
			return "nps";
		}
	}
	
	@Override
	//@Cacheable(key = "#root.methodName", value = "freeTextSmsTemplateCache1", unless = "#result == null")
	public BusinessSmsTemplate getFreeTextSmsTemplateId() {
		return CacheManager.getInstance().getCache(DefaultSmsTemplatesCache.class).getSmsTemplate(Constants.FREE_TEXT_TEMPLATE_TYPE);
	}
	
	/**
	 * Fetch Appointment custom fields associations for a template.
	 */
	@Override
	public List<TemplateCustomFieldSRO> getAppointmentTemplateCustomFieldsAssociation(Integer templateId, String templateType) {
		logger.info("Getting Appointment CampaignCustomFieldAssociation for templateId {} and type {}", templateId, templateType);
		List<CampaignCustomFieldAssociation> customFieldAssociation = customFieldAssociationRepo.getCampaignCustomFieldAssociationBySource(templateId,
				CustomFieldAssociatedObjectTypeEnum.getObjectTypeByCommType(templateType), CustomFieldSourceEnum.APPOINTMENT);
//		customFieldAssociation = customFieldAssociation.stream()
//				.filter(field -> StringUtils.equalsIgnoreCase(field.getCustomFieldSource().getType(), CustomFieldSourceEnum.APPOINTMENT.getType())).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(customFieldAssociation)) {
			return Collections.emptyList();
		}
		return customFieldAssociation.stream().map(c -> new TemplateCustomFieldSRO(c.getCustomFieldId(), c.getCustomFieldName())).collect(Collectors.toList());
	}
	
	@Override
	@Async
	public void saveAppointmentTemplateCustomFieldsAssociation(Integer templateId, String templateType, List<TemplateCustomFieldSRO> appointmentCustomFields,
			Integer userId, boolean deleteCustomFields) {
		// first delete all custom fields for that object id and type
		if (deleteCustomFields) {
			int count = customFieldAssociationRepo.deleteCampaignCustomField(templateId, CustomFieldAssociatedObjectTypeEnum.getObjectTypeByCommType(templateType),
					CustomFieldSourceEnum.APPOINTMENT);
			logger.info("for object {} and type {} the fields {} has been deleted", templateId, CustomFieldAssociatedObjectTypeEnum.getObjectTypeByCommType(templateType), count);
		}
		if (CollectionUtils.isEmpty(appointmentCustomFields)) {
			return;
		}
		List<CampaignCustomFieldAssociation> customFieldAssocnList = new ArrayList<>();
		for (TemplateCustomFieldSRO customField : appointmentCustomFields) {
			CampaignCustomFieldAssociation fieldAssociation = new CampaignCustomFieldAssociation();
			fieldAssociation.setAssociatedObjectId(templateId);
			fieldAssociation.setAssociatedType(CustomFieldAssociatedObjectTypeEnum.getObjectTypeByCommType(templateType));
			fieldAssociation.setCustomFieldName(customField.getName());
			fieldAssociation.setCustomFieldId(customField.getId());
			fieldAssociation.setCustomFieldLabel(replaceSpaceWithUnderScore(customField.getName()));
			fieldAssociation.setUpdatedBy(userId);
			fieldAssociation.setCustomFieldSource(CustomFieldSourceEnum.APPOINTMENT);
			customFieldAssocnList.add(fieldAssociation);
		}
		customFieldAssociationRepo.saveAll(customFieldAssocnList);
	}
	
	/**
	 * Fetch Location custom fields associations for a template.
	 */
	@Override
	public List<TemplateCustomFieldSRO> getTemplateLocationCustomFieldsAssociation(Integer templateId, String templateType) {
		logger.info("Getting Campaign Location Custom Field Association for templateId {} and type {}", templateId, templateType);
		List<CampaignCustomFieldAssociation> customFieldAssociation = customFieldAssociationRepo.getCampaignCustomFieldAssociationBySource(templateId,
				CustomFieldAssociatedObjectTypeEnum.getObjectTypeByCommType(templateType), CustomFieldSourceEnum.LOCATION);
		
		if (CollectionUtils.isEmpty(customFieldAssociation)) {
			return Collections.emptyList();
		}
		return customFieldAssociation.stream().map(c -> new TemplateCustomFieldSRO(c.getCustomFieldId(), c.getCustomFieldName())).collect(Collectors.toList());
	}
	
	@Override
	public void saveTemplateLocationCustomFieldsAssociation(Integer templateId, String templateType, List<TemplateCustomFieldSRO> locationCustomFields,
			Integer userId, boolean deleteCustomFields) {
		// first delete all custom fields for that object id and type
		if (deleteCustomFields) {
			int count = customFieldAssociationRepo.deleteCampaignCustomField(templateId, CustomFieldAssociatedObjectTypeEnum.getObjectTypeByCommType(templateType),
					CustomFieldSourceEnum.LOCATION);
			logger.info("for object {} and type {} the fields {} has been deleted", templateId, CustomFieldAssociatedObjectTypeEnum.getObjectTypeByCommType(templateType), count);
		}
		if (CollectionUtils.isEmpty(locationCustomFields)) {
			return;
		}
		List<CampaignCustomFieldAssociation> customFieldAssocnList = new ArrayList<>();
		for (TemplateCustomFieldSRO customField : locationCustomFields) {
			CampaignCustomFieldAssociation fieldAssociation = new CampaignCustomFieldAssociation();
			fieldAssociation.setAssociatedObjectId(templateId);
			fieldAssociation.setAssociatedType(CustomFieldAssociatedObjectTypeEnum.getObjectTypeByCommType(templateType));
			fieldAssociation.setCustomFieldName(customField.getName());
			fieldAssociation.setCustomFieldId(customField.getId());
			fieldAssociation.setCustomFieldLabel(replaceSpaceWithUnderScore(customField.getName()));
			fieldAssociation.setUpdatedBy(userId);
			fieldAssociation.setCustomFieldSource(CustomFieldSourceEnum.LOCATION);
			customFieldAssocnList.add(fieldAssociation);
		}
		customFieldAssociationRepo.saveAll(customFieldAssocnList);
	}

	public boolean extractHighCharacterLimitFlagFromResponse(Integer businessId) {
		if(!Objects.isNull(businessId)) {
			BusinessFeaturesResponse businessFeaturesResponse = cacheService.getBusinessFeaturesForAccountId(businessId,
					true);
			if (businessFeaturesResponse != null) {
		        Map<String, String> features = businessFeaturesResponse.getFeatures();
			return TemplateUtils.fetchHighCharacterLimitFlag(features); 
			}
		}
		return false;
	}
	
	/**
	 * 
	 * @param templateId
	 * @return
	 * 
	 * Prepare communication category of Email template.
	 */
	 @Override
	@Cacheable(value = "emailTemplateCategoryCache", unless = "#result == null", key = "#templateId")
	public String prepareEmailTemplateCategory(Integer templateId) {
		String emailTemplateCategory = emailTemplateRepo.findTemplateCategoryById(templateId);
		return CommunicationCategoryEnum.getCommunicationCategoryByInputCategory(emailTemplateCategory).getCommunicationCategory();
	}
	
	/**
	 * @param templateId
	 * @return
	 * Prepare communication category of SMS template.
	 */
	 @Override
	@Cacheable(value = "smsTemplateCategoryCache", unless = "#result == null", key = "#smsTemplateId")
	public String prepareSmsTemplateCategory(Integer smsTemplateId) {
		String smsTemplateCategory = businessSMSTemplateRepo.getTemplateCategoryById(smsTemplateId);
		return CommunicationCategoryEnum.getCommunicationCategoryByInputCategory(smsTemplateCategory).getCommunicationCategory();
	
	 }	

	/**
	 * BIRD-116893
	 * Extracts the text category enabled flag from the business features response.
	 *
	 * @param businessId
	 */
	public boolean extractTextCategoryEnabledFlag(Integer businessId) {
		if (!Objects.isNull(businessId)) {
			BusinessFeaturesResponse businessFeaturesResponse = cacheService.getBusinessFeaturesForAccountId(businessId, true);
			if (businessFeaturesResponse != null) {
				Map<String, String> features = businessFeaturesResponse.getFeatures();
				return TemplateUtils.fetchTextCategoryEnabledFlag(features);
			}
		}
		return false;
	}
	
	/**
	 * Processes the unsubscribe text for the given SMS template if the Text Category Flag is enabled.
	 * @param enterpriseId
	 * @param smsTemplateMessage
	 */
	public void processUnsubscribeText(Integer enterpriseId, SmsTemplateMessage smsTemplateMessage) {
		if (extractTextCategoryEnabledFlag(enterpriseId)) {
			String unsubscribeText ;
			unsubscribeText = prepareUnsubscribeText(smsTemplateMessage.getSmsCategory());
			smsTemplateMessage.setUnsubscribeText(unsubscribeText);
		}
	}
	
	public String prepareUnsubscribeText(String smsCategory) {
		String unsubscribeText= StringUtils.EMPTY;
		if (StringUtils.equalsIgnoreCase(smsCategory, CommunicationCategoryEnum.FEEDBACK.getCommunicationCategory())) {
			unsubscribeText = StringUtils.join("Txt ", Constants.UNSUB_FB_KEYWORD, " to unsub from Feedback messages");
		} else if (StringUtils.equalsIgnoreCase(smsCategory, CommunicationCategoryEnum.SERVICE.getCommunicationCategory())) {
			unsubscribeText = StringUtils.join("Txt ", Constants.UNSUB_SERV_KEYWORD, " to unsub from Service messages");
		} else {
			unsubscribeText = StringUtils.join("Txt ", Constants.UNSUB_MKT_KEYWORD, " to unsub from Marketing messages");
		}
		return unsubscribeText;
	}
	
	/**
	 * Processes the unsubscribe text for the given SMS template if the Text Category Flag is enabled.
	 * @param enterpriseId
	 * @param smsTemplate
	 */
	public void processUnsubscribeText(Integer enterpriseId, BusinessSmsTemplate smsTemplate){
		if(extractTextCategoryEnabledFlag(enterpriseId)){
			String unsubscribeText=prepareUnsubscribeText(smsTemplate.getSmsCategory());
			smsTemplate.setUnsubscribeText(unsubscribeText);
		}
	}
}
