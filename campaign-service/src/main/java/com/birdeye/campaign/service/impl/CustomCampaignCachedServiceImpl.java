package com.birdeye.campaign.service.impl;

import java.util.List;
import java.util.Map;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.entity.CustomCampaignUrl;
import com.birdeye.campaign.repository.CustomCampaignUrlRepo;
import com.birdeye.campaign.service.CustomCampaignCachedService;

@Service("customCampaignCachedService")
public class CustomCampaignCachedServiceImpl implements CustomCampaignCachedService {
	
	@Autowired
	private CustomCampaignUrlRepo	customCampaignUrlRepo;
	
	//@Autowired
	//private FreeTextSmsTemplateRepo	freeTextSmsTemplateRepo;
	
	@Override
	@Cacheable(key = "#templateId.toString()", value = "customCampaignUrlCache", unless = "#result == null")
	public List<CustomCampaignUrl> getCustomCampaignUrl(Integer templateId) {
		return customCampaignUrlRepo.findByTemplateId(templateId);
	}
	
	@Override
	@Cacheable(key = "#campaignId.toString()", value = "freeTextCampaignUrlCache", unless = "#result == null")
	public List<CustomCampaignUrl> getFreeTextCampaignUrl(Integer campaignId) {
		return customCampaignUrlRepo.findByCampaignId(campaignId);
	}
	
	@Override
	@Transactional
	@CacheEvict(value = "customCampaignUrlCache", key = "#templateId")
	public void updateCustomCampaignUrl(Map<String, String> customCampaignUrlMap, Integer enterpriseId, Integer templateId) {
		if (customCampaignUrlMap == null)
			return;
		
		customCampaignUrlRepo.deleteByTemplateId(templateId);
		for (String urlToken : customCampaignUrlMap.keySet()) {
			CustomCampaignUrl customCampaignUrl = new CustomCampaignUrl();
			customCampaignUrl.setEnterpriseId(enterpriseId);
			customCampaignUrl.setUrlToken(urlToken);
			customCampaignUrl.setUrlValue(customCampaignUrlMap.get(urlToken));
			customCampaignUrl.setTemplateId(templateId);
			customCampaignUrlRepo.saveAndFlush(customCampaignUrl);
		}
		
	}
	
	@Override
	@Transactional
	public void createCustomCampaignUrlEntry(List<CustomCampaignUrl> customCampaignUrls, Integer enterpriseId, Integer templateId) {
		if (customCampaignUrls == null)
			return;
		
		for (CustomCampaignUrl customCampaignUr : customCampaignUrls) {
			CustomCampaignUrl customCampaignUrl = new CustomCampaignUrl();
			customCampaignUrl.setEnterpriseId(enterpriseId);
			customCampaignUrl.setUrlToken(customCampaignUr.getUrlToken());
			customCampaignUrl.setUrlValue(customCampaignUr.getUrlValue());
			customCampaignUrl.setTemplateId(templateId);
			customCampaignUrlRepo.saveAndFlush(customCampaignUrl);
		}
		
	}
	
	/*@Override
	@CachePut(key = "#enterpriseId.toString()", value = "freeTextSmsTemplateCache")
	public FreeTextSmsTemplate createFreeTextTemplateEntry(Integer templateId, Integer enterpriseId) {
		FreeTextSmsTemplate freeTextSmsTemplate = new FreeTextSmsTemplate();
		freeTextSmsTemplate.setEnterpriseId(enterpriseId);
		freeTextSmsTemplate.setTemplateId(templateId);
		freeTextSmsTemplateRepo.saveAndFlush(freeTextSmsTemplate);
		return freeTextSmsTemplate;
		
	}*/
}
