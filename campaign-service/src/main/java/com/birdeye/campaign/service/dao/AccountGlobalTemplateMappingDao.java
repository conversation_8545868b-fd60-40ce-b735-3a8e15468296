package com.birdeye.campaign.service.dao;

import java.util.List;

import com.birdeye.campaign.dto.AccountGlobalTemplateMappingDTO;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.entity.AccountGlobalTemplateMapping;

public interface AccountGlobalTemplateMappingDao {
	
	CachedCollectionWrapper<AccountGlobalTemplateMappingDTO> fetchGlobalTemplateMappingsListForAccount(Integer enterpriseId);
	
	AccountGlobalTemplateMapping fetchGlobalTemplateMappingForAccountAndTemplateId(Integer enterpriseId, Integer templateId);
	
	void saveOrUpdateAccountGlobalTemplateMapping(AccountGlobalTemplateMapping accountGlobalTemplateMapping);

	void saveOrUpdateAccountGlobalTemplateMappingList(List<AccountGlobalTemplateMapping> accountGlobalTemplateMapping);

	List<AccountGlobalTemplateMapping> fetchGlobalTemplateMappingsForAccount(Integer enterpriseId);
}
