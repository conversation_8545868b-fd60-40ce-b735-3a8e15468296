package com.birdeye.campaign.service.dao.impl;

import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.entity.QRConfigReviewSourceMapping;
import com.birdeye.campaign.repository.QRConfigReviewSourceMappingRepo;
import com.birdeye.campaign.service.dao.QRConfigReviewSourceMappingDao;
import com.birdeye.dto.reviewgen.ReviewSource;

@Service("qrConfigReviewSourceMappingDao")
public class QRConfigReviewSourceMappingDaoImpl implements QRConfigReviewSourceMappingDao {

	@Autowired
	QRConfigReviewSourceMappingRepo qrConfigReviewSourceMappingRepo;

	private static final Logger LOGGER = LoggerFactory.getLogger(QRConfigReviewSourceMappingDaoImpl.class);

	@Override
	public void createNewQRConfigMapping(Integer qrConfigId, Integer templateId, Integer accountId, Integer sourceId,
			String sourceAlias, String sourceName, Integer suported, Integer priority, Integer userId) {
		QRConfigReviewSourceMapping qrConfigReviewSource = new QRConfigReviewSourceMapping();
		qrConfigReviewSource.setEnterpriseId(accountId);
		qrConfigReviewSource.setQrConfigId(qrConfigId);
		qrConfigReviewSource.setTemplateId(templateId);
		qrConfigReviewSource.setSourceId(sourceId);
		qrConfigReviewSource.setSourceAlias(sourceAlias);
		qrConfigReviewSource.setSourceName(sourceName);
		qrConfigReviewSource.setSupported(suported);
		qrConfigReviewSource.setPriorityOrder(priority);
		qrConfigReviewSource.setCreatedBy(userId);
		qrConfigReviewSource.setCreatedAt(new Date());
		qrConfigReviewSourceMappingRepo.saveAndFlush(qrConfigReviewSource);

	}

	@Override
	public List<QRConfigReviewSourceMapping> getQRConfigDetails(Integer qrConfigId) {
		return qrConfigReviewSourceMappingRepo.findByQrConfigId(qrConfigId);
	}

	@Override
	public List<ReviewSource> getQRReviewSourceFromTemplateId(Integer templateId) {
		return qrConfigReviewSourceMappingRepo.getReviewSourceByTemplateId(templateId);
	}

	@Override
	public void createQRConfigReviewSourceMapping(List<QRConfigReviewSourceMapping> qrConfigReviewSourceMappings) {
		qrConfigReviewSourceMappingRepo.saveAll(qrConfigReviewSourceMappings);
		qrConfigReviewSourceMappingRepo.flush();
	}

	@Override
	public void updateQrConfigReviewSourceMapping(Integer id, Integer qrConfigId, Integer priority, Integer userId) {
		qrConfigReviewSourceMappingRepo.updateQrConfigReviewSourceMapping(id, priority, userId);
	}

	@Override
	public void deleteRedundantQrConfigMapping(Integer id, Integer qrConfigId) {
		qrConfigReviewSourceMappingRepo.deleteById(id);
	}

	@Override
	public List<QRConfigReviewSourceMapping> getQRReviewSources(Integer qrConfigId, Integer accountId) {
		try {
			return qrConfigReviewSourceMappingRepo.findAllByQrConfigIdAndEnterpriseIdAndIsDeleted(qrConfigId, accountId,
					0);
		} catch (Exception e) {
			LOGGER.error("Exception occured while fetching availabe review sources for qr config id :: {}", qrConfigId);
		}
		return null;
	}

	@Override
	public void deleteQRConfigReviewSourceMapping(Integer qrConfigId, Integer accountId) {
		qrConfigReviewSourceMappingRepo.deleteByQrConfigIdAndAccountId(qrConfigId, accountId, 1);
	}

	@Override
	public int updateQRConfigAccountId(Integer fromBusinessId, Integer toBusinessId) {
		return qrConfigReviewSourceMappingRepo.updateQRConfigAccountId(fromBusinessId, toBusinessId);
	}

	@Override
	public List<QRConfigReviewSourceMapping> findByTemplateId(Integer templateId) {
		if(templateId == null)
			return null;
		return qrConfigReviewSourceMappingRepo.findByTemplateIdAndIsDeleted(templateId, 0);
		
	}

	@Override
	public void save(QRConfigReviewSourceMapping reviewSource) {
		if (reviewSource == null ) {
			return;
		}
		qrConfigReviewSourceMappingRepo.saveAndFlush(reviewSource);
	}
	
}
