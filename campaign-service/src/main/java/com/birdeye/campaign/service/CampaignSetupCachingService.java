package com.birdeye.campaign.service;

import java.util.List;

import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.dto.CampaignCustomEntity;
import com.birdeye.campaign.dto.CampaignLocationInfoDto;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.CampaignCondition;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CampaignSetupCachingService {

	
	public Campaign getCampaignById( Integer campaignId);
	
	public CampaignCondition getCampaignConditionByCampaign( Integer campaignId,Integer enterpriseId);
	
	public CachedCollectionWrapper<Campaign> getOngoingCampaignByEnterprise(Integer enterpriseId, List<Integer> campaignStatus, String cacheKey);

	public List<CampaignCondition> getCampaignConditionsByCampaign(List<Integer> campaignId, Integer enterpriseId);

	public void evictOngoingCampaignByEnterprise(String enterpriseId);

	public void evictCampaignConditionByCampaign(Integer campaignId);

	public void evictCampaignById(Integer campaignId);

	public Campaign getOngoingCampaignByEnterpriseAndCampaignId(Integer enterpriseId, Integer campaignId);

	void evictActiveOngoingCampaignCache(Integer campaignId);

	void evictSplitMappingForAccountIdAndSplitCampaignId(Integer accountId, Integer splitCampaignId);

	void evictSplitCampaignCacheForSplitCampaignId(Integer splitCampaignId);

	CachedCollectionWrapper<CampaignCustomEntity> getCampaignNameAndEnterpriseId(List<Integer> campaignIds, String key);

	CampaignCondition getCampaignConditionByCampaignIdAndAccountId(Integer campaignId, Integer accountId);

	void evictCampaignConditionCache(Integer campaignId, Integer accountId);

	CachedCollectionWrapper<CampaignLocationInfoDto> getCampaignLocInfo(Integer accountId);

	void evictCampaignListWithLocInfoCache(Integer accountId);

	void evictSplitCampaignListWithLocInfoCache(Integer accountId);

}
