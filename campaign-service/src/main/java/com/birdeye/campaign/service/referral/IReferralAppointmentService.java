package com.birdeye.campaign.service.referral;

import java.util.List;

import com.birdeye.campaign.dto.DoupDownloadResponse;
import com.birdeye.campaign.dto.FileDownloadResource;
import com.birdeye.campaign.projections.AppointmentDTO;
import com.birdeye.campaign.response.ExternalReferralLeadReportResponse;
import com.birdeye.referral.dto.ReferralAppointmentDataPoint;
import com.birdeye.referral.dto.ReferralLeadDataPoint;
import com.birdeye.referral.request.ReferralAppointmentListingRequest;
import com.birdeye.referral.request.ReferralLeadsCountRequest;
import com.birdeye.referral.request.ReferralUrlRequest;
import com.birdeye.referral.response.ReferralAppointmentListingResponse;
import com.birdeye.referral.response.ReferralLeadsCountResponse;

public interface IReferralAppointmentService {
	
	public FileDownloadResource downloadReferralLeads(ReferralAppointmentListingRequest request, Integer enterpriseId);
	
	/**
	 * @param page
	 * @param size
	 * @param sortOrder
	 * @param sortBy
	 * @param request
	 * @return
	 */
	ReferralAppointmentListingResponse getReferralLeads(Integer page, Integer size, String sortOrder, String sortBy, ReferralAppointmentListingRequest request);
	
	/**
	 * @param request
	 * @return
	 */
	ReferralLeadsCountResponse getReferralLeadsCount(ReferralLeadsCountRequest request);
	
	/**
	 * Api to get referral deeplink url for TitleMax.
	 * 
	 * @param request
	 * @return
	 */
	public String getReferralUrl(ReferralUrlRequest request);
	
	/**
	 * Get enterpriseIds for invalid appointments
	 * 
	 * @return
	 */
	public List<Integer> getEnterpriseIdsForInvalidAppointments();
	
	/**
	 * Appointment is invalid if referrer code is null
	 * 
	 * @param enterpriseId
	 * @return
	 */
	public List<AppointmentDTO> getInvalidAppointmentsByEnterpriseId(Integer enterpriseId);
	
	/**
	 * Update referrer info from Kontacto
	 * 
	 * @param referrerIds
	 */
	public void updateReferrerInfo(List<Integer> referrerIds);
	
	/**
	 * Update referral code from Kontacto for cids of leads
	 * 
	 * @param ecids
	 */
	public void updateLeadReferralCode(List<Integer> cids);

	void sendLeadsToKontactoInBatches(Integer batchStartId, Integer batchEndId);

	public Integer getMaxAppointmentId();

	public void migrateAppointmentDataToESInBatches(Integer batchStartId, Integer batchEndId);
	
	DoupDownloadResponse<ReferralLeadDataPoint> downloadReferralLeadsDoup(ReferralAppointmentListingRequest request, Integer enterpriseId, Integer page, Integer pageSize);

	ReferralAppointmentListingResponse getReferralLeadsDownload(Integer page, Integer size, String sortOrder, String sortBy, ReferralAppointmentListingRequest request);
	
	ExternalReferralLeadReportResponse generateExternalReferralLeads(Integer page, Integer size, ReferralAppointmentListingRequest request);
}