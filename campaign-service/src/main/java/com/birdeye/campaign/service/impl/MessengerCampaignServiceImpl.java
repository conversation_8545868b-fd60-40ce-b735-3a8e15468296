package com.birdeye.campaign.service.impl;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.entity.MessengerCampaign;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.repository.MessengerCampaignRepo;
import com.birdeye.campaign.request.CreateMessengerCampaignRequest;
import com.birdeye.campaign.service.MessengerCampaignService;

@Service("messengerCampaignService")
public class MessengerCampaignServiceImpl implements MessengerCampaignService {
	
	@Autowired
	private MessengerCampaignRepo messengerCampaignRepo;
	
	@Override
	@Cacheable(key = "#campaignId.toString()", value = "bulkSmsCampaignCache", unless = "#result == null")
	public MessengerCampaign getMessengerCampaignData(Integer campaignId) {
		return messengerCampaignRepo.findByCampaignId(campaignId);
	}
	
	@Override
	public void saveMessengerCampaignData(CreateMessengerCampaignRequest campaignRequest, Integer campaignId) {
		if(CollectionUtils.isEmpty(campaignRequest.getMessengerMediaInfo())&& StringUtils.isEmpty(campaignRequest.getFreeText())) {
			throw new CampaignException(ErrorCodes.INVALID_CAMPAIGN, "Invalid Messenger Campaign :" + campaignId);
		}
		MessengerCampaign messengerCampaign = new MessengerCampaign();
		messengerCampaign.setCampaignId(campaignId);
		messengerCampaign.setFreeText(replaceNewLineWithBrTag(campaignRequest.getFreeText()));
		messengerCampaign.setMessengerMedia(campaignRequest.getMessengerMediaInfo());
		messengerCampaignRepo.saveAndFlush(messengerCampaign);
	}
	
	private String replaceNewLineWithBrTag(String text) {
		return StringUtils.replace(text, "\n", "<br />");
	}
}
