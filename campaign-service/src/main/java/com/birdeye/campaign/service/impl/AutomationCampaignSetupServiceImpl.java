package com.birdeye.campaign.service.impl;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.birdeye.campaign.appointment.backfill.service.AppointmentBackfillService;
import com.birdeye.campaign.aspect.annotation.Profiled;
import com.birdeye.campaign.async.service.AsyncBrokerService;
import com.birdeye.campaign.business.service.BusinessService;
import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.CampaignTriggerTypeCache;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.constant.KafkaTopicTypeEnum;
import com.birdeye.campaign.dto.AppointmentScheduleInfo;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.CampaignCountAndTypeDto;
import com.birdeye.campaign.dto.CampaignUserAccessDTO;
import com.birdeye.campaign.dto.GlobalTemplateValidationDTO;
import com.birdeye.campaign.dto.KafkaMessage;
import com.birdeye.campaign.dto.RuleCondition;
import com.birdeye.campaign.entity.AppointmentBackfillEvent;
import com.birdeye.campaign.entity.BusinessEmailTemplate;
import com.birdeye.campaign.entity.BusinessSmsTemplate;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.CampaignCondition;
import com.birdeye.campaign.entity.CampaignCustomFieldAssociation;
import com.birdeye.campaign.entity.CampaignEntitiesChangeLog;
import com.birdeye.campaign.entity.CampaignTriggerType;
import com.birdeye.campaign.enums.AppointmentBackfillEventStatusEnum;
import com.birdeye.campaign.enums.CampaignRunTypeEnum;
import com.birdeye.campaign.enums.CustomFieldSourceEnum;
import com.birdeye.campaign.exception.CampaignBadRequestException;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.exception.CampaignHTTPException;
import com.birdeye.campaign.executor.services.CampaignCallable;
import com.birdeye.campaign.executor.services.CampaignExecutorService;
import com.birdeye.campaign.executor.services.ExecutorCommonService;
import com.birdeye.campaign.external.service.SurveyExternalService;
import com.birdeye.campaign.global.templates.service.GlobalTemplateHandlerService;
import com.birdeye.campaign.kafka.service.KafkaService;
import com.birdeye.campaign.platform.constant.CampaignModificationUserActionEnum;
import com.birdeye.campaign.platform.constant.CampaignStatusEnum;
import com.birdeye.campaign.platform.constant.CampaignTriggerTypeEnum;
import com.birdeye.campaign.platform.constant.CampaignTypeEnum;
import com.birdeye.campaign.platform.entity.User;
import com.birdeye.campaign.platform.readonly.repository.BusinessReadOnlyRepo;
import com.birdeye.campaign.platform.readonly.repository.UserReadOnlyRepo;
import com.birdeye.campaign.repository.BusinessEmailTemplateRepo;
import com.birdeye.campaign.repository.BusinessSMSTemplateRepo;
import com.birdeye.campaign.repository.CampaignConditionRepo;
import com.birdeye.campaign.repository.CampaignCustomFieldAssociationRepo;
import com.birdeye.campaign.repository.CampaignRepo;
import com.birdeye.campaign.repository.EmailTemplateRepo;
import com.birdeye.campaign.request.AutomationCampaignRequest;
import com.birdeye.campaign.request.CampaignEditInfo;
import com.birdeye.campaign.request.CampaignFilterRequest;
import com.birdeye.campaign.request.CampaignUpdateEvent;
import com.birdeye.campaign.request.CampaignUsageInfo;
import com.birdeye.campaign.request.CampaignUserAccessRequest;
import com.birdeye.campaign.request.CampaignsFilterCriteria;
import com.birdeye.campaign.request.UpdateDefaultCampaignRequest;
import com.birdeye.campaign.response.AutomationCampaignResponse;
import com.birdeye.campaign.response.AutomationEditCampaignMessage;
import com.birdeye.campaign.response.AutomationsCountResponse;
import com.birdeye.campaign.response.CampaignBasicResponse;
import com.birdeye.campaign.response.CreateCampaignResponse;
import com.birdeye.campaign.response.EditInfoResponse;
import com.birdeye.campaign.response.SurveyUsageInfo;
import com.birdeye.campaign.response.external.BusinessFeaturesResponse;
import com.birdeye.campaign.rule.utils.RuleEngine;
import com.birdeye.campaign.service.AutomationCampaignSetupService;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.CampaignModificationAuditService;
import com.birdeye.campaign.service.CampaignServiceHelper;
import com.birdeye.campaign.service.CampaignSetupCachingService;
import com.birdeye.campaign.service.CampaignUsageService;
import com.birdeye.campaign.service.FilterCriteriaService;
import com.birdeye.campaign.service.TemplateHelperService;
import com.birdeye.campaign.service.dao.CampaignEntitiesChangeLogDao;
import com.birdeye.campaign.user.access.settings.service.CampaignUserAccessSettingsService;
import com.birdeye.campaign.utils.AppointmentFormUtils;
import com.birdeye.campaign.utils.BusinessUtils;
import com.birdeye.campaign.utils.CampaignModificationAuditUtils;
import com.birdeye.campaign.utils.CampaignUserAccessUtils;
import com.birdeye.campaign.utils.CampaignUtils;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.DateTimeUtils;

@Service("automationCampaignSetupService")
public class AutomationCampaignSetupServiceImpl implements AutomationCampaignSetupService {
	
	private static final String					ERROR_WHILE_EXECUTING_THE_THE_GET_DETAILS_FOR_EID_AND_CAMP_ID	= "Error {} while executing the the get details for eid {} and campId {}";
	
	private static final Logger					logger															= LoggerFactory.getLogger(AutomationCampaignSetupServiceImpl.class);
	
	@Autowired
	private CampaignRepo						campaignRepo;
	
	@Autowired
	private CampaignConditionRepo				campaignConditionRepo;
	
	@Autowired
	private UserReadOnlyRepo					userRepo;
	
	@Autowired
	private EmailTemplateRepo					emailTemplateRepo;
	
	@Autowired
	private BusinessSMSTemplateRepo				smsTemplateRepo;
	
	@Autowired
	private BusinessService						businessService;
	
	@Autowired
	@Qualifier(Constants.CAMPAIGN_UI_COMPLETABLE_FUTURE_TASK_EXECUTOR)
	private ThreadPoolTaskExecutor				threadPoolTaskExecutor;
	
	@Autowired
	private ExecutorCommonService				executorCommonService;
	
	@Autowired
	private CampaignSetupCachingService			campaignCachingService;
	
	@Autowired
	private SurveyExternalService				surveyExternalService;
	
	@Autowired
	private BusinessReadOnlyRepo				businessReadOnlyRepo;
	
	@Autowired
	private BusinessEmailTemplateRepo			businessEmailTemplateRepo;
	
	@Autowired
	private BusinessSMSTemplateRepo				businessSMSTemplateRepo;
	
	@Autowired
	private CampaignCustomFieldAssociationRepo	customFieldAssociationRepo;
	
	@Autowired
	private CampaignUsageService				campaignUsageService;
	
	@Autowired
	private FilterCriteriaService				filterCriteriaService;
	
	@Autowired
	private AsyncBrokerService					asyncBrokerService;
	
	@Autowired
	AppointmentBackfillService                  appointmentBackfillService;
	
	@Autowired
	private GlobalTemplateHandlerService		globalTemplatesService;
	
	@Autowired
	private KafkaService						kafkaService;
	
	@Autowired
	private CampaignUserAccessSettingsService	userAccessSettingsService;
	
	@Autowired
	private CampaignModificationAuditService	campaignModificationAuditService;
	
	@Autowired
	private CampaignEntitiesChangeLogDao		campaignEntitiesChangeLogDao;
	
	@Autowired
	private CacheService						cacheService;

	@Autowired
	private CampaignServiceHelper 					campaignService;

	
	@Autowired
	private TemplateHelperService				templateHelperService;

	private Integer getUserId(String userId) {
		Integer uid = null;
		try {
			uid = Integer.parseInt(userId);
		} catch (NumberFormatException e) {
			logger.warn("Not a valid user id : {}", userId);
		}
		return uid;
	}
	
	private String getCampaignName(String campaignName, Integer enterpriseId) {
		Integer count = campaignRepo.getCampaignSameNameCountByEnterpriseIdAndName(enterpriseId, campaignName);
		if (count == 0)
			return campaignName;
		
		return campaignName + "(" + count + ")";
	}
	
	private CampaignCondition prepareDefaultCampaignConditionData(Integer enterpriseId, Integer userId, Integer campaignId) {
		CampaignCondition campaignCondition = new CampaignCondition();
		campaignCondition.setEnterpriseId(enterpriseId);
		campaignCondition.setCampaignId(campaignId);
		campaignCondition.setUpdatedBy(userId);
		return campaignCondition;
	}
	
	private Campaign prepareDefaultCampaignData(Integer enterpriseId, Integer userId, Integer emailTemplateId, Integer smsTemplateId, String campaignName, Integer... resellerId) {
		Campaign campaign = new Campaign();
		if (resellerId.length > 0) {
			campaign.setResellerId(resellerId[0]);
		} else {
			campaign.setEnterpriseId(enterpriseId);
		}
		campaign.setName(campaignName);
		campaign.setSmsTemplateId(smsTemplateId);
		campaign.setTemplateId(emailTemplateId);
		campaign.setCreatedBy(userId);
		campaign.setIsDefault(1);
		return campaign;
	}
	
	// private void setSchedulingDetails(CampaignBasicRequest campaignRequest, Campaign campaign) {
	// campaign.setSchedule(campaignRequest.getScheduled());
	// campaign.setSchedulingInHours(campaignRequest.getSchedulingInHours());
	// if (campaignRequest.getSchedulingInHours() != null && campaignRequest.getSchedulingInHours().intValue() == 0) {
	// campaign.setSchedule(campaignRequest.getScheduled() * 24);
	// }
	// }
	
	private void evictCampaignCacheForEnterprise(Integer campaignId, Integer enterpriseId) {
		campaignCachingService.evictCampaignById(campaignId);
		//campaignCachingService.evictCampaignConditionByCampaign(campaignId);
		campaignCachingService.evictActiveOngoingCampaignCache(campaignId);
		campaignCachingService.evictCampaignConditionCache(campaignId, enterpriseId);
		
		// BIRD-64796 | evict automation cache for both status 1(active) & 2(paused). keys : #accountId-1, #accountId-1-2
		campaignCachingService.evictOngoingCampaignByEnterprise(StringUtils.join(enterpriseId, "-", 1));
		campaignCachingService.evictOngoingCampaignByEnterprise(StringUtils.join(enterpriseId, "-", 1, "-", 2));
		campaignCachingService.evictCampaignListWithLocInfoCache(enterpriseId);
	}
	
	private List<String> getValidBusinessIds(List<String> lvlids) {
		if (CollectionUtils.isEmpty(lvlids)) {
			return Collections.emptyList();
		}
		Set<Integer> businessIds = lvlids.stream().map(Integer::parseInt).collect(Collectors.toSet());
		List<Integer> validBusinessIds = businessReadOnlyRepo.getValidBusinessIdByBusinessIds(businessIds);
		if (CollectionUtils.isEmpty(validBusinessIds)) {
			return Collections.emptyList();
		}
		return validBusinessIds.stream().map(String::valueOf).collect(Collectors.toList());
	}
	
	private void setSchedulingDetails(Campaign campaign, CampaignBasicResponse response) {
		if (campaign.getSchedulingInHours() != null) {
			response.setScheduled(campaign.getSchedule());
			response.setSchedulingInHours(campaign.getSchedulingInHours());
			if (campaign.getSchedulingInHours().intValue() == 0) {
				response.setScheduled(campaign.getSchedule() / 24);
			}
		} else { // backward compatability before scheduling changes (previously handled from UI)
			
			if (campaign.getSchedule() != null && campaign.getSchedule() > 0) {
				if (campaign.getSchedule().intValue() > 24) {
					response.setSchedulingInHours(0);
					response.setScheduled(campaign.getSchedule() / 24);
				}
				
				else {
					response.setSchedulingInHours(1);
					response.setScheduled(campaign.getSchedule());
				}
				
			}
		}
	}
	
	private String prepareSMSTemplate(Integer templateId) {
		return smsTemplateRepo.getTemplateNameById(templateId);
	}
	
	private String prepareEmailTemplateName(Integer templateId) {
		return emailTemplateRepo.findTemplateNameById(templateId);
	}
	
	private String prepareUserName(Integer userId) {
		User user = userRepo.findFirstById(userId);
		return CoreUtils.formatUserName(user.getFirstName(), user.getLastName(), user.getEmailId());
	}
	
	private boolean isEmailTemplateDeleted(Integer templateId) {
		BusinessEmailTemplate businessEmailTemplate = businessEmailTemplateRepo.findFirstByEmailTemplateId(templateId);
		if (businessEmailTemplate != null) {
			return CoreUtils.isTrueForInteger(businessEmailTemplate.getIsDeleted());
		}
		return false;
	}
	
	private boolean isSmsTemplateDeleted(Integer templateId) {
		if (templateId != null) {
			BusinessSmsTemplate businessSmsTemplate = businessSMSTemplateRepo.findFirstById(templateId);
			if (businessSmsTemplate != null) {
				return CoreUtils.isTrueForInteger(businessSmsTemplate.getIsDeleted());
			}
		}
		return false;
	}
	
	private CreateCampaignResponse createOrUpdateResellerAutomationCampaign(AutomationCampaignRequest campaignRequest, Integer resellerId, Integer campaignId, String type, String userId) {
		// Integer uid = getUserId(userId);
		Integer uid = null;
		validatePriority(campaignRequest.getPriority());
		Campaign[] newAndOldCampaigns = automationCampaignSaveOrUpdate(campaignRequest, null, campaignId, uid, resellerId);
		Campaign onGoingCampaign = newAndOldCampaigns[0];
		// saving campaign Condition
		CampaignCondition onGoingCondition = null;
		if (campaignId != null && campaignId != 0) {
			onGoingCondition = campaignConditionRepo.getByCampaignId(campaignId);
			onGoingCondition.setEnterpriseId(resellerId);
			onGoingCondition.setCampaignId(campaignId);
			onGoingCondition.setUpdatedBy(uid);
		} else {
			onGoingCondition = prepareDefaultCampaignConditionData(resellerId, null, onGoingCampaign.getId());
		}
		if (StringUtils.isBlank(campaignRequest.getLvlAlias())) {
			onGoingCondition.setLvlAlias("loc");
		} else {
			onGoingCondition.setLvlAlias(campaignRequest.getLvlAlias());
			onGoingCondition.setLvlAliasId(campaignRequest.getLvlAliasId());
		}
		
		// if select ALL is false then ids are empty then set the values else set null
		if (!campaignRequest.isSelectAll() && CollectionUtils.isNotEmpty(campaignRequest.getLvlIds())) {
			onGoingCondition.setLvlIds(campaignRequest.getLvlIds());
		} else {
			onGoingCondition.setLvlIds(null);
		}
		if (CollectionUtils.isNotEmpty(campaignRequest.getSources())) {
			onGoingCondition.setContactSources(campaignRequest.getSources().stream().filter(source -> !"quick_send".equalsIgnoreCase(source)).collect(Collectors.toList()));
		} else {
			List<String> defaultContactSources = new ArrayList<>();
			defaultContactSources.add("dashboard");
			defaultContactSources.add("sftp");
			defaultContactSources.add("integration");
			defaultContactSources.add("api");
			onGoingCondition.setContactSources(defaultContactSources);
		}
		onGoingCondition.setEvent(getTriggerTypeByEvent(StringUtils.isBlank(campaignRequest.getTriggerType()) ? CampaignTriggerTypeEnum.CONTACT_ADDED.getType() : campaignRequest.getTriggerType()));
		// saving expression and mvel expression
		if (campaignRequest.getExpression() != null) {
			onGoingCondition.setRuleExpression(campaignRequest.getExpression());
			onGoingCondition.setMvelExpression(RuleEngine.buildRuleExpression(campaignRequest.getExpression()));
			onGoingCondition.setMvelParamsAndTypes(RuleEngine.getMvelParamsFromConditions(campaignRequest.getExpression().getConditions()));
		}
		// to handle update
		else {
			onGoingCondition.setRuleExpression(null);
			onGoingCondition.setMvelExpression(null);
			onGoingCondition.setMvelParamsAndTypes(null);
			// deleteAssociationObject(onGoingCondition.getCampaignId(), "Campaign");
		}
		setTriggerConditions(onGoingCondition, campaignRequest);
		campaignConditionRepo.saveAndFlush(onGoingCondition);
		// create association for custom fields
		if (campaignRequest.getExpression() != null) {
			createCustomFieldAssociation(onGoingCondition.getCampaignId(), "Campaign", campaignRequest.getExpression().getConditions(), uid);
		}
		
		return new CreateCampaignResponse(onGoingCampaign.getId(), onGoingCampaign.getName(), onGoingCampaign.getStatus());
	}
	
	private void validateResellerCampaignType(String type) {
		if (!"promotional".equalsIgnoreCase(type) && !"review_request".equalsIgnoreCase(type) && !"survey_request".equalsIgnoreCase(type) && !"cx_request".equalsIgnoreCase(type)
				&& !"referral".equalsIgnoreCase(type)) {
			throw new CampaignException(ErrorCodes.INVALID_TYPE, ErrorCodes.INVALID_TYPE.getMessage());
		}
	}
	
	@Override
	@Transactional(value = "chainedTransactionManager", rollbackFor = Exception.class)
	public CreateCampaignResponse processResellerAutomationCampaign(AutomationCampaignRequest campaignRequest, Integer resellerId, String type, String userId) {
		if (campaignRequest == null || resellerId == null || StringUtils.isBlank(type)) {
			throw new CampaignException(ErrorCodes.ERROR_INVALID_REQUEST, "Invalid Request while creating/updating reseller campaign");
		}
		validateResellerCampaignType(type);
		// Hardcoding some request field for reseller campaigns
		campaignRequest.setStatusId(5);
		campaignRequest.setTriggerExpression(null);
		campaignRequest.setTriggerType(null);
		campaignRequest.setLvlAlias(null);
		campaignRequest.setLvlIds(null);
		campaignRequest.setIsDraft(0);
		campaignRequest.setPriority("email_and_sms");
		List<Campaign> campaigns = null;
		BusinessEnterpriseEntity business = businessReadOnlyRepo.getValidBusinessByBid(resellerId);
		if (BusinessUtils.isResellerAndNotBirdeyeReseller(business)) {
			campaigns = campaignRepo.findByResellerIdAndTypeAndIsDeleted(business.getId(), type, 0);
		} else {
			throw new CampaignException(ErrorCodes.ERROR_NOT_A_RESELLER_ACCOUNT, "Error, Not a reseller account");
		}
		if (CollectionUtils.isNotEmpty(campaigns)) {
			campaignRequest.setCampaignType(type);
			return createOrUpdateResellerAutomationCampaign(campaignRequest, resellerId, campaigns.get(0).getId(), type, userId);
			
		} else {
			campaignRequest.setCampaignType(type);
			return createOrUpdateResellerAutomationCampaign(campaignRequest, resellerId, 0, type, userId);
		}
	}
	
	/**
	 *
	 * This function creates/updates the underlying Automation of the Split Automation
	 * 
	 * @param AutomationCampaignRequest,
	 *            accountId,campaignId,userId
	 */
	@Override
	@Profiled
	public CreateCampaignResponse createOrUpdateSubAutomationForSplit(AutomationCampaignRequest campaignRequest, Integer accountId, Integer campaignId, String userId) {
		logger.info("createOrUpdateSubAutomationForSplit :: Request recieved to create sub campaign for account id {}, campaign id {} and user id {} is {}", accountId, campaignId, userId,
				campaignRequest);
		return createOrUpdateAutomationCampaign(campaignRequest, accountId, campaignId, userId);
	}
	
	@Override
	@Transactional(value = "chainedTransactionManager", rollbackFor = Exception.class)
	public CreateCampaignResponse createOrUpdateAutomationCampaign(AutomationCampaignRequest campaignRequest, Integer enterpriseId, Integer campaignId, String userId) {
		validateAutomationProperties(campaignRequest);
		// Handling Global Template Save
		GlobalTemplateValidationDTO validationDTO = globalTemplatesService.validateAndCreateBusinessSmsTemplate(campaignRequest.getIsGlobalTemplate(), campaignRequest.getSmsTemplateId(), enterpriseId,
				userId);
		campaignRequest.setSmsTemplateId((validationDTO != null) ? validationDTO.getTemplateId() : campaignRequest.getSmsTemplateId());
		
		Integer uid = getUserId(userId);
		validatePriority(campaignRequest.getPriority());
		Campaign[] newAndOldCampaigns = automationCampaignSaveOrUpdate(campaignRequest, enterpriseId, campaignId, uid);
		Campaign onGoingCampaign = newAndOldCampaigns[0];
		// saving campaign Condition
		CampaignCondition onGoingCondition = null, oldCondition = null;
		if (campaignId != null && campaignId != 0) {
			onGoingCondition = campaignConditionRepo.getByCampaignIdAndEnterpriseId(campaignId, enterpriseId);
			oldCondition = new CampaignCondition(onGoingCondition);
			onGoingCondition.setEnterpriseId(enterpriseId);
			onGoingCondition.setCampaignId(campaignId);
			onGoingCondition.setUpdatedBy(uid);
		} else {
			onGoingCondition = prepareDefaultCampaignConditionData(enterpriseId, uid, onGoingCampaign.getId());
		}
		onGoingCondition.setLvlAlias(campaignRequest.getLvlAlias());
		onGoingCondition.setLvlAliasId(campaignRequest.getLvlAliasId());
		
		// if select ALL is false then ids are empty then set the values else set null
		if (!campaignRequest.isSelectAll() && CollectionUtils.isNotEmpty(campaignRequest.getLvlIds())) {
			onGoingCondition.setLvlIds(campaignRequest.getLvlIds());
		} else {
			onGoingCondition.setLvlIds(null);
		}
		if (CollectionUtils.isNotEmpty(campaignRequest.getSources())) {
			onGoingCondition.setContactSources(campaignRequest.getSources());
		} else {
			onGoingCondition.setContactSources(null);
		}
		onGoingCondition.setEvent(getTriggerTypeByEvent(campaignRequest.getTriggerType()));
		// BIRD-702
		onGoingCondition.setRecurringReminderEventType(campaignRequest.getContactEventType());
		// saving expression and mvel expression
		if (campaignRequest.getExpression() != null) {
			onGoingCondition.setRuleExpression(campaignRequest.getExpression());
			onGoingCondition.setMvelExpression(RuleEngine.buildRuleExpression(campaignRequest.getExpression()));
			onGoingCondition.setMvelParamsAndTypes(RuleEngine.getMvelParamsFromConditions(campaignRequest.getExpression().getConditions()));
		}
		// to handle update
		else {
			onGoingCondition.setRuleExpression(null);
			onGoingCondition.setMvelExpression(null);
			onGoingCondition.setMvelParamsAndTypes(null);
			deleteAssociationObject(onGoingCondition.getCampaignId(), "Campaign", CustomFieldSourceEnum.CONTACT);
		}
		
		setTriggerConditions(onGoingCondition, campaignRequest);
		
		setAppointmentSchedulingInfo(campaignRequest, onGoingCondition);
		
		campaignConditionRepo.saveAndFlush(onGoingCondition);
		// create association for contact custom fields
		if (campaignRequest.getExpression() != null) {
			createCustomFieldAssociation(onGoingCondition.getCampaignId(), "Campaign", campaignRequest.getExpression().getConditions(), uid);
		}
		
		// create association for appointment custom fields
		if (campaignRequest.getTriggerExpression() != null) {
			createAppointmentCustomFieldAssociation(onGoingCondition.getCampaignId(), "Campaign", campaignRequest.getTriggerExpression().getConditions(), uid);
		}

//		Updates survey communication frequencies for both regular campaigns and split campaign mappings
//		when a valid survey ID is provided in the campaign request.
//		This method calls the respective update methods on the campaign service to:
//		- Update ongoing campaigns for the specified survey and enterprise.
//		Only campaigns or mappings with changed communication frequency will be updated and their caches evicted.
		boolean isSurveyRestrictionEnable = campaignService.isSurveyCommRestrictionEnable(enterpriseId);
		if(isSurveyRestrictionEnable && StringUtils.equalsIgnoreCase(CampaignTypeEnum.SURVEY_REQUEST.getType(), campaignRequest.getCampaignType())){
			campaignService.updateSurveyAutomationCampaigns(campaignRequest.getSurveyId(), enterpriseId, campaignRequest.getSurveyCommFrequency(), campaignRequest.getOverrideCommRestriction());
		}
		// evicting cache
		evictCampaignCacheForEnterprise(onGoingCampaign.getId(), enterpriseId);
		
		
		logger.info("Old conditions for automation : {} are : {}", onGoingCampaign.getId(), oldCondition);
		logger.info("New conditions for automation : {} are : {}", onGoingCampaign.getId(), onGoingCondition);
		
		// back-fill events for Appointment reminder automation
		if (isBackfillApplicableForAutomation(newAndOldCampaigns, oldCondition, onGoingCondition)) {
			appointmentBackfillService
					.appointmentBackfillEvent(new AppointmentBackfillEvent(enterpriseId, onGoingCampaign.getId(), campaignRequest.getCampaignType(), AppointmentBackfillEventStatusEnum.INIT, 0, null));
		}
		// put campaign update/addition event to kafka
		if (BooleanUtils.isFalse(CoreUtils.isTrueForInteger(campaignRequest.getIsDraft())) && BooleanUtils.isFalse(CoreUtils.isTrueForInteger(campaignRequest.getIsSplitCampaign()))) {
			CampaignUpdateEvent campaignUpdateEvent = new CampaignUpdateEvent(enterpriseId, onGoingCampaign.getId(), new Date(), getTriggerTypeByEvent(campaignRequest.getTriggerType()));
			kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_UPDATE, new KafkaMessage(campaignUpdateEvent));
		}
		
		// BIRD-50563 | User Access Settings Handling
		if (BooleanUtils.isFalse(CoreUtils.isTrueForInteger(campaignRequest.getIsSplitCampaign()))) {
			userAccessSettingsService.validateAndUpdateOwnerForCampaignAccessSetting(
					new CampaignUserAccessRequest(CampaignUtils.isNewCampaignCreationRequest(campaignId), enterpriseId, uid, onGoingCampaign.getId(), null));
		}

		// BIRD-72687 - Maintain audit of campaign modification(Async call).
		// Future case: For any change in the campaign/templates entities, consider its handling in auditing flow.
		campaignModificationAuditService.prepareAndPublishCampaignModificationEvent(
				CampaignModificationAuditUtils.prepareAllCampaignDataDTO(newAndOldCampaigns[1], oldCondition, null),
				CampaignModificationAuditUtils.prepareAllCampaignDataDTO(onGoingCampaign, onGoingCondition, null), uid,
				(campaignId != null && campaignId != 0) ? CampaignModificationUserActionEnum.EDIT.getUserActionType()
						: CampaignModificationUserActionEnum.CREATE.getUserActionType(),
				Instant.now().toEpochMilli(), enterpriseId, CoreUtils.getBooleanValueFromInteger(onGoingCampaign.getIsSplitCampaign()));
		
		return new CreateCampaignResponse(onGoingCampaign.getId(), onGoingCampaign.getName(), onGoingCampaign.getStatus());
		
	}

	/**
	 * The following conditions need to be met for running backfill job
	 * 1. It needs to be appointment reminder automation
	 * 2. Either the automation's one of the following aspects needs to be updated (priority, schedule info, custom field conditions, trigger conditions)
	 * 3. Or the automation needs to be new.
	 * 
	 * @param newAndOldCampaigns
	 *            array containing new(updated from dashboard) and old(fetched from DB) automation
	 * @param oldCondition
	 *            old campaign condition (fetched from DB)
	 * @param onGoingCondition
	 *            new campaign condition (updated from dashboard)
	 * @return
	 */
	private boolean isBackfillApplicableForAutomation(Campaign[] newAndOldCampaigns, CampaignCondition oldCondition, CampaignCondition onGoingCondition) {
		if (!StringUtils.equalsAnyIgnoreCase(newAndOldCampaigns[0].getCampaignType(), CampaignTypeEnum.APPOINTMENT_REMINDER.getType(),
				CampaignTypeEnum.APPOINTMENT_RECALL.getType(), CampaignTypeEnum.APPOINTMENT_FORM.getType())) {
			return false;
		}
		if (Objects.isNull(newAndOldCampaigns[1])) {
			return true;
		}
		
		if (!AppointmentFormUtils.isAppointmentFormBackFillApplicable(newAndOldCampaigns, onGoingCondition)) {
			logger.info("Appointmentform backfill not applicable on campaignType :: {}, therefore backfill events will not run for campaign id :: {}", onGoingCondition.getEvent(),
					newAndOldCampaigns[0].getId());
			return false;
		}
		
		if (!StringUtils.equalsIgnoreCase(newAndOldCampaigns[0].getPriority(), newAndOldCampaigns[1].getPriority())) {
			logger.info("Appointment automation priority has changed from :: {} to :: {}, therefore backfill events will run for campaign id :: {}", newAndOldCampaigns[1].getPriority(),
					newAndOldCampaigns[0].getPriority(), newAndOldCampaigns[0].getId());
			return true;
		}
		
		if (!StringUtils.equalsIgnoreCase(oldCondition.getMvelExpression(), onGoingCondition.getMvelExpression())) {
			logger.info("Appointment automation rule expression has changed from :: {} to :: {}, therefore backfill events will run for campaign id :: {}", oldCondition.getMvelExpression(),
					onGoingCondition.getMvelExpression(), newAndOldCampaigns[0].getId());
			return true;
		}
		
		if (!StringUtils.equalsIgnoreCase(oldCondition.getTriggerMvelExpression(), onGoingCondition.getTriggerMvelExpression())) {
			logger.info("Appointment automation trigger rule expression has changed from :: {} to :: {}, therefore backfill events will run for campaign id :: {}",
					oldCondition.getTriggerMvelExpression(), onGoingCondition.getTriggerMvelExpression(), newAndOldCampaigns[0].getId());
			return true;
		}
		
		if (!CampaignUtils.compareListsIgnoreOrderAndDuplicates(oldCondition.getAppointmentScheduleInfo(), onGoingCondition.getAppointmentScheduleInfo())) {
			logger.info("Appointment automation schedule info has changed from :: {} to :: {}, therefore backfill events will run for campaign id :: {}", oldCondition.getAppointmentScheduleInfo(),
					onGoingCondition.getAppointmentScheduleInfo(), newAndOldCampaigns[0].getId());
			return true;
		}
		
		if (Objects.nonNull(onGoingCondition.getExecutionDateInfo()) && Objects.nonNull(oldCondition.getExecutionDateInfo())
				&& !onGoingCondition.getExecutionDateInfo().equals(oldCondition.getExecutionDateInfo())) {
			logger.info("Appointment automation execution date info has changed from :: {} to :: {}, therefore backfill events will run for campaign id :: {}", oldCondition.getExecutionDateInfo(),
					onGoingCondition.getExecutionDateInfo(), newAndOldCampaigns[0].getId());
			return true;
		}
		
		if (!StringUtils.equalsIgnoreCase(oldCondition.getLvlAlias(), onGoingCondition.getLvlAlias())) {
			logger.info("Appointment automation level alias has changed from :: {} to :: {}, therefore backfill events will run for campaign id :: {}", oldCondition.getLvlAlias(),
					onGoingCondition.getLvlAlias(), newAndOldCampaigns[0].getId());
			return true;
		}
		
		if (!StringUtils.equalsIgnoreCase(oldCondition.getLvlAliasId(), onGoingCondition.getLvlAliasId())) {
			logger.info("Appointment automation level alias id has changed from :: {} to :: {}, therefore backfill events will run for campaign id :: {}", oldCondition.getLvlAliasId(),
					onGoingCondition.getLvlAliasId(), newAndOldCampaigns[0].getId());
			return true;
		}
		
		if (!CampaignUtils.compareListsIgnoreOrderAndDuplicates(oldCondition.getLvlIds(), onGoingCondition.getLvlIds())) {
			logger.info("Appointment automation level ids have changed from :: {} to :: {}, therefore backfill events will run for campaign id :: {}", oldCondition.getLvlIds(),
					onGoingCondition.getLvlIds(), newAndOldCampaigns[0].getId());
			return true;
		}
		
		if (!Objects.equals(newAndOldCampaigns[0].getTemplateId(), newAndOldCampaigns[1].getTemplateId())) {
			logger.info("Appointment automation email template has changed from :: {} to :: {}, therefore backfill events will run for campaign id :: {}", newAndOldCampaigns[1].getTemplateId(),
					newAndOldCampaigns[0].getTemplateId(), newAndOldCampaigns[0].getId());
			return true;
		}
		
		if (!Objects.equals(newAndOldCampaigns[0].getSmsTemplateId(), newAndOldCampaigns[1].getSmsTemplateId())) {
			logger.info("Appointment automation sms template has changed from :: {} to :: {}, therefore backfill events will run for campaign id :: {}", newAndOldCampaigns[1].getSmsTemplateId(),
					newAndOldCampaigns[0].getSmsTemplateId(), newAndOldCampaigns[0].getId());
			return true;
		}
		
		return false;
	}
	
	private void setAppointmentSchedulingInfo(AutomationCampaignRequest campaignRequest, CampaignCondition onGoingCondition) {
		if (StringUtils.equalsAnyIgnoreCase(campaignRequest.getTriggerType(), CampaignTriggerTypeEnum.BEFORE_APPOINTMENT_DATE.getType(), CampaignTriggerTypeEnum.APPOINTMENT_RECALL.getType())) {
			onGoingCondition.setAppointmentScheduleInfo(campaignRequest.getAppointmentScheduleInfo());
		} else {
			onGoingCondition.setAppointmentScheduleInfo(getValidSchedulingInfoForGivenTrigger(campaignRequest.getScheduleInfo(), campaignRequest.getTriggerType(), onGoingCondition.getEvent()));
		}
		onGoingCondition.setExecutionDateInfo(campaignRequest.getDueDateInfo());
	}
	
	private List<AppointmentScheduleInfo> getValidSchedulingInfoForGivenTrigger(List<AppointmentScheduleInfo> scheduleInfo, String triggerType, String reminderScheduleEvent) {
		if (!StringUtils.equalsAnyIgnoreCase(triggerType, CampaignTriggerTypeEnum.BEFORE_APPOINTMENT_DATE.getType(), CampaignTriggerTypeEnum.APPOINTMENT_RECALL.getType(),
				CampaignTriggerTypeEnum.CONTACT_EVENT.getType())) {
			if (CollectionUtils.isEmpty(scheduleInfo))
				return scheduleInfo;
			if (scheduleInfo.size() > 1) {
				logger.info("Invalid size of the schedule info found for the given trigger :: {} and schedule info is :: {}", triggerType, scheduleInfo);
			}
			List<AppointmentScheduleInfo> validScheduleInfo = new ArrayList<>();
			validScheduleInfo.add(scheduleInfo.get(0));
			validScheduleInfo.get(0).setSendOrder("after");
			return validScheduleInfo;
		} else if (StringUtils.equalsAnyIgnoreCase(triggerType, CampaignTriggerTypeEnum.CONTACT_EVENT.getType())) {
			if (CollectionUtils.isEmpty(scheduleInfo))
				return scheduleInfo;
			if (scheduleInfo.size() > 1) {
				logger.info("Invalid size of the schedule info found for the given trigger :: {} and schedule info is :: {}", triggerType, scheduleInfo);
			}
			List<AppointmentScheduleInfo> validScheduleInfo = new ArrayList<>();
			validScheduleInfo.add(scheduleInfo.get(0));
			String[] weakDaysarray = { "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday" };
			validScheduleInfo.get(0).setAllowedDays(Arrays.asList(weakDaysarray).stream().map(day -> day.toString()).collect(Collectors.toList()));
			validScheduleInfo.get(0).setSendOrder("on");
			validScheduleInfo.get(0).setScheduleAt(reminderScheduleEvent);
			return validScheduleInfo;
		}
		return scheduleInfo;
	}
	
	private void setTriggerConditions(CampaignCondition ongoingCampaignCondition, AutomationCampaignRequest campaignRequest) {
		if (campaignRequest == null || ongoingCampaignCondition == null)
			return;
		if (campaignRequest.getTriggerExpression() != null) {
			ongoingCampaignCondition.setTriggerRuleExpression(campaignRequest.getTriggerExpression());
			ongoingCampaignCondition.setTriggerMvelExpression(RuleEngine.buildRuleExpression(campaignRequest.getTriggerExpression()));
			ongoingCampaignCondition.setTriggerMvelParamsAndTypes(RuleEngine.getMvelParamsFromConditions(campaignRequest.getTriggerExpression().getConditions()));
		} else {
			ongoingCampaignCondition.setTriggerRuleExpression(null);
			ongoingCampaignCondition.setTriggerMvelExpression(null);
			ongoingCampaignCondition.setTriggerMvelParamsAndTypes(null);
			deleteAssociationObject(ongoingCampaignCondition.getCampaignId(), "Campaign", CustomFieldSourceEnum.APPOINTMENT);
		}
	}
	
	private Campaign[] automationCampaignSaveOrUpdate(AutomationCampaignRequest campaignRequest, Integer enterpriseId, Integer campaignId, Integer uid, Integer... resellerId) {
		Campaign onGoingCampaign = null;
		Campaign[] newAndOldCampaigns = new Campaign[2];
		// Update Campaign
		if (campaignId != null && campaignId != 0) {
			onGoingCampaign = campaignRepo.getByCampaignId(campaignId);
			if (onGoingCampaign == null) {
				StringBuilder errorMsg = resellerId.length > 0 ? new StringBuilder("CampaignId").append(campaignId).append("For reseller ").append(resellerId).append("Not found in system")
						: new StringBuilder("CampaignId").append(campaignId).append("For enterprise ").append(enterpriseId).append("Not found in system");
				throw new CampaignHTTPException(HttpStatus.NOT_FOUND, errorMsg.toString());
			}
			newAndOldCampaigns[1] = new Campaign(onGoingCampaign);
			if (onGoingCampaign.getStatus() != CampaignStatusEnum.DRAFT.getStatus() && campaignRequest.getIsDraft() == 1) {
				logger.error("Invalid draft creation request received for campaignId {} with statusId {} and enterpriseId {}", campaignId, onGoingCampaign.getStatus(), enterpriseId);
				throw new CampaignException(ErrorCodes.INVALID_REQUEST, "Invalid Draft creation request");
				
			}
			onGoingCampaign.setName(campaignRequest.getCampaignName());
			if (resellerId.length == 0) {
				onGoingCampaign.setEnterpriseId(enterpriseId);
				onGoingCampaign.setSmsTemplateId(campaignRequest.getSmsTemplateId());
				onGoingCampaign.setTemplateId(campaignRequest.getEmailTemplateId());
				// onGoingCampaign.setCreatedBy(uid);
				onGoingCampaign.setEditedBy(prepareUserName(uid));
			}
			
			onGoingCampaign.setUpdatedAt(new Date());
			onGoingCampaign.setEditedOn(new Date());
		}
		// create New Campaign
		else {
			onGoingCampaign = prepareDefaultCampaignData(enterpriseId, uid, campaignRequest.getEmailTemplateId(), campaignRequest.getSmsTemplateId(), campaignRequest.getCampaignName(), resellerId);
			if (resellerId.length == 0) {
				onGoingCampaign.setIsDefault(0);
				onGoingCampaign.setName((BooleanUtils.isFalse(CoreUtils.isTrueForInteger(campaignRequest.getIsSplitCampaign()))) ? getCampaignName(onGoingCampaign.getName(), enterpriseId)
						: onGoingCampaign.getName());
			}
		}
		
		setOngoingCampaignStatus(campaignRequest, onGoingCampaign);
		onGoingCampaign.setType(campaignRequest.getCampaignType());
		onGoingCampaign.setRunType(CampaignRunTypeEnum.ONGOING.getRunType());
		onGoingCampaign.setPriority(campaignRequest.getPriority());
		if (resellerId.length > 0) {
			onGoingCampaign.setReminderCount(campaignRequest.getReminderCount() == null ? 3 : campaignRequest.getReminderCount());
			onGoingCampaign.setReminderFrequency(campaignRequest.getReminderInterval() == null ? 2 : campaignRequest.getReminderInterval());
			onGoingCampaign.setStartAt(new Date());
			onGoingCampaign.setEndAt(new Date());
			onGoingCampaign.setCreatedBy(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("campaign.get.created.by", 12));
		} else {
			onGoingCampaign.setReminderCount(getReminderCount(campaignRequest));
			onGoingCampaign.setReminderFrequency(campaignRequest.getReminderInterval());
		}
		onGoingCampaign.setReminderSubject(campaignRequest.getReminderSubject());
		onGoingCampaign.setSendReminder(sendReminder(campaignRequest));
		onGoingCampaign.setSurveyId(CampaignUtils.isSurveyRequest(campaignRequest.getSurveyId(), campaignRequest.getCampaignType()) ? campaignRequest.getSurveyId() : null);
		// setSchedulingDetails(campaignRequest, onGoingCampaign);
		setSchedulingDetails(onGoingCampaign);
		onGoingCampaign.setTriggerType(getTriggerTypeByEvent(campaignRequest.getTriggerType()));
		onGoingCampaign.setBypassCommRestriction(campaignRequest.getOverrideCommRestriction());
		onGoingCampaign.setIsSplitCampaign(campaignRequest.getIsSplitCampaign());
		onGoingCampaign.setSurveyCommFrequency(campaignRequest.getSurveyCommFrequency());
		onGoingCampaign = campaignRepo.saveAndFlush(onGoingCampaign);
		newAndOldCampaigns[0] = onGoingCampaign;
		return newAndOldCampaigns;
	}

	/**
	 * The scheduling details are now saved in campaign condition so setting these as null for handling old automation
	 * @param onGoingCampaign
	 */
	private void setSchedulingDetails(Campaign onGoingCampaign) {
		onGoingCampaign.setSchedulingInHours(null);
		onGoingCampaign.setSchedule(null);
	}

	private int sendReminder(AutomationCampaignRequest campaignRequest) {
		if (StringUtils.equalsAnyIgnoreCase(campaignRequest.getTriggerType(), CampaignTriggerTypeEnum.BEFORE_APPOINTMENT_DATE.getType(), CampaignTriggerTypeEnum.APPOINTMENT_RECALL.getType())) {
			return 1;
		}
		return campaignRequest.isSendReminder() ? 1 : 0;
	}
	
	private Integer getReminderCount(AutomationCampaignRequest campaignRequest) {
		if (StringUtils.equalsAnyIgnoreCase(campaignRequest.getTriggerType(), CampaignTriggerTypeEnum.BEFORE_APPOINTMENT_DATE.getType(), CampaignTriggerTypeEnum.APPOINTMENT_RECALL.getType())) {
			return CollectionUtils.size(campaignRequest.getAppointmentScheduleInfo());
		}
		return campaignRequest.getReminderCount();
	}
	
	private String getTriggerTypeByEvent(String event) {
		if (StringUtils.isEmpty(event) || "Contact is added to Birdeye".equalsIgnoreCase(event))
			event = CampaignTriggerTypeEnum.CONTACT_ADDED.getType();
		
		CampaignTriggerTypeCache triggerTypeCache = CacheManager.getInstance().getCache(CampaignTriggerTypeCache.class);
		CampaignTriggerType triggerType = triggerTypeCache.getCampaignTriggerTypeByName(event);
		if (triggerType != null)
			return triggerType.getTriggerName();
		return null;
	}
	
	private void setOngoingCampaignStatus(AutomationCampaignRequest campaignRequest, Campaign onGoingCampaign) {
		if (campaignRequest.getIsDraft() != null && campaignRequest.getIsDraft() == 1) {
			onGoingCampaign.setStatus(CampaignStatusEnum.DRAFT.getStatus());
			onGoingCampaign.setPriorityOrder(CampaignStatusEnum.DRAFT.getPriorityOrder());
		} else if (campaignRequest.getStatusId() != null) {
			CampaignStatusEnum status = CampaignStatusEnum.getStatus(campaignRequest.getStatusId());
			if (status == null) {
				status = CampaignStatusEnum.ACTIVE;
				
			}
			onGoingCampaign.setStatus(status.getStatus());
		} else {
			onGoingCampaign.setStatus(CampaignStatusEnum.ACTIVE.getStatus());
			onGoingCampaign.setPriorityOrder(CampaignStatusEnum.ACTIVE.getPriorityOrder());
		}
	}
	
	@Override
	public AutomationCampaignResponse getAutomationCampaign(Integer enterpriseId, Integer campaignId, Integer userId) {
		CampaignUtils.validateAccountId(enterpriseId);
		AutomationCampaignResponse response = new AutomationCampaignResponse();
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
		Campaign campaign = campaignCachingService.getCampaignById(campaignId);
		CampaignUtils.validateCampaignId(enterpriseId, campaign.getEnterpriseId());
		executorService.submit(campaignDetailsTask(campaign, response));
		executorService.submit(campaignConditionTask(enterpriseId, campaignId, response, campaign.getTriggerType()));
		executorService.submit(campaignAuditLogInfoTask(campaign, response));
		
		// BIRD-50563 | User Access Settings Handling
		Boolean isAccessSettingApplicable = (userId != null && userAccessSettingsService.isAccessSettingApplicable(enterpriseId));
		if (BooleanUtils.isTrue(isAccessSettingApplicable)) {
			executorService.submit(userAccessTask(enterpriseId, campaignId, userId, response));
		}
		
		// reports not available for Draft campaigns
		if (campaign.getStatus() != CampaignStatusEnum.DRAFT.getStatus()) {
			CampaignsFilterCriteria filterCriteria = filterCriteriaService.prepareCampaignsFilterCriteria(enterpriseId, campaign);
			executorService.submit(getCampaignUsageReportTask(filterCriteria, response));
		}
		try {
			executorCommonService.executeTasks(executorService, 5000);
		} catch (Exception exe) {
			logger.error(ERROR_WHILE_EXECUTING_THE_THE_GET_DETAILS_FOR_EID_AND_CAMP_ID, exe, enterpriseId, campaignId);
		}
		
		// BIRD-50563 | User Access Settings Handling - Invalid Access Handling
		if (BooleanUtils.isFalse(CampaignUserAccessUtils.isUserHasViewPermission(response.getUserPermissions(), isAccessSettingApplicable))) {
			logger.error("getAutomationCampaign :: Invalid Request as user {} does not have appropriate permissions for campaign id {}", userId, campaignId);
			return CampaignUtils.getAutomationViewDetailsEmptyResponse(
					CollectionUtils.isEmpty(response.getUserPermissions()) ? new ArrayList<>(Arrays.asList(CampaignUserAccessUtils.fetchDefaultAccessSettingForAUser()))
							: response.getUserPermissions());
		}
		return response;
		
	}
    
	private Callable<Boolean> campaignDetailsTask(Campaign campaign, final AutomationCampaignResponse response) {
		return new CampaignCallable<Boolean>("Campaign details Task") {
			@Override
			public Boolean doCall() {
				prepareCampaignDetails(campaign, response);
				return true;
			}
		};
	}
	
	private Callable<Boolean> campaignConditionTask(final Integer enterpriseId, final Integer campaignId, final AutomationCampaignResponse response, final String triggerType) {
		return new CampaignCallable<Boolean>("Campaign Condition Task") {
			@Override
			public Boolean doCall() {
				CampaignCondition campaignCondition = campaignCachingService.getCampaignConditionByCampaign(campaignId, enterpriseId);
				Campaign campaign = campaignCachingService.getCampaignById(campaignId);
				if (campaignCondition != null) {
					prepareCampaignConditionData(campaignCondition, response, campaign);
				}
				return true;
			}
		};
	}
	
	private Callable<Boolean> campaignAuditLogInfoTask(final Campaign campaign, final AutomationCampaignResponse response) {
		return new CampaignCallable<Boolean>("Campaign Audit Info Task") {
			@Override
			public Boolean doCall() {
				// Fetch latest audit log of the campaign
				CampaignEntitiesChangeLog changeLog = campaignEntitiesChangeLogDao.findLatestChangeLogByIdAndType(campaign.getEnterpriseId(), campaign.getId(),
						CampaignModificationAuditUtils.CAMPAIGN_ENTITY, StringUtils.EMPTY);
				String businessTimeZoneId = businessService.getBusinessTimezoneId(campaign.getEnterpriseId());
				if (Objects.isNull(changeLog)) {
					// Prepare creation audit using the entity
					changeLog = new CampaignEntitiesChangeLog(campaign.getEnterpriseId(), campaign.getId(), campaign.getCreatedBy(),
							CampaignModificationUserActionEnum.CREATE.getUserActionType(), campaign.getCreatedAt());
				}
				prepareAuditLogInfo(changeLog, response, businessTimeZoneId);
				return true;
			}
			
			private void prepareAuditLogInfo(CampaignEntitiesChangeLog changeLog, AutomationCampaignResponse response, String businessTimeZoneId) {
				EditInfoResponse auditInfoResponse = new EditInfoResponse(cacheService.getUserResponsibleForModification(changeLog.getUserId(), campaign.getEnterpriseId()),
						changeLog.getUserId(), changeLog.getEvent().toLowerCase(),
						DateTimeUtils.prepareFormattedDate(changeLog.getEventTime(), CampaignModificationAuditUtils.MODIFICATION_DATE_FORMAT, businessTimeZoneId),
						DateTimeUtils.prepareFormattedDate(changeLog.getEventTime(), CampaignModificationAuditUtils.MODIFICATION_TIME_FORMAT, businessTimeZoneId));
				response.setAuditLogInfo(auditInfoResponse);
			}
		};
	}
	
	/**
	 * 
	 * Fetch And Set User Permissions/ Access Settings For Given Campaign Id and User Id In Response
	 * 
	 * @param accountId,
	 *            campaignId, userId, response
	 * 			
	 */
	private Callable<Boolean> userAccessTask(Integer accountId, Integer campaignId, Integer userId, AutomationCampaignResponse response) {
		return new CampaignCallable<Boolean>("User Access Task") {
			@Override
			public Boolean doCall() {
				CampaignUserAccessDTO userCampaignAccessSettings = userAccessSettingsService.fetchAccessSettings(new CampaignUserAccessRequest(accountId, userId, campaignId, null));
				if (userCampaignAccessSettings != null) {
					response.setUserPermissions(new ArrayList<>(Collections.singletonList(userCampaignAccessSettings.getAccess())));
				}
				return true;
			}
		};
	}
	
	private void prepareCampaignDetails(Campaign campaign, AutomationCampaignResponse response) {
		response.setId(campaign.getId());
		response.setCampaignName(campaign.getName());
		response.setCampaignType(campaign.getType());
		response.setRunType(CampaignRunTypeEnum.getEnum(campaign.getRunType()).getLabel());
		response.setStatusId(campaign.getStatus());
		response.setCreatedOn(prepareDateAsPerTimeZone(campaign.getCreatedAt(), "MMM dd, yyyy", campaign.getEnterpriseId()));
		response.setCreatedBy(prepareUserName(campaign.getCreatedBy()));
		response.setPriority(campaign.getPriority());
		response.setSurveyId(campaign.getSurveyId());
		response.setTriggerType(campaign.getTriggerType());
		if (CampaignTypeEnum.SURVEY_REQUEST.getType().equalsIgnoreCase(campaign.getCampaignType())) {
			response.setSurveyName(surveyExternalService.getSurveyNameById(campaign.getSurveyId()));
		}
		response.setSendReminder(campaign.getSendReminder() != null && campaign.getSendReminder() == 1);
		response.setReminderCount(campaign.getReminderCount());
		response.setReminderInterval(campaign.getReminderFrequency());
		setSchedulingDetails(campaign, response);
		if (!isEmailTemplateDeleted(campaign.getTemplateId())) {
			response.setEmailTemplateId(campaign.getTemplateId());
		}
		if (!isSmsTemplateDeleted(campaign.getSmsTemplateId())) {
			response.setSmsTemplateId(campaign.getSmsTemplateId());
		}
		response.setEmailTemplate(prepareEmailTemplateName(campaign.getTemplateId()));
		response.setSmsTemplate(prepareSMSTemplate(campaign.getSmsTemplateId()));
		
		if (StringUtils.equalsIgnoreCase(campaign.getType(), CampaignTypeEnum.PROMOTIONAL.getType())) {
			
			if (CoreUtils.isTrueForInteger(campaign.getTemplateId())) {
				response.setEmailTemplateCategory(templateHelperService.prepareEmailTemplateCategory(campaign.getTemplateId()));
			}
			
			if (CoreUtils.isTrueForInteger(campaign.getSmsTemplateId())) {
				response.setSmsTemplateCategory(templateHelperService.prepareSmsTemplateCategory(campaign.getSmsTemplateId()));
			}
			
		}
		
		// edit info
		if (StringUtils.isNotEmpty(campaign.getEditedBy()) && campaign.getEditedOn() != null) {
			response.setEditInfo(prepareCampaignEditDetails(campaign));
		}
		response.setOverrideCommRestriction(campaign.getBypassCommRestriction());
	}
	
	private CampaignEditInfo prepareCampaignEditDetails(Campaign campaign) {
		CampaignEditInfo editInfo = new CampaignEditInfo();
		editInfo.setEditedBy(campaign.getEditedBy());
		editInfo.setEditedOn(prepareDateAsPerTimeZone(campaign.getEditedOn(), "MMM dd, yyyy", campaign.getEnterpriseId()));
		return editInfo;
	}
	
	private String prepareDateAsPerTimeZone(Date date, String dateFormat, Integer businessId) {
		String timeZoneId = businessService.getBusinessTimezoneId(businessId);
		return DateTimeUtils.prepareFormattedDate(date, dateFormat, timeZoneId);
	}
	
	
	private void getAppointmentSchedulingInfo(CampaignCondition campCondition, AutomationCampaignResponse response, Campaign campaign) {
		if (StringUtils.equalsIgnoreCase(CampaignTriggerTypeEnum.BEFORE_APPOINTMENT_DATE.getType(), campaign.getTriggerType())) {
			response.setAppointmentScheduleInfo(campCondition.getAppointmentScheduleInfo());
		} else if (StringUtils.equalsIgnoreCase(CampaignTriggerTypeEnum.APPOINTMENT_RECALL.getType(), campaign.getTriggerType())) {
			response.setDueDateInfo(campCondition.getExecutionDateInfo());
			response.setAppointmentScheduleInfo(campCondition.getAppointmentScheduleInfo());
		} else {
			if (CollectionUtils.isEmpty(campCondition.getAppointmentScheduleInfo())) {
				AppointmentScheduleInfo scheduleInfo = null;
				if (campaign.getSchedule() == null || campaign.getSchedule() == 0)
					response.setScheduleInfo(null);
				else {
					if (campaign.getSchedulingInHours() == null && campaign.getSchedule() > 24) {
						scheduleInfo = new AppointmentScheduleInfo(campaign.getSchedule() / 24, 0);
					} else if (campaign.getSchedulingInHours() == null) {
						scheduleInfo = new AppointmentScheduleInfo(campaign.getSchedule(), 1);
					} else if (campaign.getSchedulingInHours() == 0) {
						scheduleInfo = new AppointmentScheduleInfo(campaign.getSchedule() / 24, 0);
					} else {
						scheduleInfo = new AppointmentScheduleInfo(campaign.getSchedule(), 1);
					}
					response.setScheduleInfo(Arrays.asList(scheduleInfo));
				}
			} else response.setScheduleInfo(campCondition.getAppointmentScheduleInfo());
		}
	}
	
	private void getAppointmentSchedulingInfoForEditAutomation(CampaignCondition campCondition, AutomationEditCampaignMessage response, Campaign campaign) {
		if (StringUtils.equalsIgnoreCase(CampaignTriggerTypeEnum.BEFORE_APPOINTMENT_DATE.getType(), campaign.getTriggerType())) {
			response.setAppointmentScheduleInfo(campCondition.getAppointmentScheduleInfo());
		} else if (StringUtils.equalsIgnoreCase(CampaignTriggerTypeEnum.APPOINTMENT_RECALL.getType(), campaign.getTriggerType())) {
			response.setDueDateInfo(campCondition.getExecutionDateInfo());
			response.setAppointmentScheduleInfo(campCondition.getAppointmentScheduleInfo());
		} else {
			if (CollectionUtils.isEmpty(campCondition.getAppointmentScheduleInfo())) {
				AppointmentScheduleInfo scheduleInfo = null;
				if (campaign.getSchedule() == null || campaign.getSchedule() == 0)
					response.setScheduleInfo(null);
				else {
					if (campaign.getSchedulingInHours() == null && campaign.getSchedule() > 24) {
						scheduleInfo = new AppointmentScheduleInfo(campaign.getSchedule() / 24, 0);
					} else if (campaign.getSchedulingInHours() == null) {
						scheduleInfo = new AppointmentScheduleInfo(campaign.getSchedule(), 1);
					} else if (campaign.getSchedulingInHours() == 0) {
						scheduleInfo = new AppointmentScheduleInfo(campaign.getSchedule() / 24, 0);
					} else {
						scheduleInfo = new AppointmentScheduleInfo(campaign.getSchedule(), 1);
					}
					response.setScheduleInfo(Arrays.asList(scheduleInfo));
				}

			} else response.setScheduleInfo(campCondition.getAppointmentScheduleInfo());
		}
	}
	
	private void prepareCampaignConditionData(CampaignCondition campCondition, AutomationCampaignResponse response, Campaign campaign) {
		response.setLvlAlias(campCondition.getLvlAlias());
		response.setLvlAliasId(campCondition.getLvlAliasId());
		response.setLvlIds(campCondition.getLvlIds());
		if (CollectionUtils.isEmpty(campCondition.getLvlIds()) && "LOC".equalsIgnoreCase(campCondition.getLvlAlias())) {
			response.setSelectAll(true);
		} else {
			response.setSelectAll(false);
			if ("LOC".equalsIgnoreCase(campCondition.getLvlAlias()) && CollectionUtils.isNotEmpty(campCondition.getLvlIds())) {
				response.setLvlIds(getValidBusinessIds(campCondition.getLvlIds()));
			}
		}
		response.setExpression(campCondition.getRuleExpression());
		response.setSources(campCondition.getContactSources());
		response.setTriggerExpression(campCondition.getTriggerRuleExpression());
		response.setContactEventType(campCondition.getRecurringReminderEventType());
		// response.setAppointmentScheduleInfo(campCondition.getAppointmentScheduleInfo());
		getAppointmentSchedulingInfo(campCondition, response, campaign);
	}
	
	@Override
	public AutomationEditCampaignMessage getEditAutomationCampaign(Integer enterpriseId, Integer campaignId, Integer userId) {
		AutomationEditCampaignMessage response = new AutomationEditCampaignMessage();
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
		executorService.submit(editCampaignDetailsTask(campaignId, response));
		executorService.submit(editCampaignConditionTask(enterpriseId, campaignId, response));
		// BIRD-50563 | User Access Settings Handling
		Boolean isAccessSettingApplicable = (userId != null && userAccessSettingsService.isAccessSettingApplicable(enterpriseId));
		if (BooleanUtils.isTrue(isAccessSettingApplicable)) {
			executorService.submit(editUserAccessTask(enterpriseId, campaignId, userId, response));
		}
		try {
			executorCommonService.executeTasks(executorService, 5000);
		} catch (Exception exe) {
			logger.error(ERROR_WHILE_EXECUTING_THE_THE_GET_DETAILS_FOR_EID_AND_CAMP_ID, exe, enterpriseId, campaignId);
		}
		
		// BIRD-50563 | User Access Settings Handling - Invalid Access Case
		if (BooleanUtils.isFalse(CampaignUserAccessUtils.isUserHasEditPermission(response.getUserPermissions(), isAccessSettingApplicable))) {
			logger.error("getEditAutomationCampaign :: Invalid Request as user {} does not have appropriate permissions for campaign id {}", userId, campaignId);
			return CampaignUtils.getEditAutomationEmptyResponse(
					(CollectionUtils.isEmpty(response.getUserPermissions()) ? new ArrayList<>(Arrays.asList(CampaignUserAccessUtils.fetchDefaultAccessSettingForAUser()))
							: response.getUserPermissions()));
		}
		return response;
		
	}
	
	private Callable<Boolean> editCampaignDetailsTask(final Integer campaignId, final AutomationEditCampaignMessage response) {
		return new CampaignCallable<Boolean>("Edit Campaign details Task") {
			@Override
			public Boolean doCall() {
				Campaign campaign = campaignRepo.getByCampaignId(campaignId);
				if (campaign != null) {
					prepareEditCampaignDetails(campaign, response);
				}
				return true;
			}
		};
	}
	
	private void prepareEditCampaignDetails(Campaign campaign, AutomationEditCampaignMessage response) {
		response.setId(campaign.getId());
		response.setCampaignName(campaign.getName());
		response.setCampaignType(campaign.getType());
		response.setPriority(campaign.getPriority());
		response.setSurveyId(campaign.getSurveyId());
		response.setRunType(CampaignRunTypeEnum.getEnum(campaign.getRunType()).getLabel());
		if (CampaignTypeEnum.SURVEY_REQUEST.getType().equalsIgnoreCase(campaign.getCampaignType())) {
			response.setSurveyName(surveyExternalService.getSurveyNameById(campaign.getSurveyId()));
		}
		response.setSendReminder(campaign.getSendReminder() != null && campaign.getSendReminder() == 1);
		response.setReminderCount(campaign.getReminderCount());
		response.setReminderInterval(campaign.getReminderFrequency());
		response.setReminderSubject(campaign.getReminderSubject());
		response.setEmailTemplateId(campaign.getTemplateId());
		response.setSmsTemplateId(campaign.getSmsTemplateId());
		response.setEmailTemplate(prepareEmailTemplateName(campaign.getTemplateId()));
		response.setSmsTemplate(prepareSMSTemplate(campaign.getSmsTemplateId()));
		response.setIsDraft(campaign.getStatus() == CampaignStatusEnum.DRAFT.getStatus() ? 1 : 0);
		response.setTriggerType(campaign.getTriggerType());
		response.setOverrideCommRestriction(campaign.getBypassCommRestriction());
		response.setSurveyCommFrequency(campaign.getSurveyCommFrequency());
		setSchedulingDetails(campaign, response);
	}
	
	private Callable<Boolean> editCampaignConditionTask(final Integer enterpriseId, final Integer campaignId, final AutomationEditCampaignMessage response) {
		return new CampaignCallable<Boolean>("Edit Campaign Condition Task") {
			@Override
			public Boolean doCall() {
				CampaignCondition campaignCondition = campaignConditionRepo.getByCampaignIdAndEnterpriseId(campaignId, enterpriseId);
				Campaign campaign = campaignCachingService.getCampaignById(campaignId);
				if (campaignCondition != null) {
					prepareEditCampaignConditionData(campaignCondition, response, campaign);
				}
				return true;
			}
		};
	}
	
	/**
	 * 
	 * Fetch And Set User Permissions/ Access Settings and Owner Permissible Locations For Given Campaign Id and User Id In Response
	 * 
	 * @param accountId,
	 *            campaignId, userId, response
	 * 			
	 */
	private Callable<Boolean> editUserAccessTask(Integer accountId, Integer campaignId, Integer userId, AutomationEditCampaignMessage response) {
		return new CampaignCallable<Boolean>("Edit User Access Task") {
			@Override
			public Boolean doCall() {
				CampaignUserAccessDTO userCampaignAccessSettings = userAccessSettingsService
						.fetchAccessSettingsWithOwnerLocationAccess(new CampaignUserAccessRequest(accountId, userId, campaignId, null));
				if (userCampaignAccessSettings != null) {
					response.setUserPermissions(new ArrayList<>(Collections.singletonList(userCampaignAccessSettings.getAccess())));
					response.setOwnerPermissibleLocations(userCampaignAccessSettings.getOwnerPermissibleLocations());
				}
				return true;
			}
		};
	}
	
	private CampaignCallable<Boolean> getCampaignUsageReportTask(CampaignsFilterCriteria filterCriteria, AutomationCampaignResponse response) {
		return new CampaignCallable<Boolean>("getCampaignUsageReportTask") {
			@Override
			public Boolean doCall() {
				CampaignUsageInfo usageInfo = new CampaignUsageInfo();
				campaignUsageService.prepareCampaignUsageReport(filterCriteria, usageInfo, response.getCampaignType());
				response.setUsageInfo(usageInfo);
				return true;
			}
		};
	}
	
	private void prepareEditCampaignConditionData(CampaignCondition campCondition, AutomationEditCampaignMessage response, Campaign campaign) {
		response.setLvlAlias(campCondition.getLvlAlias());
		response.setLvlAliasId(campCondition.getLvlAliasId());
		response.setLvlIds(campCondition.getLvlIds());
		if (CollectionUtils.isEmpty(campCondition.getLvlIds()) && "LOC".equalsIgnoreCase(campCondition.getLvlAlias())) {
			response.setSelectAll(true);
		} else {
			response.setSelectAll(false);
			if ("LOC".equalsIgnoreCase(campCondition.getLvlAlias()) && CollectionUtils.isNotEmpty(campCondition.getLvlIds())) {
				List<String> validBusinessIds = getValidBusinessIds(campCondition.getLvlIds());
				response.setLvlIds(validBusinessIds);
				response.setIsActiveSelectedLocationCountZero(CollectionUtils.isEmpty(validBusinessIds));
			}
		}
		response.setExpression(campCondition.getRuleExpression());
		response.setSources(campCondition.getContactSources());
		response.setTriggerExpression(campCondition.getTriggerRuleExpression());
		response.setContactEventType(campCondition.getRecurringReminderEventType());
		// response.setAppointmentScheduleInfo(campCondition.getAppointmentScheduleInfo());
		getAppointmentSchedulingInfoForEditAutomation(campCondition, response, campaign);
	}
	
	@SuppressWarnings("unchecked")
	private void createCustomFieldAssociation(Integer associatedObjectId, String associatedObjectType, Map<String, RuleCondition> conditions, Integer userId) {
		// first delete all custom fields for that object id and type
		deleteAssociationObject(associatedObjectId, associatedObjectType, CustomFieldSourceEnum.CONTACT);
		if (MapUtils.isEmpty(conditions)) {
			logger.info("custom fields {} are empty", conditions);
			return;
		}
		Set<Integer> addedCustomFields = new HashSet<>();
		for (RuleCondition customField : conditions.values()) {
			// handle duplicate fields and we will not save tags also in association
			if (!addedCustomFields.contains(customField.getId()) && !"Tags".equalsIgnoreCase(customField.getOperand())
					&& BooleanUtils.isFalse(StringUtils.equals(customField.getOperand(), Constants.WROTE_A_REVIEW))) {
				CampaignCustomFieldAssociation fieldAssociation = new CampaignCustomFieldAssociation();
				fieldAssociation.setAssociatedObjectId(associatedObjectId);
				fieldAssociation.setAssociatedType(associatedObjectType);
				fieldAssociation.setCustomFieldName(customField.getOperand());
				fieldAssociation.setCustomFieldId(customField.getId());
				fieldAssociation.setCustomFieldLabel(CoreUtils.convertToValidIdentifier(customField.getOperand()));
				fieldAssociation.setUpdatedBy(userId);
				fieldAssociation.setIsTag(0);
				fieldAssociation.setCustomFieldSource(CustomFieldSourceEnum.CONTACT);
				customFieldAssociationRepo.saveAndFlush(fieldAssociation);
			} else if (!addedCustomFields.contains(customField.getId()) && "Tags".equalsIgnoreCase(customField.getOperand()) && customField.getValue() instanceof List) {
				List<Integer> tagIds = (List<Integer>) customField.getValue();
				logger.info("tagIds {}", tagIds);
				if (CollectionUtils.isNotEmpty(tagIds)) {
					for (Integer tagId : tagIds) {
						CampaignCustomFieldAssociation fieldAssociation = new CampaignCustomFieldAssociation();
						fieldAssociation.setAssociatedObjectId(associatedObjectId);
						fieldAssociation.setAssociatedType(associatedObjectType);
						fieldAssociation.setCustomFieldName("Tags");
						fieldAssociation.setCustomFieldId(tagId);
						fieldAssociation.setCustomFieldLabel(CoreUtils.convertToValidIdentifier(customField.getOperand()));
						fieldAssociation.setUpdatedBy(userId);
						fieldAssociation.setIsTag(1);
						fieldAssociation.setCustomFieldSource(CustomFieldSourceEnum.CONTACT);
						customFieldAssociationRepo.saveAndFlush(fieldAssociation);
					}
				}
				
			}
			
		}
	}
	
	private void deleteAssociationObject(Integer associatedObjectId, String associatedObjectType, CustomFieldSourceEnum customFieldSource) {
		// first delete all custom fields for that object id and type
		int count = customFieldAssociationRepo.deleteCampaignCustomField(associatedObjectId, associatedObjectType, customFieldSource);
		logger.info("for object {} and type {} the fields {} has been deleted", associatedObjectId, associatedObjectType, count);
	}
	
	/**
	 * Checks if a custom field is actively associated with any campaign/template.
	 */
	@Override
	public Boolean isActiveCustomField(Integer fieldId, Integer isTag) {
		
		List<CampaignCustomFieldAssociation> customFieldAssociations = customFieldAssociationRepo.findAllByCustomFieldIdAndIsTagAndIsDeletedAndCustomFieldSource(fieldId, isTag, 0,
				CustomFieldSourceEnum.CONTACT);
		if (CollectionUtils.isEmpty(customFieldAssociations)) {
			return Boolean.FALSE;
		}
		return Boolean.TRUE;
	}
	
	private void validatePriority(String priority) {
		if (StringUtils.isBlank(priority)) {
			logger.error("validatePriority : received blank priority {}", priority);
			throw new CampaignException(ErrorCodes.INVALID_CAMPAIGN, "Invalid campaign priority received");
		}
	}

	private void validateAutomationProperties(AutomationCampaignRequest campaignRequest){
		if(Objects.isNull(campaignRequest.getAppointmentScheduleInfo())){
			if(StringUtils.equalsAnyIgnoreCase(campaignRequest.getCampaignType(), CampaignTypeEnum.APPOINTMENT_REMINDER.getType(),
				CampaignTypeEnum.APPOINTMENT_RECALL.getType())){
				logger.error("Appointment Schedule Info is blank for campaign id :: {}, campaign type :: {}", campaignRequest.getCampaignId(),campaignRequest.getCampaignType());
				throw new CampaignBadRequestException(ErrorCodes.INVALID_CAMPAIGN, "Invalid campaign appointment schedule info received");
			}
			if(StringUtils.equalsAnyIgnoreCase(campaignRequest.getCampaignType(), CampaignTypeEnum.APPOINTMENT_FORM.getType()) &&
				StringUtils.equalsAnyIgnoreCase(campaignRequest.getTriggerType(), CampaignTriggerTypeEnum.BEFORE_APPOINTMENT_DATE.getType())){
				logger.error("Appointment Form Schedule Info is blank for campaign id :: {} , campaign type :: {}", campaignRequest.getCampaignId(),campaignRequest.getCampaignType());
				throw new CampaignBadRequestException(ErrorCodes.INVALID_CAMPAIGN, "Invalid appointment form schedule info received");
			}
		}
		if(Objects.isNull(campaignRequest.getSmsTemplateId()) && Objects.isNull(campaignRequest.getEmailTemplateId())){
			logger.error("Campaign : Sms template Id and email template id both are blank for campaign id {}", campaignRequest.getCampaignId());
			throw new CampaignBadRequestException(ErrorCodes.INVALID_CAMPAIGN, "sms template id and email template id both can't be empty");
		}
	}
	
	@Override
	public void updateDefaultAutomationCampaigns(List<UpdateDefaultCampaignRequest> request) {
		if (CollectionUtils.isEmpty(request)) {
			logger.info("empty request to update default campaign");
			return;
		}
		if (CollectionUtils.size(request) > 20) {
			logger.info(" update default campaign request size can not be exceed greater than 20 for request {}", request.toString());
			return;
		}
		for (UpdateDefaultCampaignRequest enterpriseTocampaignId : request) {
			updateDefaultAutomationCampaign(enterpriseTocampaignId.getAccountId(), enterpriseTocampaignId.getCampaignId());
		}
	}
	
	@Override
	public void updateDefaultAutomationCampaign(Integer accountId, Integer campaignId) {
		if (accountId == null || campaignId == null) {
			logger.info("Invalid request params");
			return;
		}
		logger.info("request received to update default campaign for account id {} with campaign id {}", accountId, campaignId);
		BusinessEnterpriseEntity business = businessService.getBusinessById(accountId);
		if (business == null) {
			logger.info("Invalid account id {}", accountId);
			return;
		}
		Campaign campaign = campaignRepo.getValidCampaignById(campaignId);
		
		if (campaign == null || BooleanUtils.isFalse(StringUtils.contains(campaign.getPriority(), "email"))
				|| BooleanUtils.isFalse(StringUtils.equalsIgnoreCase(campaign.getCampaignType(), CampaignTypeEnum.REVIEW_REQUEST.getType()))
				|| BooleanUtils.isFalse(StringUtils.equalsIgnoreCase(campaign.getRunType(), CampaignRunTypeEnum.ONGOING.getRunType())) || campaign.getEnterpriseId().intValue() != accountId) {
			logger.info("Invalid campaign id {} for account id {}", campaignId, accountId);
			return;
		}
		List<Campaign> defaultCampaigns = campaignRepo.getDefaultOngoingCampaignByEnterpriseId(accountId);
		
		if (CollectionUtils.isNotEmpty(defaultCampaigns)) {
			for (Campaign defaultAutomationCampaign : defaultCampaigns) {
				defaultAutomationCampaign.setIsDefault(0);
				campaignRepo.saveAndFlush(defaultAutomationCampaign);
			}
			asyncBrokerService.evictDefaultCampaign(accountId);
		}
		
		campaign.setIsDefault(1);
		campaignRepo.saveAndFlush(campaign);
		logger.info("campaign marked as default with id {} and enterprise id {}", campaignId, accountId);
	}
	
	private void createAppointmentCustomFieldAssociation(Integer associatedObjectId, String associatedObjectType, Map<String, RuleCondition> conditions, Integer userId) {
		// first delete all custom fields for that object id and type
		deleteAssociationObject(associatedObjectId, associatedObjectType, CustomFieldSourceEnum.APPOINTMENT);
		if (MapUtils.isEmpty(conditions)) {
			logger.info("Appointment related custom fields and triggers {} are empty", conditions);
			return;
		}
		Set<Integer> addedCustomFields = new HashSet<>();
		for (RuleCondition customField : conditions.values()) {
			// handle duplicate fields and only save appointment custom field
			if (!addedCustomFields.contains(customField.getId()) && StringUtils.equalsIgnoreCase(customField.getCustomFieldSource(), CustomFieldSourceEnum.APPOINTMENT.getType())) {
				CampaignCustomFieldAssociation fieldAssociation = new CampaignCustomFieldAssociation();
				fieldAssociation.setAssociatedObjectId(associatedObjectId);
				fieldAssociation.setAssociatedType(associatedObjectType);
				fieldAssociation.setCustomFieldName(customField.getOperand());
				fieldAssociation.setCustomFieldId(customField.getId());
				fieldAssociation.setCustomFieldLabel(CoreUtils.convertToValidIdentifier(customField.getOperand()));
				fieldAssociation.setUpdatedBy(userId);
				fieldAssociation.setIsTag(0);
				fieldAssociation.setCustomFieldSource(CustomFieldSourceEnum.APPOINTMENT);
				customFieldAssociationRepo.saveAndFlush(fieldAssociation);
			}
		}
	}
	
	/**
	 * Checks if a location custom field is actively associated with any campaign/template.
	 */
	@Override
	public Boolean isActiveLocationCustomField(Integer fieldId, Integer isTag) {
		
		List<CampaignCustomFieldAssociation> customFieldAssociations = customFieldAssociationRepo.findAllByCustomFieldIdAndIsTagAndIsDeletedAndCustomFieldSource(fieldId, isTag, 0,
				CustomFieldSourceEnum.LOCATION);
		if (CollectionUtils.isEmpty(customFieldAssociations)) {
			return Boolean.FALSE;
		}
		return Boolean.TRUE;
	}
	
	/**
	 * 
	 * Check if Location Filtering is Applicable for the given listing request by
	 * a) If Location Filters are received in request
	 * b) If a) is true, then business is eligible for location filtering or not.
	 * 
	 * @param accountId,
	 *            campaignFilterRequest
	 * 
	 */
	@Override
	public Boolean isLocationFilteringApplicable(Integer accountId, CampaignFilterRequest campaignFilterRequest) {
		if (CampaignUtils.isCampaignListingRequestValidForLocationFiltering(campaignFilterRequest)) {
			BusinessFeaturesResponse businessFeatures = cacheService.getBusinessFeaturesForAccountId(accountId, true);
			return CampaignUtils.isAccountApplicableForLocationFiltering(businessFeatures != null ? businessFeatures.getFeatures() : new HashMap<>());
		}
		return false;
	}
	
	@Override
	public AutomationsCountResponse getOngoingCampaignCount(Integer accountId) {
		if (Objects.isNull(accountId)) {
			throw new CampaignHTTPException(HttpStatus.BAD_REQUEST, "Account Id can not be null.");
		}
		
		List<CampaignCountAndTypeDto> campaignCountAndTypeMapping = campaignRepo.getOngoingCampaignCountByAccountId(accountId);
		if (CollectionUtils.isEmpty(campaignCountAndTypeMapping)) {
			return new AutomationsCountResponse();
		}
		
		Map<String, Long> campaignCountMap = campaignCountAndTypeMapping.stream().collect(Collectors.toMap(CampaignCountAndTypeDto::getType, CampaignCountAndTypeDto::getCount));
		
		Long rrAutomationCount = getRRAutomationCountFromMap(campaignCountMap);
		
		return new AutomationsCountResponse(rrAutomationCount);
	}

	private Long getRRAutomationCountFromMap(Map<String, Long> campaignCountMap) {
		Long rrAutomationCount = campaignCountMap.containsKey(CampaignTypeEnum.REVIEW_REQUEST.getType()) ? campaignCountMap.get(CampaignTypeEnum.REVIEW_REQUEST.getType()) : 0L;
		return rrAutomationCount;
	}

	/**
	 * Retrieves a list of unique survey usage information for an enterprise.
	 * <p>
	 * For each unique survey ID, it calculates the communication frequency and
	 * the number of times the survey appears in ongoing campaigns of type "SURVEY_REQUEST".
	 *
	 * @param enterpriseId The ID of the enterprise whose survey campaign data is to be retrieved.
	 * @return A list of {@link SurveyUsageInfo} objects containing unique survey ID,
	 * communication frequency, and the count of campaigns using that survey.
	 * @throws CampaignException if the provided enterpriseId is null.
	 */
	@Override
	public List<SurveyUsageInfo> fetchSurveyUsageStats(Integer enterpriseId) {
		if (enterpriseId == null) {
			throw new CampaignException(ErrorCodes.INVALID_REQUEST);
		}
		
		List<CampaignRepo.CampaignProjection> campaigns = campaignRepo.getCampaignSurveyDetails(enterpriseId, CampaignTypeEnum.SURVEY_REQUEST.getType(), CampaignRunTypeEnum.ONGOING.getRunType(), 0);
		if (CollectionUtils.isEmpty(campaigns)) {
			return Collections.emptyList();
		}

		// Map to collect unique survey IDs with their frequency and occurrence count
		Map<Integer, SurveyUsageInfo> surveyMap = new HashMap<>();
		for (CampaignRepo.CampaignProjection campaign : campaigns) {
			Integer surveyId = campaign.getSURVEYID();
			Integer frequency = campaign.getSURVEYCOMMFREQUENCY();
			Integer overrideCommRestriction = campaign.getBYPASSCOMMRESTRICTION();
			if (surveyMap.containsKey(surveyId)) {
				SurveyUsageInfo info = surveyMap.get(surveyId);
				info.setAutomationCount(info.getAutomationCount() + 1);
				
				if (overrideCommRestriction != null && overrideCommRestriction == 1 && (info.getOverrideCommRestriction() == null || info.getOverrideCommRestriction() == 0)) {
					info.setOverrideCommRestriction(1);
				}
			} else {
				surveyMap.put(surveyId, new SurveyUsageInfo(surveyId, frequency, 1, overrideCommRestriction));
			}
		}
		return new ArrayList<>(surveyMap.values());
	}
}
