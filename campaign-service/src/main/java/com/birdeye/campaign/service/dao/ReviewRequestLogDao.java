package com.birdeye.campaign.service.dao;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.birdeye.campaign.communication.message.CommunicationInput;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.ReviewRequestLogDto;
import com.birdeye.campaign.entity.ReviewRequest;
import com.birdeye.campaign.entity.ReviewRequestsLog;

/**
 * <AUTHOR>
 */
public interface ReviewRequestLogDao {
	
	void updateEventInfo(Map<Long, CommunicationInput> messageById, Map<Long, Long> parentRequestIdByChildId, Map<Long, ReviewRequest> idByRequestsMap,
			Map<Integer, BusinessEnterpriseEntity> businessEntitiesMap);
	
	List<ReviewRequestLogDto> getReviewRequestLogByReviewRequestIds(List<Long> reviewRequestIds);

	List<ReviewRequestsLog> getByReviewRequestIdAndEventAndClickType(Set<Long> reviewRequestIds, String event, Integer clickType);

	List<ReviewRequestsLog> getByReviewRequestIdAndEventAndSourceIdNotNull(Set<Long> keySet, String event);

}
