package com.birdeye.campaign.service.dao.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.dto.AccessSettingMigrateDTO;
import com.birdeye.campaign.dto.SplitCampaignMappingDTO;
import com.birdeye.campaign.entity.SplitCampaign;
import com.birdeye.campaign.entity.SplitCampaignMapping;
import com.birdeye.campaign.repository.SplitCampaignMappingRepo;
import com.birdeye.campaign.repository.SplitCampaignRepo;
import com.birdeye.campaign.service.dao.SplitCampaignDao;

@Service("SplitCampaignDao")
public class SplitCampaignDaoImpl implements SplitCampaignDao {
	
	@Autowired
	private SplitCampaignRepo			splitRepo;
	
	@Autowired
	private SplitCampaignMappingRepo	mappingRepo;
	
	private static final Logger			logger	= LoggerFactory.getLogger(SplitCampaignDaoImpl.class);
	
	/**
	 * Fetch Split Campaign Mapping Data for given Split Campaign Id.
	 * 
	 * @param splitCampaignId
	 * @param accountId
	 * @return
	 */
	@Override
	public List<SplitCampaignMappingDTO> getSplitMappingDataBySplitCampaignId(Integer splitCampaignId) {
		if (splitCampaignId == null || splitCampaignId == 0) {
			logger.warn("Null/Zero value received for splitCampaignId!");
			return new ArrayList<SplitCampaignMappingDTO>();
		}
		return mappingRepo.getSplitCampaignMappingDataBySplitCampaignId(splitCampaignId);
	}
	
	/**
	 *
	 * Fetch Paginated Split Campaign Ids By Filter Criteria And Name
	 *
	 * 
	 * @param accountId,
	 *            campaignTypes, status, runType, triggerTypes, searchStr, page
	 */
	@Override
	public Page<SplitCampaign> fetchPaginatedSplitCampaignIdWithFilterCriteriaAndName(Integer accountId, List<String> campaignTypes, List<Integer> status, String runType, List<String> triggerTypes,
			String searchStr, Pageable page) {
		return splitRepo.getPaginatedSplitCampaignIdByFilterCriteriaAndName(accountId, campaignTypes, status, runType, triggerTypes, searchStr, page);
	}
	
	/**
	 *
	 * Fetch Paginated Split Campaign By Filter Criteria And Name And Split Campaign Ids
	 *
	 * 
	 * @param accountId,
	 *            campaignTypes, status, runType, triggerTypes, searchStr, page
	 */
	@Override
	public Page<SplitCampaign> fetchPaginatedSplitCampaignIdWithFilterCriteriaAndNameAndIds(Integer accountId, List<String> campaignTypes, List<Integer> status, String runType, List<String> triggerTypes,
			String searchStr, List<Integer> splitCampaignIds, Pageable page) {
		return splitRepo.getPaginatedSplitCampaignByFilterCriteriaAndIdAndName(accountId, campaignTypes, status, runType, triggerTypes, searchStr, splitCampaignIds, page);
	}
	
	/**
	 *
	 * Fetch Paginated Split Campaign Ids By Filter Criteria
	 *
	 * 
	 * @param accountId,
	 *            campaignTypes, status, runType, triggerTypes, page
	 */
	@Override
	public Page<SplitCampaign> fetchPaginatedSplitCampaignIdWithFilterCriteria(Integer accountId, List<String> campaignTypes, List<Integer> status, String runType, List<String> triggerTypes,
			Pageable page) {
		return splitRepo.getPaginatedSplitCampaignIdByFilterCriteria(accountId, campaignTypes, status, runType, triggerTypes, page);
	}
	
	/**
	 *
	 * Fetch Paginated Split Campaign By Filter Criteria and split campaign ids
	 *
	 * 
	 * @param accountId,
	 *            campaignTypes, status, runType, triggerTypes, splitCampaignIds, page
	 */
	@Override
	public Page<SplitCampaign> fetchPaginatedSplitCampaignIdWithFilterCriteriaAndIds(Integer accountId, List<String> campaignTypes, List<Integer> status, String runType, List<String> triggerTypes,
			List<Integer> splitCampaignIds, Pageable page) {
		return splitRepo.getPaginatedSplitCampaignIdByFilterCriteriaAndId(accountId, campaignTypes, status, runType, triggerTypes, splitCampaignIds, page);
	}
	
	/**
	 *
	 * Fetch Split Campaign Data for given split campaign id list
	 *
	 * 
	 * @param splitCampaignIdList
	 */
	@Override
	public List<SplitCampaignMappingDTO> getSplitCampaignMappingDataBySplitCampaignIds(List<Integer> splitCampaignIdList) {
		if (CollectionUtils.isEmpty(splitCampaignIdList)) {
			return new ArrayList<>();
		}
		return mappingRepo.getSplitCampaignMappingDataBySplitCampaignIds(splitCampaignIdList);
	}
	
	/**
	 *
	 * Fetch Split Campaign Count For Given Account Id and Run type.
	 *
	 * 
	 * @param accountId,
	 *            runType
	 */
	@Override
	public Integer getSplitCampaignCountByAccountIdAndRunType(Integer accountId, String runType) {
		return splitRepo.getSplitCampaignCountByAccountIdAndRunType(accountId, runType);
	}
	
	/**
	 *
	 *
	 * Fetch Split Campaign By Id
	 *
	 * 
	 * @param splitCampaignId
	 */
	@Override
	public Optional<SplitCampaign> getSplitCampaignById(Integer splitCampaignId) {
		return splitRepo.findById(splitCampaignId);
	}
	
	/**
	 *
	 * Fetch Same Name Count for given account and name
	 *
	 * 
	 * @param accountId,
	 *            splitCampaignName
	 */
	@Override
	public Integer getSameNameCount(Integer accountId, String splitCampaignName) {
		return splitRepo.getSplitCampaignSameNameCountByAccountIdAndName(accountId, splitCampaignName);
	}
	
	/**
	 *
	 * Save or Update Split Campaign Audit
	 *
	 * 
	 * @param SplitCampaign
	 */
	@Override
	public void saveSplitCampaignEntry(SplitCampaign audit) {
		splitRepo.saveAndFlush(audit);
	}
	
	/**
	 *
	 * Save or Update Split Campaign Mapping Audit
	 *
	 * 
	 * @param SplitCampaign
	 */
	@Override
	public void saveSplitCampaignMappingEntry(SplitCampaignMapping audit) {
		mappingRepo.saveAndFlush(audit);
	}
	
	/**
	 *
	 * Method to fetch SplitCampaignMapping data using split campaign id
	 * 
	 * @param splitCampaignId
	 * 
	 */
	@Override
	public List<SplitCampaignMapping> getMappingDataBySplitCampaignId(Integer splitCampaignId) {
		if(splitCampaignId == null) {
			logger.warn("Null value received for splitCampaignId!");
			return null;
		}
		return mappingRepo.findBySplitCampaignId(splitCampaignId);
	}

	/**
	 * Method to update the delete flag for a split campaign.
	 * 
	 * @param deleteFlag
	 * @param splitCampaignId
	 * @return
	 */
	@Override
	public Integer updateSplitCampaignDeleteFlag(Integer deleteFlag, Integer splitCampaignId) {
		if(deleteFlag == null || splitCampaignId == null) {
			logger.info("updateSplitCampaignDeleteFlag :: null value received for either deleteFlag or splitCampaignId!");
			return 0;
		}
		return splitRepo.updateSplitCampaignDeleteFlag(deleteFlag, splitCampaignId);
	}

	/**
	 * Method to update the enterprise id of the split campaigns.
	 * 
	 * @param fromBusinessId
	 * @param toBusinessId
	 * @return
	 */
	@Override
	public Integer updateSplitCampaignAccountId(Integer fromBusinessId, Integer toBusinessId) {
		return splitRepo.updateSplitCampaignAccountId(fromBusinessId, toBusinessId);
	}
	
	/**
	 *
	 * Method to fetch SplitCampaignMapping data by enterprise id and status list
	 * 
	 * @param accountId, statusList
	 * 
	 */
	@Override
	public List<SplitCampaignMappingDTO> getMappingDataByAccountIdAndStatus(Integer accountId, List<Integer> statusList) {
		if (accountId == null || CollectionUtils.isEmpty(statusList)) {
			logger.error("Invalid request recieved");
			return null;
		}
		return splitRepo.fetchSplitMappingsByAccountIdAndStatusIn(accountId, statusList);
	}
	
	/**
	 *
	 * Method to fetch distinct split campaign count based upon filters
	 * 
	 * @param accountId,
	 *            status, campaignType, runType
	 * 			
	 */
	@Override
	public Integer getSplitCampaignCountBasedOnFilters(Integer accountId, Integer status, String campaignType, String runType) {
		if (accountId == null || StringUtils.isAnyBlank(campaignType, runType) || status == null) {
			logger.error("getSplitCampaignCountBasedOnFilters :: Invalid request recieved");
			return 0;
		}
		return splitRepo.getDistinctSplitCampaignIdByAccountIdAndRunTypeAndStatusAndType(accountId, status, campaignType, runType);
	}
	
	/**
	 * Method to fetch split campaign id from campaign id
	 * 
	 * @param campaignId
	 * @return
	 */
	@Override
	public Integer getSplitCampaignIdForCampaignId(Integer campaignId) {
		List<Integer> splitCampaignIds = mappingRepo.getMappingByCampaignId(campaignId);
		return (CollectionUtils.isEmpty(splitCampaignIds) ? null : splitCampaignIds.get(0));
	}
	
	/**
	 * Method to fetch split campaign data for access setting migration
	 * 
	 * @param accountId
	 */
	@Override
	public List<AccessSettingMigrateDTO> getSplitCampaignDataByAccountId(Integer accountId) {
		return splitRepo.getSplitCampaignDataByAccountId(accountId);
	}
	
	/**
	 * Method to fetch split campaign data using account id and run type
	 * 
	 * @param accountId, runType
	 */
	@Override
	public List<SplitCampaignMappingDTO> fetchSplitDataByAccountIdAndRunType(Integer accountId, String runType) {
		return splitRepo.fetchSplitDataByAccountIdAndRunType(accountId, runType);
	}

}
