package com.birdeye.campaign.service.impl;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.appointment.backfill.service.AppointmentBackfillService;
import com.birdeye.campaign.appointment.service.AppointmentService;
import com.birdeye.campaign.audit.service.AppointmentReminderAuditService;
import com.birdeye.campaign.business.service.BusinessService;
import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.RestrictAppointmentCommunicationCache;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.constant.ReviewRequestTypeEnum;
import com.birdeye.campaign.dto.AppointmentInfoLiteDTO;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.TriggerFilter;
import com.birdeye.campaign.entity.AppointmentBackfillEvent;
import com.birdeye.campaign.entity.AppointmentRRMapping;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.CampaignCondition;
import com.birdeye.campaign.entity.CampaignEvent;
import com.birdeye.campaign.entity.RestrictAppointmentCommunication;
import com.birdeye.campaign.enums.AppointmentBackfillEventStatusEnum;
import com.birdeye.campaign.exception.AppointmentReminderForChainingException;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.external.utils.AppointmentExternalUtils;
import com.birdeye.campaign.platform.constant.CampaignStatusEnum;
import com.birdeye.campaign.platform.constant.CampaignTriggerTypeEnum;
import com.birdeye.campaign.platform.constant.RequestStatusEnum;
import com.birdeye.campaign.readonly.repository.CampaignEventReadOnlyRepo;
import com.birdeye.campaign.repository.CampaignRepo;
import com.birdeye.campaign.repository.RestrictAppointmentCommunicationRepo;
import com.birdeye.campaign.request.ReminderRestrictionRequest;
import com.birdeye.campaign.response.AppointmentCommRestrictionResponse;
import com.birdeye.campaign.response.external.AppointmentDetailsResponse;
import com.birdeye.campaign.service.AppointmentReminderService;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.CampaignSetupCachingService;
import com.birdeye.campaign.service.dao.ReviewRequestDao;
import com.birdeye.campaign.trigger.service.CampaignTriggerEventsService;
import com.birdeye.campaign.utils.AppointmentReminderUtils;
import com.birdeye.campaign.utils.DateTimeUtils;
import com.birdeye.campaign.utils.MvelEvaluationUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class AppointmentReminderServiceImpl implements AppointmentReminderService {
	
	public static final Logger				LOGGER	= LoggerFactory.getLogger(AppointmentReminderServiceImpl.class);
	
	@Autowired
	private CampaignSetupCachingService		campaignSetupCachingService;
	
	@Autowired
	private CampaignTriggerEventsService	campaignTriggerEventsService;
	
	@Qualifier("campaignObjectMapper")
	@Autowired
	private ObjectMapper					objectMapper;
	
	@Autowired
	private AppointmentService				appointmentService;
	
	@Autowired
	private CacheService 					cacheService;
	
	@Autowired
	private CampaignEventReadOnlyRepo				campaignEventReadOnlyRepo;
	
	@Autowired
	private AppointmentReminderAuditService			appointmentReminderAuditService;
	
	@Autowired
	private ReviewRequestDao				requestDao;
	
	@Autowired
	private RestrictAppointmentCommunicationRepo    restrictAppointmentCommunicationRepo;
	
	@Autowired
	private AppointmentBackfillService				appointmentBackfillService;
	
	@Autowired
	private CampaignRepo							campaignRepo;
	
	@Autowired
	private BusinessService							businessService;
	
	@Override
	public Date getRequestScheduleTime(Campaign campaign, Integer appointmentId) {
		CampaignCondition campaignCondition = campaignSetupCachingService.getCampaignConditionByCampaign(campaign.getId(), campaign.getEnterpriseId());	
//		campaignCondition = new CampaignCondition(campaignCondition);
		try {
			String json = objectMapper.writeValueAsString(campaignCondition);
			campaignCondition = objectMapper.readValue(json, campaignCondition.getClass());
			
		} catch (JsonProcessingException e) {
			LOGGER.info("Exception while parsing json to CampaignCondition object for campaign id {} and appointment id {}",campaign.getId(), appointmentId);
			e.printStackTrace();
		}
		
		LOGGER.info("Get reminder schedule time  for given appointment Schedule info : {} for campaign : {}",campaignCondition.getAppointmentScheduleInfo(), campaignCondition.getCampaignId());
		AppointmentDetailsResponse appointmentInfo = appointmentService.getAppointmentById(appointmentId.toString(), campaign.getEnterpriseId(), false, false);
		if (appointmentInfo == null) {
			return null;
		}	
		BusinessEnterpriseEntity business = cacheService.getBusinessById(appointmentInfo.getBusinessId());
		
		long appointmentStartTimeEpochMillis = appointmentInfo.getStartTime(); // Long.parseLong("1664517453000");
		ZoneId zoneId = DateTimeUtils.getZoneIdFromTimeZoneId(business.getTimezoneId());		
		ZonedDateTime zonedAppointmentStartTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(appointmentStartTimeEpochMillis), zoneId);
		
		//prepares a list of reminderScheduleList objects based on campaignCondition like scheduleAt, allowed days, scheduleBy and zonedAppointmentStartTime.
		List<ZonedDateTime> reminderScheduleList = AppointmentReminderUtils.prepareZonedDateTimeWiseReminderSchedule(campaignCondition, zonedAppointmentStartTime, business, appointmentId);
		// Convert the reminder schedules to UTC timezone for consistency
		reminderScheduleList = AppointmentReminderUtils.convertDateToSpecifiedTimeZone(reminderScheduleList, ZoneId.of("UTC"));
		//gets a valid appointment schedule date for the appointment reminder.
		ZonedDateTime scheduledTime = AppointmentReminderUtils.getAppointmentReminderNextValidScheduleDate(reminderScheduleList);
		
		LOGGER.info("Scheduled appointment reminder at time : {} for campaign id {} and appointment id {}",scheduledTime, campaign.getId(), appointmentId);
		if (scheduledTime != null) {
			return Date.from(scheduledTime.toInstant());
		}
		return null;
	}
	
	@Override
	public void validateReminderExecutionRequest(Integer appointmentId, Integer campaignId, Integer enterpriseId, BusinessEnterpriseEntity business, List<Date> appointmentScheduledDates) {
		AppointmentInfoLiteDTO appointmentInfo = appointmentService.getAppointmentLiteById(appointmentId.toString(), enterpriseId, true, false);
		if (appointmentInfo == null) {
			throw new CampaignException(ErrorCodes.NO_VALID_APOINTMENT_FOUND, ErrorCodes.NO_VALID_APOINTMENT_FOUND.getMessage());
		}
		
		if (!AppointmentReminderUtils.isValidAppointmentStatusForExecution(appointmentInfo.getAppointmentStatus())) {
			LOGGER.info("Invalid status found for execution for appointment {}, campaign {}", appointmentId, campaignId);
			throw new CampaignException(ErrorCodes.INVALID_APPOINTMENT_STATUS, ErrorCodes.INVALID_APPOINTMENT_STATUS.getMessage());
		}
		
		// validate MVEL expression
		List<TriggerFilter> triggerFilters = getTriggerFilters(appointmentInfo, CampaignTriggerTypeEnum.BEFORE_APPOINTMENT_DATE.getType());
		CampaignCondition campaignCondition = campaignSetupCachingService.getCampaignConditionByCampaign(campaignId, enterpriseId);
		if (!MvelEvaluationUtils.evaluateTriggerMvelExpression(campaignCondition.getTriggerMvelExpression(), campaignCondition.getTriggerMvelParamsAndTypes(), triggerFilters, false, null)) {
			throw new CampaignException(ErrorCodes.NO_VALID_CAMPAIGN_CONDITION, ErrorCodes.NO_VALID_CAMPAIGN_CONDITION.getMessage());
		}
		
		// check if current time is valid to execute request	
		if (BooleanUtils.isFalse(AppointmentReminderUtils.isValidExecutionTimeRequest(appointmentInfo, business, campaignCondition, appointmentScheduledDates))) {
			throw new AppointmentReminderForChainingException(ErrorCodes.CAMPAIGN_SCHEDULING_CONFIG_NOT_MATCHED, ErrorCodes.CAMPAIGN_SCHEDULING_CONFIG_NOT_MATCHED.getMessage());
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<TriggerFilter> getTriggerFilters(AppointmentInfoLiteDTO appointmentInfo, String triggerType) {
		List<TriggerFilter> triggerFilters = new ArrayList<>();
		try {
			triggerFilters = campaignTriggerEventsService.extractAndPopulateTriggerFilterValues(objectMapper.convertValue(appointmentInfo, Map.class),
					triggerType);
		} catch (JsonProcessingException e) {
			throw new CampaignException(ErrorCodes.NO_VALID_CAMPAIGN_CONDITION, ErrorCodes.NO_VALID_CAMPAIGN_CONDITION.getMessage());
		}
		return triggerFilters;
	}
	
	/**
	 * Retrieves the duplicate reminder communication status for a given customer and communication type.
	 * <p>
	 * This method fetches scheduled reminders and checks for duplicates based on the earliest appointment details.
	 * If there are no or only one reminder, it sets the status as false. For multiple reminders, it performs further validation
	 * and determines if the reminders are duplicates based on the earliest appointment.
	 * </p>
	 *
	 * @param customerId The ID of the customer for whom to fetch reminder communication status.
	 * @param enterpriseId The ID of the enterprise associated with the reminder. (Not used in this method but included in the signature)
	 * @param requestId The ID of the request for which the status is being retrieved.
	 * @param commType The type of communication (e.g., email, SMS) for which reminders are scheduled.
	 * @param business The business enterprise entity used for validation and filtering of reminders.
	 * @return A map where the key is the review request ID and the value is a boolean indicating if it is a duplicate reminder.
	 */
	@Override
	public Map<Long, Boolean> getDuplicateReminderCommunicationStatus(Integer customerId, Integer enterpriseId, Long requestId, String commType,
			BusinessEnterpriseEntity business) {
		LOGGER.info("Fetching duplicate reminder communication status for customerId : {} and commType : {}", customerId, commType);
		
		// Retrieve scheduled reminders for the specified customer and communication type
		List<CampaignEvent> scheduledReminders = getScheduledRemindersForAppointment(customerId, commType, requestId);
		Map<Long, Boolean> reminderStatusMap = new HashMap<>();
		
		if (CollectionUtils.isEmpty(scheduledReminders) || scheduledReminders.size() <= 1) {
			LOGGER.info("No or only one scheduled reminder found. Adding false status for request ID: {}", requestId);
			// If no scheduled reminders found or only one reminder is scheduled,
			// Populate requestMap with request ID as keys and false as value
			reminderStatusMap.put(requestId, false);
		} else {
			// Retrieve appointment IDs associated with each review request ID
			Map<Long, Integer> requestAppointmentMap = mapReviewRequestIdsToAppointmentIds(scheduledReminders);
			
			Map<Long, Integer> requestCampaignIdMap = mapReviewRequestIdsToCampaignIds(scheduledReminders);
			
//			LOGGER.info("Calling appointment batch API for appointmentIds : {}", requestAppointmentMap.values());
			
			Map<String, List<AppointmentDetailsResponse>> appointmentInfo = appointmentService.getAllAppointmentsById(new ArrayList<>(requestAppointmentMap.values()), true,
					true);
			List<AppointmentDetailsResponse> appointmentDetails = appointmentInfo.getOrDefault("appointmentDetails", Collections.emptyList());
			
			// Map appointment IDs to their details
			Map<Integer, AppointmentDetailsResponse> appointmentIdAppointmentDetailMap = mapAppointmentIdsToAppointmentDetail(appointmentDetails);
			
			LOGGER.info("Received appointment batch API response for appointmentIds : {}", appointmentIdAppointmentDetailMap.keySet());
			
			// Validate and filter the scheduled reminders
			validateAndFilterAppointmentReminderRequests(scheduledReminders, requestAppointmentMap, appointmentIdAppointmentDetailMap, requestCampaignIdMap, business);
			
			LOGGER.info("After valiation of appointment and campaign new requests : {}", requestAppointmentMap);
			
			AppointmentDetailsResponse earliestAppointment = fetchEarliestCustomerAppointmentDetails(new ArrayList<>(appointmentIdAppointmentDetailMap.values()));
			LOGGER.info("Earliest appointment details retrieved: {}", earliestAppointment);
			
			boolean earliestAppointmentExists = earliestAppointment != null;
			for (Map.Entry<Long, Integer> entry : requestAppointmentMap.entrySet()) {
				Long reviewRequestId = entry.getKey();
				Integer appointmentId = entry.getValue();
				boolean isDuplicate = earliestAppointmentExists && !Objects.equals(appointmentId, earliestAppointment.getAppointmentId());
				reminderStatusMap.put(reviewRequestId, isDuplicate);
			}
			LOGGER.info("Duplicate reminder communication status retrieved for requests : {}", reminderStatusMap);
		}
		// Return the map of review request IDs to their duplicate status
		return reminderStatusMap;
	}
	
	/**
	 * Retrieves scheduled reminder events for the given customer.
	 *
	 * @param customerId The ID of the customer
	 * @return List of scheduled reminder events
	 */
	private List<CampaignEvent> getScheduledRemindersForAppointment(Integer customerId, String commType, Long requestId) {
		List<Long> reviewRequestIds = requestDao.findReviewRequestIdsByCustomerAndTypeAndDeliveryStatus(customerId, ReviewRequestTypeEnum.APPOINTMENT_REMINDER.getType(), RequestStatusEnum.INPROGRESS.getName(), commType);
		List<CampaignEvent> campaignEvents = campaignEventReadOnlyRepo.findByReviewRequestIdIn(reviewRequestIds);
		// Find the CampaignEvent corresponding to the current appointment request ID
		Optional<CampaignEvent> currentRequestEvent = campaignEvents.stream().filter(event -> Objects.equals(event.getReviewRequestId(), requestId)).findFirst();
		
		if(CollectionUtils.isEmpty(campaignEvents) || !currentRequestEvent.isPresent()) {
			LOGGER.info("No scheduled reminder events found or current request is not found in scheduled events for currentRequestId: {} and scheduled events {}", requestId, campaignEvents);
			return Collections.emptyList();
		}
		// Filter events based on scheduled time within 30 minute
		Iterator<CampaignEvent> iterator = campaignEvents.iterator();
	    while (iterator.hasNext()) {
	        CampaignEvent event = iterator.next();
	        long diffInMillies = Math.abs(event.getScheduledTime().getTime() - currentRequestEvent.get().getScheduledTime().getTime());
	        long diffInMinutes = diffInMillies / (60 * 1000); // Convert milliseconds to minutes

	        // If the difference is not within 30 minutes, remove the event from the list
	        if (diffInMinutes > 30) {
	            iterator.remove();
	        }
	    }
	    LOGGER.info("Filtered campaign reminder scheduled events within 30 minutes are : {}", campaignEvents);
	    return campaignEvents;
	}
	
	/**
	 * Retrieves a mapping of review request IDs to appointment IDs based on a list of campaign events.
	 *
	 * @param campaignEvents List of campaign events containing review request IDs.
	 * @return               A map where review request IDs are mapped to corresponding appointment IDs.
	 */
	private Map<Long, Integer> mapReviewRequestIdsToAppointmentIds(List<CampaignEvent> campaignEvents) {
	    if (CollectionUtils.isEmpty(campaignEvents)) {
	        return Collections.emptyMap();
	    }

	    List<Long> requestIds = campaignEvents.stream().map(CampaignEvent::getReviewRequestId).collect(Collectors.toList());

	    List<AppointmentRRMapping> appointmentRRMappings = appointmentReminderAuditService.getAppointmentRequestByReviewRequestIds(requestIds);

	    return appointmentRRMappings.stream().collect(Collectors.toMap(AppointmentRRMapping::getRequestRequestId, AppointmentRRMapping::getAppointmentId));
	}
	
	
	/**
	 * Fetches details of the earliest customer appointment based on appointment details.
	 *
	 * @param appointmentIds List of appointment details
	 * @return Details of the earliest customer appointment
	 */
	private AppointmentDetailsResponse fetchEarliestCustomerAppointmentDetails(List<AppointmentDetailsResponse> appointmentDetails) {
		if (CollectionUtils.isEmpty(appointmentDetails)) {
			return null;
		}
		if(appointmentDetails.size() == 1) {
			return appointmentDetails.get(0);
		}
		
		// Find appointment with the earliest start time
		return findAppointmentWithMinStartTime(appointmentDetails);
	}
	
	/**
	 * Finds the appointment with the earliest start time among the given appointments.
	 *
	 * @param appointmentDetails List of appointment details
	 * @return Appointment with the earliest start time
	 */
	private AppointmentDetailsResponse findAppointmentWithMinStartTime(List<AppointmentDetailsResponse> appointmentDetails) {
	    return appointmentDetails.stream().min(Comparator.comparingLong(AppointmentDetailsResponse::getStartTime)).orElse(null);
	}
	
	/**
     * Restricts reminders for specified accounts within a specified time period.
     *
     * This method checks existing restrictions for given account IDs and creates new
     * restrictions for accounts that are not already restricted.
     *
     * @param reminderRestrictionRequest The request containing account IDs and restrict window.
     */
	@Override
	public void updateReminderRestrictionsForAccounts(ReminderRestrictionRequest reminderRestrictionRequest) {
        List<Integer> accountIds = reminderRestrictionRequest.getAccountIds();
        Integer restrictWindow = reminderRestrictionRequest.getRestrictWindow();

        List<RestrictAppointmentCommunication> existingRestrictions = restrictAppointmentCommunicationRepo.findAllByEnterpriseIdIn(accountIds);

        // Extract IDs of accounts that are already restricted
		Set<Integer> existingRestrictedAccountIds = existingRestrictions.stream().map(RestrictAppointmentCommunication::getEnterpriseId)
				.collect(Collectors.toSet());
		
		// Identify new account IDs to be restricted (not already restricted)
        Set<Integer> newAccountIdsToRestrict = new HashSet<>(accountIds);
        newAccountIdsToRestrict.removeAll(existingRestrictedAccountIds);

        if(CollectionUtils.isEmpty(newAccountIdsToRestrict)) {
        	return;
        }
        
        // Backfill data for new locations to ensure their appointment reminder campaigns
        // are aligned with new the prioritized reminder flow. This ensures that new locations 
        // follow the updated logic for sending reminders based on priority.
		List<Campaign> campaigns = campaignRepo.getCampaignsByEnterpriseIdInAndTypeAndRunType(new ArrayList<>(newAccountIdsToRestrict), "appointment_reminder", "ongoing");
		if(CollectionUtils.isNotEmpty(campaigns)) {
			for(Campaign campaign : campaigns) {
				appointmentBackfillService.appointmentBackfillEvent(new AppointmentBackfillEvent(campaign.getEnterpriseId(), campaign.getId(), 
						campaign.getCampaignType(), AppointmentBackfillEventStatusEnum.INIT, 0, null));
			}	
		}
        
        // Create new restriction entities for accounts that are not already restricted
		List<RestrictAppointmentCommunication> newRestrictions = newAccountIdsToRestrict.stream().map(accountId -> {
			RestrictAppointmentCommunication newRestriction = new RestrictAppointmentCommunication();
			newRestriction.setEnterpriseId(accountId);
			newRestriction.setRestrictWindow(restrictWindow);
			return newRestriction;
		}).collect(Collectors.toList());
		restrictAppointmentCommunicationRepo.saveAll(newRestrictions);
    }
	
	@Override
	public void validateManualAppointmentReminderExecutionRequest(Integer appointmentId, Integer campaignId, Integer enterpriseId) {
		AppointmentInfoLiteDTO appointmentInfo = appointmentService.getAppointmentLiteById(appointmentId.toString(), enterpriseId, true, false);
		if (appointmentInfo == null) {
			throw new CampaignException(ErrorCodes.NO_VALID_APOINTMENT_FOUND, ErrorCodes.NO_VALID_APOINTMENT_FOUND.getMessage());
		}
		
		if (!AppointmentReminderUtils.isValidAppointmentStatusForExecution(appointmentInfo.getAppointmentStatus())) {
			LOGGER.info("Invalid status found for execution for appointment {}, campaign {}", appointmentId, campaignId);
			throw new CampaignException(ErrorCodes.INVALID_APPOINTMENT_STATUS, ErrorCodes.INVALID_APPOINTMENT_STATUS.getMessage());
		}
	}
	
	private Map<Long, Integer> mapReviewRequestIdsToCampaignIds(List<CampaignEvent> campaignEvents) {
	    if (CollectionUtils.isEmpty(campaignEvents)) {
	        return Collections.emptyMap();
	    }
	    return campaignEvents.stream().collect(Collectors.toMap(CampaignEvent::getReviewRequestId, CampaignEvent::getCampaignId));
	}
	
	private Map<Integer, AppointmentDetailsResponse> mapAppointmentIdsToAppointmentDetail(List<AppointmentDetailsResponse> appointmentDetails) {
	    if (CollectionUtils.isEmpty(appointmentDetails)) {
	        return Collections.emptyMap();
	    }
		return appointmentDetails.stream().collect(Collectors.toMap(AppointmentDetailsResponse::getAppointmentId, appointmentDetail -> appointmentDetail));
	}
	
	private int changeAppointmentTimeInAppointmentDTO(Long startTime, Integer businessId) {
		String timeZoneId = businessService.getBusinessTimezoneId(businessId);
		return epochTo24HourTime(startTime, timeZoneId);
	}
	
	private int epochTo24HourTime(long epochTime, String timeZone) {
		LocalDateTime dateTime = Instant.ofEpochMilli(epochTime).atZone(ZoneId.of(timeZone)).toLocalDateTime();
		String formattedTime = dateTime.format(DateTimeFormatter.ofPattern("HHmm"));
		int intTime = Integer.parseInt(formattedTime);
		return intTime;
	}
	
	public boolean validateAppointmentReminderRequest(AppointmentDetailsResponse appointment, Integer campaignId, Integer enterpriseId, BusinessEnterpriseEntity business, List<Date> appointmentScheduledDates, int appointmentId) {
		if(appointment == null) {
			LOGGER.info("No response From Appointment for appointmentId : {}", appointmentId);
			return false;
		}
		AppointmentInfoLiteDTO appointmentInfo = AppointmentExternalUtils.getAppointmentLiteDTO(appointment, changeAppointmentTimeInAppointmentDTO(appointment.getStartTime(), business.getId()), business.getTimezoneId());
		
		if (!AppointmentReminderUtils.isValidAppointmentStatusForExecution(appointmentInfo.getAppointmentStatus())) {
			LOGGER.info("No Valid appointment status for appointment reminder for appointmentId : {} and appointment status : {}", appointmentId, appointmentInfo.getAppointmentStatus());
			return false;
		}
		
		// validate MVEL expression
		List<TriggerFilter> triggerFilters = getTriggerFilters(appointmentInfo, CampaignTriggerTypeEnum.BEFORE_APPOINTMENT_DATE.getType());
		CampaignCondition campaignCondition = campaignSetupCachingService.getCampaignConditionByCampaign(campaignId, enterpriseId);
		if (!MvelEvaluationUtils.evaluateTriggerMvelExpression(campaignCondition.getTriggerMvelExpression(), campaignCondition.getTriggerMvelParamsAndTypes(), triggerFilters, false, null)) {
			LOGGER.info("Campaign Condition not matched for appointmentId : {} and campaignId : {}", appointmentId, campaignId);
			return false;
		}
		
		// check if current time is valid to execute request	
		if (BooleanUtils.isFalse(AppointmentReminderUtils.isValidExecutionTimeRequest(appointmentInfo, business, campaignCondition, appointmentScheduledDates))) {
			LOGGER.info("Scheduling config not matched for appointmentId : {} and campaignId : {}", appointmentId, campaignId);
			return false;
		}
		return true;
	}
	
	public boolean validateAppointmentReminderCampaign(Integer campaignId) {
		Campaign campaign = campaignSetupCachingService.getCampaignById(campaignId);
		if (campaign == null) {
			return false;
		} else if (campaign.getIsDeleted() == 1) {
			LOGGER.info("Camapign is deleted for campaignID: {}", campaignId);
			return false;
		} else if (CampaignStatusEnum.PAUSED.getStatus() == campaign.getStatus()) {
			LOGGER.info("Camapign is paused for campaignID: {}", campaignId);
			return false;
		} else if (CampaignStatusEnum.STOPPED.getStatus() == campaign.getStatus()) {
			LOGGER.info("Camapign is stopped for campaignID: {}", campaignId);
			return false;
		}
		return true;
	}
	
	/**
	 * Validates and filters appointment reminder requests based on specified criteria.
	 * <p>
	 * This method iterates through a list of scheduled reminders and performs validation checks for each reminder. If a reminder does not pass validation, it is removed from the relevant maps.
	 * </p>
	 *
	 * @param scheduledReminders List of scheduled campaign events (reminders) to be validated.
	 * @param requestAppointmentMap A map where the key is the review request ID and the value is the appointment ID.
	 * @param appointmentIdAppointmentDetailMap A map where the key is the appointment ID and the value is the appointment details response.
	 * @param requestCampaignIdMap A map where the key is the review request ID and the value is the campaign ID.
	 * @param business The business enterprise entity containing enterprise and business information used for validation.
	 */
	public void validateAndFilterAppointmentReminderRequests(List<CampaignEvent> scheduledReminders, Map<Long, Integer> requestAppointmentMap,
			Map<Integer, AppointmentDetailsResponse> appointmentIdAppointmentDetailMap, Map<Long, Integer> requestCampaignIdMap, BusinessEnterpriseEntity business) {
		if (CollectionUtils.isNotEmpty(scheduledReminders)) {
			for (CampaignEvent event : scheduledReminders) {
				int appointmentId = requestAppointmentMap.get(event.getReviewRequestId());
				if (!validateAppointmentReminderRequest(appointmentIdAppointmentDetailMap.get(appointmentId), requestCampaignIdMap.get(event.getReviewRequestId()),
						business.getEnterpriseIdElseBusinessId(), business, new ArrayList<>(), appointmentId)
						|| !validateAppointmentReminderCampaign(requestCampaignIdMap.get(event.getReviewRequestId()))) {
					LOGGER.info("Appointment/Campaign validation failed for RequestId : {}", event.getReviewRequestId());
					requestAppointmentMap.remove(event.getReviewRequestId());
				}
			}
			
			Iterator<Map.Entry<Integer, AppointmentDetailsResponse>> iterator = appointmentIdAppointmentDetailMap.entrySet().iterator();

			while (iterator.hasNext()) {
			    Map.Entry<Integer, AppointmentDetailsResponse> entry = iterator.next();
			    Integer appointmentId = entry.getKey();

			    // If the appointmentId is not found in requestAppointmentMap, remove it
			    if (!requestAppointmentMap.containsValue(appointmentId)) {
			        iterator.remove();
			    }
			}
			
		}
	}
	
	/**
	 * Retrieves a list of account IDs that have restrictions on appointment communication.
	 *
	 * @return  containing the list of restricted account IDs, or null if no restrictions are found.
	 */
	@Override
	public AppointmentCommRestrictionResponse getRestrictedAccountsForAppointmentCommunication() {
		Set<Integer> accountIds = CacheManager.getInstance().getCache(RestrictAppointmentCommunicationCache.class).getAppointmentCommRestrictedAccounts();
		
		return new AppointmentCommRestrictionResponse(accountIds);	
	}
}
