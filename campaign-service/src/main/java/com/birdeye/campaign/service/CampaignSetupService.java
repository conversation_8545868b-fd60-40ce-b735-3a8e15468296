package com.birdeye.campaign.service;

import java.util.List;

import com.birdeye.campaign.dto.BusinessAllCampaignsMessage;
import com.birdeye.campaign.dto.CampaignContactInfo;
import com.birdeye.campaign.dto.CampaignInfoDTO;
import com.birdeye.campaign.dto.CampaignMetadata;
import com.birdeye.campaign.platform.constant.CampaignStatusEnum;
import com.birdeye.campaign.request.AutomationCampaignRequest;
import com.birdeye.campaign.request.CampaignFilterRequest;
import com.birdeye.campaign.request.CampaignReminderTypeMessage;
import com.birdeye.campaign.request.CampaignUpdateStatusRequest;
import com.birdeye.campaign.request.CreateManualCampaignRequest;
import com.birdeye.campaign.request.CreateMessengerCampaignRequest;
import com.birdeye.campaign.request.CustomerDeleteEventRequest;
import com.birdeye.campaign.response.CampaignAccountSettingsResponse;
import com.birdeye.campaign.response.CampaignCountsResponse;
import com.birdeye.campaign.response.CreateCampaignResponse;
import com.birdeye.campaign.response.ManualCampaignResponse;
import com.birdeye.campaign.response.ManualEditCampaignResponse;
import com.birdeye.campaign.response.OngoingEditCampaignResponse;
import com.birdeye.campaign.response.ReviewRequestResponseWrapper;
import com.birdeye.template.dto.TemplateEnterpriseConfigUpdateMessage;

/**
 * Service to handle campaign specific requests
 * <AUTHOR>
 *
 */
public interface CampaignSetupService {

	/**
	 * Return Paginated campaign list for enterprise
	 * 
	 * @param enterpriseId
	 * @param userId
	 * @param campaignFilterSearchRequest
	 * @return
	 */
	public BusinessAllCampaignsMessage getAllCampaignForEnterprise(Integer enterpriseId, Integer userId, CampaignFilterRequest campaignFilterRequest);
	
	/**
	 * Create and Launch Manual Campaign
	 * @param campaignId 
	 * @param campaignRequest
	 * @param enterpriseId
	 * @param userId
	 */
	public CreateCampaignResponse createOrUpdateManualCampaign(Integer campaignId, CreateManualCampaignRequest campaignRequest, Integer enterpriseId, String userId);
	
	Integer createDefaultOnGoingCampaign(Integer businessId, String businessName,Integer userId,Integer emailTemplateId,Integer smsTemplateId, CampaignStatusEnum status, String priority);

	/**
	 * View Details api for manual campaign.
	 * 
	 * @param filterCriteria
	 * @return
	 */
	public ManualCampaignResponse getManualCampaign(Integer enterpriseId, Integer campaignId);
	
	void updateCampaignStatus(Integer status, Integer campaignId);

	public CampaignCountsResponse getEnterpriseCampaignsCount(Integer enterpriseId);
	
	Boolean getDripCampaignFlag(Integer accountId);
	
	public void updateReminderFlag(CampaignReminderTypeMessage campaignReminderTypeMessage);

	public void updateContactUsEnabledFlagForTemplates(Integer id, Integer id2, Integer contactUsEnabled);

	public void createOnGoingCampaignForMigration(Integer enterpriseId, Integer userId, Integer emailTemplateId, Integer smsTemplateId, CampaignStatusEnum status, String priority,
			String campaignType, Integer surveyId, List<String> lvlIds, String campaignNameSuffix);

	void updateUnsubscribeTextEnabledFlagForTemplates(Integer smsTemplateId, Integer enableUnsubscribeText);
	

	public ReviewRequestResponseWrapper getCommunicationRestrictionReport(Integer businessId);


	public Boolean updateDeleteStatus(Integer campaignId,Integer deleteFlag);

	void updateCampaignStatus();

	void updateLocationBrandingEnabledFlagForTemplates(Integer emailTemplateId, Integer smsTemplateId, Integer enableLocationBranding);

	void updateMmsEnabledFlagForTemplates(Integer smsTemplateId, Integer enableMms);

	void updateNoReplyEnabledFlagForTemplates(Integer emailTemplateId, Integer noReplyEnabled);
	
	CreateCampaignResponse createMessengerCampaign(CreateMessengerCampaignRequest campaignRequest, Integer enterpriseId, String userId);

	public ManualEditCampaignResponse getEditManualCampaign(Integer enterpriseId, Integer campaignId);

	public void deleteCampaignCustomer(CustomerDeleteEventRequest request);
	
	public void deleteCidsAndUpdateCampaign(List<CampaignContactInfo> campaignContactList, Integer cid);
	
	public OngoingEditCampaignResponse getEditOngoingCampaign(Integer enterpriseId, Integer campaignId);

	public Boolean updateDeleteStatusForResellerCampaign(Integer campaignId, String type, Integer deleteFlag);

	public Integer createDefaultOnGoingCampaignForReseller(Integer enterpriseId, Integer userId, Integer emailTemplateId, Integer smsTemplateId, String priority, Integer resellerId);

	AutomationCampaignRequest getDefaultCampaignForReseller(Integer resellerId, String type);

	CampaignMetadata getCampaignMetadataByCampaignId(Integer campaignId);

	CampaignInfoDTO getCampaignDetailsById(Integer campaignId);

	void updateCommRestrictionConfig(Integer enterpriseId, TemplateEnterpriseConfigUpdateMessage templateEnterpriseConfigUpdateMessage);

	CampaignAccountSettingsResponse getCommRestrictionConfigById(Integer enterpriseId);

	/**
	 * 
	 * BIRD-53931 | API to pause and delete manual/automated campaigns.
	 * Used to mark those running campaigns as paused and deleted, whose feature flag for the given campaign type is disabled at account level.
	 * 
	 * @param request
	 * 
	 */
	void markCampaignPausedAndDeleted(CampaignUpdateStatusRequest request);

	/**
	 * Method To Create Manual Campaign For Sending Bulk Messages To Appointments.
	 * Allowed communication types: promotion only
	 * Allowed priority types: ["email_sms", "sms_email", "sms", "email", "email_and_sms"]
	 * 
	 * @param createCampaignRequest
	 * @param enterpriseId
	 * @param userId
	 * @return
	 */
	public CreateCampaignResponse createCampaignForBulkMessagesToAppointments(CreateMessengerCampaignRequest createCampaignRequest, Integer enterpriseId, String userId);
	
	void evictOngoingCampaignsListCacheForEnterprise(Integer enterpriseId);

}
