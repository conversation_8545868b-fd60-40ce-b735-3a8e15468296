package com.birdeye.campaign.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.aspect.annotation.Profiled;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.dto.BaseUsageReportResponse;
import com.birdeye.campaign.dto.BusinessAllCampaignsMessage;
import com.birdeye.campaign.dto.BusinessCampaignMessage;
import com.birdeye.campaign.dto.DoupDownloadResponse;
import com.birdeye.campaign.dto.UsageFunnelReportResponse;
import com.birdeye.campaign.dto.ViewRecipientsCommunicationDTO;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.enums.CampaignRunTypeEnum;
import com.birdeye.campaign.exception.CampaignBadRequestException;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.platform.constant.CampaignStatusEnum;
import com.birdeye.campaign.platform.constant.CampaignTypeEnum;
import com.birdeye.campaign.report.UsageReportsService;
import com.birdeye.campaign.repository.CampaignRepo;
import com.birdeye.campaign.request.CampaignFilterRequest;
import com.birdeye.campaign.request.CampaignsFilterCriteria;
import com.birdeye.campaign.request.viewrecipients.ViewRecipientsRequest;
import com.birdeye.campaign.response.AutomationCampaignResponse;
import com.birdeye.campaign.response.CampaignDetailResponse;
import com.birdeye.campaign.response.CampaignExternalResponse;
import com.birdeye.campaign.response.ManualCampaignResponse;
import com.birdeye.campaign.response.ViewRecipientsCommunicationResponse;
import com.birdeye.campaign.response.ViewRecipientsCommunicationResponse.ViewRecipientsCommunication;
import com.birdeye.campaign.service.AutomationCampaignSetupService;
import com.birdeye.campaign.service.CampaignExternalService;
import com.birdeye.campaign.service.CampaignSetupCachingService;
import com.birdeye.campaign.service.CampaignSetupService;
import com.birdeye.campaign.service.viewrecipients.IViewRecipientsService;
import com.birdeye.campaign.service.viewrecipients.impl.ViewRecipientsUtils;
import com.birdeye.campaign.utils.CampaignUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service("campaignExternalService")
public class CampaignExternalServiceImpl implements CampaignExternalService {

    @Autowired
    private CampaignSetupService campaignSetupService;

    @Autowired
    private CampaignRepo campaignRepo;

    @Autowired
    private UsageReportsService usageReportsService;
    
    @Autowired
	private CampaignSetupCachingService		campaignSetupCachingService;
    
    @Autowired
	private AutomationCampaignSetupService 	automationCampaignSetupService;
    
	@Autowired
	private IViewRecipientsService			viewRecipientsService;
	
    
    private static final ObjectMapper	OBJECT_MAPPER	= new ObjectMapper();
	
	static {
		OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
	}

    private static final Logger logger	= LoggerFactory.getLogger(CampaignExternalServiceImpl.class);

    @Override
    public CampaignExternalResponse getAllCampaignForEnterprise(Integer enterpriseId, CampaignFilterRequest campaignFilterRequest) {
		BusinessAllCampaignsMessage businessAllCampaignsMessage = campaignSetupService.getAllCampaignForEnterprise(enterpriseId, null, campaignFilterRequest);
        CampaignExternalResponse campaignExternalResponse = new CampaignExternalResponse();
        if(Objects.isNull(businessAllCampaignsMessage)){
            return campaignExternalResponse;
        }
        setDraftStatus(businessAllCampaignsMessage.getData());
        setCampaignData(businessAllCampaignsMessage.getData(),campaignExternalResponse);
        return campaignExternalResponse;
    }

    @Override
    public UsageFunnelReportResponse getCampaignReports(CampaignsFilterCriteria request) {
        request.setIsDownload(true);
        request.setSkipEnterpriseResult(true);
        request.setExternalReport(true);
        if(request.getCampaignId()== null || request.getCampaignId()==0){
            logger.warn("Campaign Id is not present for request {}",request);
            throw new CampaignBadRequestException(ErrorCodes.NO_REFERRAL_CODE_TO_MIGRATE);
        }
        Campaign campaign = campaignRepo.getById(request.getCampaignId());
		if(Objects.isNull(campaign)){
			logger.warn("Campaign is not present for this request enterprise id {} and campaign Id : {} ",request.getEnterpriseId(), request.getCampaignId()
			);
			throw new CampaignBadRequestException(ErrorCodes.INVALID_CAMPAIGN, "Campaign not found");
		}
		CampaignUtils.isValidCampaignDetailExternalRequest(request.getEnterpriseId(), campaign);

        String type = campaign.getCampaignType();

		switch (CampaignTypeEnum.getSupportedType(type)) {
			case REVIEW_REQUEST:
				return removeBusinessIdFromResponse(usageReportsService.getRRUsageTableReport(request));
			case CX_REQUEST:
				return removeBusinessIdFromResponse(usageReportsService.getCXUsageTableReport(request));
			case PROMOTIONAL:
				return removeBusinessIdFromResponse(usageReportsService.getCustomUsageTableReport(request));
			case REFERRAL:
				return removeBusinessIdFromResponse(usageReportsService.getReferralUsageTableReport(request));
			case APPOINTMENT_REMINDER:
				return removeBusinessIdFromResponse(usageReportsService.getAppointmentReminderUsageTableReport(request));
			case APPOINTMENT_RECALL:
				return removeBusinessIdFromResponse(usageReportsService.getAppointmentRecallUsageTableReport(request));
			case APPOINTMENT_FORM:
				return removeBusinessIdFromResponse(usageReportsService.getAppointmentFormUsageTableReport(request));
			default:
				return null;
		}

    }
    
    // Remove businessId from response as it shouldn't be exposed publically.
    private UsageFunnelReportResponse removeBusinessIdFromResponse(UsageFunnelReportResponse usageFunnelReportResponse){
        logger.info("Usage funnel report external response : {}",usageFunnelReportResponse);
        if (CollectionUtils.isEmpty(usageFunnelReportResponse.getUsageReportResponses())) {
        	return usageFunnelReportResponse;
        }
        for (BaseUsageReportResponse baseResp : usageFunnelReportResponse.getUsageReportResponses()) {
        	baseResp.setBusinessId(null);
        }
        return usageFunnelReportResponse;
    }
    
	private void setDraftStatus(List<BusinessCampaignMessage> businessCampaignMessageList) {
		if (CollectionUtils.isNotEmpty(businessCampaignMessageList)) {
			for (BusinessCampaignMessage businessAllCampaignsMessage : businessCampaignMessageList) {
				if (businessAllCampaignsMessage.getStatusId() == 6) {
					businessAllCampaignsMessage.setStatus("Draft");
				}
			}
		}
	}


    private void setCampaignData(List<BusinessCampaignMessage> businessCampaignMessageList, CampaignExternalResponse campaignExternalResponse){
        if(Objects.isNull(businessCampaignMessageList)){
            logger.info("Empty response in businessCampaignMessageList");
            return;
        }
        for(BusinessCampaignMessage message: businessCampaignMessageList){
            CampaignExternalResponse.CampaignMessage campaignMessage = new CampaignExternalResponse.CampaignMessage();
            campaignMessage.setId(message.getId());
            campaignMessage.setName(message.getName());
            campaignMessage.setType(message.getType());
            campaignMessage.setScheduledDt(message.getScheduledDt());
            campaignMessage.setStatus(message.getStatus());
            campaignMessage.setRunType(message.getRunType());
            campaignMessage.setSurveyId(message.getSurveyId());
            campaignMessage.setPriority(message.getPriority());
            campaignMessage.setScheduleType(message.getScheduleType());
            campaignMessage.setIsFreeTextCampaign(message.getIsFreeTextCampaign());
            campaignMessage.setIsMessengerCampaign(message.getIsMessengerCampaign());
            setUsageStats(campaignMessage,message);
            campaignExternalResponse.getData().add(campaignMessage);
        }
    }
    
	private void setUsageStats(CampaignExternalResponse.CampaignMessage campaignMessage, BusinessCampaignMessage message){
		campaignMessage.getUsageStats().setClickedCount(message.getUsageStats().getClickedCompletedCount());
		campaignMessage.getUsageStats().setClickedRate(message.getUsageStats().getClickedCompletedPercent());
		campaignMessage.getUsageStats().setOpenCount(message.getUsageStats().getOpenStartedCount());
		campaignMessage.getUsageStats().setOpenRate(message.getUsageStats().getOpenStartedPercent());
		campaignMessage.getUsageStats().setDelivered(message.getUsageStats().getDelivered());
		campaignMessage.getUsageStats().setSentSuccess(message.getUsageStats().getSentSuccess());
	}
	
    /**
     * Retrieves campaign details based on the provided enterprise ID and campaign ID.
     *
     * @param enterpriseId The ID of the enterprise associated with the campaign.
     * @param campaignId The ID of the campaign whose details are to be retrieved.
     * @return A {@link CampaignDetailResponse} object containing the campaign details.
     * @throws CampaignException If the campaign ID is null or zero, {@link ErrorCodes#INVALID_CAMPAIGN} is thrown.
     */
    @Override
    public CampaignDetailResponse getCampaignDetailsByCampaignId(Integer enterpriseId, Integer campaignId) {
    	if(Objects.isNull(campaignId) || campaignId == 0) {
    		throw new CampaignBadRequestException(ErrorCodes.INVALID_CAMPAIGN, ErrorCodes.INVALID_CAMPAIGN.getMessage());
		}
    	Campaign campaign = campaignSetupCachingService.getCampaignById(campaignId);
    	
    	CampaignUtils.isValidCampaignDetailExternalRequest(enterpriseId, campaign);
    	
    	String runType = campaign.getRunType();
		if(StringUtils.equalsIgnoreCase(runType, CampaignRunTypeEnum.ONGOING.getRunType())) {
			return getAutomationCampaignDetails(enterpriseId, campaignId);
		} else if(StringUtils.equalsIgnoreCase(runType, CampaignRunTypeEnum.MANUAL.getRunType())){
			return getManualCampaignDetails(enterpriseId, campaignId);
		}
    	return new CampaignDetailResponse();
    }
    
	private CampaignDetailResponse getAutomationCampaignDetails(Integer enterpriseId, Integer campaignId) {
		AutomationCampaignResponse automationCampaign = automationCampaignSetupService.getAutomationCampaign(enterpriseId, campaignId, null);
		CampaignDetailResponse campaignDetails = new CampaignDetailResponse();
		if (automationCampaign != null) {
			campaignDetails.setCampaignId(automationCampaign.getId());
			campaignDetails.setCampaignType(automationCampaign.getCampaignType());
			campaignDetails.setCampaignName(automationCampaign.getCampaignName());
			campaignDetails.setSource(automationCampaign.getPriority());
			campaignDetails.setSurveyName(automationCampaign.getSurveyName());
			campaignDetails.setEmailTemplate(automationCampaign.getEmailTemplate());
			campaignDetails.setSmsTemplate(automationCampaign.getSmsTemplate());
			campaignDetails.setCampaignRunType(CampaignRunTypeEnum.ONGOING.getLabel());
			campaignDetails.setCampaignStatus(CampaignStatusEnum.getStatusAlias(automationCampaign.getStatusId()));
			campaignDetails.setCreatedOn(automationCampaign.getCreatedOn());
			campaignDetails.setCreatedBy(automationCampaign.getCreatedBy());
			campaignDetails.setTriggerType(automationCampaign.getTriggerType());
			campaignDetails.setUsageInfo(automationCampaign.getUsageInfo());
			if (automationCampaign.getEditInfo() != null) {
				campaignDetails.setEditedBy(automationCampaign.getEditInfo().getEditedBy());
				campaignDetails.setEditedOn(automationCampaign.getEditInfo().getEditedOn());
			}
			
		}
		return campaignDetails;
	}
	
	private CampaignDetailResponse getManualCampaignDetails(Integer enterpriseId, Integer campaignId) {
		ManualCampaignResponse manualCampaign = campaignSetupService.getManualCampaign(enterpriseId, campaignId);
		CampaignDetailResponse campaignDetails = new CampaignDetailResponse();
		if (manualCampaign != null) {
			campaignDetails.setCampaignId(manualCampaign.getId());
			campaignDetails.setCampaignType(manualCampaign.getCampaignType());
			campaignDetails.setCampaignName(manualCampaign.getCampaignName());
			campaignDetails.setSource(manualCampaign.getPriority());
			campaignDetails.setSurveyName(manualCampaign.getSurveyName());
			campaignDetails.setEmailTemplate(manualCampaign.getEmailTemplate());
			campaignDetails.setSmsTemplate(manualCampaign.getSmsTemplate());
			campaignDetails.setCampaignRunType(CampaignRunTypeEnum.MANUAL.getLabel());
			campaignDetails.setCampaignStatus(CampaignStatusEnum.getStatusAlias(manualCampaign.getStatusId()));
			campaignDetails.setCreatedOn(manualCampaign.getCreatedOn());
			campaignDetails.setCreatedBy(manualCampaign.getCreatedBy());
			campaignDetails.setTriggerType(StringUtils.EMPTY);
			campaignDetails.setUsageInfo(manualCampaign.getUsageInfo());
			if (manualCampaign.getEditInfo() != null) {
				campaignDetails.setEditedBy(manualCampaign.getEditInfo().getEditedBy());
				campaignDetails.setEditedOn(manualCampaign.getEditInfo().getEditedOn());
			}
			
		}
		return campaignDetails;
	}
	
	@Override
	public ViewRecipientsCommunicationResponse getViewRecipientsForCampaign(ViewRecipientsRequest request, Map<String, Object> requestParams) {
		if (!ViewRecipientsUtils.isValidExternalViewRecipientsRequest(request, requestParams)) {
			logger.error("Invalid View Recipients Request :: {}", request);
			return new ViewRecipientsCommunicationResponse();
		}
		Campaign campaign = campaignSetupCachingService.getCampaignById(request.getCampaignId());
		
		CampaignUtils.isValidCampaignDetailExternalRequest(request.getEnterpriseId(), campaign);

		ViewRecipientsCommunicationResponse response = new ViewRecipientsCommunicationResponse();
		DoupDownloadResponse<ViewRecipientsCommunicationDTO> recipients = viewRecipientsService.getViewRecipientsDownloadResponseForACampaign(request, requestParams, true);
		if(recipients == null || CollectionUtils.isEmpty(recipients.getData())) {
			return response;
		}
		return getRecipientDetails(recipients.getData());
	}
	
	private ViewRecipientsCommunicationResponse getRecipientDetails(List<ViewRecipientsCommunicationDTO> recipients) {
		ViewRecipientsCommunicationResponse recipientDetails = new ViewRecipientsCommunicationResponse();
		List<ViewRecipientsCommunication>	recipientList = new ArrayList<>();
		
		for(ViewRecipientsCommunicationDTO recipient : recipients) {
			ViewRecipientsCommunication recipientsInfo = new ViewRecipientsCommunication();
			recipientsInfo.setCustomerName(recipient.getCustomerName());
			recipientsInfo.setSource(recipient.getSource());
			recipientsInfo.setLocation(recipient.getLocation());
			recipientsInfo.setStatus(recipient.getStatus());
			recipientsInfo.setLastActivityDate(recipient.getLastActivityDate());
			recipientsInfo.setLastActivityDateWithTime(recipient.getLastActivityDateWithTime());
			recipientsInfo.setFailureReason(recipient.getFailureReason());
			recipientsInfo.setIsDND(recipient.getIsDND());
			recipientsInfo.setCustomerEmail(recipient.getCustomerEmail());
			recipientsInfo.setCustomerPhone(recipient.getCustomerPhone());
			recipientsInfo.setSpecialistName(recipient.getspecialistName());
			recipientsInfo.setAppointmentDateTime(recipient.getAppointmentDateTime());
			recipientsInfo.setBusinessNumber(recipient.getBusinessNumber());
			recipientsInfo.setRecallDueDate(recipient.getRecallDueDate());
			recipientsInfo.setRecallType(recipient.getRecallType());
			
			recipientList.add(recipientsInfo);
		}
		recipientDetails.setRecipients(recipientList);
		
		return recipientDetails;	
	}
	
}

