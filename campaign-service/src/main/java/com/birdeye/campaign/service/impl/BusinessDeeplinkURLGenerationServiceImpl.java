/**
 * @file_name BusinessDeeplinkURLGenerationServiceImpl.java
 * @created_date 20 Oct 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.service.impl;

import java.net.URLEncoder;
import java.util.List;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.ReviewRequestDto;
import com.birdeye.campaign.entity.Promotion;
import com.birdeye.campaign.entity.ReviewRequest;
import com.birdeye.campaign.external.service.BusinessExternalService;
import com.birdeye.campaign.service.BusinessDeeplinkURLGenerationService;
import com.birdeye.campaign.service.BusinessOptionService;
import com.birdeye.campaign.utils.CampaignUtils;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.EncryptionUtils;

/**
 * @file_name BusinessDeeplinkURLGenerationServiceImpl.java
 * @created_date 20 Oct 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@Service("businessDeeplinkURLGenerationServiceImpl")
public class BusinessDeeplinkURLGenerationServiceImpl implements BusinessDeeplinkURLGenerationService {
	
	private static final Logger				LOGGER	= LoggerFactory.getLogger(BusinessDeeplinkURLGenerationServiceImpl.class);

	@Autowired
	private BusinessOptionService	businessOptionService;
	
	@Autowired
	private BusinessExternalService businessExternalService;
	
	@Override
	public String getDeepLinkForSMS(BusinessEnterpriseEntity business, ReviewRequest reviewRequest) throws Exception {
		String url = getMobileDeepLinkUrl(business, reviewRequest);
		String shortenUrl = businessExternalService.shortenUrl(url, CoreUtils.getSmbOrEnterpriseId(business));
		LOGGER.info("Original deeplink url: {}, shorten url: {}", url, shortenUrl);
		return shortenUrl;
	}

	@Override
	public String getDeepLinkForSMSWithException(BusinessEnterpriseEntity business, ReviewRequest reviewRequest) throws Exception {
		String url = getMobileDeepLinkUrl(business, reviewRequest);
		String shortenUrl = businessExternalService.shortenUrlWithException(url, CoreUtils.getSmbOrEnterpriseId(business));
		LOGGER.info("Original deeplink url: {}, shorten url: {}", url, shortenUrl);
		return shortenUrl;
	}
	
	@Override
	public String getReferralDeepLinkForSMS(BusinessEnterpriseEntity business, ReviewRequest reviewRequest) throws Exception {
		String url = getMobileReferralDeepLinkUrl(business, reviewRequest);
		String shortenUrl = businessExternalService.shortenUrl(url, CoreUtils.getSmbOrEnterpriseId(business));
		LOGGER.info("Original deeplink url: {}, shorten url: {}", url, shortenUrl);
		return shortenUrl;
	}

	@Override
	public String getReferralDeepLinkForSMSWithException(BusinessEnterpriseEntity business, ReviewRequest reviewRequest) {
		String url = getMobileReferralDeepLinkUrl(business, reviewRequest);
		String shortenUrl = businessExternalService.shortenUrlWithException(url, CoreUtils.getSmbOrEnterpriseId(business));
		LOGGER.info("Original deeplink url: {}, shorten url: {}", url, shortenUrl);
		return shortenUrl;
	}

	@Override
	@Cacheable(value = "getEncryptedCustomerIdCache", unless = "#result == null", key = "#reviewRequestId.toString().concat('-').concat(#customerId.toString())")
	public String getEncryptedCustomerId(Long reviewRequestId, Integer customerId) {
		if (CacheManager.getInstance().getCache(SystemPropertiesCache.class).getBooleanProperty(Constants.DEEPLINK_CUSTOMER_ID_ENCRYPTION, true)) {
			try {
				LOGGER.info("encoding deeplink url customer id {} for request id {}", customerId, reviewRequestId);
				return URLEncoder.encode(EncryptionUtils.encrypt(String.valueOf(customerId), getEncryptionKey(reviewRequestId, customerId),
						getEncryptionIv(customerId, reviewRequestId)), "UTF-8");
			} catch (Exception e) {
				LOGGER.error("error while encrypting deeplink url customer id {} and review request id {} : {}", customerId, reviewRequestId, ExceptionUtils.getStackTrace(e));
			}
		}
		return String.valueOf(customerId);
	}
	
	
	/**
	 * prepare survey link for a request to send out to a customer this is a dynamic link, as it is different for survey-request link is location specific
	 * (survey response will be tagged to location in the link), link is customer specific (review_request_id in the url) link has source info (email/sms)
	 * for sms shortened link will be returned
	 */
	@Override
	public String getSurveyLinkUrl(ReviewRequest reviewRequest, BusinessEnterpriseEntity business) throws Exception {
		String url = new StringBuilder().append(businessOptionService.getPublicProfileURL(business)).append(Constants.SURVEYLINK_SUBPATH).append("?surveyId=").append(reviewRequest.getSurveyId())
				.append("&requestId=").append(reviewRequest.getId()).append("&source=").append(reviewRequest.getSource()).toString();
		List<String> externalSources = CampaignUtils.getCampaignRequestExternalSourcesList();
		String shortenUrl = StringUtils.isNotEmpty(reviewRequest.getSource())
				&& (StringUtils.equalsIgnoreCase(reviewRequest.getSource(), "sms") || CampaignUtils.isStringValuePresentInList(reviewRequest.getSource(), externalSources))
						? businessExternalService.shortenUrl(url, CoreUtils.getSmbOrEnterpriseId(business))
						: url;
		LOGGER.info("Original url: {}, shorten url: {}", url, shortenUrl);
		return shortenUrl;
	}

	@Override
	public String getSurveyLinkUrlWithException(ReviewRequest reviewRequest, BusinessEnterpriseEntity business) throws Exception {
		String url = new StringBuilder().append(businessOptionService.getPublicProfileURL(business)).append(Constants.SURVEYLINK_SUBPATH).append("?surveyId=").append(reviewRequest.getSurveyId())
				.append("&requestId=").append(reviewRequest.getId()).append("&source=").append(reviewRequest.getSource()).toString();
		List<String> externalSources = CampaignUtils.getCampaignRequestExternalSourcesList();
		String shortenUrl = StringUtils.isNotEmpty(reviewRequest.getSource())
				&& (StringUtils.equalsIgnoreCase(reviewRequest.getSource(), "sms") || CampaignUtils.isStringValuePresentInList(reviewRequest.getSource(), externalSources))
				? businessExternalService.shortenUrlWithException(url, CoreUtils.getSmbOrEnterpriseId(business))
				: url;
		LOGGER.info("Original url: {}, shorten url: {}", url, shortenUrl);
		return shortenUrl;
	}
	
	/**
	 * DEEPLINK URL FOR MOBILE
	 * 
	 * @param business
	 * @param reviewRequest
	 * @return
	 */
	private String getMobileDeepLinkUrl(BusinessEnterpriseEntity business, ReviewRequest reviewRequest) {
		String encodedCustomerId = getEncryptedCustomerId(reviewRequest.getId(), reviewRequest.getCustId());
		LOGGER.info("Encrypting customer id for review request id {} and customer id {} is :: {}",reviewRequest.getId(), reviewRequest.getCustId(), encodedCustomerId);
		Integer encoded = 1;
		if (encodedCustomerId.equalsIgnoreCase(String.valueOf(reviewRequest.getCustId()))) {
			encoded = 0;
		}
		StringBuilder url = new StringBuilder().append(businessOptionService.getPublicProfileURL(business)).append("/")
				.append(Constants.DEEPLINK_SUBPATH_MOBILE_NEW).append("?rid=").append(reviewRequest.getId()).append("&source=").append(reviewRequest.getSource()).append("&rtype=")
				.append(CampaignUtils.getTemplateKey(reviewRequest)).append("&templateId=").append(reviewRequest.getTemplateId()).append("&custId=").append(encodedCustomerId);
		if (encoded == 1) {
			url.append("&enc=1");
		}
		return url.toString();
	}
	
	/**
	 * DEEPLINK URL FOR MOBILE
	 * 
	 * @param business
	 * @param reviewRequest
	 * @return
	 */
	@Override
	public String getMobileReferralDeepLinkUrl(BusinessEnterpriseEntity business, ReviewRequest reviewRequest) {
		String encodedCustomerId = getEncryptedCustomerId(reviewRequest.getId(), reviewRequest.getCustId());
		Integer encoded = 1;
		if (encodedCustomerId.equalsIgnoreCase(String.valueOf(reviewRequest.getCustId()))) {
			encoded = 0;
		}
		StringBuilder url = new StringBuilder().append(businessOptionService.getPublicProfileURL(business)).append("/")
				.append(Constants.DEEPLINK_REFERRAL_URL_SUFFIX).append("?rid=").append(reviewRequest.getId()).append("&source=").append(reviewRequest.getSource()).append("&rtype=")
				.append(CampaignUtils.getTemplateKey(reviewRequest)).append("&templateId=").append(reviewRequest.getTemplateId()).append("&custId=").append(encodedCustomerId);
		if (encoded == 1) {
			url.append("&enc=1");
		}
		return url.toString();
	}
	
	/**
	 * Base Redirection URL for Lead form - custom (only tmx specific)
	 * 
	 * @param business
	 * @param reviewRequest
	 * @return
	 */
	//@Override
	
	@Override
	public String getLeadFormRedirectionBaseURL(BusinessEnterpriseEntity business, ReviewRequestDto reviewRequest, Integer sid, String referralCode) {
		String encodedCustomerId = getEncryptedCustomerId(reviewRequest.getReviewRequestId(), reviewRequest.getCustomerId());
		Integer encoded = 1;
		if (encodedCustomerId.equalsIgnoreCase(String.valueOf(reviewRequest.getCustomerId()))) {
			encoded = 0;
		}
		StringBuilder url = new StringBuilder().append(businessOptionService.getPublicProfileURL(business)).append("/").append(Constants.REFERRAL_TRACKING_URL_SUFFIX).append("?rid=")
				.append(reviewRequest.getReviewRequestId()).append("&sid=").append(sid).append("&source=").append(reviewRequest.getSource()).append("&rtype=").append(CampaignUtils.getTemplateKey(reviewRequest))
				.append("&templateId=").append(reviewRequest.getTemplateId()).append("&custId=").append(encodedCustomerId).append("&referralCode=").append(referralCode);
		if (encoded == 1) {
			url.append("&enc=1");
		}
		return url.toString();
	}
	
	@Override
	public String getEncryptionKey(Long reviewRequestId, Integer customerId) {
		StringBuilder sb = new StringBuilder(StringUtils.join(reviewRequestId, customerId));
		while (sb.length() < 16) {
			sb.append(sb);
		}
		
		return sb.substring(0, 16);
	}
	
	@Override
	public String getEncryptionIv(Integer customerId, Long reviewRequestId) {
		StringBuilder sb = new StringBuilder(StringUtils.join(customerId, reviewRequestId));
		while (sb.length() < 16) {
			sb.append(sb);
		}
		
		return sb.substring(0, 16);
	}
	
	/**
	 * method to generate appointment details URL - cancel or reschedule button
	 * 
	 * demo URL - https://devpublicforms1.birdeye.com/{accountId}/view-appointment/details/{appointmentId}
	 * 
	 * @param business
	 * @param context
	 * @return
	 */
	@Override
	public String getAppointmentReminderDeeplinkForSMS(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, String encodedAppointmentId) {
		// Generate encrypted customer ID
		String encodedCustomerId = getEncryptedCustomerId(reviewRequest.getId(), reviewRequest.getCustId());
		// Construct the URL for the appointment reminder
		StringBuilder url = new StringBuilder().append(businessOptionService.getPublicProfileURL(business)).append("/remind-appointment").append("?rid=").append(reviewRequest.getId())
				.append("&appointmentId=").append(encodedAppointmentId).append("&rtype=").append(reviewRequest.getRequestType()).append("&templateId=").append(reviewRequest.getTemplateId())
				.append("&clickType=1").append("&custId=").append(encodedCustomerId).append("&enc=").append(1).append("&source=").append(reviewRequest.getSource());
		// Shorten the URL using an external service
		String shortenUrl = businessExternalService.shortenUrl(url.toString(), CoreUtils.getSmbOrEnterpriseId(business));
		LOGGER.info("Original deeplink url: {}, shorten url: {}", url, shortenUrl);
		return shortenUrl;
	}
	
	/**
	 * method to generate appointment recall URL - book button
	 * 
	 * 
	 * @param business
	 * @param reviewRequest
	 * @param encryptedaAppointmentId ,widgetId ,recallId
	 * @return String
	 */
	@Override
	public String getAppointmentRecallBookingURLForSMS(BusinessEnterpriseEntity business, Long smbOrEnterpriseBusinessNumber, ReviewRequest reviewRequest, String encryptedAppointmentId,
			String widgetId, Integer appointmentSchedulingEnabled) {
		String encodedCustomerId = getEncryptedCustomerId(reviewRequest.getId(), reviewRequest.getCustId());
		StringBuilder url = new StringBuilder().append(businessOptionService.getDomainBaseURLPrefixForBusiness(business)).append("/appointments/").append(smbOrEnterpriseBusinessNumber).append("/")
				.append(widgetId).append(getAppointmentId(encryptedAppointmentId)).append("?rid=").append(reviewRequest.getId()).append("&rtype=").append(reviewRequest.getRequestType())
				.append("&templateId=").append(reviewRequest.getTemplateId()).append("&clickType=1").append("&custId=")
				.append(encodedCustomerId).append("&enc=1");
		// Saving extra call to core service in case scheduling is not enabled, since shorten url is not supposed to be sent for scheduling off.
		// Not returning empty url due to 'empty shortenUrl' check in CampaignSmsServiceImpl.getRRSmsCampaignMessage which throws error if shorten url is empty.
		String shortenUrl = url.toString();
		if (BooleanUtils.isTrue(CoreUtils.getBooleanValueFromInteger(appointmentSchedulingEnabled))) {
			shortenUrl = businessExternalService.shortenUrl(url.toString(), CoreUtils.getSmbOrEnterpriseId(business));
		}
		LOGGER.info("Original deeplink url: {}, shorten url: {}", url, shortenUrl);
		return shortenUrl;
	}
	
	private String getAppointmentId(String encryptedAppointmentId) {
		if (StringUtils.isNotBlank(encryptedAppointmentId)) {
			return new StringBuilder().append("/").append(encryptedAppointmentId).toString();
		}
		return StringUtils.EMPTY;
	}
	
	/**
	 * method to generate promotional SMS URL - book appointment button
	 * 
	 * 
	 * @param business
	 * @param reviewRequest
	 * @param encryptedaAppointmentId
	 *            ,widgetId ,recallId
	 * @return String
	 */
	@Override
	public String getAppointmentBookingURLForPromotionalSMS(BusinessEnterpriseEntity business, Long smbOrEnterpriseBusinessNumber, Promotion promotion,
			String encryptedAppointmentId, String widgetId, Integer appointmentSchedulingEnabled) {
		String encodedCustomerId = getEncryptedCustomerId(promotion.getId(), promotion.getCustId());
		StringBuilder url = new StringBuilder().append(businessOptionService.getDomainBaseURLPrefixForBusiness(business)).append("/appointments/")
				.append(smbOrEnterpriseBusinessNumber).append("/").append(widgetId).append(getAppointmentId(encryptedAppointmentId)).append("?rid=").append(promotion.getId())
				.append("&rtype=").append(promotion.getRequestType()).append("&templateId=").append(promotion.getTemplateId()).append("&clickType=1").append("&custId=")
				.append(encodedCustomerId).append("&enc=1");
		// Saving extra call to core service in case scheduling is not enabled, since shorten url is not supposed to be sent for scheduling off.
		// Not returning empty url due to 'empty shortenUrl' check in CampaignSmsServiceImpl.getRRSmsCampaignMessage which throws error if shorten url is
		// empty.
		String shortenUrl = url.toString();
		if (BooleanUtils.isTrue(CoreUtils.getBooleanValueFromInteger(appointmentSchedulingEnabled))) {
			shortenUrl = businessExternalService.shortenUrl(url.toString(), CoreUtils.getSmbOrEnterpriseId(business));
		}
		LOGGER.info("Original deeplink url: {}, shorten url: {}", url, shortenUrl);
		return shortenUrl;
	}
	
}