package com.birdeye.campaign.service.impl;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.hibernate.StaleStateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.aerospike.redisclient.AerospikeJedisService;
import com.birdeye.campaign.aspect.annotation.Profiled;
import com.birdeye.campaign.audit.service.AppointmentReminderAuditService;
import com.birdeye.campaign.business.service.BusinessService;
import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.KafkaTopicCache;
import com.birdeye.campaign.cache.RestrictAppointmentCommunicationCache;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.communication.UsageCommunicationService;
import com.birdeye.campaign.communication.message.SurveyDataMessage;
import com.birdeye.campaign.constant.CommunicationRequestSourceEnum;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.constant.KafkaTopicTypeEnum;
import com.birdeye.campaign.constant.Operator;
import com.birdeye.campaign.customer.service.CustomerService;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.CreateLocationCustomerRequest;
import com.birdeye.campaign.dto.CustomFieldEvalRequest;
import com.birdeye.campaign.dto.ExternalReviewRequestDTO;
import com.birdeye.campaign.dto.KafkaMessage;
import com.birdeye.campaign.dto.MessengerEmailCampaignEvent;
import com.birdeye.campaign.dto.RetryRequestMetaInfo;
import com.birdeye.campaign.dto.ReviewRequestRetryDTO;
import com.birdeye.campaign.dto.RuleExpression;
import com.birdeye.campaign.dto.RunCampaignKafkaMessage;
import com.birdeye.campaign.entity.AccountCustomerCommCapping;
import com.birdeye.campaign.entity.AppointmentRRMapping;
import com.birdeye.campaign.entity.BusinessSmsTemplate;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.CampaignAccountSettings;
import com.birdeye.campaign.entity.CampaignCondition;
import com.birdeye.campaign.entity.CommunicationRequestSource;
import com.birdeye.campaign.entity.CustomerCommCapping;
import com.birdeye.campaign.entity.Promotion;
import com.birdeye.campaign.entity.ReviewRequest;
import com.birdeye.campaign.entity.SurveyCustomerCommCapping;
import com.birdeye.campaign.enums.CampaignRunTypeEnum;
import com.birdeye.campaign.exception.AppointmentReminderForChainingException;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.exception.InValidTimeZoneException;
import com.birdeye.campaign.exception.SmsUrlShortenException;
import com.birdeye.campaign.external.cache.service.CacheExternalService;
import com.birdeye.campaign.external.service.BusinessExternalService;
import com.birdeye.campaign.external.service.IContactExternalService;
import com.birdeye.campaign.external.service.SurveyExternalService;
import com.birdeye.campaign.kafka.service.KafkaService;
import com.birdeye.campaign.platform.constant.CampaignSchedulingTypeEnum;
import com.birdeye.campaign.platform.constant.CampaignStatusEnum;
import com.birdeye.campaign.platform.constant.CampaignTriggerTypeEnum;
import com.birdeye.campaign.platform.constant.CampaignTypeEnum;
import com.birdeye.campaign.platform.constant.CommunicationAcitivityEventEnum;
import com.birdeye.campaign.platform.constant.RequestStatusEnum;
import com.birdeye.campaign.platform.constant.TemplateTypeEnum;
import com.birdeye.campaign.platform.entity.BaseCommunicationEntity;
import com.birdeye.campaign.repository.AccountCustomerCommCappingRepo;
import com.birdeye.campaign.repository.CustomerCommCappingRepo;
import com.birdeye.campaign.repository.EmailTemplateRepo;
import com.birdeye.campaign.repository.PromotionRepo;
import com.birdeye.campaign.repository.ReviewRequestRepo;
import com.birdeye.campaign.repository.SurveyCustomerCommCappingRepo;
import com.birdeye.campaign.request.CampaignUpdateStatusRequest;
import com.birdeye.campaign.request.CreateReviewRequestMessage;
import com.birdeye.campaign.request.InboxEmailContentGetRequest;
import com.birdeye.campaign.request.InboxEmailRequestMessage;
import com.birdeye.campaign.request.Location;
import com.birdeye.campaign.request.OngoingRunCampaignMessage;
import com.birdeye.campaign.request.ProductFeatureRequest;
import com.birdeye.campaign.request.template.CampaignStatsKafkaMessage;
import com.birdeye.campaign.request.template.UpdateCampaignCustomerHistoryMessage;
import com.birdeye.campaign.request.template.UpdateCampaignCustomerRequest;
import com.birdeye.campaign.request.template.UpdateCustomerCommCappingRequest;
import com.birdeye.campaign.response.CreateCustomerCheckinResponse;
import com.birdeye.campaign.response.external.BusinessInfoResponseByReferenceId;
import com.birdeye.campaign.response.kontacto.KontactoDTO;
import com.birdeye.campaign.rule.utils.RuleEngine;
import com.birdeye.campaign.service.AppointmentFormService;
import com.birdeye.campaign.service.AppointmentRecallService;
import com.birdeye.campaign.service.AppointmentReminderService;
import com.birdeye.campaign.service.AppointmentTriggersService;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.CampaignEventManagementService;
import com.birdeye.campaign.service.CampaignReminderService;
import com.birdeye.campaign.service.CampaignRescheduleService;
import com.birdeye.campaign.service.CampaignReviewRequestService;
import com.birdeye.campaign.service.CampaignRunService;
import com.birdeye.campaign.service.CampaignServiceHelper;
import com.birdeye.campaign.service.CampaignSetupCachingService;
import com.birdeye.campaign.service.CampaignSmsService;
import com.birdeye.campaign.service.CommunicationActivityService;
import com.birdeye.campaign.service.DripCampaignService;
import com.birdeye.campaign.service.FreeTrialService;
import com.birdeye.campaign.service.ReviewRequestService;
import com.birdeye.campaign.service.TemplateHelperService;
import com.birdeye.campaign.service.TemplateService;
import com.birdeye.campaign.service.dao.BusinessSmsTemplateDao;
import com.birdeye.campaign.service.dao.ReviewRequestDao;
import com.birdeye.campaign.sro.SamaySRO;
import com.birdeye.campaign.template.service.TemplateConfigService;
import com.birdeye.campaign.utils.BusinessUtils;
import com.birdeye.campaign.utils.CampaignExceptionUtils;
import com.birdeye.campaign.utils.CampaignUtils;
import com.birdeye.campaign.utils.CommunicationCategoryUtils;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.DateTimeUtils;
import com.birdeye.campaign.utils.JsonUtils;
import com.birdeye.campaign.utils.MvelEvaluationUtils;
import com.birdeye.campaign.utils.ReferralWebhookUtils;
import com.birdeye.referral.request.ReferralWebhookDeliveryEvent;

@Service("campaignRunService")
public class CampaignRunServiceImpl implements CampaignRunService {
	
	private static final Logger		logger	= LoggerFactory.getLogger(CampaignRunServiceImpl.class);

	
	@Autowired
	private ReviewRequestRepo				reviewRequestRepo;
	
	@Autowired
	private BusinessService					businessService;
	
	@Autowired
	private PromotionRepo					promotionRepo;
	
	@Autowired
	private CustomerCommCappingRepo			customerCommCappingRepo;

	@Autowired
	private AccountCustomerCommCappingRepo accountCustomerCommCappingRepo;
	
	@Autowired
	private EmailTemplateRepo				emailTemplateRepo;
	
	@Autowired
	private BusinessSmsTemplateDao			businessSmsTemplateDao;
	
	@Autowired
	private CampaignEmailService			campaignEmailService;
	
	@Autowired
	private CampaignSmsService				campaignSmsService;
	
	@Autowired
	private CampaignEventManagementService	campaignEventManagementService;
	
	@Autowired
	private KafkaService					kafkaService;
	
	@Autowired
	private CustomerService					customerService;
	
	@Autowired
	private CampaignSetupCachingService		campaignSetupCachingService;
	
	@Autowired
	private ReviewRequestService			reviewRequestService;
	
	@Autowired
	private UsageCommunicationService		usageCommunicationService;
	
	@Autowired
	private TemplateConfigService			templateConfigService;
	
	@Autowired
	private TemplateHelperService			templateHelperService;
	
	@Autowired
	private CampaignReviewRequestService	campaignRRService;
	
	@Autowired
	private DripCampaignService				dripCampaignService;
	
	@Autowired
	private CommunicationActivityService	communicationActivityService;
	
	@Autowired
	private CacheService					cacheService;
	
	@Autowired
	private IContactExternalService			contactExternalService;
	
	@Autowired
	private ReviewRequestDao				reviewRequestDAO;
	
	@Autowired
	private SurveyExternalService			surveyExternalService;
	
	@Autowired
	private AerospikeJedisService			aerospikeJedisService;
	
	@Autowired
	private CacheExternalService			cacheExternalService;
	
	@Autowired
	private AppointmentReminderService		appointmentReminderService;
	
	@Autowired
	private AppointmentReminderAuditService appointmentReminderAuditService;
	
	@Autowired
	private CampaignReminderService			campaignReminderService;
	
	@Autowired
	private AppointmentRecallService		appointmentRecallService;
	
	@Autowired
	private AppointmentFormService			appointmentFormService;
	
	@Autowired
	private AppointmentTriggersService		appointmentTriggersService;

	@Autowired
	private BusinessExternalService			businessExternalService;

	@Autowired
	private CampaignRescheduleService		campaignRescheduleService;
	
	@Autowired
	private TemplateService					templateService;
	
	@Autowired
	private CampaignServiceHelper			campaignService;
	
	@Autowired
	private SurveyCustomerCommCappingRepo	surveyCustomerCommCappingRepo;
	
	@Autowired
	private FreeTrialService				freeTrialService;
	
	@Override
	public void submitReviewReqCampaign(Campaign campaign, List<Integer> customers, Integer checkinId) throws Exception {
		List<CreateReviewRequestMessage> reviewRequestCreateMessageList = campaignRRService.getCreateReviewRequestMessages(customers, campaign, campaign.getSurveyId(),
				campaign.getPriority());
		
		logger.info("push in kafka {} review-request create message(s) for instant campaign : {}", reviewRequestCreateMessageList.size(), campaign.getId());
		String topicName = CampaignUtils.appendCampaignTypeToTopicName(campaign.getCampaignType(), KafkaTopicTypeEnum.CAMPAIGN_RR_CREATE.getType());
		pushRRCreateMessageToKafka(topicName, checkinId, reviewRequestCreateMessageList);
	}
	
	/**
	 * Created separate method for single customer to utilise cached getCustomer call.
	 */
	@Override
	public void submitReviewReqCampaign(Campaign campaign, OngoingRunCampaignMessage ongoingCampaignMessage) throws Exception {
		List<CreateReviewRequestMessage> reviewRequestCreateMessageList = campaignRRService.getCreateReviewRequestMessage(ongoingCampaignMessage.getCustomerId(), campaign,
				campaign.getSurveyId(), campaign.getPriority());
		if (ongoingCampaignMessage.getAppointmentId() != null) {
			reviewRequestCreateMessageList.stream().forEach(rr -> rr.setAppointmentId(ongoingCampaignMessage.getAppointmentId()));
		}
		if (ongoingCampaignMessage.getAppointmentRecallId() != null) {
			reviewRequestCreateMessageList.stream().forEach(rr -> rr.setAppointmentRecallId(ongoingCampaignMessage.getAppointmentRecallId()));
		}
		logger.info("push in kafka review-request create message for campaign : {}", campaign.getId());
		String topicName = CampaignUtils.appendCampaignTypeToTopicName(campaign.getCampaignType(), KafkaTopicTypeEnum.CAMPAIGN_RR_CREATE.getType());
		pushRRCreateMessageToKafka(topicName, ongoingCampaignMessage.getCheckinId(), reviewRequestCreateMessageList);
	}

	private void pushRRCreateMessageToKafka(String topicName, Integer checkinId, List<CreateReviewRequestMessage> reviewRequestCreateMessageList) {
		if (CollectionUtils.isNotEmpty(reviewRequestCreateMessageList)) {
			List<KafkaMessage> kafkaMessages = new ArrayList<>();
			for (CreateReviewRequestMessage createReviewRequestMessage : reviewRequestCreateMessageList) {
				// this is to handle the checkin part only as for
				if (checkinId != null) {
					createReviewRequestMessage.setCheckinId(checkinId);
				}
				kafkaMessages.add(new KafkaMessage(createReviewRequestMessage));
			}
			kafkaService.pushMessagesListToKafka(topicName, kafkaMessages);
		}
	}
	
	/**
	 * Runs a RR Campaign (Instant/Reminder) and Schedule reminder jobs
	 * 
	 * requestId = requestId for first execution, isReminder = 0 requestId = parent request id, isReminder = 1
	 */
	@Override
//	@Async(Constants.CAMPAIGN_RUN_RR_TASK_EXECUTOR)
	@Profiled
	public void runReviewRequestCampaign(Long requestId, Integer isReminder) throws CampaignException {
		
		ReviewRequest reviewRequest = createAndGetReminderRequest(requestId, isReminder);
		if (reviewRequest == null || reviewRequest.getCustId() == null || reviewRequest.getCampaignId() == null) {
			logger.warn("run review request campaign : invalid request id {}", requestId);
			return;
		}
		
		Campaign campaign = campaignSetupCachingService.getCampaignById(reviewRequest.getCampaignId());
		
		// BIRD-100846 WAR Condition Re-evaluation
		CustomFieldEvalRequest cfEvalRequest = isReqEligibleForCFEval(campaign, isReminder, reviewRequest.getId());
		
		KontactoDTO customer = getCustomerDetails(reviewRequest.getId(), reviewRequest.getCustId(), cfEvalRequest);
		if (customer == null) {
			// BIRDEYE-103922 | Queued retry in case of kontacto service failure.
			boolean isEventRescheduled = validateAndRescheduleRequestExecutionEvent(reviewRequest.getId(), reviewRequest.getRequestType());
			if (isEventRescheduled) {
				return;
			}
			postReviewRequestFailureActions(isReminder, reviewRequest, campaign, new CampaignException(ErrorCodes.EXTERNAL_SERVICE_UAVAILABLE), null);
			logger.error("run review request campaign : invalid customer id {}", reviewRequest.getCustId());
			return;
		}
		BusinessEnterpriseEntity business = cacheService.getBusinessById(reviewRequest.getBusinessId());
		if (campaign == null || business == null) {
			logger.error("run review request campaign : invalid campaign data for request {}", reviewRequest.getId());
			return;
//			throw new CampaignException(ErrorCodes.INVALID_REQUEST);
		}
		
		// list is created to add appointment reminder scheduled times. It will be used in the cache to restrict reminder communication.
		// By storing the scheduled times, the cache can calculate the TTL (Time-To-Live) based on the scheduled time rather than the current time.
		List<Date> appointmentScheduledDates = new ArrayList<>();
		if (BooleanUtils.isFalse(isValidReviewRequest(reviewRequest, campaign, business, appointmentScheduledDates, cfEvalRequest, customer))) {
			pushCampaignStatsDataToKafka(campaign, 0);
			return;
		}	
		
		executeReviewRequest(isReminder, reviewRequest, campaign, customer, business, appointmentScheduledDates);
		
	}

	private KontactoDTO getCustomerDetails(Long reviewRequestId, Integer customerId, CustomFieldEvalRequest cfEvalRequest) {
		return (BooleanUtils.isFalse(MvelEvaluationUtils.isWARCFEvalRequest(cfEvalRequest))) ? cacheExternalService.getRRCustomerCached(reviewRequestId, customerId)
				: cacheExternalService.getRRCustomerCachedWithReviewSource(reviewRequestId, customerId);
	}
	
	/**
	 * 
	 * BIRD-100846
	 * Checks if Request is eligible for custom field evaluation
	 * 
	 * @param campaign,
	 *            isReminder, requestId
	 * 
	 */
	private CustomFieldEvalRequest isReqEligibleForCFEval(Campaign campaign, Integer isReminder, Long requestId) {
		if (BooleanUtils.isTrue(CoreUtils.getBooleanValueFromInteger(isReminder))) {
			logger.info("isReqEligibleForCFEval :: Request with request id {} not valid for cf evaluation", requestId);
			return new CustomFieldEvalRequest(new ArrayList<>());
		}
		return validateAndAddCFData(campaign, MvelEvaluationUtils.fetchAllowedCFForReEval(), requestId);
	}
	
	/**
	 * 
	 * BIRD-100846
	 * Validate and Add Custom Field Data Based Upon Custom Field And Condition Matching
	 * 
	 * @param campaign,
	 *            applicableCFList, requestId
	 * 
	 */
	private CustomFieldEvalRequest validateAndAddCFData(Campaign campaign, List<String> applicableCFList, Long requestId) {
		CustomFieldEvalRequest cfEvalRequest = new CustomFieldEvalRequest(new ArrayList<>());
		if (CollectionUtils.isEmpty(applicableCFList)) {
			return cfEvalRequest;
		}
		
		// For different custom fields, specific handling can be done here.
		applicableCFList.stream().forEach(customField -> {
			if (StringUtils.equals(customField, MvelEvaluationUtils.WROTE_A_REVIEW_OPERAND) && BooleanUtils.isTrue(MvelEvaluationUtils.isCampaignEligibleForWARCFEval(campaign))) {
				CampaignCondition campaignCondition = campaignSetupCachingService.getCampaignConditionByCampaignIdAndAccountId(campaign.getId(), campaign.getEnterpriseId());
				if (MvelEvaluationUtils.validateWARCFEvalBasedOnConditionAndScheduling(campaignCondition, campaign)) {
					MvelEvaluationUtils.addWARCFToCFEvalRequest(cfEvalRequest);
				}
			}
		});
//		logger.info("validateAndAddCFData :: For request id {} custom field evaluation request is {}", requestId, cfEvalRequest);
		logger.info("validateAndAddCFData :: For request id {}", requestId);
		return cfEvalRequest;
	}
	
	private boolean isValidReviewRequest(ReviewRequest reviewRequest, Campaign campaign, BusinessEnterpriseEntity business, List<Date> appointmentScheduledDates, CustomFieldEvalRequest cfEvalRequest,
			KontactoDTO customer) {
		if ((!RequestStatusEnum.INPROGRESS.getName().equals(reviewRequest.getDeliveryStatus())) && (!RequestStatusEnum.DND.getName().equals(reviewRequest.getDeliveryStatus()))) {
			logger.error("review request {} delivery status {} not valid to run campaign", reviewRequest.getId(), reviewRequest.getDeliveryStatus());
			return false;
//			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "invalid request");
		}
		
		// BIRD-100846 WAR Condition Re-evaluation
		if (BooleanUtils.isFalse(evaluateCFAndValidateRequest(campaign, customer, reviewRequest, business, cfEvalRequest))) {
			return false;
		}
		
		// Check if the campaign run type is MANUAL
		if(StringUtils.equalsIgnoreCase(campaign.getRunType(), CampaignRunTypeEnum.MANUAL.getRunType())) {
			 // Check if the review request is a custom appointment request
			if(appointmentFormService.isManualCampaignCustomAppointmentRequest(campaign, reviewRequest, campaign.getEnterpriseId())) {
				return validateCustomAppointmentForm(reviewRequest, campaign, business);
			}
			if(!appointmentFormService.isManualCampaignCustomAppointmentRequest(campaign, reviewRequest, campaign.getEnterpriseId()) && StringUtils.equalsIgnoreCase(CampaignTypeEnum.APPOINTMENT_FORM.getType(), campaign.getType())) {
				return validateAppointmentForm(reviewRequest, campaign, business);
			}
			if (StringUtils.equalsIgnoreCase(CampaignTypeEnum.APPOINTMENT_REMINDER.getType(), campaign.getType())) {
				return validateAppointmentReminderRequest(reviewRequest, campaign, business, appointmentScheduledDates);
			}
			return true;
		}
		if (StringUtils.equalsIgnoreCase(CampaignTypeEnum.APPOINTMENT_REMINDER.getType(), campaign.getType())) {
			return validateAppointmentReminderRequest(reviewRequest, campaign, business, appointmentScheduledDates);
		} else if (StringUtils.equalsIgnoreCase(CampaignTypeEnum.APPOINTMENT_RECALL.getType(), campaign.getType())) {
			return validateAppointmentRecall(reviewRequest, campaign, business);
		} else if (StringUtils.equalsIgnoreCase(CampaignTypeEnum.APPOINTMENT_FORM.getType(), campaign.getType())) {
			return validateAppointmentForm(reviewRequest, campaign, business);
		} else if (StringUtils.equalsAnyIgnoreCase(campaign.getTriggerType(), CampaignTriggerTypeEnum.APPOINTMENT_BOOKED.getType(), CampaignTriggerTypeEnum.APPOINTMENT_CANCELED.getType(),
				CampaignTriggerTypeEnum.APPOINTMENT_MISSED.getType(), CampaignTriggerTypeEnum.APPOINTMENT_COMPLETED.getType())) {
			return validateAppointmentTriggersRR(reviewRequest, campaign, business.getEnterpriseIdElseBusinessId());
		}

		return true;
	}
	
	/**
	 * 
	 * BIRD-100846
	 * This function does the following
	 * 1. Validate And Evaluate Custom Field And Return the request is valid or not.
	 * 
	 * @param campaign,
	 *            requestd, cfEvalRequest, customer
	 * 			
	 */
	private boolean validateAndEvaluateExpression(Campaign campaign, Long requestId, CustomFieldEvalRequest cfEvalRequest, KontactoDTO customer) throws Exception {
		logger.info("validateAndEvaluateExpression :: For request id {}, Request received for building and evaluating custom field expression", requestId);
		for (CustomFieldEvalRequest.CustomFieldDetails customFieldData : cfEvalRequest.getCustomFieldData()) {
			if (BooleanUtils.isTrue(MvelEvaluationUtils.isWARCFEvalRequest(cfEvalRequest))) {
				logger.info("validateAndEvaluateExpression :: For request id {}, building and evaluating Wrote A Review expression with review data {}", requestId, customer.getReviewSourceIds());
				return buildAndEvaluateWARExpression(campaign, customer, new ArrayList<>(Collections.singletonList(customFieldData.getCustomFieldName())), requestId);
			}
		}
		return true;
	}
	
	/**
	 * 
	 * BIRD-100846
	 * This function does the following
	 * 1. Evaluate Custom Field Condition
	 * 2. In case of Exception update review request with the appropriate failure reason
	 * 
	 * @param campaign,
	 *            customer, reviewRequest, business, cfEvalRequest
	 * 			
	 */
	private boolean evaluateCFAndValidateRequest(Campaign campaign, KontactoDTO customer, BaseCommunicationEntity reviewRequest, BusinessEnterpriseEntity business,
			CustomFieldEvalRequest cfEvalRequest) {
		if (BooleanUtils.isFalse(MvelEvaluationUtils.isCFEvalRequest(cfEvalRequest))) {
			return true;
		}
		
		try {
			return validateAndEvaluateExpression(campaign, reviewRequest.getId(), cfEvalRequest, customer);
		} catch (CampaignException campaignException) {
			logger.warn("review request {} not valid since cf matching failed", reviewRequest.getId());
			markReviewRequestFailure(reviewRequest, campaignException.getLocalizedMessage(), (reviewRequest instanceof Promotion) ? null : business.getEnterpriseIdElseBusinessId());
			return false;
		} catch (Exception exception) {
			logger.error("Exception occurred while validating cf for review request {} :: {}", reviewRequest.getId(), ExceptionUtils.getStackTrace(exception));
			markReviewRequestFailure(reviewRequest, getFailureReasonForException(exception, false, false), (reviewRequest instanceof Promotion) ? null : business.getEnterpriseIdElseBusinessId());
			return false;
		}
	}
	
	/**
	 * 
	 * BIRD-100846
	 * This function does the following
	 * 1. Extract Custom Fields with given id, from Campaign Condition
	 * 2. Build And Compile Mvel Expression
	 * 3. Evaluate Mvel Expression
	 * 4. Throw Different Exception Based Upon Operator, if condition evaluates to false
	 * 
	 * @param campaign,
	 *            customer, cfName
	 * 
	 */
	private boolean buildAndEvaluateWARExpression(Campaign campaign, KontactoDTO customer, List<String> cfNameList, Long requestId) throws Exception {
		RuleExpression ruleExpression = RuleEngine.extractAndPrepareCFCondition(campaignSetupCachingService.getCampaignConditionByCampaignIdAndAccountId(campaign.getId(), campaign.getEnterpriseId()),
				cfNameList, "AND");
		if (BooleanUtils.isFalse(MvelEvaluationUtils.evaluateMvelExpression(RuleEngine.buildRuleExpression(ruleExpression), RuleEngine.getMvelParamsFromConditions(ruleExpression.getConditions()),
				null, null, customer.getReviewSourceIds()))) {
			logger.warn("buildAndEvaluateWARExpression :: The expression evalutes to false for request id {}", requestId);
			if (BooleanUtils.isTrue(RuleEngine.matchOperatorInRuleExpressionCondition(ruleExpression.getConditions(), Operator.NOT_IN.getLabel(), "1"))) {
				throw new CampaignException(ErrorCodes.REVIEW_WRITTEN_CONDITION_REVIEWED_ERROR, ErrorCodes.REVIEW_WRITTEN_CONDITION_REVIEWED_ERROR.getMessage());
			}
			throw new CampaignException(ErrorCodes.REVIEW_WRITTEN_CONDITION_NO_REVIEW_ERROR, ErrorCodes.REVIEW_WRITTEN_CONDITION_NO_REVIEW_ERROR.getMessage());
		}
		logger.info("buildAndEvaluateWARExpression :: The expression evalutes to true for request id {}", requestId);
		return true;
	}
	
	private boolean validateAppointmentTriggersRR(ReviewRequest reviewRequest, Campaign campaign, Integer enterpriseId) {
		AppointmentRRMapping appointmentRequestAudit = appointmentReminderAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
		if (appointmentRequestAudit == null) {
			logger.warn("No valid appointment exits for review request id {}", reviewRequest.getId());
			throw new CampaignException(ErrorCodes.INVALID_APPOINTMENT, ErrorCodes.INVALID_APPOINTMENT.getMessage());
		}
		try {
			appointmentTriggersService.validateAppointmentTriggersExecutionRequest(appointmentRequestAudit.getAppointmentId(), campaign);
		} catch (Exception e) {
			markReviewRequestDropped(reviewRequest, e.getLocalizedMessage(), false, false, enterpriseId);
			return false;
		}
		return true;
	}

	private boolean validateAppointmentForm(ReviewRequest reviewRequest, Campaign campaign, BusinessEnterpriseEntity business) {
		AppointmentRRMapping appointmentRequestAudit = appointmentReminderAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
		if (appointmentRequestAudit == null) {
			logger.warn("No valid appointment exits for review request id {}", reviewRequest.getId());
			throw new CampaignException(ErrorCodes.INVALID_APPOINTMENT, ErrorCodes.INVALID_APPOINTMENT.getMessage());
		}
		try {
			if(StringUtils.equalsIgnoreCase(campaign.getRunType(), CampaignRunTypeEnum.MANUAL.getRunType())) {
				appointmentFormService.validateManualAppointmentFormExecutionRequest(reviewRequest, appointmentRequestAudit.getAppointmentId(), campaign);
			} else {
				appointmentFormService.validateFormExecutionRequest(reviewRequest, appointmentRequestAudit.getAppointmentId(), campaign.getId(), campaign.getEnterpriseId(), business);
			}
		} catch (Exception e) {
			markReviewRequestDropped(reviewRequest, e.getLocalizedMessage(), true, false, business.getEnterpriseIdElseBusinessId());
			return false;
		}
		return true;
	}

	private boolean validateAppointmentRecall(ReviewRequest reviewRequest, Campaign campaign, BusinessEnterpriseEntity business) {
		AppointmentRRMapping appointmentRequestAudit = appointmentReminderAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
		if (appointmentRequestAudit == null) {
			logger.warn("No valid recall exits for review request id {}", reviewRequest.getId());
			throw new CampaignException(ErrorCodes.INVALID_APPOINTMENT, ErrorCodes.INVALID_APPOINTMENT.getMessage());
		}
		try {
			appointmentRecallService.isValidRecallExecutionRequest(appointmentRequestAudit.getRecallId(), campaign.getId(), campaign.getEnterpriseId(), appointmentRequestAudit.getAppointmentId(), business.getTimezoneId(), campaign.getTriggerType());
		} catch (CampaignException e) {
			if (e.getCode().equals(ErrorCodes.FUTURE_APPOINTMENT_BOOKED) || e.getCode().equals(ErrorCodes.CAMPAIGN_SCHEDULING_CONFIG_NOT_MATCHED)) {
				campaignReminderService.scheduleReminder(reviewRequest, campaign);
			}
			markReviewRequestDropped(reviewRequest, e.getLocalizedMessage(), false, false, business.getEnterpriseIdElseBusinessId());
			return false;
		}
		return true;
	}

	private boolean validateAppointmentReminderRequest(ReviewRequest reviewRequest, Campaign campaign, BusinessEnterpriseEntity business, List<Date> appointmentScheduledDates) {
		AppointmentRRMapping appointmentRequestAudit = appointmentReminderAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
		if (appointmentRequestAudit == null) {
			logger.warn("No valid appointment exits for review request id {}", reviewRequest.getId());
			throw new CampaignException(ErrorCodes.INVALID_APPOINTMENT, ErrorCodes.INVALID_APPOINTMENT.getMessage());
		}
		try {
			if(StringUtils.equalsIgnoreCase(campaign.getRunType(), CampaignRunTypeEnum.MANUAL.getRunType())) {
				appointmentReminderService.validateManualAppointmentReminderExecutionRequest(appointmentRequestAudit.getAppointmentId(), campaign.getId(), campaign.getEnterpriseId());
			} else {
				appointmentReminderService.validateReminderExecutionRequest(appointmentRequestAudit.getAppointmentId(), campaign.getId(), campaign.getEnterpriseId(), business, appointmentScheduledDates);
			}
		} catch (AppointmentReminderForChainingException e) {
			markReviewRequestDropped(reviewRequest, e.getLocalizedMessage(), true, true, business.getEnterpriseIdElseBusinessId());
			return false;
		} catch (Exception e) {
			markReviewRequestDropped(reviewRequest, e.getLocalizedMessage(), true, false, business.getEnterpriseIdElseBusinessId());
			return false;
		}
		return true;
	}

	private void executeReviewRequest(Integer isReminder, ReviewRequest reviewRequest, Campaign campaign, KontactoDTO customer, BusinessEnterpriseEntity business, List<Date> appointmentScheduledDates) {
		String communicationCategory = templateService.fetchRequestCommunicationCategory(reviewRequest.getSource(), reviewRequest.getTemplateId());
		boolean sent = false;
		try {
			if (Constants.TEMPLATE_BASE_TYPE_EMAIL.equalsIgnoreCase(reviewRequest.getSource())) {
				// send email
				sent = sendEmailRequest(reviewRequest, customer, business, campaign, isReminder, applyCustomerEngagementFlag(reviewRequest.getRequestType()), appointmentScheduledDates, communicationCategory);
			} else {
				// send sms
				sent = sendRRSMSRequest(reviewRequest, customer, business, campaign, applyDnd(reviewRequest.getRequestType()), appointmentScheduledDates, communicationCategory);
			}
		} catch (CampaignException campaignException) {
			handleCampaignExceptionForReviewRequestExecution(campaignException, isReminder, reviewRequest, campaign, business);
		} catch (Exception exception) {
			logger.error("Review request {} could not be sent: {}", reviewRequest.getId(), ExceptionUtils.getStackTrace(exception));
			postReviewRequestFailureActions(isReminder, reviewRequest, campaign, exception, business.getEnterpriseIdElseBusinessId());
		}
		
		// Prepare an event object with RR and status
		if (sent) {
			postReviewRequestSuccessActions(reviewRequest, campaign, business, customer, campaign.getType(), isReminder);
		}
	}
	
	private void handleCampaignExceptionForReviewRequestExecution(CampaignException campaignException, Integer isReminder, ReviewRequest reviewRequest, Campaign campaign,
			BusinessEnterpriseEntity business) {
		
		// reschedule request for url shortening exception
		if (CampaignExceptionUtils.isURLShorteningRetriableException(campaignException)) {
			handleURLShorteningRetriableException(isReminder, reviewRequest, campaign, business, campaignException);
		}
		
	}
	
	private void handleURLShorteningRetriableException(Integer isReminder, ReviewRequest reviewRequest, Campaign campaign, BusinessEnterpriseEntity business, CampaignException campaignException) {
		// validate and reschedule execution of review request at samay
		boolean rescheduleRequest = campaignRescheduleService.validateAndRescheduleRequestExecutionEvent(
				new ReviewRequestRetryDTO(reviewRequest.getId(), reviewRequest.getRequestType(), new RetryRequestMetaInfo().populateURLShortenErrorRetryMetaInfo()));
		if (rescheduleRequest) {
			return;
		}
		logger.error("exception occurred while shortening url for request id {} , exc {} ", reviewRequest.getId(), ExceptionUtils.getStackTrace(campaignException));
		postReviewRequestFailureActions(isReminder, reviewRequest, campaign, new CampaignException(ErrorCodes.EXTERNAL_SERVICE_UAVAILABLE), business.getEnterpriseIdElseBusinessId());
	}
	
	private boolean applyDnd(String requestType) {
		return BooleanUtils.isFalse(StringUtils.equalsAnyIgnoreCase(requestType, CampaignTypeEnum.APPOINTMENT_REMINDER.getType(), CampaignTypeEnum.APPOINTMENT_RECALL.getType(), CampaignTypeEnum.APPOINTMENT_FORM.getType()));
	}
	
	private boolean applyCustomerEngagementFlag(String requestType) {
		return BooleanUtils.isFalse(StringUtils.equalsAnyIgnoreCase(requestType, CampaignTypeEnum.APPOINTMENT_REMINDER.getType(), CampaignTypeEnum.APPOINTMENT_RECALL.getType(), CampaignTypeEnum.APPOINTMENT_FORM.getType()));
	}

	@Override
	public void runPromotionRequestCampaign(Long promotionId) {
		Optional<Promotion> promotionRequestOptional = promotionRepo.findById(promotionId);
		Promotion promotionRequest = null;
		if (promotionRequestOptional.isPresent()) {
			promotionRequest = promotionRequestOptional.get();
		}
		if (promotionRequest == null || promotionRequest.getCampaignId() == null) {
			logger.warn("run promotion request campaign : invalid promotion request id {}", promotionId);
			return;
		}
		Campaign campaign = campaignSetupCachingService.getCampaignById(promotionRequest.getCampaignId());
		
		// BIRD-100846 WAR Condition Re-evaluation
		CustomFieldEvalRequest cfEvalRequest = isReqEligibleForCFEval(campaign, 0, promotionRequest.getId());
		
		KontactoDTO customer = getCustomerDetails(promotionRequest.getId(), promotionRequest.getCustId(), cfEvalRequest);
		if (customer == null) {
			// BIRDEYE-103922 | Queued retry in case of kontacto service failure.
			boolean isEventRescheduled = validateAndRescheduleRequestExecutionEvent(promotionRequest.getId(), promotionRequest.getRequestType());
			if (isEventRescheduled) {
				return;
			}
			postPromotionRequestFailureActions(promotionRequest, campaign, new CampaignException(ErrorCodes.EXTERNAL_SERVICE_UAVAILABLE));
			logger.error("run promotion request campaign : invalid customer id {}", promotionRequest.getCustId());
			return;
		}
		
		BusinessEnterpriseEntity business = cacheService.getBusinessById(promotionRequest.getBusinessId());
		if (campaign == null || business == null) {
			logger.error("run promotion campaign : invalid campaign data for request {}", promotionRequest.getId());
			return;
//			throw new CampaignException(ErrorCodes.INVALID_REQUEST);
		}
		
		if (BooleanUtils.isFalse(isValidPromotionalRequest(promotionRequest, campaign, cfEvalRequest, customer, business)))
			return;
		
		String communicationCategory = templateService.fetchRequestCommunicationCategory(promotionRequest.getSource(), promotionRequest.getTemplateId());
		
		boolean sent = false;
		try {
			if (Constants.TEMPLATE_BASE_TYPE_EMAIL.equalsIgnoreCase(promotionRequest.getSource())) {
				// send email
				sent = sendPromotionMailToCustomer(business, promotionRequest, campaign, customer, true, communicationCategory);
			} else {
				// send sms
				sent = sendPromotionSMSRequest(promotionRequest, customer, business, campaign, communicationCategory);
			}
		} catch (CampaignException campaignException) {
			handleCampaignExceptionForPromotionRequest(promotionRequest, campaign, campaignException);
		}
		
		catch (Exception e) {
			logger.error("Promotion request id {} could not be sent: {}", promotionId, e);
			postPromotionRequestFailureActions(promotionRequest, campaign, e);
		}
		
		// Prepare an event object with RR and status
		if (sent) {
			postPromotionRequestSuccessActions(promotionRequest, campaign, business, customer);
		}
	}
	
	private void handleCampaignExceptionForPromotionRequest(Promotion promotionRequest, Campaign campaign, CampaignException campaignException) {
		
		// reschedule promotion request for url shortening exception
		if (CampaignExceptionUtils.isURLShorteningRetriableException(campaignException)) {
			boolean rescheduleRequest = campaignRescheduleService.validateAndRescheduleRequestExecutionEvent(
					new ReviewRequestRetryDTO(promotionRequest.getId(), promotionRequest.getRequestType(), new RetryRequestMetaInfo().populateURLShortenErrorRetryMetaInfo()));
			if (rescheduleRequest) {
				return;
			}
			logger.error("exception occurred while shortening url for request id {} , exc {} ", promotionRequest.getId(), ExceptionUtils.getStackTrace(campaignException));
			postPromotionRequestFailureActions(promotionRequest, campaign, campaignException);
		}
		
	}
	
	private Boolean isValidPromotionalRequest(Promotion promotionRequest, Campaign campaign, CustomFieldEvalRequest cfEvalRequest, KontactoDTO customer, BusinessEnterpriseEntity business) {
		if ((!RequestStatusEnum.INPROGRESS.getName().equals(promotionRequest.getDeliveryStatus()))
				&& (!RequestStatusEnum.DND.getName().equals(promotionRequest.getDeliveryStatus()))) {
			
			logger.error("promotion request {} delivery status not valid to run campaign", promotionRequest.getId());
			return false;
//			throw new CampaignException(ErrorCodes.INVALID_REQUEST);
		}
		
		// BIRD-100846 WAR Condition Re-evaluation
		if (BooleanUtils.isFalse(evaluateCFAndValidateRequest(campaign, customer, promotionRequest, business, cfEvalRequest))) {
			return false;
		}
		
		if (StringUtils.equalsAnyIgnoreCase(campaign.getTriggerType(), CampaignTriggerTypeEnum.APPOINTMENT_BOOKED.getType(), CampaignTriggerTypeEnum.APPOINTMENT_CANCELED.getType(),
				CampaignTriggerTypeEnum.APPOINTMENT_MISSED.getType(), CampaignTriggerTypeEnum.APPOINTMENT_COMPLETED.getType())) {
			return validateAppointmentTriggersPromotion(promotionRequest, campaign);
		}

		return true;
	}

	private Boolean validateAppointmentTriggersPromotion(Promotion promotionRequest, Campaign campaign) {
		AppointmentRRMapping appointmentRequestAudit = appointmentReminderAuditService.getReminderRequestAuditByReviewRequestId(promotionRequest.getId());
		if (appointmentRequestAudit == null || appointmentRequestAudit.getAppointmentId() == null) {
			logger.warn("No valid appointment exits for review request id {}", promotionRequest.getId());
			throw new CampaignException(ErrorCodes.INVALID_APPOINTMENT, ErrorCodes.INVALID_APPOINTMENT.getMessage());
		}
		try {
			appointmentTriggersService.validateAppointmentTriggersExecutionRequest(appointmentRequestAudit.getAppointmentId(), campaign);
		} catch (Exception e) {
			markPromotionRequestDropped(promotionRequest, e.getLocalizedMessage());
			return false;
		}
		return true;
	}

	// @Async
	@Override
	public void sendMessengerInboxEmail(InboxEmailRequestMessage inboxEmailRequest) {
		Boolean isExternalTemplateCreationRequest = CampaignUtils.isExternalTemplateContentPreparationRequest(inboxEmailRequest);
		CampaignTypeEnum campaignType = CampaignUtils.getCampaignTypeByTemplateType(TemplateTypeEnum.findByKey(inboxEmailRequest.getTemplateType()));
		if (campaignType == null) {
			logger.error("send email via inbox : invalid tempate type {}", inboxEmailRequest.getTemplateType());
			// BIRD-133895 - Failure Reason Send to Inbox
			campaignEmailService.sendFailureResponseToInbox("Invalid template type", inboxEmailRequest);
			return;
		}
		KontactoDTO customer = contactExternalService.getCustomerById(inboxEmailRequest.getCustomerId());
		BusinessEnterpriseEntity business = cacheService.getBusinessById(inboxEmailRequest.getBusinessId());
		if (Objects.isNull(business) || CoreUtils.getBooleanValueFromInteger(business.getClosed())) {
			logger.error("send email via inbox: Invalid business for mail send!");
			// BIRD-133895 - Failure Reason Send to Inbox
			campaignEmailService.sendFailureResponseToInbox("Invalid business", inboxEmailRequest);
			return;
		}
		BaseCommunicationEntity reviewRequest = null;
		
		// BIRDEYE-100734 Employee tagging for Inbox emails
		setCheckinIdToInboxEmailRequest(inboxEmailRequest, customer, business);
		try {
			if (CampaignTypeEnum.PROMOTIONAL.equals(campaignType)) {
				reviewRequest = getInboxEmailPromotionRequest(inboxEmailRequest);
				String communicationCategory = BooleanUtils.isTrue(isExternalTemplateCreationRequest) ? StringUtils.EMPTY
						: templateService.fetchRequestCommunicationCategory(reviewRequest.getSource(), reviewRequest.getTemplateId());
				// BIRDEYEV2-11406 : Creates a mapping of review request and communication source.
				campaignRRService.createCommunicationRequestSource(reviewRequest, CampaignUtils.fetchCommunicationSource(inboxEmailRequest));
				campaignEmailService.sendPromotionMailToCustomer(business, (Promotion) reviewRequest, inboxEmailRequest.getTemplateId(), customer, inboxEmailRequest, true, communicationCategory);
				postInboxEmailSuccessActions((Promotion) reviewRequest, business.getEnterpriseIdElseBusinessId());
			} else {
				reviewRequest = campaignRRService.getInboxEmailReviewRequest(inboxEmailRequest, campaignType.getTemplateType());
				String communicationCategory = BooleanUtils.isTrue(isExternalTemplateCreationRequest) ? StringUtils.EMPTY
						: templateService.fetchRequestCommunicationCategory(reviewRequest.getSource(), reviewRequest.getTemplateId());
				// BIRDEYEV2-11406 : Creates a mapping of review request and communication source.
				campaignRRService.createCommunicationRequestSource(reviewRequest, CampaignUtils.fetchCommunicationSource(inboxEmailRequest));
				if (BooleanUtils.isFalse(isExternalTemplateCreationRequest) && BooleanUtils.isFalse(freeTrialService.validateFreeTrialAccountLimits(reviewRequest.getRequestType(), business.getEnterpriseIdElseBusinessId()))) {
					logger.error("Free Trial Limit Exhausted and therefore not sending Email for business  id : {}, reviewRequest id : {}", business.getId(), reviewRequest.getId());
					throw new CampaignException(ErrorCodes.ERROR_OCCURRED_FREE_TRIAL_LIMIT_EXHAUSTED, Constants.FREE_TRIAL_INBOX_FAILURE_REASON);
				}
				// sendOnPriority flag true for messenger inbox email
				campaignEmailService.sendRequestMailToCustomer(business, (ReviewRequest) reviewRequest, false, campaignType.getType(), customer, inboxEmailRequest, null, true, communicationCategory);
				postInboxEmailSuccessActions((ReviewRequest) reviewRequest, business.getEnterpriseIdElseBusinessId());
			}
		} catch (Exception e) {
			logger.error("request id {} for email via inbox failed - {}", reviewRequest != null ? reviewRequest.getId() : "", e.getMessage());
			updateInboxFailure(reviewRequest, e, business, inboxEmailRequest, isExternalTemplateCreationRequest);
		}
		// BIRDEYE-127257 added created at field for messenger
		pushCampaignStatusToMessenger(reviewRequest, null, business, customer, inboxEmailRequest, new Date());
	}
	
	private void updateInboxFailure(BaseCommunicationEntity reviewRequest, Exception e, BusinessEnterpriseEntity business, InboxEmailRequestMessage inboxEmailRequest, Boolean isExternalTemplateCreationRequest) {
		// BIRD-133895 - Failure Reason Send to Inbox and RR Update
		if (BooleanUtils.isTrue(isExternalTemplateCreationRequest)) {
			postInboxEmailFailureActionsWithOnlyDBUpdate(reviewRequest, e, business.getEnterpriseIdElseBusinessId());
			campaignEmailService.sendFailureResponseToInbox("Exception occurred while preparing template data", inboxEmailRequest);
			return;
		}
		postInboxEmailFailureActions(reviewRequest, e, business.getEnterpriseIdElseBusinessId()); // NOSONAR
	}

	private void setCheckinIdToInboxEmailRequest(InboxEmailRequestMessage inboxEmailRequest, KontactoDTO customer, BusinessEnterpriseEntity business) {
		if (StringUtils.isNotBlank(inboxEmailRequest.getEmployeeEmailId())) {
			CreateLocationCustomerRequest locationCustomerRequest = prepareCreateLocationCustomerRequest(inboxEmailRequest, customer, business);
			try {
				CreateCustomerCheckinResponse checkInResponse = contactExternalService.createOrGetCustomerWithCheckin(locationCustomerRequest);
				inboxEmailRequest.setCheckinId(checkInResponse != null ? checkInResponse.getCheckinId() : null);
			} catch (Exception e) {
				logger.warn("[sendMessengerInboxEmail] Error occured while setting checkin id for request : {}", inboxEmailRequest);
			}
		}
	}
	
	private CreateLocationCustomerRequest prepareCreateLocationCustomerRequest(InboxEmailRequestMessage inboxEmailRequest, KontactoDTO customer, BusinessEnterpriseEntity business) {
		Location location = new Location();
		location.setCountryCode(business.getCountryCode());
		return new CreateLocationCustomerRequest(customer.getEmailId(), customer.getPhone(), customer.getDisplayName(), inboxEmailRequest.getEmployeeEmailId(),
				location, business.getId(), business.getBusinessId(), business.getEnterpriseIdElseBusinessId());
	}
	
	@Override
	public void prepareEmailTemplateContent(InboxEmailContentGetRequest request) {
		logger.info("Request received to prepare email template content {}", request);
		if (request == null || request.getExternalUId() == null || request.getTemplateId() == null || request.getCustomerId() == null
				|| (CampaignUtils.isSuspectedSurveyRequest(request.getSurveyId(), request.getTemplateType())
						&& BooleanUtils.isFalse(CampaignUtils.isSurveyRequest(request.getSurveyId(), request.getTemplateType())))) {
			logger.error("Invalid request {}", request);
			return;
		}
		sendMessengerInboxEmail(request);
	}
	
	//// Quick Send
	@Override
	@Async(Constants.QUICK_SEND_RR_TASK_EXECUTOR)
	@Profiled
	public void runQuickSendReviewRequest(Long requestId, Integer isReminder) throws CampaignException {
		logger.info("Quick send run RR for request Id {} and reminder {}", requestId, isReminder);
		// get and validate RR
		ReviewRequest reviewRequest = campaignRRService.validateAndGetReviewRequestForQuickSend(requestId, isReminder);
		
		KontactoDTO customer = cacheExternalService.getRRCustomerCached(reviewRequest.getId(), reviewRequest.getCustId());
		BusinessEnterpriseEntity business = cacheService.getBusinessById(reviewRequest.getBusinessId());
		
		if (business == null || customer == null) {
			logger.error("run review request campaign : invalid  data as business and customer is mandatory for requestId {}", requestId);
			return;
//			throw new CampaignException(ErrorCodes.INVALID_REQUEST);
		}
		// get Campaign Type
		CampaignTypeEnum campaignType = CampaignUtils.getCampaignType(reviewRequest.getRequestType());
		if (campaignType == null) {
			logger.error("run review request campaign : invalid  type of RR  as rr type ia mandatory for requestId {}", requestId);
			return;
//			throw new CampaignException(ErrorCodes.INVALID_REQUEST);
		}
		
		String communicationCategory = templateService.fetchRequestCommunicationCategory(reviewRequest.getSource(), reviewRequest.getTemplateId());
		
		boolean sent = false;
		try {
			if (Constants.TEMPLATE_BASE_TYPE_EMAIL.equalsIgnoreCase(reviewRequest.getSource())) {
				// send email
				sent = quickSendEmailRequest(reviewRequest, customer, business, campaignType, isReminder, communicationCategory);
			} else {
				// send sms
				sent = quickSendSMSRequest(reviewRequest, customer, campaignType, business, true, false, communicationCategory);
			}
		} catch (Exception e) {
			logger.error("Review request {} could not be sent: {}", reviewRequest.getId(), e);
			markRRFailure(reviewRequest, e, business.getEnterpriseIdElseBusinessId());
		}
		
		// Prepare an event object with RR and status
		if (sent) {
			postReviewRequestSuccessActions(reviewRequest, null, business, customer, campaignType.getType(), isReminder);
		}
	}

	private boolean isUpdateNotRequired(Long exitingTime, Long currentTime){
		if(exitingTime>currentTime){
			return true;
		}
		return false;
	}
	@Override
	public void updateAccountCustomerCapping(UpdateCustomerCommCappingRequest commCappingRequest){
		CampaignTypeEnum campaignType = CampaignTypeEnum.getEnum(commCappingRequest.getRequestType());
		Date lastSentOn = new Date();
		
		//process survey request 
		if(BooleanUtils.isTrue(commCappingRequest.getSurveyCommRestrictionEnable()) && StringUtils.equalsIgnoreCase(CampaignTypeEnum.SURVEY_REQUEST.getType(), commCappingRequest.getRequestType())) {
			updateAccountSurveyCustomerCommCapping(commCappingRequest);
		}
		
		AccountCustomerCommCapping accountCommCapping = accountCustomerCommCappingRepo.findByAccountCustomerId(commCappingRequest.getEcid());
		if(accountCommCapping == null){
			accountCommCapping = new AccountCustomerCommCapping();
			accountCommCapping.setAccountCustomerId(commCappingRequest.getEcid());
		}
		logger.info("Account comm capping is {}", accountCommCapping);
		if (Constants.RR_SOURCE_EMAIL.equalsIgnoreCase(commCappingRequest.getSource())) {
			switch (campaignType) {
				case REVIEW_REQUEST:
					if(isUpdateNotRequired(accountCommCapping.getRrEmailOn().getTime(), lastSentOn.getTime())){
						return;
					}
					accountCommCapping.setRrEmailOn(lastSentOn);
					break;
				case SURVEY_REQUEST:
					if(isUpdateNotRequired(accountCommCapping.getSurveyEmailOn().getTime(), lastSentOn.getTime())){
						return;
					}
					accountCommCapping.setSurveyEmailOn(lastSentOn);
					break;

				case CX_REQUEST:
					if(isUpdateNotRequired(accountCommCapping.getCxEmailOn().getTime(), lastSentOn.getTime())){
						return;
					}
					accountCommCapping.setCxEmailOn(lastSentOn);
					break;

				case PROMOTIONAL:
					if(isUpdateNotRequired(accountCommCapping.getPromotionalEmailOn().getTime(), lastSentOn.getTime())){
						return;
					}
					accountCommCapping.setPromotionalEmailOn(lastSentOn);
					break;

				case REFERRAL:
					if(isUpdateNotRequired(accountCommCapping.getReferralEmailOn().getTime(), lastSentOn.getTime())){
						return;
					}
					accountCommCapping.setReferralEmailOn(lastSentOn);
					break;
				default:
					break;

			}
		}

		else {
			switch (campaignType) {
				case REVIEW_REQUEST:
					if(isUpdateNotRequired(accountCommCapping.getRrSmsOn().getTime(), lastSentOn.getTime())){
						return;
					}
					accountCommCapping.setRrSmsOn(lastSentOn);
					break;
				case SURVEY_REQUEST:
					if(isUpdateNotRequired(accountCommCapping.getSurveySmsOn().getTime(), lastSentOn.getTime())){
						return;
					}
					accountCommCapping.setSurveySmsOn(lastSentOn);
					break;
				case CX_REQUEST:
					if(isUpdateNotRequired(accountCommCapping.getCxSmsOn().getTime(), lastSentOn.getTime())){
						return;
					}
					accountCommCapping.setCxSmsOn(lastSentOn);
					break;

				case PROMOTIONAL:
					if(isUpdateNotRequired(accountCommCapping.getPromotionalSmsOn().getTime(), lastSentOn.getTime())){
						return;
					}
					accountCommCapping.setPromotionalSmsOn(lastSentOn);
					break;

				case REFERRAL:
					if(isUpdateNotRequired(accountCommCapping.getReferralSmsOn().getTime(), lastSentOn.getTime())){
						return;
					}
					accountCommCapping.setReferralSmsOn(lastSentOn);
					break;

				default:
					break;

			}
		}
		logger.info("after update account commm capping {}",accountCommCapping);
		saveOrUpdateAccountCustomerCommCapping(accountCommCapping);
	}
	
	/**
	 * Update customer_comm_capping for a customer.
	 * Push campaign customer update event to kontacto.
	 */
	@Override
	public void updateCustomerCommCapping(UpdateCustomerCommCappingRequest commCappingRequest) {
		UpdateCampaignCustomerRequest updateRequest = new UpdateCampaignCustomerRequest();
		updateRequest.setCustomerId(commCappingRequest.getCustomerId());
		updateRequest.setSource(commCappingRequest.getSource());
		CampaignTypeEnum campaignType = CampaignTypeEnum.getEnum(commCappingRequest.getRequestType());
		Date lastSentOn = new Date();
		
		//Process survey request
		if(BooleanUtils.isTrue(commCappingRequest.getSurveyCommRestrictionEnable()) && StringUtils.equalsIgnoreCase(CampaignTypeEnum.SURVEY_REQUEST.getType(), commCappingRequest.getRequestType())) {
			updateLocationSurveyCustomerCommCapping(commCappingRequest);
		}
		CustomerCommCapping commCapping = customerCommCappingRepo.getCustomerLastCommunicationMessage(commCappingRequest.getCustomerId());
		if (commCapping == null) {
			commCapping = new CustomerCommCapping();
		}
		commCapping.setCustomerId(commCappingRequest.getCustomerId());
		if (Constants.RR_SOURCE_EMAIL.equalsIgnoreCase(commCappingRequest.getSource())) {
			switch (campaignType) {
			case REVIEW_REQUEST:
				updateRequest.setRrEmailOn(lastSentOn);
				commCapping.setRrEmailOn(lastSentOn);
				kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_CUSTOMER_UPDATE, new KafkaMessage(updateRequest));
				break;
			case SURVEY_REQUEST:
				updateRequest.setSurveyEmailOn(lastSentOn);
				commCapping.setSurveyEmailOn(lastSentOn);
				kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_CUSTOMER_UPDATE, new KafkaMessage(updateRequest));
				break;
			
			case CX_REQUEST:
				updateRequest.setCxEmailOn(lastSentOn);
				commCapping.setCxEmailOn(lastSentOn);
				kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_CUSTOMER_UPDATE, new KafkaMessage(updateRequest));
				break;
			
			case PROMOTIONAL:
				updateRequest.setPromotionEmailOn(lastSentOn);
				commCapping.setPromotionalEmailOn(lastSentOn);
				kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_CUSTOMER_UPDATE, new KafkaMessage(updateRequest));
				break;
			
			case REFERRAL:
				updateRequest.setReferralEmailOn(lastSentOn);
				commCapping.setReferralEmailOn(lastSentOn);
				kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_CUSTOMER_UPDATE, new KafkaMessage(updateRequest));
				break;
			
			default:
				break;
			
			}
		}
		
		else {
			switch (campaignType) {
			case REVIEW_REQUEST:
				updateRequest.setRrSmsOn(lastSentOn);
				commCapping.setRrSmsOn(lastSentOn);
				kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_CUSTOMER_UPDATE, new KafkaMessage(updateRequest));
				break;
			case SURVEY_REQUEST:
				updateRequest.setSurveySmsOn(lastSentOn);
				commCapping.setSurveySmsOn(lastSentOn);
				kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_CUSTOMER_UPDATE, new KafkaMessage(updateRequest));
				break;
			case CX_REQUEST:
				updateRequest.setCxSmsOn(lastSentOn);
				commCapping.setCxSmsOn(lastSentOn);
				kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_CUSTOMER_UPDATE, new KafkaMessage(updateRequest));
				break;
			
			case PROMOTIONAL:
				updateRequest.setPromotionSmsOn(lastSentOn);
				commCapping.setPromotionalSmsOn(lastSentOn);
				kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_CUSTOMER_UPDATE, new KafkaMessage(updateRequest));
				break;
			
			case REFERRAL:
				updateRequest.setReferralSmsOn(lastSentOn);
				commCapping.setReferralSmsOn(lastSentOn);
				kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_CUSTOMER_UPDATE, new KafkaMessage(updateRequest));
				break;
			
			default:
				break;
			
			}
		}
		// updating the Cache
		if(!StringUtils.equalsAnyIgnoreCase(commCappingRequest.getRequestType(), CampaignTypeEnum.APPOINTMENT_REMINDER.getType(), CampaignTypeEnum.APPOINTMENT_RECALL.getType(),
				CampaignTypeEnum.APPOINTMENT_FORM.getType())) {
			//customerService.updateCustomerLastCommunicationTimeToCache(commCappingRequest.getCustomerId(), commCappingRequest.getSource(), campaignType.getType(), lastSentOn, 86400);
			KontactoDTO customer = new KontactoDTO();
			customer.setEcid(commCappingRequest.getEcid());
			customer.setId(commCappingRequest.getCustomerId());
			customerService.updateCampaignLastCommunicationTimeToCaches(commCappingRequest.getCustomerId(), commCappingRequest.getSource(), campaignType.getType(), lastSentOn, 86400, customer);
		}
		
		// updating the capping
		saveOrUpadteCustomerCommCapping(commCapping);

	}
	
	/**
	 * Queueing CustomerCommCapping update Event To Kafka to handle race conditions due to concurrent request for same customer.
	 * kafka partition key - customerId
	 * 
	 */
	private void pushCustomerCommCappingEventToKafka(Integer customerId, String requestType, String source, Integer ecid, Integer surveyId, Integer enterpriseId, boolean isPerSurveyRequest) {
		logger.info("pushing data in comm capping kafka ecid : {}",ecid);
		boolean isSurveyCommRestrictionEnable =  campaignService.isSurveyCommRestrictionEnable(enterpriseId);
		kafkaService.pushMessageToKafkaWithKey(KafkaTopicTypeEnum.CUSTOMER_COMM_CAPPING_UPDATE,
				new KafkaMessage(customerId, new UpdateCustomerCommCappingRequest(customerId, requestType, source,ecid, surveyId, isSurveyCommRestrictionEnable, isPerSurveyRequest)));
		kafkaService.pushMessageToKafkaWithKey(KafkaTopicTypeEnum.ACCOUNT_CUSTOMER_COMM_CAPPING_UPDATE,
				new KafkaMessage(ecid, new UpdateCustomerCommCappingRequest(customerId, requestType, source,ecid, surveyId, isSurveyCommRestrictionEnable, isPerSurveyRequest)));
	}

	private void postInboxEmailFailureActions(BaseCommunicationEntity reviewRequest, Exception e, Integer enterpriseId) {
		reviewRequest.setDeliveryStatus(RequestStatusEnum.FAILURE.getName());
		reviewRequest.setFailureReason(getFailureReasonForException(e, true, false));
		reviewRequest.setSentOn(new Date());
		if (reviewRequest instanceof Promotion) {
			promotionRepo.saveAndFlush((Promotion) reviewRequest);
			usageCommunicationService.updateDeliveryStatusForPromotion((Promotion) reviewRequest);
			communicationActivityService.publishCommunicationAcitivity(CommunicationAcitivityEventEnum.FAILURE, (Promotion) reviewRequest, null, enterpriseId);
		} else {
			reviewRequestRepo.saveAndFlush((ReviewRequest) reviewRequest);
			usageCommunicationService.updateDeliveryStatusForRequest((ReviewRequest) reviewRequest);
			communicationActivityService.publishCommunicationAcitivity(CommunicationAcitivityEventEnum.FAILURE, (ReviewRequest) reviewRequest, null, enterpriseId);
		}
	}
	
	private void postInboxEmailFailureActionsWithOnlyDBUpdate(BaseCommunicationEntity reviewRequest, Exception e, Integer enterpriseId) {
		reviewRequest.setDeliveryStatus(RequestStatusEnum.FAILURE.getName());
		reviewRequest.setSentOn(new Date());
		if (reviewRequest instanceof Promotion) {
			reviewRequest.setFailureReason(CampaignUtils.getSubstring(getFailureReasonForException(e, true, true), 190));
			promotionRepo.saveAndFlush((Promotion) reviewRequest);
		} else {
			reviewRequest.setFailureReason(CampaignUtils.getSubstring(getFailureReasonForException(e, true, true), 900));
			reviewRequestRepo.saveAndFlush((ReviewRequest) reviewRequest);
		}
	}
	
	private void postInboxEmailSuccessActions(BaseCommunicationEntity reviewRequest, Integer enterpriseId) {
		reviewRequest.setSentOn(new Date());
		reviewRequest.setDeliveryStatus(RequestStatusEnum.SUCCESS.getName());
		if (reviewRequest instanceof Promotion) {
			promotionRepo.saveAndFlush((Promotion) reviewRequest);
		} else {
			reviewRequestRepo.saveAndFlush((ReviewRequest) reviewRequest);
		}
		communicationActivityService.publishCommunicationAcitivity(CommunicationAcitivityEventEnum.SENT, reviewRequest, null, enterpriseId);
		sendRequestDataToElastic(reviewRequest);
	}
	
	private Promotion getInboxEmailPromotionRequest(InboxEmailRequestMessage request) {
		Promotion promotion = new Promotion();
		promotion.setCustomer(request.getCustomerId());
		promotion.setSource(Constants.RR_SOURCE_EMAIL);
		promotion.setTemplateId(request.getTemplateId());
		promotion.setBusinessId(request.getBusinessId());
		promotion.setCheckinId(request.getCheckinId());
		promotionRepo.saveAndFlush(promotion);
		return promotion;
	}
	
	private boolean sendPromotionRequestSms(Promotion promotion, KontactoDTO customer, Integer isMessengerCampaign) throws Exception {
		BusinessEnterpriseEntity business = businessService.getBusinessById(customer.getBusinessId().toString());
		logger.info("preparing to send promotion request sms for requestId : {}", promotion.getId());
		// add appropriate number of hours to make sure that if it is within the 8am-8pm window
		int numberOfHours = DateTimeUtils.calculateSmsDNDRemainingTimeForATimeZoneId(business.getTimezoneId());
		logger.info("For promotion request sms id {} and TimeZoneId {} the delay Hr {}", promotion.getId(), business.getTimezoneId(), numberOfHours);
		Campaign campaign = campaignSetupCachingService.getCampaignById(promotion.getCampaignId());
		if (numberOfHours > 0 && campaign != null) {
			// for new drip campaigns, DND sms needs to be scheduled for the next available date
			if (CampaignSchedulingTypeEnum.DRIP.getType().equalsIgnoreCase(campaign.getSchedulingType())) {
				// check if campaign falls in NEW drip category and any further valid dates left in execution range of the campaign
				Date newScheduleDate = dripCampaignService.scheduleDNDRequestForDrip(campaign, business.getTimezoneId());
				if (newScheduleDate != null) {
					// go for schedule
					logger.info("sendPromotionRequestSms : rescheduling Drip Campaign {} DND Customer {} to new time {}", customer.getId(), campaign.getId(), newScheduleDate);
					campaignEventManagementService.saveReviewEventFromRequest(campaignRRService.getDripDNDRREvent(promotion.getId(), promotion.getSource(), business.getId(),
							CampaignTypeEnum.PROMOTIONAL.getType(), promotion.getRequestedOn(), campaign, newScheduleDate));
					return true;
				}
			}
			// go for schedule
			campaignEventManagementService.saveReviewEventFromRequest(campaignRRService.getCreateReviewRequestSMS(promotion.getId(), promotion.getSource(), business.getId(),
					CampaignTypeEnum.PROMOTIONAL.getType(), promotion.getRequestedOn(), campaign, numberOfHours, business.getTimezoneId()));
			return true;
		}
		sendPromotionSmsToCustomer(business, promotion, customer, isMessengerCampaign, campaign);
		return false;
	}
	
	private void sendPromotionSmsToCustomer(BusinessEnterpriseEntity business, Promotion promotion, KontactoDTO customer, Integer isMessengerCampaign, Campaign campaign) throws Exception {
		BusinessSmsTemplate smsTemplate = businessSmsTemplateDao
				.getSMSTemplateByEnterpriseIdAndTemplateId(business.getEnterpriseId() != null ? business.getEnterpriseId() : business.getId(), promotion.getTemplateId());
		
		Integer isAppointmentCampaign = (campaign != null && campaign.getIsAppointmentTabCampaign() != null) ? campaign.getIsAppointmentTabCampaign() : 0;
		
		// it could be a free text campaign where we use global level template id
		if (smsTemplate == null && (isMessengerCampaign == 1 || isAppointmentCampaign == 1)) {
			BusinessSmsTemplate freeTextSmsTemplate = templateHelperService.getFreeTextSmsTemplateId();
			if (freeTextSmsTemplate != null && promotion.getTemplateId().equals(freeTextSmsTemplate.getId()))
				smsTemplate = freeTextSmsTemplate;
		}
		
		if (smsTemplate == null) {
			throw new CampaignException(ErrorCodes.INVALID_TEMPLATE, "No SMS template configured");
		}
		
		if (isMessengerCampaign == 1) {
			campaignSmsService.sendPromotionMessengerSmsCampaign(business, promotion, customer, smsTemplate);
			return;
		}
		
		if(isAppointmentCampaign == 1) {
			campaignSmsService.sendAppointmentSmsCampaign(business, promotion, customer, smsTemplate);
			return;		
		}

		
		campaignSmsService.sendPromotionSmsCampaign(business, promotion, customer, smsTemplate);
	}
	
	private boolean sendPromotionMailToCustomer(BusinessEnterpriseEntity business, Promotion promotionRequest, Campaign campaign, KontactoDTO customer, boolean checkCustomerEngagement, String emailCategory) throws Exception {
		if (validateEmailCampaign(promotionRequest, customer, business, campaign, 0, checkCustomerEngagement, null, emailCategory) == 0) {
			pushCampaignStatsDataToKafka(campaign, 0);
			return false;
		}
		
		//BIRDEYE-122978 sendOnPriority flag false for general campaign communication
		campaignEmailService.sendPromotionMailToCustomer(business, promotionRequest, campaign.getTemplateId(), customer, null, false, emailCategory);
		return true;
	}
	
	private boolean sendPromotionSMSRequest(Promotion promotion, KontactoDTO customer, BusinessEnterpriseEntity business, Campaign campaign, String smsCategory) throws Exception {
		if (validateSmsCampaign(promotion, customer, business, campaign, 0, null, smsCategory) == 0) {
			pushCampaignStatsDataToKafka(campaign, 0);
			return false;
		}
		boolean isScheduledDueToDND = sendPromotionRequestSms(promotion, customer, campaign.getIsMessengerCampaign() != null ? campaign.getIsMessengerCampaign() : 0);
		if (isScheduledDueToDND) {
			markRequestInDND(promotion);
			// delete the cache key
			deleteCustomerCacheForFailureCase(customer.getId(), Constants.RR_SOURCE_SMS, campaign.getType());
			return false;
		}
		return true;
	}
	
	private void postPromotionRequestSuccessActions(Promotion promotionRequest, Campaign campaign, BusinessEnterpriseEntity business, KontactoDTO customer) {
		logger.info("postPromotionRequestSuccessActions : performing success actions for request id :{}", promotionRequest.getId());
		updateRequestStatusToSuccess(promotionRequest, campaign.getType(), business.getEnterpriseIdElseBusinessId(), customer.getEcid(), campaign.getSurveyId(), false);
		pushCampaignStatsDataToKafka(campaign, 1);
		if (Constants.RR_SOURCE_EMAIL.equalsIgnoreCase(promotionRequest.getSource())) {
			// messenger event for sms campaign should also be moved here, though it requires a lot of metadata not present here, need to make explicit DB calls
			// to be handled here
			//BIRDEYE-127257 added request on field for messenger
			pushCampaignStatusToMessenger(promotionRequest, campaign, business, customer, null, promotionRequest.getRequestedOn());
		}
	}
	
	private void postPromotionRequestFailureActions(Promotion promotionRequest, Campaign campaign, Exception e) {
		logger.info("postPromotionRequestFailureActions : performing failure actions for promotion request id :{}", promotionRequest.getId());
		// failure reason as "failed" instead of error message as we need to push only the failure reasons that can be shown to the customer in status mails
		String failureReason = getFailureReasonForException(e, false, false);
		markReviewRequestFailure(promotionRequest, failureReason, null);
		pushCampaignStatsDataToKafka(campaign, 0);
	}
	
	private boolean quickSendSMSRequest(ReviewRequest reviewRequest, KontactoDTO customer, CampaignTypeEnum campaignType, BusinessEnterpriseEntity business, boolean isQuickSend,
			boolean retryOnShortenURlError, String communicationCategory) throws Exception {
		/// validate SMS Request in next Phase
		if (validateCustomerForSms(reviewRequest, customer, business, communicationCategory) == 0) {
			return false;
		}
		// validation if free trial account
		if (BooleanUtils.isFalse(freeTrialService.validateFreeTrialAccountLimits(reviewRequest.getRequestType(), business.getEnterpriseIdElseBusinessId()))){
			markReviewRequestFailure(reviewRequest, Constants.FREE_TRIAL_ACCOUNT_LIMIT_REACHED, business.getEnterpriseIdElseBusinessId());
			return false;
		}
		// Handling DND part in next phase
		boolean isScheduledDueToDND = sendReviewRequestSms(reviewRequest, customer, false, isQuickSend, retryOnShortenURlError);
		if (isScheduledDueToDND) { // NOSONAR
			// push dnd data to elastic
			markRequestInDND(reviewRequest);
			// delete the cache key
			deleteCustomerCacheForFailureCase(customer.getId(), Constants.RR_SOURCE_SMS, campaignType.getType());
			return false;
		}
		return true;
	}
	
	private boolean quickSendEmailRequest(ReviewRequest reviewRequest, KontactoDTO customer, BusinessEnterpriseEntity business, CampaignTypeEnum campaignType, Integer isReminder, String communicationCategory) throws Exception {
		// validate Email Request in next Phase
		if (validateCustomerForEmail(reviewRequest, customer, business.getEnterpriseIdElseBusinessId(), false, communicationCategory) == 0) {
			return false;
		}
		
		// BIRD-112185 | validation if free trial account
		if (BooleanUtils.isFalse(freeTrialService.validateFreeTrialAccountLimits(reviewRequest.getRequestType(), business.getEnterpriseIdElseBusinessId()))){
			markReviewRequestFailure(reviewRequest, Constants.FREE_TRIAL_ACCOUNT_LIMIT_REACHED, business.getEnterpriseIdElseBusinessId());
			return false;
		}
		
		// Send email.
		// added sendOnPriority true for quick send
		campaignEmailService.sendRequestMailToCustomer(business, reviewRequest, CoreUtils.isTrueForInteger(isReminder), campaignType.getType(), customer, null, null, true,
				communicationCategory);
		// update RR Status
		// updateRequestStatusToSuccess(reviewRequest); handled in post review request success actions
		// Campaign Status Update and handle Reminder if applicable
		if (isReminder == 0) {
			// Should success be checked for reminder creation too?
		}
		// Reminder processing. UPDATE REMINDER COUNT IN HEAD NODE
		// handleReminderPostRRSend(reviewRequest, isReminder,campaign)
		return true;
		
	}

	private void saveOrUpdateAccountCustomerCommCapping(AccountCustomerCommCapping accountCustomerCommCapping){
		try{
			accountCustomerCommCapping = accountCustomerCommCappingRepo.saveAndFlush(accountCustomerCommCapping);
		}catch (ObjectOptimisticLockingFailureException | DataIntegrityViolationException | StaleStateException e){
			logger.error("hibernate lock error while updating account customer capping for {} and account customer id {} - updating it again", accountCustomerCommCapping.getId(), accountCustomerCommCapping.getAccountCustomerId());
			AccountCustomerCommCapping existingAccountCommCapping = accountCustomerCommCappingRepo.findByAccountCustomerId(accountCustomerCommCapping.getAccountCustomerId());
			prepareAccountCustomerCapping(existingAccountCommCapping, accountCustomerCommCapping);
			existingAccountCommCapping.setAccountCustomerId(accountCustomerCommCapping.getAccountCustomerId());
			try {
				accountCustomerCommCappingRepo.saveAndFlush(existingAccountCommCapping);
			} catch (ObjectOptimisticLockingFailureException | DataIntegrityViolationException | StaleStateException exe) {
				logger.error("again hibernate lock error while updating account customer capping for {} and account customer id {} - updating it again", accountCustomerCommCapping.getId(), accountCustomerCommCapping.getAccountCustomerId());
				existingAccountCommCapping = accountCustomerCommCappingRepo.findByAccountCustomerId(accountCustomerCommCapping.getAccountCustomerId());
				prepareAccountCustomerCapping(existingAccountCommCapping, accountCustomerCommCapping);
				existingAccountCommCapping.setAccountCustomerId(accountCustomerCommCapping.getAccountCustomerId());
				accountCustomerCommCappingRepo.saveAndFlush(existingAccountCommCapping);
			} catch (Exception exe) {
				logger.error("again error while updating the account customer capping for id {} and account customer id {} error :: {}", accountCustomerCommCapping.getId(), accountCustomerCommCapping.getAccountCustomerId(), exe);
			}
		} catch (Exception exe) {
			logger.error("error while updating the account customer capping for id {} and account customer id {} error :: {}", accountCustomerCommCapping.getId(), accountCustomerCommCapping.getAccountCustomerId(), exe);
		}
	}

	
	private void saveOrUpadteCustomerCommCapping(CustomerCommCapping commCapping) {
		try {
			commCapping = customerCommCappingRepo.saveAndFlush(commCapping);
		} catch (ObjectOptimisticLockingFailureException | DataIntegrityViolationException | StaleStateException e) {
			logger.error("hibernate lock error while updating customer capping for {} and customer id {} - updating it again", commCapping.getId(), commCapping.getCustomerId());
			CustomerCommCapping existingCommCapping = customerCommCappingRepo.getCustomerLastCommunicationMessage(commCapping.getCustomerId());
			prepareCustomerCapping(existingCommCapping, commCapping);
			existingCommCapping.setCustomerId(commCapping.getCustomerId());
			try {
				customerCommCappingRepo.saveAndFlush(existingCommCapping);
			} catch (ObjectOptimisticLockingFailureException | DataIntegrityViolationException | StaleStateException exe) {
				logger.error("again hibernate lock error while updating customer capping for {} and customer id {} - updating it again", commCapping.getId(), commCapping.getCustomerId());
				existingCommCapping = customerCommCappingRepo.getCustomerLastCommunicationMessage(commCapping.getCustomerId());
				prepareCustomerCapping(existingCommCapping, commCapping);
				existingCommCapping.setCustomerId(commCapping.getCustomerId());
				customerCommCappingRepo.saveAndFlush(existingCommCapping);
			} catch (Exception exe) {
				logger.error("again error while updating the customer capping for id {} and customer id {} error :: {}", commCapping.getId(), commCapping.getCustomerId(), exe);
			}
		} catch (Exception exe) {
			logger.error("error while updating the customer capping for id {} and customer id {} error :: {}", commCapping.getId(), commCapping.getCustomerId(), exe);
		}
	}


	private void prepareAccountCustomerCapping(AccountCustomerCommCapping existingCommCapping,AccountCustomerCommCapping commCapping ){
		if (existingCommCapping == null) {
			existingCommCapping = new AccountCustomerCommCapping();
		}
		if (commCapping.getRrEmailOn().after(new Date(0))) {
			existingCommCapping.setRrEmailOn(commCapping.getRrEmailOn());
		}
		if (commCapping.getRrSmsOn().after(new Date(0))) {
			existingCommCapping.setRrSmsOn(commCapping.getRrSmsOn());
		}
		if (commCapping.getCxEmailOn().after(new Date(0))) {
			existingCommCapping.setCxEmailOn(commCapping.getCxEmailOn());
		}
		if (commCapping.getCxSmsOn().after(new Date(0))) {
			existingCommCapping.setCxSmsOn(commCapping.getCxSmsOn());
		}
		if (commCapping.getReferralEmailOn().after(new Date(0))) {
			existingCommCapping.setReferralEmailOn(commCapping.getReferralEmailOn());
		}
		if (commCapping.getReferralSmsOn().after(new Date(0))) {
			existingCommCapping.setReferralSmsOn(commCapping.getReferralSmsOn());
		}
		if (commCapping.getSurveyEmailOn().after(new Date(0))) {
			existingCommCapping.setSurveyEmailOn(commCapping.getSurveyEmailOn());
		}
		if (commCapping.getSurveySmsOn().after(new Date(0))) {
			existingCommCapping.setSurveySmsOn(commCapping.getSurveySmsOn());
		}
		if (commCapping.getPromotionalEmailOn().after(new Date(0))) {
			existingCommCapping.setPromotionalEmailOn(commCapping.getPromotionalEmailOn());
		}
		if (commCapping.getPromotionalSmsOn().after(new Date(0))) {
			existingCommCapping.setPromotionalSmsOn(commCapping.getPromotionalSmsOn());
		}
	}
	
	private void prepareCustomerCapping(CustomerCommCapping existingCommCapping, CustomerCommCapping commCapping) {
		if (existingCommCapping == null) {
			existingCommCapping = new CustomerCommCapping();
		}
		if (commCapping.getRrEmailOn().after(new Date(0))) {
			existingCommCapping.setRrEmailOn(commCapping.getRrEmailOn());
		}
		if (commCapping.getRrSmsOn().after(new Date(0))) {
			existingCommCapping.setRrSmsOn(commCapping.getRrSmsOn());
		}
		if (commCapping.getCxEmailOn().after(new Date(0))) {
			existingCommCapping.setCxEmailOn(commCapping.getCxEmailOn());
		}
		if (commCapping.getCxSmsOn().after(new Date(0))) {
			existingCommCapping.setCxSmsOn(commCapping.getCxSmsOn());
		}
		if (commCapping.getReferralEmailOn().after(new Date(0))) {
			existingCommCapping.setReferralEmailOn(commCapping.getReferralEmailOn());
		}
		if (commCapping.getReferralSmsOn().after(new Date(0))) {
			existingCommCapping.setReferralSmsOn(commCapping.getReferralSmsOn());
		}
		if (commCapping.getSurveyEmailOn().after(new Date(0))) {
			existingCommCapping.setSurveyEmailOn(commCapping.getSurveyEmailOn());
		}
		if (commCapping.getSurveySmsOn().after(new Date(0))) {
			existingCommCapping.setSurveySmsOn(commCapping.getSurveySmsOn());
		}
		if (commCapping.getPromotionalEmailOn().after(new Date(0))) {
			existingCommCapping.setPromotionalEmailOn(commCapping.getPromotionalEmailOn());
		}
		if (commCapping.getPromotionalSmsOn().after(new Date(0))) {
			existingCommCapping.setPromotionalSmsOn(commCapping.getPromotionalSmsOn());
		}
	}
	
	// Send Email
	private boolean sendEmailRequest(ReviewRequest reviewRequest, KontactoDTO customer, BusinessEnterpriseEntity business, Campaign campaign, Integer isReminder, boolean checkCustomerEngagement, List<Date> appointmentScheduledDates, String communicationCategory) throws Exception {
		if (validateEmailCampaign(reviewRequest, customer, business, campaign, isReminder, checkCustomerEngagement, appointmentScheduledDates, communicationCategory) == 0) {
			if (isReminder == 0) {
				pushCampaignStatsDataToKafka(campaign, 0);
			}
			return false;
		}
		
		// Send email.
		campaignEmailService.sendRequestMailToCustomer(business, reviewRequest, CoreUtils.isTrueForInteger(isReminder), campaign.getType(), customer, null, campaign, false,
				communicationCategory);
		return true;
	}
	
	// Send SMS
	private boolean sendRRSMSRequest(ReviewRequest reviewRequest, KontactoDTO customer, BusinessEnterpriseEntity business, Campaign campaign,boolean applyDnd, List<Date> appointmentScheduledDates, String communicationCategory) throws Exception {
		if (validateSmsCampaign(reviewRequest, customer, business, campaign, 0, appointmentScheduledDates, communicationCategory) == 0) {
			pushCampaignStatsDataToKafka(campaign, 0);
			return false;
		}
		
		boolean isScheduledDueToDND = sendReviewRequestSms(reviewRequest, customer, applyDnd, false, true);
		if (isScheduledDueToDND) {
			markRequestInDND(reviewRequest);
			// delete the cache key
			deleteCustomerCacheForFailureCase(customer.getId(), Constants.RR_SOURCE_SMS, campaign.getType());
			return false;
		}
		
		return true;
	}
	
	private void postReviewRequestFailureActions(Integer isReminder, ReviewRequest reviewRequest, Campaign campaign, Exception e, Integer enterpriseId) {
		logger.info("postReviewRequestFailureActions : performing failure actions for request id :{}", reviewRequest.getId());
		if (isReminder == 0) {
			pushCampaignStatsDataToKafka(campaign, 0);
		}
		if (reviewRequest.getParentRequestId() != null) {
			logger.info("Updating parent review request reminder count for id {}", reviewRequest.getId());
			campaignRRService.incrementHeadRequestReminderCount(reviewRequest.getParentRequestId());
		}
		
		markRRFailure(reviewRequest, e, enterpriseId);
	}
	
	private void markRRFailure(ReviewRequest reviewRequest, Exception e, Integer enterpriseId) {
		String failureReason = getFailureReasonForException(e, false, false);
		markReviewRequestFailure(reviewRequest, failureReason, enterpriseId);
	}
	
	private String getFailureReasonForException(Exception e, boolean isInboxEmailFailure, boolean isExternalTemplateCreationRequest) {
		/*
		 * failure reason as "failed" instead of error message as we need to push only the failure reasons that can be shown to the customer in status mails
		 */
		String failureReason = Constants.RR_FAILURE_REASON_GENERIC;
		if (e instanceof SmsUrlShortenException || e instanceof InValidTimeZoneException || validateIfFreeTrialLimitExhaustedError(e, isInboxEmailFailure) || isExternalTemplateCreationRequest) {
			failureReason = e.getMessage();
		}
		return failureReason;
	}

	private boolean validateIfFreeTrialLimitExhaustedError(Exception e, boolean isInboxEmailFailure) {
		if (BooleanUtils.isTrue(isInboxEmailFailure) && e instanceof CampaignException) {
			CampaignException cex = (CampaignException) e;
			return (cex.getCode() != null && cex.getCode().getValue() == 3002);
		}
		return false;
	}
	
	// TODO: It should be single event object
	// TODO: NEED DISCUSSION IF THIS NEEDS TO BE MOVED TO NEXUS CALLBACK
	private void postReviewRequestSuccessActions(ReviewRequest reviewRequest, Campaign campaign, BusinessEnterpriseEntity business, KontactoDTO customer, String campaignType, Integer isReminder) {
		logger.info("postReviewRequestSuccessActions : performing success actions for request id :{}", reviewRequest.getId());
		// increment parent request reminder count.
		if (reviewRequest.getParentRequestId() != null) {
			logger.info("Updating parent review request reminder count for id {}", reviewRequest.getId());
			campaignRRService.incrementHeadRequestReminderCount(reviewRequest.getParentRequestId());
		}
		Integer surveyId = campaign != null ? campaign.getSurveyId() : reviewRequest.getSurveyId();
		Integer overRideCommRestriction = campaign != null ? campaign.getBypassCommRestriction() : 0;
		CampaignAccountSettings campaignAccountSettings = getCampaignAccountSetting(business);
		boolean isPerSurveyRequest = false;
		if(StringUtils.equalsIgnoreCase(CampaignTypeEnum.SURVEY_REQUEST.getType(), campaignType)) {
			isPerSurveyRequest = isPerSurveyRequest(campaignAccountSettings, overRideCommRestriction);
		}
		// ES write
		updateRequestStatusToSuccess(reviewRequest, campaignType, business.getEnterpriseIdElseBusinessId(), customer.getEcid(), surveyId, isPerSurveyRequest);
		
		campaignReminderService.scheduleReminder(reviewRequest, campaign);
		// Campaign Status Update
		if (campaign != null && isReminder == 0) {
			pushCampaignStatsDataToKafka(campaign, 1);
		}
		
		if (Constants.RR_SOURCE_EMAIL.equalsIgnoreCase(reviewRequest.getSource())) {
			// messenger event for sms campaign should also be moved here, though it requires a lot of metadata not present here, need to make explicit DB calls
			// to be handled here
			//BIRDEYE-127257 added request on field for messenger
			pushCampaignStatusToMessenger(reviewRequest, campaign, business, customer, null, reviewRequest.getRequestDate());
		}
	}
	//BIRDEYE-127257 added created at field for messenger
	// this is used for 3 cases - Campaign Emails, Emails Via Inbox, Quick Send Emails
	private void pushCampaignStatusToMessenger(BaseCommunicationEntity reviewRequest, Campaign campaign, BusinessEnterpriseEntity business, KontactoDTO customer,
			InboxEmailRequestMessage inboxEmailRequestMessage, Date requestCreateDate) {
		// BIRD-133895 - Ignore In case of Template Content Prepare Request
		if (CampaignUtils.isExternalTemplateContentPreparationRequest(inboxEmailRequestMessage)) {
			return;
		}
		logger.info("pushing email via inbox request {} status event to Messenger", reviewRequest.getId());
		MessengerEmailCampaignEvent messengerEmailCampaignEvent = new MessengerEmailCampaignEvent();
		messengerEmailCampaignEvent.setBusinessId(business.getId());
		messengerEmailCampaignEvent.setLocationId(business.getId());
		messengerEmailCampaignEvent.setEnterpriseId(business.getEnterpriseIdElseBusinessId());
		messengerEmailCampaignEvent.setRequestType(reviewRequest.getRequestType());
		messengerEmailCampaignEvent.setCampaignId(campaign != null ? campaign.getId() : (inboxEmailRequestMessage == null ? -100 : null)); // campaign id -100 for quicksend
		messengerEmailCampaignEvent.setCampaignType(campaign != null ? campaign.getRunType() : null);
		messengerEmailCampaignEvent.setCustomerId(reviewRequest.getCustId());
		messengerEmailCampaignEvent.setFromEmailId(inboxEmailRequestMessage != null ? inboxEmailRequestMessage.getFromEmailId() : business.getBazaarifyEmailId());
		messengerEmailCampaignEvent.setReviewRequestId(reviewRequest.getId());
		messengerEmailCampaignEvent.setStatus(reviewRequest.getDeliveryStatus());
		messengerEmailCampaignEvent.setFailureReason(reviewRequest.getFailureReason());
		messengerEmailCampaignEvent.setTemplateId(reviewRequest.getTemplateId()); //
		messengerEmailCampaignEvent.setToEmailId(customer.getEmailId());
		messengerEmailCampaignEvent.setUserId(campaign != null ? campaign.getCreatedBy() : (inboxEmailRequestMessage == null ? getUserIdForQuickSend(reviewRequest.getId()) : null));
		messengerEmailCampaignEvent.setTemplateName(getTemplateName(reviewRequest.getTemplateId()));
		messengerEmailCampaignEvent.setExternalUId(inboxEmailRequestMessage != null ? inboxEmailRequestMessage.getExternalUId() : null);
		//BIRDEYE-127257 added sent on field for messenger
		messengerEmailCampaignEvent.setSentOn(reviewRequest.getSentOn() != null ? reviewRequest.getSentOn() : new Date());
		messengerEmailCampaignEvent.setCreatedAt(requestCreateDate);
		if (StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.APPOINTMENT_REMINDER.getType())) {
			AppointmentRRMapping audit = appointmentReminderAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
			messengerEmailCampaignEvent.setAppointmentId(audit != null ? audit.getAppointmentId() : null);
		}
		
		kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.MESSENGER_CAMPAIGN_EVENT, new KafkaMessage(messengerEmailCampaignEvent));
	}
	
	private String getTemplateName(Integer templateId) {
		return emailTemplateRepo.findTemplateNameById(templateId);
	}
	
	private Integer getUserIdForQuickSend(Long reviewRequestId) {
		String userId = reviewRequestService.getUserIdForReviewRequestId(reviewRequestId.toString());
		return StringUtils.isNotBlank(userId) ? Integer.parseInt(userId) : null;
	}
	
	private void updateRequestStatusToSuccess(BaseCommunicationEntity reviewRequest, String campaignType, Integer enterpriseId, Integer ecid, Integer surveyId, boolean isPerSurveyRequest) {
		reviewRequest.setDeliveryStatus(RequestStatusEnum.SUCCESS.getName());
		reviewRequest.setSentOn(new Date());
		if (reviewRequest instanceof ReviewRequest) {
			reviewRequestRepo.saveAndFlush((ReviewRequest) reviewRequest);
		} else {
			promotionRepo.saveAndFlush((Promotion) reviewRequest);
		}
		sendRequestDataToElastic(reviewRequest);
		communicationActivityService.publishCommunicationAcitivity(CommunicationAcitivityEventEnum.SENT, reviewRequest, null, enterpriseId);
		pushCustomerCommCappingEventToKafka(reviewRequest.getCustId(), campaignType, reviewRequest.getSource(), ecid, surveyId, enterpriseId, isPerSurveyRequest);
		updateCustomerHistoryInKafka(reviewRequest);
	}
	
	/*
	 * this will be used for inserting (not update - for update, we have update method in usage communication service, which will be called when webhook
	 * status is received) request data of all types ( failed, dnd, success) to elastic
	 */
	private void sendRequestDataToElastic(BaseCommunicationEntity reviewRequest) {
		CampaignTypeEnum campaignType = CampaignTypeEnum.getSupportedType(reviewRequest.getRequestType());
		if (CampaignTypeEnum.CX_REQUEST == campaignType) {
			usageCommunicationService.pushCXSentRequest((ReviewRequest) reviewRequest, null, reviewRequest.getSentOn(), null);
		} else if (CampaignTypeEnum.REVIEW_REQUEST == campaignType || CampaignTypeEnum.SURVEY_REQUEST == campaignType) {
			usageCommunicationService.pushRRSentRequest((ReviewRequest) reviewRequest, reviewRequest.getSentOn(), null);
		} else if (CampaignTypeEnum.REFERRAL == campaignType) {
			usageCommunicationService.pushReferralSentRequest((ReviewRequest) reviewRequest, reviewRequest.getSentOn(), null);
		} else if (CampaignTypeEnum.APPOINTMENT_REMINDER == campaignType) {
			AppointmentRRMapping appointmentReminderRequestAudit = appointmentReminderAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
			usageCommunicationService.pushAppointmentReminderSentRequest((ReviewRequest) reviewRequest, reviewRequest.getSentOn(), appointmentReminderRequestAudit.getAppointmentId());
		} else if (CampaignTypeEnum.APPOINTMENT_RECALL == campaignType) {
			AppointmentRRMapping appointmentReminderRequestAudit = appointmentReminderAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
			usageCommunicationService.pushAppointmentRecallSentRequest((ReviewRequest) reviewRequest, reviewRequest.getSentOn(), appointmentReminderRequestAudit.getAppointmentId(),
					appointmentReminderRequestAudit.getRecallId());
		} else if (CampaignTypeEnum.APPOINTMENT_FORM == campaignType) {
			AppointmentRRMapping appointmentReminderRequestAudit = appointmentReminderAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
			usageCommunicationService.pushAppointmentFormSentRequest((ReviewRequest) reviewRequest, reviewRequest.getSentOn(), appointmentReminderRequestAudit.getAppointmentId());
		} else { // Promotion Case
			usageCommunicationService.pushPromotionSentRequest((Promotion) reviewRequest, reviewRequest.getSentOn());
		}
	}
	
	/**
	 * Update customer history on platform for campaign executed on customer;
	 * 
	 * @param customer
	 * @param reviewRequest
	 */
	private void updateCustomerHistoryInKafka(BaseCommunicationEntity reviewRequest) {
		try {
//			logger.info("Sending update customer history message for request : {}", reviewRequest.getId());
			UpdateCampaignCustomerHistoryMessage customerHistoryMessage = new UpdateCampaignCustomerHistoryMessage();
			customerHistoryMessage.setRequestId(reviewRequest.getId());
			String eventDesc = null;
			if ("failure".equalsIgnoreCase(reviewRequest.getDeliveryStatus())) {
				eventDesc = "Alert: " + (reviewRequest.getFailureReason() == null ? "Unknown error" : reviewRequest.getFailureReason());
			} else {
				eventDesc = "Sent";
			}
			customerHistoryMessage.setEventDesc(eventDesc);
			customerHistoryMessage.setActivityType("create");
			customerHistoryMessage.setEventName(CampaignTypeEnum.getAlias(reviewRequest.getRequestType()));
			if (reviewRequest instanceof Promotion) {
				customerHistoryMessage.setPromotionRequest(true);
			}
			customerHistoryMessage.setCustomerId(reviewRequest.getCustId());
			kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_CUSTOMER_UPDATE_HISTORY, new KafkaMessage(customerHistoryMessage.getRequestId(), customerHistoryMessage));
		} catch (Exception exp) {
			logger.error("Unable to send update customer history message for request : {}", reviewRequest.getId(), exp);
		}
	}
	private CampaignAccountSettings getCampaignAccountSetting(BusinessEnterpriseEntity business) {
		List<CampaignAccountSettings> campaignSettings = templateConfigService.getCampaignAccountSettings(business.getEnterpriseId() != null ? business.getEnterpriseId() : business.getId());
		if (CollectionUtils.isNotEmpty(campaignSettings)) {
			return campaignSettings.get(0);
		}
		return null;
	}
	
	private int validateCampaign(BaseCommunicationEntity reviewRequest, BusinessEnterpriseEntity business, Campaign campaign, Integer isReminder, KontactoDTO customer) {
		if (business.getClosed() == 1 || business.getStatus().equals("inactive")) {
			markReviewRequestFailure(reviewRequest, "Business is suspended", business.getEnterpriseIdElseBusinessId());
			return 0;
		} else if (campaign != null && campaign.getIsDeleted() == 1) {
			markReviewRequestFailure(reviewRequest, "Campaign is already deleted", business.getEnterpriseIdElseBusinessId());
			return 0;
		}
		// isDnd and Reminder and Ongoing cases don't need this, this is being handled at drip batch execution level, don't need this here as we can always
		// have
		// throttling even for instant campaigns and they maybe sent later than thier execution date, so commenting this
		
		else if (campaign != null && isReminder == 0 && CampaignStatusEnum.PAUSED.getStatus() == campaign.getStatus()) {
			markReviewRequestFailure(reviewRequest, "Campaign is paused", business.getEnterpriseIdElseBusinessId());
			logger.warn("Camapign is paused because of status: {}", campaign.getStatus());
			return 0;
		}
		// this is for instant campaign only
		else if (campaign != null && isReminder == 0 && CampaignStatusEnum.STOPPED.getStatus() == campaign.getStatus()) {
			markReviewRequestFailure(reviewRequest, "Campaign is stopped", business.getEnterpriseIdElseBusinessId());
			logger.warn("Camapign is stopped because of status: {}", campaign.getStatus());
			return 0;
		}
		// to update campaign stats so that campaign completion can be marked appropriately - BIRDEYE-72588
		else if(customer == null)
		{
			markReviewRequestFailure(reviewRequest, "Contact not found", business.getEnterpriseIdElseBusinessId());
			logger.warn("Customer {} not found", reviewRequest.getCustId());
			return 0;
		}
		// Product feature validation for campaigns
		else if (campaign != null && BooleanUtils.isTrue(isInvalidCampaignBasedOnFeatureFlag(reviewRequest, business, campaign))) {
			logger.warn("validateCampaignUponFeatureFlag :: For review request {}, campaign id {} and type {}, the feature flag is disabled", reviewRequest.getId(), campaign.getId(),
					campaign.getCampaignType());
			markReviewRequestFailure(reviewRequest, "Feature Disabled", business.getEnterpriseIdElseBusinessId());
			dispatchCampaignPauseAndDeleteEvent(campaign);
			return 0;
		}
		return 1;
	}
	
	/**
	 * 
	 * BIRD-53931 | API to send event to pause/delete the given campaign.
	 * 
	 * @param campaign
	 * 
	 */
	private void dispatchCampaignPauseAndDeleteEvent(Campaign campaign) {
		if (StringUtils.equalsIgnoreCase(campaign.getCampaignType(), CampaignTypeEnum.PROMOTIONAL.getType())) {
			return;
		}
		
		Integer status = CampaignStatusEnum.PAUSED.getStatus();
		
		logger.info("dispatchCampaignPauseAndDeleteEvent :: For campaign id {} and type {}, sending pause and delete event with status {}", campaign.getId(), campaign.getCampaignType(), status);
		Boolean isEventDispatched = kafkaService.pushMessageToKafkaAcknowledged(KafkaTopicTypeEnum.CAMPAIGN_STATUS_UPDATE.getType(), String.valueOf(campaign.getId()),
				new CampaignUpdateStatusRequest(campaign.getId(), status));
		logger.info("dispatchCampaignPauseAndDeleteEvent :: For campaign id {} and type {}, pause and delete event with status {} event dispatched is {}", campaign.getId(), campaign.getCampaignType(), status,
				isEventDispatched);
	}
	
	/**
	 * 
	 * BIRD-53931 | API to validate if the campaign is invalid based upon feature flag
	 * 
	 * @param reviewRequest, business, campaign
	 * 
	 */
	private Boolean isInvalidCampaignBasedOnFeatureFlag(BaseCommunicationEntity reviewRequest, BusinessEnterpriseEntity business, Campaign campaign) {
		
		// 1.1 Check if split campaign, no validation for split campaign
		if (BooleanUtils.isTrue(CoreUtils.getBooleanValueFromInteger(campaign.getIsSplitCampaign()))) {
			return false;
		}
		
		// 1.2 Check if campaign type lies in allowed campaign types for validation
		if (BooleanUtils.isFalse(CampaignUtils.isRequestTypeInValidTypes(campaign.getCampaignType()))) {
			return false;
		}
		
		// 1.3 Fetch Product Feature Flags
		ProductFeatureRequest featureRequest = null;
		try {
			featureRequest = cacheService.getProductFeatureForBusiness(business.getEnterpriseIdElseBusinessId());
		} catch (Exception e) {
			logger.error("isInvalidCampaignBasedOnFeatureFlag :: Exception occcurred while fetching feature flag for review request {} :: {}", reviewRequest.getId(), ExceptionUtils.getStackTrace(e));
			return false;
		}
		
		// 1.4 Check if feature flag is null
		if (featureRequest == null) {
			logger.error("isInvalidCampaignBasedOnFeatureFlag :: Feature flag is null for review request {}", reviewRequest.getId());
			return false;
		}
		
		// 1.5 Special Handling for Promotion Campaign based upon Source
		if (StringUtils.equalsIgnoreCase(campaign.getCampaignType(), CampaignTypeEnum.PROMOTIONAL.getType())) {
			return BooleanUtils.isFalse(CampaignUtils.isFeatureFlagEnabledForPromotion(featureRequest, reviewRequest.getSource()));
		}
		
		
		// 1.6 Validate if concerned feature flag is disabled, mark review request failure
		if (BooleanUtils.isFalse(CampaignUtils.isFeatureFlagEnabledForCampaignType(featureRequest, campaign.getCampaignType()))) {
			return true;
		}
		
		return false;
	}

	private int validateEmailCampaign(BaseCommunicationEntity reviewRequest, KontactoDTO customer, BusinessEnterpriseEntity business, Campaign campaign, Integer isReminder, boolean checkCustomerEnagement, List<Date> appointmentScheduledDates, String communicationCategory) {
		// just valdate business Time zone
//		DateTimeUtils.validateBusinessTimeZone(business.getTimezone());
		boolean isSurveyRestrictionEnable = campaignService.isSurveyCommRestrictionEnable(business.getEnterpriseIdElseBusinessId());
		
		if (validateCampaign(reviewRequest, business, campaign, isReminder, customer) == 0) {
			return 0;
		} else if (CoreUtils.isNotValidBusiness(business)) {
			markReviewRequestFailure(reviewRequest, getBusinessStatusReason(business.getActivationStatus()), business.getEnterpriseIdElseBusinessId());
			logger.warn("Business {} is cancelled/suspended {}", business.getId(), business.getActivationStatus());
			return 0;
		} else if (validateCustomerForEmail(reviewRequest, customer, business.getEnterpriseId(), checkCustomerEnagement, communicationCategory) == 0) {
			return 0;
		} else if (isReminder == 0 && isRequestMailAlreadySent(business, customer, campaign, isSurveyRestrictionEnable)) {
			Integer mailResendFrequency = getCampaignCommFrequency(business, campaign, isSurveyRestrictionEnable);
			markReviewRequestFailure(reviewRequest, "Email already sent in last " + mailResendFrequency + " days", business.getEnterpriseIdElseBusinessId());
			logger.warn("For business {} and {} email already sent in last {} days", business.getId(), campaign.getCampaignType(), mailResendFrequency);
			return 0;
		}
		else if (isDuplicateAppointmentCommunication(reviewRequest, business, customer, campaign.getType(), Constants.RR_SOURCE_EMAIL, appointmentScheduledDates, campaign.getRunType())) {
			if(StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.APPOINTMENT_REMINDER.getType()) && CacheManager.getInstance().getCache(RestrictAppointmentCommunicationCache.class).isEnterpriseIdRestrictedForTimeWindow(business.getEnterpriseIdElseBusinessId())) {
				markReviewRequestDropped((ReviewRequest) reviewRequest, "Duplicate request", false, true, business.getEnterpriseIdElseBusinessId());
			} else {
				// BIRDEYE-115745
				markReviewRequestDropped((ReviewRequest) reviewRequest, "Duplicate request", false, false, business.getEnterpriseIdElseBusinessId());
			}
			
			logger.info("Duplicate appointment request {} for business {} customer {} rTpe {} commType {}", reviewRequest.getId(), business.getId(), customer.getId(),
					campaign.getCampaignType(), Constants.RR_SOURCE_EMAIL);
			return 0;
		} else if (BooleanUtils.isFalse(freeTrialService.validateFreeTrialAccountLimits(reviewRequest.getRequestType(), business.getEnterpriseIdElseBusinessId()))) {
			markReviewRequestFailure(reviewRequest, Constants.FREE_TRIAL_ACCOUNT_LIMIT_REACHED, business.getEnterpriseIdElseBusinessId());
			return 0;
		}
		return 1;
	}
	
	/**
	 * 
	 * @param activationStatus
	 * @return
	 */
	private String getBusinessStatusReason(String activationStatus) {
		if (StringUtils.isNotBlank(activationStatus) && StringUtils.equalsAnyIgnoreCase(activationStatus, "suspended", "cancelled")) {
			return "Business is already suspended.";
		}
		return null;
	}
	
	private int validateCustomerForEmail(BaseCommunicationEntity reviewRequest, KontactoDTO customer, Integer enterpriseId, boolean checkCustomerEngagement, String communicationCategory) {
		if (!Objects.isNull(customer.getCommPreferences()) && !CommunicationCategoryUtils.isCustomerOptedInForEmailCategory(communicationCategory, customer.getCommPreferences())) {
			markReviewRequestFailure(reviewRequest, CommunicationCategoryUtils.prepareCommunicationFailureMessageForEmail(communicationCategory, customer.getCommPreferences()),
					enterpriseId);
			logger.warn("Customer is unsubscribed!");
			return 0;
		} else if (StringUtils.isBlank(customer.getEmailId())) {
			markReviewRequestFailure(reviewRequest, "Email id not present for this contact", enterpriseId);
			logger.warn("Email id not present for this customer {}", customer.getId());
			return 0;
		} else if (!CoreUtils.isEmailIdValid(customer.getEmailId())) {
			markReviewRequestFailure(reviewRequest, "Email id : " + customer.getEmailId() + " is invalid", enterpriseId);
			logger.warn("email id is invalid for customer id {}", customer.getId());
			return 0;
		} else if (BooleanUtils.isTrue(customer.getBlocked())) {
			markReviewRequestFailure(reviewRequest, "Contact has been blocked from email", enterpriseId);
			logger.error("Customer {} has been blocked.", customer.getId());
			return 0;
		} else if (checkCustomerEngagement && customer.getEmailEngaged() != null && customer.getEmailEngaged() == 0) {
			markReviewRequestFailure(reviewRequest, "Unengaged customer", enterpriseId);
			logger.warn("unengaged customer with customer id {}", customer.getId());
			return 0;
		} else if(StringUtils.isBlank(customer.getReferralCode()) && StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.REFERRAL.getType())) {
			markReviewRequestFailure(reviewRequest, "Customer Missing Referral Code", enterpriseId);
			logger.warn("Customer {} missing referral code!", customer.getId());
			return 0;
		}
		return 1;
	}
	
	private void markReviewRequestFailure(BaseCommunicationEntity reviewRequest, String failureReason, Integer enterpriseId) {
		reviewRequest.setDeliveryStatus(RequestStatusEnum.FAILURE.getName());
		reviewRequest.setFailureReason(failureReason);
		reviewRequest.setSentOn(new Date());
		if (reviewRequest instanceof Promotion) {
			promotionRepo.saveAndFlush((Promotion) reviewRequest);
		} else {
			reviewRequestDAO.saveReviewRequest(((ReviewRequest) reviewRequest));
		}
		sendRequestDataToElastic(reviewRequest);
		communicationActivityService.publishCommunicationAcitivity(CommunicationAcitivityEventEnum.FAILURE,reviewRequest, null, enterpriseId);
		updateCustomerHistoryInKafka(reviewRequest);
		if (reviewRequest instanceof  ReviewRequest) {
			publishFailureEventForReferralWebhooks(((ReviewRequest) reviewRequest));
		}
	}
	
	/*
	 * publish request failure event to topic: 'referral-requests-delivery-webhooks'
	 */
	private void publishFailureEventForReferralWebhooks(ReviewRequest reviewRequest) {
		if (!Objects.isNull(reviewRequest) && BooleanUtils.isTrue(ReferralWebhookUtils
				.isValidRequestForReferralWebhook(BusinessUtils.getEnterpriseId(cacheService.getBusinessById(reviewRequest.getBusinessId())), reviewRequest.getRequestType()))) {
			ReferralWebhookDeliveryEvent referralWebhookDeliveryEvent = ReferralWebhookUtils.prepareReferralWebhookDeliveryEvent(Constants.FAILURE_STATUS, reviewRequest,
					StringUtils.EMPTY);
			
			kafkaService.pushMessageToKafkaAcknowledged(KafkaTopicTypeEnum.REFERRAL_REQUESTS_DELIVERY_WEBHOOKS.getType(), null, referralWebhookDeliveryEvent);
		}
		
	}

	private void pushCampaignStatsDataToKafka(Campaign campaign, Integer status) {
		if (CampaignRunTypeEnum.ONGOING.getRunType().equalsIgnoreCase(campaign.getRunType()))
			return;
		
		CampaignStatsKafkaMessage kafkaMessage = new CampaignStatsKafkaMessage();
		kafkaMessage.setCampaignId(campaign.getId());
		kafkaMessage.setStatus(status);
		String topicName = CacheManager.getInstance().getCache(KafkaTopicCache.class).getKafkaTopic(KafkaTopicTypeEnum.CAMPAIGN_STATS.getType());
		if (!kafkaService.pushMessageToKafkaAcknowledged(topicName, campaign.getId().toString(), kafkaMessage)) {
			logger.error("error while pushing campaign stats to kafka for campaign id {} and topic {}", campaign.getId(), KafkaTopicTypeEnum.CAMPAIGN_STATS.getType());
		}
		logger.info("campaign stats pushed to kafka successfully for campaign id {} and topic {}", campaign.getId(), KafkaTopicTypeEnum.CAMPAIGN_STATS.getType());
	}
	
	private int validateSmsCampaign(BaseCommunicationEntity reviewRequest, KontactoDTO customer, BusinessEnterpriseEntity business, Campaign campaign, Integer isReminder, List<Date> appointmentScheduledDates, String communicationCategory) {
		// just valdate business Time zone
		boolean isSurveyRestrictionEnable = campaignService.isSurveyCommRestrictionEnable(business.getEnterpriseIdElseBusinessId());
		
		DateTimeUtils.validateBusinessTimeZone(business.getTimezoneId());
		if (validateCampaign(reviewRequest, business, campaign, isReminder, customer) == 0) {
			return 0;
		} else if (CoreUtils.isNotValidBusiness(business)) {
			markReviewRequestFailure(reviewRequest, getBusinessStatusReason(business.getActivationStatus()), business.getEnterpriseIdElseBusinessId());
			logger.warn("Business {} is cancelled/suspended {}", business.getId(), business.getActivationStatus());
			return 0;
		} else if (business.getSmsEnabled() == null || business.getSmsEnabled() == 0) {
			markReviewRequestFailure(reviewRequest, "Business is not enabled to send Text.", business.getEnterpriseIdElseBusinessId());
			logger.warn("Business: {} is not enable to send Text, requestId : {}", business.getId(), reviewRequest.getId());
			return 0;
		} else if (validateCustomerForSms(reviewRequest, customer, business, communicationCategory) == 0) {
			return 0;
		} else if (isReminder == 0 && isRequestSMSAlreadySent(business, customer, campaign, isSurveyRestrictionEnable)) {
			Integer mailResendFrequency = getCampaignCommFrequency(business, campaign, isSurveyRestrictionEnable);
			markReviewRequestFailure(reviewRequest, "Text already sent in last " + mailResendFrequency + " days", business.getEnterpriseIdElseBusinessId());
			logger.warn("Review request text already sent in last {} days for request : {}", mailResendFrequency, reviewRequest.getId());
			return 0;
		} 
		else if (isDuplicateAppointmentCommunication(reviewRequest, business, customer, campaign.getType(), Constants.RR_SOURCE_SMS, appointmentScheduledDates, campaign.getRunType())) {
			if(StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.APPOINTMENT_REMINDER.getType()) && CacheManager.getInstance().getCache(RestrictAppointmentCommunicationCache.class).isEnterpriseIdRestrictedForTimeWindow(business.getEnterpriseIdElseBusinessId())) {
				markReviewRequestDropped((ReviewRequest) reviewRequest, "Duplicate request", false, true, business.getEnterpriseIdElseBusinessId());
			} else {
				// BIRDEYE-115745
				markReviewRequestDropped((ReviewRequest) reviewRequest, "Duplicate request", false, false, business.getEnterpriseIdElseBusinessId());
				
			}
			logger.info("Duplicate appointment request {} for business {} customer {} rTpe {} commType {}", reviewRequest.getId(), business.getId(), customer.getId(),
					campaign.getCampaignType(), Constants.RR_SOURCE_SMS);
			return 0;
		} else if (BooleanUtils.isFalse(freeTrialService.validateFreeTrialAccountLimits(reviewRequest.getRequestType(), business.getEnterpriseIdElseBusinessId()))) {
			markReviewRequestFailure(reviewRequest, Constants.FREE_TRIAL_ACCOUNT_LIMIT_REACHED, business.getEnterpriseIdElseBusinessId());
			return 0;
		}
		return 1;
	}
	
	private int validateCustomerForSms(BaseCommunicationEntity reviewRequest, KontactoDTO customer, BusinessEnterpriseEntity business, String communicationCategory) {
		if (!templateHelperService.extractTextCategoryEnabledFlag(business.getEnterpriseIdElseBusinessId()) && !Objects.isNull(customer.getCommPreferences()) && !customer.getCommPreferences()
				.getSmsPreferences().isServiceOptin()) {
			markReviewRequestFailure(reviewRequest, "Contact has been restricted from Text", business.getEnterpriseIdElseBusinessId());
			logger.warn("Customer: {} has been restricted from Text, request : {}", customer.getId(), reviewRequest.getId());
			return 0;
		}else if (!Objects.isNull(customer.getCommPreferences()) && !CommunicationCategoryUtils.isCustomerOptedInForSmsCategory(communicationCategory, customer.getCommPreferences())) {
			markReviewRequestFailure(reviewRequest, CommunicationCategoryUtils.prepareCommunicationFailureMessageForSms(communicationCategory, customer.getCommPreferences()),
					business.getEnterpriseIdElseBusinessId());
			logger.warn("Customer is unsubscribed!");
			return 0;
		} else if (StringUtils.isBlank(CoreUtils.formatPhoneNumber(customer.getPhone(), customer.getCountryCode()))) {
			markReviewRequestFailure(reviewRequest, "Phone number not present for this contact", business.getEnterpriseIdElseBusinessId());
			logger.warn("Phone number is not present for this customer: {}", customer.getId());
			return 0;
		} else if (BooleanUtils.isTrue(customer.getBlocked())) {
			markReviewRequestFailure(reviewRequest, "Contact has been blocked from Text", business.getEnterpriseIdElseBusinessId());
			logger.error("Customer {} has been blocked.", customer.getId());
			return 0;
		} else if (StringUtils.isBlank(customer.getReferralCode()) && StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.REFERRAL.getType())) {
			markReviewRequestFailure(reviewRequest, "Customer Missing Referral Code", business.getEnterpriseIdElseBusinessId());
			logger.warn("Customer {} missing referral code!", customer.getId());
			return 0;
		}
		return 1;
	}
	
	/**
	 * To check whether customer phone area code is blocked or not
	 * 
	 * @param customerPhoneNumber
	 *            Customer's phone number
	 * @param blockedAreaCodes
	 *            list of blocked area codes
	 * @return true if phone area code is blocked else false
	 */
	public boolean isCustomerPhoneBlocked(String customerPhoneNumber, List<String> blockedAreaCodes) {
		if (StringUtils.isNotBlank(customerPhoneNumber) && CollectionUtils.isNotEmpty(blockedAreaCodes)) {
			if (customerPhoneNumber.startsWith("+") || customerPhoneNumber.startsWith("(")) {
				customerPhoneNumber = customerPhoneNumber.substring(1, customerPhoneNumber.length());
			}
			String[] blockedAreaCodesArray = new String[blockedAreaCodes.size()];
			blockedAreaCodes.toArray(blockedAreaCodesArray);
			if (StringUtils.startsWithAny(customerPhoneNumber, blockedAreaCodesArray)) {
				return true;
			}
		}
		return false;
	}
	
	private boolean isRequestSMSAlreadySent(BusinessEnterpriseEntity business, KontactoDTO customer, Campaign campaign, boolean isSurveyRestrictionEnable) {
		
		logger.info("Request received to validate sms sent request for business {}, customer {} and campaign {} where campaign override flag is {}",
				business.getId(), customer.getId(),  campaign.getId(), campaign.getBypassCommRestriction());
		
		// Communication restriction for appointment reminders and recalls is separate handled.
		String requestType = campaign.getType();
		if (BooleanUtils.isTrue(StringUtils.equalsAnyIgnoreCase(requestType, CampaignTypeEnum.APPOINTMENT_REMINDER.getType(), CampaignTypeEnum.APPOINTMENT_RECALL.getType(), CampaignTypeEnum.APPOINTMENT_FORM.getType()))) {
			return false;
		}
		
		CampaignAccountSettings campaignAccountSettings = getCampaignAccountSetting(business);
		
		// Validating if campaign overrides communication restrictions
		if (campaign != null && CoreUtils.isTrueForInteger(campaign.getBypassCommRestriction()) && (campaign.getSurveyCommFrequency() == null || campaign.getSurveyCommFrequency() == 0)) {
			// logger.info("Bypassing communication restriction for sms send request where business is {}, customer is {} and campaign is {} ", business.getId(),
			// customer.getId(), campaign.getId());
			return false;
		}
				
		// Only proceed for SURVEY_REQUEST type and other than split automation
		if (isSurveyRestrictionEnable && StringUtils.equalsIgnoreCase(CampaignTypeEnum.SURVEY_REQUEST.getType(), requestType) && !CoreUtils.isTrueForInteger(campaign.getIsSplitCampaign())) {
			return isSurveyRequestAlreadySent(requestType, Constants.RR_SOURCE_SMS, campaign, campaignAccountSettings, business, customer);
		}
		
		boolean requestAlreadySent = false;
//		check if restriction is not required for given account
		if(!CoreUtils.isTrueForInteger(campaignAccountSettings.getIsCommRestrictionEnabled())){
			return  requestAlreadySent;
		}

		// CoreUtils.isTrueForInteger
		if(CoreUtils.isTrueForInteger(campaignAccountSettings.getRestrictionAtAccountLevel())){
			//logger.info("Account level restriction customer ecid {}", customer.getEcid());
//			for account level restriction fetch cache from ecid
			requestAlreadySent = validateCustomerOnAccountLevelCommunication(Constants.RR_SOURCE_SMS, requestType, getCampaignTypeFrequency(campaignAccountSettings,campaign), business.getTimezoneId(), customer, campaignAccountSettings);
		}
		else {
			//logger.info("Location level restriction customer id {}", customer.getId());
			//			for location level restriction fetch cache from c_id
			requestAlreadySent = validateCustomerOnLocationLevelCommunication(Constants.RR_SOURCE_SMS, requestType, getCampaignTypeFrequency(campaignAccountSettings,campaign), business.getTimezoneId(), customer, campaignAccountSettings);
		}
		
		if (BooleanUtils.isTrue(requestAlreadySent)) {
			logger.info("For business {} and customer {} SMS requestAlreadySent is {} ", business.getId(), customer.getId(), requestAlreadySent);
		}
		
		return requestAlreadySent;

	}
	private Boolean validateCustomerOnAccountLevelCommunication( String commType, String rrType, Integer mailResendFrequency,String businessTimeZone, KontactoDTO customer, CampaignAccountSettings campaignAccountSettings){
		Boolean requestAlreadySent = false;
		requestAlreadySent = customerService.isCustomerCommunicationRequestSent(customer.getEcid(),commType, rrType, mailResendFrequency, businessTimeZone, campaignAccountSettings.getRestrictionOnCampaignType(), true, customer);
		return requestAlreadySent;
	}

	/**
	 *
	 * @param commType
	 * @param rrType
	 * @param mailResendFrequency
	 * @param businessTimeZone
	 * @param customer
	 * @param campaignAccountSettings
	 * @return
	 */
	private Boolean validateCustomerOnLocationLevelCommunication(String commType, String rrType, Integer mailResendFrequency,String businessTimeZone, KontactoDTO customer, CampaignAccountSettings campaignAccountSettings){
		Boolean requestAlreadySent = false;
		requestAlreadySent = customerService.isCustomerCommunicationRequestSent(customer.getId(),commType, rrType, mailResendFrequency, businessTimeZone, campaignAccountSettings.getRestrictionOnCampaignType(), false, customer);
		return requestAlreadySent;
	}


	private Integer getCampaignTypeFrequency(CampaignAccountSettings campaignAccountSettings, Campaign campaign ){
		CampaignTypeEnum value = CampaignTypeEnum.getEnum(campaign.getType());
		switch(value){
			case REFERRAL:
				return campaignAccountSettings.getReferralCommFrequency();
			case CX_REQUEST:
				return campaignAccountSettings.getCxCommFrequency();
			case SURVEY_REQUEST:
				return campaignAccountSettings.getSurveyCommFrequency();
			case REVIEW_REQUEST:
				return campaignAccountSettings.getReviewCommFrequency();
			case PROMOTIONAL:
				return campaignAccountSettings.getPromotionCommFrequency();
			default:
				return null;
		}
	}

	private boolean isRequestMailAlreadySent(BusinessEnterpriseEntity business, KontactoDTO customer, Campaign campaign, boolean isSurveyRestrictionEnable) {
		
		logger.info("Request received to validate email sent request for business {}, customer {} and campaign {} where campaign override flag is {}",
				business.getId(), customer.getId(),  campaign.getId(), campaign.getBypassCommRestriction());
		
		// Communication restriction for appointment reminders and recalls is separate handled.
		String requestType = campaign.getType();
		if (BooleanUtils.isTrue(StringUtils.equalsAnyIgnoreCase(requestType, CampaignTypeEnum.APPOINTMENT_REMINDER.getType(), CampaignTypeEnum.APPOINTMENT_RECALL.getType(),
				CampaignTypeEnum.APPOINTMENT_FORM.getType()))) {
			return false;
		}
		CampaignAccountSettings campaignAccountSettings = getCampaignAccountSetting(business);
		
		// Validating if campaign overrides communication restrictions
		if (campaign != null && CoreUtils.isTrueForInteger(campaign.getBypassCommRestriction()) && !CoreUtils.isTrueForInteger(campaign.getSurveyCommFrequency())) {
			// logger.info("Bypassing communication restriction for email send request where business is {}, customer is {} and campaign is {} ",
			// business.getId(), customer.getId(), campaign.getId());
			return false;
		}
				
		// Only proceed for SURVEY_REQUEST type and other than split automation
		if (isSurveyRestrictionEnable && StringUtils.equalsIgnoreCase(CampaignTypeEnum.SURVEY_REQUEST.getType(), requestType) && !CoreUtils.isTrueForInteger(campaign.getIsSplitCampaign())) {
			return isSurveyRequestAlreadySent(requestType, Constants.RR_SOURCE_EMAIL, campaign, campaignAccountSettings, business, customer);
		}
		
		boolean requestAlreadySent = false;
		
		//		check if restriction is not required for given account
		if(campaignAccountSettings.getIsCommRestrictionEnabled() == 0){
			return requestAlreadySent;
		}
		
		if(CoreUtils.isTrueForInteger(campaignAccountSettings.getRestrictionAtAccountLevel())){
//			for account level restriction fetch cache from ecid
			requestAlreadySent = validateCustomerOnAccountLevelCommunication(Constants.RR_SOURCE_EMAIL, requestType, getCampaignTypeFrequency(campaignAccountSettings,campaign), business.getTimezoneId(), customer, campaignAccountSettings);
		}
		else {
//			for location level restriction fetch cache from c_id
			requestAlreadySent = validateCustomerOnLocationLevelCommunication(Constants.RR_SOURCE_EMAIL, requestType, getCampaignTypeFrequency(campaignAccountSettings,campaign), business.getTimezoneId(), customer, campaignAccountSettings);
		}
		
		if(BooleanUtils.isTrue(requestAlreadySent)) {
			logger.info("For business {} and customer {} SMS requestAlreadySent is {} ", business.getId(), customer.getId(), requestAlreadySent);
		}
		
		return requestAlreadySent;

	}
	
	/**
	 * [BIRDEYE-115745] Method to handle duplicate reminder/recall events which get scheduled due to :
	 * 1. Data backfill
	 * 2. Some business have not added any condition on Appointment status. So for both booked and confirmed event, duplicate reminders get scheduled.
	 * 
	 */
	private boolean isDuplicateAppointmentCommunication(BaseCommunicationEntity reviewRequest, BusinessEnterpriseEntity business, KontactoDTO customer, String requestType, String commType, List<Date> appointmentScheduledDates, String campaignRunType) {
		
		if (!StringUtils.equalsAnyIgnoreCase(requestType, CampaignTypeEnum.APPOINTMENT_REMINDER.getType(), CampaignTypeEnum.APPOINTMENT_RECALL.getType(),
				CampaignTypeEnum.APPOINTMENT_FORM.getType())) {
			return false;
		}
		
		Date scheduledDate = CollectionUtils.isNotEmpty(appointmentScheduledDates) ? appointmentScheduledDates.get(0) : new Date();
		logger.info("Scheduled time for requestId : {} is  {}", reviewRequest.getId(), scheduledDate);
		
		if (checkAccountForSpecialAppointmentCommRestriction(business.getEnterpriseIdElseBusinessId(), requestType, campaignRunType)) {
			return customerService.validateDuplicateAppointmentCommunicationForPatient(customer.getId(), commType, requestType, business.getEnterpriseIdElseBusinessId(), reviewRequest.getId(), scheduledDate, business);
		}
		
		AppointmentRRMapping appointmentRequestAudit = appointmentReminderAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
		return customerService.validateDuplicateAppointmentCommunication(customer.getId(), commType, requestType, appointmentRequestAudit.getAppointmentId());
	}
	
	/**
	 * JIRA:- https://birdeye.atlassian.net/browse/BIRDEYEV2-13382
	 * 
	 * This method is for businesses for whom the communication restriction does not follow 10 minute rule.
	 * 
	 * @param enterpriseId
	 * @param requestType 
	 * @return
	 */
	private boolean checkAccountForSpecialAppointmentCommRestriction(Integer enterpriseId, String requestType, String campaignRunType) {
		if (!StringUtils.equalsIgnoreCase(requestType, CampaignTypeEnum.APPOINTMENT_REMINDER.getType()) || !StringUtils.equalsIgnoreCase(campaignRunType, "ongoing"))
			return false;
		
//		List<Integer> accountIds = CacheManager.getInstance().getCache(SystemPropertiesCache.class)
//				.getCommaSeparatedPropertiesIntegerList(Constants.REMINDER_COMM_RESTRICT_ENTERPRISE_IDS_CSV, Constants.REMINDER_COMM_RESTRICT_ENTERPRISE_IDS_DEFAULT);
		
		return CacheManager.getInstance().getCache(RestrictAppointmentCommunicationCache.class).isEnterpriseIdRestrictedForTimeWindow(enterpriseId);
	}
	
	private boolean sendReviewRequestSms(ReviewRequest reviewRequest, KontactoDTO customer, boolean applyDND, boolean isQuickSend, boolean retryOnShortenURlError) throws Exception {
		BusinessEnterpriseEntity business = cacheService.getBusinessById(customer.getBusinessId());
		logger.info("preparing to send review request sms for requestId : {}", reviewRequest.getId());
		Campaign campaign = getCampaignForCampaignId(reviewRequest.getCampaignId());
		// messenger campaign Flag
		Integer isMessangerCampaign = (campaign != null && campaign.getIsMessengerCampaign() != null) ? campaign.getIsMessengerCampaign() : 0;
		if (applyDND) {
			// add appropriate number of hours to make sure that if it is within the 8am-8pm window
			int numberOfHours = DateTimeUtils.calculateSmsDNDRemainingTimeForATimeZoneId(business.getTimezoneId());
			logger.info("For review request id {} and TimeZoneId {} the delay Hr {}", reviewRequest.getId(), business.getTimezoneId(), numberOfHours);
			if (numberOfHours > 0 && campaign != null) {
				// for new drip campaigns, DND sms needs to be scheduled for the next available date
				if (CampaignSchedulingTypeEnum.DRIP.getType().equalsIgnoreCase(campaign.getSchedulingType())) {
					// check if campaign falls in NEW drip category and any further valid dates left in execution range of the campaign
					Date newScheduleDate = dripCampaignService.scheduleDNDRequestForDrip(campaign, business.getTimezoneId());
					if (newScheduleDate != null) {
						// go for schedule
						logger.info("sendReviewRequestSms : rescheduling Drip Campaign {} DND Customer {} to new time {}", customer.getId(), campaign.getId(), newScheduleDate);
						campaignEventManagementService.saveReviewEventFromRequest(campaignRRService.getDripDNDRREvent(reviewRequest.getId(), reviewRequest.getSource(), business.getId(),
								reviewRequest.getRequestType(), reviewRequest.getRequestDate(), campaign, newScheduleDate));
						return true;
					}
				}
				// go for schedule
				campaignEventManagementService.saveReviewEventFromRequest(campaignRRService.getCreateReviewRequestSMS(reviewRequest.getId(), reviewRequest.getSource(), business.getId(),
						reviewRequest.getRequestType(), reviewRequest.getRequestDate(), campaign, numberOfHours, business.getTimezoneId()));
				return true;
			}
		}
		sendRequestSmsToCustomer(business, reviewRequest, customer, isMessangerCampaign, applyDND, isQuickSend, campaign, retryOnShortenURlError);
		
		return false;
	}
	
	/**
	 * 
	 * Calculates scheduled time for sms
	 * 
	 * For dnd, add no of hours and days so that scheduled time is in valid zone
	 *
	 * @param business
	 * @param reviewRequest
	 * @param customer
	 * @param isMessengerCampaign
	 * @param applyDND
	 * @param isQuickSend
	 * @param campaign
	 * @param retryOnShortenURlError
	 * @throws Exception
	 * 
	 */
	private void sendRequestSmsToCustomer(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, KontactoDTO customer, Integer isMessengerCampaign, boolean applyDND, boolean isQuickSend,
			Campaign campaign, boolean retryOnShortenURlError) throws Exception {
		BusinessSmsTemplate smsTemplate = businessSmsTemplateDao.getSMSTemplateByEnterpriseIdAndTemplateId(business.getEnterpriseId() != null ? business.getEnterpriseId() : business.getId(),
				reviewRequest.getTemplateId());
		Integer isAppointmentCampaign = (campaign != null && campaign.getIsAppointmentTabCampaign() != null) ? campaign.getIsAppointmentTabCampaign() : 0;
		
		if (smsTemplate == null && isAppointmentCampaign == 1) {
			BusinessSmsTemplate freeTextSmsTemplate = templateHelperService.getFreeTextSmsTemplateId();
			if (freeTextSmsTemplate != null && reviewRequest.getTemplateId().equals(freeTextSmsTemplate.getId()))
				smsTemplate = freeTextSmsTemplate;
		}
		
		if (smsTemplate == null) {
			throw new CampaignException(ErrorCodes.INVALID_TEMPLATE, "No SMS template configured");
		}
		
		if (isMessengerCampaign != null && isMessengerCampaign == 1) {
			campaignSmsService.sendRRMessengerSmsCampaign(business, reviewRequest, customer, smsTemplate, applyDND, retryOnShortenURlError);
			return;
		}
		
		if(isAppointmentCampaign == 1) {
			campaignSmsService.sendAppointmentSmsCampaign(business, reviewRequest, customer, smsTemplate);
			return;		
		}
		
		campaignSmsService.sendRRSmsCampaign(business, reviewRequest, customer, smsTemplate, applyDND, isQuickSend, campaign, retryOnShortenURlError);
	}
	
	private Campaign getCampaignForCampaignId(Integer campaignId) {
		return campaignId != null ? campaignSetupCachingService.getCampaignById(campaignId) : null;
	}
	
	// DND requests also need to be shown in View Audience - hence they are marked separately now - BIRDEYE-70737
	private void markRequestInDND(BaseCommunicationEntity reviewRequest) {
		reviewRequest.setSentOn(new Date());
		reviewRequest.setDeliveryStatus(RequestStatusEnum.DND.getName());
		// update DND data to Elastic
		if (reviewRequest instanceof Promotion) {
			logger.info("marking promotion request id {} in DND", reviewRequest.getId());
			promotionRepo.saveAndFlush((Promotion) reviewRequest);
		} else {
			logger.info("marking review request id {} in DND", reviewRequest.getId());
			reviewRequestRepo.saveAndFlush((ReviewRequest) reviewRequest);
		}
		sendRequestDataToElastic(reviewRequest);
	}
	
	private void deleteCustomerCacheForFailureCase(Integer customerId, String commType, String rrType) {
		customerService.deleteCustomerLastCommunicationTimeCache(customerId, commType, rrType);
		
	}
	
	/**
	 * Return shorten url for review request with source as sms
	 */
	
	@Override
	public ExternalReviewRequestDTO createExternalReviewRequest(CreateReviewRequestMessage createRRMessage) {
		logger.info("Request recieved to get review request link {} ", createRRMessage);
		// BIRDEYE-120178
		if (createRRMessage.getBusinessId() == null && createRRMessage.getReferenceId() != null) {
			createRRMessage.setBusinessId(getBusinessIdFromReferenceId(createRRMessage.getReferenceId(), createRRMessage.getApiKey()));
			logger.info("BusinessId fetched using referenceId: {} is {}", createRRMessage.getReferenceId(), createRRMessage.getBusinessId());
		}
		if (createRRMessage.getBusinessId() == null || createRRMessage.getCustomerId() == null
				|| (createRRMessage.getTemplateId() == null && createRRMessage.getSurveyId() == null)) {
			logger.error("invalid data for request {}", createRRMessage);
			return new ExternalReviewRequestDTO(null, "invalid data");
		}
		KontactoDTO customer = contactExternalService.getCustomerById(createRRMessage.getCustomerId());
		BusinessEnterpriseEntity business = cacheService.getBusinessLocationById(createRRMessage.getBusinessId());
		
		if (business == null) {
			logger.warn("Business not found for id : {}", createRRMessage.getBusinessId());
			throw new CampaignException(ErrorCodes.INVALID_BUSINESS, "Business not found.");
		}
		
		if (customer == null || customer.getBusinessId().intValue() != createRRMessage.getBusinessId().intValue()) {
			logger.warn("Contact not found for contact id : {}", createRRMessage.getCustomerId());
			throw new CampaignException(ErrorCodes.INVALID_CUSTOMER_DETAILS, "Contact could not be retrieved");
		}
		
		Integer enterpriseId = business.getEnterpriseId() != null ? business.getEnterpriseId() : business.getId();
		
		BusinessSmsTemplate smsTemplate = null;
		
		if (createRRMessage.getSurveyId() != null) {
			smsTemplate = businessSmsTemplateDao.getDefaultBusinessSmsTemplateByType(TemplateTypeEnum.SURVEY_REQUEST_SMS.getName());
		} else {
			smsTemplate = businessSmsTemplateDao.getSMSTemplateByEnterpriseIdAndTemplateId(enterpriseId, createRRMessage.getTemplateId());
		}
		
		if (smsTemplate == null) {
			logger.error("No template found for businessId {}, template Id {} and request {}", createRRMessage.getBusinessId(), createRRMessage.getTemplateId(), createRRMessage);
			throw new CampaignException(ErrorCodes.NO_TEMPLATE_FOUND, "No Template found for this Id");
		}
		
		if (StringUtils.equalsAnyIgnoreCase(smsTemplate.getType(), TemplateTypeEnum.SURVEY_REQUEST.getName())
				&& !checkSurveyIsValidForEnterprise(createRRMessage.getSurveyId(), enterpriseId)) {
			logger.error("Survey id is not available for survey template for request {}", createRRMessage);
			throw new CampaignException(ErrorCodes.INVALID_SURVEY_DETAILS, "Invalid survey Id");
			
		}
		
		createRRMessage.setRequestType(smsTemplate.getType());
		createRRMessage.setSource(Constants.RR_SOURCE_SMS);
		if (StringUtils.equalsAnyIgnoreCase(smsTemplate.getType(), TemplateTypeEnum.REVIEW_REQUEST_SMS.getName(), TemplateTypeEnum.REFERRAL.getName(),
				TemplateTypeEnum.CUSTOMER_EXPERIENCE.getName(), TemplateTypeEnum.SURVEY_REQUEST.getName())) {
			ReviewRequest reviewRequest = getReviewRequestFromCreateRRMessageExternal(createRRMessage, smsTemplate.getType());
			logger.info("received request to run review request for request id {}", reviewRequest.getId());
			// BIRDEYEV2-11406 : Creates a mapping of review request and communication source.
			campaignRRService.createCommunicationRequestSource(reviewRequest, CommunicationRequestSourceEnum.EXTERNAL_REQUEST.getType());
			
			String shortenURL = campaignSmsService.getShortenUrlForSms(business, reviewRequest, smsTemplate, null);
			communicationActivityService.publishCommunicationAcitivity(CommunicationAcitivityEventEnum.SENT, reviewRequest, null, enterpriseId);
			pushCustomerCommCappingEventToKafka(reviewRequest.getCustId(), getCampaignTypeFromTemplateType(smsTemplate.getType()), reviewRequest.getSource(),customer.getEcid(), reviewRequest.getSurveyId(), enterpriseId, false);
			updateCustomerHistoryInKafka(reviewRequest);
			sendRequestDataToElastic(reviewRequest);
			return new ExternalReviewRequestDTO(reviewRequest.getId(), shortenURL);
			
		}
		return new ExternalReviewRequestDTO();
	}
	
	/**
	 * @param referenceId
	 * @return business id for provided referenceId
	 */
	private Integer getBusinessIdFromReferenceId(String referenceId, String apiKey) {
		if (StringUtils.isBlank(apiKey)) {
			logger.info("Invalid api key");
			return null;
		}
		BusinessInfoResponseByReferenceId businessInfoByReferenceId = businessExternalService.getBusinessInfoResponseByReferenceId(referenceId, null, apiKey);
		if (businessInfoByReferenceId != null && BooleanUtils.isTrue(businessInfoByReferenceId.getSuccess()) && businessInfoByReferenceId.getResponse() != null) {
			return businessInfoByReferenceId.getResponse().getBusinessId();
		}
		return null;
	}

	private boolean checkSurveyIsValidForEnterprise(Integer surveyId, Integer enterpriseId) {
		logger.info("Getting survey details for survey Id {} and enterpriseId {}", surveyId, enterpriseId);
		SurveyDataMessage surveyDetails = surveyExternalService.getSurveyById(surveyId);
		
		return surveyDetails != null && surveyDetails.getBusinessId().intValue() == enterpriseId.intValue();
	}

	private ReviewRequest getReviewRequestFromCreateRRMessageExternal(CreateReviewRequestMessage createReviewRequestMessage, String templateType) {
		ReviewRequest reviewRequest = new ReviewRequest();
		reviewRequest.setCustId(createReviewRequestMessage.getCustomerId());
		reviewRequest.setSource(createReviewRequestMessage.getSource());
		reviewRequest.setDeliveryStatus(RequestStatusEnum.SUCCESS.getName());
		if (createReviewRequestMessage.getCheckinId() != null && !Objects.equals(createReviewRequestMessage.getCheckinId(), 0)) {
			reviewRequest.setCheckinId(createReviewRequestMessage.getCheckinId());
		}
		reviewRequest.setBusinessId(createReviewRequestMessage.getBusinessId());
		reviewRequest.setTemplateId(createReviewRequestMessage.getTemplateId());
		if (createReviewRequestMessage.getSurveyId() != null && !Objects.equals(createReviewRequestMessage.getSurveyId(), 0)
				&& StringUtils.equalsIgnoreCase(templateType, TemplateTypeEnum.SURVEY_REQUEST.getName())) {
			reviewRequest.setSurveyId(createReviewRequestMessage.getSurveyId());
		}
		// Need to support old review request type which is getting used in old code.
		reviewRequest.setRequestType(createReviewRequestMessage.getRequestType());
		reviewRequestDAO.saveReviewRequest(reviewRequest);
		return reviewRequest;
	}
	
	private String getCampaignTypeFromTemplateType(String templateType) {
		String campaignType = null;
		TemplateTypeEnum templateTypeEnum = TemplateTypeEnum.findByKey(templateType);
		switch (templateTypeEnum) {
			case REVIEW_REQUEST_SMS:
			case REVIEW_REQUEST_NEW:
				campaignType = CampaignTypeEnum.REVIEW_REQUEST.getType();
				break;
			case CUSTOMER_EXPERIENCE_SMS:
			case CUSTOMER_EXPERIENCE:
				campaignType = CampaignTypeEnum.CX_REQUEST.getType();
				break;
			case REFERRAL:
			case REFERRAL_SMS:
				campaignType = CampaignTypeEnum.REVIEW_REQUEST.getType();
				break;
			case SURVEY_REQUEST_SMS:
			case SURVEY_REQUEST:
				campaignType = CampaignTypeEnum.SURVEY_REQUEST.getType();
				break;
			case PROMOTION:
			case PROMOTION_SMS:
				campaignType = CampaignTypeEnum.PROMOTIONAL.getType();
				break;
			default:
				break;
		}
		return campaignType;
		
	}
	
	/**
	 * Submit an event to SAMAY to reschedule RR/Promotion requests for execution.
	 * Added retry to handle external service calls failure which lead to Campaigns stuck in running state.
	 * Configurable retry counter limit.
	 * Retry delay is exponential -> retryCounter * fixed delay in min.
	 * 
	 * @param requestId
	 * @param batchId
	 * @return 
	 * 
	 */
	private boolean validateAndRescheduleRequestExecutionEvent(Long requestId, String requestType) {
		logger.info("Checking retry validity for requestId {} and type {}", requestId, requestType);
		String key = StringUtils.join(requestType, "_", requestId);
		String counter = aerospikeJedisService.get(key, "rrExecutionRetryCounter");
		Integer retryCounterCurr = StringUtils.isAllBlank(counter) ? 0 : Integer.parseInt(counter);
		
		Integer retryCountLimit = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("rr.execution.retry.count.limit", 1);
		
		if (retryCounterCurr >= retryCountLimit) {
			logger.info("Execution Retry limit exhausted for requestId {} and type {}", requestId, requestType);
			return false;
		}
		retryCounterCurr = retryCounterCurr + 1;
		aerospikeJedisService.set(key, retryCounterCurr, "rrExecutionRetryCounter",
				CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("rr.execution.retry.event.aerospike.ttl.sec", 86400));
		
		rescheduleRequestExecutionEvent(requestId, requestType, retryCounterCurr);
		return true;
		
	}

	private void rescheduleRequestExecutionEvent(Long requestId, String requestType, Integer retryCounter) {
		logger.info("Submitting Request Execution Event to Scheduler for requestId {} and type {}. Counter : {}", requestId, requestType, retryCounter);
		
		String jobDelay = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("rr.execution.retry.delay.in.min", "60"); // in minutes
		
		// Multiplying delay with counter to induce exponential delay.
		long expiryTime = LocalDateTime.now(ZoneId.of("UTC")).plusMinutes(retryCounter * Long.valueOf(jobDelay)).atZone(ZoneId.of("UTC")).toInstant().toEpochMilli();
		
		String topicName = CacheManager.getInstance().getCache(KafkaTopicCache.class).getKafkaTopic(KafkaTopicTypeEnum.SEND_CAMPAIGN_REQUEST.getType());
		
		String samayPayloadStr = JsonUtils.toJSON(new RunCampaignKafkaMessage(requestId, requestType));
		
		SamaySRO schedulingRequest = new SamaySRO();
		schedulingRequest.setApplication("Campaigns");
		schedulingRequest.setExpiry(expiryTime);
		schedulingRequest.setPayload(samayPayloadStr);
		schedulingRequest.setTopic(topicName);
		
		kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.SAMAY_SCHEDULE, new KafkaMessage(requestId, schedulingRequest));
	}

	private void markReviewRequestDropped(ReviewRequest reviewRequest, String droppedReason, boolean publishDataToES, boolean scheduleReminder, Integer enterpriseId) {
		reviewRequest.setDeliveryStatus(RequestStatusEnum.DROPPED.getName());
		reviewRequest.setFailureReason(droppedReason);
		reviewRequestDAO.saveReviewRequest(reviewRequest);
		if (reviewRequest.getParentRequestId() != null) {
			logger.info("Updating parent review request reminder count for id {}", reviewRequest.getId());
			campaignRRService.incrementHeadRequestReminderCount(reviewRequest.getParentRequestId());
		}
		communicationActivityService.publishCommunicationAcitivity(CommunicationAcitivityEventEnum.DROPPED, reviewRequest, null, enterpriseId);
		updateCustomerHistoryInKafka(reviewRequest);
		if (publishDataToES) {
			sendRequestDataToElastic(reviewRequest);
		}
		if (scheduleReminder) {
			campaignReminderService.scheduleReminder(reviewRequest, campaignSetupCachingService.getCampaignById(reviewRequest.getCampaignId()));
		}
	}
	
	private void markPromotionRequestDropped(Promotion promotionRequest, String droppedReason) {
		promotionRequest.setDeliveryStatus(RequestStatusEnum.DROPPED.getName());
		promotionRequest.setFailureReason(droppedReason);
		promotionRepo.saveAndFlush(promotionRequest);
	}
	
	@Override
	public void markReviewRequestDropped(Long reviewRequestId, String droppedReason, boolean publishDataToES) {
		ReviewRequest reviewRequest = reviewRequestDAO.getReviewRequestById(reviewRequestId);
		markReviewRequestDropped(reviewRequest, droppedReason, publishDataToES, false, null);
	}

	private ReviewRequest createAndGetReminderRequest(Long requestId, int isReminder) {
		ReviewRequest reviewRequest = reviewRequestDAO.getReviewRequestById(requestId);
		if (!CoreUtils.isTrueForInteger(isReminder) || Objects.isNull(reviewRequest)) {
			return reviewRequest;
		}
		
		String communicationCategory = templateService.fetchRequestCommunicationCategory(reviewRequest.getSource(), reviewRequest.getTemplateId());
		ReviewRequest reminderReviewRequest = campaignRRService.createAndGetReminderReviewRequest(reviewRequest, communicationCategory);
		if (reminderReviewRequest == null) {
			return null;
		}
		
		Campaign campaign = campaignSetupCachingService.getCampaignById(reminderReviewRequest.getCampaignId());
		if (campaign == null) {
			return null;
		}
		
		if (StringUtils.equalsAnyIgnoreCase(campaign.getTriggerType(), CampaignTriggerTypeEnum.BEFORE_APPOINTMENT_DATE.getType(),
				CampaignTriggerTypeEnum.APPOINTMENT_RECALL.getType(), CampaignTriggerTypeEnum.APPOINTMENT_BOOKED.getType(), CampaignTriggerTypeEnum.APPOINTMENT_CANCELED.getType(),
				CampaignTriggerTypeEnum.APPOINTMENT_MISSED.getType(), CampaignTriggerTypeEnum.APPOINTMENT_COMPLETED.getType())) {
			AppointmentRRMapping appointmentRequestAudit = appointmentReminderAuditService.getReminderRequestAuditByReviewRequestId(requestId);
			if (appointmentRequestAudit == null) {
				logger.warn("No valid appointment exits for review request id {}", requestId);
				throw new CampaignException(ErrorCodes.INVALID_APPOINTMENT, ErrorCodes.INVALID_APPOINTMENT.getMessage());
			}
			appointmentReminderAuditService.saveAppointmentRequestInfo(reminderReviewRequest.getId(), appointmentRequestAudit.getAppointmentId(),
					appointmentRequestAudit.getRecallId(), reminderReviewRequest.getRequestType());
		}
		return reminderReviewRequest;
	}
	
	@Override
	public void submitReviewReqAppointmentCampaign(Campaign campaign, List<Integer> appointmentIds, Integer checkinId, List<Integer> customerIds) throws Exception {
		List<CreateReviewRequestMessage> reviewRequestCreateMessageList = campaignRRService.getCreateReviewRequestMessagesForAppointment(appointmentIds, campaign, campaign.getSurveyId(),
				campaign.getPriority(), customerIds);
		
		logger.info("push in kafka review-request create message for instant appointment campaign : {} and size : {}", campaign.getId(), reviewRequestCreateMessageList.size());
		String topicName = CampaignUtils.appendCampaignTypeToTopicName(campaign.getCampaignType(), KafkaTopicTypeEnum.CAMPAIGN_RR_CREATE.getType());
		pushRRCreateMessageToKafka(topicName, checkinId, reviewRequestCreateMessageList);
//		pushRRCreateMessageToKafka(checkinId, reviewRequestCreateMessageList);
	}
	
	/**
	 * Validates a custom appointment form for a review request within a campaign.
	 * @return True if the appointment form is successfully validated, false otherwise.
	 */
	private boolean validateCustomAppointmentForm(ReviewRequest reviewRequest, Campaign campaign, BusinessEnterpriseEntity business) {
		 // Retrieve the appointment audit information based on the review request ID
		AppointmentRRMapping appointmentRequestAudit = appointmentReminderAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
		 // Check if a valid appointment audit exists for the review request
		if (appointmentRequestAudit == null) {
			logger.warn("No valid appointment exits for review request id {}", reviewRequest.getId());
			throw new CampaignException(ErrorCodes.INVALID_APPOINTMENT, ErrorCodes.INVALID_APPOINTMENT.getMessage());
		}
		try {
			// Validate the custom appointment form execution request based on the campaign's run type
			if(StringUtils.equalsIgnoreCase(campaign.getRunType(), CampaignRunTypeEnum.MANUAL.getRunType())) {
				 // Validate the manual custom appointment
				appointmentFormService.validateManualCustomAppointmentFormExecutionRequest(reviewRequest, appointmentRequestAudit.getAppointmentId(), campaign);
			} else {
				// Validate the custom appointment form execution request
				appointmentFormService.validateCustomFormExecutionRequest(reviewRequest, appointmentRequestAudit.getAppointmentId(), campaign.getId(), campaign.getEnterpriseId(), business);
			}
		} catch (Exception e) {
			// If an exception occurs during validation, mark the review request as dropped and log the error
			markReviewRequestDropped(reviewRequest, e.getLocalizedMessage(), true, false, business.getEnterpriseIdElseBusinessId());
			return false;
		}
		return true;
	}

	/**
	 * Persist communication_request_sources for a review request.
	 * Made async version to perform writes in less traffic hours. CRON based.
	 */
	@Override
	public void saveCommunicationRequestSource(CommunicationRequestSource communicationRequestSource) {
		campaignRRService.saveCommunicationRequestSource(communicationRequestSource);
	}
	
	/**
	 * Checks if a survey communication request has already been sent to the customer at the account level.
	 * 
	 * @return True if the survey communication has already been sent at the account level; false otherwise.
	 */
	private Boolean isAccountLevelSurveyCommunicationAlreadySent(String commType, String rrType, Integer mailResendFrequency, String businessTimeZone, KontactoDTO customer,
			CampaignAccountSettings campaignAccountSettings, Integer surveyId) {
		return customerService.isCustomerSurveyCommunicationRequestSent(customer.getEcid(), commType, rrType, mailResendFrequency, businessTimeZone,
				campaignAccountSettings.getRestrictionOnCampaignType(), true, customer, CoreUtils.isTrueForInteger(campaignAccountSettings.getSurveyRestrictionScope()), surveyId, 0, campaignAccountSettings.getSurveyRestrictionScope());
	}

	/**
	 * Checks if a survey communication request has already been sent to the customer at the location level.
	 * 
	 * @return True if the survey communication has already been sent at the location level; false otherwise.
	 */
	private Boolean isLocationLevelSurveyCommunicationAlreadySent(String commType, String rrType, Integer mailResendFrequency, String businessTimeZone, KontactoDTO customer,
			CampaignAccountSettings campaignAccountSettings, Integer surveyId) {
		return customerService.isCustomerSurveyCommunicationRequestSent(customer.getId(), commType, rrType, mailResendFrequency, businessTimeZone,
				campaignAccountSettings.getRestrictionOnCampaignType(), false, customer, CoreUtils.isTrueForInteger(campaignAccountSettings.getSurveyRestrictionScope()), surveyId, 0, campaignAccountSettings.getSurveyRestrictionScope());	
	}
	
	/**
	 * Checks if a survey communication request triggered by automation has already been sent to the customer.
	 * 
	 * @return True if the automated survey communication has already been sent; false otherwise.
	 */
	private Boolean isAutomationSurveyCommunicationAlreadySent(String commType, String rrType, Integer mailResendFrequency, String businessTimeZone, KontactoDTO customer,
			CampaignAccountSettings campaignAccountSettings, Integer surveyId) {
		boolean isAccountLevelComm = campaignAccountSettings != null && CoreUtils.isTrueForInteger(campaignAccountSettings.getIsCommRestrictionEnabled()) && CoreUtils.isTrueForInteger(campaignAccountSettings.getRestrictionAtAccountLevel());
		Integer customerId = isAccountLevelComm ? customer.getEcid() : customer.getId();
		return customerService.isCustomerSurveyCommunicationRequestSent(customerId, commType, rrType, mailResendFrequency, businessTimeZone,
				campaignAccountSettings.getRestrictionOnCampaignType(), isAccountLevelComm, customer, true, surveyId, 1, campaignAccountSettings.getSurveyRestrictionScope());	
	}
	
	/**
	 * Checks whether a survey request communication has already been sent to the customer
	 * based on campaign-level, account-level, or location-level restrictions.
	 *
	 * <p>This method evaluates if a survey request communication should be prevented
	 * due to frequency limits and restriction levels. It handles different levels of restriction:
	 * <ul>
	 *   <li><b>Campaign-level:</b> If campaign is set to bypass restrictions and frequency is set.</li>
	 *   <li><b>Account-level:</b> If account-level restriction is enabled in campaign settings.</li>
	 *   <li><b>Location-level:</b> If location-level restriction is enabled in campaign settings.</li>
	 * </ul>
	 *
	 * @param requestType              the type of request, expected to be "SURVEY_REQUEST"
	 * @param campaign                 the campaign entity containing survey and restriction configuration
	 * @param campaignAccountSettings the campaign account settings (can be null)
	 * @param business                 the business entity for time zone context
	 * @param customer                 the customer DTO representing the communication recipient
	 * @return true if a survey communication has already been sent based on the rules; false otherwise
	 */
	public boolean isSurveyRequestAlreadySent(String requestType, String source, Campaign campaign, CampaignAccountSettings campaignAccountSettings, BusinessEnterpriseEntity business,
			KontactoDTO customer) {
		
		String timezoneId = business.getTimezoneId();
		Integer surveyId = campaign.getSurveyId();
		Integer campaignSurveyFrequency = campaign.getSurveyCommFrequency();
		boolean bypassRestriction = CoreUtils.isTrueForInteger(campaign.getBypassCommRestriction());
		
		// Campaign-level restriction: check if bypass is enabled and frequency is defined
		if (bypassRestriction && campaignSurveyFrequency != null) {
			return isAutomationSurveyCommunicationAlreadySent(source, requestType, campaignSurveyFrequency, timezoneId, customer, campaignAccountSettings, surveyId);
		} else if (campaignAccountSettings != null && CoreUtils.isTrueForInteger(campaignAccountSettings.getIsCommRestrictionEnabled())) {
			// Global-level restriction: check based on account or location settings
			int campaignFrequency = getCampaignTypeFrequency(campaignAccountSettings, campaign);
			boolean isAccountLevel = CoreUtils.isTrueForInteger(campaignAccountSettings.getRestrictionAtAccountLevel());
			
			return isAccountLevel ? isAccountLevelSurveyCommunicationAlreadySent(source, requestType, campaignFrequency, timezoneId, customer, campaignAccountSettings, surveyId)
					: isLocationLevelSurveyCommunicationAlreadySent(source, requestType, campaignFrequency, timezoneId, customer, campaignAccountSettings, surveyId);
		}
		// No restrictions found — allow communication
		return false;
	}
	
	/**
	 * Saves or updates a {@link SurveyCustomerCommCapping} entity.
	 * <p>
	 * Handles concurrency issues such as {@link ObjectOptimisticLockingFailureException},
	 * {@link DataIntegrityViolationException}, and {@link StaleStateException}.
	 * If an optimistic lock error or data integrity issue occurs, it attempts to fetch
	 * the existing record and update it again.
	 *
	 * @param accountCustomerCommCapping the {@link SurveyCustomerCommCapping} object to be saved or updated
	 */
	private void saveOrUpdateSurveyAccountCustomerCommCapping(SurveyCustomerCommCapping accountCustomerCommCapping) {
		try {
			accountCustomerCommCapping = surveyCustomerCommCappingRepo.saveAndFlush(accountCustomerCommCapping);
		} catch (ObjectOptimisticLockingFailureException | DataIntegrityViolationException | StaleStateException e) {
			logger.error("hibernate lock error while updating survey account customer capping for {} and account customer id {} - updating it again",
					accountCustomerCommCapping.getId(), accountCustomerCommCapping.getAccountCustomerId());
			
			SurveyCustomerCommCapping existingAccountCommCapping = surveyCustomerCommCappingRepo
					.findFirstByAccountCustomerIdAndSurveyId(accountCustomerCommCapping.getAccountCustomerId(), accountCustomerCommCapping.getSurveyId());
			// Copy fields from new object to existing object
			prepareSurveyCustomerCommCapping(existingAccountCommCapping, accountCustomerCommCapping);
			
			try {
				surveyCustomerCommCappingRepo.saveAndFlush(existingAccountCommCapping);
			} catch (ObjectOptimisticLockingFailureException | DataIntegrityViolationException | StaleStateException exe) {
				logger.error("again hibernate lock error while updating survey account customer capping for {} and account customer id {} - updating it again",
						accountCustomerCommCapping.getId(), accountCustomerCommCapping.getAccountCustomerId());
				SurveyCustomerCommCapping existingSurveyAccountCommCapping = surveyCustomerCommCappingRepo
						.findFirstByAccountCustomerIdAndSurveyId(accountCustomerCommCapping.getAccountCustomerId(), accountCustomerCommCapping.getSurveyId());
				
				prepareSurveyCustomerCommCapping(existingSurveyAccountCommCapping, accountCustomerCommCapping);
				
				surveyCustomerCommCappingRepo.saveAndFlush(existingSurveyAccountCommCapping);
			} catch (Exception exe) {
				logger.error("again error while updating the survey account customer capping for id {} and account customer id {} error :: {}", accountCustomerCommCapping.getId(),
						accountCustomerCommCapping.getAccountCustomerId(), exe);
			}
		} catch (Exception exe) {
			logger.error("error while updating the survey account customer capping for id {} and account customer id {} error :: {}", accountCustomerCommCapping.getId(),
					accountCustomerCommCapping.getAccountCustomerId(), exe);
		}
	}
	
	/**
	 * Saves or updates a {@link SurveyCustomerCommCapping} record related to a survey location customer.
	 * <p>
	 * This method attempts to persist the entity using {@code saveAndFlush}. In case of concurrency-related exceptions
	 * such as {@link ObjectOptimisticLockingFailureException}, {@link DataIntegrityViolationException}, or {@link StaleStateException},
	 * it fetches the existing entity from the database and retries the update.
	 * <p>
	 * If a second retry is also needed, it re-fetches the entity again and tries a final update.
	 *
	 * @param locationCustomerCommCapping the {@link SurveyCustomerCommCapping} object representing the location customer's communication capping info
	 */
	private void saveOrUpdateSurveyLocationCustomerCommCapping(SurveyCustomerCommCapping locationCustomerCommCapping) {
		try {
			locationCustomerCommCapping = surveyCustomerCommCappingRepo.saveAndFlush(locationCustomerCommCapping);
		} catch (ObjectOptimisticLockingFailureException | DataIntegrityViolationException | StaleStateException e) {
			logger.error("hibernate lock error while updating survey location customer capping for {} and customer id {} - updating it again", locationCustomerCommCapping.getId(),
					locationCustomerCommCapping.getAccountCustomerId());
			
			SurveyCustomerCommCapping existingLocationCommCapping = surveyCustomerCommCappingRepo.findFirstByCustomerIdAndSurveyId(locationCustomerCommCapping.getCustomerId(),
					locationCustomerCommCapping.getSurveyId());
			
			// Copy updated fields to the existing record
			prepareSurveyCustomerCommCapping(existingLocationCommCapping, locationCustomerCommCapping);
			
			try {
				surveyCustomerCommCappingRepo.saveAndFlush(existingLocationCommCapping);
			} catch (ObjectOptimisticLockingFailureException | DataIntegrityViolationException | StaleStateException exe) {
				logger.error("again hibernate lock error while updating survey location customer capping for {} and customer id {} - updating it again",
						locationCustomerCommCapping.getId(), locationCustomerCommCapping.getAccountCustomerId());
				
				SurveyCustomerCommCapping existingSurveyLocationCommCapping = surveyCustomerCommCappingRepo
						.findFirstByCustomerIdAndSurveyId(locationCustomerCommCapping.getCustomerId(), locationCustomerCommCapping.getSurveyId());
				
				prepareSurveyCustomerCommCapping(existingSurveyLocationCommCapping, locationCustomerCommCapping);
				
				surveyCustomerCommCappingRepo.saveAndFlush(existingSurveyLocationCommCapping);
			} catch (Exception exe) {
				logger.error("again error while updating the survey location customer capping for id {} and customer id {} error :: {}", locationCustomerCommCapping.getId(),
						locationCustomerCommCapping.getAccountCustomerId(), exe);
			}
		} catch (Exception exe) {
			logger.error("error while updating the survey location customer capping for id {} and customer id {} error :: {}", locationCustomerCommCapping.getId(),
					locationCustomerCommCapping.getAccountCustomerId(), exe);
		}
	}
	
	private void prepareSurveyCustomerCommCapping(SurveyCustomerCommCapping existingCommCapping, SurveyCustomerCommCapping commCapping) {
		if (existingCommCapping == null) {
			existingCommCapping = new SurveyCustomerCommCapping();
		}
		existingCommCapping.setSurveyId(commCapping.getSurveyId());
		if (commCapping.getAccountCustomerId() != null) {
			existingCommCapping.setAccountCustomerId(commCapping.getAccountCustomerId());
		} else {
			existingCommCapping.setCustomerId(commCapping.getCustomerId());
		}
		if (commCapping.getSurveyEmailSentOn().after(new Date(0))) {
			existingCommCapping.setSurveyEmailSentOn(commCapping.getSurveyEmailSentOn());
		}
		if (commCapping.getSurveySmsSentOn().after(new Date(0))) {
			existingCommCapping.setSurveySmsSentOn(commCapping.getSurveySmsSentOn());
		}
		
	}
	
	/**
	 * Updates the communication capping information for an account customer based on the provided request.
	 * <p>
	 * This includes:
	 * <ul>
	 *     <li>Finding or creating a {@link SurveyCustomerCommCapping} record for the specific survey.</li>
	 *     <li>Updating the communication timestamp (email or SMS) based on the request source.</li>
	 *     <li>Saving or updating the entity while handling optimistic locking.</li>
	 * </ul>
	 *
	 * @param commCappingRequest the request object containing ECID, survey ID, source, and other metadata
	 */
	public void updateAccountSurveyCustomerCommCapping(UpdateCustomerCommCappingRequest commCappingRequest) {
		Integer ecid = commCappingRequest.getEcid();
		Integer surveyId = commCappingRequest.getSurveyId();
		String source = commCappingRequest.getSource();
		
		SurveyCustomerCommCapping accountCommCapping = surveyCustomerCommCappingRepo.findFirstByAccountCustomerIdAndSurveyId(ecid, surveyId);
		
		if (accountCommCapping == null) {
			accountCommCapping = new SurveyCustomerCommCapping();
			accountCommCapping.setAccountCustomerId(ecid);
			accountCommCapping.setSurveyId(surveyId);
		}
		logger.info("Account survey comm capping is {}", accountCommCapping);
		Date lastSentOn = new Date();
		if (StringUtils.equalsIgnoreCase(Constants.RR_SOURCE_EMAIL, source)) {
			accountCommCapping.setSurveyEmailSentOn(lastSentOn);
		} else {
			accountCommCapping.setSurveySmsSentOn(lastSentOn);
		}
		
		logger.info("after update account survey commm capping {}", accountCommCapping);
		// Save or update per survey or across all survey data
		saveOrUpdateSurveyAccountCustomerCommCapping(accountCommCapping);
		
	}
	
	/**
	 * Updates the communication capping data for a survey related to a specific location-level customer.
	 * <p>
	 * This includes:
	 * <ul>
	 *   <li>Retrieving or creating a {@link SurveyCustomerCommCapping} record for the given customer and survey.</li>
	 *   <li>Setting the last sent timestamp based on the source (email or SMS).</li>
	 *   <li>Pushing an update message to Kafka for downstream systems.</li>
	 *   <li>Saving or updating the capping data in the database with retry logic.</li>
	 *   <li>Updating the cache with the latest communication time for real-time capping validation.</li>
	 * </ul>
	 *
	 * @param commCappingRequest contains customer ID, survey ID, source type, and other context.
	 */
	public void updateLocationSurveyCustomerCommCapping(UpdateCustomerCommCappingRequest commCappingRequest) {
		Integer customerId = commCappingRequest.getCustomerId();
		Integer surveyId = commCappingRequest.getSurveyId();
		String source = commCappingRequest.getSource();
		UpdateCampaignCustomerRequest updateRequest = new UpdateCampaignCustomerRequest();
		updateRequest.setCustomerId(customerId);
		updateRequest.setSource(source);
		
		SurveyCustomerCommCapping locationCommCapping = surveyCustomerCommCappingRepo.findFirstByCustomerIdAndSurveyId(customerId, surveyId);
		
		if (locationCommCapping == null) {
			locationCommCapping = new SurveyCustomerCommCapping();
			locationCommCapping.setCustomerId(customerId);
			locationCommCapping.setSurveyId(surveyId);
		}
		logger.info("Location survey comm capping is {}", locationCommCapping);
		Date lastSentOn = new Date();
		if (StringUtils.equalsIgnoreCase(Constants.RR_SOURCE_EMAIL, source)) {
			updateRequest.setRrEmailOn(lastSentOn);
			locationCommCapping.setSurveyEmailSentOn(lastSentOn);
			kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_CUSTOMER_UPDATE, new KafkaMessage(updateRequest));
		} else {
			updateRequest.setRrSmsOn(lastSentOn);
			locationCommCapping.setSurveySmsSentOn(lastSentOn);
			kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_CUSTOMER_UPDATE, new KafkaMessage(updateRequest));
		}
		
		logger.info("after update location survey commm capping {}", locationCommCapping);
		// Save or update the specific survey capping info
		saveOrUpdateSurveyLocationCustomerCommCapping(locationCommCapping);
		
		// Update the last communication time in cache for this customer
		updateCampaignLastCommunicationTimeInCache(commCappingRequest, lastSentOn);
	}
	
	private void updateSurveyLastCommDate(SurveyCustomerCommCapping surveyCommCapping, Date lastSentOn, String source) {
		if (StringUtils.equalsIgnoreCase(Constants.RR_SOURCE_EMAIL, source)) {
			surveyCommCapping.setSurveyEmailSentOn(lastSentOn);
		} else {
			surveyCommCapping.setSurveySmsSentOn(lastSentOn);
		}
	}
	
	private SurveyCustomerCommCapping prepareSurveyCustomerCommCapping(UpdateCustomerCommCappingRequest commCappingRequest, boolean isAccountLevel, Date lastSentOn, boolean isPerSurveyRequest) {
		SurveyCustomerCommCapping surveyCommCapping = new SurveyCustomerCommCapping();
		if (isAccountLevel) {
			surveyCommCapping.setAccountCustomerId(commCappingRequest.getEcid());
		} else {
			surveyCommCapping.setCustomerId(commCappingRequest.getCustomerId());
		}
		
		if(isPerSurveyRequest) {
			surveyCommCapping.setSurveyId(commCappingRequest.getSurveyId());
		}
		
		if (StringUtils.equalsIgnoreCase(Constants.RR_SOURCE_EMAIL, commCappingRequest.getSource())) {
			surveyCommCapping.setSurveyEmailSentOn(lastSentOn);
		} else {
			surveyCommCapping.setSurveySmsSentOn(lastSentOn);
		}
		return surveyCommCapping;
	}
	
	/**
	 * Updates the last communication timestamp for a customer in the survey campaign cache.
	 * <p>
	 * This method prepares a {@link KontactoDTO} customer object using data from the request
	 * and calls the {@link CustomerService} to update the aerospike cache with the latest 
	 * communication time (email/SMS) for tracking capping.
	 *
	 * @param commCappingRequest the request containing customer ID, ECID, source, request type, etc.
	 * @param lastSentOn the timestamp when the last communication was sent
	 */
	private void updateCampaignLastCommunicationTimeInCache(UpdateCustomerCommCappingRequest commCappingRequest, Date lastSentOn) {
		KontactoDTO customer = new KontactoDTO();
		customer.setEcid(commCappingRequest.getEcid());
		customer.setId(commCappingRequest.getCustomerId());
		customerService.updateSurveyCampaignLastCommunicationTimeToCaches(commCappingRequest.getSource(), commCappingRequest.getRequestType(), lastSentOn, 86400, customer, commCappingRequest.getIsPerSurveyRequest(), commCappingRequest.getSurveyId());
	}
	
	/**
	 * Determines whether the communication restriction is scoped per survey.
	 * <p>
	 * The logic checks if an override flag is provided; if so, it forces a per-survey restriction.
	 * Otherwise, it evaluates account-level settings to determine whether communication restriction
	 * is enabled and if its scope is limited to per-survey.
	 *
	 * @param campaignAccountSettings the campaign account settings which may define restriction behavior
	 * @param overRideCommRestriction an optional override flag (1 = force per-survey restriction)
	 * @return {@code true} if the request is considered per-survey, {@code false} otherwise
	 */
	public boolean isPerSurveyRequest(CampaignAccountSettings campaignAccountSettings, Integer overRideCommRestriction) {
	    if (CoreUtils.isTrueForInteger(overRideCommRestriction)) {
	        return true;
	    } else if (campaignAccountSettings != null && CoreUtils.isTrueForInteger(campaignAccountSettings.getIsCommRestrictionEnabled())) {
	        return campaignAccountSettings.getSurveyRestrictionScope() == 1;
	    }
	    return false;
	}
	
	/**
	 * Retrieves the survey communication frequency for a given campaign.
	 * <p>
	 * Priority is given to the campaign-specific frequency if it's set
	 * (i.e., non-null and non-zero). If not set, the method falls back 
	 * to the global frequency defined in the campaign account settings.
	 *
	 * @param campaignAccountSettings the account-level settings containing global frequency configuration
	 * @param campaign the campaign whose specific frequency setting is being evaluated
	 * @return the effective survey communication frequency for the campaign
	 */
	private Integer getSurveyCampaignFrequency(CampaignAccountSettings campaignAccountSettings, Campaign campaign) {
	    Integer campaignCommFrequency = campaign.getSurveyCommFrequency();
	    
	    if (campaignCommFrequency != null && campaignCommFrequency != 0) {
	        return campaignCommFrequency;
	    }

	    return getCampaignTypeFrequency(campaignAccountSettings, campaign);
	}

	/**
	 * Determines the appropriate mail resend frequency for a given campaign and business.
	 * <p>
	 * If survey restrictions are enabled and the campaign meets specific criteria
	 * (i.e., it is a survey request, not a split campaign, and is ongoing),
	 * then the survey-specific communication frequency is returned.
	 * Otherwise, the global campaign type frequency is used.
	 *
	 * @param business the business entity for which the campaign is being evaluated
	 * @param campaign the campaign whose frequency configuration is needed
	 * @param isSurveyRestrictionEnable flag indicating whether survey restriction logic should be applied
	 * @return the resend frequency in days for the email campaign
	 */
	private Integer getCampaignCommFrequency(BusinessEnterpriseEntity business, Campaign campaign, boolean isSurveyRestrictionEnable) {
		CampaignAccountSettings campaignAccountSettings = getCampaignAccountSetting(business);
		
		boolean isSurveyCampaignRequest = isSurveyRestrictionEnable && campaign != null 
				&& StringUtils.equalsIgnoreCase(CampaignTypeEnum.SURVEY_REQUEST.getType(), campaign.getType()) && !CoreUtils.isTrueForInteger(campaign.getIsSplitCampaign()) 
				&& StringUtils.equalsIgnoreCase(CampaignRunTypeEnum.ONGOING.getRunType(), campaign.getRunType());
		
		return isSurveyCampaignRequest ? getSurveyCampaignFrequency(campaignAccountSettings, campaign) : getCampaignTypeFrequency(campaignAccountSettings, campaign);
	}

}