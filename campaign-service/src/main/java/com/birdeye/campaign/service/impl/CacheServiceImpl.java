package com.birdeye.campaign.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import com.birdeye.campaign.entity.*;
import com.birdeye.campaign.service.dao.AccountCommLimitsDao;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.aspect.annotation.Profiled;
import com.birdeye.campaign.business.service.BusinessService;
import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.ParametersCache;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.dto.BusinessBrandingWithResellerInfo;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.BusinessProfileResponse;
import com.birdeye.campaign.dto.BusinessTemplateEntity;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.dto.LocationInfo;
import com.birdeye.campaign.dto.LocationsDetail;
import com.birdeye.campaign.dto.SplitCampaignMappingDTO;
import com.birdeye.campaign.enums.BusinessAccountTypeEnum;
import com.birdeye.campaign.executor.services.CampaignCallable;
import com.birdeye.campaign.executor.services.CampaignExecutorService;
import com.birdeye.campaign.executor.services.ExecutorCommonService;
import com.birdeye.campaign.external.service.AppointmentExternalService;
import com.birdeye.campaign.external.service.BizAppsExternalService;
import com.birdeye.campaign.external.service.BusinessExternalService;
import com.birdeye.campaign.platform.entity.AggregationSource;
import com.birdeye.campaign.platform.entity.User;
import com.birdeye.campaign.platform.readonly.repository.AggregationSourceReadOnlyRepository;
import com.birdeye.campaign.platform.readonly.repository.BusinessReadOnlyRepo;
import com.birdeye.campaign.platform.readonly.repository.UserReadOnlyRepo;
import com.birdeye.campaign.readonly.repository.BusinessEmailTemplateReadOnlyRepo;
import com.birdeye.campaign.repository.BusinessEmailTemplateRepo;
import com.birdeye.campaign.repository.CampaignAccountSettingsRepo;
import com.birdeye.campaign.repository.CampaignRepo;
import com.birdeye.campaign.repository.ReferralSettingRepo;
import com.birdeye.campaign.repository.ReviewGenerationSourceRepository;
import com.birdeye.campaign.repository.SystemPropertyRepository;
import com.birdeye.campaign.request.ProductFeatureRequest;
import com.birdeye.campaign.request.core.BusinessDataBatchGetRequest;
import com.birdeye.campaign.response.AppointmentSettings;
import com.birdeye.campaign.response.external.BizappsPrimaryContactResponse;
import com.birdeye.campaign.response.external.BusinessFeaturesResponse;
import com.birdeye.campaign.response.external.BusinessOptionsResponse;
import com.birdeye.campaign.response.external.GetBusinessLiteResponse;
import com.birdeye.campaign.response.external.UserLocationAccessResponse;
import com.birdeye.campaign.reviewgen.response.ReviewGenerationSourcesResponse;
import com.birdeye.campaign.service.BusinessOptionService;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.dao.BusinessSmsTemplateDao;
import com.birdeye.campaign.service.dao.SplitCampaignDao;
import com.birdeye.campaign.social.service.BamService;
import com.birdeye.campaign.utils.BusinessBrandingUtils;
import com.birdeye.campaign.utils.CoreUtils;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service("cacheService")
public class CacheServiceImpl implements CacheService {
	
	@Autowired
	private BusinessReadOnlyRepo				businessReadOnlyRepo;
	
	@Autowired
	private BusinessExternalService				businessExternalService;
	
	@Autowired
	private AggregationSourceReadOnlyRepository	aggregationSourceReadOnlyRepo;
	
	@Autowired
	private ReviewGenerationSourceRepository	reviewGenerationSourceRepository;
	
	@Autowired
	private BusinessService						businessService;
	
	@Autowired
	private BusinessOptionService				businessOptionService;
	
	@Autowired
	private ReferralSettingRepo					referralSettingRepo;
	
	@Autowired
	private CampaignRepo						campaignRepo;
	
	@Autowired
	private SystemPropertyRepository			systemPropertyRepo;
	
	@Autowired
	private CampaignAccountSettingsRepo			campaignAccountSettingsRepo;
	
	@Autowired
	private UserReadOnlyRepo							userRepo;
	
	@Autowired
	private ReviewGenerationSourceRepository			reviewGenerationSourceRepo;
	
	@Autowired
	private BamService									bamService;
	
	@Autowired
	private BusinessEmailTemplateRepo                   businessEmailTemplateRepo;
	
	@Autowired
	private BusinessEmailTemplateReadOnlyRepo			businessEmailTemplateReadOnlyRepo;
	
	@Autowired
	private BusinessSmsTemplateDao                      businessSmsTemplateDao;

	@Autowired
	private BizAppsExternalService				        bizzAppExternalService;
	
	@Autowired
	private AppointmentExternalService					appointmentExternalService;

	@Autowired
	private SplitCampaignDao                            splitCampaignDao;
	
	@Autowired
	@Qualifier(Constants.CAMPAIGN_COMPLETABLE_FUTURE_TASK_EXECUTOR)
	private ThreadPoolTaskExecutor						threadPoolTaskExecutor;
	
	@Autowired
	private ExecutorCommonService						executorCommonService;

	@Autowired
	private AccountCommLimitsDao                        accountCommLimitsDao;

	
	private static final Logger					logger	= LoggerFactory.getLogger(CacheServiceImpl.class);
	
	// Caution : Core service is not sending correct product name in BrandingInfoByLocationResponse#name field. Use getSourceAliasForDefaultAggregation
	// method instead to get product name.
	@Override
	@Cacheable(value = "productNameCache1", unless = "#result == null")
	public String getProductName(Integer businessId) {
		
		BusinessEnterpriseEntity business = businessReadOnlyRepo.getValidBusinessByBid(businessId);
//		BrandingInfo brandingInfo = businessExternalService.getBrandingInfoByEnterpriseId(businessId);
		BusinessBrandingWithResellerInfo businessBrandingWithResellerInfo = businessService.getBusinessBrandingWithResellerInfo(businessId);
		if (businessBrandingWithResellerInfo != null && (BusinessAccountTypeEnum.COBRANDED.getType().equalsIgnoreCase(business.getAccountType())
				|| BusinessAccountTypeEnum.WHITELABELED.getType().equalsIgnoreCase(business.getAccountType()))) {
			return businessBrandingWithResellerInfo.getProductName();
		} else {
			String productNameParam = CacheManager.getInstance().getCache(ParametersCache.class).getProperty("product_name");
			String productName = Constants.DEFAULT_BIRDEYE_SOURCENAME_BIRDEYE;
			if (StringUtils.isNotBlank(productNameParam)) {
				productName = productNameParam;
			}
			return productName;
		}
	}
	
	/**
	 * For direct business (SMB, location under enterprise) it would be birdeye
	 * For reseller cases, it would be first reseller name. (for both whitelabel & cobranded.)
	 * 
	 * @param businessId
	 *            - location id for business where communication is getting sent.
	 * @return Name for default business aggregation (100)
	 */
	@Override
	@Cacheable(value = "bizAggNameCache1", unless = "#result == null")
	public String getSourceAliasForDefaultAggregation(Integer businessId) {
		BusinessEnterpriseEntity business = businessReadOnlyRepo.getBusinessByBid(businessId);
		
		// For direct business, Our website needs to be replaced with Birdeye
		if (BusinessBrandingUtils.isDirectBusiness(business)) {
			return Constants.DEFAULT_BIRDEYE_SOURCENAME_BIRDEYE;
		}
		
		// For enterprise location, business should be Enterprise
		if (business.getEnterpriseId() != null) {
			business = businessReadOnlyRepo.getBusinessByBid(business.getEnterpriseId());
		}
		
		if (business.getResellerId() != null) {
			business = businessReadOnlyRepo.getBusinessByBid(business.getResellerId());
			if (business != null) {
				return business.getBusinessName();
			}
		}
		return Constants.DEFAULT_BIRDEYE_SOURCENAME_BIRDEYE;
	}
	
	@Override
	@Cacheable(value = "sourceCache1", unless = "#result == null")
	public String getAggregationSourceName(Integer sourceId) {
		AggregationSource source = aggregationSourceReadOnlyRepo.findById(sourceId).orElse(null);
		if (source != null) {
			return source.getName();
		}
		return null;
	}
	
	@Override
	@Cacheable(value = "reviewGenSourceCache1", unless = "#result == null")
	public String getReviewGenSourceName(Integer sourceId) {
		ReviewGenerationSource source = reviewGenerationSourceRepository.findById(sourceId).orElse(null);
		if (source != null) {
			return source.getName();
		}
		return null;
	}
	
	@Override
	@Cacheable(value = "enterpriseReviewGenSourceCache1", unless = "#result == null")
	public ReviewGenerationSourcesResponse getEnterpriseReviewGenSourceName(Integer enterpriseId) {
		try {
			String res = "{\"businessId\":97554,\"sources\":[{\"id\":15,\"name\":\"Dex Knows\",\"supported\":1},{\"id\":23,\"name\":\"Vitals\",\"supported\":1},{\"id\":103,\"name\":\"WebMD\",\"supported\":1},{\"id\":100,\"name\":\"Our Website\",\"supported\":1},{\"id\":13,\"name\":\"Kudzu\",\"supported\":1},{\"id\":110,\"name\":\"Facebook\",\"supported\":1},{\"id\":5,\"name\":\"Yellow Pages\",\"supported\":1},{\"id\":324,\"name\":\"Md.com\",\"supported\":1},{\"id\":345,\"name\":\"Fertilityiq\",\"supported\":1},{\"id\":17,\"name\":\"RateMDs\",\"supported\":1},{\"id\":10,\"name\":\"Foursquare\",\"supported\":1},{\"id\":49,\"name\":\"Groupon\",\"supported\":1},{\"id\":394,\"name\":\"CareDash\",\"supported\":1},{\"id\":24,\"name\":\"Wellness\",\"supported\":1},{\"id\":84,\"name\":\"Mapquest\",\"supported\":1},{\"id\":33,\"name\":\"Merchant Circle\",\"supported\":1},{\"id\":42,\"name\":\"ZocDoc\",\"supported\":1},{\"id\":3,\"name\":\"Citysearch\",\"supported\":1},{\"id\":1,\"name\":\"Yelp\",\"supported\":1},{\"id\":68,\"name\":\"Doctorbase\",\"supported\":1},{\"id\":2,\"name\":\"Google\",\"supported\":1},{\"id\":16,\"name\":\"Superpages\",\"supported\":1},{\"id\":82,\"name\":\"Healthgrades\",\"supported\":1},{\"id\":4,\"name\":\"Yahoo! Local\",\"supported\":1},{\"id\":6,\"name\":\"Insider Pages\",\"supported\":1},{\"id\":69,\"name\":\"Toothssenger\",\"supported\":1},{\"id\":63,\"name\":\"YellowBot\",\"supported\":1},{\"id\":139,\"name\":\"Realself\",\"supported\":1}]}";
			ObjectMapper mapper = new ObjectMapper();
			ReviewGenerationSourcesResponse reviewSourcesResponse = mapper.readValue(res, ReviewGenerationSourcesResponse.class);// platformExternalServiceImpl.getReviewSourcesForEnetrprise(enterpriseId)
			if (reviewSourcesResponse != null) {
				return reviewSourcesResponse;
			}
		} catch (Exception exe) {
			logger.error("error {} while parsing the data for weil cornel", ExceptionUtils.getStackTrace(exe));
		}
		return null;
	}
	
	@Override
	@Cacheable(value = "businessCache", unless = "#result == null", key = "#businessId")
	public BusinessEnterpriseEntity getBusinessById(Integer businessId) {
		return businessService.getBusinessById(businessId);
	}
	
	@Override
	@Cacheable(value = "businessMigrationCache", unless = "#result == null", key = "#businessId")
	public BusinessEnterpriseEntity getBusinessByIdForMigration(Integer businessId) {
		return businessService.getBusinessById(businessId);
	}
	
	@Override
	@Cacheable(value = "businessNumberCache", unless = "#result == null", key = "#businessId")
	public BusinessEnterpriseEntity getBusinessByBusinessNumber(Long businessId) {
		return businessService.getBusinessByBusinessNumber(businessId);
	}
	
	@Override
	@Cacheable(value = "businessBatchnDetailsByIdsCache", key = "#key", unless = "#result == null")
	public Map<Long, GetBusinessLiteResponse> getBusinessDataInBatch(Set<Integer> enterpriseIds, boolean location, String key){
		BusinessDataBatchGetRequest request = new BusinessDataBatchGetRequest();	
		request.setBusinessIds(new ArrayList<>(enterpriseIds));
		
		return businessExternalService.getBusinessDataInBatch(request, false);
	}
	
	@Override
	@Cacheable(value = "businessLocationCache", unless = "#result == null", key = "#businessId")
	public BusinessEnterpriseEntity getBusinessLocationById(Integer businessId) {
		return businessReadOnlyRepo.getBusinessLocationById(businessId);
	}
	
	@Override
	@Cacheable(value = "businessLocationByLongIdCache", unless = "#result == null", key = "#businessLongId")
	public BusinessEnterpriseEntity getBusinessLocationByLongId(Long businessLongId) {
		return businessReadOnlyRepo.getBusinessLocationByBusinessLongId(businessLongId);
	}
	
	@Override
	@Cacheable(value = "productFeatureCache", unless = "#result == null", key = "#enterpriseId")
	public ProductFeatureRequest getProductFeatureForBusiness(Integer enterpriseId) {
		return getProductFeature(enterpriseId);
	}
	
	public ProductFeatureRequest getProductFeature(Integer enterpriseId) {
		BusinessOptionsResponse response = businessExternalService.getBusinessOptions(enterpriseId, true);
		boolean isSurveyEnabledForBusiness = businessOptionService.isSurveyEnabledForBusiness(enterpriseId);
		ProductFeatureRequest request = new ProductFeatureRequest();
		request.setIsSurveyEnabled(isSurveyEnabledForBusiness ? 1 : 0);
		request.setEnableReferral(response.getEnableReferral());
		request.setIsSuspectSupportOn(response.getIsSuspectSupportOn());
		request.setAppointmentRemindersEnabled(response.getAppointmentRemindersEnabled());
		request.setAppointmentRecallEnabled(response.getAppointmentRecallEnabled());
		request.setAppointmentSchedulingEnabled(response.getAppointmentSchedulingEnabled());
		request.setAppointmentFormEnabled(response.getAppointmentFormsEnabled() != null ? response.getAppointmentFormsEnabled() : 0);
		request.setReviewGenEnabled(response.getReviewGenEnabled());
		request.setBulkCustomSms(response.getBulkCustomSms());
		request.setCampaignLocationUserAccess(response.getCampaignLocationUserAccess());
		request.setLocationLevelTemplateEnabled(response.getLocationTemplateEnabled() != null ? response.getLocationTemplateEnabled() : 0);
		return request;
	}
	
	@Override
	@Cacheable(value = "businessOptionsCache", unless = "#result == null", key = "#businessId.toString().concat('-hierarchy-').concat(#traversehierarchy ? 'true' : 'false')")
	public BusinessOptionsResponse getBusinessOptionsCached(Integer businessId, boolean traversehierarchy) {
		return businessExternalService.getBusinessOptions(businessId, traversehierarchy);
	}
	
	@Override
	@Cacheable(value = "productFeatureCacheForAsyncReports", unless = "#result == null", key = "#accountId")
	public ProductFeatureRequest getAccountProductFeatureForAsynchronousReports(Integer accountId) {
		return getProductFeature(accountId);
	}
	
	/**
	 * returns DEFAULT referral setting
	 * 
	 * TODO : 1. make it in-memory cache later
	 * 2. Also check the usefulness of this table, can it be removed? - After Referral Form configuration in template
	 * 
	 * @return
	 */
	@Override
	@Cacheable(value = "referralSettingDefaultCache", unless = "#result == null", key = "referralSettingCache_default")
	public ReferralSetting findDefaultReferralFormSettings() {
		return referralSettingRepo.findByBusinessIdIsNull();
	}
	
	@Override
	@Cacheable(key = "#userId", value = "userNameCache", unless = "#result == null")
	public String getUserNameById(Integer userId) {
		User user = userRepo.findById(userId).orElse(null);
		if (user == null) {
			return null;
		}
		return user.getName();
	}
	
	@Override
	@Cacheable(value = "defaultOngoingCampaign", unless = "#result == null", key = "#businessId")
	public List<Campaign> getDefaultOngoingCampaignByEnterpriseId(Integer businessId) {
		List<Campaign> campaignList = campaignRepo.getDefaultOngoingCampaignByEnterpriseId(businessId);
		if (CollectionUtils.isEmpty(campaignList)) {
			return null;
		}
		return campaignList;
	}
	
	@Cacheable(value = "ESToESDataMigrationOffset", key = "'ESToESDataMigrationOffset'", unless = "#result==null")
	@Override
	public String getESToESDataMigrationOffset() {
		SystemProperty sysProperty = systemPropertyRepo.findByName("es_to_es_data_migration_offset_id");
		if (sysProperty != null)
			return sysProperty.getValue();
		
		return null;
	}
	
	@CachePut(value = "ESToESDataMigrationOffset", key = "'ESToESDataMigrationOffset'", unless = "#result==null")
	@Override
	public String updateESToESDataMigrationOffset(Long offset) {
		return String.valueOf(offset);
	}
	
	// BIRDEYE-93797 storing the service area provider flag in cache
	@Override
	@Cacheable(value = "businessLiteCache", unless = "#result == null", key = "#businessId")
	public GetBusinessLiteResponse getBusinessLiteDetails(Integer businessId) {
		if (businessId != null) {
			return businessExternalService.getBusinessLite(businessId, true);
		}
		return null;
	}
	
	@Override
	@Cacheable(value = "campaignAccountSettingsCache", unless = "#result == null", key = "#businessId")
	public List<CampaignAccountSettings> getCampaignAccountSettings(Integer businessId) {
		return campaignAccountSettingsRepo.getCampaignAccountSettingsByAccountId(businessId);
	}
	
	@Async
	@Override
	@CacheEvict(key = "#businessId.toString()", value = "campaignAccountSettingsCache")
	public void evictCampaignAccountSettingsCache(Integer businessId) {
		// method to evictCampaignAccountSettingsCache
	}
	
	@Override
	@Cacheable(value = "freeTrialAccountCommLimitsCache", unless = "#result == null", key = "#accountId")
	public AccountCommLimits getAccountCommLimits(Integer accountId) {
		return accountCommLimitsDao.getActiveReviewsFreeTrialByAccountId(accountId);
	}
	
	@Override
	@CacheEvict(value = "freeTrialAccountCommLimitsCache", key = "#accountId")
	public void evictAccountCommLimitsCache(Integer accountId) {
		// Method to evict freeTrialAccountCommLimitsCache
	}
	
	@Override
	@Cacheable(value = "reviewGenerationSourceCache", key = "#id", unless = "#result ==null")
	public ReviewGenerationSource getReviewGenerationSource(Integer id) {
		Optional<ReviewGenerationSource> reviewGenerationSource = reviewGenerationSourceRepo.findById(id);
		return reviewGenerationSource.orElse(null);
	}
	
	@Override
	@Cacheable(value = "enterpriseLocationDetailsCache", key = "#enterpriseId", unless = "#result ==null")
	public LocationsDetail getEnterpriseAllLocationsDetail(Integer enterpriseId) {
		LocationsDetail response = new LocationsDetail();
		List<LocationInfo> locationDetailsList = new ArrayList<>();
		try {
			locationDetailsList = businessService.getLocationsInfo(enterpriseId);
			if (CollectionUtils.isEmpty(locationDetailsList)) {
				return null;
			}
			response.setLocationsDetail(locationDetailsList);
		} catch (Exception e) {
			
			logger.error("Error fetching location details for enterprise id {} :: {}", enterpriseId, ExceptionUtils.getStackTrace(e));
			return null;
		}
		
		return response;
	}
	
	@Override
	@Cacheable(value = "placeUrlForBusiness", key = "#businessId", unless = "#result ==null")
	public String fetchPlaceUrlforBusinessId(Integer businessId) {
		
		return bamService.getGooglePlaceIdforABusiness(businessId);
	}
	
	@Override
	@Cacheable(key = "#userId", value = "userEmailCache", unless = "#result == null")
	public String getUserEmailById(Integer userId) {
		User user = userRepo.findById(userId).orElse(null);
		if (user == null) {
			return null;
		}
		return user.getEmailId();
	}
	
	@Cacheable(value = "validBusinessCache", unless = "#result == null", key = "#businessId")
	public BusinessEnterpriseEntity getValidBusinessById(Integer businessId) {
		return businessService.getValidBusinessById(businessId);
	}

	@Override
	@CacheEvict(key = "#accountId.toString().concat('-').concat(#templateId.toString()).concat('-').concat(#deviceType.toString())"  ,value = "selectedQRReviewSourcesCache")
	public void deleteQRReviewSourcesForTemplate(Integer templateId, Integer accountId, String deviceType) {
		
	}	
	
	@Override
	@Cacheable(value = "businessLocationCache", unless = "#result == null", key = "#businessId")
	public BusinessEnterpriseEntity getBusinessLocationByBId(Integer businessId) {
		return businessReadOnlyRepo.getBusinessLocationByBid(businessId);
	}
	
	@Override
	@Cacheable(value = "bzAppsIndustryCache", unless = "#result == null", key = "#businessNumber")
	public BizappsPrimaryContactResponse getPrimaryContactInfo(Long businessNumber) {
		if (businessNumber != null) {
			return bizzAppExternalService.getPrimaryContactInfo(businessNumber);
		}
		return null;
	}

	@Cacheable(value = "businessProfileCache", unless = "#result == null", key = "#businessId")
	public BusinessProfileResponse getBusinessProfileByBusinessId(Integer businessId) {
		return businessExternalService.getBusinessProfileInfoByBusinessId(businessId);
	}
	
	@Override
	@Cacheable(key = "#accountId.toString().concat('-').concat(#templateType).concat('-').concat('email')", value = "defaultTemplateCache", unless = "#result == null")
	public BusinessTemplateEntity getDefaultEmailTemplate(Integer accountId, String templateType) {
		List<BusinessTemplateEntity> businessEmailTemplateList = businessEmailTemplateRepo.getActiveDefaultTemplateForType(accountId, templateType);
		if (CollectionUtils.isNotEmpty(businessEmailTemplateList)) {
			return businessEmailTemplateList.get(0);
		}
		return null;
	}
	
	@Override
	@Cacheable(key = "#accountId.toString().concat('-').concat(#templateType).concat('-').concat('sms')", value = "defaultTemplateCache", unless = "#result == null")
	public BusinessTemplateEntity getDefaultSmsTemplate(Integer accountId, String templateType) {
		List<BusinessTemplateEntity> businessSmsTemplateList = businessSmsTemplateDao.getDefaultSmsTemplateByEnterpriseIdAndType(accountId, templateType);
		if (CollectionUtils.isNotEmpty(businessSmsTemplateList)) {
			return businessSmsTemplateList.get(0);
		}
		return null;
	}
	
	@Override
	@CachePut(key = "#accountId.toString().concat('-').concat(#templateType).concat('-').concat(#source)", value = "defaultTemplateCache", unless = "#result == null")
	public BusinessTemplateEntity updateDefaultTemplateByEnterpriseIdAndTypeAndSource(Integer accountId, String templateType, String source, BusinessTemplateEntity template) {
		return template;
	}
	
	@Override
	@Cacheable(key = "#accountId.toString().concat('-').concat(#templateType).concat('-').concat(#source)", value = "defaultTemplateCache", unless = "#result == null")
	public BusinessTemplateEntity updateDefaultTemplateCacheIfNotPresent(Integer accountId, String templateType, String source, BusinessTemplateEntity template) {
		return template;
	}
	
	@Override
	@Profiled
	@Cacheable(key = "#enterpriseId.toString()", value = "emailTemplatesListCache", unless = "#result == null")
	public CachedCollectionWrapper<BusinessTemplateEntity> getTemplatesListByEnterpriseId(Integer enterpriseId) {
		return new CachedCollectionWrapper<BusinessTemplateEntity>(businessEmailTemplateRepo.getByEnterpriseIdAndIsDeletedV2(enterpriseId));
	}
	
	@Override
	@Cacheable(key = "#accountId.toString().concat('-').concat(#splitCampaignId)", value = "splitMappingCache", unless = "#result == null")
	public CachedCollectionWrapper<SplitCampaignMappingDTO> getSplitCampaignMappingListBySplitCampaignId(Integer splitCampaignId, Integer accountId) {
		List<SplitCampaignMappingDTO> mappingData = splitCampaignDao.getSplitMappingDataBySplitCampaignId(splitCampaignId);
		if (CollectionUtils.isNotEmpty(mappingData)) {
			return new CachedCollectionWrapper<SplitCampaignMappingDTO>(mappingData);
		}
		return null;
	}
	
	@Override
	@Profiled
	@Cacheable(key = "#splitCampaignId.toString()", value = "splitCampaignCache", unless = "#result == null")
	public SplitCampaign getSplitCampaignById(Integer splitCampaignId) {
		Optional<SplitCampaign> splitCampaign = splitCampaignDao.getSplitCampaignById(splitCampaignId);
		if (splitCampaign.isPresent()) {
			return splitCampaign.get();
		}
		return null;
	}
	
	@Override
	@Cacheable(key = "#enterpriseId.toString()", value = "appointmentSettingsCache")
	public AppointmentSettings getAppointmentSettings(Integer enterpriseId) {
		return appointmentExternalService.getAppointmentSettings(enterpriseId);
	}
	
	@Cacheable(key = "#userId", value = "userCache", unless = "#result == null")
	public User getUserDetailByUserId(Integer userId) {
		return userRepo.findById(userId).orElse(null);	
	}
	
	@Override
	@Cacheable(key = "#userId.toString().concat('-').concat(#accountId.toString())", value = "userInfoAndLocationAccessCache", unless = "#result == null")
	public UserLocationAccessResponse getUserInfoAndLocationAccessCached(Integer userId, Integer accountId) {
		return businessService.getUserInfoWithLocationAccess(userId, accountId);
	}
	
	@Override
	@CacheEvict(value = "modificationAuditCache", key = "T(String).valueOf(#entityId) + '-' + T(String).valueOf(#entityType) + T(String).valueOf(#category)")
	public void evictModificationAuditCache(Integer entityId, String entityType, String category) {
		logger.info("evicting modification audit cache for entityId: {}, type: {} & category: {}.", entityId, entityType, category);
	}

	/**
	 * Method to get name of user responsible for modification.
	 * 
	 * If user==null, @return ProductName
	 * else @return userName
	 * 
	 * @param userId
	 * @param accountId
	 * @return
	 */
	@Override
	@Cacheable(key = "T(String).valueOf(#userId) + '-' + T(String).valueOf(#accountId)", value = "userInfoCache", unless = "#result == null")
	public String getUserResponsibleForModification(Integer userId, Integer accountId) {
		if (Objects.isNull(userId)) {
			return getSourceAliasForDefaultAggregation(accountId);
		}
		User user = userRepo.findFirstById(userId);
		return CoreUtils.formatUserName(user.getFirstName(), user.getLastName(), user.getEmailId());
	}

	@Override
	@Cacheable(key = "#accountId.toString().concat('-account_id-').concat(#traverseHierarchy.toString())", value = "businessFeaturesCache", unless = "#result == null")
	public BusinessFeaturesResponse getBusinessFeaturesForAccountId(Integer accountId, boolean traverseHierarchy) {
		return businessService.getBusinessFeatures(accountId, null, traverseHierarchy);
	}
	
	@Override
	@Cacheable(key = "#accountNumber.toString().concat('-account_number-').concat(#traverseHierarchy.toString())", value = "businessFeaturesCache", unless = "#result == null")
	public BusinessFeaturesResponse getBusinessFeaturesForAccountNumber(Long accountNumber, boolean traverseHierarchy) {
		return businessService.getBusinessFeatures(null, accountNumber, traverseHierarchy);
	}

	@Override
	@CacheEvict(value = "communicationCategoryCache", key = "#requestSource.concat('-').concat(#templateId.toString())")
	public void evictCommunicationCategoryCache(String requestSource, Integer templateId) {
		logger.info("evicting communication category cache for templateId: {} & source: {}.", templateId, requestSource);
	}
	
	/**
	 * Get all email & sms template IDs for an account by template type & template category.
	 * 
	 * @param accountId
	 * @param templateType
	 * @param communicationCategory
	 * @return
	 */
	@Override
	@Cacheable(value = "accountTemplatesCache", key = "T(String).valueOf(#accountId) + '-' + T(String).valueOf(#templateType) + '-' + T(String).valueOf(#communicationCategory)", unless = "#result == null")
	public CachedCollectionWrapper<Integer> getTemplatesForAccountByTypeAndCategory(Integer accountId, String templateType, String communicationCategory) {
		if (Objects.isNull(accountId) || StringUtils.isBlank(templateType) || StringUtils.isBlank(communicationCategory)) {
			logger.info("Invalid request for fetching templates by type & category!");
			return null;
		}
		
		List<Integer> templateList = Collections.synchronizedList(new ArrayList<Integer>());
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
		executorService.submit(getSmsTemplatesForAccountByTypeAndCategory(templateList, accountId, templateType, communicationCategory));
		executorService.submit(getEmailTemplatesForAccountByTypeAndCategory(templateList, accountId, templateType, communicationCategory));
		
		try {
			executorCommonService.executeTasks(executorService, 5000);
		} catch (Exception exe) {
			
		}
		
		if (CollectionUtils.isNotEmpty(templateList)) {
			return new CachedCollectionWrapper<Integer>(templateList);
		}
		return null;
	}
	
	/*
	 * Get active email templates for an account by template type & email category task.
	 */
	private CampaignCallable<Boolean> getEmailTemplatesForAccountByTypeAndCategory(List<Integer> templateList, Integer accountId, String templateType,
			String communicationCategory) {
		return new CampaignCallable<Boolean>("Get Email Templates Task") {
			@Override
			public Boolean doCall() {
				List<Integer> emailTemplates = businessEmailTemplateReadOnlyRepo.getActiveTemplatesForAccountByTypeAndCategory(accountId, templateType, communicationCategory);
				if (CollectionUtils.isNotEmpty(emailTemplates)) {
					templateList.addAll(emailTemplates);
				}
				return true;
			}
		};
	}
	
	/*
	 * Get active SMS templates for an account by template type & SMS category task.
	 */
	private CampaignCallable<Boolean> getSmsTemplatesForAccountByTypeAndCategory(List<Integer> templateList, Integer accountId, String templateType, String communicationCategory) {
		return new CampaignCallable<Boolean>("Get Sms Templates Task") {
			@Override
			public Boolean doCall() {
				List<Integer> smsTemplates = businessSmsTemplateDao.getSmsTemplatesForAccountByTypeAndCategory(accountId, templateType, communicationCategory);
				if (CollectionUtils.isNotEmpty(smsTemplates)) {
					templateList.addAll(smsTemplates);
				}
				return true;
			}
		};
	}
}
