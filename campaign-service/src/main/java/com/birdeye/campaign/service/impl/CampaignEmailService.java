package com.birdeye.campaign.service.impl;

import java.io.IOException;

import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.NexusTemplateContentProcessDTO;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.Promotion;
import com.birdeye.campaign.entity.ReviewRequest;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.request.InboxEmailRequestMessage;
import com.birdeye.campaign.response.kontacto.KontactoDTO;

public interface CampaignEmailService {
	

	/***
	 * 1. Get Email Templates 2. Get HTML Template 3. Get Data Map for HTML template 4. Replace Data Map fetched from 3 in HTML Template get in 2 5. Send
	 * Mail
	 * @param business
	 * @param reviewRequest
	 * @param isReminder
	 * @param campaignType
	 * @param customer
	 * @param inboxEmailRequestMessage
	 * Added sendEmailOnPriority BIRDEYE-122978
	 * @param sendEmailOnPriority
	 * @param communicationCategory TODO
	 * @throws Exception
	 */
	void sendRequestMailToCustomer(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, boolean isReminder, String campaignType, KontactoDTO customer,
			InboxEmailRequestMessage inboxEmailRequestMessage, Campaign campaign, boolean sendEmailOnPriority, String communicationCategory) throws Exception;
	
	void sendPromotionMailToCustomer(BusinessEnterpriseEntity business, Promotion promotionRequest, Integer templateId, KontactoDTO customer, InboxEmailRequestMessage inboxEmailRequestMessage, boolean sendEmailOnPriority, String communicationCategory)
			throws IOException;

	void sendFailureResponseToInbox(String failureReason, InboxEmailRequestMessage inboxEmailRequest);

}
