package com.birdeye.campaign.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.birdeye.campaign.aspect.annotation.Profiled;
import com.birdeye.campaign.business.service.BusinessService;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.constant.KafkaTopicTypeEnum;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.dto.CampaignBulkLocDataDto;
import com.birdeye.campaign.dto.CampaignUserAccessDTO;
import com.birdeye.campaign.dto.CommunicationUsageStatsMessage;
import com.birdeye.campaign.dto.KafkaMessage;
import com.birdeye.campaign.dto.SplitCampaignAllListingResponse;
import com.birdeye.campaign.dto.SplitCampaignDTO;
import com.birdeye.campaign.dto.SplitCampaignMappingDTO;
import com.birdeye.campaign.dto.SplitCampaignUsageRequest;
import com.birdeye.campaign.dto.SplitCampaignVariantData;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.CampaignCondition;
import com.birdeye.campaign.entity.CampaignEntitiesChangeLog;
import com.birdeye.campaign.entity.SplitCampaign;
import com.birdeye.campaign.entity.SplitCampaignMapping;
import com.birdeye.campaign.enums.CampaignRunTypeEnum;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.exception.CampaignHTTPException;
import com.birdeye.campaign.executor.services.CampaignCallable;
import com.birdeye.campaign.executor.services.CampaignExecutorService;
import com.birdeye.campaign.executor.services.ExecutorCommonService;
import com.birdeye.campaign.kafka.service.KafkaService;
import com.birdeye.campaign.platform.constant.CampaignModificationUserActionEnum;
import com.birdeye.campaign.platform.constant.CampaignStatusEnum;
import com.birdeye.campaign.platform.constant.CampaignTypeEnum;
import com.birdeye.campaign.report.ReportService;
import com.birdeye.campaign.repository.CampaignConditionRepo;
import com.birdeye.campaign.request.AutomationCampaignRequest;
import com.birdeye.campaign.request.CampaignFilterRequest;
import com.birdeye.campaign.request.CampaignUpdateEvent;
import com.birdeye.campaign.request.CampaignUsageInfo;
import com.birdeye.campaign.request.CampaignUserAccessRequest;
import com.birdeye.campaign.request.CampaignsFilterCriteria;
import com.birdeye.campaign.request.ProductFeatureRequest;
import com.birdeye.campaign.request.SplitAutomationRequest;
import com.birdeye.campaign.request.SplitAutomationViewDetailsRequest;
import com.birdeye.campaign.response.CreateCampaignResponse;
import com.birdeye.campaign.response.EditInfoResponse;
import com.birdeye.campaign.response.SplitAutomationEditResponse;
import com.birdeye.campaign.response.SplitAutomationViewDetailsResponse;
import com.birdeye.campaign.service.AutomationCampaignSetupService;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.CampaignServiceHelper;
import com.birdeye.campaign.service.CampaignSetupCachingService;
import com.birdeye.campaign.service.CampaignUsageService;
import com.birdeye.campaign.service.FilterCriteriaService;
import com.birdeye.campaign.service.SplitAutomationSetupService;
import com.birdeye.campaign.service.SplitCampaignHelperService;
import com.birdeye.campaign.service.TemplateHelperService;
import com.birdeye.campaign.service.dao.CampaignEntitiesChangeLogDao;
import com.birdeye.campaign.service.dao.SplitCampaignDao;
import com.birdeye.campaign.user.access.settings.service.CampaignUserAccessSettingsService;
import com.birdeye.campaign.utils.CampaignModificationAuditUtils;
import com.birdeye.campaign.utils.CampaignUserAccessUtils;
import com.birdeye.campaign.utils.CampaignUtils;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.DateTimeUtils;
import com.birdeye.campaign.utils.SplitCampaignUtils;

@Service("splitAutomationSetupService")
public class SplitAutomationSetupServiceImpl implements SplitAutomationSetupService {
	
	private static final Logger			logger	= LoggerFactory.getLogger(SplitAutomationSetupServiceImpl.class);
	
	@Autowired
	@Qualifier(Constants.CAMPAIGN_UI_COMPLETABLE_FUTURE_TASK_EXECUTOR)
	private ThreadPoolTaskExecutor			splitAutomationTaskExecutor;
	
	@Autowired
	private SplitCampaignDao				splitCampaignDao;
	
	@Autowired
	private SplitCampaignHelperService		splitCampaignHelperService;
	
	@Autowired
	private ExecutorCommonService			executorCommonService;
	
	@Autowired
	private FilterCriteriaService			filterCriteriaService;
	
	@Autowired
	private CampaignUsageService			campaignUsageService;
	
	@Autowired
	private CampaignConditionRepo			campaignConditionRepo;
	
	@Autowired
	private AutomationCampaignSetupService	automationCampaignSetupService;
	
	@Autowired
	private CacheService					cacheService;
	
	@Autowired
	private CampaignSetupCachingService		campaignCachingService;
	
	@Autowired
	private ReportService					reportService;
	
	@Autowired
	private KafkaService					kafkaService;
	
	@Autowired
	private CampaignUserAccessSettingsService		userAccessSettingsService;
	
	@Autowired
	private CampaignEntitiesChangeLogDao		campaignEntitiesChangeLogDao;
	
	@Autowired
	private BusinessService						businessService;

	@Autowired
	private CampaignServiceHelper						campaignService;
	
	@Autowired
	private TemplateHelperService				templateHelperService;
	/**
	 * Method to prepare edit split automation request's data.
	 * 
	 * @param enterpriseId
	 * @param splitCampaignId
	 */
	@Profiled
	@Override
	public SplitAutomationEditResponse getEditSplitAutomation(Integer enterpriseId, Integer splitCampaignId, Integer userId) {
		SplitAutomationEditResponse response = new SplitAutomationEditResponse();
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(splitAutomationTaskExecutor);
		List<SplitCampaignMappingDTO> mappingData = splitCampaignHelperService.prepareSplitMappingDataBySplitCampaignId(splitCampaignId, enterpriseId);
		if(BooleanUtils.isFalse(SplitCampaignUtils.fetchAndValidateSplitMappingListSize(mappingData))) {
			logger.info("No mapping data exists for the provided splitCampaignId: {}, return empty response!", splitCampaignId);
			return response;
		}
		executorService.submit(editSplitAutomationDetailsTask(mappingData, response));
		executorService.submit(editSplitAutomationConditionTask(enterpriseId, mappingData.get(0).getCampaignId(), response));
		// BIRD-50563 | User Access Settings Handling
		Boolean isAccessSettingApplicable = (userId != null && userAccessSettingsService.isAccessSettingApplicable(enterpriseId));
		if (BooleanUtils.isTrue(isAccessSettingApplicable)) {
			executorService.submit(editUserAccessTask(enterpriseId, splitCampaignId, userId, response));
		}
		try {
			executorCommonService.executeTasks(executorService, 5000);
		} catch (Exception exe) {
			logger.error("Exception encountered while executing get details for eId: {} and splitCampaignId: {}. Details: {}", enterpriseId, splitCampaignId, exe.getMessage());
		}
		
		// BIRD-50563 | User Access Settings Handling - Invalid Access Case
		if (BooleanUtils.isFalse(CampaignUserAccessUtils.isUserHasEditPermission(response.getUserPermissions(), isAccessSettingApplicable))) {
			logger.error("getEditSplitAutomation :: Invalid Request as user {} does not have appropriate permissions for split campaign id {}", userId, splitCampaignId);
			return SplitCampaignUtils.getEditSplitCampaignEmptyResponse(
					(CollectionUtils.isEmpty(response.getUserPermissions()) ? new ArrayList<>(Arrays.asList(CampaignUserAccessUtils.fetchDefaultAccessSettingForAUser()))
							: response.getUserPermissions()));
		}
		return response;
	}
	
	private CampaignCallable<Boolean> editSplitAutomationConditionTask(final Integer enterpriseId, final Integer campaignId, final SplitAutomationEditResponse response) {
		return new CampaignCallable<Boolean>("Edit Split Automation Condition Task") {
			@Override
			public Boolean doCall() {
				CampaignCondition campaignCondition = campaignConditionRepo.getByCampaignIdAndEnterpriseId(campaignId, enterpriseId);
				if (campaignCondition != null) {
					prepareSplitAutomationConditionData(campaignCondition, response);
				}
				return true;
			}
		};
	}
	
	/**
	 * 
	 * Fetch And Set User Permissions/ Access Settings and Owner Permissible Locations For Given Split Campaign Id and User Id In Response
	 * 
	 * @param accountId,
	 *            campaignId, userId, response
	 * 			
	 */
	private CampaignCallable<Boolean> editUserAccessTask(final Integer accountId, final Integer splitCampaignId, final Integer userId, final SplitAutomationEditResponse response) {
		return new CampaignCallable<Boolean>("Edit User Access Task") {
			@Override
			public Boolean doCall() {
				CampaignUserAccessDTO userCampaignAccessSettings = userAccessSettingsService
						.fetchAccessSettingsWithOwnerLocationAccess(new CampaignUserAccessRequest(accountId, userId, null, splitCampaignId));
				if (userCampaignAccessSettings != null) {
					response.setUserPermissions(new ArrayList<>(Collections.singletonList(userCampaignAccessSettings.getAccess())));
					response.setOwnerPermissibleLocations(userCampaignAccessSettings.getOwnerPermissibleLocations());
				}
				return true;
			}
		};
		
	}
	
	private void prepareSplitAutomationConditionData(CampaignCondition campCondition, SplitAutomationEditResponse response) {
		response.setLvlAlias(campCondition.getLvlAlias());
		response.setLvlAliasId(campCondition.getLvlAliasId());
		response.setLvlIds(campCondition.getLvlIds());
		if (CollectionUtils.isEmpty(campCondition.getLvlIds()) && "LOC".equalsIgnoreCase(campCondition.getLvlAlias())) {
			response.setSelectAll(true);
		} else {
			response.setSelectAll(false);
			if ("LOC".equalsIgnoreCase(campCondition.getLvlAlias()) && CollectionUtils.isNotEmpty(campCondition.getLvlIds())) {
				List<String> validBusinessIds = splitCampaignHelperService.getValidBusinessIds(campCondition.getLvlIds());
				response.setLvlIds(validBusinessIds);
				response.setIsActiveSelectedLocationCountZero(CollectionUtils.isEmpty(validBusinessIds));
			}
		}
		response.setExpression(campCondition.getRuleExpression());
		response.setSources(campCondition.getContactSources());
		response.setTriggerExpression(campCondition.getTriggerRuleExpression());
		response.setContactEventType(campCondition.getRecurringReminderEventType());
		response.setScheduleInfo(SplitCampaignUtils.getScheduleInfoForACampaign(campCondition));
	}
	
	private CampaignCallable<Boolean> editSplitAutomationDetailsTask(final List<SplitCampaignMappingDTO> mappingDataList, final SplitAutomationEditResponse response) {
		return new CampaignCallable<Boolean>("Edit SplitAutomation details Task") {
			@Override
			public Boolean doCall() {
				Campaign campaign = campaignCachingService.getCampaignById(mappingDataList.get(0).getCampaignId());
				if (CollectionUtils.isNotEmpty(mappingDataList) && campaign != null) {
					prepareSplitAutomationEditDetails(mappingDataList, campaign, response);
				}
				return true;
			}
		};
	}
	
	private void prepareSplitAutomationEditDetails(List<SplitCampaignMappingDTO> mappingDataList, Campaign campaign, SplitAutomationEditResponse response) {
		response.setId(mappingDataList.get(0).getSplitCampaignId());
		response.setCampaignName(campaign.getName()); // Sub Campaign Name is same as Split Campaign Name.
		response.setPriority(campaign.getPriority()); 
		response.setRunType(CampaignRunTypeEnum.getEnum(campaign.getRunType()).getLabel()); 
		response.setSendReminder(campaign.getSendReminder() != null && campaign.getSendReminder() == 1);
		response.setReminderCount(campaign.getReminderCount());
		response.setReminderInterval(campaign.getReminderFrequency());
		response.setReminderSubject(campaign.getReminderSubject());
		response.setIsDraft(campaign.getStatus() == CampaignStatusEnum.DRAFT.getStatus() ? 1 : 0);
		response.setTriggerType(campaign.getTriggerType());
		response.setOverrideCommRestriction(campaign.getBypassCommRestriction());
		response.setVariants(splitCampaignHelperService.prepareEditSplitAutomationVariantData(mappingDataList));
	}
	
	/**
	 * Method to prepare split automation view details request's response data
	 * 
	 * @param SplitAutomationViewDetailsRequest
	 *            - enterpriseId, splitCampaignId, campaignId
	 */
	@Profiled
	@Override
	public SplitAutomationViewDetailsResponse getSplitAutomation(SplitAutomationViewDetailsRequest request, Integer userId) {
		CampaignUtils.validateAccountId(request.getEnterpriseId());
		SplitAutomationViewDetailsResponse response = new SplitAutomationViewDetailsResponse();
		if(BooleanUtils.isFalse(splitCampaignHelperService.validateSplitCampaignRequest(request.getSplitCampaignId(), request.getCampaignId(), request.getEnterpriseId()))) {
			logger.info("getSplitAutomation :: Validation failed for split campaign request, returning empty response!");
			return response;
		}
		
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(splitAutomationTaskExecutor);
		List<SplitCampaignMappingDTO> mappingDTO = splitCampaignHelperService.prepareSplitMappingDataBySplitCampaignId(request.getSplitCampaignId(), request.getEnterpriseId());
		Campaign campaign = campaignCachingService.getCampaignById(request.getCampaignId() != null && request.getCampaignId()!=0 ? request.getCampaignId() : mappingDTO.get(0).getCampaignId());
		CampaignUtils.validateCampaignId(request.getEnterpriseId(), campaign.getEnterpriseId());
		executorService.submit(splitAutomationDetailsTask(mappingDTO, campaign, response));
		executorService.submit(campaignDetailsAndConditionTask(request.getEnterpriseId(), campaign.getId(), response));
		executorService.submit(campaignAuditLogInfoTask(campaign, response));
		// BIRD-50563 | User Access Settings Handling
		Boolean isAccessSettingApplicable = (userId != null && userAccessSettingsService.isAccessSettingApplicable(request.getEnterpriseId()));
		if (BooleanUtils.isTrue(isAccessSettingApplicable)) {
			executorService.submit(userAccessTask(request.getEnterpriseId(), request.getSplitCampaignId(), userId, response));
		}
		// Preparing usage reports data only if the split campaign is not in draft
		if (campaign.getStatus() != CampaignStatusEnum.DRAFT.getStatus()) {
			executorService.submit(splitAutomationReportTask(campaign.getId(), response, request.getEnterpriseId()));
		}
		try {
			executorCommonService.executeTasks(executorService, 5000);
		} catch (Exception exe) {
			logger.error("Exception encountered while executing view details details for eId: {} and splitCampaignId: {}. Details: {}", request.getEnterpriseId(),
					request.getSplitCampaignId(), exe.getMessage());
		}

		// BIRD-50563 | User Access Settings Handling - No Valid Access
		if (BooleanUtils.isFalse(CampaignUserAccessUtils.isUserHasViewPermission(response.getUserPermissions(), isAccessSettingApplicable))) {
			logger.error("getSplitAutomation :: Invalid Request as user {} does not have appropriate permissions for split campaign id {}", userId, request.getSplitCampaignId());
			return SplitCampaignUtils.getSplitCampaignViewDetailsEmptyResponse(CollectionUtils.isEmpty(response.getUserPermissions()) ? new ArrayList<>(Arrays.asList(CampaignUserAccessUtils.fetchDefaultAccessSettingForAUser()))
					: response.getUserPermissions());
		}
		if (CoreUtils.isTrueForInteger(campaign.getTemplateId())) {
			response.setEmailTemplateCategory(templateHelperService.prepareEmailTemplateCategory(campaign.getTemplateId()));
		}
		if (CoreUtils.isTrueForInteger(campaign.getSmsTemplateId())) {
			response.setSmsTemplateCategory(templateHelperService.prepareSmsTemplateCategory(campaign.getSmsTemplateId()));
		}
		return response;
	}
	
	private Callable<Boolean> campaignAuditLogInfoTask(Campaign campaign, final SplitAutomationViewDetailsResponse response) {
		return new CampaignCallable<Boolean>("Campaign Audit Info Task") {
			@Override
			public Boolean doCall() {
				// Fetch latest audit log information corresponding to entity
				CampaignEntitiesChangeLog changeLog = campaignEntitiesChangeLogDao.findLatestChangeLogByIdAndType(campaign.getEnterpriseId(), campaign.getId(),
						CampaignModificationAuditUtils.CAMPAIGN_ENTITY, Constants.SPLIT);
				String businessTimeZoneId = businessService.getBusinessTimezoneId(campaign.getEnterpriseId());
				if (Objects.isNull(changeLog)) {
					// Prepare creation audit log using entity
					changeLog = new CampaignEntitiesChangeLog(campaign.getEnterpriseId(), campaign.getId(), campaign.getCreatedBy(),
							CampaignModificationUserActionEnum.CREATE.getUserActionType(), campaign.getCreatedAt());
				}
				prepareAuditLogInfo(changeLog, response, businessTimeZoneId);
				return true;
			}
			
			private void prepareAuditLogInfo(CampaignEntitiesChangeLog changeLog, SplitAutomationViewDetailsResponse response, String businessTimeZoneId) {
				EditInfoResponse auditInfoResponse = new EditInfoResponse(cacheService.getUserResponsibleForModification(changeLog.getUserId(), campaign.getEnterpriseId()),
						changeLog.getUserId(), changeLog.getEvent().toLowerCase(),
						DateTimeUtils.prepareFormattedDate(changeLog.getEventTime(), CampaignModificationAuditUtils.MODIFICATION_DATE_FORMAT, businessTimeZoneId),
						DateTimeUtils.prepareFormattedDate(changeLog.getEventTime(), CampaignModificationAuditUtils.MODIFICATION_TIME_FORMAT, businessTimeZoneId));
				response.setAuditLogInfo(auditInfoResponse);
			}
		};
	}
	
	private CampaignCallable<Boolean> splitAutomationReportTask(final Integer campaignId, final SplitAutomationViewDetailsResponse response, final Integer enterpriseId) {
		return new CampaignCallable<Boolean>("getCampaignUsageReportTask") {
			@Override
			public Boolean doCall() {
				prepareSplitAutomationUsageReport(campaignId, response, enterpriseId);
				return true;
			}
		};
	}
	
	private void prepareSplitAutomationUsageReport(Integer campaignId, SplitAutomationViewDetailsResponse response, Integer enterpriseId) {
		Campaign campaign = campaignCachingService.getCampaignById(campaignId);
		CampaignsFilterCriteria filterCriteria = filterCriteriaService.prepareCampaignsFilterCriteria(enterpriseId, campaign);
		CampaignUsageInfo usageInfo = new CampaignUsageInfo();
		campaignUsageService.prepareCampaignUsageReport(filterCriteria, usageInfo, campaign.getType());
		response.setUsageInfo(usageInfo);
	}
	
	private CampaignCallable<Boolean> campaignDetailsAndConditionTask(final Integer enterpriseId, final Integer campaignId, final SplitAutomationViewDetailsResponse response) {
		return new CampaignCallable<Boolean>("Split Automation Condition Task") {
			@Override
			public Boolean doCall() {
				CampaignCondition campaignCondition = campaignConditionRepo.getByCampaignIdAndEnterpriseId(campaignId, enterpriseId);
				if (campaignCondition != null) {
					prepareConditionDataForViewDetails(campaignCondition, response);
				}
				return true;
			}
		};
	}
	
	/**
	 * 
	 * Fetch And Set User Permissions/ Access Settings For Given Split Campaign Id and User Id In Response
	 * 
	 * @param accountId,
	 *            campaignId, userId, response
	 * 			
	 */
	private CampaignCallable<Boolean> userAccessTask(final Integer accountId, final Integer splitCampaignId, final Integer userId, final SplitAutomationViewDetailsResponse response) {
		return new CampaignCallable<Boolean>("User Access Task") {
			@Override
			public Boolean doCall() {
				CampaignUserAccessDTO userCampaignAccessSettings = userAccessSettingsService.fetchAccessSettings(new CampaignUserAccessRequest(accountId, userId, null, splitCampaignId));
				if (userCampaignAccessSettings != null) {
					response.setUserPermissions(new ArrayList<>(Collections.singletonList(userCampaignAccessSettings.getAccess())));
				}
				return true;
			}
		};
	}
	
	private void prepareConditionDataForViewDetails(CampaignCondition campCondition, SplitAutomationViewDetailsResponse response) {
		response.setLvlAlias(campCondition.getLvlAlias());
		response.setLvlAliasId(campCondition.getLvlAliasId());
		response.setLvlIds(campCondition.getLvlIds());
		if (CollectionUtils.isEmpty(campCondition.getLvlIds()) && "LOC".equalsIgnoreCase(campCondition.getLvlAlias())) {
			response.setSelectAll(true);
		} else {
			response.setSelectAll(false);
			if ("LOC".equalsIgnoreCase(campCondition.getLvlAlias()) && CollectionUtils.isNotEmpty(campCondition.getLvlIds())) {
				response.setLvlIds(splitCampaignHelperService.getValidBusinessIds(campCondition.getLvlIds()));
			}
		}
		response.setExpression(campCondition.getRuleExpression());
		response.setSources(campCondition.getContactSources());
		response.setTriggerExpression(campCondition.getTriggerRuleExpression());
		response.setContactEventType(campCondition.getRecurringReminderEventType());
		response.setScheduleInfo(SplitCampaignUtils.getScheduleInfoForACampaign(campCondition));
	}

	private CampaignCallable<Boolean> splitAutomationDetailsTask(final List<SplitCampaignMappingDTO> mappingDataList, final Campaign campaign, final SplitAutomationViewDetailsResponse response) {
		return new CampaignCallable<Boolean>("SplitAutomation details Task") {
			@Override
			public Boolean doCall() {
				if (CollectionUtils.isNotEmpty(mappingDataList) && campaign != null) {
					prepareSplitAutomationDetails(mappingDataList, campaign, response);
				}
				return true;
			}
		};
	}
	
	private void prepareSplitAutomationDetails(List<SplitCampaignMappingDTO> mappingDataList, Campaign campaign, SplitAutomationViewDetailsResponse response) {
		response.setId(mappingDataList.get(0).getSplitCampaignId());
		response.setCampaignName(campaign.getName());
		response.setPriority(campaign.getPriority());
		response.setRunType(CampaignRunTypeEnum.getEnum(campaign.getRunType()).getLabel());
		response.setSendReminder(campaign.getSendReminder() != null && campaign.getSendReminder() == 1);
		response.setReminderCount(campaign.getReminderCount());
		response.setReminderInterval(campaign.getReminderFrequency());
		response.setStatusId(campaign.getStatus());
		response.setCreatedOn(splitCampaignHelperService.prepareDateAsPerTimeZone(campaign.getCreatedAt(), "MMM dd, yyyy", campaign.getEnterpriseId()));
		response.setCreatedBy(splitCampaignHelperService.prepareUserName(campaign.getCreatedBy()));
		response.setTriggerType(campaign.getTriggerType());
		response.setOverrideCommRestriction(campaign.getBypassCommRestriction());
		response.setVariants(splitCampaignHelperService.prepareEditSplitAutomationVariantData(mappingDataList));
	}

	/**
	 * Method to update the delete flag of a split campaign.
	 * 
	 * @param splitCampaignId
	 * @param deleteFlag
	 * @param enterpriseId
	 * @return true/false
	 */
	@Override
	@Profiled
	@Transactional(value = "chainedTransactionManager", rollbackFor = Exception.class)
	public Boolean deleteSplitCampaign(Integer splitCampaignId, Integer deleteFlag, Integer enterpriseId, String userId) {
		if(splitCampaignId == null || splitCampaignId == 0) {
			logger.error("deleteSplitCampaign :: no campaignId :: {} found", splitCampaignId);
			throw new CampaignException(ErrorCodes.INVALID_REQUEST);
		}
		SplitCampaign splitCampaign = cacheService.getSplitCampaignById(splitCampaignId);
		List<SplitCampaignMappingDTO> mappingDataList = splitCampaignHelperService.prepareSplitMappingDataBySplitCampaignId(splitCampaignId, enterpriseId);
		if(splitCampaign == null || BooleanUtils.isFalse(SplitCampaignUtils.fetchAndValidateSplitMappingListSize(mappingDataList))) {
			logger.error("deleteSplitCampaign :: no split campaign found for id {}.", splitCampaignId);
			throw new CampaignException(ErrorCodes.INVALID_REQUEST);
		}
		
		if(BooleanUtils.isTrue(SplitCampaignUtils.isSplitCampaignStatusValidForDeletion(splitCampaign))) {
			List<Integer> campaignIds = SplitCampaignUtils.fetchCampaignIdsFromMappingDataList(mappingDataList);
			Boolean isSubCampaignsDeleted = splitCampaignHelperService.deleteSubCampaignForSplitCampaign(campaignIds, deleteFlag, splitCampaign);
			Boolean isSplitCampaignDeleted = splitCampaignHelperService.markSplitCampaignForDeletion(splitCampaignId, deleteFlag);
			
			// BIRD-50563 | User Access Setting Deletion Handling
			userAccessSettingsService.deleteCampaignAccessSetting(new CampaignUserAccessRequest(null, splitCampaignId));
			
			//Handling cache eviction
			splitCampaignHelperService.evictCampaignsAndSplitCampaignCaches(campaignIds, splitCampaignId, enterpriseId);
			
			//Audit Campaign Modification
			splitCampaignHelperService.publishSplitCampaignModificationEvent(campaignIds, null, null, enterpriseId, deleteFlag);
			
			return isSplitCampaignDeleted && isSubCampaignsDeleted;
		} else {
			logger.error("deleteSplitCampaign: Invalid status found for split campaign {}", splitCampaignId);
			throw new CampaignException(ErrorCodes.CAMPAIGN_DELETE_ERROR, ErrorCodes.CAMPAIGN_DELETE_ERROR.getMessage());
		}
	}	
	
	///// Split Automation Listing Flow
	
	/**
	 *
	 * Validate and fetch paginated split campaign list based upon filters.
	 *
	 * 
	 * @param accountId,
	 *            CampaignFilterRequest
	 */
	@Override
	public SplitCampaignAllListingResponse getAllSplitAutomationForEnterprise(Integer accountId, CampaignFilterRequest campaignFilterRequest, Integer userId) {
		logger.info("Request received to get split automation list for accountId : {} and request : {} and user id : {}", accountId, campaignFilterRequest, userId);
		if (accountId == null) {
			throw new CampaignHTTPException(HttpStatus.BAD_REQUEST, "Account Id can not be null.");
		}
		
		// 1.1 Initialize response object and fetch total non deleted split campaigns data present for given enterprise and run type.
		SplitCampaignAllListingResponse response = new SplitCampaignAllListingResponse();
		Boolean isLocationFilterApplicable = automationCampaignSetupService.isLocationFilteringApplicable(accountId, campaignFilterRequest);
		CampaignBulkLocDataDto splitCampaignLocData = validateAndFetchCampaignLocDataWithTotalCount(accountId, campaignFilterRequest, isLocationFilterApplicable);
		response.setTotalCount(splitCampaignLocData != null ? splitCampaignLocData.getTotalCount() : 0);
		if (response.getTotalCount() == 0 || CollectionUtils.isEmpty(campaignFilterRequest.getCampaignType()) || CollectionUtils.isEmpty(campaignFilterRequest.getCampaignStatus())) {
			logger.info("No split campaign found for account id {} and request {}", accountId, campaignFilterRequest);
			return response;
		}
		
		// 1.2 Get List of applicable Campaign Status Ids from Campaign Status List
		List<Integer> campaignStatus = CampaignUtils.getCampaignStatus(campaignFilterRequest.getCampaignStatus());
		
		// 1.3 Validate And Fetch Split Campaign Data
		Map<Integer, SplitCampaignDTO> splitCampaignIdToDataMap = validateAndPopulateSplitCampaignData(accountId, campaignFilterRequest, campaignStatus, isLocationFilterApplicable,
				splitCampaignLocData, response);
		if (splitCampaignIdToDataMap.isEmpty()) {
			// Send empty response back to UI
			return response;
		}
		
		// 1.4 Prepare request and fetch usage stats for campaign ids.
		List<SplitCampaignUsageRequest> splitUsageRequestList = SplitCampaignUtils.prepareSplitCampaignUsageRequestList(splitCampaignIdToDataMap);
		Map<Integer, CommunicationUsageStatsMessage> campaignIdToUsageMap = reportService.getSplitCampaignsCommunicationUsageData(accountId, splitUsageRequestList);
		
		// 1.5 Prepare Response to be sent to UI
		SplitCampaignUtils.prepareSplitCampaignListingResponse(splitCampaignIdToDataMap, campaignIdToUsageMap, response);
		
		// 1.6 BIRD-50563 | User Access Settings Handling in Listing API
		addAccessSettingsInSplitCampaignListingResponse(accountId, userId, response);
		
		return response;
		
	}

	/**
	 * BIRD-50559 | If Location Filtering Applicable, Get Split Campaign List with Location Data, Else Size of Applicable Split Campaigns
	 *
	 * @param accountId, campaignFilterRequest, isLocationFilterApplicable
	 * 
	 */
	private CampaignBulkLocDataDto validateAndFetchCampaignLocDataWithTotalCount(Integer accountId, CampaignFilterRequest campaignFilterRequest, Boolean isLocationFilterApplicable) {
		return (BooleanUtils.isTrue(isLocationFilterApplicable)) ? splitCampaignHelperService.fetchCampaignLocationData(accountId)
				: new CampaignBulkLocDataDto(new ArrayList<>(), splitCampaignDao.getSplitCampaignCountByAccountIdAndRunType(accountId, campaignFilterRequest.getRunType()));
	}
	
	/**
	 * 
	 * This method fetches the access status for the current user for list of split campaigns
	 * 1. If the user is enterprise user , By default he/she would have edit access to all campaigns.
	 * 2. Else if a location user, depends upon what access he has been given. Default access is no-access.
	 * 
	 * @param accountId,
	 *            userId, response
	 * 
	 */
	private void addAccessSettingsInSplitCampaignListingResponse(Integer accountId, Integer userId, SplitCampaignAllListingResponse response) {
		if (CollectionUtils.isEmpty(response.getData())) {
			logger.info("Access Settings not applicable as split campaign list is empty for accountId {} and userId {}", accountId, userId);
			return;
		}
		
		if (userId == null || BooleanUtils.isFalse(userAccessSettingsService.isAccessSettingApplicable(accountId))) {
			return;
		}
		
		logger.info("addAccessSettingsInSplitCampaignListingResponse :: Fetching Access Settings for accountId {} and userId {}", accountId, userId);
		CachedCollectionWrapper<CampaignUserAccessDTO> userCampaignAccessWrapper = userAccessSettingsService
				.fetchUserCampaignsAccess(new CampaignUserAccessRequest(accountId, userId, Constants.USER_ACCESS_SPLIT_CAMPAIGN));
		if (BooleanUtils.isTrue(CampaignUserAccessUtils.isAccessSettingsListForEnterpriseUser(userCampaignAccessWrapper))) {
			response.getData().stream().forEach(e -> e.setUserPermissions(new ArrayList<>(Collections.singletonList(userCampaignAccessWrapper.getElementsList().get(0).getAccess()))));
			return;
		}
		Map<Integer, CampaignUserAccessDTO> campaignIdToCampaignAccess = CampaignUserAccessUtils.prepareIdToCampaignAccessMap(userCampaignAccessWrapper, Constants.USER_ACCESS_SPLIT_CAMPAIGN);
		response.getData().stream().forEach(e -> e.setUserPermissions(CampaignUserAccessUtils.fetchAccessSettingsForCampaignId(campaignIdToCampaignAccess, e.getId())));
	}
	
	/**
	 * 
	 * This method filters the campaign types based on business options purchased by the business.
	 * 
	 * @param enterpriseId,
	 *            campaignFilterRequest
	 * 
	 */
	private void filterCampaignTypes(Integer enterpriseId, CampaignFilterRequest campaignFilterRequest) {
		logger.info("Filtering campaign types based on business options for request: {}", campaignFilterRequest);
		ProductFeatureRequest featureRequest = cacheService.getProductFeatureForBusiness(enterpriseId);
		List<String> filteredCampaignTypes = new ArrayList<>();
		campaignFilterRequest.getCampaignType().stream().forEach(campaignType -> {
			if ((StringUtils.equalsIgnoreCase(campaignType, CampaignTypeEnum.SURVEY_REQUEST.getType()) && !Objects.equals(featureRequest.getIsSurveyEnabled(), 1))
					|| (StringUtils.equalsIgnoreCase(campaignType, CampaignTypeEnum.REFERRAL.getType()) && !Objects.equals(featureRequest.getEnableReferral(), 1))
					|| (StringUtils.equalsIgnoreCase(campaignType, CampaignTypeEnum.REVIEW_REQUEST.getType())) && !Objects.equals(featureRequest.getReviewGenEnabled(), 1)
					|| (StringUtils.equalsIgnoreCase(campaignType, CampaignTypeEnum.CX_REQUEST.getType())) && !Objects.equals(featureRequest.getReviewGenEnabled(), 1)) {
				return;
			}
			filteredCampaignTypes.add(campaignType);
		});
		campaignFilterRequest.setCampaignType(filteredCampaignTypes);
	}
	
	/**
	 *
	 * Validate campaign types and return split campaign data
	 *
	 * 
	 * @param accountId,
	 *            campaignFilterRequest, campaignStatus, isLocationFilterApplicable, splitCampaignListingData, response
	 */
	private Map<Integer, SplitCampaignDTO> validateAndPopulateSplitCampaignData(Integer accountId, CampaignFilterRequest campaignFilterRequest, List<Integer> campaignStatus,
			Boolean isLocationFilterApplicable, CampaignBulkLocDataDto splitCampaignListingData, SplitCampaignAllListingResponse response) {
		// 1.1 Filter campaign types for which business options flag is disabled
		filterCampaignTypes(accountId, campaignFilterRequest);
		if (CollectionUtils.isEmpty(campaignFilterRequest.getCampaignType())) {
			logger.info("No split campaign found for account id {} and request {} since campaign types empty after filtering", accountId, campaignFilterRequest);
			return new HashMap<>();
		}
		
		// 1.2 Fetch paginated split campaign id list by given filters
		Page<SplitCampaign> pageOfSplitCampaigns = fetchFilteredSplitCampaigns(accountId, campaignFilterRequest, campaignStatus, splitCampaignListingData, isLocationFilterApplicable);
		if (pageOfSplitCampaigns == null || CollectionUtils.isEmpty(pageOfSplitCampaigns.getContent())) {
			logger.info("No split campaign list found from db for account id {} and request {}", accountId, campaignFilterRequest);
			return new HashMap<>();
		}
		
		// 1.3 Fetch split campaign ids from split campaign list
		List<Integer> splitCampaignIds = pageOfSplitCampaigns.getContent().stream().map(e -> e.getId()).collect(Collectors.toList());
		logger.info("Split campaign ids size fetched for account id {} is {}", accountId, CollectionUtils.size(splitCampaignIds));
		
		// 1.4 Fetch split campaign data for earlier fetched split campaign ids
		List<SplitCampaignMappingDTO> splitCampaignMappingData = splitCampaignDao.getSplitCampaignMappingDataBySplitCampaignIds(splitCampaignIds);
		if (CollectionUtils.isEmpty(splitCampaignMappingData)) {
			logger.error("No split campaign list found from db for account id {} and split campaign id size {}", accountId, CollectionUtils.size(splitCampaignIds));
			return new HashMap<>();
		}
		logger.info("Split campaign Mapping data size prepared for account id {} is {}", accountId, CollectionUtils.size(splitCampaignMappingData));
		
		// 1.5 Prepare split campaign id to split campaign data map
		Map<Integer, SplitCampaignDTO> splitCampaignIdToDataMap = SplitCampaignUtils.prepareSplitCampaignMapFromList(pageOfSplitCampaigns.getContent());
		
		// 1.6 Update split campaign data map with mapping data
		SplitCampaignUtils.updateSplitCampaignMap(splitCampaignMappingData, splitCampaignIdToDataMap);
		logger.info("Split campaign data size prepared for account id {} is {}", accountId, CollectionUtils.size(splitCampaignIdToDataMap));
		
		// 1.7 Set Filtered Count for Pagination in response
		response.setFilteredCount(Integer.valueOf(String.valueOf(pageOfSplitCampaigns.getTotalElements())));
		
		return splitCampaignIdToDataMap;
	}
	
	/**
	 * 
	 * BIRD-50559 | Fetch Filtered Split Campaigns Based by including/excluding Location Hierarchy Filters
	 *
	 * @param accountId,
	 *            campaignFilterRequest, campaignStatus, campaignData, isLocationFilterApplicable
	 * 			
	 */
	private Page<SplitCampaign> fetchFilteredSplitCampaigns(Integer accountId, CampaignFilterRequest campaignFilterRequest, List<Integer> campaignStatus, CampaignBulkLocDataDto campaignData,
			Boolean isLocationFilterApplicable) {
		Pageable pageable = PageRequest.of(campaignFilterRequest.getPage(), campaignFilterRequest.getSize(), Sort.by("priorityOrder").ascending().and(Sort.by("updatedAt").descending()));
		if (BooleanUtils.isTrue(isLocationFilterApplicable)) {
			List<Integer> splitCampaignIds = CampaignUtils.filterCampaignBasedUponLocHierarchy(campaignFilterRequest, campaignData, true);
			return (CollectionUtils.isEmpty(splitCampaignIds)) ? new PageImpl<>(Collections.emptyList(), pageable, 0)
					: fetchFilteredPaginatedSplitCampaignUsingSplitCampaignIds(accountId, campaignFilterRequest, campaignStatus, pageable, splitCampaignIds);
		}
		return fetchFilteredPaginatedSplitCampaignIds(accountId, campaignFilterRequest, campaignStatus, pageable);
	}	
				
	/**
	 *
	 * BIRD-50559 | Fetch Paginated Split Campaign Ids By Existing Filters + Location Filtered Split Campaign Ids
	 *
	 * 
	 * @param accountId,
	 *            campaignFilterRequest, campaignStatus, campaignLocationData
	 */
	private Page<SplitCampaign> fetchFilteredPaginatedSplitCampaignUsingSplitCampaignIds(Integer accountId, CampaignFilterRequest campaignFilterRequest, List<Integer> campaignStatus,
			Pageable pageable, List<Integer> splitCampaignIds) {
		Page<SplitCampaign> pageOfSplitCampaigns = null;
		if (CampaignRunTypeEnum.ONGOING.getRunType().equalsIgnoreCase(campaignFilterRequest.getRunType())) {
			// 1.1 Validate and set Trigger Types in Request
			SplitCampaignUtils.setTriggerTypes(campaignFilterRequest);
			
			// 1.2 Fetch Split Campaign Based upon whether search string is given or not.
			if (StringUtils.isEmpty(campaignFilterRequest.getSearchStr())) {
				pageOfSplitCampaigns = splitCampaignDao.fetchPaginatedSplitCampaignIdWithFilterCriteriaAndIds(accountId, campaignFilterRequest.getCampaignType(), campaignStatus,
						campaignFilterRequest.getRunType(), campaignFilterRequest.getTriggerType(), splitCampaignIds, pageable);
			} else {
				pageOfSplitCampaigns = splitCampaignDao.fetchPaginatedSplitCampaignIdWithFilterCriteriaAndNameAndIds(accountId, campaignFilterRequest.getCampaignType(), campaignStatus,
						campaignFilterRequest.getRunType(), campaignFilterRequest.getTriggerType(), campaignFilterRequest.getSearchStr(), splitCampaignIds, pageable);
			}
		}
		return pageOfSplitCampaigns;
	}
	
	/**
	 *
	 * Fetch Paginated Split Campaign Ids By Filters
	 *
	 * 
	 * @param CampaignFilterRequest
	 */
	private Page<SplitCampaign> fetchFilteredPaginatedSplitCampaignIds(Integer accountId, CampaignFilterRequest campaignFilterRequest, List<Integer> campaignStatus, Pageable pageable) {
		Page<SplitCampaign> pageOfSplitCampaigns = null;
		if (CampaignRunTypeEnum.ONGOING.getRunType().equalsIgnoreCase(campaignFilterRequest.getRunType())) {
			// 1.1 Validate and set Trigger Types in Request
			SplitCampaignUtils.setTriggerTypes(campaignFilterRequest);
			
			// 1.2 Fetch Split Campaign Ids Based upon whether search string is given or not.
			if (StringUtils.isEmpty(campaignFilterRequest.getSearchStr())) {
				pageOfSplitCampaigns = splitCampaignDao.fetchPaginatedSplitCampaignIdWithFilterCriteria(accountId, campaignFilterRequest.getCampaignType(), campaignStatus,
						campaignFilterRequest.getRunType(), campaignFilterRequest.getTriggerType(), pageable);
			} else {
				pageOfSplitCampaigns = splitCampaignDao.fetchPaginatedSplitCampaignIdWithFilterCriteriaAndName(accountId, campaignFilterRequest.getCampaignType(), campaignStatus,
						campaignFilterRequest.getRunType(), campaignFilterRequest.getTriggerType(), campaignFilterRequest.getSearchStr(), pageable);
			}
		}
		return pageOfSplitCampaigns;
	}
	
	///// Split Automation Create/Update Flow
	
	/**
	 *
	 * Create or Update Split Automation
	 *
	 * 
	 * @param SplitAutomationRequest,
	 *            accountId, splitCampaignId, userId
	 */
	@Override
	@Transactional(value = "chainedTransactionManager", rollbackFor = Exception.class)
	public CreateCampaignResponse saveOrUpdateSplitAutomation(SplitAutomationRequest request, Integer accountId, Integer splitCampaignId, String userId) throws Exception {
		
		// 1.1 Basic Validation of Request
		SplitCampaignUtils.validateSaveOrUpdateSplitAutomationRequest(request, splitCampaignId);
		
		// 1.2 Prepare split campaign object with changes and update split automation name in request
		SplitCampaign splitCampaign = prepareSplitCampaignObjectWithChanges(request, accountId, splitCampaignId, userId);
		
		// 1.3 Create or Update Sub Automation Entries
		List<CreateCampaignResponse> subCampaignResponseList = createOrUpdateSubCampaignDetails(request, accountId, splitCampaignId, userId);
		logger.info("Save sub campaign response received is {}", subCampaignResponseList);
		
		// 1.4 Create or Update Split Automation Audit and Mapping audit
		splitCampaign = createOrUpdateSplitCampaignAndMappingAudit(request, accountId, splitCampaignId, userId, splitCampaign);
		
		// 1.5 Send event for birthday reminders handling
		sendAutomationCreateOrUpdateEventToKafka(request, accountId, subCampaignResponseList);

		// 1.6 Evict Split Automation Related Caches
		splitCampaignHelperService.evictSplitAutomationCaches(splitCampaign.getId(), accountId);
		
		// 1.7 BIRD-50563 | User Access Settings Handling
		userAccessSettingsService.validateAndUpdateOwnerForCampaignAccessSetting(
				new CampaignUserAccessRequest(CampaignUtils.isNewCampaignCreationRequest(splitCampaignId), accountId, CampaignUtils.getUserId(userId), null, splitCampaign.getId()));
		
		// 1.8 Create and return response object`
		return new CreateCampaignResponse(splitCampaign.getId(), splitCampaign.getName(), splitCampaign.getStatus());
	}
	
	/**
	 *
	 * Send Split Automation Create Or Update Event For Birthday Reminders
	 *
	 * 
	 * @param SplitAutomationRequest,
	 *            accountId,subCampaignResponseList
	 */
	private void sendAutomationCreateOrUpdateEventToKafka(SplitAutomationRequest request, Integer accountId, List<CreateCampaignResponse> subCampaignResponseList) {
		if (BooleanUtils.isFalse(CoreUtils.isTrueForInteger(request.getIsDraft()))) {
			logger.info("sendAutomationCreateOrUpdateEventToKafka :: Going to send event for account id {} and sub campaign id {}", accountId, subCampaignResponseList.get(0).getId());
			CampaignUpdateEvent campaignUpdateEvent = new CampaignUpdateEvent(accountId, subCampaignResponseList.get(0).getId(), new Date(),
					CampaignUtils.getTriggerTypeByEvent(request.getTriggerType()));
			kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_UPDATE, new KafkaMessage(campaignUpdateEvent));
		}
	}
	
	/**
	 *
	 * This function creates/updates the underlying Automations of the Split Automation
	 * 1. Create AutomationCampaignRequest object from SplitAutomationRequest
	 * 2. Call Function To Create/Update Underlying Automation.
	 * 3. Update variant object with campaign id and add it in list.
	 * 4. Repeat the above process for all the variants in the input request
	 *
	 * 
	 * @param SplitAutomationRequest,
	 *            accountId,splitCampaignId,userId
	 */
	private List<CreateCampaignResponse> createOrUpdateSubCampaignDetails(SplitAutomationRequest request, Integer accountId, Integer splitCampaignId, String userId) {
		List<CreateCampaignResponse> subCampaignResponseList = new ArrayList<>();
		for (SplitCampaignVariantData variant : request.getVariants()) {
			AutomationCampaignRequest automationRequest = SplitCampaignUtils.createAutomationRequest(request, variant);
			CreateCampaignResponse response = automationCampaignSetupService.createOrUpdateSubAutomationForSplit(automationRequest, accountId, variant.getCampaignId(), userId);
			variant.setCampaignId(response.getId());
			variant.setSmsTemplateId(automationRequest.getSmsTemplateId());
			subCampaignResponseList.add(response);
			logger.info("createOrUpdateSubCampaignDetails :: Updated Variant object for account id {} and split campaign id {} is {}", accountId, splitCampaignId, variant);
		}
		return subCampaignResponseList;
	}
	
	/**
	 *
	 * This function creates/updates the SplitCampaign and SplitCampaignMapping table
	 *
	 * 
	 * @param SplitAutomationRequest,
	 *            accountId,splitCampaignId,userId,splitCampaign
	 */
	private SplitCampaign createOrUpdateSplitCampaignAndMappingAudit(SplitAutomationRequest request, Integer accountId, Integer splitCampaignId, String userId, SplitCampaign splitCampaign) {
		
		// 1.1 Prepare Split Campaign Mapping Entries
		List<SplitCampaignMapping> splitMappingList = prepareSplitCampaignMappingObjectList(request, accountId, splitCampaignId);
		
		// 1.2 Create or Update Split Campaign Entry
		splitCampaignDao.saveSplitCampaignEntry(splitCampaign);
		logger.info("createOrUpdateSplitCampaignAndMappingAudit :: Split Automation id created/updated is {}", splitCampaign.getId());
		
		// 1.3 Create or Update Split Campaign Mappings
		splitMappingList.stream().forEach(e -> {
			e.setSplitCampaignId(splitCampaign.getId());
			splitCampaignDao.saveSplitCampaignMappingEntry(e);
		});
		
		return splitCampaign;
	}
	
	/**
	 *
	 * This function prepares splitCampaignMapping object with updated data in request
	 * 1. Firstly check if its a new Automation or existing one based on splitCampaignId and validate the splitCampaignMapping object provided.
	 * 2. Call function to update SplitCampaignMapping object with data in request.
	 *
	 * 
	 * @param request,
	 *            accountId,splitCampaignId,campaignIdToSplitMappingMap
	 */
	private SplitCampaignMapping prepareSplitCampaignMappingObject(SplitCampaignVariantData request, Integer accountId, Integer splitCampaignId,
			Map<Integer, SplitCampaignMappingDTO> campaignIdToSplitMappingMap) {
		SplitCampaignMappingDTO splitMapping = null;
		if (!SplitCampaignUtils.isNewSplitAutomationCreationRequest(splitCampaignId)) {
			SplitCampaignUtils.validateSplitCampaignMapping(campaignIdToSplitMappingMap, splitCampaignId, accountId, request.getCampaignId());
			splitMapping = campaignIdToSplitMappingMap.get(request.getCampaignId());
		}
		return SplitCampaignUtils.createOrUpdateSplitMappingObject(request, splitMapping, accountId, splitCampaignId);
	}
	
	/**
	 *
	 * This function prepares updated splitCampaignMapping object list
	 * 1. Get SplitCampaignMapping List for SplitCampaignId and convert it to a map with campaignId as the key.
	 * 2. For each variant update the splitCampaignMapping object.
	 * 3. Return the updated list.
	 *
	 * 
	 * @param request,
	 *            accountId,splitCampaignId
	 */
	private List<SplitCampaignMapping> prepareSplitCampaignMappingObjectList(SplitAutomationRequest request, Integer accountId, Integer splitCampaignId) {
		Map<Integer, SplitCampaignMappingDTO> campaignIdToSplitMappingMap = SplitCampaignUtils.getCampaignIdToSplitMappingFromList(getSplitCampaignMappingDTOList(accountId, splitCampaignId));
		List<SplitCampaignMapping> splitCampaignMappingList = new ArrayList<>();
		request.getVariants().stream().forEach(e -> {
			SplitCampaignMapping mapping = prepareSplitCampaignMappingObject(e, accountId, splitCampaignId, campaignIdToSplitMappingMap);
			splitCampaignMappingList.add(mapping);
		});
		return splitCampaignMappingList;
	}
	
	/**
	 *
	 * This function does the following :
	 * 1. Validates if its an update request, Fetches splitCampaignMappings for a given splitCampaignId and throws an exception if not found.
	 * 2. If its a create request, send empty list of mappings.
	 *
	 * 
	 * @param accountId,splitCampaignId
	 */
	private List<SplitCampaignMappingDTO> getSplitCampaignMappingDTOList(Integer accountId, Integer splitCampaignId) {
		if (!SplitCampaignUtils.isNewSplitAutomationCreationRequest(splitCampaignId)) {
			List<SplitCampaignMappingDTO> existingMappingData = splitCampaignHelperService.prepareSplitMappingDataBySplitCampaignId(splitCampaignId, accountId);
			if (BooleanUtils.isFalse(SplitCampaignUtils.fetchAndValidateSplitMappingListSize(existingMappingData))) {
				logger.error("getSplitCampaignMappingDTOList :: No Split Automation Mapping found for split automation id {}", splitCampaignId);
				StringBuilder errorMsg = new StringBuilder("For SplitCampaignId ").append(splitCampaignId).append("For enterprise ").append(accountId).append("sub campaign mappings ")
						.append("Not found in system");
				throw new CampaignHTTPException(HttpStatus.NOT_FOUND, errorMsg.toString());
			}
			return existingMappingData;
		}
		return new ArrayList<>();
	}
	
	/**
	 *
	 * This function does the following :
	 * 1. Validates if its an create new automation request, fetch split automation name from db.
	 * 2. If its an update request, fetch splitCampaign entry from db, if not found throw error.
	 * 3. If for an update request the current name is changed, fetch split automation name from db.
	 * 4. Create or update request with updated data.
	 *
	 * 
	 * @param request,accountId,splitCampaignId,userId
	 */
	private SplitCampaign prepareSplitCampaignObjectWithChanges(SplitAutomationRequest request, Integer accountId, Integer splitCampaignId, String userId) {
		SplitCampaign splitCampaign = null;
		if (SplitCampaignUtils.isNewSplitAutomationCreationRequest(splitCampaignId)) {
			request.setCampaignName(splitCampaignHelperService.prepareAndGetSplitCampaignName(request.getCampaignName(), accountId));
		} else {
			splitCampaign = cacheService.getSplitCampaignById(splitCampaignId);
			
			if (splitCampaign == null) {
				logger.error("prepareSplitCampaignObjectWithChanges :: No Split Automation found with id {}", splitCampaignId);
				StringBuilder errorMsg = new StringBuilder("SplitCampaignId ").append(splitCampaignId).append(" For enterprise ").append(accountId).append(" Not found in system");
				throw new CampaignHTTPException(HttpStatus.NOT_FOUND, errorMsg.toString());
			}
			
			if (!StringUtils.equals(splitCampaign.getName(), request.getCampaignName())) {
				request.setCampaignName(splitCampaignHelperService.prepareAndGetSplitCampaignName(request.getCampaignName(), accountId));
			}
			
		}
		return SplitCampaignUtils.createOrUpdateSplitCampaignObject(request, accountId, CampaignUtils.getUserId(userId), splitCampaign, splitCampaignId);
	}
	
	/**
	 *
	 * Update status for split Automation
	 *
	 * 
	 * @param splitCampaignId,
	 *            status
	 */
	@Override
	@Transactional(value = "chainedTransactionManager", rollbackFor = Exception.class)
	public Boolean updateStatusForSplitAutomation(Integer splitCampaignId, Integer status, String userId) throws Exception {
		logger.info("updateStatusForSplitAutomation :: Request received to update status {} for split automation {}", status, splitCampaignId);
		Boolean isStatusUpdated = splitCampaignHelperService.updateStatusForSplitCampaign(splitCampaignId, status, userId);
		logger.info("updateStatusForSplitAutomation :: Request completion status {} to update status {} for split automation {}", isStatusUpdated, status, splitCampaignId);
		return isStatusUpdated;
	}
}
