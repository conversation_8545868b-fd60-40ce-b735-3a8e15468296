package com.birdeye.campaign.service.dao.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import javax.transaction.Transactional;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.aspect.annotation.Profiled;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.constant.TemplateStaticTokens;
import com.birdeye.campaign.deeplink.context.DeeplinkContextDTO;
import com.birdeye.campaign.dto.BusinessTemplateEntity;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.dto.TemplateDetailsDTO;
import com.birdeye.campaign.entity.BusinessSmsTemplate;
import com.birdeye.campaign.entity.ReferralTemplateData;
import com.birdeye.campaign.entity.ReviewRequest;
import com.birdeye.campaign.enums.SentimentCheckTypeEnum;
import com.birdeye.campaign.external.utils.DeeplinkUtils;
import com.birdeye.campaign.platform.constant.CampaignTypeEnum;
import com.birdeye.campaign.platform.constant.TemplateTypeEnum;
import com.birdeye.campaign.readonly.repository.BusinessSMSTemplateReadOnlyRepo;
import com.birdeye.campaign.repository.BusinessSMSTemplateRepo;
import com.birdeye.campaign.repository.ReviewRequestRepo;
import com.birdeye.campaign.service.dao.BusinessSmsTemplateDao;
import com.birdeye.campaign.service.referral.IReferralTemplateService;
import com.birdeye.campaign.sro.CXDataByTypeSRO;
import com.birdeye.campaign.sro.CXNPSDataSRO;
import com.birdeye.campaign.sro.CXSentimentDataSRO;
import com.birdeye.campaign.sro.CXStarDataSRO;
import com.birdeye.campaign.sro.DeeplinkInfoSRO;
import com.birdeye.campaign.utils.CampaignUtils;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.ReferralUtils;
import com.birdeye.template.exception.TemplateConfigException;
import com.birdeye.template.sms.dto.SmsTemplateMessage;
import com.birdeye.campaign.repository.BusinessSMSTemplateRepo.SmsTemplateCategoryToTypeProjection;

/**
 * <AUTHOR>
 */

@Service
public class BusinessSmsTemplateDaoImpl implements BusinessSmsTemplateDao {
	
	@Autowired
	private ReviewRequestRepo			reviewRequestRepo;
	
	@Autowired
	private BusinessSMSTemplateRepo		businessSMSTemplateRepo;
	
	@Autowired
	private BusinessSMSTemplateReadOnlyRepo	businessSMSTemplateReadOnlyRepo;
	
	@Autowired
	private IReferralTemplateService	referralTemplateService;

	private static final Logger			logger	= LoggerFactory.getLogger(BusinessSmsTemplateDaoImpl.class);
	
	@Override
	@Transactional
	public void deeplinkInfoSmsTaskSync(Integer templateId, String campaignType, DeeplinkInfoSRO deeplinkInfoSRO, Long requestId, DeeplinkContextDTO context) {
		
		BusinessSmsTemplate businessSmsTemplate = getSMSTemplateById(templateId);
		if (businessSmsTemplate == null) {
			TemplateTypeEnum templateTypeEnum = CampaignUtils.getEmailTemplateTypeByCampaignType(campaignType);
			businessSmsTemplate = getDefaultBusinessSmsTemplateByType(templateTypeEnum.getName());
		}
		Boolean fetchEmployeeToken = StringUtils.contains(businessSmsTemplate.toString(), TemplateStaticTokens.EMPLOYEE_NAME_TOKEN);
		context.setFetchEmployeeToken(fetchEmployeeToken);
		prepareDeeplinkInfoForSms(deeplinkInfoSRO, businessSmsTemplate, requestId);
		
	}
	
	@Override
	@Transactional
	public SmsTemplateMessage getSmsTemplateMessage(Integer templateId, String type) {
		BusinessSmsTemplate businessSmsTemplate = getSmsTemplateByType(templateId, type);
		return new SmsTemplateMessage(businessSmsTemplate, businessSmsTemplate.getNpsLabels(), businessSmsTemplate.getStarLabels());
	}
	
	@Override
	@Transactional
	public SmsTemplateMessage getSmsTemplateMessageForReseller(Integer resellerId, String type) {
		BusinessSmsTemplate businessSmsTemplate = getResellerSmsTemplate(resellerId, type);
		if (businessSmsTemplate == null) {
			return null;
		}
		return new SmsTemplateMessage(businessSmsTemplate, businessSmsTemplate.getNpsLabels(), businessSmsTemplate.getStarLabels());
	}
	
	private void prepareDeeplinkInfoForSms(DeeplinkInfoSRO deeplinkInfoSRO, BusinessSmsTemplate businessSmsTemplate, Long requestId) {
		// review
		deeplinkInfoSRO.setReviewHeading(businessSmsTemplate.getRecommendPageHeading());
		deeplinkInfoSRO.setReviewMessage(businessSmsTemplate.getRecommendPageMessage());
		deeplinkInfoSRO.setReviewEnabled(businessSmsTemplate.getReviewEnable());
		deeplinkInfoSRO.setReviewSiteButtonColor(businessSmsTemplate.getReviewSiteButtonColor());
		deeplinkInfoSRO.setReviewSiteButtonTextColor(businessSmsTemplate.getReviewSiteButtonTextColor());
		DeeplinkUtils.prepareTemplateLabelConfigsForDeeplinkResponse(deeplinkInfoSRO, businessSmsTemplate.getLabelConfigs(), businessSmsTemplate.getType());
		
		// thankyou
		deeplinkInfoSRO.setThankYouHeading(businessSmsTemplate.getThankyouHeading());
		deeplinkInfoSRO.setThankYouMessage(businessSmsTemplate.getThankyouMessage());
		
		// contact
		deeplinkInfoSRO.setContactUsEnabled(businessSmsTemplate.getEnableContactUs());
		deeplinkInfoSRO.setContactUsButtonText(businessSmsTemplate.getContactUsButtonText());
		deeplinkInfoSRO.setContactUsMessage(businessSmsTemplate.getContactUsMessage());
		deeplinkInfoSRO.setContactUsButtonColor(businessSmsTemplate.getContactUsButtonColor());
		deeplinkInfoSRO.setContactUsButtonTextColor(businessSmsTemplate.getContactUsButtonTextColor());
		deeplinkInfoSRO.setContactUsCustomUrl(prepareCustomUrl(businessSmsTemplate.getNonRecommendedUrl(), requestId));
		
		// feedback
		deeplinkInfoSRO.setFeedbackCallbackEnabled(businessSmsTemplate.getEnableFeedbackMessage());
		deeplinkInfoSRO.setFeedbackCallbackMessage(businessSmsTemplate.getFeedbackCallbackMessage());
		deeplinkInfoSRO.setFeedbackCheckboxEnabled(businessSmsTemplate.getEnableFeedbackCheckbox());
		deeplinkInfoSRO.setFeedbackMessage(businessSmsTemplate.getNonRecommendReviewPageMessage());
		
		// cx
		if (businessSmsTemplate.getType().equalsIgnoreCase(Constants.CX_TEMPLATE_TYPE)) {
			deeplinkInfoSRO.setCxDataByType(getCxDataByType(businessSmsTemplate));
		}
		
		deeplinkInfoSRO.setLocationBrandingEnabled(businessSmsTemplate.getLocationBrandingEnabled());
		
		// referral message

		if (StringUtils.equalsIgnoreCase(businessSmsTemplate.getType(), Constants.REFERRAL_TEMPLATE_TYPE)) {

			deeplinkInfoSRO.setReferralMessage(businessSmsTemplate.getReferralMessage());
			deeplinkInfoSRO.setReferralImage(getReferralImage(businessSmsTemplate));
			deeplinkInfoSRO.getReferralInfo().setReferralImage(getReferralImage(businessSmsTemplate));
			
			deeplinkInfoSRO.getReferralInfo().setReferralMessageWithoutLink(ReferralUtils.getReferralMessageWithoutReferralLinkForText(businessSmsTemplate.getReferralMessage()));
			ReferralTemplateData referralTemplate = referralTemplateService.getSMSReferralTemplateById(businessSmsTemplate.getId());
			if (referralTemplate != null) {
				deeplinkInfoSRO.getReferralInfo().setReferralFormCtaLabel(referralTemplate.getReferralFormCtaLabel());
				deeplinkInfoSRO.getReferralInfo().setReferralMessageCustomUrlEnabled(referralTemplate.getReferralMessageCtaUrlEnabled());
				deeplinkInfoSRO.getReferralInfo().setReferralMessageCustomUrl(referralTemplate.getReferralMessageCtaUrl());
				deeplinkInfoSRO.getReferralInfo().setReferralMessageEmailSubject(referralTemplate.getReferralMessageEmailSubject());
			}
		}
		
		deeplinkInfoSRO.setConfirmButtonEnabled(CoreUtils.getBooleanValueFromInteger(businessSmsTemplate.getConfirmButtonEnabled()));
		deeplinkInfoSRO.setRescheduleButtonEnabled(CoreUtils.getBooleanValueFromInteger(businessSmsTemplate.getRescheduleButtonEnabled()));
		deeplinkInfoSRO.setCancelButtonEnabled(CoreUtils.getBooleanValueFromInteger(businessSmsTemplate.getCancelButtonEnabled()));
		deeplinkInfoSRO.setBookAppointmentButtonEnabled(CoreUtils.getBooleanValueFromInteger(businessSmsTemplate.getBookAppointmentButtonEnabled()));
		deeplinkInfoSRO.setFeedbackTextLabel(businessSmsTemplate.getFeedbackTextLabel());
		deeplinkInfoSRO.setSubmitButtonText(businessSmsTemplate.getSubmitButtonText());
	}

	private String getReferralImage(BusinessSmsTemplate businessSmsTemplate) {
		List<String> mediaUrls = CoreUtils.parseDelimitedStringsWithDefaultAsEmptyList(businessSmsTemplate.getMediaUrls(), ",");
		if(CollectionUtils.isEmpty(mediaUrls)) {
			return null;
		}
		return mediaUrls.get(0);
		
	}
	
	private CXDataByTypeSRO getCxDataByType(BusinessSmsTemplate smsTemplateMessage) {
		CXDataByTypeSRO cxDataByType = new CXDataByTypeSRO();
		if (SentimentCheckTypeEnum.SENTIMENT.getType().equalsIgnoreCase(smsTemplateMessage.getSentimentCheckType())) {
			cxDataByType.setSentiment(getCXSentimentDataSRO(smsTemplateMessage));
		} else if (SentimentCheckTypeEnum.STAR.getType().equalsIgnoreCase(smsTemplateMessage.getSentimentCheckType())) {
			cxDataByType.setStar(getCXStarDataSRO(smsTemplateMessage));
		} else {
			cxDataByType.setNps(getCXNPSDataSRO(smsTemplateMessage));
		}
		
		return cxDataByType;
	}
	
	private CXSentimentDataSRO getCXSentimentDataSRO(BusinessSmsTemplate smsTemplateMessage) {
		CXSentimentDataSRO sentiment = new CXSentimentDataSRO();
		// SENTIMENT TYPE
		sentiment.setSelected(CoreUtils.getIntegerValueFromBoolean(StringUtils.equalsIgnoreCase(smsTemplateMessage.getSentimentCheckType(), SentimentCheckTypeEnum.SENTIMENT.getType())));
		// SENTIMENT
		sentiment.setHeading(smsTemplateMessage.getSentimentHeading());
		sentiment.setMessage(smsTemplateMessage.getSentimentMessage());
		sentiment.setPositiveButtonLabel(smsTemplateMessage.getPositiveLinkLabel());
		sentiment.setNegativeButtonLabel(smsTemplateMessage.getNegativeLinkLabel());
		sentiment.setNeutralButtonLabel(smsTemplateMessage.getNeutralLinkLabel());
		sentiment.setShowEmoticon(smsTemplateMessage.getShowEmoticon());
		sentiment.setExcludeNeutralButton(smsTemplateMessage.getExcludeNeutral());
		return sentiment;
	}
	
	private String prepareCustomUrl(String nonRecommendedUrl, Long requestId) {
		
		if (StringUtils.isBlank(nonRecommendedUrl)) {
			return nonRecommendedUrl;
		}
		
		ReviewRequest rr = reviewRequestRepo.getById(requestId);
		StringBuilder customURLBuilder = new StringBuilder(nonRecommendedUrl);
		
		if (rr != null) {
			
			if (CoreUtils.ifUrlContainsQueryParams(nonRecommendedUrl)) {
				customURLBuilder.append("&becid=").append(rr.getCustId()).append("&checkinid=").append(rr.getCheckinId());
			} else {
				customURLBuilder.append("?becid=").append(rr.getCustId()).append("&checkinid=").append(rr.getCheckinId());
			}
			
			appendPrefillSurveyParams(customURLBuilder, rr);
		}
		
		return customURLBuilder.toString();
	}
	
	// BIRD-103156
	private void appendPrefillSurveyParams(StringBuilder customURLBuilder, ReviewRequest reviewRequest) {
		if (!StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.REVIEW_REQUEST.getType())) {
			return;
		}
		
		customURLBuilder.append("&requestId=").append(reviewRequest.getId()).append("&prefillSurvey=1");
	}

	private CXNPSDataSRO getCXNPSDataSRO(BusinessSmsTemplate smsTemplateMessage) {
		CXNPSDataSRO nps = new CXNPSDataSRO();
		// SENTIMENT TYPE
		nps.setSelected(CoreUtils.getIntegerValueFromBoolean(StringUtils.equalsIgnoreCase(smsTemplateMessage.getSentimentCheckType(), SentimentCheckTypeEnum.NPS.getType())));
		// NPS
		nps.setHeading(smsTemplateMessage.getQuestion());
		nps.setMessage(smsTemplateMessage.getWriteReviewQuestion());
		nps.setLabels(new ArrayList<>(smsTemplateMessage.getNpsLabels()));
		return nps;
	}

	@Override
	public BusinessSmsTemplate getSMSTemplateById(Integer templateId) {
		return businessSMSTemplateRepo.findFirstById(templateId);
	}
	
	@Override
	public BusinessSmsTemplate getSMSTemplateByEnterpriseIdAndTemplateId(Integer enterpriseId, Integer templateId) {
		return businessSMSTemplateRepo.findOneByEnterpriseIdAndTemplateId(enterpriseId, templateId);
	}
	
	@Override
	public BusinessSmsTemplate getSMSTemplateByTemplateIdAndType(Integer templateId,String type) {
		return businessSMSTemplateRepo.findByTemplateIdAndType(templateId,type);
	}

	@Override
	public BusinessSmsTemplate getDefaultBusinessSmsTemplateByType(String type) {
		BusinessSmsTemplate businessSmsTemplate = null;
		List<BusinessSmsTemplate> businessSmsTemplates = businessSMSTemplateRepo.getDefaultBusinessSmsTemplateByType(type);
		if (businessSmsTemplates != null && !businessSmsTemplates.isEmpty()) {
			businessSmsTemplate = businessSmsTemplates.get(0);
		}
		return businessSmsTemplate;
	}
	
	private CXStarDataSRO getCXStarDataSRO(BusinessSmsTemplate smsTemplateMessage) {
		CXStarDataSRO star = new CXStarDataSRO();
		// SENTIMENT TYPE
		star.setSelected(CoreUtils.getIntegerValueFromBoolean(StringUtils.equalsIgnoreCase(smsTemplateMessage.getSentimentCheckType(), SentimentCheckTypeEnum.STAR.getType())));
		// STAR
		star.setHeading(smsTemplateMessage.getStarHeading());
		star.setMessage(smsTemplateMessage.getStarMessage());
		star.setLabels(new ArrayList<>(smsTemplateMessage.getStarLabels()));
		return star;
	}
	
	private BusinessSmsTemplate getSmsTemplateByType(Integer templateId, String type) {
		BusinessSmsTemplate businessSmsTemplate = null;
		
		if (templateId == null || templateId == 0) {
			List<BusinessSmsTemplate> businessSmsTemplates = businessSMSTemplateRepo.getDefaultBusinessSmsTemplateByType(type);
			if (businessSmsTemplates != null && !businessSmsTemplates.isEmpty()) {
				businessSmsTemplate = businessSmsTemplates.get(0);
			}
		} else {
			businessSmsTemplate = businessSMSTemplateRepo.findFirstById(templateId);
		}
		
		if (businessSmsTemplate == null) {
			throw new TemplateConfigException(HttpStatus.BAD_REQUEST, ErrorCodes.NO_TEMPLATE_FOUND.getMessage());
		}
		return businessSmsTemplate;
	}
	
	private BusinessSmsTemplate getResellerSmsTemplate(Integer resellerId, String type) {
		
		List<BusinessSmsTemplate> businessSmsTemplates = businessSMSTemplateRepo.findByResellerIdAndTypeAndIsDeleted(resellerId, type, 0);
		
		if (CollectionUtils.isEmpty(businessSmsTemplates)) {
			logger.info("No business sms template found for Reseller id {} and type {}", resellerId, type);
			return null;
		}
		return businessSmsTemplates.get(0);
	}
	
	@Override
	@Cacheable(key = "#enterpriseId.toString()", value = "smsTemplatesListCache", unless = "#result == null")
	public CachedCollectionWrapper<BusinessTemplateEntity> getTemplatesListByEnterpriseId(Integer enterpriseId) {
		return new CachedCollectionWrapper<>(businessSMSTemplateRepo.getByEnterpriseIdAndIsDeletedV2(enterpriseId));
	}
	

	/**
	 * 30 mins cache to get location level templates for an enterprise
	 * cache will get evict on create update and delete flow
	 */
	
	@Override
	@Profiled
	@Cacheable(key = "#enterpriseId.toString()", value = "smsLocationTemplatesListCache", unless = "#result == null")
	public CachedCollectionWrapper<BusinessTemplateEntity> getLocationTemplatesListByEnterpriseId(Integer enterpriseId) {
		return new CachedCollectionWrapper<>(businessSMSTemplateRepo.getLocationLevelTemplateByEnterpriseIdAndIsDeletedForMessenger(enterpriseId));
	}
	
	@Override
	public boolean getBusinessSmsTemplateExistsByNameAndEnterpriseId(String templateName, Integer businessId) {
		return businessSMSTemplateRepo.existsByNameAndEnterpriseIdAndIsDeleted(templateName, businessId, 0);
	}
	
	@Override
	@Profiled
	public List<BusinessTemplateEntity> getDefaultSmsTemplateByEnterpriseIdAndType(Integer enterpriseOrSMBId, String templateType) {
		return businessSMSTemplateRepo.getByEnterpriseIdAndIsDeletedAndType(enterpriseOrSMBId, templateType);
	}
	
	@Override
	public List<BusinessTemplateEntity> getSmsTemplateListByEnterpriseAndTemplateIds(List<Integer> templateIds, Integer enterpriseOrSMBId) {
		return businessSMSTemplateRepo.getByEnterpriseIdAndTemplateIdIn(enterpriseOrSMBId, templateIds);
	}
	
	@Override
	@Profiled
	public Integer updateDefaultTemplateFlagInBusinessSmsTemplate(Integer enterpriseOrSMBId, Integer isDefaultTemplate) {
		return businessSMSTemplateRepo.updateDefaultTemplateFlag(enterpriseOrSMBId, isDefaultTemplate);
	}
	
	public BusinessSmsTemplate saveBusinessSmsTemplate(BusinessSmsTemplate audit) {
		return businessSMSTemplateRepo.saveAndFlush(audit);
	}
	
	@Override
	public Integer getBusinessSmsTemplateByEnterpriseIdAndName(Integer enterpriseId, String name) {
		return businessSMSTemplateRepo.getBusinessSmsTemplateSameNameCountByEnterpriseIdAndName(enterpriseId, name);
	}
	
	@Override
	public CachedCollectionWrapper<BusinessTemplateEntity> getTemplatesListByAccountId(Integer enterpriseId) {
		return new CachedCollectionWrapper<>(businessSMSTemplateRepo.getByEnterpriseIdAndIsDeleted(enterpriseId));
	}
	
	@Override
	public List<BusinessTemplateEntity> getBusinesTemplateListByAccountIdAndTemplateTypes(Integer accountId, List<String> templateTypes){
		return businessSMSTemplateRepo.getByEnterpriseIdAndIsDeletedAndTypeIn(accountId, templateTypes);
	}
	
	@Override
	public Integer findByEnterpriseIdAndTypeAndIsDefaultTemplate(Integer enterpriseId, String templateType, Integer isDefaultTemplate) {
		return businessSMSTemplateRepo.findByEnterpriseIdAndTypeAndIsDefaultTemplate(enterpriseId, templateType, isDefaultTemplate);
	}
	
	@Override
	public List<TemplateDetailsDTO> getBySMSTemplateIds(Set<Integer> smsTemplateIds) {
		return businessSMSTemplateRepo.getTemplateDetailsByIdIn(smsTemplateIds);
	}

	/**
	 * Fetch active SMS templates IDs for account by template type & communication category.
	 * 
	 * @param accountId
	 * @param templateType
	 * @param communicationCategory
	 * @return
	 */
	@Override
	public List<Integer> getSmsTemplatesForAccountByTypeAndCategory(Integer accountId, String templateType, String communicationCategory) {
		if (Objects.isNull(accountId) || StringUtils.isBlank(templateType) || StringUtils.isBlank(communicationCategory)) {
			return Collections.emptyList();
		}
		
		return businessSMSTemplateReadOnlyRepo.getActiveTemplatesByEnterpriseIdAndTypeAndCategory(accountId, templateType, communicationCategory);
	}
	
	@Override
	public SmsTemplateCategoryToTypeProjection fetchSmsCategoryByTemplateId(Integer templateId) {
		return businessSMSTemplateRepo.getSmsTemplateTypeAndCategoryById(templateId).orElse(null);
	}
	
	/**
	 * Get count of active sms templates present in an account by type.
	 * 
	 */
	@Override
	public Integer getActiveSmsTemplatesCountForAnAccountByTypes(Integer accountId, List<String> templateTypes) {
		if (Objects.isNull(accountId) || CollectionUtils.isEmpty(templateTypes)) {
			logger.info("getActiveSmsTemplatesCountForAnAccount - Blank value received for accountId or templateTypes, returning 0!");
			return 0;
		}
		return businessSMSTemplateRepo.countByEnterpriseIdAndIsDeletedAndTypeIn(accountId, 0, templateTypes);
	}
}
