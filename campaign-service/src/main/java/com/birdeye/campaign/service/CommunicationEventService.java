/**
 * @file_name CommunicationEventService.java
 * @created_date 16 Jul 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.service;

import com.birdeye.campaign.request.CampaignCommunicationEventRequest;
import com.birdeye.campaign.request.EmailEventInfo;

/**
 * @file_name CommunicationEventService.java
 * @created_date 16 Jul 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public interface CommunicationEventService {

	/**
	 * @param request
	 */
	void logCampaignCommunicationEvent(CampaignCommunicationEventRequest request);
	
	void handlerForCampaignEmailOpenEvents(EmailEventInfo emailEventInfo);

	/**
	 * @param request
	 */
	void handlerForCampaignEmailFailureEvents(EmailEventInfo request);
	
}
