package com.birdeye.campaign.service.dao.impl;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import org.springframework.stereotype.Service;

import com.birdeye.campaign.entity.Promotion;
import com.birdeye.campaign.readonly.repository.PromotionReadOnlyRepo;
import com.birdeye.campaign.request.InfluenceRateFilterCriteria;
import com.birdeye.campaign.service.dao.PromotionRequestDao;

@Service
public class PromotionRequestDaoImpl implements PromotionRequestDao {
	
	private PromotionReadOnlyRepo promotionReadOnlyRepo;
	
	public PromotionRequestDaoImpl(PromotionReadOnlyRepo promotionReadOnlyRepo) {
		this.promotionReadOnlyRepo = promotionReadOnlyRepo;
	}
	
	/**
	 * Fetch eligible promotional requests for influence rate.
	 * 
	 * @param influenceRateFilterCriteria
	 * @return
	 */
	@Override
	public List<Promotion> getEligiblePromotionRequestsForInfluence(InfluenceRateFilterCriteria influenceRateFilterCriteria) {
		if (Objects.isNull(influenceRateFilterCriteria)) {
			return Collections.emptyList();
		}
		
		return promotionReadOnlyRepo.findByCustomerIdAndBusinessIdAndTemplateIdsInAndDeliveryStatusAndSentDateBetween(influenceRateFilterCriteria.getCustomerId(),
				influenceRateFilterCriteria.getBusinessId(), influenceRateFilterCriteria.getTemplateIds(), influenceRateFilterCriteria.getDeliveryStatus(),
				influenceRateFilterCriteria.getFromDate(), influenceRateFilterCriteria.getToDate());
	}
	
}
