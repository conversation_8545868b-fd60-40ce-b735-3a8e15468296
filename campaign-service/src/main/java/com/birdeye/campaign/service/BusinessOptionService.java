package com.birdeye.campaign.service;

import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.sro.DeeplinkInfoSRO;

/**
 * 
 * <AUTHOR>
 *
 */
public interface BusinessOptionService {
	
	public String getBirdeyeAsReview(BusinessEnterpriseEntity business);
	
	/**
	 * Method to check if survey is enabled for this business [with hierarchy support].
	 * 
	 * @param business - Id of business
	 * @return
	 */
	public boolean isSurveyEnabledForBusiness(Integer businessId);

	/**
	 * @param business
	 * @return
	 */
	String getPublicProfileURL(BusinessEnterpriseEntity business);

	
	public boolean isUnsubscribeSMSTextEnable(BusinessEnterpriseEntity business);
	
	public boolean isSMSDeeplinkEnabledForEmailRequest(BusinessEnterpriseEntity business);
	
	public boolean isSentimentCheckOverride(BusinessEnterpriseEntity business);

	String getReplyToEmailIds(BusinessEnterpriseEntity business);

	String getEmailIdForNoReplyToOption(BusinessEnterpriseEntity business);
	/**
	 * @param businessId
	 * @return
	 */
	String getReplyToEmailForABusiness(Integer businessId);

	boolean isMmsOpted(BusinessEnterpriseEntity business);

	boolean isMobileFlowEnabledForDeeplink(BusinessEnterpriseEntity business);
	
	/**
	 * Setting disclaimer data in deeplink info object.
	 * 
	 * @param business
	 * @param deeplinkInfoSRO
	 */
	public void setDisclaimerInfoInDeeplink(BusinessEnterpriseEntity business, DeeplinkInfoSRO deeplinkInfoSRO);

	Long showRatingAndCount(BusinessEnterpriseEntity business);
	
	/**
	 * Fetch business title from businessType. Eg: Product/ loan officer.
	 * If businessTypeTitle is set in businessOptions of this business, then it is given priority.
	 * 
	 * @param businessId
	 * @return
	 */
	public String getBusinessTypeTitle(Integer businessId);

	String getRtobEmailSenderOption(BusinessEnterpriseEntity business);

	/**
	 * @param business
	 * @return
	 */
	String getDomainBaseURLPrefixForBusiness(BusinessEnterpriseEntity business);
	
	public Integer getSmsOptedForSmb(BusinessEnterpriseEntity business);

}
