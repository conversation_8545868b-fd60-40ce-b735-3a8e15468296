package com.birdeye.campaign.service.impl;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.birdeye.campaign.aspect.annotation.Profiled;
import com.birdeye.campaign.async.service.AsyncBrokerService;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.constant.KafkaTopicTypeEnum;
import com.birdeye.campaign.dto.BusinessTemplateEntity;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.dto.DefaultTemplateDTO;
import com.birdeye.campaign.dto.DefaultTemplateUpdateRequestDTO;
import com.birdeye.campaign.dto.KafkaMessage;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.exception.CampaignHTTPException;
import com.birdeye.campaign.executor.services.CampaignCallable;
import com.birdeye.campaign.executor.services.CampaignExecutorService;
import com.birdeye.campaign.executor.services.ExecutorCommonService;
import com.birdeye.campaign.kafka.service.KafkaService;
import com.birdeye.campaign.report.ReportService;
import com.birdeye.campaign.repository.BusinessEmailTemplateRepo;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.CampaignModificationAuditService;
import com.birdeye.campaign.service.DefaultTemplateHelperService;
import com.birdeye.campaign.service.dao.BusinessSmsTemplateDao;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.DefaultTemplateUtils;

@Service("DefaultTemplateHelperService")
public class DefaultTemplateHelperServiceImpl implements DefaultTemplateHelperService {
	
	private static final Logger			logger	= LoggerFactory.getLogger(DefaultTemplateHelperServiceImpl.class);
	
	@Autowired
	private ReportService				reportService;
	
	@Autowired
	private CacheService				cacheService;
	
	@Autowired
	private BusinessEmailTemplateRepo	businessEmailTemplateRepo;
	
	@Autowired
	private BusinessSmsTemplateDao		businessSmsTemplateDao;
	
	@Autowired
	private AsyncBrokerService			asyncBrokerService;
	
	@Autowired
	@Qualifier(Constants.CAMPAIGN_DT_COMPLETABLE_FUTURE_TASK_EXECUTOR)
	private ThreadPoolTaskExecutor		threadPoolTaskExecutor;
	
	@Autowired
	private ExecutorCommonService		executorCommonService;
	
	@Autowired
	private KafkaService				kafkaService;
	
	@Autowired
	private CampaignModificationAuditService	campaignModificationAuditService;
	
	private static final Integer		ONE		= 1;
	
	private static final Integer		ZERO	= 0;
	
	/**
	 * Fetch default template based upon enterprise id, template type and source i.e email or sms
	 *
	 * 
	 * @param enterpriseId,templateType,source
	 */
	@Override
	public BusinessTemplateEntity getDefaultTemplateByEnterpriseIdAndTypeAndSource(Integer enterpriseId, String templateType, String source) {
		logger.info("getDefaultTemplateByEnterpriseIdAndTypeAndSource :: request recieved to get default template for enterprise id {}, template type {} and source {}", enterpriseId, templateType,
				source);
		BusinessTemplateEntity defaultTemplate = null;
		if (StringUtils.equalsIgnoreCase(source, Constants.SMS_TYPE)) {
			defaultTemplate = cacheService.getDefaultSmsTemplate(enterpriseId, StringUtils.lowerCase(templateType));
		} else if (StringUtils.equalsIgnoreCase(source, Constants.EMAIL_TYPE)) {
			defaultTemplate = cacheService.getDefaultEmailTemplate(enterpriseId, StringUtils.lowerCase(templateType));
		}
		return defaultTemplate;
	}
	
	/**
	 * Fetch template list from db based upon template ids, enterprise id and source i.e email or sms
	 *
	 * 
	 * @param templateIds,source,enterpriseId
	 */
	private List<BusinessTemplateEntity> getTemplatesFromDb(List<Integer> templateIds, String source, Integer enterpriseId) {
		if (StringUtils.equalsIgnoreCase(source, Constants.SMS_TYPE)) {
			return businessSmsTemplateDao.getSmsTemplateListByEnterpriseAndTemplateIds(templateIds, enterpriseId);
		} else if (StringUtils.equalsIgnoreCase(source, Constants.EMAIL_TYPE)) {
			return businessEmailTemplateRepo.getByEnterpriseIdAndTemplateIdList(enterpriseId, templateIds);
		}
		return null;
	}
	
	/**
	 * This function does the following :
	 * 1. If default template id is given, fetch that and new template id to be set as default from db.
	 * 2. If default template id is not given
	 * Fetch default template with given enterprise id, template type and source(email or sms)
	 * Fetch template id to be set as default from db
	 * If template id to be set as default not found return null
	 * Else if default template found, set its template id in dto and add to template list
	 *
	 * 
	 * @param defaultTemplateObject
	 */
	private List<BusinessTemplateEntity> getBusinessTemplateEntity(DefaultTemplateUpdateRequestDTO defaultTemplateObject) {
		if (defaultTemplateObject.getDefaultTemplateId() != null) {
			List<BusinessTemplateEntity> templateList = getTemplatesFromDb(new ArrayList<>(Arrays.asList(defaultTemplateObject.getDefaultTemplateId(), defaultTemplateObject.getTemplateId())),
					defaultTemplateObject.getSource(), defaultTemplateObject.getEnterpriseId());
			return templateList;
		} else {
			BusinessTemplateEntity defaultBusinessTemplate = getDefaultTemplateByEnterpriseIdAndTypeAndSource(defaultTemplateObject.getEnterpriseId(), defaultTemplateObject.getTemplateType(),
					defaultTemplateObject.getSource());
			List<BusinessTemplateEntity> templateList = getTemplatesFromDb(new ArrayList<>(Arrays.asList(defaultTemplateObject.getTemplateId())), defaultTemplateObject.getSource(),
					defaultTemplateObject.getEnterpriseId());
			if (CollectionUtils.isEmpty(templateList)) {
				logger.info("getBusinessTemplateEntity :: No template found for template id {} and enterprise id {} and source {}", defaultTemplateObject.getTemplateId(),
						defaultTemplateObject.getEnterpriseId(), defaultTemplateObject.getSource());
				return null;
			}
			if (defaultBusinessTemplate != null) {
				logger.info("getBusinessTemplateEntity :: Default template found {} for enterprise id {} and source {} , updating default template id in request",
						defaultBusinessTemplate.getTemplateId(), defaultTemplateObject.getEnterpriseId(), defaultTemplateObject.getSource());
				defaultTemplateObject.setDefaultTemplateId(defaultBusinessTemplate.getTemplateId());
				templateList.add(defaultBusinessTemplate);
			}
			return templateList;
		}
	}
	
	/**
	 * Update default template status to given status for sms template with given template id
	 *
	 * 
	 * @param templateId, status
	 */
	private Integer updateDefaultStatusForSmsTemplate(Integer templateId, Integer status) {
		return businessSmsTemplateDao.updateDefaultTemplateFlagInBusinessSmsTemplate(templateId, status);
	}
	
	/**
	 * Update default template status to given status for email template with given template id
	 *
	 * 
	 * @param templateId, status
	 */
	private Integer updateDefaultStatusForEmailTemplate(Integer templateId, Integer status) {
		return businessEmailTemplateRepo.updateDefaultTemplateFlag(templateId, status);
	}
	
	/**
	 * This function does the following :
	 * 1. Based upon source(email or sms), if default template id present set it to non default
	 * 2. Set template id to be set as default to default
	 * 3. Check if both update queries's result is 1 or not
	 * 4. If not throw an exception, to revert any changes made in db
	 * 5. Update new default template in cache
	 *
	 * 
	 * @param DefaultTemplateUpdateRequestDTO,templateList
	 */
	private void updateDefaultTemplateInDbAndCache(DefaultTemplateUpdateRequestDTO object, List<BusinessTemplateEntity> templateList) {
		Integer nonDefaultTemplateUpdate = ONE;
		Integer defaultTemplateUpdate = ZERO;
		if (StringUtils.equalsIgnoreCase(object.getSource(), Constants.SMS_TYPE)) {
			if (object.getDefaultTemplateId() != null) {
				logger.info("updateDefaultTemplateInDbAndCache :: Updating original default template to non default for template id {} and source {}", object.getDefaultTemplateId(),
						object.getSource());
				nonDefaultTemplateUpdate = updateDefaultStatusForSmsTemplate(object.getDefaultTemplateId(), ZERO);
			}
			logger.info("updateDefaultTemplateInDbAndCache :: Updating default template flag for template id {} and source {}", object.getTemplateId(), object.getSource());
			defaultTemplateUpdate = updateDefaultStatusForSmsTemplate(object.getTemplateId(), ONE);
		} else if (StringUtils.equalsIgnoreCase(object.getSource(), Constants.EMAIL_TYPE)) {
			if (object.getDefaultTemplateId() != null) {
				logger.info("updateDefaultTemplateInDbAndCache :: Updating original default template to non default for template id {} and source {}", object.getDefaultTemplateId(),
						object.getSource());
				nonDefaultTemplateUpdate = updateDefaultStatusForEmailTemplate(object.getDefaultTemplateId(), ZERO);
			}
			logger.info("updateDefaultTemplateInDbAndCache :: Updating default template flag for template id {} and source {}", object.getTemplateId(), object.getSource());
			defaultTemplateUpdate = updateDefaultStatusForEmailTemplate(object.getTemplateId(), ONE);
		}
		
		if (nonDefaultTemplateUpdate != ONE || defaultTemplateUpdate != ONE) {
			logger.error("updateDefaultTemplateInDbAndCache :: Error occurred while updating flag for enterprise id {} and source {}", object.getEnterpriseId(), object.getSource());
			throw new CampaignException(ErrorCodes.ERROR_OCCURRED_WHILE_UPDATING_REQUEST, ErrorCodes.ERROR_OCCURRED_WHILE_UPDATING_REQUEST.getMessage());
		}
		
		BusinessTemplateEntity template = DefaultTemplateUtils.fetchTemplateById(object.getTemplateId(), templateList);
		if (template != null) {
			logger.info("updateDefaultTemplateInDbAndCache :: Updating default template cache for template id {} and source {}", object.getTemplateId(), object.getSource());
			template.setIsDefaultTemplate(ONE);
			cacheService.updateDefaultTemplateByEnterpriseIdAndTypeAndSource(object.getEnterpriseId(), StringUtils.lowerCase(object.getTemplateType()), StringUtils.lowerCase(object.getSource()), template);
			evictTemplateListingCache(object.getSource(), object.getEnterpriseId());
		}
	}
	
	/**
	 * This function updates the listing cache based upon source (email or sms)
	 *
	 * 
	 * @param source,enterpriseId
	 */
	@Override
	public void evictTemplateListingCache(String source, Integer enterpriseId) {
		if (StringUtils.equalsIgnoreCase(source, Constants.EMAIL_TYPE)) {
			asyncBrokerService.evictEmailTemplatesListCache(enterpriseId);
		}
		if (StringUtils.equalsIgnoreCase(source, Constants.SMS_TYPE)) {
			asyncBrokerService.evictSmsTemplatesListCache(enterpriseId);
		}
	}

	/**
	 * This function does the following :
	 * 1. Fetch template list
	 * 2. Validate template list
	 * 3. Call update function if request is valid else throw an exception
	 *
	 * 
	 * @param DefaultTemplateUpdateRequestDTO
	 */
	@Transactional(value = "chainedTransactionManager", rollbackFor = Exception.class, readOnly = false)
	@Override
	public void validateAndUpdateDefaultTemplate(DefaultTemplateUpdateRequestDTO object) {
		logger.info("validateAndUpdateDefaultTemplate :: Request recieved to update default template {}", object);
		List<BusinessTemplateEntity> templateList = getBusinessTemplateEntity(object);
		if (DefaultTemplateUtils.validateTemplateList(templateList, object)) {
			updateDefaultTemplateInDbAndCache(object, templateList);
			// BIRD-74509 - Maintain audit for default template update
			campaignModificationAuditService.prepareAndPublishDefaultTemplateUpdateEvent(object.getDefaultTemplateId(), object.getTemplateId(),
					CoreUtils.getNullSafeInteger(MDC.get(Constants.USER_ID)), object.getEnterpriseId(), Instant.now().toEpochMilli(), object.getSource());
			return;
		}
		throw new CampaignHTTPException(HttpStatus.BAD_REQUEST, ErrorCodes.INVALID_REQUEST_UPDATE_DEFAULT_TEMPLATE.getMessage());
	}
	
	/**
	 * Validates and check if any default template is present for the given enterprise id , template type and source(email or sms)
	 *
	 * 
	 * @param enterpriseId,templateType,source
	 */
	@Override
	public Integer isDefaultTemplateNotPresent(Integer enterpriseId, String templateType, String source, Integer isLocationTemplate) {
		if (BooleanUtils.isFalse(DefaultTemplateUtils.validateDefaultTemplateGetOrExistRequest(enterpriseId, templateType, source)) || BooleanUtils.isTrue(CoreUtils.getBooleanValueFromInteger(isLocationTemplate))) {
			logger.info("isDefaultTemplateNotPresent :: Invalid request with enterprise id {}, template type {} and source {}", enterpriseId, templateType, source);
			return 0;
		}
		logger.info("isDefaultTemplateNotPresent :: Request received with enterprise id {}, template type {} and source {}", enterpriseId, templateType, source);
		BusinessTemplateEntity defaultTemplate = getDefaultTemplateByEnterpriseIdAndTypeAndSource(enterpriseId, templateType, source);
		if (defaultTemplate != null) {
			return 0;
		}
		return 1;
	}
	
	/**
	 * This function does the following :
	 * 1. Validate input
	 * 2. Stream template list, fetch default template for each type if present and collect them in a map
	 * 3. Update default template list in map
	 *
	 * 
	 * @param templateList,source,enterpriseId
	 */
	@Override
	public void updateDefaultTemplateCacheByAccountIdAndTypeAndSource(List<BusinessTemplateEntity> templateList, String source, Integer enterpriseId) {
		if (CollectionUtils.isEmpty(templateList) || StringUtils.isBlank(source) || enterpriseId == null) {
			logger.info("updateDefaultTemplateCacheByTypeAndSource :: template list, enterprise id, and source can't be empty");
			return;
		}
		Map<String, BusinessTemplateEntity> templateMapByType = templateList.stream().filter(e -> (DefaultTemplateUtils.validateDefaultTemplate(e.getIsDefaultTemplate(), e.getTemplateType(), source)))
				.collect(Collectors.toMap(e -> e.getTemplateType(), e -> e, (oldValue, newValue) -> oldValue));
		if (templateMapByType == null || CollectionUtils.sizeIsEmpty(templateMapByType)) {
			logger.info("updateDefaultTemplateCacheByTypeAndSource :: No default templates found for updation for enterprise id {} and source {}", enterpriseId, source);
			return;
		}
		logger.info("updateDefaultTemplateCacheByTypeAndSource :: default templates size found for updation for enterprise id {} and source {} is {}", enterpriseId, source,
				CollectionUtils.size(templateMapByType));
		templateMapByType.forEach((k, v) -> {
			cacheService.updateDefaultTemplateCacheIfNotPresent(enterpriseId, k, StringUtils.lowerCase(source),
					new BusinessTemplateEntity(v.getTemplateId(), StringUtils.lowerCase(v.getTemplateType()), v.getTemplateName(), v.getCreatedAt(), enterpriseId, v.getIsDefaultTemplate()));
		});
		return;
	}
	
	/**
	 * This function does the following :
	 * 1. Create default template dto with enterprise id and source
	 * 2. Set template Type for email and sms in default dto from input template type parameter
	 * 3. Validate Do
	 *
	 * 
	 * @param enterpriseId,
	 *            templateType, Source
	 */
	private DefaultTemplateDTO validateAndCreateDefaultTemplateDTO(Integer enterpriseId, String templateType, String source) {
		DefaultTemplateDTO defaultDTO = new DefaultTemplateDTO(enterpriseId, source);
		DefaultTemplateUtils.setTemplateTypeForGetDefaultTemplatesRequest(defaultDTO, templateType);
		if (BooleanUtils.isFalse(DefaultTemplateUtils.isValidGetDefaultTemplateRequest(defaultDTO))) {
			logger.warn("validateAndCreateDefaultTemplateDTO :: Invalid request :: {}", defaultDTO);
			return null;
		}
		return defaultDTO;
	}
	
	/**
	 * Get default email , sms template for given template type and enterprise id from db and update result in dto
	 *
	 * 
	 * @param DefaultTemplateDTO
	 */
	private void getEmailSmsDefaultTemplates(DefaultTemplateDTO defaultDTO) {
		logger.info("getEmailSmsDefaultTemplates :: Request received is {}", defaultDTO);
		if (StringUtils.containsIgnoreCase(defaultDTO.getSource(), Constants.EMAIL_TYPE)) {
			BusinessTemplateEntity defaultEmailTemplate = getDefaultTemplateByEnterpriseIdAndTypeAndSource(defaultDTO.getEnterpriseId(), defaultDTO.getEmailTemplateType(), Constants.EMAIL_TYPE);
			defaultDTO.setEmailTemplateId(defaultEmailTemplate != null ? defaultEmailTemplate.getTemplateId() : null);
		}
		
		if (StringUtils.containsIgnoreCase(defaultDTO.getSource(), Constants.SMS_TYPE)) {
			BusinessTemplateEntity defaultSmsTemplate = getDefaultTemplateByEnterpriseIdAndTypeAndSource(defaultDTO.getEnterpriseId(), defaultDTO.getSmsTemplateType(), Constants.SMS_TYPE);
			defaultDTO.setSmsTemplateId(defaultSmsTemplate != null ? defaultSmsTemplate.getTemplateId() : null);
		}
		
		DefaultTemplateUtils.validateAndUpdateRequestCompletionFlag(defaultDTO);
	}
	
	/**
	 * This function does the following :
	 * 1. Validate input
	 * 2. get default templates for desired request, email and sms
	 *
	 * 
	 * @param enterpriseId, templateType, source
	 */
	@Override
	public DefaultTemplateDTO getDefaultTemplates(Integer enterpriseId, String templateType, String source) {
		DefaultTemplateDTO defaultDTO = validateAndCreateDefaultTemplateDTO(enterpriseId, templateType, source);
		if (defaultDTO == null) {
			logger.warn("getDefaultTemplates :: Default dto is null for enterprise id {}, template type {} and source {}", enterpriseId, templateType, source);
			return null;
		}
		getEmailSmsDefaultTemplates(defaultDTO);
		return defaultDTO;
	}
	
	// Backup For Default Template Flow

	/**
	 * 
	 * Get default campaign for given enterprise, and check if campaign obtained is of type review_request and not deleted.
	 *
	 * 
	 * @param enterpriseId
	 */
	private Campaign getDefaultCampaign(DefaultTemplateDTO defaultDTO) {
		List<Campaign> campaign = null;
	    campaign = cacheService.getDefaultOngoingCampaignByEnterpriseId(defaultDTO.getEnterpriseId());
		
		if (CollectionUtils.isNotEmpty(campaign)) {
			Campaign defaultCampaign = DefaultTemplateUtils.fetchCampaignByType(campaign, defaultDTO);
			if (defaultCampaign == null) {
				logger.info("getDefaultCampaign :: Either default campaign is deleted or not of the required type for enterprise id {}", defaultDTO.getEnterpriseId());
				return null;
			}
			logger.info("getDefaultCampaign :: Returning campaign with id {}", defaultCampaign.getId());
			return defaultCampaign;
		}
		return null;
	}
	
	/**
	 *
	 * Fetch default campaign and update dto
	 *
	 * 
	 * @param DefaultTemplateDTO
	 */
	private void fetchDefaultTemplateFromDefaultCampaign(DefaultTemplateDTO defaultDTO) {
		// Fetch default campaign for enterprise id
		logger.info("fetchDefaultTemplateFromDefaultCampaign :: Fetching default campaign for enterprise id {}", defaultDTO.getEnterpriseId());
		Campaign campaign = getDefaultCampaign(defaultDTO);
		if (campaign == null) {
			logger.warn("fetchDefaultTemplateFromDefaultCampaign :: No campaign found for enterprise id {}", defaultDTO.getEnterpriseId());
			return;
		}
		
		// If email template id is required, set in dto
		if (DefaultTemplateUtils.isDefaultEmailTemplateRequired(defaultDTO)) {
			defaultDTO.setEmailTemplateId(campaign.getTemplateId());
		}
		
		// If sms template id is required, set in dto
		if (DefaultTemplateUtils.isDefaultSmsTemplateRequired(defaultDTO)) {
			defaultDTO.setSmsTemplateId(campaign.getSmsTemplateId());
		}
		
		// Update request type and completion flag in dto
		DefaultTemplateUtils.validateAndUpdateRequestCompletionFlag(defaultDTO);
	}
	
	/**
	 *
	 * Get sms templates for given account id
	 *
	 * 
	 * @param accountId,smsTemplates
	 */
	private CampaignCallable<Boolean> getSmsBusinessTemplateListForEnterpriseId(Integer accountId, List<BusinessTemplateEntity> smsTemplates) {
		return new CampaignCallable<Boolean>("Business Template List Fetch For Sms") {
			@Override
			public Boolean doCall() throws Exception {
				try {
					CachedCollectionWrapper<BusinessTemplateEntity> wrapper = businessSmsTemplateDao.getTemplatesListByEnterpriseId(accountId);
					if (wrapper != null && CollectionUtils.isNotEmpty(wrapper.getElementsList())) {
						smsTemplates.addAll(wrapper.getElementsList());
					}
				} catch (Exception e) {
					logger.error("Exception while getting sms templates list for account id {} is {}", accountId, ExceptionUtils.getStackTrace(e));
				}
				return true;
			}
		};
	}
	
	/**
	 *
	 * Get email templates for given account id
	 *
	 * 
	 * @param accountId,emailTemplates
	 */
	private CampaignCallable<Boolean> getEmailBusinessTemplateListForEnterpriseId(Integer accountId, List<BusinessTemplateEntity> emailTemplates) {
		return new CampaignCallable<Boolean>("Business Template List Fetch For Email") {
			@Override
			public Boolean doCall() throws Exception {
				try {
					CachedCollectionWrapper<BusinessTemplateEntity> wrapper = cacheService.getTemplatesListByEnterpriseId(accountId);
					if (wrapper != null && CollectionUtils.isNotEmpty(wrapper.getElementsList())) {
						emailTemplates.addAll(wrapper.getElementsList());
					}
				} catch (Exception e) {
					logger.error("Exception while getting email templates list for account id {} is {}", accountId, ExceptionUtils.getStackTrace(e));
				}
				return true;
			}
		};
	}
	
	/**
	 *
	 * Fetch email,sms templates from db for given enterprise id parallely
	 *
	 * 
	 * @param DefaultTemplateDTO,
	 *            emailTemplates, smsTemplates
	 * @throws Exception 
	 */
	private void fetchEmailAndSmsTemplateList(DefaultTemplateDTO defaultDTO, List<BusinessTemplateEntity> emailTemplates, List<BusinessTemplateEntity> smsTemplates) throws Exception {
		
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
		
		// get business email template list
		if (DefaultTemplateUtils.isDefaultEmailTemplateRequired(defaultDTO)) {
			executorService.submit(getEmailBusinessTemplateListForEnterpriseId(defaultDTO.getEnterpriseId(), emailTemplates));
		}
		
		// get business sms template list
		if (DefaultTemplateUtils.isDefaultSmsTemplateRequired(defaultDTO)) {
			executorService.submit(getSmsBusinessTemplateListForEnterpriseId(defaultDTO.getEnterpriseId(), smsTemplates));
		}
		
		try {
			logger.info("Executing fetch template list tasks for account id : {}", defaultDTO.getEnterpriseId());
			executorCommonService.executeTasks(executorService);
		} catch (Exception exception) {
			logger.error("Exception occurred while executing fetch template list tasks for account id : {} ,{}", defaultDTO.getEnterpriseId(), ExceptionUtils.getStackTrace(exception));
			throw exception;
		}
	}
	
	/**
	 *
	 * Get default templates for email, sms and given template type, and enterprise id, from db, if not found use backup options
	 *
	 * 
	 * @param enterpriseId,
	 *            emailTemplateType, smsTemplateType, source
	 */
	private void fetchAndUpdateDefaultTemplateInDtoFromTemplateList(DefaultTemplateDTO defaultDTO, List<BusinessTemplateEntity> emailTemplates, List<BusinessTemplateEntity> smsTemplates, Integer isMigrationEvent) {
		// filter email and sms template list by respective type and returned combined list
		Map<String, Integer> sourceToTemplateIdMap = new HashMap<>();
		List<BusinessTemplateEntity> templateList = DefaultTemplateUtils.prepareTemplateListAndMap(emailTemplates, smsTemplates, defaultDTO, sourceToTemplateIdMap);
		if (CollectionUtils.isNotEmpty(templateList)) {
			fetchAndSetMostRecentlyUsedTemplate(defaultDTO, templateList);
			
			// Backup Case, if most recently template not found set first template from list
			DefaultTemplateUtils.validateAndSetFirstTemplateAsDefault(defaultDTO, sourceToTemplateIdMap);
			
			// Remove _sms from template type, if present and is a migration event
			if (BooleanUtils.isTrue(CoreUtils.getBooleanValueFromInteger(isMigrationEvent))) {
				DefaultTemplateUtils.removeSmsFromTemplateType(templateList);
			}
			
			// Update request type and completion flag in dto
			DefaultTemplateUtils.validateAndUpdateRequestCompletionFlag(defaultDTO);
		}
	}
	
	/**
	 *
	 * Fetch email,sms templates from db for given enterprise id, filter and prepare template list and then use that template list to fetch most recently
	 * used template
	 * each for email and sms
	 *
	 * 
	 * @param DefaultTemplateDTO
	 */
	private void fetchDefaultTemplatesUsingTemplateListAndES(DefaultTemplateDTO defaultDTO) {
		
		List<BusinessTemplateEntity> emailTemplates = new ArrayList<>();
		List<BusinessTemplateEntity> smsTemplates = new ArrayList<>();
		
		// Fetch email and sms templates list
		try {
			fetchEmailAndSmsTemplateList(defaultDTO, emailTemplates, smsTemplates);
		} catch (Exception e) {
			logger.error("Exception occurred while executing fetch template list tasks for account id : {}", defaultDTO.getEnterpriseId());
			return;
		}
		
		fetchAndUpdateDefaultTemplateInDtoFromTemplateList(defaultDTO, emailTemplates, smsTemplates, ZERO);
	}

	/**
	 *
	 * Fetch most recently used template source wise from list of templates, and set in defaultDTO
	 *
	 * 
	 * @param DefaultTemplateDTO,templateList
	 */
	private void fetchAndSetMostRecentlyUsedTemplate(DefaultTemplateDTO defaultDTO, List<BusinessTemplateEntity> templateList) {
		// Fetch Most recently used template from ES each for email and sms
		String templateType = StringUtils.isNotBlank(defaultDTO.getEmailTemplateType()) ? defaultDTO.getEmailTemplateType() : defaultDTO.getSmsTemplateType();
		Map<String, Integer> sourceToMostRecentlyUsedTemplateIdMap = reportService.getMostRecentlyUsedTemplate(defaultDTO.getEnterpriseId(), templateList, templateType);
		
		// Set fetched most recently email and sms template id in dto
		if (sourceToMostRecentlyUsedTemplateIdMap.containsKey(Constants.EMAIL_TYPE)) {
			defaultDTO.setEmailTemplateId(sourceToMostRecentlyUsedTemplateIdMap.get(Constants.EMAIL_TYPE));
		}
		if (sourceToMostRecentlyUsedTemplateIdMap.containsKey(Constants.SMS_TYPE)) {
			defaultDTO.setSmsTemplateId(sourceToMostRecentlyUsedTemplateIdMap.get(Constants.SMS_TYPE));
		}
	}
	
	/**
	 *
	 * Send email or sms update default template event if applicable
	 *
	 * 
	 * @param DefaultTemplateDTO,
	 *            isEmailEventRequired, isSmsEventRequired
	 */
	private void sendDefaultTemplateUpdateEvent(DefaultTemplateDTO defaultDTO, Boolean isEmailEventRequired, Boolean isSmsEventRequired, Integer createDefaultTemplate) {
		// Send email event
		if (BooleanUtils.isTrue(isEmailEventRequired) && defaultDTO.getEmailTemplateId() != null) {
			logger.info("sendDefaultTemplateUpdateEvent :: Request received to send async default template event for enterprise id {} and email template id {}", defaultDTO.getEnterpriseId(),
					defaultDTO.getEmailTemplateId());
			kafkaService.pushMessageToKafkaWithKey(KafkaTopicTypeEnum.UPDATE_DEFAULT_TEMPLATE, new KafkaMessage(defaultDTO.getEnterpriseId(),
					new DefaultTemplateUpdateRequestDTO(null, defaultDTO.getEmailTemplateId(), defaultDTO.getEmailTemplateType(), Constants.EMAIL_TYPE, defaultDTO.getEnterpriseId(), createDefaultTemplate)));
		}
		
		// Send sms event
		if (BooleanUtils.isTrue(isSmsEventRequired) && defaultDTO.getSmsTemplateId() != null) {
			logger.info("sendDefaultTemplateUpdateEvent :: Request received to send async default template event for enterprise id {} and sms template id {}", defaultDTO.getEnterpriseId(),
					defaultDTO.getSmsTemplateId());
			kafkaService.pushMessageToKafkaWithKey(KafkaTopicTypeEnum.UPDATE_DEFAULT_TEMPLATE, new KafkaMessage(defaultDTO.getEnterpriseId(),
					new DefaultTemplateUpdateRequestDTO(null, defaultDTO.getSmsTemplateId(), defaultDTO.getSmsTemplateType(), Constants.SMS_TYPE, defaultDTO.getEnterpriseId())));
		}
	}
	
	/**
	 *
	 * Get default templates for email, sms and given template type, and enterprise id, from db, if not found use backup options
	 * 
	 * 
	 * @param enterpriseId,
	 *            emailTemplateType, smsTemplateType, source
	 */
	@Profiled
	@Override
	public DefaultTemplateDTO getDefaultTemplatesWithBackup(Integer enterpriseId, String templateType, String source, String callerAPI) {
		logger.info("getDefaultTemplatesWithBackup :: Request received for enterprise id {}, email template type {}, source {} and caller api {}", enterpriseId, templateType, source, callerAPI);
		
		// Create, set template types in DTO and validate request
		DefaultTemplateDTO defaultDTO = validateAndCreateDefaultTemplateDTO(enterpriseId, templateType, source);
		if (defaultDTO == null) {
			return null;
		}
		
		// First Try to fetch the Default templates from cache or db
		getEmailSmsDefaultTemplates(defaultDTO);
		if (BooleanUtils.isTrue(defaultDTO.getRequestComplete())) {
			logger.info("getDefaultTemplatesWithBackup :: Request completed at first stage, fetching templates from db {}", defaultDTO);
			return defaultDTO;
		}
		
		// Setting up variables, that will be used to identify update default template event needs to be sent or not
		Boolean isEmailEventRequired = DefaultTemplateUtils.isDefaultEmailTemplateRequired(defaultDTO);
		Boolean isSmsEventRequired = DefaultTemplateUtils.isDefaultSmsTemplateRequired(defaultDTO);
		logger.info("getDefaultTemplatesWithBackup :: Request status after first stage completion {}", defaultDTO);
		
		// First backup to check for default campaign
		if(DefaultTemplateUtils.isValidRequestForFetchingDefaultCampaign(defaultDTO)) {
			fetchDefaultTemplateFromDefaultCampaign(defaultDTO);
			if (BooleanUtils.isTrue(defaultDTO.getRequestComplete())) {
				logger.info("getDefaultTemplatesWithBackup :: Request completed at second stage, fetching default campaign from db {}", defaultDTO);
				sendDefaultTemplateUpdateEvent(defaultDTO, isEmailEventRequired, isSmsEventRequired, ZERO);
				return defaultDTO;
			}
			logger.info("getDefaultTemplatesWithBackup :: Request status after second stage completion {}", defaultDTO);
		}
		
		// Second backup, Fetch Most recently used templates from ES
		fetchDefaultTemplatesUsingTemplateListAndES(defaultDTO);
		if (BooleanUtils.isTrue(defaultDTO.getRequestComplete())) {
			logger.info("getDefaultTemplatesWithBackup :: Request completed at third stage, fetching most recently used template from es {}", defaultDTO);
			sendDefaultTemplateUpdateEvent(defaultDTO, isEmailEventRequired, isSmsEventRequired, ZERO);
			return defaultDTO;
		}
		
		// Special Backup For Getting deep link info(required default email template) in case of war and review_qr_code APIs
		if (StringUtils.equalsIgnoreCase(defaultDTO.getSource(), Constants.EMAIL_TYPE) && StringUtils.equalsAnyIgnoreCase(callerAPI, Constants.DEEPLINK_WAR, Constants.DEEPLINK_REVIEW_QR_CODE)) {
			defaultDTO.setEmailTemplateId(ZERO);
			logger.info("getDefaultTemplatesWithBackup :: Setting template id as 0 for request {}", defaultDTO);
			sendDefaultTemplateUpdateEvent(defaultDTO, isEmailEventRequired, isSmsEventRequired, ONE);
			return defaultDTO;
		}
		
		logger.warn("getDefaultTemplatesWithBackup :: Request was unable to complete {}", defaultDTO);
		// If even partial event got completed, need to update default template
		sendDefaultTemplateUpdateEvent(defaultDTO, isEmailEventRequired, isSmsEventRequired, ZERO);
		return defaultDTO;
	}
	
	// Migration API Flow

	/**
	 *
	 * Update default email and sms template in db
	 *
	 * 
	 * @param defaultDTO,
	 *            isEmailDefaultTemplateRequired, isSmsDefaultTemplateRequired
	 */
	@Override
	public void validateAndUpdateEmailAndSmsDefaultTemplateInDb(DefaultTemplateDTO defaultDTO, Boolean isEmailDefaultTemplateRequired, Boolean isSmsDefaultTemplateRequired) {
		if (BooleanUtils.isTrue(isSmsDefaultTemplateRequired) && defaultDTO.getSmsTemplateId() != null) {
			Integer defaultTemplateUpdate = updateDefaultStatusForSmsTemplate(defaultDTO.getSmsTemplateId(), ONE);
			logger.info("updateEmailAndSmsTemplateIdDefaultStatus :: Default sms template status update for template id {} is {}", defaultDTO.getSmsTemplateId(), defaultTemplateUpdate);
			if (defaultTemplateUpdate != ONE) {
				defaultDTO.setSmsTemplateId(null);
			}
		}
		
		if (BooleanUtils.isTrue(isEmailDefaultTemplateRequired) && defaultDTO.getEmailTemplateId() != null) {
			Integer defaultTemplateUpdate = updateDefaultStatusForEmailTemplate(defaultDTO.getEmailTemplateId(), ONE);
			logger.info("updateEmailAndSmsTemplateIdDefaultStatus :: Default email template status update for template id {} is {}", defaultDTO.getEmailTemplateId(), defaultTemplateUpdate);
			if (defaultTemplateUpdate != ONE) {
				defaultDTO.setEmailTemplateId(null);
			}
		}
	}
	
	/**
	 *
	 * Migration Helper Api, To fetch default templates from es or template list
	 * 
	 * @param defaultDTO,
	 *            emailTemplates, smsTemplates, eventType, isOneTimeMigrationRequest
	 */
	@Override
	public void migrationHelperApi(DefaultTemplateDTO defaultDTO, List<BusinessTemplateEntity> emailTemplates, List<BusinessTemplateEntity> smsTemplates, String eventType,
			Integer isOneTimeMigrationRequest) {
		if (StringUtils.equalsIgnoreCase(eventType, Constants.DT_MIGRATION_MOST_RECENT_TEMPLATE_FETCH)) {
			fetchAndUpdateDefaultTemplateInDtoFromTemplateList(defaultDTO, emailTemplates, smsTemplates, ONE);
		}
	}
	
}
