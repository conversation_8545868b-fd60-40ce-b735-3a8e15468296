/**
 * @file_name BusinessDeeplinkURLGenerationService.java
 * @created_date 20 Oct 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.service;

import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.ReviewRequestDto;
import com.birdeye.campaign.entity.Promotion;
import com.birdeye.campaign.entity.ReviewRequest;

/**
 * @file_name BusinessDeeplinkURLGenerationService.java
 * @created_date 20 Oct 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public interface BusinessDeeplinkURLGenerationService {

	/**
	 * @param business
	 * @param reviewRequest
	 * @return
	 * @throws Exception
	 */
	String getDeepLinkForSMS(BusinessEnterpriseEntity business, ReviewRequest reviewRequest) throws Exception;

	String getDeepLinkForSMSWithException(BusinessEnterpriseEntity business, ReviewRequest reviewRequest) throws Exception;

	/**
	 * @param business
	 * @param reviewRequest
	 * @return
	 * @throws Exception
	 */
	String getReferralDeepLinkForSMS(BusinessEnterpriseEntity business, ReviewRequest reviewRequest) throws Exception;

	/**
	 * @param reviewRequest
	 * @param business
	 * @return
	 * @throws Exception
	 */
	String getSurveyLinkUrl(ReviewRequest reviewRequest, BusinessEnterpriseEntity business) throws Exception;

	/**
	 * @param reviewRequestId
	 * @param customerId
	 * @return
	 */
	String getEncryptionKey(Long reviewRequestId, Integer customerId);

	/**
	 * @param customerId
	 * @param reviewRequestId
	 * @return
	 */
	String getEncryptionIv(Integer customerId, Long reviewRequestId);

	String getSurveyLinkUrlWithException(ReviewRequest reviewRequest, BusinessEnterpriseEntity business) throws Exception;

	/**
	 * @param business
	 * @param reviewRequest
	 * @return
	 */
	String getMobileReferralDeepLinkUrl(BusinessEnterpriseEntity business, ReviewRequest reviewRequest);

	/**
	 * @param business
	 * @param reviewRequest
	 * @param sid
	 * @return
	 */
	String getLeadFormRedirectionBaseURL(BusinessEnterpriseEntity business, ReviewRequestDto reviewRequest, Integer sid, String referralCode);

	/**
	 * @param reviewRequestId
	 * @param customerId
	 * @return
	 */
	String getEncryptedCustomerId(Long reviewRequestId, Integer customerId);

	/**
	 * method to generate appointment details URL - cancel or reschedule button
	 * 
	 * demo URL - https://devpublicforms1.birdeye.com/{accountId}/view-appointment/details/{appointmentId}
	 * 
	 * @param business
	 * @param context
	 * @return
	 */
	String getAppointmentReminderDeeplinkForSMS(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, String encodedAppointmentId);

	String getAppointmentRecallBookingURLForSMS(BusinessEnterpriseEntity business, Long smbOrEnterpriseBusinessNumber, ReviewRequest reviewRequest, String encryptedAppointmentId,
			String widgetId, Integer appointmentSchedulingEnabled);

	/**
	 * method to generate promotional SMS URL - book appointment button
	 * 
	 * 
	 * @param business
	 * @param reviewRequest
	 * @param encryptedaAppointmentId
	 *            ,widgetId ,recallId
	 * @return String
	 */
	String getAppointmentBookingURLForPromotionalSMS(BusinessEnterpriseEntity business, Long smbOrEnterpriseBusinessNumber, Promotion promotion,
			String encryptedAppointmentId, String widgetId, Integer appointmentSchedulingEnabled);

	String getReferralDeepLinkForSMSWithException(BusinessEnterpriseEntity business, ReviewRequest reviewRequest);

}
