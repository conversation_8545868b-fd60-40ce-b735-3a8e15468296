package com.birdeye.campaign.service.impl;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.cache.AggregationSourceCache;
import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.entity.DeeplinkTracking;
import com.birdeye.campaign.entity.Promotion;
import com.birdeye.campaign.entity.PromotionRequestsLog;
import com.birdeye.campaign.entity.ReviewGenerationSource;
import com.birdeye.campaign.entity.ReviewRequest;
import com.birdeye.campaign.entity.ReviewRequestsLog;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.external.service.IContactExternalService;
import com.birdeye.campaign.kafka.service.KafkaService;
import com.birdeye.campaign.platform.constant.ClickTrackRequestTypeEnum;
import com.birdeye.campaign.platform.constant.CommunicationAcitivityEventEnum;
import com.birdeye.campaign.platform.constant.CustomerCategoryEnum;
import com.birdeye.campaign.platform.entity.AggregationSource;
import com.birdeye.campaign.platform.readonly.repository.AggregationSourceReadOnlyRepository;
import com.birdeye.campaign.platform.readonly.repository.BusinessReadOnlyRepo;
import com.birdeye.campaign.repository.DeeplinkTrackingRepository;
import com.birdeye.campaign.repository.PromotionRepo;
import com.birdeye.campaign.repository.PromotionRequestsLogRepo;
import com.birdeye.campaign.repository.ReviewRequestLogRepo;
import com.birdeye.campaign.repository.ReviewRequestRepo;
import com.birdeye.campaign.request.TrackClicksRequest;
import com.birdeye.campaign.response.kontacto.KontactoDTO;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.CommunicationActivityService;
import com.birdeye.campaign.service.DataTrackingElasticService;
import com.birdeye.campaign.service.DataTrackingService;
import com.birdeye.campaign.utils.CustomerUtils;
import com.birdeye.dto.reviewgen.ReviewSource;

@Service("dataTrackingService")
public class DataTrackingServiceImpl implements DataTrackingService {
	
	private static final Logger	logger			= LoggerFactory.getLogger(DataTrackingServiceImpl.class);
	
	@Autowired
	AggregationSourceReadOnlyRepository	aggregationSourceReadOnlyRepo;
	
	@Autowired
	DeeplinkTrackingRepository	deeplinkTrackingRepo;
	
	@Autowired
	BusinessReadOnlyRepo				businessReadOnlyRepo;
	
	@Autowired
	KafkaService				kafkaService;
	
	@Autowired
	ReviewRequestLogRepo		rrLogRepo;
	
	@Autowired
	PromotionRequestsLogRepo		prLogRepo;
	
	@Autowired
	private IContactExternalService			contactExternalService;
	
	@Autowired
	ReviewRequestRepo			rrRepo;
	
	@Autowired
	private PromotionRepo		promotionRepo;
	
	@Autowired
	DataTrackingElasticService	dataTrackingElasticService;
	
	@Autowired
	private CommunicationActivityService communicationActivityService;
	
	@Autowired
	private CacheService					cacheService;
	
	@Override
	public void trackClickEvent(TrackClicksRequest request) {
		BusinessEnterpriseEntity business = getBusinessFromBusinessNumber(request.getBusinessNumber(), request.getBusinessId());
		
		ClickTrackRequestTypeEnum requestType = ClickTrackRequestTypeEnum.getEnum(request.getRequestType());
		ReviewSource reviewSource = null;
		if (request.getSourceId() != null) {
			reviewSource = getReviewSourceById(request.getSourceId());
			if (reviewSource == null) {
				// For non-birdeye sources, clicks are not yet supported.
				logger.warn("Source not found for click tracking, sourceId: {} type: {} and request id: {} ", request.getSourceId(), request.getRequestType(),
						request.getRequestId());
				return;
			}
		}
		
		switch (requestType) {
			case CX:
				handleCXClickEvent(request, business, reviewSource);
				break;
			
			case REVIEW:
			case REVIEW_REQUEST:
			case REVIEW_REQUEST_NEW:
				handleRRClickEvent(request, business, reviewSource);
				break;
			
			case SURVEY:
				handleSurveyClickEvent(request, business, reviewSource);
				break;
			
			case PROMOTION:
				handlePromotionClickEvent(request, business);
				break;
			
			case DEEPLINK:
				handleDeeplinkClickEvent(request, business, reviewSource);
				break;
			
			case APPOINTMENT_REMINDER:
				handleAppointmentClickEvent(request, business);
				break;
			
			case APPOINTMENT_RECALL:
				handleAppointmentRecallClickEvent(request);
				break;
				
			case APPOINTMENT_FORM:
				handleAppointmentFormClickEvent(request, business);
				break;
			
			default:
				logger.error("trackClickEvent : invalid request type {}", requestType);
		}
	}
	
	private void handleAppointmentClickEvent(TrackClicksRequest request, BusinessEnterpriseEntity business) {
		ReviewRequest reviewRequest = getReviewRequestById(request.getRequestId());
		ReviewRequestsLog rrLog = new ReviewRequestsLog();
		populateClickMetaData(request, business, rrLog);
		rrLogRepo.saveAndFlush(rrLog);
		dataTrackingElasticService.publishAppointmentClickEvent(rrLog, request, reviewRequest);
		
	}
	
	private void handleAppointmentRecallClickEvent(TrackClicksRequest request) {
		ReviewRequest reviewRequest = getReviewRequestById(request.getRequestId());
		ReviewRequestsLog rrLog = null;
		rrLog = new ReviewRequestsLog();
		populateClickMetaDataRecall(request, rrLog, reviewRequest);
		rrLogRepo.saveAndFlush(rrLog);
		dataTrackingElasticService.publishAppointmentRecallClickEvent(rrLog, request, reviewRequest);		
	}

	private void handleRRClickEvent(TrackClicksRequest request, BusinessEnterpriseEntity business, ReviewSource reviewSource) {
		// this is because the deeplink tracking request send "review_request" as rtype with request id = 2
		if (request.getRequestId() != null && request.getRequestId() == 2) {
			handleDeeplinkClickEvent(request, business, reviewSource);
			return;
		}
		
		ReviewRequest reviewRequest = getReviewRequestById(request.getRequestId());
		KontactoDTO customer = getCustomerFromId(reviewRequest.getCustId());
		ReviewRequestsLog rrLog = new ReviewRequestsLog();
		populateClickMetaData(request, business, rrLog);
		rrLog.setRecommended(request.getRecommended());
		// case for Birdeye Source Click in RR
		if (request.getRecommended() != null && request.getRecommended() == 0) {
			reviewSource = getReviewSourceById(100);
			rrLog.setSourceId(100);
		}
		rrLogRepo.saveAndFlush(rrLog);
		dataTrackingElasticService.publishRRClickEvent(request, business, reviewSource, reviewRequest, customer, rrLog);
	}
	
	private void handleSurveyClickEvent(TrackClicksRequest request, BusinessEnterpriseEntity business, ReviewSource reviewSource) {
		ReviewRequest reviewRequest = getReviewRequestById(request.getRequestId());
		KontactoDTO customer = getCustomerFromId(reviewRequest.getCustId());
		ReviewRequestsLog rrLog = new ReviewRequestsLog();
		populateClickMetaData(request, business, rrLog);
		rrLogRepo.saveAndFlush(rrLog);
		dataTrackingElasticService.publishSurveyClickEvent(request, business, reviewSource, reviewRequest, customer, rrLog);
		
	}
	
	private void populateClickMetaData(TrackClicksRequest request, BusinessEnterpriseEntity business, ReviewRequestsLog rrLog) {
		rrLog.setClickedAt(new Date());
		rrLog.setSourceId(request.getSourceId());
		rrLog.setClickType(request.getClickType());
		rrLog.setBusinessId(business.getId());
		rrLog.setReviewRequestId(request.getRequestId());
		rrLog.setClickedOn(StringUtils.isNotBlank(request.getSource()) ? request.getSource() : "web");
		rrLog.setOs(request.getOs());
		rrLog.setEventId(request.getEventId());
		rrLog.setSentimentCheckType(request.getSentimentCheckType());
	}

	/**
	 * 
	 * @param clickType
	 * @param source
	 * 
	 */
	private boolean checkValidClickTypeForRecall(Integer clickType, String source) {
		return ((clickType == 1 && Constants.TEMPLATE_BASE_TYPE_SMS.equalsIgnoreCase(source)) || clickType == 2 || clickType == 16);
	}
	
	private void populateClickMetaDataRecall(TrackClicksRequest request, ReviewRequestsLog rrLog, ReviewRequest reviewRequest) {
		rrLog.setClickedAt(new Date());
		rrLog.setSourceId(request.getSourceId());
		rrLog.setClickType(request.getClickType());
		if (Constants.TEMPLATE_BASE_TYPE_EMAIL.equalsIgnoreCase(reviewRequest.getSource()) && request.getClickType() != null && request.getClickType() == 1) {
			rrLog.setEvent("open");
		} else if (request.getClickType() != null && checkValidClickTypeForRecall(request.getClickType(), reviewRequest.getSource())) {
			rrLog.setEvent("click");
		}
		rrLog.setBusinessId(reviewRequest.getBusinessId());
		rrLog.setReviewRequestId(request.getRequestId());
		rrLog.setClickedOn(StringUtils.isNotBlank(request.getSource()) ? request.getSource() : "web");
		rrLog.setOs(request.getOs());
		rrLog.setEventId(request.getEventId());
		rrLog.setSentimentCheckType(request.getSentimentCheckType());
	}
	
	private void populateClickMetaDataPromotion(TrackClicksRequest request, BusinessEnterpriseEntity business, PromotionRequestsLog prLog, Promotion promotionRequest) {
		prLog.setClickedAt(new Date());
		prLog.setSourceId(request.getSourceId());
		prLog.setClickType(request.getClickType());
		if (Constants.TEMPLATE_BASE_TYPE_SMS.equalsIgnoreCase(promotionRequest.getSource()) && request.getClickType() != null && request.getClickType() == 1) {
			prLog.setEvent("click");
		} else {
			prLog.setEvent("open");
		}
		prLog.setBusinessId(business.getId());
		prLog.setPromotionRequestId(request.getRequestId());
		prLog.setClickedOn(StringUtils.isNotBlank(request.getSource()) ? request.getSource() : "web");
		prLog.setOs(request.getOs());
		prLog.setEventId(request.getEventId());
	}
	
	private void handleCXClickEvent(TrackClicksRequest request, BusinessEnterpriseEntity business, ReviewSource reviewSource) {
		ReviewRequest reviewRequest = getReviewRequestById(request.getRequestId());
		KontactoDTO customer = getCustomerFromId(reviewRequest.getCustId());
		ReviewRequestsLog rrLog = new ReviewRequestsLog();
		populateClickMetaData(request, business, rrLog);
		if (request.getRating() != null)
			rrLog.setRating(getCXRating(request.getRating(), request.getSentimentCheckType()));
			
		// recomm is set for CX only when rating is null and recommended is not null (the only such case is Yes-No(Sentiment) sentiment check type where
		// recomm could be 1 or 0 or -1). In case of Birdeye source click for CX, recomm is null. Hence this is the only case.
		if (request.getRating() == null && request.getRecommended() != null) {
			rrLog.setRecommended(request.getRecommended());
		}
		
		// case for Birdeye Source Click in CX
		if (request.getClickType() == 3 && request.getRecommended() == null && request.getRating() == null) {
			reviewSource = getReviewSourceById(100);
			rrLog.setSourceId(100);
		}
		rrLogRepo.saveAndFlush(rrLog);
		
		Integer customerCategory = null;
		// add customer category in case of CX sentiment click
		if (request.getClickType() == 3 && (request.getRating() != null || request.getRecommended() != null)) {
			customerCategory = getCustomerCategory(request);
		}
		dataTrackingElasticService.publishCxClickEvent(request, business, reviewSource, reviewRequest, customer, rrLog, customerCategory);
	}

	private Integer getCustomerCategory(TrackClicksRequest request) {
		CustomerCategoryEnum customerCategoryEnum = CustomerUtils.getCustomerCategory(request.getRecommended(), request.getRating(), request.getSentimentCheckType());
		return customerCategoryEnum != null? customerCategoryEnum.getCustomerCategory() : null;
	}

	private void handlePromotionClickEvent(TrackClicksRequest request, BusinessEnterpriseEntity business) {
		
		Promotion promotionRequest = getPromotionRequestById(request.getRequestId());
		PromotionRequestsLog prLog = new PromotionRequestsLog();
		populateClickMetaDataPromotion(request, business, prLog,promotionRequest);
		prLogRepo.saveAndFlush(prLog);
		publishPromotionalCampaignClickEvent(request, business, promotionRequest, prLog);
//		 dataTrackingElasticService.pushClickDataToCustomerActivityIndex(prLog, aggregationSource, promotionRequest, business); NOSONAR
	}

	private void publishPromotionalCampaignClickEvent(TrackClicksRequest request, BusinessEnterpriseEntity business, Promotion promotionRequest,
			PromotionRequestsLog prLog) {
		if (CommunicationAcitivityEventEnum.OPENED.getType().contains(prLog.getEvent())) {
			communicationActivityService.publishPromotionCommunicationAcitivity(CommunicationAcitivityEventEnum.OPENED,  promotionRequest, prLog);
		} else if (CommunicationAcitivityEventEnum.CLICKED.getType().contains(prLog.getEvent())) {
			communicationActivityService.publishPromotionCommunicationAcitivity(CommunicationAcitivityEventEnum.CLICKED,  promotionRequest, prLog);
		}
		dataTrackingElasticService.pushPromotionClickEventToElastic(request, business, promotionRequest);
	}
	

	private KontactoDTO getCustomerFromId(Integer customerId) {
		KontactoDTO customer = contactExternalService.getCustomerById(customerId);
		if (customer == null) {
			logger.error("track click event - invalid customer id {}", customerId);
			throw new CampaignException(ErrorCodes.INVALID_REQUEST);
		}
		return customer;
	}
	
	private ReviewRequest getReviewRequestById(Long requestId) {
		ReviewRequest rr = rrRepo.getById(requestId);
		if (rr == null) {
			logger.error("track click event - invalid request id {}", requestId);
			throw new CampaignException(ErrorCodes.INVALID_REQUEST);
		}
		
		return rr;
	}
	
	private Promotion getPromotionRequestById(Long requestId) {
		Promotion rr = promotionRepo.findFirstById(requestId);
		if (rr == null) {
			logger.error("track click event - invalid request id {}", requestId);
			throw new CampaignException(ErrorCodes.INVALID_REQUEST);
		}
		
		return rr;
	}
	
	private void handleDeeplinkClickEvent(TrackClicksRequest request, BusinessEnterpriseEntity business, ReviewSource reviewSource) {
		DeeplinkTracking deeplinkTracking = new DeeplinkTracking();
		deeplinkTracking.setIp(request.getClientIp());
		deeplinkTracking.setBusinessId(business.getId());
		if (request.getReviewDate() != null) {
			deeplinkTracking.setClickedAt(new Date(request.getReviewDate()));
		} else {
			deeplinkTracking.setClickedAt(new Date());
		}
		if (request.getClickType() != null && request.getClickType() == 1) {
			deeplinkTracking.setEvent("open");
		} else {
			deeplinkTracking.setEvent("click");
		}
		
		if (request.getClickType() != null)
			deeplinkTracking.setClickType(request.getClickType());
		
		if (StringUtils.isNotEmpty(request.getEventId()))
			deeplinkTracking.setEventId(request.getEventId());
		
		if (request.getRecommended() != null)
			deeplinkTracking.setRecommended(request.getRecommended());
		
		if (StringUtils.isNotEmpty(request.getSentimentCheckType()))
			deeplinkTracking.setSentimentCheckType(request.getSentimentCheckType());
		
		if (StringUtils.isNotEmpty(request.getSource()))
			deeplinkTracking.setClickedOn(request.getSource());
		
		if (StringUtils.isNotEmpty(request.getOs()))
			deeplinkTracking.setOs(request.getOs());
		
		if (request.getRating() != null)
			deeplinkTracking.setRating(request.getRating());
		
		if (request.getSourceId() != null) {
			deeplinkTracking.setSourceId(request.getSourceId());
		}
		if (request.getReviewId() != null) {
			deeplinkTracking.setReviewId(request.getReviewId());
		}
		
		deeplinkTrackingRepo.saveAndFlush(deeplinkTracking);
		
		// update details on ES
		dataTrackingElasticService.pushDeepLinkTrackingToES(business, deeplinkTracking, reviewSource);
		
	}
	
	private BusinessEnterpriseEntity getBusinessFromBusinessNumber(Long businessNumber, Integer businessId) {
		BusinessEnterpriseEntity business = null;
		if(businessNumber != null) {
			business = businessReadOnlyRepo.getBusinessByBusinessLongId(businessNumber);
		} else {
			business = cacheService.getBusinessById(businessId);
		}
		
		if (business == null) {
			logger.warn("Invalid business {} or {}", businessNumber, businessId);
//			throw new CampaignException(ErrorCodes.INVALID_BUSINESS);
		}
		return business;
	}
	
	private Integer getCXRating(Integer rating, String type) {
		if ("nps".equalsIgnoreCase(type)) {
			if (rating < 0) {
				return 0;
			} else if (rating > 10) {
				return 10;
			} else {
				return rating;
			}
		}
		if ("star".equalsIgnoreCase(type)) {
			if (rating < 1) {
				return 1;
			} else if (rating > 5) {
				return 5;
			} else {
				return rating;
			}
		}
		if ("sentiment".equalsIgnoreCase(type)) {
			if (rating < -1) {
				return -1;
			} else if (rating > 1) {
				return 1;
			} else {
				return rating;
			}
		}
		return rating;
	}
	
//	private AggregationSource getAggregationSourceById(Integer sourceId) {
//		return CacheManager.getInstance().getCache(AggregationSourceCache.class).getAggregationSource(sourceId);
//	}
	
	// BIRDEYE-111100 | Added support for Review Gen sources in click tracking and reporting.
	private ReviewSource getReviewSourceById(Integer sourceId) {
		AggregationSource aggregationSource = CacheManager.getInstance().getCache(AggregationSourceCache.class).getAggregationSource(sourceId);
		if (aggregationSource != null) {
			return new ReviewSource.Builder(aggregationSource.getId(), aggregationSource.getName()).build();
		}
		ReviewGenerationSource reviewGenSource = cacheService.getReviewGenerationSource(sourceId);
		if (reviewGenSource != null) {
			return new ReviewSource.Builder(reviewGenSource.getId(), reviewGenSource.getName()).build();
		}
		return null;
	}
	
	private void handleAppointmentFormClickEvent(TrackClicksRequest request, BusinessEnterpriseEntity business) {
		ReviewRequest reviewRequest = getReviewRequestById(request.getRequestId());
		ReviewRequestsLog rrLog = new ReviewRequestsLog();
		populateClickMetaData(request, business, rrLog);
		rrLogRepo.saveAndFlush(rrLog);
		
		dataTrackingElasticService.publishAppointmentFormClickEvent(rrLog, request, business, reviewRequest);
		
	}

}
