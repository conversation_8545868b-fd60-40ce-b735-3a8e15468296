package com.birdeye.campaign.service.impl;

import java.io.Serializable;
import java.net.URLEncoder;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.commons.text.TextStringBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.birdeye.campaign.service.TemplateHelperService;
import com.birdeye.campaign.appointment.service.AppointmentService;
import com.birdeye.campaign.audit.service.AppointmentReminderAuditService;
import com.birdeye.campaign.business.service.BusinessService;
import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.constant.KafkaTopicTypeEnum;
import com.birdeye.campaign.constant.ReviewRequestTypeEnum;
import com.birdeye.campaign.constant.TemplateStaticTokens;
import com.birdeye.campaign.dto.AccountMessage;
import com.birdeye.campaign.dto.AppointmentInfoLiteDTO;
import com.birdeye.campaign.dto.BrandingInfo;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.BusinessFilterDto;
import com.birdeye.campaign.dto.BusinessProfileResponse;
import com.birdeye.campaign.dto.CampaignSMSProperties;
import com.birdeye.campaign.dto.KafkaMessage;
import com.birdeye.campaign.dto.MessengerSmsEntity;
import com.birdeye.campaign.dto.SmsDto;
import com.birdeye.campaign.entity.Appointment;
import com.birdeye.campaign.entity.AppointmentRRMapping;
import com.birdeye.campaign.entity.BusinessSmsTemplate;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.CampaignCustomFieldAssociation;
import com.birdeye.campaign.entity.CustomCampaignUrl;
import com.birdeye.campaign.entity.MessengerCampaign;
import com.birdeye.campaign.entity.Promotion;
import com.birdeye.campaign.entity.ReviewRequest;
import com.birdeye.campaign.enums.AppointmentStatusEnum;
import com.birdeye.campaign.enums.CustomFieldSourceEnum;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.exception.SmsUrlShortenException;
import com.birdeye.campaign.external.cache.service.CacheExternalService;
import com.birdeye.campaign.external.service.BusinessExternalService;
import com.birdeye.campaign.external.service.MessengerExternalService;
import com.birdeye.campaign.external.service.SurveyExternalService;
import com.birdeye.campaign.external.utils.AppointmentExternalUtils;
import com.birdeye.campaign.external.utils.BusinessExternalUtils;
import com.birdeye.campaign.external.utils.KontactoExternalUtils;
import com.birdeye.campaign.kafka.service.KafkaService;
import com.birdeye.campaign.platform.constant.CampaignTriggerTypeEnum;
import com.birdeye.campaign.platform.constant.CampaignTypeEnum;
import com.birdeye.campaign.platform.constant.CustomFieldAssociatedObjectTypeEnum;
import com.birdeye.campaign.platform.constant.TemplateTypeEnum;
import com.birdeye.campaign.platform.entity.BaseCommunicationEntity;
import com.birdeye.campaign.platform.entity.Location;
import com.birdeye.campaign.platform.entity.User;
import com.birdeye.campaign.platform.repository.BusinessSmsRepo;
import com.birdeye.campaign.platform.repository.LocationRepo;
import com.birdeye.campaign.repository.CampaignCustomFieldAssociationRepo;
import com.birdeye.campaign.repository.PromotionRepo;
import com.birdeye.campaign.request.MessengerMediaInfo;
import com.birdeye.campaign.response.LocationCustomField;
import com.birdeye.campaign.response.ReferralAppointmentTemplateDetails;
import com.birdeye.campaign.response.external.AppointmentDetailsResponse;
import com.birdeye.campaign.response.external.AppointmentDetailsResponse.AppointmentConfig;
import com.birdeye.campaign.response.external.AppointmentRecallResponse;
import com.birdeye.campaign.response.external.BusinessOptionsResponse;
import com.birdeye.campaign.response.external.GetBusinessLiteResponse;
import com.birdeye.campaign.response.external.SurveyInfoResponse;
import com.birdeye.campaign.response.kontacto.KontactoDTO;
import com.birdeye.campaign.service.AppointmentFormService;
import com.birdeye.campaign.service.BusinessDeeplinkService;
import com.birdeye.campaign.service.BusinessDeeplinkURLGenerationService;
import com.birdeye.campaign.service.BusinessOptionService;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.CampaignEmailHtmlService;
import com.birdeye.campaign.service.CampaignRescheduleService;
import com.birdeye.campaign.service.CampaignSetupCachingService;
import com.birdeye.campaign.service.CampaignSmsService;
import com.birdeye.campaign.service.CustomCampaignCachedService;
import com.birdeye.campaign.service.CustomCampaignService;
import com.birdeye.campaign.service.FreeTrialService;
import com.birdeye.campaign.service.MessengerCampaignService;
import com.birdeye.campaign.service.ReviewRequestService;
import com.birdeye.campaign.service.SmsService;
import com.birdeye.campaign.utils.AppointmentRecallUtils;
import com.birdeye.campaign.utils.AppointmentReminderUtils;
import com.birdeye.campaign.utils.BusinessBrandingUtils;
import com.birdeye.campaign.utils.CampaignExceptionUtils;
import com.birdeye.campaign.utils.CampaignUtils;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.DateTimeUtils;
import com.birdeye.campaign.utils.EncryptionUtils;
import com.birdeye.campaign.utils.FreeTrialProductUtils;
import com.birdeye.campaign.utils.PhoneNoValidator;
import com.birdeye.campaign.utils.TwilioAPIUtils;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service("campaignSmsService")
public class CampaignSmsServiceImpl implements CampaignSmsService {

	private static final String NPS = "nps";

	private static final String SURVEY_TYPE = "surveyType";

	private static final Logger logger = LoggerFactory.getLogger(CampaignSmsServiceImpl.class);

	@Autowired
	private CustomCampaignService customCampaignService;

	@Autowired
	private CacheService cacheService;

	@Autowired
	private BusinessDeeplinkService businessDeeplinkService;

	@Autowired
	CampaignEmailHtmlService campaignEmailHtmlService;

	@Autowired
	private CampaignSetupCachingService campaignSetupCachingService;

	@Autowired
	private BusinessOptionService businessOptionService;

	@Autowired
	private BusinessExternalService businessExternalService;

	@Autowired
	private MessengerCampaignService messengerCampaignService;

	@Autowired
	private BusinessSmsRepo businessSmsRepo;

	@Autowired
	private LocationRepo locationRepo;

	@Autowired
	private SmsService smsService;

	@Autowired
	private KafkaService kafkaService;

	@Autowired
	private ReviewRequestService reviewRequestService;

	@Autowired
	private BusinessDeeplinkURLGenerationService deeplinkURLGenerationService;

	@Autowired
	private PromotionRepo promotionRepo;

	@Autowired
	private SurveyExternalService surveyService;
	
	@Autowired
	private MessengerExternalService messengerExternalService;
	
	@Autowired
	private CampaignCustomFieldAssociationRepo customFieldAssociationRepo;
	
	@Autowired
	private AppointmentReminderAuditService appointmentAuditService;
	
	@Autowired
	private AppointmentService			appointmentService;
	
	@Autowired
	private BusinessService							businessService;
	
	@Autowired
	private AppointmentReminderAuditService appointmentReminderAuditService;
	
	@Autowired
	private CacheExternalService cacheExternalService;
	
	@Autowired
	private AppointmentFormService appointmentFormService;
	
	@Autowired
	private CustomCampaignCachedService				customCampaignCachedService;
	
	@Autowired
	private CampaignRescheduleService				campaignRescheduleService;
	
	@Autowired
	private FreeTrialService	freeTrialService;
	
	@Autowired
	private TemplateHelperService templateHelperService;
	
	@Override
	public void sendRRSmsCampaign(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, KontactoDTO customer, BusinessSmsTemplate smsTemplate, boolean applyDnd, boolean quickSend,
			Campaign campaign, boolean retryOnShortenURlError) throws Exception {
		String formattedMsg = null;
		String surveyType = null;
		Integer questionId = null;
		
		// appending first question and suffix to template message in case of pulse survey.
		Map<String, Object> npsSurveyResponse = getNpsSurveyData(reviewRequest, business);
		if (MapUtils.isNotEmpty(npsSurveyResponse)) {
			ObjectMapper mapper = new ObjectMapper();
			SurveyInfoResponse surveyInfoResponse = mapper.convertValue(npsSurveyResponse, SurveyInfoResponse.class);
			if (surveyInfoResponse != null && CollectionUtils.isNotEmpty(surveyInfoResponse.getPages()) && CollectionUtils.isNotEmpty(surveyInfoResponse.getPages().get(0).getQuestions())) {
				String templateMessage = getRRSmsCampaignMessage(business, reviewRequest, customer, smsTemplate, NPS, campaign, retryOnShortenURlError, quickSend);
				//BIRD-116893 | Handling unsubscribe text if Text Category Flag is enabled.
				templateHelperService.processUnsubscribeText(business.getEnterpriseIdElseBusinessId(), smsTemplate);
				
				formattedMsg = StringUtils.join(templateMessage, CampaignUtils.getNewLineByCount(2), String.valueOf(surveyInfoResponse.getPages().get(0).getQuestions().get(0).get("title")),
						StringUtils.SPACE, String.valueOf(surveyInfoResponse.getPages().get(0).getQuestions().get(0).get("pulseSuffix")),
						CampaignUtils.getUnsubscribeTextFromSmsTemplate(smsTemplate, KontactoExternalUtils.getCustomFieldValue(customer, Constants.LOCALE_CUSTOM_FIELD)));

				surveyType = String.valueOf(npsSurveyResponse.get(SURVEY_TYPE));
				questionId = (Integer) surveyInfoResponse.getPages().get(0).getQuestions().get(0).get("id");
			}
		} else {
			
			//BIRD-116893 | Handling unsubscribe text if Text Category Flag is enabled.
			templateHelperService.processUnsubscribeText(business.getEnterpriseIdElseBusinessId(), smsTemplate);
			
			// BIRDEYE-78374 | Handling unsub text to append at last in case of pulse survey.
			formattedMsg = StringUtils.join(getRRSmsCampaignMessage(business, reviewRequest, customer, smsTemplate, StringUtils.EMPTY, campaign, retryOnShortenURlError, quickSend),
					CampaignUtils.getUnsubscribeTextFromSmsTemplate(smsTemplate, KontactoExternalUtils.getCustomFieldValue(customer, Constants.LOCALE_CUSTOM_FIELD)));
		}
		boolean mmsSupported = businessOptionService.isMmsOpted(business);
		boolean mmsEnabled = (smsTemplate.getMmsEnabled() == null || smsTemplate.getMmsEnabled() == 1);
		logger.info("mmsEnabled : {}, mmsSupported : {}, for request id: {}", mmsEnabled, mmsSupported, reviewRequest.getId());
		logger.info("Sending sms message: {} for request id: {}", formattedMsg, reviewRequest.getId());
		sendSmsToCustomer(business, customer, formattedMsg, reviewRequest, getMediaUrl(business, smsTemplate, mmsSupported, mmsEnabled), getRequestTypeAcronym(reviewRequest.getRequestType()), null,
				surveyType, reviewRequest.getSurveyId(), questionId,applyDnd,quickSend);
	}
	
	public void sendRRMessengerSmsCampaign(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, KontactoDTO customer, BusinessSmsTemplate smsTemplate, boolean applyDND,
			boolean retryOnShortenUrlError) throws Exception {
		MessengerCampaign messengerCampaign = validateMessengerCampaign(reviewRequest);
		
		String formattedMsg = CampaignUtils.replaceBrWithNewLineTag(getRRSmsMessengerCampaignMessage(business, reviewRequest, customer, messengerCampaign, smsTemplate, retryOnShortenUrlError));
		
		String surveyType = null;
		Integer questionId = null;
		// appending first question and suffix to template message in case of pulse survey.
		Map<String, Object> npsSurveyResponse = getNpsSurveyData(reviewRequest, business);
		if (MapUtils.isNotEmpty(npsSurveyResponse)) {
			ObjectMapper mapper = new ObjectMapper();
			SurveyInfoResponse surveyInfoResponse = mapper.convertValue(npsSurveyResponse, SurveyInfoResponse.class);
			if (surveyInfoResponse != null && CollectionUtils.isNotEmpty(surveyInfoResponse.getPages()) && CollectionUtils.isNotEmpty(surveyInfoResponse.getPages().get(0).getQuestions())) {
				
				//BIRD-116893 | Handling unsubscribe text if Text Category Flag is enabled.
				templateHelperService.processUnsubscribeText(business.getEnterpriseIdElseBusinessId(), smsTemplate);
				
				// BIRDEYE-78374 | Handling unsub text to append at last in case of pulse survey.
				formattedMsg = StringUtils.join(formattedMsg, CampaignUtils.getNewLineByCount(2), String.valueOf(surveyInfoResponse.getPages().get(0).getQuestions().get(0).get("title")),
						StringUtils.SPACE, String.valueOf(surveyInfoResponse.getPages().get(0).getQuestions().get(0).get("pulseSuffix")),
						CampaignUtils.getUnsubscribeTextFromSmsTemplate(smsTemplate, KontactoExternalUtils.getCustomFieldValue(customer, Constants.LOCALE_CUSTOM_FIELD)));
				surveyType = String.valueOf(npsSurveyResponse.get(SURVEY_TYPE));
				questionId = (Integer) surveyInfoResponse.getPages().get(0).getQuestions().get(0).get("id");
			}
		}
		boolean mmsSupported = businessOptionService.isMmsOpted(business);
		logger.info("Sending messenger sms message for request id: {}", reviewRequest.getId());
		MessengerMediaInfo mediaInfo = CampaignUtils.getMediaInfoForMessengerCampaign(messengerCampaign);
		sendSmsToCustomer(business, customer, formattedMsg, reviewRequest, (mmsSupported && mediaInfo != null) ? mediaInfo.getUrl() : null, getRequestTypeAcronym(reviewRequest.getRequestType()),
				mediaInfo, surveyType, reviewRequest.getSurveyId(), questionId, applyDND, false);
	}
	
	@Override
	public void sendPromotionSmsCampaign(BusinessEnterpriseEntity business, Promotion promotion, KontactoDTO customer, BusinessSmsTemplate smsTemplate) throws Exception {
		
		//BIRD-116893 | Handling unsubscribe text if Text Category Flag is enabled.
		templateHelperService.processUnsubscribeText(business.getEnterpriseIdElseBusinessId(), smsTemplate);
		
		// BIRDEYE-78374 | Handling unsub text to append at last in case of pulse survey.
		String formattedMsg = StringUtils.join(getSmsMessage(smsTemplate, customer, business, getShortenURLforPromotionalSms(business, promotion), promotion, StringUtils.EMPTY, null, true),
				CampaignUtils.getUnsubscribeTextFromSmsTemplate(smsTemplate));
		boolean mmsSupported = businessOptionService.isMmsOpted(business);
		boolean mmsEnabled = (smsTemplate.getMmsEnabled() == null || smsTemplate.getMmsEnabled() == 1);
		logger.info("mmsEnabled : {}, mmsSupported : {}, for promotion request id: {}", mmsEnabled, mmsSupported, promotion.getId());
		logger.info("Sending sms message: {} for request id: {}", formattedMsg, promotion.getId());
		String mediaUrl = getMediaUrlPromotionSms(smsTemplate, mmsSupported, mmsEnabled);
		Campaign campaign = campaignSetupCachingService.getCampaignById(promotion.getCampaignId());
		String triggerType = campaign.getTriggerType();
		boolean scheduleOnLimitBreached = !StringUtils.equalsIgnoreCase(triggerType, CampaignTriggerTypeEnum.CONTACT_EVENT.getType());
		
		CampaignSMSProperties smsProperties = prepareCampaignSMSProperties(business, customer, formattedMsg, promotion, mediaUrl, Constants.PROMOTION_SMS_TYPE, null, null, null, null, true, false,
				scheduleOnLimitBreached);
		sendSmsToCustomer(smsProperties);
		updatePromotionRequestSegmentCount(promotion, formattedMsg, mediaUrl, business.getCountryCode());
	}

	private CampaignSMSProperties prepareCampaignSMSProperties(BusinessEnterpriseEntity business, KontactoDTO customer, String formattedMsg, Promotion promotion, String mediaUrl, String promotionSmsType,
			MessengerMediaInfo messengerMediaInfo, String surveyType, Integer surveyId, Integer questionId, boolean applyDnd, boolean quickSend, boolean scheduleOnLimitBreached) {
		CampaignSMSProperties smsProperties = new CampaignSMSProperties();
		smsProperties.setBusiness(business);
		smsProperties.setCustomer(customer);
		smsProperties.setSmsBody(formattedMsg);
		smsProperties.setReviewRequest(promotion);
		smsProperties.setMediaUrl(mediaUrl);
		smsProperties.setRequestType(promotionSmsType);
		smsProperties.setMessengerMediaInfo(messengerMediaInfo);
		smsProperties.setSurveyType(surveyType);
		smsProperties.setSurveyId(surveyId);
		smsProperties.setQuestionId(questionId);
		smsProperties.setApplyDnd(applyDnd);
		smsProperties.setQuickSend(quickSend);
		smsProperties.setScheduleOnLimitBreached(scheduleOnLimitBreached);
		return smsProperties;
	}

	private void updatePromotionRequestSegmentCount(Promotion promotion, String formattedMsg, String mediaUrl, String countryCode) {
		if (promotion == null)
			return;
		promotion.setSegmentCount(TwilioAPIUtils.getCustomSMSSegments(formattedMsg, mediaUrl, countryCode));
		promotionRepo.saveAndFlush(promotion);
	}

	@Override
	public void sendPromotionMessengerSmsCampaign(BusinessEnterpriseEntity business, Promotion promotion, KontactoDTO customer, BusinessSmsTemplate smsTemplate) throws Exception {
		MessengerCampaign messengerCampaign = validateMessengerCampaign(promotion);
		String formattedMsg = getPromotionSmsMessengerCampaignMessage(business, promotion, customer, messengerCampaign, smsTemplate);
		boolean mmsSupported = businessOptionService.isMmsOpted(business);
		logger.info("Sending messenger sms message for promotion request id: {}", promotion.getId());
		MessengerMediaInfo mediaInfo = CampaignUtils.getMediaInfoForMessengerCampaign(messengerCampaign);
		String mediaUrl = (mmsSupported && mediaInfo != null) ? mediaInfo.getUrl() : null;
		updatePromotionRequestSegmentCount(promotion, formattedMsg, mediaUrl, business.getCountryCode());
		sendSmsToCustomer(business, customer, formattedMsg, promotion, mediaUrl, Constants.PROMOTION_SMS_TYPE, mediaInfo, null, null, null, true, false);
	}
	
	private String getMediaUrlPromotionSms(BusinessSmsTemplate smsTemplate, boolean mmsSupported, boolean mmsEnabled) {
		String mediaUrl = null;
		if (mmsSupported && mmsEnabled) {
			mediaUrl = smsTemplate.getMediaUrls();
		}
		return StringUtils.isBlank(mediaUrl) ? null : mediaUrl;
	}
	
	@Override
	public void sendSmsToCustomer(BusinessEnterpriseEntity business, KontactoDTO customer, String smsBody, BaseCommunicationEntity reviewRequest, String mediaUrl, String requestType,
			MessengerMediaInfo messengerMediaInfo, String surveyType, Integer surveyId, Integer questionId, boolean applyDnd, boolean quickSend) throws Exception {
		String businessSmsNumber = validateSmsDetails(business, customer, smsBody, mediaUrl, requestType);
		
		SmsDto sms = saveSmsMessage(business, businessSmsNumber, customer, reviewRequest.getId(), smsBody, mediaUrl);
		// Send SMS Flow
		// submit event to nexus.
		sms.setMessageBodyUnencrypted(smsBody);
		sendSMSViaNexus(sms, reviewRequest, requestType, messengerMediaInfo, surveyType, surveyId, questionId, applyDnd, business, quickSend);
		// user/customer unsubscription will be handled in the acknowledgement callback from nexus.
	}
	private void sendSmsToCustomer(CampaignSMSProperties smsProperties) throws Exception {
		String businessSmsNumber = validateSmsDetails(smsProperties);
		
		SmsDto sms = saveSmsMessage(smsProperties, businessSmsNumber);
		// Send SMS Flow
		// submit event to nexus.
		sms.setMessageBodyUnencrypted(smsProperties.getSmsBody());
		sendSMSViaNexus(sms, smsProperties);
		// user/customer unsubscription will be handled in the acknowledgement callback from nexus.
	}
	
	private String validateSmsDetails(CampaignSMSProperties smsProperties) {
		
	// only for custom/Promotional SMS template sms body can be null and as we can send media URL
	if ((StringUtils.isEmpty(smsProperties.getSmsBody()) && !smsProperties.getRequestType().contains(CampaignTypeEnum.PROMOTIONAL.getType()))
			|| (StringUtils.isEmpty(smsProperties.getSmsBody()) && StringUtils.isEmpty(smsProperties.getMediaUrl()) && smsProperties.getRequestType().contains(CampaignTypeEnum.PROMOTIONAL.getType()))) {
		throw new CampaignException(ErrorCodes.INVALID_SMS_MESSAGE, "invalid sms message");
	}

	String businessSmsNumber = null;
	List<String> businessSmsNumbers = businessSmsRepo.getBusinessSmsNumberById(smsProperties.getBusiness().getId());
	if (CollectionUtils.isEmpty(businessSmsNumbers)) {
		logger.error("no sms number exists for business id : {}", smsProperties.getBusiness().getId());
		throw new CampaignException(ErrorCodes.INVALID_SMS_NUMBER);
	}

	businessSmsNumber = businessSmsNumbers.get(0);
	return businessSmsNumber;
	}

	private String validateSmsDetails(BusinessEnterpriseEntity business, KontactoDTO customer, String smsBody, String mediaUrl, String requestType) {
		// only for custom/Promotional SMS template sms body can be null and as we can send media URL
		if ((StringUtils.isEmpty(smsBody) && !requestType.contains(CampaignTypeEnum.PROMOTIONAL.getType()))
				|| (StringUtils.isEmpty(smsBody) && StringUtils.isEmpty(mediaUrl) && requestType.contains(CampaignTypeEnum.PROMOTIONAL.getType()))) {
			throw new CampaignException(ErrorCodes.INVALID_SMS_MESSAGE, "invalid sms message");
		}

		String businessSmsNumber = null;
		List<String> businessSmsNumbers = businessSmsRepo.getBusinessSmsNumberById(business.getId());
		if (CollectionUtils.isEmpty(businessSmsNumbers)) {
			logger.error("no sms number exists for business id : {}", business.getId());
			throw new CampaignException(ErrorCodes.INVALID_SMS_NUMBER);
		}

		businessSmsNumber = businessSmsNumbers.get(0);
		return businessSmsNumber;
	}

	private String getMediaUrl(BusinessEnterpriseEntity business, BusinessSmsTemplate smsTemplate, boolean mmsSupported, boolean mmsEnabled) {
		String mediaUrl = null;
		if (mmsSupported && mmsEnabled) {
			if (TemplateTypeEnum.REFERRAL.getName().equalsIgnoreCase(smsTemplate.getType())) {
				mediaUrl = CoreUtils.parseDelimitedStringsWithDefaultAsEmptyList(smsTemplate.getMediaUrls(), ",").get(0);
			} else if (TemplateTypeEnum.APPOINTMENT_FORM_SMS.getName().equalsIgnoreCase(smsTemplate.getType())) {
				mediaUrl = CoreUtils.parseDelimitedStringsWithDefaultAsEmptyList(smsTemplate.getMediaUrls(), ",").get(0);
			} else {
				BrandingInfo brandingInfo = null;
				if (smsTemplate.getLocationBrandingEnabled() != null && smsTemplate.getLocationBrandingEnabled() == 1) {
					brandingInfo = businessExternalService.getBrandingInfoByLocationId(business.getId());
				} else {
					brandingInfo = businessExternalService.getBrandingInfoByLocationId(business.getEnterpriseId() != null ? business.getEnterpriseId() : business.getId());
				}
				if ("custom".equalsIgnoreCase(brandingInfo.getMediaType())) {
					mediaUrl = BusinessBrandingUtils.getAbsoluteCDNImageURLForBusiness(brandingInfo.getCustomImageUrl(), business.getBusinessId());
				} else {
					mediaUrl = BusinessBrandingUtils.getAbsoluteImageURL(brandingInfo.getBrandingImage(), false);
				}
			}
			logger.info("adding media to sms (mms) mediaUrl: {}", mediaUrl);
		}

		return StringUtils.isBlank(mediaUrl) ? null : mediaUrl;
	}

	@Override
	public String getPromotionSmsMessengerCampaignMessage(BusinessEnterpriseEntity business, Promotion promotionRequest, KontactoDTO customer, MessengerCampaign messengerCampaign,
			BusinessSmsTemplate smsTemplate) {
		TextStringBuilder formattedMsg = new TextStringBuilder();
		BusinessProfileResponse businessProfileInfo = cacheService.getBusinessProfileByBusinessId(promotionRequest.getBusinessId());
		String messageBody = replaceTokensInSmsBody(smsTemplate, business, messengerCampaign.getFreeText(), promotionRequest, 1, null, true, 0, businessProfileInfo);
		formattedMsg.append(messageBody);

		if (customer != null) {
			String customerName = StringUtils.isEmpty(customer.getFirstName()) ? "there" : extractFirstNameOnly(customer.getFirstName());
			formattedMsg.replaceAll(TemplateStaticTokens.CUSTOMER_NAME_TOKEN, customerName);
			// BIRDEYE-49916
			formattedMsg.replaceAll(TemplateStaticTokens.CONTACT_FIRST_NAME_TOKEN, customerName);
			// BIRDEYE-82410
			formattedMsg.replaceAll(TemplateStaticTokens.CONTACT_LAST_NAME_TOKEN, customer.getLastName());
			formattedMsg.replaceAll(TemplateStaticTokens.CONTACT_EMAIL, customer.getEmailId());
			formattedMsg.replaceAll(TemplateStaticTokens.CONTACT_PHONE_NUMER, customer.getPhoneE164());
			// replace custom fields.
			replaceCustomFields(formattedMsg, customer, smsTemplate.getId());
			
			replaceLocationCustomFields(formattedMsg, smsTemplate.getId(), businessProfileInfo, CustomFieldSourceEnum.LOCATION, promotionRequest, business, true);
		}
		
		//BIRD-116893 | Handling unsubscribe text if Text Category Flag is enabled.
		templateHelperService.processUnsubscribeText(business.getEnterpriseIdElseBusinessId(), smsTemplate);
		
		formattedMsg.append(CampaignUtils.getUnsubscribeTextFromSmsTemplate(smsTemplate));
		// BIRDEYE-49916 | Replacing any other token with empty string.
		return formattedMsg.toString().replaceAll("\\[[^\\]]*\\]", StringUtils.EMPTY);
	}
	
	private String getRRSmsMessengerCampaignMessage(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, KontactoDTO customer, MessengerCampaign messengerCampaign,
			BusinessSmsTemplate smsTemplate, boolean retryOnShortenUrlError) throws Exception {
		TextStringBuilder formattedMsg = new TextStringBuilder();
		BusinessProfileResponse businessProfileInfo = cacheService.getBusinessProfileByBusinessId(reviewRequest.getBusinessId());
		String messageBody = replaceTokensInSmsBody(smsTemplate, business, messengerCampaign.getFreeText(), reviewRequest, 1, null, retryOnShortenUrlError, 0, businessProfileInfo);
		formattedMsg.append(messageBody);

		if (customer != null) {
			String customerName = StringUtils.isEmpty(customer.getFirstName()) ? "there" : extractFirstNameOnly(customer.getFirstName());
			formattedMsg.replaceAll(TemplateStaticTokens.CUSTOMER_NAME_TOKEN, customerName);
			// BIRDEYE-49916
			formattedMsg.replaceAll(TemplateStaticTokens.CONTACT_FIRST_NAME_TOKEN, customerName);
			// BIRDEYE-82410
			formattedMsg.replaceAll(TemplateStaticTokens.CONTACT_LAST_NAME_TOKEN, customer.getLastName());
			formattedMsg.replaceAll(TemplateStaticTokens.CONTACT_EMAIL, customer.getEmailId());
			formattedMsg.replaceAll(TemplateStaticTokens.CONTACT_PHONE_NUMER, customer.getPhoneE164());
			// replacing referral tokens
			// KontactoDTO kontactoDTO = KontactoExternalUtils.getKontactoDTOFromCustomer(customer);
			formattedMsg.replaceAll(TemplateStaticTokens.REFERRER_NAME_TOKEN, customer.getCustomerName());
			formattedMsg.replaceAll(TemplateStaticTokens.REFERRAL_CODE_TOKEN, customer.getReferralCode() != null ? customer.getReferralCode() : StringUtils.EMPTY);
			// replace custom fields.
			replaceCustomFields(formattedMsg, customer, smsTemplate.getId());
			
			replaceLocationCustomFields(formattedMsg, smsTemplate.getId(), businessProfileInfo, CustomFieldSourceEnum.LOCATION, reviewRequest, business, retryOnShortenUrlError);
		}

		replaceMessengerDummyBitlyLink(business, reviewRequest, formattedMsg, smsTemplate);

		// BIRDEYE-49916 | Replacing any other token with empty string.
		return formattedMsg.toString().replaceAll("\\[[^\\]]*\\]", StringUtils.EMPTY);
	}

	// SMS Body from INBOX UI has a static hardcoded dummy bitly link, replacing it with actual bitly link based on review request
	private void replaceMessengerDummyBitlyLink(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, TextStringBuilder formattedMsg, BusinessSmsTemplate smsTemplate) throws Exception {
		String shortenUrl = getShortenUrlForSmsWithException(business, reviewRequest, smsTemplate, null, true, false);
		if (StringUtils.isBlank(shortenUrl)) {
			logger.error("Url Shortening returned empty url and therefore not sending SMS message via messenger campaign for business  id : {},reviewRequest id : {}", business.getId(),
					reviewRequest.getId());
			throw new SmsUrlShortenException(ErrorCodes.URL_SHORTEN_ERROR, "Url Shortening returned empty url");
		}
		formattedMsg.replaceAll(Constants.MESSENGER_STATIC_BITLY_URL, shortenUrl);
	}
	
	/**
	 * 
	 * Prepare and fetch RR SMS campaign message content
	 * 
	 * @param business
	 * @param reviewRequest
	 * @param customer
	 * @param smsTemplate
	 * @param surveyType
	 * @param campaign
	 * @param retryOnShortenURlError
	 * @param quickSend
	 * @return
	 * @throws Exception
	 */
	public String getRRSmsCampaignMessage(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, KontactoDTO customer, BusinessSmsTemplate smsTemplate, String surveyType, Campaign campaign,
			boolean retryOnShortenURlError, boolean quickSend) throws Exception {
		
		// Get a shortened URL for the SMS - throws exception if error from core service
		String shortenUrl = getShortenUrlForSmsWithException(business, reviewRequest, smsTemplate, campaign, retryOnShortenURlError, quickSend);
		
		// Check if the shortened URL is empty and if manual/qick-send custom appointment request conditions are not met
		if (StringUtils.isBlank(shortenUrl) && !appointmentFormService.isManualCampaignCustomAppointmentRequest(campaign, reviewRequest, business.getEnterpriseIdElseBusinessId())
				&& !appointmentFormService.isQuickSendCustomAppointmentRequest(campaign, reviewRequest, business.getEnterpriseIdElseBusinessId())) {
			logger.error("Url Shortening returned empty url and therefore not sending SMS message for business  id : {},reviewRequest id : {}", business.getId(), reviewRequest.getId());
			throw new SmsUrlShortenException(ErrorCodes.URL_SHORTEN_ERROR, "Url Shortening returned empty url");
		}
		
		return getSmsMessage(smsTemplate, customer, business, shortenUrl, reviewRequest, surveyType, null, retryOnShortenURlError);
	}

	private Map<String, Object> getNpsSurveyData(ReviewRequest reviewRequest, BusinessEnterpriseEntity business) {

		// Pulse survey will only be available for accounts that have Inbox available
		if (Constants.SURVEY_REQUEST_TEMPLATE_TYPE.equalsIgnoreCase(reviewRequest.getRequestType())
				&& CoreUtils.isTrueForInteger(businessExternalService.isMessengerEnabled(business.getEnterpriseIdElseBusinessId()))) {

			Map<String, Object> surveyDataById = surveyService.getSurveyDataByIdAsMap(reviewRequest.getSurveyId());
			if (NPS.equalsIgnoreCase(String.valueOf(surveyDataById.get(SURVEY_TYPE)))) {
				return surveyDataById;
			}
		}
		return Collections.emptyMap();
	}
	
	private String getShortenURLforRecallSms(BusinessEnterpriseEntity business, ReviewRequest reviewRequest) {
		AppointmentRRMapping appointmentRequestAudit = appointmentAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
		if (appointmentRequestAudit == null) {
			logger.error("AppointmentRRMapping received is null for Review Request Id {}", reviewRequest.getId());
			return StringUtils.EMPTY;
		}
		AppointmentRecallResponse recallResponse = appointmentService.getAppointmentRecallInfoByAppointmentId(appointmentRequestAudit.getAppointmentId(), business.getEnterpriseIdElseBusinessId());
		if (recallResponse == null) {
			logger.error("error while fetching recallResponse for appointment recall id {}", appointmentRequestAudit.getRecallId());
			return StringUtils.EMPTY;
		}
		Integer appointmentSchedulingEnabled = cacheService.getProductFeatureForBusiness(business.getEnterpriseIdElseBusinessId()).getAppointmentSchedulingEnabled();
		
		Long smbOrEnterpriseBusinessNumber = getSMBorEnterpriseBusinessNumber(business);
		AppointmentDetailsResponse appointmentResponse = appointmentService.getAppointmentById(String.valueOf(appointmentRequestAudit.getAppointmentId()), business.getEnterpriseIdElseBusinessId(), false, false);
		String encryptedAppointmentId = appointmentResponse != null ? appointmentResponse.getExtAppointmentId() : StringUtils.EMPTY;
		String widgetId = appointmentResponse != null && StringUtils.isNotBlank(appointmentResponse.getWidgetId()) ? appointmentResponse.getWidgetId() : Constants.DEFAULT_WIDGET_ID;
		return deeplinkURLGenerationService.getAppointmentRecallBookingURLForSMS(business, smbOrEnterpriseBusinessNumber, reviewRequest, encryptedAppointmentId, widgetId, appointmentSchedulingEnabled);
	}
	
	private String getShortenURLforPromotionalSms(BusinessEnterpriseEntity business, Promotion promotion) {
		AppointmentRRMapping appointmentRequestAudit = appointmentAuditService.getReminderRequestAuditByReviewRequestId(promotion.getId());
		if (appointmentRequestAudit == null) {
			logger.error("AppointmentRRMapping received is null for Review Request Id {}", promotion.getId());
			return StringUtils.EMPTY;
		}
		
		Integer appointmentSchedulingEnabled = cacheService.getProductFeatureForBusiness(business.getEnterpriseIdElseBusinessId()).getAppointmentSchedulingEnabled();
		
		Long smbOrEnterpriseBusinessNumber = getSMBorEnterpriseBusinessNumber(business);
		AppointmentDetailsResponse appointmentResponse = appointmentService.getAppointmentById(String.valueOf(appointmentRequestAudit.getAppointmentId()),
				business.getEnterpriseIdElseBusinessId(), false, false);
		String encryptedAppointmentId = appointmentResponse != null ? appointmentResponse.getExtAppointmentId() : StringUtils.EMPTY;
		String widgetId = appointmentResponse != null && StringUtils.isNotBlank(appointmentResponse.getWidgetId()) ? appointmentResponse.getWidgetId()
				: Constants.DEFAULT_WIDGET_ID;
		return deeplinkURLGenerationService.getAppointmentBookingURLForPromotionalSMS(business, smbOrEnterpriseBusinessNumber, promotion, encryptedAppointmentId, widgetId,
				appointmentSchedulingEnabled);
	}
	
	private Long getSMBorEnterpriseBusinessNumber(BusinessEnterpriseEntity locationOrSMB) {
		if (locationOrSMB.getEnterpriseId() != null) {
			GetBusinessLiteResponse businessLiteInfo = businessService.getBusinessLiteDetails(CoreUtils.getSmbOrEnterpriseId(locationOrSMB), false);
			return businessLiteInfo.getBusinessNumber();
		}
		return locationOrSMB.getBusinessId();
	}
	
	/**
	 *
	 * Shorten URL for review request SMS Use fallback original URL if shortening fails at Core service
	 * 
	 * @param business
	 * @param reviewRequest
	 * @param smsTemplate
	 * @param campaign
	 * @return
	 */
	@Override
	public String getShortenUrlForSms(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, BusinessSmsTemplate smsTemplate, Campaign campaign) {
		String shortenUrl = null;
		try {
			if (reviewRequest.getSurveyId() != null) {
				shortenUrl = getSurveyLinkUrl(business, reviewRequest, false);
			} else if (StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.REFERRAL.getTemplateType())) {
				shortenUrl = getReferralDeepLinkForSMS(business, reviewRequest, false);
			} else if (StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.APPOINTMENT_REMINDER.getType())) {
				AppointmentRRMapping appointmentReminderRequestAudit = appointmentAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
				AppointmentInfoLiteDTO appointmentInfo = appointmentService.getAppointmentLiteById(appointmentReminderRequestAudit.getAppointmentId().toString(),
						business.getEnterpriseIdElseBusinessId(), false, false);
				shortenUrl = deeplinkURLGenerationService.getAppointmentReminderDeeplinkForSMS(business, reviewRequest, appointmentInfo.getExtAppointmentId());
			} else if (StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.APPOINTMENT_RECALL.getType())) {
				shortenUrl = getShortenURLforRecallSms(business, reviewRequest);
			} else if (StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.APPOINTMENT_FORM.getType())) {
				shortenUrl = getAppointmentFormShortenUrl(reviewRequest, business, smsTemplate);
			} else {
				shortenUrl = getDeepLinkForSMS(business, reviewRequest, false);
			}
		} catch (Exception e) {
			logger.error("error while generating shortened sms url for request type : {}, error : {}", reviewRequest.getId(), ExceptionUtils.getStackTrace(e));
		}
		return shortenUrl;
	}
	
	/**
	 * 
	 * Shorten URL for review request SMS. Throw exception in case of URL shortening error for applicable use cases else use original URL as fallback)
	 * 
	 * @param business
	 * @param reviewRequest
	 * @param smsTemplate
	 * @param campaign
	 * @param retryOnShortenURlError
	 * @param quickSend
	 * @return
	 * @throws Exception
	 */
	@Override
	public String getShortenUrlForSmsWithException(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, BusinessSmsTemplate smsTemplate, Campaign campaign, boolean retryOnShortenURlError,
			boolean quickSend) throws Exception {
		String shortenUrl = null;
		boolean rescheduleOnShortenUrlError = campaignRescheduleService.rescheduleRequiredOnShortenUrlError(quickSend, reviewRequest, retryOnShortenURlError);
		try {
			if (reviewRequest.getSurveyId() != null && StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.SURVEY_REQUEST.getTemplateType())) {
				shortenUrl = getSurveyLinkUrl(business, reviewRequest, rescheduleOnShortenUrlError);
			} else if (StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.REFERRAL.getTemplateType())) {
				shortenUrl = getReferralDeepLinkForSMS(business, reviewRequest, rescheduleOnShortenUrlError);
			} else if (StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.APPOINTMENT_REMINDER.getType())) {
				shortenUrl = getAppointmentReminderShortenUrl(business, reviewRequest);
			} else if (StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.APPOINTMENT_RECALL.getType())) {
				shortenUrl = getShortenURLforRecallSms(business, reviewRequest);
			} else if (StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.APPOINTMENT_FORM.getType())) {
				shortenUrl = getAppointmentFormShortenUrl(reviewRequest, business, smsTemplate);
			} else {
				shortenUrl = getDeepLinkForSMS(business, reviewRequest, rescheduleOnShortenUrlError);
			}
		} catch (CampaignException campaignException) {
			if (CampaignExceptionUtils.isURLShorteningRetriableException(campaignException) || CampaignExceptionUtils.isAppointmentNotFoundException(campaignException)) {
				throw campaignException;
			}
		} catch (Exception exception) {
			logger.error("error while generating shortened sms url for request type : {}, error : {}", reviewRequest.getId(), ExceptionUtils.getStackTrace(exception));
		}
		return shortenUrl;
	}
	
	/**
	 * 
	 * @param business
	 * @param reviewRequest
	 * @param rescheduleOnShortenUrlError
	 * @return
	 * @throws Exception
	 */
	private String getDeepLinkForSMS(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, boolean rescheduleOnShortenUrlError) throws Exception {
		if (rescheduleOnShortenUrlError) {
			return deeplinkURLGenerationService.getDeepLinkForSMSWithException(business, reviewRequest);
		}
		return deeplinkURLGenerationService.getDeepLinkForSMS(business, reviewRequest);
	}
	
	/**
	 * 
	 * @param reviewRequest
	 * @param business
	 * @param smsTemplate
	 * @return
	 */
	private String getAppointmentFormShortenUrl(ReviewRequest reviewRequest, BusinessEnterpriseEntity business, BusinessSmsTemplate smsTemplate) {
		AppointmentRRMapping appointmentReminderRequestAudit = appointmentAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
		AppointmentInfoLiteDTO appointmentInfo = appointmentService.getAppointmentLiteById(appointmentReminderRequestAudit.getAppointmentId().toString(), business.getEnterpriseIdElseBusinessId(),
				false, false);
		if (appointmentInfo == null) {
			throw new CampaignException(ErrorCodes.NO_VALID_APOINTMENT_FOUND, ErrorCodes.NO_VALID_APOINTMENT_FOUND.getMessage());
		}
		return getShortenURLforAppointmentFormSms(business, reviewRequest, smsTemplate.getFormUrl(), appointmentInfo.getExtAppointmentId());
	}
	
	/**
	 * 
	 * @param business
	 * @param reviewRequest
	 * @param rescheduleOnShortenUrlError
	 * @return
	 * @throws Exception
	 */
	private String getReferralDeepLinkForSMS(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, boolean rescheduleOnShortenUrlError) throws Exception {
		if (rescheduleOnShortenUrlError) {
			return deeplinkURLGenerationService.getReferralDeepLinkForSMSWithException(business, reviewRequest);
		}
		return deeplinkURLGenerationService.getReferralDeepLinkForSMS(business, reviewRequest);
	}
	
	/**
	 * 
	 * @param business
	 * @param reviewRequest
	 * @return
	 */
	private String getAppointmentReminderShortenUrl(BusinessEnterpriseEntity business, ReviewRequest reviewRequest) {
		AppointmentRRMapping appointmentReminderRequestAudit = appointmentAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
		AppointmentInfoLiteDTO appointmentInfo = appointmentService.getAppointmentLiteById(appointmentReminderRequestAudit.getAppointmentId().toString(), business.getEnterpriseIdElseBusinessId(),
				false, false);
		if (appointmentInfo == null) {
			throw new CampaignException(ErrorCodes.NO_VALID_APOINTMENT_FOUND, ErrorCodes.NO_VALID_APOINTMENT_FOUND.getMessage());
		}
		return deeplinkURLGenerationService.getAppointmentReminderDeeplinkForSMS(business, reviewRequest, appointmentInfo.getExtAppointmentId());
	}
	
	/**
	 * 
	 * @param business
	 * @param reviewRequest
	 * @param rescheduleOnShortenUrlError
	 * @return
	 * @throws Exception
	 */
	private String getSurveyLinkUrl(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, boolean rescheduleOnShortenUrlError) throws Exception {
		if (rescheduleOnShortenUrlError) {
			return deeplinkURLGenerationService.getSurveyLinkUrlWithException(reviewRequest, business);
		}
		// with original URL as fallback
		return deeplinkURLGenerationService.getSurveyLinkUrl(reviewRequest, business);
	}
	
	// added surveyType param to handle mandatory addition of shorten url at EOM.
	@Override
	public String getSmsMessage(BusinessSmsTemplate smsTemplate, KontactoDTO customer, BusinessEnterpriseEntity business, String shortenUrl, BaseCommunicationEntity reviewRequest, String surveyType,
			Integer userId, boolean retryOnUrlShortenError) {
		
		TextStringBuilder formattedMsg = new TextStringBuilder();
		TextStringBuilder shortenUrlBuilder = new TextStringBuilder(shortenUrl);
		BusinessProfileResponse businessProfileInfo = cacheService.getBusinessProfileByBusinessId(business.getId());
		String messageBody = replaceTokensInSmsBody(smsTemplate, business, smsTemplate.getMessageBody(), reviewRequest, 0, userId, retryOnUrlShortenError, 0, businessProfileInfo);
		formattedMsg.append(messageBody);
		if (customer != null) {
			replaceCustomerTokens(formattedMsg,customer);
			// replacing referral tokens
			// KontactoDTO kontactoDTO = KontactoExternalUtils.getKontactoDTOFromCustomer(customer);
			formattedMsg.replaceAll(TemplateStaticTokens.REFERRER_NAME_TOKEN, customer.getCustomerName());
			formattedMsg.replaceAll(TemplateStaticTokens.REFERRAL_CODE_TOKEN,
					customer.getReferralCode() != null ? customer.getReferralCode() : StringUtils.EMPTY);
			// replace custom fields.
			replaceCustomFields(formattedMsg, customer, smsTemplate.getId());
			
			//replace appointment tokens
			replaceAppointmentTokens(formattedMsg, reviewRequest, business, shortenUrlBuilder,smsTemplate, businessProfileInfo);
			
			replaceBusinessTokens(formattedMsg, businessProfileInfo, smsTemplate.getId(), reviewRequest, business, retryOnUrlShortenError);
		}
		if (StringUtils.isNotBlank(shortenUrlBuilder.toString())) {
			if (!formattedMsg.contains("[Shortlink]") && !NPS.equalsIgnoreCase(surveyType)) {
				formattedMsg.appendNewLine().append(shortenUrlBuilder.toString());
			} else {
				formattedMsg.replaceAll("[Shortlink]", shortenUrlBuilder.toString());
			}
		}
		// BIRDEYE-49916 | Replacing any other token with empty string.
		return formattedMsg.toString().replaceAll("\\[[^\\]]*\\]", StringUtils.EMPTY);
	}
	
	private void replaceCustomerTokens(TextStringBuilder formattedMsg, KontactoDTO customer) {
		String customerName = StringUtils.isEmpty(customer.getFirstName()) ? "there" : extractFirstNameOnly(customer.getFirstName());
		Map<String, String> tokenMap = new HashMap<>();
		
		tokenMap.put(TemplateStaticTokens.CUSTOMER_NAME_TOKEN, customerName);
		
		// BIRDEYE-49916
		tokenMap.put(TemplateStaticTokens.CONTACT_FIRST_NAME_TOKEN, customerName);
		
		// BIRDEYE-82410
		tokenMap.put(TemplateStaticTokens.CONTACT_LAST_NAME_TOKEN, customer.getLastName());
		
		tokenMap.put(TemplateStaticTokens.CONTACT_EMAIL, customer.getEmailId());
		tokenMap.put(TemplateStaticTokens.CONTACT_PHONE_NUMER, customer.getPhoneE164());
		for (Map.Entry<String, String> map : tokenMap.entrySet()) {
			formattedMsg.replaceAll(map.getKey(), map.getValue());
		}
		
	}

	private void replaceAppointmentTokens(TextStringBuilder formattedMsg, BaseCommunicationEntity reviewRequest, BusinessEnterpriseEntity locationOrSMB,
			TextStringBuilder shortenUrlBuilder, BusinessSmsTemplate smsTemplate, BusinessProfileResponse businessInfo) {
		Map<String, String> tokenMap = new HashMap<>();
		
		if (businessInfo != null) {
			if (businessInfo.getAdditionalData() != null) {
				tokenMap.put(TemplateStaticTokens.APPOINTMENT_LINK, businessInfo.getAdditionalData().getAppointmentLink());
			}
			if (businessInfo.getInternalUseData() != null) {
				tokenMap.put(TemplateStaticTokens.APPOINTMENT_FORM_LINK, businessInfo.getInternalUseData().getAppointmentFormLink());
			}
		}
		if (!StringUtils.equalsIgnoreCase(CampaignTypeEnum.APPOINTMENT_RECALL.getType(), reviewRequest.getRequestType())) {
			AppointmentRRMapping appointmentReminderRequestAudit = appointmentAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
			if (appointmentReminderRequestAudit == null || appointmentReminderRequestAudit.getAppointmentId() == null) {
				logger.info("No AppointmentRRMapping found for requestId {}", reviewRequest.getId());
				return;
			}
			Integer enterpriseOrSMBId = CoreUtils.getSmbOrEnterpriseId(locationOrSMB);
			AppointmentDetailsResponse response = appointmentService.getAppointmentById(appointmentReminderRequestAudit.getAppointmentId().toString(), enterpriseOrSMBId, true, false);
			if (response == null) {
				logger.info("No appointment details found for appointment id {}", appointmentReminderRequestAudit.getAppointmentId());
				return;
			}
			tokenMap.put(TemplateStaticTokens.APPOINTMENT_TIME,
					AppointmentReminderUtils.getAppointmentTime(response.getStartTime(), locationOrSMB.getTimezoneId(), response.getLocale()));
			tokenMap.put(TemplateStaticTokens.APPOINTMENT_DATE,
					AppointmentReminderUtils.getAppointmentDate(response.getStartTime(), locationOrSMB.getTimezoneId(), response.getLocale()));
			tokenMap.put(TemplateStaticTokens.PATIENT_FIRST_NAME, CoreUtils.getStringValue(response.getPatientDetails() != null ? response.getPatientDetails().getFirstName() : StringUtils.EMPTY));
			tokenMap.put(TemplateStaticTokens.PATIENT_LAST_NAME, CoreUtils.getStringValue(response.getPatientDetails() != null ? response.getPatientDetails().getLastName() : StringUtils.EMPTY));
			tokenMap.put(TemplateStaticTokens.SPECIALIST_NAME, CoreUtils.getStringValue(response.getSpecialistDetails() != null ? response.getSpecialistDetails().getSpecialistName() : StringUtils.EMPTY));
			tokenMap.put(TemplateStaticTokens.APPOINTMENT_TYPE, CoreUtils.getStringValue(response.getServiceDetails() != null ? response.getServiceDetails().getService() : StringUtils.EMPTY));
			
			Integer appointmentSchedulingEnabled = cacheService.getProductFeatureForBusiness(enterpriseOrSMBId).getAppointmentSchedulingEnabled();
			Boolean hideRescheduleOption = response.getAppointmentConfig() != null ? response.getAppointmentConfig().getHideRescheduleOption() : Boolean.FALSE;
			Boolean hideCancelOption = response.getAppointmentConfig() != null ? response.getAppointmentConfig().getHideCancelOption() : Boolean.FALSE;
			tokenMap.put(TemplateStaticTokens.APPLICABLE_CTAS,
					AppointmentReminderUtils.getApplicableCTAsText(smsTemplate.getConfirmButtonEnabled(), smsTemplate.getRescheduleButtonEnabled(),
							smsTemplate.getCancelButtonEnabled(), appointmentSchedulingEnabled, response.getAppointmentStatus(), hideRescheduleOption, hideCancelOption,
							response.getLocale()));
			tokenMap.put(TemplateStaticTokens.BUSINESS_PHONE_TOKEN, locationOrSMB.getPhone());
			if (StringUtils.equalsIgnoreCase(CampaignTypeEnum.APPOINTMENT_REMINDER.getType(), reviewRequest.getRequestType())) {
				hideAppointmentReminderShortenURL(response.getAppointmentStatus(), smsTemplate, appointmentSchedulingEnabled, response.getAppointmentConfig(), shortenUrlBuilder);
			} else if (StringUtils.equalsIgnoreCase(CampaignTypeEnum.PROMOTIONAL.getType(), reviewRequest.getRequestType()) && shortenUrlBuilder != null) {
				hidePromotionalShortenURL(appointmentSchedulingEnabled, smsTemplate.getBookAppointmentButtonEnabled(), shortenUrlBuilder);
				tokenMap.put(TemplateStaticTokens.BOOKING_URL, shortenUrlBuilder.toString());
			}
			replaceAppointmentCustomFields(formattedMsg, smsTemplate.getId(), response, CustomFieldSourceEnum.APPOINTMENT);
			ZoneId zoneId = DateTimeUtils.getZoneIdFromTimeZoneId(locationOrSMB.getTimezoneId());
			// Format the date as the day of the week using the SimpleDateFormat.
		    tokenMap.put(TemplateStaticTokens.DAY_OF_THE_WEEK, AppointmentRecallUtils.getDayOfTheWeekByMillisecond(response.getStartTime(), zoneId));
			
		} else {
			AppointmentRRMapping appointmentRequestAudit = appointmentAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
			if (appointmentRequestAudit == null) {
				logger.info("No AppointmentRRMapping found for requestId {}", reviewRequest.getId());
				return;
			}
			AppointmentDetailsResponse appointmentResponse = appointmentService.getAppointmentById(
					appointmentRequestAudit.getAppointmentId() != null ? appointmentRequestAudit.getAppointmentId().toString() : null,
					locationOrSMB.getEnterpriseIdElseBusinessId(), true, false);
			AppointmentRecallResponse recallResponse = appointmentService.getAppointmentRecallInfoByAppointmentId(appointmentRequestAudit.getAppointmentId(),
					locationOrSMB.getEnterpriseIdElseBusinessId());
			if (recallResponse == null) {
				logger.info("AppointmentRecallResponse is null for requestId {}", reviewRequest.getId());
				return;
			}
			Integer enterpriseOrSMBId = CoreUtils.getSmbOrEnterpriseId(locationOrSMB);
			Integer appointmentSchedulingEnabled = cacheService.getProductFeatureForBusiness(enterpriseOrSMBId).getAppointmentSchedulingEnabled();
			hideAppointmentRecallShortenURL(appointmentSchedulingEnabled, smsTemplate.getBookAppointmentButtonEnabled(), shortenUrlBuilder);
			
			String[] specialistName = AppointmentRecallUtils.splitSpecialistName(
					appointmentResponse != null && appointmentResponse.getSpecialistDetails() != null ? appointmentResponse.getSpecialistDetails().getSpecialistName() : null);
			
			// get customer call to fetch locale information
			KontactoDTO customer = cacheExternalService.getRRCustomerCached(reviewRequest.getId(), reviewRequest.getCustId());
			if (customer == null) {
				return;
			}
			Map<String, String> contactCustomField = KontactoExternalUtils.getCustomFieldsMap(customer);
			String locale = StringUtils.EMPTY;
			if (contactCustomField != null) {
				locale = contactCustomField.get(Constants.LOCALE_CUSTOM_FIELD);
			}
			tokenMap.put(TemplateStaticTokens.BOOK_APPOINTMENT_CTA_TOKEN,
					AppointmentRecallUtils.getBookAppointmentCTAForSMS(smsTemplate.getBookAppointmentButtonEnabled(), appointmentSchedulingEnabled, locale));
			tokenMap.put(TemplateStaticTokens.BUSINESS_PHONE_TOKEN, locationOrSMB.getPhone());
			tokenMap.put(TemplateStaticTokens.SPECIALIST_FIRST_NAME, specialistName[0]);
			tokenMap.put(TemplateStaticTokens.SPECIALIST_LAST_NAME, specialistName[1]);
			tokenMap.put(TemplateStaticTokens.DAYS_OVERDUE, AppointmentRecallUtils.getDaysOverdue(recallResponse.getDueDate()));
			tokenMap.put(TemplateStaticTokens.RECALL_TYPE, recallResponse.getRecallTypeLabel());
			tokenMap.put(TemplateStaticTokens.LAST_VISIT_DATE,
					AppointmentRecallUtils.getLastAppointmentDate(recallResponse.getLastAppointmentDate(), locationOrSMB.getTimezoneId(), locale));
			replaceAppointmentCustomFields(formattedMsg, smsTemplate.getId(), appointmentResponse, CustomFieldSourceEnum.APPOINTMENT);
			ZoneId zoneId = DateTimeUtils.getZoneIdFromTimeZoneId(locationOrSMB.getTimezoneId());
			if(appointmentResponse != null) {
				// Format the date as the day of the week using the SimpleDateFormat.
				tokenMap.put(TemplateStaticTokens.DAY_OF_THE_WEEK, AppointmentRecallUtils.getDayOfTheWeekByMillisecond(appointmentResponse.getStartTime(), zoneId));
				tokenMap.put(TemplateStaticTokens.APPOINTMENT_TIME,
						AppointmentReminderUtils.getAppointmentTime(appointmentResponse.getStartTime(), locationOrSMB.getTimezoneId(), appointmentResponse.getLocale()));
				tokenMap.put(TemplateStaticTokens.APPOINTMENT_DATE,
						AppointmentReminderUtils.getAppointmentDate(appointmentResponse.getStartTime(), locationOrSMB.getTimezoneId(), appointmentResponse.getLocale()));
				tokenMap.put(TemplateStaticTokens.PATIENT_FIRST_NAME, CoreUtils.getStringValue(appointmentResponse.getPatientDetails() != null ? appointmentResponse.getPatientDetails().getFirstName() : StringUtils.EMPTY));
				tokenMap.put(TemplateStaticTokens.PATIENT_LAST_NAME, CoreUtils.getStringValue(appointmentResponse.getPatientDetails() != null ? appointmentResponse.getPatientDetails().getLastName() : StringUtils.EMPTY));
				tokenMap.put(TemplateStaticTokens.SPECIALIST_NAME, CoreUtils.getStringValue(appointmentResponse.getSpecialistDetails() != null ? appointmentResponse.getSpecialistDetails().getSpecialistName() : StringUtils.EMPTY));
				tokenMap.put(TemplateStaticTokens.APPOINTMENT_TYPE, CoreUtils.getStringValue(appointmentResponse.getServiceDetails() != null ? appointmentResponse.getServiceDetails().getService() : StringUtils.EMPTY));
				
			}
		}
		
		for (Map.Entry<String, String> map : tokenMap.entrySet()) {
			formattedMsg.replaceAll(map.getKey(), map.getValue());
		}
	}
	
	/**
	 * 
	 * @param appointmentSchedulingEnabled
	 * @param bookAppointmentButtonEnabled
	 * @param shortenUrlBuilder
	 */
	private void hidePromotionalShortenURL(Integer appointmentSchedulingEnabled, Integer bookAppointmentButtonEnabled, TextStringBuilder shortenUrlBuilder) {
		if (BooleanUtils.isFalse(CoreUtils.getBooleanValueFromInteger(appointmentSchedulingEnabled)) || BooleanUtils.isFalse(CoreUtils.getBooleanValueFromInteger(bookAppointmentButtonEnabled))) {
			shortenUrlBuilder.replace(0, shortenUrlBuilder.length(), StringUtils.EMPTY);
		}
	}

	/**
	 * @param appointmentSchedulingEnabled
	 * @param bookAppointmentButtonEnabled
	 * @param shortenUrlBuilder
	 * Hiding shorten URL in case scheduling is not enabled or book_appointment_button is disabled at template level
	 */
	private void hideAppointmentRecallShortenURL(Integer appointmentSchedulingEnabled, Integer bookAppointmentButtonEnabled, TextStringBuilder shortenUrlBuilder) {
		if (BooleanUtils.isFalse(CoreUtils.getBooleanValueFromInteger(appointmentSchedulingEnabled)) || BooleanUtils.isFalse(CoreUtils.getBooleanValueFromInteger(bookAppointmentButtonEnabled))) {
			shortenUrlBuilder.replace(0, shortenUrlBuilder.length(), StringUtils.EMPTY);
		}
	}

	/**
	 * @param smsTemplate
	 * @param shortenUrlBuilder
	 * Hiding appointment reminder short URL when applicable CTAs(Confirm, Reschedule, Cancel) and/or business level
	 *flags are disabled
	 */
	private void hideAppointmentReminderShortenURL(String appointmentStatus, BusinessSmsTemplate smsTemplate, Integer appointmentSchedulingEnabled, AppointmentConfig appointmentConfig,
			TextStringBuilder shortenUrlBuilder) {
		boolean confirmTextNotAllowed = (BooleanUtils.isTrue(AppointmentStatusEnum.CONFIRMED.getType().equalsIgnoreCase(appointmentStatus))
				|| BooleanUtils.isFalse(CoreUtils.getBooleanValueFromInteger(smsTemplate.getConfirmButtonEnabled())));
		boolean rescheduleTextNotAllowed = (BooleanUtils.isTrue(appointmentConfig != null ? appointmentConfig.getHideRescheduleOption() : Boolean.FALSE)
				|| BooleanUtils.isFalse(CoreUtils.getBooleanValueFromInteger(smsTemplate.getRescheduleButtonEnabled()))
				|| BooleanUtils.isFalse(CoreUtils.getBooleanValueFromInteger(appointmentSchedulingEnabled)));
		boolean cancelTextNotAllowed = (BooleanUtils.isTrue(appointmentConfig != null ? appointmentConfig.getHideCancelOption() : Boolean.FALSE)
				|| BooleanUtils.isFalse(CoreUtils.getBooleanValueFromInteger(smsTemplate.getCancelButtonEnabled())));
		if (confirmTextNotAllowed && rescheduleTextNotAllowed && cancelTextNotAllowed) {
			shortenUrlBuilder.replace(0, shortenUrlBuilder.length(), StringUtils.EMPTY);
		}
	}
	
	/**
	 * Replace custom field tokens for a customer is sms body.
	 * 
	 * @param formattedMsg
	 * @param customer
	 * @param templateId
	 */
	private void replaceCustomFields(TextStringBuilder formattedMsg, KontactoDTO customer, Integer templateId) {
		List<CampaignCustomFieldAssociation> customFieldsByTemplate = customFieldAssociationRepo.getCampaignCustomFieldAssociationBySource(templateId,
				CustomFieldAssociatedObjectTypeEnum.getObjectTypeByCommType(Constants.TEMPLATE_BASE_TYPE_SMS), CustomFieldSourceEnum.CONTACT);
		if (CollectionUtils.isEmpty(customFieldsByTemplate)) {
			return;
		}
		Map<String, String> tokenMap = new HashMap<>();
		// adding all template level custom fields to token map with dot values.
		for (CampaignCustomFieldAssociation customField : customFieldsByTemplate) {
			tokenMap.put("[" + customField.getCustomFieldName() + "]", "...");
		}
		// overriding contact level custom fields to token map.
		for (Map.Entry<String, String> contactCustomField : KontactoExternalUtils.getCustomFieldsMap(customer).entrySet()) {
			if (StringUtils.isNotBlank(contactCustomField.getValue())) {
				tokenMap.put("[" + contactCustomField.getKey() + "]", contactCustomField.getValue());
			}
		}
		// replacing custom field tokens.
		for (Map.Entry<String, String> entry : tokenMap.entrySet()) {
			formattedMsg.replaceAll(entry.getKey(), entry.getValue());
		}

	}
	
	private String replaceTokensInSmsBody(BusinessSmsTemplate smsTemplate, BusinessEnterpriseEntity business, String messageBody, BaseCommunicationEntity reviewRequest, Integer isMessengerCampaign,
			Integer userId, boolean retryOnShortenUrlError, Integer isAppointmentCampaign, BusinessProfileResponse businessProfileInfo) {
		if (StringUtils.isNotBlank(messageBody)) {
			if (messageBody.contains(TemplateStaticTokens.BUSINESS_NAME_TOKEN)) {
				String businessName = StringUtils.isNotBlank(smsTemplate.getMessageFrom()) ? smsTemplate.getMessageFrom() : business.getName();
				messageBody = messageBody.replace(TemplateStaticTokens.BUSINESS_NAME_TOKEN, businessName);
				if (messageBody.length() > 125 && businessName.length() > 27) {
					int ind = businessName.lastIndexOf(businessName.substring(25, businessName.length()));
					if (ind >= 0)
						businessName = new StringBuilder(businessName).replace(ind, businessName.length(), "..").toString();
					messageBody = messageBody.replace(TemplateStaticTokens.BUSINESS_NAME_TOKEN, businessName);
				}
			}
			if (messageBody.contains(TemplateStaticTokens.BUSINESS_LOCATION_ALIAS_TOKEN)) {
				String locationAlias = StringUtils.isNotBlank(business.getBusinessAlias()) ? business.getBusinessAlias() : StringUtils.EMPTY;
				messageBody = messageBody.replace(TemplateStaticTokens.BUSINESS_LOCATION_ALIAS_TOKEN, locationAlias);
				if (messageBody.length() > 125 && locationAlias.length() > 27) {
					int ind = locationAlias.lastIndexOf(locationAlias.substring(25, locationAlias.length()));
					if (ind >= 0)
						locationAlias = new StringBuilder(locationAlias).replace(ind, locationAlias.length(), "..").toString();
					messageBody = messageBody.replace(TemplateStaticTokens.BUSINESS_LOCATION_ALIAS_TOKEN, locationAlias);
				}
			}
			if (messageBody.contains(TemplateStaticTokens.BUSINESS_PHONE_TOKEN)) {
				messageBody = messageBody.replace(TemplateStaticTokens.BUSINESS_PHONE_TOKEN, business.getPhone() != null ? business.getPhone() : StringUtils.EMPTY);
			}
			if (messageBody.contains(TemplateStaticTokens.BUSINESS_EMAIL_TOKEN)) {
				messageBody = messageBody.replace(TemplateStaticTokens.BUSINESS_EMAIL_TOKEN, business.getEmailId() != null ? business.getEmailId() : StringUtils.EMPTY);
			}
			if (messageBody.contains(TemplateStaticTokens.PRODUCT_NAME_TOKEN)) {
				String productName = cacheService.getSourceAliasForDefaultAggregation(business.getId());
				messageBody = messageBody.replace(TemplateStaticTokens.PRODUCT_NAME_TOKEN, productName != null ? productName : StringUtils.EMPTY);
			}
			if (messageBody.contains(TemplateStaticTokens.EMPLOYEE_NAME_TOKEN)) {
				String employeeName = null;
				if (reviewRequest instanceof ReviewRequest) {
					employeeName = businessDeeplinkService.getAssistedByEmployeeForReviewRequest((ReviewRequest) reviewRequest);
				} else {
					employeeName = businessDeeplinkService.getAssistedByEmployeeForPromotionRequest((Promotion) reviewRequest);
				}
				messageBody = messageBody.replace(TemplateStaticTokens.EMPLOYEE_NAME_TOKEN, employeeName != null ? employeeName : StringUtils.EMPTY);
			}
			if (messageBody.contains(TemplateStaticTokens.BUSINESS_ADDRESS_INLINE_TOKEN)) {
				String businessAddressInline = CoreUtils.getBusinessAddress(business);
				messageBody = messageBody.replace(TemplateStaticTokens.BUSINESS_ADDRESS_INLINE_TOKEN, businessAddressInline != null ? businessAddressInline : StringUtils.EMPTY);
			}
			
			if (StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.APPOINTMENT_FORM.getType())) {
				messageBody = messageBody.replace("<br/>", "\n");
				messageBody = messageBody.replace("<br />", "\n");
			}
			
			if (messageBody.contains(TemplateStaticTokens.WEBSITE_URL) && businessProfileInfo.getBusinessData() != null) {
				messageBody = messageBody.replace(TemplateStaticTokens.WEBSITE_URL,
						StringUtils.isNotEmpty(businessProfileInfo.getBusinessData().getWebsiteUrl()) ? businessProfileInfo.getBusinessData().getWebsiteUrl() : StringUtils.EMPTY);
			}
			
			User userDetail = null;
			if ((reviewRequest instanceof Promotion)) {
				if (isMessengerCampaign == null || isMessengerCampaign == 0) {
					retryOnShortenUrlError = campaignRescheduleService.isURLShorteningRetryLimitBelowThreshold(ReviewRequestTypeEnum.PROMOTIONAL.getType(), reviewRequest.getId(),
							retryOnShortenUrlError);
					messageBody = customCampaignService.replaceUrlTokenInSmsMessage(messageBody, smsTemplate.getId(), business, (Promotion) reviewRequest, retryOnShortenUrlError);
				} else {
					// for messenger campaigns, tokens will be stored at campaign level, not template level
					retryOnShortenUrlError = campaignRescheduleService.isURLShorteningRetryLimitBelowThreshold(ReviewRequestTypeEnum.PROMOTIONAL.getType(), reviewRequest.getId(),
							retryOnShortenUrlError);
					messageBody = customCampaignService.replaceUrlTokenInFreeTextSms(messageBody, reviewRequest.getCampaignId(), business, (Promotion) reviewRequest, retryOnShortenUrlError);
				}
				
				// replacing your name token for custom sms
				if (reviewRequest.getCampaignId() != null) {
					Campaign campaign = campaignSetupCachingService.getCampaignById(reviewRequest.getCampaignId());
					userDetail = cacheService.getUserDetailByUserId(campaign.getCreatedBy());
//					String user = cacheService.getUserNameById(campaign.getCreatedBy());
					messageBody = messageBody.replace(Constants.YOUR_NAME_TOKEN, userDetail != null ? userDetail.getName() : StringUtils.EMPTY);
					if(userId == null) {
						userId = campaign.getCreatedBy();
					}
				} else if(userId != null){
//					String user = cacheService.getUserNameById(userId);
					userDetail = cacheService.getUserDetailByUserId(userId);
					messageBody = messageBody.replace(Constants.YOUR_NAME_TOKEN, userDetail != null ? userDetail.getName() : StringUtils.EMPTY);
				}
				
				// replacing the <BR> with space as we can't support line break mobile phone.
				if (CampaignUtils.isCampaignSMSNextLineSupportEnabled()) {
					messageBody = messageBody.replace("<br />", "\n");
				} else {
					messageBody = messageBody.replace("<br />", " ");
				}
			}
			if ((reviewRequest instanceof ReviewRequest) && (isAppointmentCampaign != null && isAppointmentCampaign == 1)) {
				if (StringUtils.equalsIgnoreCase(CampaignTypeEnum.APPOINTMENT_FORM.getType(), reviewRequest.getRequestType())) {
					List<CustomCampaignUrl> customCampaignUrls = customCampaignCachedService.getFreeTextCampaignUrl(reviewRequest.getCampaignId());
					if (CollectionUtils.isNotEmpty(customCampaignUrls)) {
						for (CustomCampaignUrl customCampaignUrl : customCampaignUrls) {
							String shortenUrl = getAppointmentFormShortenUrl(customCampaignUrl.getUrlValue(), reviewRequest, business);
							if (StringUtils.isBlank(shortenUrl)) {
								logger.error("Url Shortening returned empty url and therefore not sending Free Text SMS for request id : {}", reviewRequest.getId());
								throw new SmsUrlShortenException(ErrorCodes.URL_SHORTEN_ERROR, "Url Shortening returned empty url");
							}
							messageBody = messageBody.replace(customCampaignUrl.getUrlToken(), shortenUrl);
						}
					}
				}
				
			}
			if(userId != null) {
				userDetail = cacheService.getUserDetailByUserId(userId);
			}
			messageBody = messageBody.replace(TemplateStaticTokens.YOUR_FIRST_NAME, userDetail != null ? StringUtils.defaultString(userDetail.getFirstName()) : StringUtils.EMPTY);
			messageBody = messageBody.replace(TemplateStaticTokens.YOUR_LAST_NAME, userDetail != null ? StringUtils.defaultString(userDetail.getLastName()) : StringUtils.EMPTY);
			
		}
		return messageBody;
	}
	
	private String extractFirstNameOnly(String fullName) {

		if (fullName == null) {
			return "";
		}

		StringBuilder firstName = new StringBuilder();
		if (StringUtils.contains(fullName, " ")) {
			firstName.append(StringUtils.substringBefore(fullName, " "));
		} else {
			firstName.append(fullName);
		}
		return StringEscapeUtils.unescapeHtml4((firstName.toString()));
	}

	private String getRequestTypeAcronym(String requestType) {
		if (requestType.contains(Constants.CX_TEMPLATE_TYPE)) {
			return Constants.TEMPLATE_TYPE_CX_ACRONYM;
		} else if (requestType.contains(Constants.SURVEY_REQUEST_TEMPLATE_TYPE)) {
			return Constants.TEMPLATE_TYPE_SR_ACRONYM;
		} else if (requestType.contains(Constants.REFERRAL_REQUEST_TEMPLATE_TYPE)) {
			return Constants.TEMPLATE_TYPE_REFERRAL_ACRONYM;
		} else if (StringUtils.equalsIgnoreCase(Constants.APPOINTMENT_REMINDER_TYPE, requestType)) {
			return Constants.TEMPLATE_TYPE_APPOINTMENT_REMINDER_ACRONYM;
		} else if (StringUtils.equalsIgnoreCase(Constants.TEMPLATE_TYPE_APPOINTMENT_RECALL_ACRONYM, requestType)) {
			return Constants.TEMPLATE_TYPE_APPOINTMENT_RECALL_ACRONYM;
		} else if (StringUtils.equalsIgnoreCase(Constants.TEMPLATE_TYPE_APPOINTMENT_FORM_ACRONYM, requestType)) {
			return Constants.TEMPLATE_TYPE_APPOINTMENT_FORM_ACRONYM;
		} else {
			return Constants.TEMPLATE_TYPE_RR_ACRONYM;
		}
	}

	@Override
	public MessengerCampaign validateMessengerCampaign(BaseCommunicationEntity reviewRequest) {
		MessengerCampaign messengerCampaign = messengerCampaignService.getMessengerCampaignData(reviewRequest.getCampaignId());
		if (messengerCampaign == null) {
			throw new CampaignException(ErrorCodes.INVALID_CAMPAIGN, "Invalid Messenger Campaign" + reviewRequest.getCampaignId());
		}
		return messengerCampaign;
	}

	private List<String> getCountryCodes(BusinessEnterpriseEntity business, boolean defaultCodes) {
		List<String> codes = new ArrayList<>();
		String countryCode = getCountryCodeForBusiness(business);
		if (countryCode != null && ("US".equals(countryCode) || "CA".equals(countryCode)) && defaultCodes) {
			codes.add("US");
			codes.add("CA");
		} else {
			codes.add(countryCode);
		}
		return codes;
	}

	public String getCountryCodeForBusiness(BusinessEnterpriseEntity business) {
		if (business == null) {
			return null;
		}

		Optional<Location> location = locationRepo.findById(business.getLocationId());
		if (location.isPresent())
			return CoreUtils.getCountryCodeForLocation(location.get());

		return null;
	}

	
	private SmsDto saveSmsMessage(CampaignSMSProperties smsProperties, String fromNumber) throws Exception {
		logger.info("sms message params: from business:{} to customer:{} request Id :{} mediaurl:{}", smsProperties.getBusiness(), smsProperties.getCustomer(), smsProperties.getReviewRequest().getId(), smsProperties.getMediaUrl());
		SmsDto sms = new SmsDto();
		sms.setMessageBody(smsProperties.getSmsBody());
		List<String> countryCodes = getCountryCodes(smsProperties.getBusiness(), true);
		logger.info("#1 fromBusinessSms: {}, countryCodes: {}", smsProperties.getBusiness(), countryCodes);
		sms.setFromNumber(CoreUtils.getFormattedBusinessPhoneNumber(fromNumber, countryCodes));
		// if (toCustomer != null) {
		// List<String> ccs = new ArrayList<>();
		// if (toCustomer.getLocId() == null || locationRepo.findFirstById(toCustomer.getLocId()) == null) {
		// ccs = getCountryCodes(business, false);
		// } else {
		// ccs.add(toCustomer.getCountryCode());
		// }
		// logger.info("#2 toCustomer: {}, countryCodes: {}", toCustomer, ccs);
		// sms.setToNumber(CoreUtils.getFormattedBusinessPhoneNumber(CoreUtils.formatPhoneNumber(toCustomer.getPhone(), toCustomer.getCountryCode()), ccs));
		// }
		
		sms.setBusinessId(smsProperties.getBusiness().getId());
		
		sms.setCustomerId(smsProperties.getCustomer().getId());
		sms.setToNumber(smsProperties.getCustomer().getPhoneE164());
		
		sms.setCreateDate(new Date());
		sms.setSentOn(new Date());
		sms.setMediaURL(smsProperties.getMediaUrl());
		sms.setReviewRequestId(smsProperties.getReviewRequest().getId());
		try {
			String encryptionEnabled = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("encryption.enabled");
			if (("true").equalsIgnoreCase(encryptionEnabled)) {
				if (StringUtils.isNotEmpty(smsProperties.getSmsBody())) {
					sms.setEncrypted(1);
					sms.setMessageBody(EncryptionUtils.encrypt(smsProperties.getSmsBody(), StringUtils.join(sms.getFromNumber(), sms.getToNumber()), StringUtils.join(sms.getToNumber(), sms.getFromNumber())));
				}
			} else {
				sms.setEncrypted(0);
			}
		} catch (Exception e) {
			logger.error("Encryption failed for sms body with request id {}, {}", smsProperties.getReviewRequest().getId(), sms.getBusinessId());
		}

		return sms;
	}

	private SmsDto saveSmsMessage(BusinessEnterpriseEntity business, String fromNumber, KontactoDTO toCustomer, Long requestId, String smsBody, String mediaUrl) throws Exception {
		logger.info("sms message params: from business:{} to customer:{} request Id:{} mediaurl:{}", business.getId(), toCustomer, requestId, mediaUrl);
		SmsDto sms = new SmsDto();
		sms.setMessageBody(smsBody);
		List<String> countryCodes = getCountryCodes(business, true);
		logger.info("#1 fromBusinessSms: {}, countryCodes: {}", business, countryCodes);
		sms.setFromNumber(CoreUtils.getFormattedBusinessPhoneNumber(fromNumber, countryCodes));
		// if (toCustomer != null) {
		// List<String> ccs = new ArrayList<>();
		// if (toCustomer.getLocId() == null || locationRepo.findFirstById(toCustomer.getLocId()) == null) {
		// ccs = getCountryCodes(business, false);
		// } else {
		// ccs.add(toCustomer.getCountryCode());
		// }
		// logger.info("#2 toCustomer: {}, countryCodes: {}", toCustomer, ccs);
		// sms.setToNumber(CoreUtils.getFormattedBusinessPhoneNumber(CoreUtils.formatPhoneNumber(toCustomer.getPhone(), toCustomer.getCountryCode()), ccs));
		// }

		sms.setBusinessId(business.getId());
		if (toCustomer != null) {
			sms.setCustomerId(toCustomer.getId());
			sms.setToNumber(toCustomer.getPhoneE164());
		}
		sms.setCreateDate(new Date());
		sms.setSentOn(new Date());
		sms.setMediaURL(mediaUrl);
		sms.setReviewRequestId(requestId);
		try {
			String encryptionEnabled = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("encryption.enabled");
			if (("true").equalsIgnoreCase(encryptionEnabled)) {
				if (StringUtils.isNotEmpty(smsBody)) {
					sms.setEncrypted(1);
					sms.setMessageBody(EncryptionUtils.encrypt(smsBody, StringUtils.join(sms.getFromNumber(), sms.getToNumber()), StringUtils.join(sms.getToNumber(), sms.getFromNumber())));
				}
			} else {
				sms.setEncrypted(0);
			}
		} catch (Exception e) {
			logger.error("Encryption failed for sms body with request id {}, {}", requestId, sms.getBusinessId());
		}

		return sms;
	}

	/**
	 * Send SMS Via Nexus
	 * 
	 * @param sms
	 * @param reviewRequest
	 * @param rType
	 * @param surveyId
	 * @param surveyType
	 * @param questionId
	 * @param applyDnd 
	 * @param quickSend 
	 * @param businessTimezone 
	 * @return
	 */
	private SmsDto sendSMSViaNexus(SmsDto sms, BaseCommunicationEntity reviewRequest, String rType, MessengerMediaInfo messengerMediaInfo, String surveyType, Integer surveyId,
			Integer questionId, boolean applyDnd, BusinessEnterpriseEntity business, boolean quickSend) {
		if (NPS.equalsIgnoreCase(surveyType)) {
			// call messenger to create pulse context. BIRDEYE-82010
			createPuslseSurveyContext(sms, reviewRequest, messengerMediaInfo, surveyType, surveyId, questionId, business, rType);
		}
		BusinessOptionsResponse businessOptionsResponse = cacheService.getBusinessOptionsCached(business.getId(), true);
		boolean freeTrialProductSMS = freeTrialService.isFreeTrialProductComm(reviewRequest, businessOptionsResponse);
		smsService.sendSMS(sms, getSMSParamsMap(reviewRequest.getId(), rType), applyDnd, business, quickSend, freeTrialProductSMS);
		sms.setSentOn(new Date());
		if (sms.getCustomerId() > 0) {
			pushSmsEventToMessengerES(sms, reviewRequest, messengerMediaInfo, surveyType, surveyId, questionId, business, rType);
		}
		return sms;
	}

	private SmsDto sendSMSViaNexus(SmsDto sms, CampaignSMSProperties smsProperties) {
		if (NPS.equalsIgnoreCase(smsProperties.getSurveyType())) {
			// call messenger to create pulse context. BIRDEYE-82010
			createPuslseSurveyContext(sms, smsProperties);
		}
		
		smsService.sendSMS(sms,smsProperties);
		//getSMSParamsMap(reviewRequest.getId(), rType), applyDnd, business, quickSend);
		sms.setSentOn(new Date());
		if (sms.getCustomerId() > 0) {
			pushSmsEventToMessengerES(sms, smsProperties);
		}
		return sms;
	}

	private void createPuslseSurveyContext(SmsDto sms, BaseCommunicationEntity reviewRequest, MessengerMediaInfo messengerMediaInfo, String surveyType, Integer surveyId, Integer questionId,
			BusinessEnterpriseEntity business, String rType) {
		logger.info("creating pulse survey context for messenger for review request id : {}", sms.getReviewRequestId());
		Campaign campaign = reviewRequest.getCampaignId() != null ? campaignSetupCachingService.getCampaignById(reviewRequest.getCampaignId()) : null;
		if (campaign != null) {
			MessengerSmsEntity messengerSmsEntity = new MessengerSmsEntity(reviewRequest.getCustId(), null, campaign.getId(), reviewRequest.getId(), campaign.getRunType(),
					String.valueOf(campaign.getCreatedBy()), messengerMediaInfo, surveyType, surveyId, questionId, business.getId(), business.getEnterpriseIdElseBusinessId(),
					reviewRequest.getRequestType(), rType);
			createSmsData(sms, messengerSmsEntity);
			messengerExternalService.createPuslseSurveyContext(messengerSmsEntity);
		} else {
			String userId = reviewRequestService.getUserIdForReviewRequestId(String.valueOf(reviewRequest.getId()));
			logger.info("For rrid : {}  userid is {}", reviewRequest.getId(), userId);
			MessengerSmsEntity messengerSmsEntity = new MessengerSmsEntity(null, -100, reviewRequest.getId(), userId, surveyType, surveyId, questionId, business.getId(),
					business.getEnterpriseIdElseBusinessId(), reviewRequest.getRequestType(), rType);
			createSmsData(sms, messengerSmsEntity);
			messengerExternalService.createPuslseSurveyContext(messengerSmsEntity);
		}
	}

	private void createPuslseSurveyContext(SmsDto sms, CampaignSMSProperties smsProperties) {
		logger.info("creating pulse survey context for messenger for review request id : {}", sms.getReviewRequestId());
		BaseCommunicationEntity reviewRequest = smsProperties.getReviewRequest();
		Campaign campaign = reviewRequest.getCampaignId() != null ? campaignSetupCachingService.getCampaignById(reviewRequest.getCampaignId()) : null;
		if (campaign != null) {
			MessengerSmsEntity messengerSmsEntity = new MessengerSmsEntity(reviewRequest.getCustId(), null, campaign.getId(), reviewRequest.getId(), campaign.getRunType(),
					String.valueOf(campaign.getCreatedBy()), smsProperties.getMessengerMediaInfo(), smsProperties.getSurveyType(), smsProperties.getSurveyId(), smsProperties.getQuestionId(), smsProperties.getBusiness().getId(), smsProperties.getBusiness().getEnterpriseIdElseBusinessId(),
					reviewRequest.getRequestType(), smsProperties.getRequestType());
			createSmsData(sms, messengerSmsEntity);
			messengerExternalService.createPuslseSurveyContext(messengerSmsEntity);
		} else {
			String userId = reviewRequestService.getUserIdForReviewRequestId(String.valueOf(reviewRequest.getId()));
			logger.info("For rrid : {}  userid is {}", reviewRequest.getId(), userId);
			MessengerSmsEntity messengerSmsEntity = new MessengerSmsEntity(null, -100, reviewRequest.getId(), userId, smsProperties.getSurveyType(), smsProperties.getSurveyId(), smsProperties.getQuestionId(), smsProperties.getBusiness().getId(),
					smsProperties.getBusiness().getEnterpriseIdElseBusinessId(), reviewRequest.getRequestType(), smsProperties.getRequestType());
			createSmsData(sms, messengerSmsEntity);
			messengerExternalService.createPuslseSurveyContext(messengerSmsEntity);
		}
	}

	/**
	 * Get SMS Parameters Map
	 * 
	 * @param requestId
	 * @param rType
	 * @return
	 */
	private static Map<String, Serializable> getSMSParamsMap(Long requestId, String rType) {
		Map<String, Serializable> params = new HashMap<>();
		if (requestId != null) {
			params.put("rid", requestId);
		}
		if (StringUtils.isNotBlank(rType)) {
			params.put("rType", rType);
		}
		return params;
	}

	private void pushSmsEventToMessengerES(SmsDto sms, CampaignSMSProperties smsProperties) {
		logger.info("pushing in kafka sms with review request id : {} to messenger ES", sms.getReviewRequestId());
		BaseCommunicationEntity reviewRequest = smsProperties.getReviewRequest();
		Campaign campaign = reviewRequest.getCampaignId() != null ? campaignSetupCachingService.getCampaignById(reviewRequest.getCampaignId()) : null;
		if (campaign != null) {
			MessengerSmsEntity messengerSmsEntity = getMessengerSMSEntity(reviewRequest, campaign, sms, smsProperties.getMessengerMediaInfo() ,smsProperties.getSurveyType(), smsProperties.getSurveyId(), smsProperties.getQuestionId(), smsProperties.getBusiness(), smsProperties.getRequestType());
			
			// BIRDEYE-102769 - To handle slowness in syncing campaign messages to inbox(BIRDEYE-100680).
			pushPromotionCampaignEventToMessenger(messengerSmsEntity);
			
			 //BIRD-108311
			pushAppointmentReminderEventToMessenger(messengerSmsEntity);
			kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.SMS_MESSENGER, new KafkaMessage(messengerSmsEntity));
			
			
		} else {
			String userId = reviewRequestService.getUserIdForReviewRequestId(String.valueOf(reviewRequest.getId()));
			logger.info("For rrid : {}  userid is {}", reviewRequest.getId(), userId);
			MessengerSmsEntity messengerSmsEntity = new MessengerSmsEntity(null, -100, reviewRequest.getId(), userId, smsProperties.getSurveyType(), smsProperties.getSurveyId(), smsProperties.getQuestionId(), smsProperties.getBusiness().getId(),
					smsProperties.getBusiness().getEnterpriseIdElseBusinessId(), reviewRequest.getRequestType(), smsProperties.getRequestType(), reviewRequest.getCustId(), new Date());
			createSmsData(sms, messengerSmsEntity);
			
			if (StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.APPOINTMENT_REMINDER.getType())) {
				AppointmentRRMapping audit = appointmentReminderAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
				messengerSmsEntity.setAppointmentId(audit != null ? audit.getAppointmentId() : null);
				// These flags indicate which options should be enabled for the user:
				// - "confirmTextEnabled": Enables the "confirm" option if confirmTextAllowed is true
				// - "rescheduleTextEnabled": Enables the "reschedule" option if rescheduleTextAllowed is true
				// - "cancelTextEnabled": Enables the "cancel" option if cancelTextAllowed is true
				// The messenger will read these flags to determine available actions for the user.
				messengerSmsEntity.setAppointmentConfirmOptionEnabled(Boolean.valueOf(MDC.get("confirmTextEnabled")));
				messengerSmsEntity.setAppointmentCancelOptionEnabled(Boolean.valueOf(MDC.get("cancelTextEnabled")));
				messengerSmsEntity.setAppointmentRescheduleOptionEnabled(Boolean.valueOf(MDC.get("rescheduleTextEnabled")));
			}			
			//BIRD-108311
			pushAppointmentReminderEventToMessenger(messengerSmsEntity);
			kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.SMS_MESSENGER, new KafkaMessage(messengerSmsEntity));
		}	
	}
	
	private void pushSmsEventToMessengerES(SmsDto sms, BaseCommunicationEntity reviewRequest, MessengerMediaInfo messengerMediaInfo, String surveyType, Integer surveyId,
			Integer questionId, BusinessEnterpriseEntity business, String rType) {
		logger.info("pushing in kafka sms with review request id : {} to messenger ES", sms.getReviewRequestId());
		Campaign campaign = reviewRequest.getCampaignId() != null ? campaignSetupCachingService.getCampaignById(reviewRequest.getCampaignId()) : null;
		if (campaign != null) {
			MessengerSmsEntity messengerSmsEntity = getMessengerSMSEntity(reviewRequest, campaign, sms, messengerMediaInfo, surveyType, surveyId, questionId, business, rType);
			
			// BIRDEYE-102769 - To handle slowness in syncing campaign messages to inbox(BIRDEYE-100680).
			pushPromotionCampaignEventToMessenger(messengerSmsEntity);
			//BIRD-108311
			pushAppointmentReminderEventToMessenger(messengerSmsEntity);
			kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.SMS_MESSENGER, new KafkaMessage(messengerSmsEntity));
			
			
		} else {
			String userId = reviewRequestService.getUserIdForReviewRequestId(String.valueOf(reviewRequest.getId()));
			logger.info("For rrid : {}  userid is {}", reviewRequest.getId(), userId);
			MessengerSmsEntity messengerSmsEntity = new MessengerSmsEntity(null, -100, reviewRequest.getId(), userId, surveyType, surveyId, questionId, business.getId(),
					business.getEnterpriseIdElseBusinessId(), reviewRequest.getRequestType(), rType, reviewRequest.getCustId(), new Date());
			
			createSmsData(sms, messengerSmsEntity);
			
			if (StringUtils.equalsIgnoreCase(reviewRequest.getRequestType(), CampaignTypeEnum.APPOINTMENT_REMINDER.getType())) {
				AppointmentRRMapping audit = appointmentReminderAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
				messengerSmsEntity.setAppointmentId(audit != null ? audit.getAppointmentId() : null);
				// These flags indicate which options should be enabled for the user:
				// - "confirmTextEnabled": Enables the "confirm" option if confirmTextAllowed is true
				// - "rescheduleTextEnabled": Enables the "reschedule" option if rescheduleTextAllowed is true
				// - "cancelTextEnabled": Enables the "cancel" option if cancelTextAllowed is true
				// The messenger will read these flags to determine available actions for the user.
				messengerSmsEntity.setAppointmentConfirmOptionEnabled(Boolean.valueOf(MDC.get("confirmTextEnabled")));
				messengerSmsEntity.setAppointmentCancelOptionEnabled(Boolean.valueOf(MDC.get("cancelTextEnabled")));
				messengerSmsEntity.setAppointmentRescheduleOptionEnabled(Boolean.valueOf(MDC.get("rescheduleTextEnabled")));
			}
			//BIRD-108311
			pushAppointmentReminderEventToMessenger(messengerSmsEntity);
			kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.SMS_MESSENGER, new KafkaMessage(messengerSmsEntity));
		}
	}
	
	/**
	 * Prepare Sms related data to be sent to messenger in MessengerEntity Object
	 * 
	 * @param smsData
	 * @param message
	 * @return
	 */
	private void createSmsData(SmsDto smsData, MessengerSmsEntity message) {
		MessengerSmsEntity.SmsData sms = new MessengerSmsEntity.SmsData();
		message.createSmsData(sms, smsData);
		message.setSmsData(sms);
	}

	private MessengerSmsEntity getMessengerSMSEntity(BaseCommunicationEntity reviewRequest, Campaign campaign, SmsDto sms, MessengerMediaInfo messengerMediaInfo, String surveyType, Integer surveyId,
			Integer questionId, BusinessEnterpriseEntity business, String rType) {
		MessengerSmsEntity message = new MessengerSmsEntity();
		message.setCustomerId(reviewRequest.getCustId());
		message.setReviewRequestId(reviewRequest.getId());
		message.setRequestType(reviewRequest.getRequestType());
		message.setCampaignId(campaign.getId());
		message.setType(campaign.getRunType());
		message.setUserId(String.valueOf(campaign.getCreatedBy()));
		// get appointment id
		if (StringUtils.equalsIgnoreCase(campaign.getCampaignType(), CampaignTypeEnum.APPOINTMENT_REMINDER.getType())) {
			AppointmentRRMapping audit = appointmentAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
			message.setAppointmentId(audit != null ? audit.getAppointmentId() : null);
			// These flags indicate which options should be enabled for the user:
			// - "confirmTextEnabled": Enables the "confirm" option if confirmTextAllowed is true
			// - "rescheduleTextEnabled": Enables the "reschedule" option if rescheduleTextAllowed is true
			// - "cancelTextEnabled": Enables the "cancel" option if cancelTextAllowed is true
			// The messenger will read these flags to determine available actions for the user.
			message.setAppointmentConfirmOptionEnabled(Boolean.valueOf(MDC.get("confirmTextEnabled")));
			message.setAppointmentCancelOptionEnabled(Boolean.valueOf(MDC.get("cancelTextEnabled")));
			message.setAppointmentRescheduleOptionEnabled(Boolean.valueOf(MDC.get("rescheduleTextEnabled")));
		}
		//message.setSmsId(sms.getSmsId());
		createSmsData(sms, message);
		message.setMediaInfo(messengerMediaInfo);
		message.setSurveyType(surveyType);
		message.setSurveyId(surveyId);
		message.setQuestionId(questionId);
		message.setLocationId(business.getId());
		message.setEnterpriseId(CoreUtils.getSmbOrEnterpriseId(business));
		message.setrType(rType);
		message.setSentOn(new Date());
		return message;
	}

	private void pushPromotionCampaignEventToMessenger(MessengerSmsEntity messengerSmsEntity) {
		if (CampaignTypeEnum.PROMOTIONAL.getType().equalsIgnoreCase(messengerSmsEntity.getRequestType())) {
			messengerExternalService.postCampaignEventToMessengerRedis(messengerSmsEntity);
		}
	}
	
	private void pushAppointmentReminderEventToMessenger(MessengerSmsEntity messengerSmsEntity) {
		if (CampaignTypeEnum.APPOINTMENT_REMINDER.getType().equalsIgnoreCase(messengerSmsEntity.getRequestType())) {
			messengerExternalService.pushAppointmentReminderEventToMessenger(messengerSmsEntity);
		}
	}

	@Async
	@Override
	public void sendReferralThankYouSms(BusinessEnterpriseEntity business, ReferralAppointmentTemplateDetails templateData, KontactoDTO customer, Appointment appointment) {

		logger.info("Sending thank you text to lead id : {}, for business : {}, from customer : {}", appointment.getId(), business.getId(), customer.getId());

		SmsDto sms = new SmsDto();
		String smsBody = StringUtils.join(templateData.getTyHeading(), templateData.getTyMessage(), templateData.getTyCtaUrl()).replace("<br />", " ");
		if (StringUtils.isBlank(smsBody)) {
			return;
		}
		sms.setMessageBodyUnencrypted(smsBody);
		sms.setBusinessId(business.getId());

		// getting business sms number.
		List<String> businessSmsNumbers = businessSmsRepo.getBusinessSmsNumberById(business.getId());
		if (CollectionUtils.isEmpty(businessSmsNumbers)) {
			logger.error("no sms number exists for business id : {}", business.getId());
			throw new CampaignException(ErrorCodes.INVALID_SMS_NUMBER);
		}
		String businessSmsNumber = businessSmsNumbers.get(0);

		List<String> countryCodes = getCountryCodes(business, true);
		logger.info("#1 fromBusinessSms: {}, countryCodes: {}", business, countryCodes);
		sms.setFromNumber(CoreUtils.getFormattedBusinessPhoneNumber(businessSmsNumber, countryCodes));

		String defaultCountryCode = "US";
		AccountMessage accountMessage = businessExternalService.getAccountDetailsCached(business.getId(), new BusinessFilterDto());
		if (accountMessage != null && CollectionUtils.isNotEmpty(accountMessage.getSupportedCountries())) {
			defaultCountryCode = accountMessage.getSupportedCountries().iterator().next();
		}

		if (!PhoneNoValidator.isAnyValidPhoneNumber(CoreUtils.formatPhoneNumber(appointment.getPhoneNumber(), defaultCountryCode), countryCodes)) {
			logger.error("Could not send SMS to lead as phone number is invalid for lead id {}", appointment.getId());
			throw new CampaignException(ErrorCodes.INVALID_SMS_NUMBER, "phone number is invalid");
		}

		sms.setToNumber(CoreUtils.getFormattedBusinessPhoneNumber(CoreUtils.formatPhoneNumber(appointment.getPhoneNumber(), defaultCountryCode), countryCodes));
		sms.setCustomerId(appointment.getCid());
		sms.setBusinessId(business.getId());
		sms.setSmsId(appointment.getId()); // just setting to add it in externalUid in sms audit. not saving in sms table.
        sms.setReviewRequestId(appointment.getId().longValue()); // just setting it to add in external uid.
		smsService.sendSMS(sms, getSMSParamsMap(appointment.getId().longValue(), "lead_thankyou"), false, business, false, false);
		
	}
	
	@Override
	public void sendLeadSuspectSMS(BusinessEnterpriseEntity business, Appointment appointment, String smsBody) {
		logger.info("Sending text msg to lead id : {}, for business : {}, from customer : {}", appointment.getId(), business.getId(), appointment.getCid());
		
		if (StringUtils.isBlank(smsBody)) {
			return;
		}
		
		String businessSmsNumber = getBusinessSMSNumber(business);
		if (StringUtils.isBlank(businessSmsNumber)) {
			logger.error("no sms number exists for business id : {}", business.getId());
			throw new CampaignException(ErrorCodes.INVALID_SMS_NUMBER);
		}
		SmsDto sms = new SmsDto();
		sms.setMessageBodyUnencrypted(smsBody);
		sms.setBusinessId(business.getId());
		
		List<String> countryCodes = getCountryCodes(business, true);
		logger.info("#1 fromBusinessSms: {}, countryCodes: {}", business, countryCodes);
		sms.setFromNumber(CoreUtils.getFormattedBusinessPhoneNumber(businessSmsNumber, countryCodes));
		
		String defaultCountryCode = "US";
		AccountMessage accountMessage = businessExternalService.getAccountDetailsCached(business.getId(), new BusinessFilterDto());
		if (accountMessage != null && CollectionUtils.isNotEmpty(accountMessage.getSupportedCountries())) {
			defaultCountryCode = accountMessage.getSupportedCountries().stream().findFirst().get();
		}
		
		if (!PhoneNoValidator.isAnyValidPhoneNumber(CoreUtils.formatPhoneNumber(appointment.getPhoneNumber(), defaultCountryCode), countryCodes)) {
			logger.error("Could not send SMS to lead as phone number is invalid for lead id {}", appointment.getId());
			throw new CampaignException(ErrorCodes.INVALID_SMS_NUMBER, "phone number is invalid");
		}
		
		sms.setToNumber(CoreUtils.getFormattedBusinessPhoneNumber(CoreUtils.formatPhoneNumber(appointment.getPhoneNumber(), defaultCountryCode), countryCodes));
		sms.setCustomerId(appointment.getCid());
		sms.setBusinessId(business.getId());
		sms.setSmsId(appointment.getId()); // just setting to add it in externalUid in sms audit. not saving in sms table.
		sms.setReviewRequestId(appointment.getId().longValue()); // just setting it to add in external uid.
		smsService.sendSMS(sms, getSMSParamsMap(appointment.getId().longValue(), Constants.REFERRAL_LEAD_SUSPECT_SMS_TYPE), true, business, false, false);
	}

	
	private String getBusinessSMSNumber(BusinessEnterpriseEntity business) {
		List<String> businessSmsNumbers = businessSmsRepo.getBusinessSmsNumberById(business.getId());
		if (CollectionUtils.isEmpty(businessSmsNumbers)) {
			logger.error("no sms number exists for business id : {}", business.getId());
			throw new CampaignException(ErrorCodes.INVALID_SMS_NUMBER);
		}
		return businessSmsNumbers.get(0);
	}
	
	/**
	 * Generates a shortened URL for an appointment form SMS based on the provided parameters.
	 *
	 * @param business The business enterprise entity.
	 * @param reviewRequest The review request entity.
	 * @param formUrl The URL of the appointment form.
	 * @param encodedAppointmentId The encoded appointment ID.
	 * @return The shortened URL for the appointment form SMS, or null if an error occurs or no URL is available.
	 */
	private String getShortenURLforAppointmentFormSms(BusinessEnterpriseEntity business, ReviewRequest reviewRequest, String formUrl, String encodedAppointmentId) {
		BusinessProfileResponse businessInfo = cacheService.getBusinessProfileByBusinessId(business.getId());

		String encodedCustomerId = deeplinkURLGenerationService.getEncryptedCustomerId(reviewRequest.getId(), reviewRequest.getCustId());
		String redirectUrl = StringUtils.EMPTY;
		formUrl = CampaignUtils.replaceTokensWithBusinessProfileValue(businessInfo, formUrl);
		try {
			if(StringUtils.isNotEmpty(formUrl)) {
				redirectUrl = URLEncoder.encode(new StringBuilder().append(formUrl).append("?").append("reviewRequestId=").append(reviewRequest.getId()).append("&appointmentId=")
						.append(encodedAppointmentId).append("&businessNumber=").append(business.getBusinessId()).toString(), "UTF-8");
			} else {
				// If no form URL is available, retrieve and encode the custom template URL if present.
				redirectUrl =  getCustomTemplateUrlByTemplateId(reviewRequest.getTemplateId());
				if(StringUtils.isNotEmpty(redirectUrl)) {
					redirectUrl = URLEncoder.encode(redirectUrl, "UTF-8");
				}
			}
			
		} catch (Exception exception) {
			logger.warn("Error occurred while encoding form URL for review request id {}", reviewRequest.getId());
		}
		
		if(StringUtils.isNotEmpty(redirectUrl)) {
			StringBuilder url = new StringBuilder().append(businessOptionService.getPublicProfileURL(business)).append("/forms-appointment").append("?rid=").append(reviewRequest.getId())
					.append("&appointmentId=").append(encodedAppointmentId).append("&rtype=").append(reviewRequest.getRequestType()).append("&templateId=").append(reviewRequest.getTemplateId())
					.append("&clickType=1").append("&custId=").append(encodedCustomerId).append("&enc=").append(1).append("&r=").append(redirectUrl);
			String shortenUrl = businessExternalService.shortenUrl(url.toString(), CoreUtils.getSmbOrEnterpriseId(business));
			logger.info("Original deeplink url: {}, shorten url: {}", url, shortenUrl);
			return shortenUrl;
		}
		
		return null;
	}
	
	/**
	 * Retrieves the custom template URL by the provided template ID.
	 *
	 * @param templateId The ID of the template for which to retrieve the custom URL.
	 * @return The custom URL associated with the template, or null if no URL is found.
	 */
	@Override
	public String getCustomTemplateUrlByTemplateId(Integer templateId) {
		// Retrieve custom campaign URLs based on the template ID
		List<CustomCampaignUrl> customCampaignUrls = customCampaignCachedService.getCustomCampaignUrl(templateId);
		
		if (CollectionUtils.isNotEmpty(customCampaignUrls)) {
			 // Return the URL value of the first custom campaign URL
			return customCampaignUrls.get(0).getUrlValue();
		}
		return null;
	}
	
	/**

	 * Replace appointment custom field tokens for sms body.
	 * 
	 * @param formattedMsg
	 * @param customer
	 * @param templateId
	 */
	private void replaceAppointmentCustomFields(TextStringBuilder formattedMsg, Integer templateId, AppointmentDetailsResponse appointmentResponse,  CustomFieldSourceEnum customFieldSource) {
		List<CampaignCustomFieldAssociation> customFieldsByTemplate = customFieldAssociationRepo.getCampaignCustomFieldAssociationBySource(templateId, CustomFieldAssociatedObjectTypeEnum.getObjectTypeByCommType(Constants.TEMPLATE_BASE_TYPE_SMS), customFieldSource);
		if (CollectionUtils.isEmpty(customFieldsByTemplate)) {
			return;
		}
		Map<String, String> tokenMap = new HashMap<>();
		// adding all template level custom fields to token map with dot values.
		for (CampaignCustomFieldAssociation customField : customFieldsByTemplate) {
			tokenMap.put("[" + customField.getCustomFieldName() + "]", "...");
		}
		// overriding contact level custom fields to token map.
		for (Map.Entry<String, String> customField : AppointmentExternalUtils.getCustomFieldsMap(appointmentResponse).entrySet()) {
			if (StringUtils.isNotBlank(customField.getValue())) {
				tokenMap.put("[" + customField.getKey() + "]", customField.getValue());
			}
		}
		// replacing custom field tokens.
		for (Map.Entry<String, String> entry : tokenMap.entrySet()) {
			formattedMsg.replaceAll(entry.getKey(), entry.getValue());
		}

	}
	
	private void replaceBusinessTokens(TextStringBuilder formattedMsg, BusinessProfileResponse businessInfo, Integer smsTemplateId, BaseCommunicationEntity promotionOrRr,
			BusinessEnterpriseEntity business, boolean retryOnShortenUrlError) {
		Map<String, String> tokenMap = new HashMap<>();
		
		if (businessInfo != null) {
			if (businessInfo.getAdditionalData() != null) {
				tokenMap.put(TemplateStaticTokens.APPOINTMENT_LINK, businessInfo.getAdditionalData().getAppointmentLink());
			}
			if (businessInfo.getInternalUseData() != null) {
				tokenMap.put(TemplateStaticTokens.APPOINTMENT_FORM_LINK, businessInfo.getInternalUseData().getAppointmentFormLink());
			}
		}
		replaceLocationCustomFields(formattedMsg, smsTemplateId, businessInfo, CustomFieldSourceEnum.LOCATION, promotionOrRr, business, retryOnShortenUrlError);
		for (Map.Entry<String, String> map : tokenMap.entrySet()) {
			formattedMsg.replaceAll(map.getKey(), map.getValue());
		}
		
	}
	
	/**
	 * 
	 * 
	 * Replace Location Custom Fields For Sms Body Based On whether it is RR or Promotion
	 * 1. Check if Request is of Type ReviewRequest, Put all Custom tokens from API in token map
	 * 2. If request is Promotional then
	 * If token is of type url and it is not blank, prepare a shorten url and add it to token map.
	 * Else Add the token directly in token map.
	 * 
	 * @param business,
	 *            businessProfile, customFieldsByTemplate, promotionOrRr, retryOnShortenUrlError, tokenMap
	 * 			
	 */
	private void updateLocationCustomFieldValuesInTokenMap(BusinessEnterpriseEntity business, BusinessProfileResponse businessProfile, List<CampaignCustomFieldAssociation> customFieldsByTemplate,
			BaseCommunicationEntity promotionOrRr, boolean retryOnShortenUrlError, Map<String, String> tokenMap) {
		if (promotionOrRr == null || business == null || (promotionOrRr instanceof ReviewRequest)) {
			for (Map.Entry<String, String> customField : BusinessExternalUtils.getCustomFieldsMap(businessProfile).entrySet()) {
				if (StringUtils.isNotBlank(customField.getValue())) {
					tokenMap.put("[" + customField.getKey() + "]", customField.getValue());
				}
			}
			return;
		}
		
		Promotion promotionRequest = (Promotion) promotionOrRr;
		Map<String, CampaignCustomFieldAssociation> templateLocationFieldNameToObjectMap = customFieldsByTemplate.stream()
				.collect(Collectors.toMap(e -> e.getCustomFieldName(), e -> e, (first, second) -> second));
		Map<String, LocationCustomField> nameToLocationCustomFieldMap = BusinessExternalUtils.getNameToCustomFieldMap(businessProfile);
		retryOnShortenUrlError = campaignRescheduleService.isURLShorteningRetryLimitBelowThreshold(ReviewRequestTypeEnum.PROMOTIONAL.getType(), promotionRequest.getId(), retryOnShortenUrlError);
		for (Map.Entry<String, LocationCustomField> customField : nameToLocationCustomFieldMap.entrySet()) {
			LocationCustomField locationCustomField = customField.getValue();
			if (StringUtils.isNotBlank(locationCustomField.getFieldValue())) {
				if (StringUtils.equals(locationCustomField.getType(), "URL") && templateLocationFieldNameToObjectMap.containsKey(locationCustomField.getFieldName())) {
					String shortenUrl = customCampaignService.fetchShortenedUrl(business, promotionRequest, locationCustomField.getFieldValue(), retryOnShortenUrlError);
					tokenMap.put("[" + locationCustomField.getFieldName() + "]", shortenUrl);
				} else {
					tokenMap.put("[" + locationCustomField.getFieldName() + "]", locationCustomField.getFieldValue());
				}
			}
		}
	}
	
	/**

	 * Replace location custom field tokens for sms body.
	 * 
	 * @param formattedMsg
	 * @param customer
	 * @param templateId
	 */
	private void replaceLocationCustomFields(TextStringBuilder formattedMsg, Integer templateId, BusinessProfileResponse businessProfile,  CustomFieldSourceEnum customFieldSource, BaseCommunicationEntity promotionOrRr, BusinessEnterpriseEntity business, boolean retryOnShortenUrlError) {
		List<CampaignCustomFieldAssociation> customFieldsByTemplate = customFieldAssociationRepo.getCampaignCustomFieldAssociationBySource(templateId, CustomFieldAssociatedObjectTypeEnum.getObjectTypeByCommType(Constants.TEMPLATE_BASE_TYPE_SMS), customFieldSource);
		if (CollectionUtils.isEmpty(customFieldsByTemplate)) {
			return;
		}
		
		Map<String, String> tokenMap = new HashMap<>();
		// adding all template level custom fields to token map with dot values.
		for (CampaignCustomFieldAssociation customField : customFieldsByTemplate) {
			tokenMap.put("[" + customField.getCustomFieldName() + "]", "...");
		}
		
		// overriding contact level custom fields to token map.
		// BIRD-74183 | In case of Promotion Templates, Location Custom Tokens of Type Url should be replaced with Shorten Url for Tracking
		updateLocationCustomFieldValuesInTokenMap(business, businessProfile, customFieldsByTemplate, promotionOrRr, retryOnShortenUrlError, tokenMap);
		
		// replacing custom field tokens.
		for (Map.Entry<String, String> entry : tokenMap.entrySet()) {
			formattedMsg.replaceAll(entry.getKey(), entry.getValue());
		}

	}
	
	private String getAppointmentCampaignMessage(BusinessEnterpriseEntity business, BaseCommunicationEntity reviewRequest, KontactoDTO customer, MessengerCampaign messengerCampaign,
			BusinessSmsTemplate smsTemplate, boolean retryOnShortenUrlError) throws Exception {
		TextStringBuilder formattedMsg = new TextStringBuilder();
		BusinessProfileResponse businessProfileInfo = cacheService.getBusinessProfileByBusinessId(business.getId());
		String messageBody = replaceTokensInSmsBody(smsTemplate, business, messengerCampaign.getFreeText(), reviewRequest, 1, null, retryOnShortenUrlError, 1, businessProfileInfo);
		formattedMsg.append(messageBody);

		if (customer != null) {
			replaceCustomerTokens(formattedMsg,customer);

			formattedMsg.replaceAll(TemplateStaticTokens.REFERRER_NAME_TOKEN, customer.getCustomerName());
			formattedMsg.replaceAll(TemplateStaticTokens.REFERRAL_CODE_TOKEN, customer.getReferralCode() != null ? customer.getReferralCode() : StringUtils.EMPTY);
			
			replaceAppointmentTokens(formattedMsg, reviewRequest, business, null,smsTemplate, businessProfileInfo);
			// replace custom fields.
			replaceCustomFields(formattedMsg, customer, smsTemplate.getId());
						
			replaceBusinessTokens(formattedMsg, businessProfileInfo, smsTemplate.getId(), reviewRequest, business, retryOnShortenUrlError);
		}

		// BIRDEYE-49916 | Replacing any other token with empty string.
		return formattedMsg.toString().replaceAll("\\[[^\\]]*\\]", StringUtils.EMPTY);
	}
	
	@Override
	public void sendAppointmentSmsCampaign(BusinessEnterpriseEntity business, BaseCommunicationEntity reviewRequest, KontactoDTO customer, BusinessSmsTemplate smsTemplate) throws Exception {
		MessengerCampaign messengerCampaign = validateMessengerCampaign(reviewRequest);
		
		String formattedMsg = getAppointmentCampaignMessage(business, reviewRequest, customer, messengerCampaign, smsTemplate, true);
		
		boolean mmsSupported = businessOptionService.isMmsOpted(business);
		logger.info("Sending Appointment sms message: {} for request id: {}", formattedMsg, reviewRequest.getId());
		MessengerMediaInfo mediaInfo = CampaignUtils.getMediaInfoForMessengerCampaign(messengerCampaign);
		String mediaUrl = (mmsSupported && mediaInfo != null) ? mediaInfo.getUrl() : null;
		if ((reviewRequest instanceof Promotion)) {
			updatePromotionRequestSegmentCount((Promotion) reviewRequest, formattedMsg, mediaUrl, business.getCountryCode());
		}
		sendSmsToCustomer(business, customer, formattedMsg, reviewRequest, mediaUrl, Constants.PROMOTION_SMS_TYPE, mediaInfo, null, null, null, true, false);
		
	}
	
	private String getAppointmentFormShortenUrl(String url, BaseCommunicationEntity reviewRequest, BusinessEnterpriseEntity business) {
		if(StringUtils.isEmpty(url)) {
			return StringUtils.EMPTY;
		}
		try {
			String redirectUrl = URLEncoder.encode(url, "UTF-8");
			AppointmentRRMapping appointmentReminderRequestAudit = appointmentAuditService.getReminderRequestAuditByReviewRequestId(reviewRequest.getId());
			AppointmentInfoLiteDTO appointmentInfo = appointmentService.getAppointmentLiteById(appointmentReminderRequestAudit.getAppointmentId().toString(), business.getEnterpriseIdElseBusinessId(),
					false, false);
			if (appointmentInfo == null) {
				throw new CampaignException(ErrorCodes.NO_VALID_APOINTMENT_FOUND, ErrorCodes.NO_VALID_APOINTMENT_FOUND.getMessage());
			}
			String encodedCustomerId = deeplinkURLGenerationService.getEncryptedCustomerId(reviewRequest.getId(), reviewRequest.getCustId());
			if(StringUtils.isNotEmpty(redirectUrl)) {
				StringBuilder urlBuilder = new StringBuilder().append(businessOptionService.getPublicProfileURL(business)).append("/forms-appointment").append("?rid=").append(reviewRequest.getId())
						.append("&appointmentId=").append(appointmentInfo.getExtAppointmentId()).append("&rtype=").append(reviewRequest.getRequestType()).append("&templateId=").append(reviewRequest.getTemplateId())
						.append("&clickType=1").append("&custId=").append(encodedCustomerId).append("&enc=").append(1).append("&r=").append(redirectUrl);
				String shortenUrl = businessExternalService.shortenUrl(urlBuilder.toString(), CoreUtils.getSmbOrEnterpriseId(business));
				logger.info("Deeplink url: {}, shorten url: {}", urlBuilder, shortenUrl);
				return shortenUrl;
			}
		} catch (Exception e) {
			logger.warn("Exception while encoding form URL for review request id {}", reviewRequest.getId());
		}
		return StringUtils.EMPTY;
	}
}
