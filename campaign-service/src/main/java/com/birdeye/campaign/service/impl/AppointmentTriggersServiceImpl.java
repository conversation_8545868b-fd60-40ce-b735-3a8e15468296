package com.birdeye.campaign.service.impl;

import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.appointment.service.AppointmentService;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.dto.AppointmentInfoLiteDTO;
import com.birdeye.campaign.dto.TriggerFilter;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.CampaignCondition;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.platform.constant.CampaignTriggerTypeEnum;
import com.birdeye.campaign.platform.constant.CampaignTypeEnum;
import com.birdeye.campaign.service.AppointmentTriggersService;
import com.birdeye.campaign.service.CampaignSetupCachingService;
import com.birdeye.campaign.trigger.service.CampaignTriggerEventsService;
import com.birdeye.campaign.utils.MvelEvaluationUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class AppointmentTriggersServiceImpl implements AppointmentTriggersService {
	
	@Autowired
	private AppointmentService				appointmentService;
	
	@Autowired
	private CampaignSetupCachingService		campaignSetupCachingService;
	
	@Autowired
	private CampaignTriggerEventsService	campaignTriggerEventsService;
	
	@Qualifier("campaignObjectMapper")
	@Autowired
	private ObjectMapper					objectMapper;
	
	@Override
	public void validateAppointmentTriggersExecutionRequest(Integer appointmentId, Campaign campaign) {
		AppointmentInfoLiteDTO appointmentInfo = appointmentService.getAppointmentLiteById(appointmentId.toString(), campaign.getEnterpriseId(), true, true);
		if (appointmentInfo == null) {
			throw new CampaignException(ErrorCodes.NO_VALID_APOINTMENT_FOUND, ErrorCodes.NO_VALID_APOINTMENT_FOUND.getMessage());
		}
		
		// Check for automation types supported for specific triggers
		switch (CampaignTriggerTypeEnum.getCampaignTriggerTypeEnum(campaign.getTriggerType())) {
			case APPOINTMENT_CANCELED:
			case APPOINTMENT_MISSED:
				if (!StringUtils.equalsAnyIgnoreCase(campaign.getType(), CampaignTypeEnum.SURVEY_REQUEST.getType(), CampaignTypeEnum.CX_REQUEST.getType(),
						CampaignTypeEnum.PROMOTIONAL.getType())) {
					throw new CampaignException(ErrorCodes.INVALID_CAMPAIGN, "Invalid trigger " + campaign.getTriggerType() + " for campaign type " + campaign.getType());
				}
				break;
			
			case APPOINTMENT_BOOKED:
			case APPOINTMENT_COMPLETED:
				if (!StringUtils.equalsAnyIgnoreCase(campaign.getType(), CampaignTypeEnum.REVIEW_REQUEST.getType(), CampaignTypeEnum.SURVEY_REQUEST.getType(),
						CampaignTypeEnum.REFERRAL.getType(), CampaignTypeEnum.CX_REQUEST.getType(), CampaignTypeEnum.PROMOTIONAL.getType())) {
					throw new CampaignException(ErrorCodes.INVALID_CAMPAIGN, "Invalid trigger " + campaign.getTriggerType() + " for campaign type " + campaign.getType());
				}
				break;
			
			default:
				break;
		}
		
		// validate if appointment time is already passed for appointment_booked trigger
		if (StringUtils.equalsIgnoreCase(campaign.getTriggerType(), CampaignTriggerTypeEnum.APPOINTMENT_BOOKED.getType())
				&& BooleanUtils.isFalse(isValidRequestExecutionTime(appointmentInfo.getStartTime()))) {
			throw new CampaignException(ErrorCodes.APPOINTMENT_TIME_PASSED, ErrorCodes.APPOINTMENT_TIME_PASSED.getMessage());
		}
		
		// Check if future appointment is booked or not
		if (StringUtils.equalsAnyIgnoreCase(campaign.getTriggerType(), CampaignTriggerTypeEnum.APPOINTMENT_CANCELED.getType(), CampaignTriggerTypeEnum.APPOINTMENT_MISSED.getType())
				&& BooleanUtils.isTrue(appointmentInfo.getFutureAppointmentPresent())) {
			throw new CampaignException(ErrorCodes.FUTURE_APPOINTMENT_BOOKED, "Future appointment is present for the given appointment id :: " + appointmentId);
		}
		
		// validate MVEL expression
		List<TriggerFilter> triggerFilters = getTriggerFilters(appointmentInfo, campaign.getTriggerType());
		CampaignCondition campaignCondition = campaignSetupCachingService.getCampaignConditionByCampaign(campaign.getId(), campaign.getEnterpriseId());
		if (!MvelEvaluationUtils.evaluateTriggerMvelExpression(campaignCondition.getTriggerMvelExpression(), campaignCondition.getTriggerMvelParamsAndTypes(), triggerFilters, false, null)) {
			throw new CampaignException(ErrorCodes.NO_VALID_CAMPAIGN_CONDITION, ErrorCodes.NO_VALID_CAMPAIGN_CONDITION.getMessage());
		}
	}
	
	@SuppressWarnings("unchecked")
	private List<TriggerFilter> getTriggerFilters(AppointmentInfoLiteDTO appointmentInfo, String triggerType) {
		List<TriggerFilter> triggerFilters = new ArrayList<>();
		try {
			triggerFilters = campaignTriggerEventsService.extractAndPopulateTriggerFilterValues(objectMapper.convertValue(appointmentInfo, Map.class), triggerType);
		} catch (JsonProcessingException e) {
			throw new CampaignException(ErrorCodes.NO_VALID_CAMPAIGN_CONDITION, ErrorCodes.NO_VALID_CAMPAIGN_CONDITION.getMessage());
		}
		return triggerFilters;
	}
	
	private boolean isValidRequestExecutionTime(Long appointmentStartTimeEpochMillis) {
		ZonedDateTime zonedAppointmentStartTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(appointmentStartTimeEpochMillis), ZoneId.of("UTC"));
		ZonedDateTime currentTime = ZonedDateTime.ofInstant(Instant.now(), ZoneId.of("UTC"));
		Duration durationBeforeAppointment = Duration.between(currentTime, zonedAppointmentStartTime);
		return !durationBeforeAppointment.isNegative();
	}
	
}
