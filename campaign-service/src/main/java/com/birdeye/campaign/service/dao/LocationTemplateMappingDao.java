package com.birdeye.campaign.service.dao;

import java.util.List;
import java.util.Map;

import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.entity.LocationTemplateMapping;

public interface LocationTemplateMappingDao {
	
	void saveLocationtemplateMapping(Integer templateId, List<Integer> businessIds);

	List<LocationTemplateMapping> getTemplateBusinessMapping(List<Integer> templateIds, List<Integer> businessIds);

	Map<String, List<Integer>> updateLocationTemplateMapping(Integer templateId, List<Integer> businessIds);

	void deleteTemplateBusinessMapping(List<LocationTemplateMapping> deleteTemplateBusinessMappings);

	List<LocationTemplateMapping> getLocationTemplateMapping(Integer templateId);

	CachedCollectionWrapper<LocationTemplateMapping> getTemplateBusinessMapping(Integer enterpriseId, Integer userId, List<Integer> templateIds, List<Integer> businessIds);
}
