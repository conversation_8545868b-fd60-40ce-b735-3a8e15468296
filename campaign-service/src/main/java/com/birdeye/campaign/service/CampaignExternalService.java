package com.birdeye.campaign.service;

import java.util.Map;

import com.birdeye.campaign.dto.UsageFunnelReportResponse;
import com.birdeye.campaign.request.CampaignFilterRequest;
import com.birdeye.campaign.request.CampaignsFilterCriteria;
import com.birdeye.campaign.request.viewrecipients.ViewRecipientsRequest;
import com.birdeye.campaign.response.CampaignDetailResponse;
import com.birdeye.campaign.response.CampaignExternalResponse;
import com.birdeye.campaign.response.ViewRecipientsCommunicationResponse;

public interface CampaignExternalService {

    public CampaignExternalResponse getAllCampaignForEnterprise(Integer enterpriseId, CampaignFilterRequest campaignFilterRequest);

    public UsageFunnelReportResponse getCampaignReports(CampaignsFilterCriteria campaignsFilterCriteria);

    CampaignDetailResponse getCampaignDetailsByCampaignId(Integer enterpriseId, Integer campaignId);
    
    ViewRecipientsCommunicationResponse getViewRecipientsForCampaign(ViewRecipientsRequest request, Map<String, Object> requestParams);
}
