package com.birdeye.campaign.service.viewrecipients.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.bucket.filter.Filter;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.Cardinality;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.appointment.service.AppointmentService;
import com.birdeye.campaign.aspect.annotation.Profiled;
import com.birdeye.campaign.business.service.BusinessService;
import com.birdeye.campaign.communication.message.AppointmentFormCommMessage;
import com.birdeye.campaign.communication.message.AppointmentRecallCommMessage;
import com.birdeye.campaign.communication.message.AppointmentReminderCommMessage;
import com.birdeye.campaign.communication.message.CXCommMessage;
import com.birdeye.campaign.communication.message.CommMessage;
import com.birdeye.campaign.communication.message.PromotionCommMessage;
import com.birdeye.campaign.communication.message.SurveyCommMessage;
import com.birdeye.campaign.communication.message.referral.ReferralCommMessage;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.customer.service.CustomerService;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.dto.CampaignUserAccessDTO;
import com.birdeye.campaign.dto.DoupDownloadResponse;
import com.birdeye.campaign.dto.SplitCampaignMappingDTO;
import com.birdeye.campaign.dto.SplitCampaignVariantData;
import com.birdeye.campaign.dto.SplitCampaignViewRecipientsResponse;
import com.birdeye.campaign.dto.ViewRecipientsCommunicationDTO;
import com.birdeye.campaign.dto.ViewRecipientsListResultDTO;
import com.birdeye.campaign.dto.ViewRecipientsOverallStatsDTO;
import com.birdeye.campaign.dto.ViewRecipientsResponse;
import com.birdeye.campaign.dto.ViewRecipientsSummaryDTO;
import com.birdeye.campaign.elasticsearch.request.ElasticSearchBaseRequest;
import com.birdeye.campaign.elasticsearch.service.ElasticQueryTemplateEnum;
import com.birdeye.campaign.elasticsearch.service.ElasticSearchHelperService;
import com.birdeye.campaign.elasticsearch.service.ElasticSearchService;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.enums.CampaignRunTypeEnum;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.external.factory.ElasticSearchClientFactory;
import com.birdeye.campaign.external.utils.ESUtils;
import com.birdeye.campaign.platform.constant.CampaignTypeEnum;
import com.birdeye.campaign.platform.constant.CommunicationCategoryEnum;
import com.birdeye.campaign.report.ReportServiceUtils;
import com.birdeye.campaign.report.utils.UsageReportsUtil;
import com.birdeye.campaign.repository.CampaignRepo;
import com.birdeye.campaign.request.CampaignUserAccessRequest;
import com.birdeye.campaign.request.viewrecipients.ViewRecipientsRequest;
import com.birdeye.campaign.response.external.AppointmentDetailsResponse;
import com.birdeye.campaign.response.external.AppointmentRecallResponse;
import com.birdeye.campaign.response.external.CustomerInfoResponse;
import com.birdeye.campaign.service.SplitCampaignHelperService;
import com.birdeye.campaign.service.TemplateHelperService;
import com.birdeye.campaign.service.viewrecipients.IViewRecipientsService;
import com.birdeye.campaign.user.access.settings.service.CampaignUserAccessSettingsService;
import com.birdeye.campaign.utils.CampaignUserAccessUtils;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.DateTimeUtils;
import com.birdeye.campaign.utils.SplitCampaignUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.searchbox.core.SearchResult;
import io.searchbox.core.search.aggregation.CardinalityAggregation;
import io.searchbox.core.search.aggregation.FilterAggregation;
import io.searchbox.core.search.aggregation.TermsAggregation;

/**
 * @file_name ViewRecipientsServiceImpl.java
 * @created_date 16 Aug 2020
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
@Service("viewRecipientsService")
public class ViewRecipientsServiceImpl implements IViewRecipientsService {
	
	private static final Logger			logger								= LoggerFactory.getLogger(ViewRecipientsServiceImpl.class);
	
	@Autowired
	private CampaignRepo				campaignRepo;
	
	@Autowired
	private ElasticSearchService		elasticSearchService;
	
	@Autowired
	private ElasticSearchClientFactory elasticSearchClientFactory;
	
	@Autowired
	private ElasticSearchHelperService	elasticSearchHelperService;
	
	@Autowired
	private CustomerService				customerService;
	
	@Autowired
	private BusinessService				businessService;
	
	@Autowired
	private AppointmentService          appointmentService;
	
	@Autowired
	private SplitCampaignHelperService	splitCampaignHelperService;
	
	@Autowired
	private CampaignUserAccessSettingsService  userAccessSettingsService;
	
	@Autowired
	private TemplateHelperService				templateHelperService;
	
	private static final String	    AMERICA_LOS_ANGELES	= "America/Los_Angeles";
	
	private static final ObjectMapper	OBJECT_MAPPER	= new ObjectMapper();
	
	static {
		OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
	}
	
	/**
	 * GET View Recipients RESPONSE
	 * by filters - status, type, locations
	 * 
	 * 1. Campaign Requests/Audience LISTING
	 * 2. Campaign SUMMARY
	 * 3. Campaign METADATA
	 * 
	 */
	@Override
	public ViewRecipientsResponse getViewRecipientsResponseForACampaign(ViewRecipientsRequest request, Map<String, Object> requestParams, Integer userId) {
		if (ViewRecipientsUtils.isViewRecipientsRequestInvalid(request)) {
			logger.error("Invalid View Recipients Request :: {}", request);
			throw new CampaignException(ErrorCodes.INVALID_VIEW_RECIPIENT_REQUEST, ErrorCodes.INVALID_VIEW_RECIPIENT_REQUEST.getMessage());
		}
		
		Campaign campaign = campaignRepo.getById(request.getCampaignId());
		if (campaign == null) {
			logger.error("Invalid View Recipients Request - Campaign does not exist :: {}", request);
			return new ViewRecipientsResponse();
		}
		
		String emailTemplateCategory = CoreUtils.isTrueForInteger(campaign.getTemplateId()) ? templateHelperService.prepareEmailTemplateCategory(campaign.getTemplateId()) : "";
		String smsTemplateCategoty = CoreUtils.isTrueForInteger(campaign.getSmsTemplateId()) ? templateHelperService.prepareSmsTemplateCategory(campaign.getSmsTemplateId()) : "";
		boolean showInfluenceMetric =  showInfluenceMetric(emailTemplateCategory, smsTemplateCategoty);
		ViewRecipientsResponse response = new ViewRecipientsResponse();
		response.setMeta(ViewRecipientsUtils.getCampaignMetadata(campaign, emailTemplateCategory, smsTemplateCategoty));
		if (BooleanUtils.isFalse(CoreUtils.getBooleanValueFromInteger(campaign.getIsSplitCampaign()))) {
			List<String> userPermissions = validateAndFetchUserAccessPermissionsForViewRecipients(request.getEnterpriseId(), userId, request.getCampaignId(), campaign.getRunType(), null);
			// BIRD-50563 | User Access Settings Handling - No Appropriate Permission Case
			if (BooleanUtils.isFalse(CampaignUserAccessUtils.isUserHasViewPermission(userPermissions))) {
				logger.error("Invalid View Recipients Request - User does not have appropriate permissions :: {}", request);
				return ViewRecipientsUtils.prepareEmptyResponseWithUserPermissions(userPermissions);
			}
			// BIRD-50563 | User Access Settings Handling - Normal Case
			response.getMeta().setUserPermissions(userPermissions);
		}
		
		CampaignTypeEnum campaignType = CampaignTypeEnum.getEnum(campaign.getCampaignType());
		Map<String, Object> tokenData = ViewRecipientsUtils.getViewRecipientsQueryTokens(request, requestParams, campaignType);
		
		response.setSummary(getSummaryByCampaignType(campaignType, request.getEnterpriseId(), tokenData));
		response.setList(getListingByCampaignType(campaignType, request, tokenData, false, showInfluenceMetric));
		response.setOverall(getOverallStatsByCampaignType(campaignType, request, tokenData));
		return response;
	}
	
	/**
	 * 
	 * Fetch User Permissions/ Access Settings For Given Campaign Id/ Split Campaign Id and User Id
	 * 
	 * @param accountId, userId, campaignId, campaignRunType, splitCampaignId
	 * 
	 */
	private List<String> validateAndFetchUserAccessPermissionsForViewRecipients(Integer accountId, Integer userId, Integer campaignId, String campaignRunType, Integer splitCampaignId) {
		if (StringUtils.equalsIgnoreCase(campaignRunType, CampaignRunTypeEnum.MANUAL.getRunType())) {
			logger.info("validateAndFetchUserAccessPermissionsForViewRecipients :: Access Settings not applicable for accountId {} and userId {} and campaign run type {} and campaign id {}",
					accountId, userId, campaignRunType, campaignId);
			return new ArrayList<>();
		}
		
		if (userId == null || BooleanUtils.isFalse(userAccessSettingsService.isAccessSettingApplicable(accountId))) {
			logger.info(
					"validateAndFetchUserAccessPermissionsForViewRecipients :: Access Settings not applicable for accountId {} and userId {} and campaign run type {} and campaign id {} and split campaign id {}",
					accountId, userId, campaignRunType, campaignId, splitCampaignId);
			return new ArrayList<>();
		}
		
		CampaignUserAccessDTO userCampaignAccess = userAccessSettingsService.fetchAccessSettings(new CampaignUserAccessRequest(accountId, userId, campaignId, splitCampaignId));
		return (userCampaignAccess != null) ? new ArrayList<>(Collections.singletonList(userCampaignAccess.getAccess())) : new ArrayList<>();
	}
	
	/**
	 * 	
	 * GET Split Automation View Recipients Response
	 * by filters - status, type, locations
	 * 
	 * 1. Campaign Requests/Audience Listing
	 * 2. Campaign Summary
	 * 3. Campaign Metadata
	 * 
	 * 
	 * @param request
	 * @param requestParams
	 * @param userId
	 * @return
	 */
	@Override
	@Profiled
	public SplitCampaignViewRecipientsResponse getViewRecipientsResponseForSplitCampaign(ViewRecipientsRequest request, Map<String, Object> requestParams, Integer userId) {
		if (ViewRecipientsUtils.isViewRecipientsRequestInvalidForSplitCampaign(request, requestParams)) {
			logger.error("Invalid View Recipients Request for split-campaign id: {}, req: {}.", requestParams.get(SplitCampaignUtils.SPLIT_CAMPAIGN_ID), request);
			throw new CampaignException(ErrorCodes.INVALID_VIEW_RECIPIENT_REQUEST, ErrorCodes.INVALID_VIEW_RECIPIENT_REQUEST.getMessage());
		}
		
		List<SplitCampaignMappingDTO> mappingData = splitCampaignHelperService.prepareSplitMappingDataBySplitCampaignId((Integer)requestParams.get(SplitCampaignUtils.SPLIT_CAMPAIGN_ID), request.getEnterpriseId());
		if(BooleanUtils.isFalse(SplitCampaignUtils.fetchAndValidateSplitMappingListSize(mappingData))) {
			logger.info("getViewRecipientsResponseForSplitCampaign :: Empty or invalid length mapping data list found for split campaign id {}. Returning empty response!", requestParams.get(SplitCampaignUtils.SPLIT_CAMPAIGN_ID));
			return new SplitCampaignViewRecipientsResponse();
		}
		
		// BIRD-50563 | User Access Settings Handling - No Appropriate Permission Case
		List<String> userPermissions = validateAndFetchUserAccessPermissionsForViewRecipients(request.getEnterpriseId(), userId, null, CampaignRunTypeEnum.ONGOING.getRunType(),
				(Integer) requestParams.get(SplitCampaignUtils.SPLIT_CAMPAIGN_ID));
		if(BooleanUtils.isFalse(CampaignUserAccessUtils.isUserHasViewPermission(userPermissions))) {
			logger.error("Invalid View Recipients Request - User does not have appropriate permissions :: {}", request);
			return ViewRecipientsUtils.prepareEmptyResponseWithUserPermissionsForSplit(userPermissions);
		}
		
		if(request.getCampaignId() == null || request.getCampaignId() == 0) {
			request.setCampaignId(mappingData.get(0).getCampaignId());
		}
		
		List<SplitCampaignVariantData> variantsData = splitCampaignHelperService.prepareVariantViewRecipientsResponse(mappingData);
		ViewRecipientsResponse recipientsBasicResponse = getViewRecipientsResponseForACampaign(request, requestParams, null);
		SplitCampaignViewRecipientsResponse response = SplitCampaignUtils.prepareViewRecipientsResponse(variantsData, recipientsBasicResponse);
		// BIRD-50563 | User Access Settings Handling - Normal Case
		response.getMeta().setUserPermissions(userPermissions);
		return response;
	}
	
	
	/**
	 * POPULATE APPOINTMENT DETAILS -> APPOINTMENT TIME, DATE AND DOCTOR DETAILS
	 * for VIEW Recipients download Response
	 * 
	 * 1. List of ViewRecipientsCommunicationDTO request
	 * 2. Map of appointmentdetails from contacto api
	 * @param <T>
	 * 
	 */
	private void populateAppointmentDetails(List<ViewRecipientsCommunicationDTO> requests, Map<Integer, AppointmentDetailsResponse> appointmentMap, Integer enterpriseId) {
		if (appointmentMap == null) {
			logger.info("populateAppointmentDetails : Recieved list of appointment data is null");
			return;
		}
		String timeZoneId = (enterpriseId != null) ? businessService.getBusinessTimezoneId(enterpriseId) : AMERICA_LOS_ANGELES;
		for (ViewRecipientsCommunicationDTO request : requests) {
			Integer appointmentId = request.getAppointmentId();
			if (appointmentMap.containsKey(appointmentId)) {
				AppointmentDetailsResponse response = appointmentMap.get(appointmentId);
				request.setAppointmentDateTime(response.getStartTime() != null ? DateTimeUtils.getDateTimeAfterConversion(response.getStartTime(), timeZoneId) : StringUtils.EMPTY);
				request.setspecialistName(response.getSpecialistDetails() != null ? response.getSpecialistDetails().getSpecialistName() : StringUtils.EMPTY);
			}
		}
	}
	
	/**
	 * GET View Recipients DOWNLOAD RESPONSE
	 * by filters - status, type, locations
	 * 
	 * 1. Campaign Requests/Audience LISTING
	 * 
	 * @param <T>
	 * 
	 */
	@Override
	public DoupDownloadResponse<ViewRecipientsCommunicationDTO> getViewRecipientsDownloadResponseForACampaign(ViewRecipientsRequest request, Map<String, Object> requestParams, boolean isExternalRequest) {
		if (ViewRecipientsUtils.isViewRecipientsRequestInvalid(request)) {
			logger.error("Invalid View Recipients Request :: {}", request);
			return new DoupDownloadResponse<>();
		}
		
		Campaign campaign = campaignRepo.getById(request.getCampaignId());
		if (campaign == null) {
			logger.error("Invalid View Recipients Request - Campaign does not exist :: {}", request);
			return new DoupDownloadResponse<>();
		}
		String emailTemplateCategory = CoreUtils.isTrueForInteger(campaign.getTemplateId()) ? templateHelperService.prepareEmailTemplateCategory(campaign.getTemplateId()) : "";
		String smsTemplateCategoty = CoreUtils.isTrueForInteger(campaign.getSmsTemplateId()) ? templateHelperService.prepareSmsTemplateCategory(campaign.getSmsTemplateId()) : "";
		boolean showInfluenceMetric =  showInfluenceMetric(emailTemplateCategory, smsTemplateCategoty);
		CampaignTypeEnum campaignType = CampaignTypeEnum.getEnum(campaign.getCampaignType());
		Map<String, Object> tokenData;
		if(isExternalRequest) {
			tokenData = ViewRecipientsUtils.getExternalViewRecipientsQueryTokens(request, requestParams, campaignType);
		} else {
			tokenData = ViewRecipientsUtils.getViewRecipientsQueryTokens(request, requestParams, campaignType);
		}
		DoupDownloadResponse<ViewRecipientsCommunicationDTO> response = new DoupDownloadResponse<>();
		populateViewRecipientCommResponse(response, request, campaignType, tokenData, campaign, isExternalRequest, showInfluenceMetric);
		
		response.setMetaData(ViewRecipientsUtils.getCampaignMetadataForDownload(campaign));
		return response;
	}
	
	public void populateViewRecipientCommResponse(DoupDownloadResponse<ViewRecipientsCommunicationDTO> response, ViewRecipientsRequest request, CampaignTypeEnum campaignType,
			Map<String, Object> tokenData, Campaign campaign, boolean isExternalRequest, boolean showInfluenceMetric) {
		ViewRecipientsListResultDTO listingResult = getListingByCampaignType(campaignType, request, tokenData, isExternalRequest, showInfluenceMetric);
		if (listingResult != null) {
			List<ViewRecipientsCommunicationDTO> dataList = listingResult.getData();
			if (StringUtils.equalsAnyIgnoreCase(campaign.getCampaignType(), Constants.APPOINTMENT_REMINDER_TYPE, Constants.APPOINTMENT_FORM_TYPE)) {
				List<Integer> appointmentIds = dataList.stream().map(ViewRecipientsCommunicationDTO::getAppointmentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
				Map<String, List<AppointmentDetailsResponse>> appointmentDetailsResponse = appointmentService.getAllAppointmentsById(appointmentIds, false, false);
				
				if (appointmentDetailsResponse != null) {
					Map<Integer, AppointmentDetailsResponse> appointmentDetailsMap = appointmentDetailsResponse.get("appointmentDetails").stream()
							.collect(Collectors.toMap(AppointmentDetailsResponse::getAppointmentId, e -> e));
					populateAppointmentDetails(dataList, appointmentDetailsMap, request.getEnterpriseId());
				}
				
			} else if (StringUtils.equalsIgnoreCase(campaign.getCampaignType(), Constants.APPOINTMENT_RECALL_TYPE)) {
				
//				List<Integer> recallIds = dataList.stream().map(ViewRecipientsCommunicationDTO::getAppointmentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
				List<Integer> appointmentIds = dataList.stream().map(ViewRecipientsCommunicationDTO::getAppointmentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
				
				CachedCollectionWrapper<AppointmentRecallResponse> cachedAppointmentRecallDetailsResponse = appointmentService.getAllAppointmentRecallsByAppointmentIds(appointmentIds);
				
				List<AppointmentRecallResponse> appointmentRecallDetailsResponse = cachedAppointmentRecallDetailsResponse != null ? cachedAppointmentRecallDetailsResponse.getElementsList() : null;
				
				if (appointmentRecallDetailsResponse != null && BooleanUtils.isFalse(appointmentRecallDetailsResponse.isEmpty())) {
					
					Map<Integer, AppointmentRecallResponse> appointmentRecallDetailsMap = appointmentRecallDetailsResponse.stream()
							.collect(Collectors.toMap(AppointmentRecallResponse::getAppointmentId, e -> e));
					
					Map<String, List<AppointmentDetailsResponse>> appointmentDetailsResponse = appointmentService.getAllAppointmentsById(appointmentIds, false, false);
					
					Map<Integer, AppointmentDetailsResponse> appointmentDetailsMap = (appointmentDetailsResponse != null)
							? appointmentDetailsResponse.get("appointmentDetails").stream().collect(Collectors.toMap(AppointmentDetailsResponse::getAppointmentId, e -> e))
							: null;
					
					populateAppointmentRecallDetails(dataList, appointmentRecallDetailsMap, appointmentDetailsMap, request.getEnterpriseId());
				}
			}
			response.setData(dataList);
		}
	}
	
	/**
	 * POPULATE APPOINTMENT RECALL DETAILS -> APPOINTMENT TIME AND DATE, DOCTOR DETAILS, DUE DATE, RECALL TYPE
	 * for VIEW Recipients download Response
	 * 
	 * 1. List of ViewRecipientsCommunicationDTO request
	 * 2. Map of appointmentRecalldetails from appointment api
	 * 
	 * @param <T>
	 * 
	 */
	private void populateAppointmentRecallDetails(List<ViewRecipientsCommunicationDTO> requests, Map<Integer, AppointmentRecallResponse> appointmentRecallDetailsMap,
			Map<Integer, AppointmentDetailsResponse> appointmentDetailsMap, Integer enterpriseId) {
		if (appointmentRecallDetailsMap == null) {
			logger.info("populateAppointmentRecallDetails : Recieved list of appointment recall data is null");
			return;
		}
		String timeZoneId = (enterpriseId != null) ? businessService.getBusinessTimezoneId(enterpriseId) : AMERICA_LOS_ANGELES;
		for (ViewRecipientsCommunicationDTO request : requests) {
			Integer rAppointmentId = request.getAppointmentId();
			if (appointmentRecallDetailsMap.containsKey(rAppointmentId)) {
				AppointmentRecallResponse response = appointmentRecallDetailsMap.get(rAppointmentId);
				if (response.getLastAppointmentDate() != null) {
					request.setAppointmentDateTime(DateTimeUtils.getDateTimeAfterConversion(response.getLastAppointmentDate(), timeZoneId));
				}
				if (response.getDueDate() != null) {
					request.setRecallDueDate(DateTimeUtils.getDateTimeAfterConversion(response.getDueDate(), timeZoneId));
				}
				request.setRecallType(response.getRecallTypeLabel());
			}
			
			Integer appointmentId = request.getAppointmentId();
			if (appointmentId != null && appointmentDetailsMap != null && appointmentDetailsMap.containsKey(appointmentId) && appointmentDetailsMap.get(appointmentId).getSpecialistDetails() != null) {
				AppointmentDetailsResponse appointmentDetails = appointmentDetailsMap.get(appointmentId);
				request.setspecialistName(appointmentDetails.getSpecialistDetails().getSpecialistName());
			}
			
		}
	}
	
	/**
	 * GET View Recipients SUMMARY By Campaign Type
	 * 
	 * @param campaignType
	 * @param request
	 * @param tokenData
	 * @return
	 */
	@Override
	public ViewRecipientsSummaryDTO getSummaryByCampaignType(CampaignTypeEnum campaignType, Integer enterpriseId, Map<String, Object> tokenData) {
		// Temporary IF condition to handle ES upgrade activity. Once all indices move to newer version, remove IF.
//		if (StringUtils.equalsAnyIgnoreCase(campaignType.getType(), CampaignTypeEnum.PROMOTIONAL.getType(), CampaignTypeEnum.APPOINTMENT_REMINDER.getType(),
//				CampaignTypeEnum.APPOINTMENT_RECALL.getType(), CampaignTypeEnum.REFERRAL.getType(), CampaignTypeEnum.SURVEY_REQUEST.getType(), CampaignTypeEnum.REFERRAL.getType(),
//				CampaignTypeEnum.CX_REQUEST.getType(), CampaignTypeEnum.REVIEW_REQUEST.getType(), CampaignTypeEnum.APPOINTMENT_FORM.getType())) {
			return getSummaryV1(getSummaryESQueryByCampaignType(campaignType, enterpriseId, tokenData));
//		} else {
//			return getSummary(getSummaryESQueryByCampaignType(campaignType, enterpriseId, tokenData));
//		}
	}
	
	/**
	 * Generates - View Recipients SUMMARY - ES Query
	 * 
	 * @param campaignType
	 * @param request
	 * @param tokenData
	 * @return
	 */
	private ElasticSearchBaseRequest getSummaryESQueryByCampaignType(CampaignTypeEnum campaignType, Integer enterpriseId, Map<String, Object> tokenData) {
		switch (campaignType) {
		case REVIEW_REQUEST:
			return elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData, ElasticQueryTemplateEnum.VIEW_RECIPIENTS_SUMMARY_RR_V2.getQueryName(), Constants.RR_REPORT_INDEX,
					Constants.RR_REPORT_TYPE);
		case SURVEY_REQUEST:
			return elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData, ElasticQueryTemplateEnum.VIEW_RECIPIENTS_SUMMARY_SURVEY_V3.getQueryName(),
					Constants.SURVEY_REPORT_INDEX, Constants.SURVEY_REPORT_TYPE);
		case CX_REQUEST:
			return elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData, ElasticQueryTemplateEnum.VIEW_RECIPIENTS_SUMMARY_CX_V2.getQueryName(), Constants.CX_REPORT_INDEX,
					Constants.CX_REPORT_TYPE);
		case REFERRAL:
			return elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData, ElasticQueryTemplateEnum.VIEW_RECIPIENTS_SUMMARY_REFERRAL_V2.getQueryName(),
					Constants.REFERRAL_REPORT_INDEX, Constants.REFERRAL_REPORT_TYPE);
		case PROMOTIONAL:
			return elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData, ElasticQueryTemplateEnum.VIEW_RECIPIENTS_SUMMARY_PROMOTION_V2.getQueryName(),
					Constants.PROMOTION_REPORT_INDEX, Constants.PROMOTION_REPORT_TYPE);
		case APPOINTMENT_REMINDER:
			return elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData, ElasticQueryTemplateEnum.VIEW_RECIPIENTS_SUMMARY_APPOINTMENT_REMINDER_V1.getQueryName(),
					Constants.APPOINTMENT_REMINDER_REPORT_INDEX, Constants.APPOINTMENT_REMINDER_REPORT_TYPE);
		case APPOINTMENT_RECALL:
			return elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData, ElasticQueryTemplateEnum.VIEW_RECIPIENTS_SUMMARY_APPOINTMENT_RECALL.getQueryName(),
					Constants.APPOINTMENT_RECALL_REPORT_INDEX, Constants.APPOINTMENT_RECALL_REPORT_TYPE);
		case APPOINTMENT_FORM:
			return elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData, ElasticQueryTemplateEnum.VIEW_RECIPIENTS_SUMMARY_APPOINTMENT_FORM.getQueryName(),
					Constants.APPOINTMENT_FORM_REPORT_INDEX, Constants.APPOINTMENT_FORM_REPORT_TYPE);
		default:
			return null;
	}
	}
	
	/**
	 * Generates - View Recipients SUMMARY - response
	 * 
	 * @param esRequest
	 * @return
	 */
	private ViewRecipientsSummaryDTO getSummary(ElasticSearchBaseRequest esRequest) { //NOSONAR
		SearchResult result = elasticSearchService.execute(esRequest);
		if (result == null || result.getAggregations() == null) {
			return new ViewRecipientsSummaryDTO();
		}
		
		ViewRecipientsSummaryDTO summary = new ViewRecipientsSummaryDTO();
		
		//SENT count
		CardinalityAggregation sentCount = result.getAggregations().getAggregation("sent_count", CardinalityAggregation.class);
		if (sentCount != null) {
			summary.setSent(sentCount.getCardinality());
		}
		
		//DELIVERED count
		FilterAggregation deliveredGroup = result.getAggregations().getAggregation("delivered_count_group", FilterAggregation.class);
		if (deliveredGroup != null) {
			TermsAggregation deliveredCountBySource = deliveredGroup.getAggregation("delivered_count_by_source", TermsAggregation.class);
			if (deliveredCountBySource != null && CollectionUtils.isNotEmpty(deliveredCountBySource.getBuckets())) {
				for (TermsAggregation.Entry bucket : deliveredCountBySource.getBuckets()) {
					if (StringUtils.equalsIgnoreCase(bucket.getKey(), "email")) {
						summary.setEmailDelivered(bucket.getCount());
					}
					if (StringUtils.equalsIgnoreCase(bucket.getKey(), "sms")) {
						summary.setTextDelivered(bucket.getCount());
					}
				}
			}
		}
		
		//FAILURE_COUNT_BY_SOURCE
		FilterAggregation failureGroup = result.getAggregations().getAggregation("failure_count_by_source_group", FilterAggregation.class);
		if (failureGroup != null) {
			TermsAggregation failureCountBySource = failureGroup.getAggregation("failure_count_by_source", TermsAggregation.class);
			if (failureCountBySource != null && CollectionUtils.isNotEmpty(failureCountBySource.getBuckets())) {
				for (TermsAggregation.Entry bucket : failureCountBySource.getBuckets()) {
					if (StringUtils.equalsIgnoreCase(bucket.getKey(), "email")) {
						summary.setEmailFailed(bucket.getCount());
					}
					if (StringUtils.equalsIgnoreCase(bucket.getKey(), "sms")) {
						summary.setTextFailed(bucket.getCount());
					}
				}
			}
		}
		
		//Number of Unique Customers - Distinct cust ids
		CardinalityAggregation numContacts = result.getAggregations().getAggregation("num_customers", CardinalityAggregation.class);
		if (numContacts != null) {
			summary.setNumContacts(numContacts.getCardinality());
		}
		return summary;
	}
	
	/**
	 * Generates - View Recipients SUMMARY - response
	 * V1 method using elastic High level client in place of Jest.
	 * 
	 * @param esRequest
	 * @return
	 */
	private ViewRecipientsSummaryDTO getSummaryV1(ElasticSearchBaseRequest esRequest) { // NOSONAR
		SearchResponse response = elasticSearchClientFactory.getElasticSearchHighClientService(esRequest.getIndex()).execute(esRequest);
		if (response == null || response.getAggregations() == null) {
			return new ViewRecipientsSummaryDTO();
		}
		
		ViewRecipientsSummaryDTO summary = new ViewRecipientsSummaryDTO();
		
		// SENT count
		Cardinality sentCount = response.getAggregations().get("sent_count");
		if (sentCount != null) {
			summary.setSent(sentCount.getValue());
		}
		
		// DELIVERED count
		Filter deliveredGroup = response.getAggregations().get("delivered_count_group");
		if (deliveredGroup != null) {
			Terms deliveredCountBySource = deliveredGroup.getAggregations().get("delivered_count_by_source");
			if (deliveredCountBySource != null && CollectionUtils.isNotEmpty(deliveredCountBySource.getBuckets())) {
				for (Terms.Bucket bucket : deliveredCountBySource.getBuckets()) {
					if (StringUtils.equalsIgnoreCase(bucket.getKey().toString(), "email")) {
						summary.setEmailDelivered(bucket.getDocCount());
					}
					if (StringUtils.equalsIgnoreCase(bucket.getKey().toString(), "sms")) {
						summary.setTextDelivered(bucket.getDocCount());
					}
				}
			}
		}
		
		// FAILURE_COUNT_BY_SOURCE
		Filter failureGroup = response.getAggregations().get("failure_count_by_source_group");
		if (failureGroup != null) {
			Terms failureCountBySource = failureGroup.getAggregations().get("failure_count_by_source");
			if (failureCountBySource != null && CollectionUtils.isNotEmpty(failureCountBySource.getBuckets())) {
				for (Terms.Bucket bucket : failureCountBySource.getBuckets()) {
					if (StringUtils.equalsIgnoreCase(bucket.getKey().toString(), "email")) {
						summary.setEmailFailed(bucket.getDocCount());
					}
					if (StringUtils.equalsIgnoreCase(bucket.getKey().toString(), "sms")) {
						summary.setTextFailed(bucket.getDocCount());
					}
				}
			}
		}
		
		// Number of Unique Customers - Distinct cust ids
		Cardinality numContacts = response.getAggregations().get("num_customers");
		if (numContacts != null) {
			summary.setNumContacts(numContacts.getValue());
		}
		return summary;
	}
	
	/**
	 * GET View Recipients OVERALL STATS By Campaign Type
	 * 
	 * @param campaignType
	 * @param request
	 * @param tokenData
	 * @return
	 */
	private ViewRecipientsOverallStatsDTO getOverallStatsByCampaignType(CampaignTypeEnum campaignType, ViewRecipientsRequest request, Map<String, Object> tokenData) {
		// Temporary IF condition to handle ES upgrade activity. Once all indices move to newer version, remove IF.
//		if (StringUtils.equalsAnyIgnoreCase(campaignType.getType(), CampaignTypeEnum.PROMOTIONAL.getType(), CampaignTypeEnum.APPOINTMENT_REMINDER.getType(),
//				CampaignTypeEnum.CX_REQUEST.getType(), CampaignTypeEnum.APPOINTMENT_RECALL.getType(), CampaignTypeEnum.REFERRAL.getType(),
//				CampaignTypeEnum.SURVEY_REQUEST.getType(), CampaignTypeEnum.REVIEW_REQUEST.getType(), CampaignTypeEnum.APPOINTMENT_FORM.getType())) {
			return getOverralStatsV1(getOverallStatsESQueryByCampaignType(campaignType, request, tokenData));
//		} else {
//			return getOverralStats(getOverallStatsESQueryByCampaignType(campaignType, request, tokenData));
//		}
	}
	
	
	/**
	 * Generates - View OVERALL STATS - ES Query 
	 * No location or source filter
	 * 
	 * @param campaignType
	 * @param request
	 * @param tokenData
	 * @return
	 */
	private ElasticSearchBaseRequest getOverallStatsESQueryByCampaignType(CampaignTypeEnum campaignType, ViewRecipientsRequest request, Map<String, Object> tokenData) {
		switch (campaignType) {
			case REVIEW_REQUEST:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData,
						ElasticQueryTemplateEnum.VIEW_RECIPIENTS_OVERALL_STATS_RR_V1.getQueryName(), Constants.RR_REPORT_INDEX, Constants.RR_REPORT_TYPE);
			case SURVEY_REQUEST:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData,
						ElasticQueryTemplateEnum.VIEW_RECIPIENTS_OVERALL_STATS_SURVEY_V1.getQueryName(), Constants.SURVEY_REPORT_INDEX, Constants.SURVEY_REPORT_TYPE);
			case CX_REQUEST:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData,
						ElasticQueryTemplateEnum.VIEW_RECIPIENTS_OVERALL_STATS_CX_V1.getQueryName(), Constants.CX_REPORT_INDEX, Constants.CX_REPORT_TYPE);
			case REFERRAL:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData,
						ElasticQueryTemplateEnum.VIEW_RECIPIENTS_OVERALL_STATS_REFERRAL_V1.getQueryName(), Constants.REFERRAL_REPORT_INDEX, Constants.REFERRAL_REPORT_TYPE);
			case PROMOTIONAL:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData,
						ElasticQueryTemplateEnum.VIEW_RECIPIENTS_OVERALL_STATS_PROMOTION_V1.getQueryName(), Constants.PROMOTION_REPORT_INDEX, Constants.PROMOTION_REPORT_TYPE);
			case APPOINTMENT_REMINDER:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData, ElasticQueryTemplateEnum.VIEW_RECIPIENTS_OVERALL_STATS_APPOINTMENT_REMINDER_V1.getQueryName(),
						Constants.APPOINTMENT_REMINDER_REPORT_INDEX, Constants.APPOINTMENT_REMINDER_REPORT_TYPE);
			case APPOINTMENT_RECALL:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData, ElasticQueryTemplateEnum.VIEW_RECIPIENTS_OVERALL_STATS_APPOINTMENT_RECALL.getQueryName(),
						Constants.APPOINTMENT_RECALL_REPORT_INDEX, Constants.APPOINTMENT_RECALL_REPORT_TYPE);
			case APPOINTMENT_FORM:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData, ElasticQueryTemplateEnum.VIEW_RECIPIENTS_OVERALL_STATS_APPOINTMENT_FORM.getQueryName(),
						Constants.APPOINTMENT_FORM_REPORT_INDEX, Constants.APPOINTMENT_FORM_REPORT_TYPE);
			default:
				return null;
		}
	}
	
	/**
	 * Generates - View OVERALL STATS - response
	 * No location or source filter
	 * 
	 * @param esRequest
	 * @return
	 */
	private ViewRecipientsOverallStatsDTO getOverralStats(ElasticSearchBaseRequest esRequest) {
		SearchResult result = elasticSearchService.execute(esRequest);
		if (result == null || result.getAggregations() == null) {
			return new ViewRecipientsOverallStatsDTO();
		}
		
		ViewRecipientsOverallStatsDTO summary = new ViewRecipientsOverallStatsDTO();
		
		//No of Requests
		CardinalityAggregation numRequests = result.getAggregations().getAggregation("overall_requests_count", CardinalityAggregation.class);
		if (numRequests != null) {
			summary.setNumRequests(numRequests.getCardinality());
		}
		
		//No of Contacts
		CardinalityAggregation numContacts = result.getAggregations().getAggregation("overall_customers_count", CardinalityAggregation.class);
		if (numContacts != null) {
			summary.setNumContacts(numContacts.getCardinality());
		}
		
		return summary;
	}
	
	/**
	 * Generates - View OVERALL STATS - response
	 * No location or source filter
	 * V1 method using elastic High level client in place of Jest.
	 * 
	 * @param esRequest
	 * @return
	 */
	private ViewRecipientsOverallStatsDTO getOverralStatsV1(ElasticSearchBaseRequest esRequest) {
		if (esRequest == null || StringUtils.isBlank(esRequest.getIndex())) {
			return new ViewRecipientsOverallStatsDTO();
		}
		SearchResponse response = elasticSearchClientFactory.getElasticSearchHighClientService(esRequest.getIndex()).execute(esRequest);
		
		ViewRecipientsOverallStatsDTO summary = new ViewRecipientsOverallStatsDTO();
		
		// No of Requests
		Cardinality numRequests = response.getAggregations().get("overall_requests_count");
		if (numRequests != null) {
			summary.setNumRequests(numRequests.getValue());
		}
		
		// No of Contacts
		Cardinality numContacts = response.getAggregations().get("overall_customers_count");
		if (numContacts != null) {
			summary.setNumContacts(numContacts.getValue());
		}
		
		return summary;
	}
	
	/**
	 * 
	 * GET View Recipients LIST By Campaign Type
	 *  
	 * @param campaignType
	 * @param request
	 * @param tokenData
	 * @return
	 */
	
	private ViewRecipientsListResultDTO getListingByCampaignType(CampaignTypeEnum campaignType, ViewRecipientsRequest request, Map<String, Object> tokenData, boolean isExternalRequest, boolean showInfluenceMetric) {
		switch (campaignType) {
			case REVIEW_REQUEST:
				return getListingRR(request, tokenData, isExternalRequest);
			case SURVEY_REQUEST:
				return getListingSurvey(request, tokenData, isExternalRequest);
			case CX_REQUEST:
				return getListingCX(request, tokenData, isExternalRequest);
			case REFERRAL:
				return getListingReferral(request, tokenData, isExternalRequest);
			case PROMOTIONAL:
				return getListingPromotion(request, tokenData, isExternalRequest, showInfluenceMetric);
			case APPOINTMENT_REMINDER:
				return getListingAppointmentReminder(request, tokenData, isExternalRequest);
			case APPOINTMENT_RECALL:
				return getListingAppointmentRecall(request, tokenData, isExternalRequest);
			case APPOINTMENT_FORM:
				return getListingAppointmentForm(request, tokenData, isExternalRequest);
			default:
				return null;
		}
	}
	
	
	/**
	 * Generates - View Recipients LISTING - ES Query
	 * 
	 * @param campaignType
	 * @param request
	 * @param tokenData
	 * @return
	 */
	
	private ElasticSearchBaseRequest getListingESQueryByCampaignType(CampaignTypeEnum campaignType, ViewRecipientsRequest request, Map<String, Object> tokenData) {
		switch (campaignType) {
			case REVIEW_REQUEST:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData,
						ElasticQueryTemplateEnum.VIEW_RECIPIENTS_LIST_RR_V1.getQueryName(), Constants.RR_REPORT_INDEX, Constants.RR_REPORT_TYPE);
			case SURVEY_REQUEST:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData,
						ElasticQueryTemplateEnum.VIEW_RECIPIENTS_LIST_SURVEY_V1.getQueryName(), Constants.SURVEY_REPORT_INDEX, Constants.SURVEY_REPORT_TYPE);
			case CX_REQUEST:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData,
						ElasticQueryTemplateEnum.VIEW_RECIPIENTS_LIST_CX_V1.getQueryName(), Constants.CX_REPORT_INDEX, Constants.CX_REPORT_TYPE);
			case REFERRAL:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData,
						ElasticQueryTemplateEnum.VIEW_RECIPIENTS_LIST_REFERRAL_V1.getQueryName(), Constants.REFERRAL_REPORT_INDEX, Constants.REFERRAL_REPORT_TYPE);
			case PROMOTIONAL:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData,
						ElasticQueryTemplateEnum.VIEW_RECIPIENTS_LIST_PROMOTION_V1.getQueryName(), Constants.PROMOTION_REPORT_INDEX, Constants.PROMOTION_REPORT_TYPE);
			case APPOINTMENT_REMINDER:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData, ElasticQueryTemplateEnum.VIEW_RECIPIENTS_LIST_APPOINTMENT_REMINDER_V1.getQueryName(),
						Constants.APPOINTMENT_REMINDER_REPORT_INDEX, Constants.APPOINTMENT_REMINDER_REPORT_TYPE);
			case APPOINTMENT_RECALL:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData, ElasticQueryTemplateEnum.VIEW_RECIPIENTS_LIST_APPOINTMENT_RECALL.getQueryName(),
						Constants.APPOINTMENT_RECALL_REPORT_INDEX, Constants.APPOINTMENT_RECALL_REPORT_TYPE);
			case APPOINTMENT_FORM:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData, ElasticQueryTemplateEnum.VIEW_RECIPIENTS_LIST_APPOINTMENT_FORM.getQueryName(),
						Constants.APPOINTMENT_FORM_REPORT_INDEX, Constants.APPOINTMENT_FORM_REPORT_TYPE);
			default:
				return null;
		}
	}
	
	/**
	 * 
	 * View Recipients Listing - REVIEW REQUEST
	 * 
	 * @param request
	 * @param tokenData
	 * @return
	 */
	private ViewRecipientsListResultDTO getListingRR(ViewRecipientsRequest request, Map<String, Object> tokenData, boolean isExternalRequest) {
		ElasticSearchBaseRequest esRequest;
		if(isExternalRequest) {
			esRequest = getExternalListingESQueryByCampaignType(CampaignTypeEnum.REVIEW_REQUEST, request, tokenData);
		} else {
			esRequest = getListingESQueryByCampaignType(CampaignTypeEnum.REVIEW_REQUEST, request, tokenData);
		}
		SearchResponse result = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.RR_REPORT_INDEX).execute(esRequest);
		if (result == null || result.getHits() == null || result.getHits().getHits().length == 0l) {
			return ViewRecipientsListResultDTO.emptyResponse();
		}
		List<CommMessage> messages = ESUtils.mapESSearchResponse(result, CommMessage.class);
		
		if (CollectionUtils.isEmpty(messages)) {
			return ViewRecipientsListResultDTO.emptyResponse();
		}
		
		Set<Integer> customerIdsWithoutName = new HashSet<>();
		Set<Integer> locationIdsWithoutName = new HashSet<>();
		
		List<ViewRecipientsCommunicationDTO> requests = messages.stream().map(a -> getCommunicationDTORR(a, customerIdsWithoutName, locationIdsWithoutName)).collect(Collectors.toList());
		populateBusinessAndCustomerDetailsForMissingListingData(requests, customerIdsWithoutName, locationIdsWithoutName, StringUtils.EMPTY);
		return new ViewRecipientsListResultDTO(result.getHits().getTotalHits().value, requests);
	}
	
	/**
	 * Represents a ROW in View Recipients Listing - RR
	 * 
	 * @param message
	 * @param customerIdsWithoutName
	 * @param locationIdsWithoutName
	 * @return
	 */
	private ViewRecipientsCommunicationDTO getCommunicationDTORR(CommMessage message, Set<Integer> customerIdsWithoutName, Set<Integer> locationIdsWithoutName) {
		ViewRecipientsCommunicationDTO request = new ViewRecipientsCommunicationDTO();
		
		request.setCustomerId(message.getCustomerId());
		request.setCustomerName(message.getCustomerName());
		customerIdsWithoutName.add(message.getCustomerId());
		
		request.setBusinessId(message.getBusinessId());
		if (StringUtils.isNotBlank(message.getBusinessAlias())) {
			request.setLocation(message.getBusinessAlias());
		} else {
			locationIdsWithoutName.add(message.getBusinessId());
		}
		request.setSource(message.getSrc());
		request.setDisplaySource(ViewRecipientsUtils.getDisplaySource(message.getSrc()));
		String status = ReportServiceUtils.getLatestStatusForCustomerForRRCampaign(message);
		request.setStatus(status);
		request.setLocation(message.getBusinessAlias());
		if (message.getcTime() != null) {
			// request.setLastActivityDate(ViewRecipientsUtils.getLastActivityDate(message.getcTime()));
			// request.setLastActivityDateWithTime(ViewRecipientsUtils.getLastActivityDateTime(message.getcTime()));
			String timeZoneId = businessService.getBusinessTimezoneId(message.getEnterpriseId());
			request.setLastActivityDate(DateTimeUtils.getDateAfterConversion(message.getcTime(), timeZoneId));
			request.setLastActivityDateWithTime(DateTimeUtils.getDateTimeAfterConversion(message.getcTime(), timeZoneId));
		}
		Integer failureId = ReportServiceUtils.getFailureIdForMessage(message.getFailureReason());
		request.setFailureId(failureId);
		request.setIsDND(ReportServiceUtils.isDNDScheduled(message.getStatus()));
		String failureReason = UsageReportsUtil.getFailureMessageForId(failureId);
		request.setFailureReason(failureReason);
		return request;
	}
	
	/**
	 * 
	 * View Recipients Listing - SURVEY REQUEST
	 * 
	 * @param request
	 * @param tokenData
	 * @return
	 */
	private ViewRecipientsListResultDTO getListingSurvey(ViewRecipientsRequest request, Map<String, Object> tokenData, boolean isExternalRequest) {

		ElasticSearchBaseRequest esRequest;
		if(isExternalRequest) {
			esRequest = getExternalListingESQueryByCampaignType(CampaignTypeEnum.SURVEY_REQUEST, request, tokenData);
		} else {
			esRequest = getListingESQueryByCampaignType(CampaignTypeEnum.SURVEY_REQUEST, request, tokenData);
		}
		SearchResponse searchResponse = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.SURVEY_REPORT_INDEX).execute(esRequest);
		
		if (searchResponse == null || searchResponse.getHits() == null || searchResponse.getHits().getTotalHits().value == 0l) {
			return ViewRecipientsListResultDTO.emptyResponse();
		}
		
		List<SurveyCommMessage> messages = new ArrayList<>();
		for (SearchHit hit : searchResponse.getHits().getHits()) {
			messages.add(OBJECT_MAPPER.convertValue(hit.getSourceAsMap(), SurveyCommMessage.class));
		}	
		
		if (CollectionUtils.isEmpty(messages)) {
			return ViewRecipientsListResultDTO.emptyResponse();
		}
		
		Set<Integer> customerIdsWithoutName = new HashSet<>();
		Set<Integer> locationIdsWithoutName = new HashSet<>();
		
		List<ViewRecipientsCommunicationDTO> requests = messages.stream().map(a -> getCommunicationDTOSurvey(a, customerIdsWithoutName, locationIdsWithoutName))
				.collect(Collectors.toList());
		populateBusinessAndCustomerDetailsForMissingListingData(requests, customerIdsWithoutName, locationIdsWithoutName, StringUtils.EMPTY);
		return new ViewRecipientsListResultDTO(searchResponse.getHits().getTotalHits().value, requests);
		
	}
	
	
	/**
	 * Represents a ROW in View Recipients Listing - Survey
	 * 
	 * @param message
	 * @param customerIdsWithoutName
	 * @param locationIdsWithoutName
	 * @return
	 */
	private ViewRecipientsCommunicationDTO getCommunicationDTOSurvey(SurveyCommMessage message, Set<Integer> customerIdsWithoutName, Set<Integer> locationIdsWithoutName) {
		ViewRecipientsCommunicationDTO request = new ViewRecipientsCommunicationDTO();
		
		request.setCustomerId(message.getCustomerId());
		request.setCustomerName(message.getCustomerName());
		customerIdsWithoutName.add(message.getCustomerId());
		
		request.setBusinessId(message.getBusinessId());
		if (StringUtils.isNotBlank(message.getBusinessAlias())) {
			request.setLocation(message.getBusinessAlias());
		} else {
			locationIdsWithoutName.add(message.getBusinessId());
		}
		request.setSource(message.getSrc());
		request.setDisplaySource(ViewRecipientsUtils.getDisplaySource(message.getSrc()));
		String status = ReportServiceUtils.getLatestStatusForCustomerForSurveyCampaign(message);
		request.setStatus(status);
		request.setLocation(message.getBusinessAlias());
		if (message.getcTime() != null) {
			String timeZoneId = businessService.getBusinessTimezoneId(message.getEnterpriseId());
			request.setLastActivityDate(DateTimeUtils.getDateAfterConversion(message.getcTime(), timeZoneId));
			request.setLastActivityDateWithTime(DateTimeUtils.getDateTimeAfterConversion(message.getcTime(), timeZoneId));
//			request.setLastActivityDate(ViewRecipientsUtils.getLastActivityDate(message.getcTime()));
//			request.setLastActivityDateWithTime(ViewRecipientsUtils.getLastActivityDateTime(message.getcTime()));
		}
		Integer failureId = ReportServiceUtils.getFailureIdForMessage(message.getFailureReason());
		request.setFailureId(failureId);
		request.setIsDND(ReportServiceUtils.isDNDScheduled(message.getStatus()));
		String failureReason = UsageReportsUtil.getFailureMessageForId(failureId);
		request.setFailureReason(failureReason);
		return request;
	}
	
	
	/**
	 * 
	 * View Recipients Listing - CX REQUEST
	 * 
	 * @param request
	 * @param tokenData
	 * @return
	 */
	private ViewRecipientsListResultDTO getListingCX(ViewRecipientsRequest request, Map<String, Object> tokenData, boolean isExternalRequest) {
		ElasticSearchBaseRequest esRequest;
		if(isExternalRequest) {
			esRequest = getExternalListingESQueryByCampaignType(CampaignTypeEnum.CX_REQUEST, request, tokenData);
		} else {
			esRequest = getListingESQueryByCampaignType(CampaignTypeEnum.CX_REQUEST, request, tokenData);
		}
		SearchResponse result = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.CX_REPORT_INDEX).execute(esRequest);
		if (result == null || result.getHits() == null || result.getHits().getTotalHits().value == 0l) {
			return ViewRecipientsListResultDTO.emptyResponse();
		}
		List<CXCommMessage> messages = new ArrayList<>();
		for (SearchHit hit : result.getHits().getHits()) {
			messages.add(OBJECT_MAPPER.convertValue(hit.getSourceAsMap(), CXCommMessage.class));
		}
		
		if (CollectionUtils.isEmpty(messages)) {
			return ViewRecipientsListResultDTO.emptyResponse();
		}
		
		Set<Integer> customerIdsWithoutName = new HashSet<>();
		Set<Integer> locationIdsWithoutName = new HashSet<>();
		
		List<ViewRecipientsCommunicationDTO> requests = messages.stream().map(a -> getCommunicationDTOCX(a, customerIdsWithoutName, locationIdsWithoutName)).collect(Collectors.toList());
		populateBusinessAndCustomerDetailsForMissingListingData(requests, customerIdsWithoutName, locationIdsWithoutName, StringUtils.EMPTY);
		return new ViewRecipientsListResultDTO(result.getHits().getTotalHits().value, requests);
	}
	
	
	/**
	 * Represents a ROW in View Recipients Listing - CX
	 * 
	 * @param message
	 * @param customerIdsWithoutName
	 * @param locationIdsWithoutName
	 * @return
	 */
	private ViewRecipientsCommunicationDTO getCommunicationDTOCX(CXCommMessage message, Set<Integer> customerIdsWithoutName, Set<Integer> locationIdsWithoutName) {
		ViewRecipientsCommunicationDTO request = new ViewRecipientsCommunicationDTO();
		
		request.setCustomerId(message.getCustomerId());
		request.setCustomerName(message.getCustomerName());
		customerIdsWithoutName.add(message.getCustomerId());
		
		request.setBusinessId(message.getBusinessId());
		if (StringUtils.isNotBlank(message.getBusinessAlias())) {
			request.setLocation(message.getBusinessAlias());
		} else {
			locationIdsWithoutName.add(message.getBusinessId());
		}
		request.setSource(message.getSrc());
		request.setDisplaySource(ViewRecipientsUtils.getDisplaySource(message.getSrc()));
		String status = ReportServiceUtils.getLatestStatusForCustomerForCxCampaign(message);
		request.setStatus(status);
		request.setLocation(message.getBusinessAlias());
		if (message.getRequestDate() != null) {
//			request.setLastActivityDate(ViewRecipientsUtils.getLastActivityDate(message.getUpdatedAt()));
//			request.setLastActivityDateWithTime(ViewRecipientsUtils.getLastActivityDateTime(message.getUpdatedAt()));
			String timeZoneId = businessService.getBusinessTimezoneId(message.getEnterpriseId());
			request.setLastActivityDate(DateTimeUtils.getDateAfterConversion(message.getUpdatedAt(), timeZoneId));
			request.setLastActivityDateWithTime(DateTimeUtils.getDateTimeAfterConversion(message.getUpdatedAt(), timeZoneId));
		}
		Integer failureId = ReportServiceUtils.getFailureIdForMessage(message.getFailureReason());
		request.setFailureId(failureId);
		request.setIsDND(ReportServiceUtils.isDNDScheduled(message.getStatus()));
		String failureReason = UsageReportsUtil.getFailureMessageForId(failureId);
		request.setFailureReason(failureReason);
		return request;
	}

	
	/**
	 * 
	 * View Recipients Listing - REFERRAL REQUEST
	 * 
	 * @param request
	 * @param tokenData
	 * @return
	 */
	private ViewRecipientsListResultDTO getListingReferral(ViewRecipientsRequest request, Map<String, Object> tokenData, boolean isExternalRequest) {
		ElasticSearchBaseRequest esRequest;
		if(isExternalRequest) {
			esRequest = getExternalListingESQueryByCampaignType(CampaignTypeEnum.REFERRAL, request, tokenData);
		} else {
			esRequest = getListingESQueryByCampaignType(CampaignTypeEnum.REFERRAL, request, tokenData);
		}
		if (esRequest == null || StringUtils.isBlank(esRequest.getIndex())) {
			return ViewRecipientsListResultDTO.emptyResponse();
		}
		SearchResponse response = elasticSearchClientFactory.getElasticSearchHighClientService(esRequest.getIndex()).execute(esRequest);
		if (response == null || response.getHits() == null || response.getHits().getTotalHits().value == 0l) {
			return ViewRecipientsListResultDTO.emptyResponse();
		}
		
		List<ReferralCommMessage> messages = new ArrayList<>();
		for (SearchHit hit : response.getHits().getHits()) {
			messages.add(OBJECT_MAPPER.convertValue(hit.getSourceAsMap(), ReferralCommMessage.class));
		}
		if (CollectionUtils.isEmpty(messages)) {
			return ViewRecipientsListResultDTO.emptyResponse();
		}
		
		Set<Integer> customerIdsWithoutName = new HashSet<>();
		Set<Integer> locationIdsWithoutName = new HashSet<>();
		
		List<ViewRecipientsCommunicationDTO> requests = messages.stream().map(a -> getCommunicationDTOReferral(a, customerIdsWithoutName, locationIdsWithoutName)).collect(Collectors.toList());
		populateBusinessAndCustomerDetailsForMissingListingData(requests, customerIdsWithoutName, locationIdsWithoutName, StringUtils.EMPTY);
		return new ViewRecipientsListResultDTO(response.getHits().getTotalHits().value, requests);
	}
	
	/**
	 * Represents a ROW in View Recipients Listing - Referral
	 * 
	 * @param message
	 * @param customerIdsWithoutName
	 * @param locationIdsWithoutName
	 * @return
	 */
	private ViewRecipientsCommunicationDTO getCommunicationDTOReferral(ReferralCommMessage message, Set<Integer> customerIdsWithoutName, Set<Integer> locationIdsWithoutName) {
		ViewRecipientsCommunicationDTO request = new ViewRecipientsCommunicationDTO();
		
		request.setCustomerId(message.getCustomerId());
		request.setCustomerName(message.getCustomerName());
		customerIdsWithoutName.add(message.getCustomerId());
			
		request.setBusinessId(message.getBusinessId());
		if (StringUtils.isNotBlank(message.getBusinessAlias())) {
			request.setLocation(message.getBusinessAlias());
		} else {
			locationIdsWithoutName.add(message.getBusinessId());
		}
		request.setSource(message.getSource());
		request.setDisplaySource(ViewRecipientsUtils.getDisplaySource(message.getSource()));
		String status = ReportServiceUtils.getLatestStatusForCustomerForReferralCampaign(message);
		request.setStatus(status);
		request.setLocation(message.getBusinessAlias());
		if (message.getRequestDate() != null) {
//			request.setLastActivityDate(ViewRecipientsUtils.getLastActivityDate(message.getUpdatedAt()));
//			request.setLastActivityDateWithTime(ViewRecipientsUtils.getLastActivityDateTime(message.getUpdatedAt()));
			String timeZoneId = businessService.getBusinessTimezoneId(message.getEnterpriseId());
			request.setLastActivityDate(DateTimeUtils.getDateAfterConversion(message.getUpdatedAt(), timeZoneId));
			request.setLastActivityDateWithTime(DateTimeUtils.getDateTimeAfterConversion(message.getUpdatedAt(), timeZoneId));
		}
		Integer failureId = ReportServiceUtils.getFailureIdForMessage(message.getFailureReason());
		request.setFailureId(failureId);
		request.setIsDND(ReportServiceUtils.isDNDScheduled(message.getStatus()));
		String failureReason = UsageReportsUtil.getFailureMessageForId(failureId);
		request.setFailureReason(failureReason);
		return request;
	}
	
	
	/**
	 * 
	 * View Recipients Listing - PROMOTION REQUEST
	 * 
	 * @param request
	 * @param tokenData
	 * @return
	 */
	private ViewRecipientsListResultDTO getListingPromotion(ViewRecipientsRequest request, Map<String, Object> tokenData, boolean isExternalRequest, boolean showInfluenceMetric) {
		ElasticSearchBaseRequest esRequest;
		if(isExternalRequest) {
			esRequest = getExternalListingESQueryByCampaignType(CampaignTypeEnum.PROMOTIONAL, request, tokenData);
		} else {
			esRequest = getListingESQueryByCampaignType(CampaignTypeEnum.PROMOTIONAL, request, tokenData);
		}
		SearchResponse response = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.PROMOTION_REPORT_INDEX).execute(esRequest);
		if (response == null || response.getHits() == null || response.getHits().getTotalHits().value == 0l) {
			return ViewRecipientsListResultDTO.emptyResponse();
		}
		
		List<PromotionCommMessage> messages = new ArrayList<>();
		for (SearchHit hit : response.getHits().getHits()) {
			messages.add(OBJECT_MAPPER.convertValue(hit.getSourceAsMap(), PromotionCommMessage.class));
		}
		if (CollectionUtils.isEmpty(messages)) {
			return ViewRecipientsListResultDTO.emptyResponse();
		}
		
		Set<Integer> customerIdsWithoutName = new HashSet<>();
		Set<Integer> locationIdsWithoutName = new HashSet<>();
		
		List<ViewRecipientsCommunicationDTO> requests = messages.stream().map(a -> getCommunicationDTOPromotion(a, customerIdsWithoutName, locationIdsWithoutName, showInfluenceMetric)).collect(Collectors.toList());
		populateBusinessAndCustomerDetailsForMissingListingData(requests, customerIdsWithoutName, locationIdsWithoutName, StringUtils.EMPTY);
		return new ViewRecipientsListResultDTO(response.getHits().getTotalHits().value, requests);
	}
	
	/**
	 * 
	 * View Recipients Listing - APPOINTMENT REMINDER REQUEST
	 * 
	 * @param request
	 * @param tokenData
	 * @return
	 */
	private ViewRecipientsListResultDTO getListingAppointmentReminder(ViewRecipientsRequest request, Map<String, Object> tokenData, boolean isExternalRequest) {
		ElasticSearchBaseRequest esRequest;
		if(isExternalRequest) {
			esRequest = getExternalListingESQueryByCampaignType(CampaignTypeEnum.APPOINTMENT_REMINDER, request, tokenData);
		} else {
			esRequest = getListingESQueryByCampaignType(CampaignTypeEnum.APPOINTMENT_REMINDER, request, tokenData);
		}
		SearchResponse response = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.APPOINTMENT_REMINDER_REPORT_INDEX).execute(esRequest);
		if (response == null || response.getHits() == null || response.getHits().getTotalHits().value == 0l) {
			logger.info("Empty response received from ES listing query for request {}", request);
			return ViewRecipientsListResultDTO.emptyResponse();
		}
		List<AppointmentReminderCommMessage> messages = new ArrayList<>();
		for (SearchHit hit : response.getHits().getHits()) {
			messages.add(OBJECT_MAPPER.convertValue(hit.getSourceAsMap(), AppointmentReminderCommMessage.class));
		}
		if (CollectionUtils.isEmpty(messages)) {
			logger.info("AppointmentReminderCommMessage List found empty for request {}", request);
			return ViewRecipientsListResultDTO.emptyResponse();
		}
		
		Set<Integer> customerIdsWithoutName = new HashSet<>();
		Set<Integer> locationIdsWithoutName = new HashSet<>();
		
		List<ViewRecipientsCommunicationDTO> requests = messages.stream().map(a -> getCommunicationDTOAppointmentReminder(a, customerIdsWithoutName, locationIdsWithoutName))
				.collect(Collectors.toList());
		populateBusinessAndCustomerDetailsForMissingListingData(requests, customerIdsWithoutName, locationIdsWithoutName, Constants.APPOINTMENT_REMINDER_TYPE);
		return new ViewRecipientsListResultDTO(response.getHits().getTotalHits().value, requests);
	}
	/**
	 * 
	 * View Recipients Listing - APPOINTMENT RECALL REQUEST
	 * 
	 * @param request
	 * @param tokenData
	 * @return
	 */
	private ViewRecipientsListResultDTO getListingAppointmentRecall(ViewRecipientsRequest request, Map<String, Object> tokenData, boolean isExternalRequest) {
		ElasticSearchBaseRequest esRequest;
		if(isExternalRequest) {
			esRequest = getExternalListingESQueryByCampaignType(CampaignTypeEnum.APPOINTMENT_RECALL, request, tokenData);
		} else {
			esRequest = getListingESQueryByCampaignType(CampaignTypeEnum.APPOINTMENT_RECALL, request, tokenData);
		}
		SearchResponse response = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.APPOINTMENT_RECALL_REPORT_INDEX).execute(esRequest);
		if (response == null || response.getHits() == null || response.getHits().getTotalHits().value == 0l) {
			logger.info("Empty response received from ES listing query for request {}", request);
			return ViewRecipientsListResultDTO.emptyResponse();
		}
		List<AppointmentRecallCommMessage> messages = new ArrayList<>();
		for (SearchHit hit : response.getHits().getHits()) {
			messages.add(OBJECT_MAPPER.convertValue(hit.getSourceAsMap(), AppointmentRecallCommMessage.class));
		}
		if (CollectionUtils.isEmpty(messages)) {
			logger.info("AppointmentRecallCommMessage List found empty for request {}", request);
			return ViewRecipientsListResultDTO.emptyResponse();
		}
		
		Set<Integer> customerIdsWithoutName = new HashSet<>();
		Set<Integer> locationIdsWithoutName = new HashSet<>();
		
		List<ViewRecipientsCommunicationDTO> requests = messages.stream().map(a -> getCommunicationDTOAppointmentRecall(a, customerIdsWithoutName, locationIdsWithoutName)).collect(Collectors.toList());
		populateBusinessAndCustomerDetailsForMissingListingData(requests, customerIdsWithoutName, locationIdsWithoutName, Constants.APPOINTMENT_RECALL_TYPE);
		return new ViewRecipientsListResultDTO(response.getHits().getTotalHits().value, requests);
	}
	
	/**
	 * 
	 * View Recipients Listing - APPOINTMENT FORM REQUEST
	 * 
	 * @param request
	 * @param tokenData
	 * @return
	 */
	private ViewRecipientsListResultDTO getListingAppointmentForm(ViewRecipientsRequest request, Map<String, Object> tokenData, boolean isExternalRequest) {
		ElasticSearchBaseRequest esRequest;
		if(isExternalRequest) {
			esRequest = getExternalListingESQueryByCampaignType(CampaignTypeEnum.APPOINTMENT_FORM, request, tokenData);
		} else {
			esRequest = getListingESQueryByCampaignType(CampaignTypeEnum.APPOINTMENT_FORM, request, tokenData);
		}
		SearchResponse response = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.APPOINTMENT_FORM_REPORT_INDEX).execute(esRequest);
		if (response == null || response.getHits() == null || response.getHits().getTotalHits().value == 0l) {
			logger.info("Empty response received from ES listing query for request {}", request);
			return ViewRecipientsListResultDTO.emptyResponse();
		}
		List<AppointmentFormCommMessage> messages = new ArrayList<>();
		for (SearchHit hit : response.getHits().getHits()) {
			messages.add(OBJECT_MAPPER.convertValue(hit.getSourceAsMap(), AppointmentFormCommMessage.class));
		}
		if (CollectionUtils.isEmpty(messages)) {
			logger.info("AppointmentFormCommMessage List found empty for request {}", request);
			return ViewRecipientsListResultDTO.emptyResponse();
		}
		
		Set<Integer> customerIdsWithoutName = new HashSet<>();
		Set<Integer> locationIdsWithoutName = new HashSet<>();
		
		List<ViewRecipientsCommunicationDTO> requests = messages.stream().map(a -> getCommunicationDTOAppointmentForm(a, customerIdsWithoutName, locationIdsWithoutName))
				.collect(Collectors.toList());
		populateBusinessAndCustomerDetailsForMissingListingData(requests, customerIdsWithoutName, locationIdsWithoutName, Constants.APPOINTMENT_FORM_TYPE);
		return new ViewRecipientsListResultDTO(response.getHits().getTotalHits().value, requests);
	}
	
	/**
	 * Represents a ROW in View Recipients Listing - Appointment Reminder
	 * 
	 * @param message
	 * @param customerIdsWithoutName
	 * @param locationIdsWithoutName
	 * @return
	 */
	
	private ViewRecipientsCommunicationDTO getCommunicationDTOAppointmentReminder(AppointmentReminderCommMessage message, Set<Integer> customerIdsWithoutName, Set<Integer> locationIdsWithoutName) {
		ViewRecipientsCommunicationDTO request = new ViewRecipientsCommunicationDTO();
		
		request.setCustomerId(message.getCustomerId());
		request.setCustomerName(message.getCustomerName());
		customerIdsWithoutName.add(message.getCustomerId());
		locationIdsWithoutName.add(message.getBusinessId());
		request.setBusinessId(message.getBusinessId());
		if (StringUtils.isNotBlank(message.getBusinessAlias())) {
			request.setLocation(message.getBusinessAlias());
		} 
//			else {
//			locationIdsWithoutName.add(message.getBusinessId());
//	    }
		request.setSource(message.getSource());
		request.setDisplaySource(ViewRecipientsUtils.getDisplaySource(message.getSource()));
		String status = ReportServiceUtils.getLatestStatusForCustomerForAppointmentReminderCampaign(message);
		request.setStatus(status);
		if (message.getRequestDate() != null) {
			String timeZoneId = businessService.getBusinessTimezoneId(message.getEnterpriseId());
			request.setLastActivityDate(DateTimeUtils.getDateAfterConversion(message.getUpdatedAt(), timeZoneId));
			request.setLastActivityDateWithTime(DateTimeUtils.getDateTimeAfterConversion(message.getUpdatedAt(), timeZoneId));
//			request.setLastActivityDate(ViewRecipientsUtils.getLastActivityDate(message.getUpdatedAt()));
//			request.setLastActivityDateWithTime(ViewRecipientsUtils.getLastActivityDateTime(message.getUpdatedAt()));
		}
		Integer failureId = ReportServiceUtils.getFailureIdForMessage(message.getFailureReason());
		request.setFailureId(failureId);
		request.setIsDND(ReportServiceUtils.isDNDScheduled(message.getStatus()));
		String failureReason = UsageReportsUtil.getFailureMessageForId(failureId);
		request.setFailureReason(failureReason);
		request.setAppointmentId(message.getAppointmentId() != null ? (message.getAppointmentId().intValue()) : null);
		return request;
	}
	
	/**
	 * Represents a ROW in View Recipients Listing - Appointment Recall
	 * 
	 * @param message
	 * @param customerIdsWithoutName
	 * @param locationIdsWithoutName
	 * @return
	 */
	
	private ViewRecipientsCommunicationDTO getCommunicationDTOAppointmentRecall(AppointmentRecallCommMessage message, Set<Integer> customerIdsWithoutName, Set<Integer> locationIdsWithoutName) {
		ViewRecipientsCommunicationDTO request = new ViewRecipientsCommunicationDTO();
		
		request.setCustomerId(message.getCustomerId());
		request.setCustomerName(message.getCustomerName());
		customerIdsWithoutName.add(message.getCustomerId());
		locationIdsWithoutName.add(message.getBusinessId());
		request.setBusinessId(message.getBusinessId());
		if (StringUtils.isNotBlank(message.getBusinessAlias())) {
			request.setLocation(message.getBusinessAlias());
		}
		
		request.setSource(message.getSource());
		request.setDisplaySource(ViewRecipientsUtils.getDisplaySource(message.getSource()));
		String status = ReportServiceUtils.getLatestStatusForCustomerForAppointmentRecallCampaign(message);
		request.setStatus(status);
		if (message.getRequestDate() != null) {
			String timeZoneId = businessService.getBusinessTimezoneId(message.getEnterpriseId());
			request.setLastActivityDate(DateTimeUtils.getDateAfterConversion(message.getUpdatedAt(), timeZoneId));
			request.setLastActivityDateWithTime(DateTimeUtils.getDateTimeAfterConversion(message.getUpdatedAt(), timeZoneId));
//			request.setLastActivityDate(ViewRecipientsUtils.getLastActivityDate(message.getUpdatedAt()));
//			request.setLastActivityDateWithTime(ViewRecipientsUtils.getLastActivityDateTime(message.getUpdatedAt()));
		}
		Integer failureId = ReportServiceUtils.getFailureIdForMessage(message.getFailureReason());
		request.setFailureId(failureId);
		request.setIsDND(ReportServiceUtils.isDNDScheduled(message.getStatus()));
		String failureReason = UsageReportsUtil.getFailureMessageForId(failureId);
		request.setFailureReason(failureReason);
		request.setAppointmentId(message.getAppointmentId() != null ? (message.getAppointmentId().intValue()) : null);
		request.setAppointmentRecallId(message.getAppointmentRecallId() != null ? (message.getAppointmentRecallId().intValue()) : null);
		return request;
	}
	
	/**
	 * Represents a ROW in View Recipients Listing - Appointment Form
	 * 
	 * @param message
	 * @param customerIdsWithoutName
	 * @param locationIdsWithoutName
	 * @return
	 */
	private ViewRecipientsCommunicationDTO getCommunicationDTOAppointmentForm(AppointmentFormCommMessage message, Set<Integer> customerIdsWithoutName, Set<Integer> locationIdsWithoutName) {
		ViewRecipientsCommunicationDTO request = new ViewRecipientsCommunicationDTO();
		
		request.setCustomerId(message.getCustomerId());
		request.setCustomerName(message.getCustomerName());
		customerIdsWithoutName.add(message.getCustomerId());
		locationIdsWithoutName.add(message.getBusinessId());
		request.setBusinessId(message.getBusinessId());
		if (StringUtils.isNotBlank(message.getBusinessAlias())) {
			request.setLocation(message.getBusinessAlias());
		}
		
		request.setSource(message.getSource());
		request.setDisplaySource(ViewRecipientsUtils.getDisplaySource(message.getSource()));
		String status = ReportServiceUtils.getLatestStatusForCustomerForAppointmentFormCampaign(message);
		request.setStatus(status);
		if (message.getRequestDate() != null) {
			String timeZoneId = businessService.getBusinessTimezoneId(message.getEnterpriseId());
			request.setLastActivityDate(DateTimeUtils.getDateAfterConversion(message.getUpdatedAt(), timeZoneId));
			request.setLastActivityDateWithTime(DateTimeUtils.getDateTimeAfterConversion(message.getUpdatedAt(), timeZoneId));
//			request.setLastActivityDate(ViewRecipientsUtils.getLastActivityDate(message.getUpdatedAt()));
//			request.setLastActivityDateWithTime(ViewRecipientsUtils.getLastActivityDateTime(message.getUpdatedAt()));
		}
		Integer failureId = ReportServiceUtils.getFailureIdForMessage(message.getFailureReason());
		request.setFailureId(failureId);
		request.setIsDND(ReportServiceUtils.isDNDScheduled(message.getStatus()));
		String failureReason = UsageReportsUtil.getFailureMessageForId(failureId);
		request.setFailureReason(failureReason);
		request.setAppointmentId(message.getAppointmentId() != null ? (message.getAppointmentId().intValue()) : null);
		return request;
	}
	
	/**
	 * Represents a ROW in View Recipients Listing - Promotion
	 * 
	 * @param message
	 * @param customerIdsWithoutName
	 * @param locationIdsWithoutName
	 * @return
	 */
	private ViewRecipientsCommunicationDTO getCommunicationDTOPromotion(PromotionCommMessage message, Set<Integer> customerIdsWithoutName, Set<Integer> locationIdsWithoutName, boolean showInfluenceMetric) {
		ViewRecipientsCommunicationDTO request = new ViewRecipientsCommunicationDTO();
		
		request.setCustomerId(message.getCustomerId());
		request.setCustomerName(message.getCustomerName());
		customerIdsWithoutName.add(message.getCustomerId());
		
		request.setBusinessId(message.getBusinessId());
		if (StringUtils.isNotBlank(message.getBusinessAlias())) {
			request.setLocation(message.getBusinessAlias());
		} else {
			locationIdsWithoutName.add(message.getBusinessId());
		}
		request.setSource(message.getSource());
		request.setDisplaySource(ViewRecipientsUtils.getDisplaySource(message.getSource()));
		String status = ReportServiceUtils.getLatestStatusForCustomerForPromotionCampaign(message, showInfluenceMetric);
		request.setStatus(status);
		request.setLocation(message.getBusinessAlias());
		if (message.getRequestDate() != null) {
//			request.setLastActivityDate(ViewRecipientsUtils.getLastActivityDate(message.getUpdatedAt()));
//			request.setLastActivityDateWithTime(ViewRecipientsUtils.getLastActivityDateTime(message.getUpdatedAt()));
			String timeZoneId = businessService.getBusinessTimezoneId(message.getEnterpriseId());
			request.setLastActivityDate(DateTimeUtils.getDateAfterConversion(message.getUpdatedAt(), timeZoneId));
			request.setLastActivityDateWithTime(DateTimeUtils.getDateTimeAfterConversion(message.getUpdatedAt(), timeZoneId));
		}
		Integer failureId = ReportServiceUtils.getFailureIdForMessage(message.getFailureReason());
		request.setFailureId(failureId);
		request.setIsDND(ReportServiceUtils.isDNDScheduled(message.getStatus()));
		String failureReason = UsageReportsUtil.getFailureMessageForId(failureId);
		request.setFailureReason(failureReason);
		return request;
	}
	
	/**
	 * 
	 * Populates missing details of ES Documents
	 * - Customer Name
	 * - Location Name/Alias
	 * 
	 * @param customerCampaignStatusSet
	 * @param customerIds
	 * @param businessIdsForRequest
	 */
	private void populateBusinessAndCustomerDetailsForMissingListingData(List<ViewRecipientsCommunicationDTO> customerCampaignStatusSet, Set<Integer> customerIds, Set<Integer> businessIdsForRequest, String campaignType) {
		Map<Integer, CustomerInfoResponse> idToCustomerMessageMap = null;
		if (CollectionUtils.isNotEmpty(customerIds)) {
			idToCustomerMessageMap = customerService.getCustomersMap(customerIds);
		}
		Map<Integer, BusinessEnterpriseEntity> idToBusinessMap = null;
		if (CollectionUtils.isNotEmpty(businessIdsForRequest)) {
			idToBusinessMap = businessService.getIdToEnterpriseMapForIds(businessIdsForRequest);
		}
		for (ViewRecipientsCommunicationDTO viewRecipientsCommunicationDTO : customerCampaignStatusSet) {
			if (idToCustomerMessageMap != null
					&& idToCustomerMessageMap.containsKey(viewRecipientsCommunicationDTO.getCustomerId())) {
				viewRecipientsCommunicationDTO.setCustomerName(
						idToCustomerMessageMap.get(viewRecipientsCommunicationDTO.getCustomerId()).getDisplayName());
			}
			if (idToCustomerMessageMap != null
					&& idToCustomerMessageMap.containsKey(viewRecipientsCommunicationDTO.getCustomerId())) {
				viewRecipientsCommunicationDTO.setCustomerEmail(
						idToCustomerMessageMap.get(viewRecipientsCommunicationDTO.getCustomerId()).getEmail());
			}
			if (idToCustomerMessageMap != null
					&& idToCustomerMessageMap.containsKey(viewRecipientsCommunicationDTO.getCustomerId())) {
				viewRecipientsCommunicationDTO.setCustomerPhone(
						idToCustomerMessageMap.get(viewRecipientsCommunicationDTO.getCustomerId()).getPhone());
			}
			if (idToBusinessMap != null
					&& idToBusinessMap.containsKey(viewRecipientsCommunicationDTO.getBusinessId())) {
				viewRecipientsCommunicationDTO.setLocation(
						idToBusinessMap.get(viewRecipientsCommunicationDTO.getBusinessId()).getBusinessAlias());
			}
			if (idToBusinessMap != null && idToBusinessMap.containsKey(viewRecipientsCommunicationDTO.getBusinessId())
					&& StringUtils.equalsAnyIgnoreCase(campaignType, Constants.APPOINTMENT_REMINDER_TYPE, Constants.APPOINTMENT_RECALL_TYPE, Constants.APPOINTMENT_FORM_TYPE)) {
				viewRecipientsCommunicationDTO.setBusinessNumber(idToBusinessMap.get(viewRecipientsCommunicationDTO.getBusinessId()).getBusinessId());
			}
		}
	}
	
	private ElasticSearchBaseRequest getExternalListingESQueryByCampaignType(CampaignTypeEnum campaignType, ViewRecipientsRequest request, Map<String, Object> tokenData) {
		switch (campaignType) {
			case REVIEW_REQUEST:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData,
						ElasticQueryTemplateEnum.EXTERNAL_VIEW_RECIPIENTS_LIST_RR_V1.getQueryName(), Constants.RR_REPORT_INDEX, Constants.RR_REPORT_TYPE);
			case SURVEY_REQUEST:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData,
						ElasticQueryTemplateEnum.EXTERNAL_VIEW_RECIPIENTS_LIST_SURVEY_V1.getQueryName(), Constants.SURVEY_REPORT_INDEX, Constants.SURVEY_REPORT_TYPE);
			case CX_REQUEST:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData,
						ElasticQueryTemplateEnum.EXTERNAL_VIEW_RECIPIENTS_LIST_CX_V1.getQueryName(), Constants.CX_REPORT_INDEX, Constants.CX_REPORT_TYPE);
			case REFERRAL:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData,
						ElasticQueryTemplateEnum.EXTERNAL_VIEW_RECIPIENTS_LIST_REFERRAL_V1.getQueryName(), Constants.REFERRAL_REPORT_INDEX, Constants.REFERRAL_REPORT_TYPE);
			case PROMOTIONAL:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData,
						ElasticQueryTemplateEnum.EXTERNAL_VIEW_RECIPIENTS_LIST_PROMOTION_V1.getQueryName(), Constants.PROMOTION_REPORT_INDEX, Constants.PROMOTION_REPORT_TYPE);
			case APPOINTMENT_REMINDER:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData, ElasticQueryTemplateEnum.EXTERNAL_VIEW_RECIPIENTS_LIST_APPOINTMENT_REMINDER_V1.getQueryName(),
						Constants.APPOINTMENT_REMINDER_REPORT_INDEX, Constants.APPOINTMENT_REMINDER_REPORT_TYPE);
			case APPOINTMENT_RECALL:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData, ElasticQueryTemplateEnum.EXTERNAL_VIEW_RECIPIENTS_LIST_APPOINTMENT_RECALL.getQueryName(),
						Constants.APPOINTMENT_RECALL_REPORT_INDEX, Constants.APPOINTMENT_RECALL_REPORT_TYPE);
			case APPOINTMENT_FORM:
				return elasticSearchHelperService.prepareElasticSearchRequest(request.getEnterpriseId(), tokenData, ElasticQueryTemplateEnum.EXTERNAL_VIEW_RECIPIENTS_LIST_APPOINTMENT_FORM.getQueryName(),
						Constants.APPOINTMENT_FORM_REPORT_INDEX, Constants.APPOINTMENT_FORM_REPORT_TYPE);
			default:
				return null;
		}
	}
	

	private boolean showInfluenceMetric(String emailTemplateCategory, String smsTemplateCategory) {
		boolean isEmailMarketing = StringUtils.equalsIgnoreCase(emailTemplateCategory, CommunicationCategoryEnum.MARKETING.getCommunicationCategory());
		boolean isSmsMarketing = StringUtils.equalsIgnoreCase(smsTemplateCategory, CommunicationCategoryEnum.MARKETING.getCommunicationCategory());
		
		if (StringUtils.isBlank(emailTemplateCategory) && StringUtils.isBlank(smsTemplateCategory)) {
			return false;
		}
		
		if (StringUtils.isBlank(emailTemplateCategory)) {
			return isSmsMarketing;
		}
		
		if (StringUtils.isBlank(smsTemplateCategory)) {
			return isEmailMarketing;
		}
		
		return isEmailMarketing && isSmsMarketing;
	}

}
