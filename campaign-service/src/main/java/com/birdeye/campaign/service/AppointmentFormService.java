package com.birdeye.campaign.service;

import java.util.List;

import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.ReviewRequest;
import com.birdeye.campaign.response.external.AppointmentDetailsResponse;

public interface AppointmentFormService {
	
	void validateFormExecutionRequest(ReviewRequest reviewRequest, Integer appointmentId, Integer id, Integer enterpriseId, BusinessEnterpriseEntity business);
		
	List<AppointmentDetailsResponse> getAppointmentDetailsInBatch(List<Integer> appointmentIds);
	
	void validateManualAppointmentFormExecutionRequest(ReviewRequest reviewRequest, Integer appointmentId, Campaign campaign);
	
	String getFormUrl(Campaign campaign, ReviewRequest reviewRequest, Integer enterpriseId);
	
	boolean validateManualCustomAppointmentFormExecutionRequest(ReviewRequest reviewRequest, Integer appointmentId, Campaign campaign);
	
	boolean validateCustomFormExecutionRequest(ReviewRequest reviewRequest, Integer appointmentId, Integer campaignId, Integer enterpriseId, BusinessEnterpriseEntity business);
	
	boolean isManualCampaignCustomAppointmentRequest(Campaign campaign, ReviewRequest reviewRequest, Integer enterpriseId);
	
	String getFormUrl(Integer templateId, String source, Integer enterpriseId, Integer isAppointmentCampaign);
	
	boolean isFormUrlAvailableForCampaign(Campaign campaign);
	
	boolean isManualCampaignAppointmentFormRequest(Campaign campaign);
	
	boolean isQuickSendCustomAppointmentRequest(Campaign campaign, ReviewRequest reviewRequest, Integer enterpriseId);
	
}
