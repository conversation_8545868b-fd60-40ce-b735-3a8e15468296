package com.birdeye.campaign.service.impl;

import java.util.List;


import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.TextStringBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.entity.ReviewRequest;
import com.birdeye.campaign.platform.entity.HtmlTemplates;
import com.birdeye.campaign.platform.repository.HtmlTemplatesRepo;
import com.birdeye.campaign.response.template.v2.SurveyEmbeddedResponse;
import com.birdeye.campaign.service.SurveyEmbedService;

@Service("surveyEmbedService")
public class SurveyEmbedServiceImpl implements SurveyEmbedService {
	
	@Autowired
	private HtmlTemplatesRepo htmlTemplatesRepo;
	
	@Override
	public TextStringBuilder getSurveyEmbedContent(BusinessEnterpriseEntity business, TextStringBuilder content, SurveyEmbeddedResponse embeddedSurveyField, ReviewRequest reviewRequest, String surveyLink) {
		String surveyFieldType = embeddedSurveyField.getType();
		//List<HtmlTemplates> embedHtmlTemplates = htmlTemplatesRepo.findByTemplateType("survey_request_embed");
		//if (CollectionUtils.isNotEmpty(embedHtmlTemplates) && StringUtils.isNotEmpty(embedHtmlTemplates.get(0).getContent())) {
			String positiveLabel = embeddedSurveyField.getMaxValueLabel();
			String negativeLabel = embeddedSurveyField.getMinValueLabel();
			surveyLink = surveyLink + "&selectedAnswer=";
			//content = new TextStringBuilder(embedHtmlTemplates.get(0).getContent());
			// BIRDEYE-107373 Handling Embbed survey in custom HTML
			content.replaceAll("<!-- Embedded", StringUtils.EMPTY);
			content.replaceAll("Embedded -->", StringUtils.EMPTY);
			replaceGeneralEmbedEmailTokens(content, surveyLink, positiveLabel, negativeLabel, embeddedSurveyField);
			switch (surveyFieldType) {
			case "nps":
				for(int i=0; i<=10; i++) {
					content.replaceAll("<!-- [include "+i+" rating start]", StringUtils.EMPTY);
					content.replaceAll("[include "+i+" rating end] -->", StringUtils.EMPTY);
					// handling for survey embed content in custom HTML 
					content.replaceAll("<!__ [include "+i+" rating start]", StringUtils.EMPTY);
					content.replaceAll("[include "+i+" rating end] __>", StringUtils.EMPTY);
				}
				break;
			
			case "rating":
				Integer maxRating = embeddedSurveyField.getMaxValue();
				for(int i=1; i<=maxRating; i++) {
					content.replaceAll("<!-- [include "+i+" rating start]", StringUtils.EMPTY);
					content.replaceAll("[include "+i+" rating end] -->", StringUtils.EMPTY);
					// BIRDEYE-107373 Handling Embbed survey in custom HTML
					content.replaceAll("<!__ [include "+i+" rating start]", StringUtils.EMPTY);
					content.replaceAll("[include "+i+" rating end] __>", StringUtils.EMPTY);
				}
				content.replaceAll("<!__ [include 0 rating start]", "<!-- [include 0 rating start]");
				content.replaceAll("[include 0 rating end] __>", "[include 0 rating end] -->");
				// commenting blocks which are greater than max rating.
				for (int i = maxRating + 1; i <= 10; i++) {
					content.replaceAll("<!__ [include " + i + " rating start]", "<!-- [include " + i + " rating start]");
					content.replaceAll("[include " + i + " rating end] __>", "[include " + i + " rating end] -->");
				}
				break;
			
			default:
				break;
			}
		//}
		return content;
	}
	
	private void replaceGeneralEmbedEmailTokens(TextStringBuilder content, String surveyLink, String positiveLabel, String negativeLabel, SurveyEmbeddedResponse embeddedSurveyField) {
		content.replaceAll("[nps star url 0]", surveyLink + "0");
		content.replaceAll("[nps star url 1]", surveyLink + "1");
		content.replaceAll("[nps star url 2]", surveyLink + "2");
		content.replaceAll("[nps star url 3]", surveyLink + "3");
		content.replaceAll("[nps star url 4]", surveyLink + "4");
		content.replaceAll("[nps star url 5]", surveyLink + "5");
		content.replaceAll("[nps star url 6]", surveyLink + "6");
		content.replaceAll("[nps star url 7]", surveyLink + "7");
		content.replaceAll("[nps star url 8]", surveyLink + "8");
		content.replaceAll("[nps star url 9]", surveyLink + "9");
		content.replaceAll("[nps star url 10]", surveyLink + "10");
		content.replaceAll("[Positive Label]", positiveLabel);
		content.replaceAll("[Negative Label]", negativeLabel);
		content.replaceAll("[Question]", embeddedSurveyField.getLabel());
	}
	
	@Override
	public boolean updateAMPTemplate(String content) {
		List<HtmlTemplates> templates = htmlTemplatesRepo.findByTemplateType("survey_request_amp_v1");
		if(CollectionUtils.isEmpty(templates)) {
			return false;
		}
		HtmlTemplates template = templates.stream().findFirst().get();
		template.setContent(content);
		htmlTemplatesRepo.saveAndFlush(template);
		return true;
	}
	
}