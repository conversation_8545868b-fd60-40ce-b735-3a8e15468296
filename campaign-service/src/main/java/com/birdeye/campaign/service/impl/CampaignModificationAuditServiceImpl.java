package com.birdeye.campaign.service.impl;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.birdeye.campaign.aspect.annotation.Profiled;
import com.birdeye.campaign.business.service.BusinessService;
import com.birdeye.campaign.cdc.dto.AllCampaignDataDTO;
import com.birdeye.campaign.cdc.dto.EntityChangeLogDto;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.constant.KafkaTopicTypeEnum;
import com.birdeye.campaign.dto.AllTemplateDataDto;
import com.birdeye.campaign.dto.BusinessEmailTemplateDTO;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.BusinessSmsTemplateDto;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.dto.CampaignModificationEvent;
import com.birdeye.campaign.dto.KafkaMessage;
import com.birdeye.campaign.dto.SplitCampaignMappingDTO;
import com.birdeye.campaign.entity.BusinessDeeplinkPriority;
import com.birdeye.campaign.entity.BusinessEmailTemplate;
import com.birdeye.campaign.entity.BusinessSmsTemplate;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.CampaignEntitiesChangeLog;
import com.birdeye.campaign.entity.CampaignModificationAudit;
import com.birdeye.campaign.exception.CampaignBadRequestException;
import com.birdeye.campaign.kafka.service.KafkaService;
import com.birdeye.campaign.platform.constant.CampaignModificationUserActionEnum;
import com.birdeye.campaign.readonly.repository.CampaignReadOnlyRepo;
import com.birdeye.campaign.repository.BusinessEmailTemplateRepo;
import com.birdeye.campaign.repository.BusinessSMSTemplateRepo;
import com.birdeye.campaign.repository.CampaignModificationAuditRepo;
import com.birdeye.campaign.request.ChangeLogsRequest;
import com.birdeye.campaign.response.ChangeLogsResponse;
import com.birdeye.campaign.response.ChangeLogsResponse.AuditLogs;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.CampaignModificationAuditService;
import com.birdeye.campaign.service.dao.BusinessDeeplinkPriorityDao;
import com.birdeye.campaign.service.dao.CampaignEntitiesChangeLogDao;
import com.birdeye.campaign.service.dao.SplitCampaignModificationAuditDao;
import com.birdeye.campaign.utils.CampaignModificationAuditUtils;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.DateTimeUtils;
import com.birdeye.campaign.utils.ModifiedFieldNameMapper;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class CampaignModificationAuditServiceImpl implements CampaignModificationAuditService {
	
	@Autowired
	private BusinessDeeplinkPriorityDao		businessDeeplinkPriorityDaoService;
	
	@Autowired
	CampaignModificationAuditRepo			campaignModificationAuditRepo;
	
	@Autowired
	SplitCampaignModificationAuditDao		splitModificationAuditDao;
	
	@Autowired
	private KafkaService					kafkaService;
	
	@Autowired
	private CampaignEntitiesChangeLogDao	campaignEntitiesChangeLogDao;
	
	@Autowired
	private CampaignReadOnlyRepo			campaignReadOnlyRepo;
	
	@Autowired
	private BusinessSMSTemplateRepo			smsTemplateRepo;
	
	@Autowired
	private BusinessEmailTemplateRepo		emailTemplateRepo;
	
	@Autowired
	private CacheService					cacheService;
	
	@Autowired
	private BusinessService					businessService;
	
	private static final Logger				LOG	= LoggerFactory.getLogger(CampaignModificationAuditServiceImpl.class);
	
	@Override
	@Async
	public void createAuditForDefaultTemplates(Integer businessId, Integer emailTemplateId, Integer smsTemplateId, Integer campaignId, String templateType) {
		if (StringUtils.equalsIgnoreCase(templateType, "appointment_reminder")) {
			if (emailTemplateId != null) {
				String msg = "Default Appointment Reminder email template created";
				CampaignModificationAudit audit = CampaignModificationAudit.builder().withEmailTemplateId(emailTemplateId).withCampaignId(campaignId).withBusinessId(businessId).withEventMessage(msg)
						.build();
				campaignModificationAuditRepo.saveAndFlush(audit);
			}
			if (smsTemplateId != null) {
				String msg = "Default Appointment Reminder sms template created";
				CampaignModificationAudit audit = CampaignModificationAudit.builder().withSmsTemplateId(smsTemplateId).withCampaignId(campaignId).withBusinessId(businessId).withEventMessage(msg)
						.build();
				campaignModificationAuditRepo.saveAndFlush(audit);
			}
		} else if(StringUtils.equalsIgnoreCase(templateType, "review_request_new")){
			if (emailTemplateId != null) {
				List<BusinessDeeplinkPriority> emailPriorities = businessDeeplinkPriorityDaoService.getDeeplinksByTemplateIdAndSource(emailTemplateId, Constants.DEVICE_TYPE_WEB);
				String msg = "Default email template created with sources : " + emailPriorities.stream().map(p -> p.getSourceId()).collect(Collectors.toList());
				CampaignModificationAudit audit = CampaignModificationAudit.builder().withEmailTemplateId(emailTemplateId).withCampaignId(campaignId).withBusinessId(businessId).withEventMessage(msg)
						.build();
				campaignModificationAuditRepo.saveAndFlush(audit);
			}
			if (smsTemplateId != null) {
				List<BusinessDeeplinkPriority> smsPriorities = businessDeeplinkPriorityDaoService.getDeeplinksByTemplateIdAndSource(smsTemplateId, Constants.DEEPLINK_DEVICETYPE_IOS);
				String msg = "Default sms template created with sources : " + smsPriorities.stream().map(p -> p.getSourceId()).collect(Collectors.toList());
				CampaignModificationAudit audit = CampaignModificationAudit.builder().withSmsTemplateId(smsTemplateId).withCampaignId(campaignId).withBusinessId(businessId).withEventMessage(msg)
						.build();
				campaignModificationAuditRepo.saveAndFlush(audit);
			}
		} else if (StringUtils.equalsIgnoreCase(templateType, "appointment_recall")) {
			if (emailTemplateId != null) {
				String msg = "Default Appointment Recall email template created";
				CampaignModificationAudit audit = CampaignModificationAudit.builder().withEmailTemplateId(emailTemplateId).withCampaignId(campaignId).withBusinessId(businessId).withEventMessage(msg)
						.build();
				campaignModificationAuditRepo.saveAndFlush(audit);
			}
			if (smsTemplateId != null) {
				String msg = "Default Appointment Recall sms template created";
				CampaignModificationAudit audit = CampaignModificationAudit.builder().withSmsTemplateId(smsTemplateId).withCampaignId(campaignId).withBusinessId(businessId).withEventMessage(msg)
						.build();
				campaignModificationAuditRepo.saveAndFlush(audit);
			}
		}
	}
	
	@Override
	@Async
	public void createAuditForDefaultPromotionalTemplates(Integer businessId, Integer emailTemplateId, Integer smsTemplateId, String templateName) {
		if (emailTemplateId != null) {
			String msg = templateName + " Default Custom email template created";
			CampaignModificationAudit audit = CampaignModificationAudit.builder().withEmailTemplateId(emailTemplateId).withBusinessId(businessId).withEventMessage(msg).build();
			campaignModificationAuditRepo.saveAndFlush(audit);
		}
		if (smsTemplateId != null) {
			String msg = templateName + " Default Custom sms template created";
			CampaignModificationAudit audit = CampaignModificationAudit.builder().withSmsTemplateId(smsTemplateId).withBusinessId(businessId).withEventMessage(msg).build();
			campaignModificationAuditRepo.saveAndFlush(audit);
		}
	}
	
	@Override
	@Async
	public void auditCleanedUpPriority(Integer accountId, Integer templateId, String deviceType, Integer sourceId) {
		
		if (Constants.DEVICE_TYPE_WEB.equalsIgnoreCase(deviceType)) {
			CampaignModificationAudit audit = CampaignModificationAudit.builder().withBusinessId(accountId).withEmailTemplateId(templateId)
					.withEventMessage("Priority with sourceId : " + sourceId + " cleaned up as it was not aggregated").build();
			campaignModificationAuditRepo.saveAndFlush(audit);
		} else {
			CampaignModificationAudit audit = CampaignModificationAudit.builder().withBusinessId(accountId).withSmsTemplateId(templateId)
					.withEventMessage("Priority with sourceId : " + sourceId + " cleaned up as it was not aggregated").build();
			campaignModificationAuditRepo.saveAndFlush(audit);
		}
	}
	
	/**
	 * BIRD-72687
	 * Async Method to prepare & publish the campaign modification event.
	 * 
	 * 
	 * @param oldCampaign
	 * @param onGoingCampaign
	 * @param userId
	 * @param modificationActionType
	 */
	@Override
	@Async
	public void prepareAndPublishCampaignModificationEvent(AllCampaignDataDTO oldCampaign, AllCampaignDataDTO onGoingCampaign, Integer userId, String modificationActionType,
			Long modificationTimeStamp, Integer accountId, boolean isSplitCampaign) {
		if (Objects.isNull(oldCampaign) || Objects.isNull(onGoingCampaign)) {
			LOG.warn("prepareAndPublishCampaignModificationEvent::CampaignModificationAuditServiceImpl - Received empty data for {} campaign!", Objects.isNull(oldCampaign)? "old": "ongoing");
			return;
		}
		
		CampaignModificationEvent event = CampaignModificationAuditUtils.prepareModificationEvent(
				CampaignModificationAuditUtils.populateModifiedEntitiesMap(oldCampaign, CampaignModificationAuditUtils.CAMPAIGN_DATA),
				CampaignModificationAuditUtils.populateModifiedEntitiesMap(onGoingCampaign, CampaignModificationAuditUtils.CAMPAIGN_DATA), userId, modificationActionType,
				modificationTimeStamp, CampaignModificationAuditUtils.CAMPAIGN_ENTITY, accountId, onGoingCampaign.getCampaignEntityData().getId(),
				BooleanUtils.isTrue(isSplitCampaign) ? Constants.SPLIT : StringUtils.EMPTY);
		kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_USER_ACTIVITY_AUDIT, new KafkaMessage(event));
	}
	
	/**
	 * Prepare & Publish Default Template Update Event.
	 * 
	 * @param oldDefaultTemplateId
	 * @param currentDefaultTemplateId
	 * @param userId
	 * @param enterpriseId
	 * @param modificationTimeStamp
	 * @param templateCategory
	 */
	@Override
	@Async
	public void prepareAndPublishDefaultTemplateUpdateEvent(Integer oldDefaultTemplateId, Integer currentDefaultTemplateId, Integer userId, Integer enterpriseId,
			Long modificationTimeStamp, String templateCategory) {
		if (!Objects.isNull(oldDefaultTemplateId)) {
			CampaignModificationEvent modificationEvent = CampaignModificationAuditUtils.prepareModificationEventForDefaultTemplateUpdate(oldDefaultTemplateId, enterpriseId,
					userId, modificationTimeStamp, templateCategory, false);
			kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_USER_ACTIVITY_AUDIT, new KafkaMessage(modificationEvent));
		}
		
		if (!Objects.isNull(currentDefaultTemplateId)) {
			CampaignModificationEvent modificationEvent = CampaignModificationAuditUtils.prepareModificationEventForDefaultTemplateUpdate(currentDefaultTemplateId, enterpriseId,
					userId, modificationTimeStamp, templateCategory, true);
			kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_USER_ACTIVITY_AUDIT, new KafkaMessage(modificationEvent));
		}
	}
	
	/**
	 * Prepare & Publish Template Delete Event(For Auditing Purpose).
	 * 
	 * @param templateId
	 * @param templateType (sms/email)
	 * @param userId
	 * @param enterpriseId
	 * @param modificationTimeStamp
	 */
	@Override
	@Async
	public void prepareAndPublishTemplateDeleteEvent(Integer templateId, String templateType, Integer userId, Integer enterpriseId, Long modificationTimeStamp) {
		CampaignModificationEvent modificationEvent = null;
		if (StringUtils.equalsIgnoreCase(templateType, Constants.SMS_TYPE)) {
			AllTemplateDataDto oldTemplateData = new AllTemplateDataDto();
			oldTemplateData.setBusinessSmsTemplate(new BusinessSmsTemplateDto());
			oldTemplateData.getBusinessSmsTemplate().setId(templateId);
			oldTemplateData.getBusinessSmsTemplate().setIsDeleted(0);
			
			AllTemplateDataDto newTemplateData = new AllTemplateDataDto();
			newTemplateData.setBusinessSmsTemplate(new BusinessSmsTemplateDto());
			newTemplateData.getBusinessSmsTemplate().setId(templateId);
			newTemplateData.getBusinessSmsTemplate().setIsDeleted(1);
			
			modificationEvent = CampaignModificationAuditUtils.prepareModificationEvent(
					CampaignModificationAuditUtils.populateModifiedEntitiesMap(oldTemplateData, CampaignModificationAuditUtils.TEMPLATE_DATA),
					CampaignModificationAuditUtils.populateModifiedEntitiesMap(newTemplateData, CampaignModificationAuditUtils.TEMPLATE_DATA), userId,
					CampaignModificationUserActionEnum.DELETE.getUserActionType(), modificationTimeStamp, CampaignModificationAuditUtils.TEMPLATE_ENTITY, enterpriseId, templateId,
					templateType);
		} else if (StringUtils.equalsIgnoreCase(templateType, Constants.EMAIL_TYPE)) {
			AllTemplateDataDto oldTemplateData = new AllTemplateDataDto();
			oldTemplateData.setBusinessEmailTemplate(new BusinessEmailTemplateDTO());
			oldTemplateData.getBusinessEmailTemplate().setEmailTemplateId(templateId);
			oldTemplateData.getBusinessEmailTemplate().setIsDeleted(0);
			
			AllTemplateDataDto newTemplateData = new AllTemplateDataDto();
			newTemplateData.setBusinessEmailTemplate(new BusinessEmailTemplateDTO());
			newTemplateData.getBusinessEmailTemplate().setEmailTemplateId(templateId);
			newTemplateData.getBusinessEmailTemplate().setIsDeleted(1);
			
			modificationEvent = CampaignModificationAuditUtils.prepareModificationEvent(
					CampaignModificationAuditUtils.populateModifiedEntitiesMap(oldTemplateData, CampaignModificationAuditUtils.TEMPLATE_DATA),
					CampaignModificationAuditUtils.populateModifiedEntitiesMap(newTemplateData, CampaignModificationAuditUtils.TEMPLATE_DATA), userId,
					CampaignModificationUserActionEnum.DELETE.getUserActionType(), modificationTimeStamp, CampaignModificationAuditUtils.TEMPLATE_ENTITY, enterpriseId, templateId,
					templateType);
		}
		
		if (!Objects.isNull(modificationEvent)) {
			kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_USER_ACTIVITY_AUDIT, new KafkaMessage(modificationEvent));
		}
	}
	
	/**
	 * BIRD-74509 | Method to prepare & publish the campaign modification event.
	 * 
	 * 
	 * @param oldDataMap,newDataMap,userId,modificationActionType,modificationTimeStamp,entityName,accountId,entityId,
	 *            eventSubType
	 */
	private void prepareAndPublishModificationEvent(Map<String, Object> oldDataMap, Map<String, Object> newDataMap, Integer userId, String modificationActionType, Long modificationTimeStamp,
			String entityName, Integer accountId, Integer entityId, String eventSubType) {
		if (Objects.isNull(oldDataMap) || Objects.isNull(newDataMap)) {
			LOG.warn("prepareAndPublishModificationEvent - Received empty data for Event!");
			return;
		}
		
		CampaignModificationEvent event = CampaignModificationAuditUtils.prepareModificationEvent(oldDataMap, newDataMap, userId, modificationActionType, modificationTimeStamp, entityName, accountId,
				entityId, eventSubType);
		kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_USER_ACTIVITY_AUDIT, new KafkaMessage(event));
	}
	
	/**
	 * 
	 * BIRD-74509 | Process Event and audit changed made to templates/campaigns.
	 * 1. Validate the request
	 * 2. Evaluate and fetch changes to templates/campaigns.
	 * 3. Audit in the db.
	 *
	 * @param modificationEvent
	 * 
	 */
	@Override
	public void detectAndAuditChanges(CampaignModificationEvent modificationEvent) {
		if (BooleanUtils.isFalse(validateModificationEvent(modificationEvent))) {
			return;
		}
		try {
			EntityChangeLogDto dataDifference = performActionBasedOnEventType(modificationEvent);
			if (Objects.isNull(dataDifference) || MapUtils.isEmpty(dataDifference.getDataDiffMap())) {
				LOG.info("detectAndAuditChanges - Evaluated data difference is null for {} having id: {}!", modificationEvent.getEntityName(), modificationEvent.getEntityId());
				return;
			}
			campaignEntitiesChangeLogDao.saveCampaignEntitiesChangeLog(
					CampaignModificationAuditUtils.prepareCampaignChangeLogAudit(modificationEvent.getAccountId(), modificationEvent.getModificationActionType(), modificationEvent.getEntityName(),
							modificationEvent.getEntityId(), modificationEvent.getModificationTimeStamp(), modificationEvent.getUserId(), dataDifference, modificationEvent.getEntitySubType()));
			
			cacheService.evictModificationAuditCache(modificationEvent.getEntityId(), modificationEvent.getEntityName(),
					StringUtils.equalsAnyIgnoreCase(modificationEvent.getEntitySubType(), Constants.SPLIT, Constants.EMAIL_TYPE, Constants.SMS_TYPE)
							? modificationEvent.getEntitySubType()
							: StringUtils.EMPTY);
		} catch (Exception e) {
			LOG.error("detectAndAuditChanges - Error while evaluating diff and audit change log for {} having id {} :: Exception {}", modificationEvent.getEntityName(),
					modificationEvent.getEntityId(), ExceptionUtils.getStackTrace(e));
		}
	}
	
	/**
	 * 
	 * BIRD-74509 | Based on Action and event type fetch changes made to templates/campaigns.
	 * 
	 * 1. Validate the request
	 * 2. Evaluate and fetch changes to templates/campaigns.
	 *
	 * @param modificationEvent
	 * 
	 */
	private EntityChangeLogDto performActionBasedOnEventType(CampaignModificationEvent modificationEvent) throws Exception {
		CampaignModificationUserActionEnum userActionEnum = CampaignModificationUserActionEnum.getEnum(modificationEvent.getModificationActionType());
		switch (userActionEnum) {
			case CREATE:
				Object entity = fetchDataObject(
						CampaignModificationAuditUtils.prepareSerializedStringForObject(
								modificationEvent.getNewEntities().get(CampaignModificationAuditUtils.getKeyBasedUponEventType(modificationEvent.getEntityName()))),
						CampaignModificationAuditUtils.findClassBasesUponEventType(modificationEvent.getEntityName()));
				return new EntityChangeLogDto(CampaignModificationAuditUtils.fetchFieldsAndData(entity), new ArrayList<>());
			case EDIT:
			case PAUSE:
			case RESUME:
			case DELETE:
			case MARK_DEFAULT:
			case UNMARK_DEFAULT:
			case CAMPAIGN_COMPLETE:
			case STATUS_UPDATE:
				Object oldEntity = fetchDataObject(
						CampaignModificationAuditUtils.prepareSerializedStringForObject(
								modificationEvent.getOldEntities().get(CampaignModificationAuditUtils.getKeyBasedUponEventType(modificationEvent.getEntityName()))),
						CampaignModificationAuditUtils.findClassBasesUponEventType(modificationEvent.getEntityName()));
				Object newEntity = fetchDataObject(
						CampaignModificationAuditUtils.prepareSerializedStringForObject(
								modificationEvent.getNewEntities().get(CampaignModificationAuditUtils.getKeyBasedUponEventType(modificationEvent.getEntityName()))),
						CampaignModificationAuditUtils.findClassBasesUponEventType(modificationEvent.getEntityName()));
				return CampaignModificationAuditUtils.evaluateDataDifference(oldEntity, newEntity);
			default:
				LOG.warn("performActionBasedOnEventType::CampaignModificationAuditServiceImpl - Received action type : {} not recognised!",
						modificationEvent.getModificationActionType());
				return null;
			
		}
	}
	
	/**
	 * 
	 * BIRD-74509 | Convert given string to corresponding object using object mapper
	 *
	 * @param entityString,
	 *            targetClass
	 * 
	 */
	private <T> T fetchDataObject(String entityString, Class<T> targetClass) {
		if(StringUtils.isBlank(entityString) || Objects.isNull(targetClass)) {
			LOG.warn("fetchDataObject::CampaignModificationAuditServiceImpl - Blank/NULL values received for entityString or targetClass!");
			return null;
		}
		
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.S"));
			return objectMapper.readValue(entityString, targetClass);
		} catch (Exception e) {
			LOG.error("Exception occurred while evaluating changed data. Exception: {}.", ExceptionUtils.getStackTrace(e));
			return null;
		}
	}
	
	/**
	 * Method to validate campaign modification event.
	 * 
	 * @param modificationEvent
	 */
	private Boolean validateModificationEvent(CampaignModificationEvent modificationEvent) {
		if (Objects.isNull(modificationEvent)) {
			LOG.error("validateModificationEvent::CampaignModificationAuditServiceImpl - Received empty modification event!");
			return false;
		}
		
		if (CollectionUtils.isEmpty(modificationEvent.getOldEntities()) && CollectionUtils.isEmpty(modificationEvent.getNewEntities())) {
			LOG.error("validateModificationEvent::CampaignModificationAuditServiceImpl - Received empty values for old and new entity modification data!");
			return false;
		}
		
		if (StringUtils.isBlank(modificationEvent.getEntityName()) || Objects.isNull(modificationEvent.getEntityId())) {
			LOG.error("validateModificationEvent::CampaignModificationAuditServiceImpl -  Received blank value for modified entity name or id!");
			return false;
		}
		
		if (Objects.isNull(modificationEvent.getAccountId())) {
			LOG.error("validateModificationEvent::CampaignModificationAuditServiceImpl - Received empty value for account id!");
			return false;
		}
		
		if (StringUtils.isBlank(modificationEvent.getModificationActionType())) {
			LOG.error("validateModificationEvent::CampaignModificationAuditServiceImpl - Received blank value for modification action type!");
			return false;
		}
		
		return true;
	}
	
	
	/**
	 * 
	 * BIRD-74509 | Prepare and publish change log event for templates
	 *
	 * @param isNewOrGlobalTemplate,resellerId,enterpriseId,oldNewEntitiesDtoMap,userId, eventSubType
	 * 
	 */
	@Override
	@Async
	public void validateAndSendChangeLogEventForTemplates(Boolean isNewOrGlobalTemplate, Integer resellerId, Integer enterpriseId, Integer templateId,
			Map<String, AllTemplateDataDto> oldNewEntitiesDtoMap, Integer userId, String eventSubType) {
		if (resellerId == null) {
			prepareAndPublishModificationEvent(CampaignModificationAuditUtils.fetchAndPrepareMapWithPrefixKeyForTemplates(oldNewEntitiesDtoMap, "old"),
					CampaignModificationAuditUtils.fetchAndPrepareMapWithPrefixKeyForTemplates(oldNewEntitiesDtoMap, "new"), userId,
					BooleanUtils.isTrue(isNewOrGlobalTemplate) ? CampaignModificationUserActionEnum.CREATE.getUserActionType() : CampaignModificationUserActionEnum.EDIT.getUserActionType(),
					Instant.now().toEpochMilli(), CampaignModificationAuditUtils.TEMPLATE_ENTITY, enterpriseId, templateId, eventSubType);
		}
	}

	/*
	 * Method to get change logs of campaigns/templates.
	 * Sample Response:- Most recent modification is displayed at top.
	 * {
	 * "totalCount": 2,
	 * "auditLogs": [
	 * {
	 * "actionType": "completed",
	 * "actionDate": "Jan 03, 2025",
	 * "actionTime": "11:19 AM",
	 * "userName": "Birdeye",
	 * "userId": null,
	 * "dataChanges": [] //Only populated in case the actionType is edited.
	 * },
	 * {
	 * "actionType": "created",
	 * "actionDate": "Jan 03, 2025",
	 * "actionTime": "11:19 AM",
	 * "userName": "Uat Birdeye",
	 * "userId": 1879296,
	 * "dataChanges": [] //Only populated in case the actionType is edited.
	 * }
	 * ]
	 * }
	 */
	@Override
	@Profiled
	public ChangeLogsResponse getChangeLogs(Integer accountId, ChangeLogsRequest request) {
		validateChangeLogsRequest(request);
		String businessTimeZoneId = businessService.getBusinessTimezoneId(accountId);
		ChangeLogsResponse response = null;
		switch (request.getEntityType()) {
			case CampaignModificationAuditUtils.CAMPAIGN_ENTITY:
				response = getChangeLogsInformationForCampaigns(accountId, request, businessTimeZoneId);
				break;
			
			case CampaignModificationAuditUtils.TEMPLATE_ENTITY:
				response = getChangeLogsInformationForTemplates(accountId, request, businessTimeZoneId);
				break;
		}
		return response;
	}

	/*
	 * Method to validate the change logs request.
	 * @throw exception in case of invalid request.
	 */
	private void validateChangeLogsRequest(ChangeLogsRequest request) {
		if (Objects.isNull(request.getEntityId())) {
			LOG.warn("validateChangeLogsRequest::CampaignModificationAuditServiceImpl - Received null value for entity id!");
			throw new CampaignBadRequestException(ErrorCodes.INVALID_REQUEST, "entityId can't be null");
		}
		
		if (BooleanUtils.isFalse(
				StringUtils.equalsAnyIgnoreCase(request.getEntityType(), CampaignModificationAuditUtils.CAMPAIGN_ENTITY, CampaignModificationAuditUtils.TEMPLATE_ENTITY))) {
			LOG.warn("validateChangeLogsRequest::CampaignModificationAuditServiceImpl - Received invalid entity type!");
			throw new CampaignBadRequestException(ErrorCodes.INVALID_REQUEST, "Invalid entity type");
		}
		
		if (BooleanUtils.isTrue(StringUtils.equalsIgnoreCase(request.getEntityType(), CampaignModificationAuditUtils.TEMPLATE_ENTITY))
				&& BooleanUtils.isFalse(StringUtils.equalsAnyIgnoreCase(request.getCategory(), Constants.SMS_TYPE, Constants.EMAIL_TYPE))) {
			LOG.warn("validateChangeLogsRequest::CampaignModificationAuditServiceImpl - Received invalid category for entityType 'template'");
			throw new CampaignBadRequestException(ErrorCodes.INVALID_REQUEST, "Invalid category");
		}
	}
	
	/**
	 * Method to fetch change logs information related to campaigns.
	 */
	private ChangeLogsResponse getChangeLogsInformationForCampaigns(Integer accountId, ChangeLogsRequest request, String businessTimeZoneId) {
		List<CampaignEntitiesChangeLog> changeLogs;
		Integer entityId = request.getEntityId();
		String category = StringUtils.EMPTY;
		if (CoreUtils.getBooleanValueFromInteger(request.getIsSplitCampaign())) {
			CachedCollectionWrapper<SplitCampaignMappingDTO> mappingWrapper = cacheService.getSplitCampaignMappingListBySplitCampaignId(entityId, accountId);
			if(mappingWrapper != null) {
				entityId = mappingWrapper.getElementsList().get(0).getCampaignId();
			} else {
				LOG.warn("getChangeLogsInformationForCampaigns:: Invalid split campaign id {}", entityId);
				throw new CampaignBadRequestException(ErrorCodes.INVALID_REQUEST, "Invalid split campaign id!");
			}
			
			category = Constants.SPLIT;
		}
		Campaign campaign = campaignReadOnlyRepo.findFirstByIdAndIsDeleted(entityId, 0);
		if(Objects.isNull(campaign)) {
			LOG.warn("getChangeLogsInformationForCampaigns - Received request to fetch audit logs of deleted campaign");
			throw new CampaignBadRequestException(ErrorCodes.INVALID_REQUEST, "Can't fetch audit logs for deleted entity!");
		}
		
		LOG.info("getChangeLogsInformationForCampaigns - Preparing modification log response for campaignId: {}, category: {} & businessTimeZone: {}.", entityId, category,
				businessTimeZoneId);
		changeLogs = campaignEntitiesChangeLogDao.findChangeLogsByIdAndType(accountId, entityId, request.getEntityType(), category);
		changeLogs = CollectionUtils.isEmpty(changeLogs) ? Collections.emptyList() : changeLogs;
		CampaignEntitiesChangeLog campaignCreationLog = filterAndFetchCreationLogForAnEntity(changeLogs, campaign);
		return prepareChangeLogsResponse(campaignCreationLog, changeLogs, campaign, businessTimeZoneId);
	}
	
	/*
	 * Filter out 'deletion' & 'unmark default' modification event.
	 * And fetch/prepare creation event - (i). If creation event already exists in 'changeLogs' list, retrieve that event.
	 * (ii). Else prepare the creation event using entity's 'createdAt' field.
	 */
	private CampaignEntitiesChangeLog filterAndFetchCreationLogForAnEntity(List<CampaignEntitiesChangeLog> changeLogs, Object object) {
		CampaignEntitiesChangeLog creationChangeLog = null;
		List<CampaignEntitiesChangeLog> elementsToRemoveList = new ArrayList<>();
		for (CampaignEntitiesChangeLog changeLog : changeLogs) {
			if (StringUtils.equalsIgnoreCase(changeLog.getEvent(), CampaignModificationUserActionEnum.CREATE.getUserActionType())) {
				creationChangeLog = changeLog;
				elementsToRemoveList.add(changeLog);
			}
			
			// Filter out deletion & unmark default event(Not to be displayed at dashboard)
			if (StringUtils.equalsAnyIgnoreCase(changeLog.getEvent(), CampaignModificationUserActionEnum.DELETE.getUserActionType(),
					CampaignModificationUserActionEnum.UNMARK_DEFAULT.getUserActionType())) {
				elementsToRemoveList.add(changeLog);
			}
		}
		
		changeLogs.removeAll(elementsToRemoveList);
		
		if (Objects.isNull(creationChangeLog) && object instanceof Campaign) {
			Campaign campaign = (Campaign) object;
			creationChangeLog = new CampaignEntitiesChangeLog(campaign.getEnterpriseId(), campaign.getId(), campaign.getCreatedBy(),
					CampaignModificationUserActionEnum.CREATE.getUserActionType(), campaign.getCreatedAt());
		} else if (Objects.isNull(creationChangeLog) && object instanceof BusinessEmailTemplate) {
			BusinessEmailTemplate businessEmailTemplate = (BusinessEmailTemplate) object;
			creationChangeLog = new CampaignEntitiesChangeLog(businessEmailTemplate.getEnterpriseId(), businessEmailTemplate.getTemplateId().getId(),
					businessEmailTemplate.getCreatedById(), CampaignModificationUserActionEnum.CREATE.getUserActionType(), businessEmailTemplate.getCreatedAt());
		} else if (Objects.isNull(creationChangeLog) && object instanceof BusinessSmsTemplate) {
			BusinessSmsTemplate smsTemplate = (BusinessSmsTemplate) object;
			creationChangeLog = new CampaignEntitiesChangeLog(smsTemplate.getEnterpriseId(), smsTemplate.getId(), smsTemplate.getCreatedBy(),
					CampaignModificationUserActionEnum.CREATE.getUserActionType(), smsTemplate.getCreatedAt());
		}
		
		return creationChangeLog;
	}

	/*
	 * The 'changeLogs' list here, will not contain the entity's creation log. Entity creation log is independently received in the method parameters.
	 */
	private ChangeLogsResponse prepareChangeLogsResponse(CampaignEntitiesChangeLog creationLog, List<CampaignEntitiesChangeLog> changeLogs, Object object, String businessTimeZoneId) {
		ChangeLogsResponse response = new ChangeLogsResponse();
		List<AuditLogs> auditLogs = new ArrayList<>();
		//Add the most recent modifications first.
		for (CampaignEntitiesChangeLog changeLog : changeLogs) {
			if (BooleanUtils.isFalse(StringUtils.equalsIgnoreCase(changeLog.getEvent(), CampaignModificationUserActionEnum.CREATE.getUserActionType()))) {
				auditLogs.add(prepareAuditLogsFromCampaignEntitiesChangeLog(changeLog, object, businessTimeZoneId));
			}
		}
		//Add the entity creation log at last. This ensure the most recent modification is displayed at top.
		auditLogs.add(prepareAuditLogsFromCampaignEntitiesChangeLog(creationLog, object, businessTimeZoneId));
		
		response.setAuditLogs(auditLogs);
		response.setTotalCount(auditLogs.size());
		return response;
	}

	private AuditLogs prepareAuditLogsFromCampaignEntitiesChangeLog(CampaignEntitiesChangeLog creationLog, Object object, String businessTimeZoneId) {
		AuditLogs auditLogs = new AuditLogs();
		auditLogs.setActionType(creationLog.getEvent().toLowerCase());
		auditLogs.setActionDate(DateTimeUtils.prepareFormattedDate(creationLog.getEventTime(), CampaignModificationAuditUtils.MODIFICATION_DATE_FORMAT, businessTimeZoneId));
		auditLogs.setActionTime(DateTimeUtils.prepareFormattedDate(creationLog.getEventTime(), CampaignModificationAuditUtils.MODIFICATION_TIME_FORMAT, businessTimeZoneId));
		auditLogs.setUserId(creationLog.getUserId());
		auditLogs.setUserName(cacheService.getUserResponsibleForModification(creationLog.getUserId(), creationLog.getAccountId()));
		auditLogs.setDataChanges(prepareDataChangesForEditEvent(creationLog.getEvent(), creationLog.getModifiedFields(), object));
		return auditLogs;
	}

	/*
	 * 'modifiedFields' string here is a comma separated list of modified fields of entity. E.g: "priorityOrder,status".
	 */
	private List<String> prepareDataChangesForEditEvent(String eventType, String modifiedFields, Object object) {
		if (StringUtils.isBlank(modifiedFields) || !StringUtils.equalsIgnoreCase(eventType, CampaignModificationUserActionEnum.EDIT.getUserActionType())) {
			return Collections.emptyList();
		}
		if(object instanceof Campaign) {
			return prepareModifiedFieldsForCampaign(modifiedFields, ((Campaign) object).getCampaignType());
		} else if (object instanceof BusinessSmsTemplate ){
			return prepareModifiedFieldsForTemplate(modifiedFields, Constants.SMS_TYPE, ((BusinessSmsTemplate) object).getType());
		} else if (object instanceof BusinessEmailTemplate) {
			return prepareModifiedFieldsForTemplate(modifiedFields, Constants.EMAIL_TYPE, ((BusinessEmailTemplate) object).getTemplateId().getType());
		}
		return Collections.emptyList();
	}

	private List<String> prepareModifiedFieldsForTemplate(String modifiedFields, String category, String templateType) {
		if(StringUtils.equalsIgnoreCase(category, Constants.SMS_TYPE)) {
			return prepareModifiedFieldsForSmsTemplate(modifiedFields, templateType);
		} else {
			return prepareModifiedFieldsForEmailTemplate(modifiedFields, templateType);
		}
	}

	private List<String> prepareModifiedFieldsForEmailTemplate(String modifiedFields, String templateType) {
		List<String> response = new ArrayList<>();
		String[] modifiedFieldsList = modifiedFields.split(",");
		for(String modifiedField: modifiedFieldsList) {
			String fieldName = ModifiedFieldNameMapper.getMappedValue(StringUtils.join(Constants.EMAIL_TYPE, "_", CampaignModificationAuditUtils.TEMPLATE_ENTITY), modifiedField, templateType);
			if(StringUtils.isNotBlank(fieldName) && !response.contains(fieldName)) {
				response.add(fieldName);
			}
		}
		return response;
	}

	private List<String> prepareModifiedFieldsForSmsTemplate(String modifiedFields, String templateType) {
		List<String> response = new ArrayList<>();
		String[] modifiedFieldsList = modifiedFields.split(",");
		for(String modifiedField: modifiedFieldsList) {
			String fieldName = ModifiedFieldNameMapper.getMappedValue(StringUtils.join(Constants.SMS_TYPE, "_", CampaignModificationAuditUtils.TEMPLATE_ENTITY), modifiedField, templateType);
			if(StringUtils.isNotBlank(fieldName) && !response.contains(fieldName)) {
				response.add(fieldName);
			}
		}
		return response;
	}
	
	private List<String> prepareModifiedFieldsForCampaign(String modifiedFields, String campaignType) {
		List<String> response = new ArrayList<>();
		String[] modifiedFieldsList = modifiedFields.split(",");
		for (String modifiedField : modifiedFieldsList) {
			String fieldName = ModifiedFieldNameMapper.getMappedValue(CampaignModificationAuditUtils.CAMPAIGN_ENTITY, modifiedField, campaignType);
			if (StringUtils.isNotBlank(fieldName) && !response.contains(fieldName)) {
				response.add(fieldName);
			}
		}
		return response;
	}
	
	/**
	 * Method to fetch change logs information related to templates.
	 * 
	 */
	private ChangeLogsResponse getChangeLogsInformationForTemplates(Integer accountId, ChangeLogsRequest request, String businessTimeZoneId) {
		List<CampaignEntitiesChangeLog> changeLogs;
		Object object = null;
		if(StringUtils.equalsIgnoreCase(request.getCategory(), Constants.SMS_TYPE)) {
			if (BooleanUtils.isTrue(request.isGlobalTemplate())) {
				return prepareAuditLogsForGlobalTemplate(accountId, businessTimeZoneId);
			}
			object = smsTemplateRepo.findFirstByIdAndIsDeleted(request.getEntityId(), 0);
		} else {
			object = emailTemplateRepo.getTemplateByTemplateIdAndIsDeleted(request.getEntityId(), 0);
		}
	
		if(Objects.isNull(object)) {
			LOG.warn("getChangeLogsInformationForTemplates - Received request to fetch audit logs of deleted template.");
			throw new CampaignBadRequestException(ErrorCodes.INVALID_REQUEST, "Can't fetch audit logs for deleted entity!");
		}
		
		changeLogs = campaignEntitiesChangeLogDao.findChangeLogsByIdAndType(accountId, request.getEntityId(), request.getEntityType(), request.getCategory());
		changeLogs = CollectionUtils.isEmpty(changeLogs) ? Collections.emptyList() : changeLogs;
		CampaignEntitiesChangeLog campaignCreationLog = filterAndFetchCreationLogForAnEntity(changeLogs, object);
		return prepareChangeLogsResponse(campaignCreationLog, changeLogs, object, businessTimeZoneId);
	}
	
	/*
	 * Method to prepare audit logs for global templates.
	 * Prepare only creation logs.
	 */
	private ChangeLogsResponse prepareAuditLogsForGlobalTemplate(Integer accountId, String businessTimeZoneId) {
		ChangeLogsResponse response = new ChangeLogsResponse();
		AuditLogs creationLog = new AuditLogs();
		
		BusinessEnterpriseEntity business = cacheService.getBusinessById(accountId);
		creationLog.setActionType(CampaignModificationUserActionEnum.CREATE.getUserActionType());
		creationLog.setActionDate(DateTimeUtils.prepareFormattedDate(business.getCreated(), CampaignModificationAuditUtils.MODIFICATION_DATE_FORMAT, businessTimeZoneId));
		creationLog.setActionTime(DateTimeUtils.prepareFormattedDate(business.getCreated(), CampaignModificationAuditUtils.MODIFICATION_TIME_FORMAT, businessTimeZoneId));
		creationLog.setUserId(null);
		creationLog.setUserName(cacheService.getUserResponsibleForModification(null, accountId));
		creationLog.setDataChanges(Collections.emptyList());
		
		response.setAuditLogs(Arrays.asList(creationLog));
		response.setTotalCount(response.getAuditLogs().size());
		return response;
	}
	
}
