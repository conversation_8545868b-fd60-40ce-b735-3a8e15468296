package com.birdeye.campaign.service.impl;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;

import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.entity.CampaignTemporaryAccountBlocklist;
import com.birdeye.campaign.repository.CampaignTemporaryAccountBlocklistRepo;
import com.birdeye.campaign.service.CampaignBlocklistingService;

@Service("CampaignBlocklistingService")
public class CampaignBlocklistingServiceImpl implements CampaignBlocklistingService {
	@Autowired
	private CampaignTemporaryAccountBlocklistRepo	campaignTemporaryAccountBlocklistRepo;
	private static final Logger						logger	= LoggerFactory.getLogger(CampaignBlocklistingServiceImpl.class);
	
	@Cacheable(value = "temporaryAccountBlockCache", unless = "#result == null", key = "#accountId")
	@Override
	public Long isTemporaryBlockedAccount(Integer accountId) {
		try {
		if(accountId == null) {
			return null;
		}
		CampaignTemporaryAccountBlocklist blockedAccount = campaignTemporaryAccountBlocklistRepo.findByAccountIdAndDeleted(accountId, 0);
//		If temporaryBlockDuration is changed, need to change cache name and sync the ttl values at both places.
		Long temporaryBlockDuration = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLongProperty("campaign.temporary.block.account.ms", ********l);
		if (blockedAccount == null || BooleanUtils.isFalse(isAccountInBlockWindow(blockedAccount.getBlockedTime().getTime(), temporaryBlockDuration))) {
			return 0l;
		}
		logger.info("account is temporary blocked in campaign service {}", blockedAccount.getAccountId());
		return 1l;
		}
		catch(Exception e){
			logger.error("exception while adding account to temporary block list {}: {}", accountId, ExceptionUtils.getStackTrace(e));
			return null;
		}
		
	}
	
	@Override
	@CacheEvict(key = "#accountId", value = "temporaryAccountBlockCache")
	public void evictTemporaryBlockCache(Integer accountId) {
		
	}
	
	@Override
	@Cacheable(value = "temporaryAccountBlockCache", unless = "#result == null", key = "#accountId")
	public Long addAccountToTemporaryBlockList(Integer accountId, String blockedReason) {
		try {
		CampaignTemporaryAccountBlocklist blockedAccount = campaignTemporaryAccountBlocklistRepo.findByAccountIdAndDeleted(accountId, 0);
		//If temporaryBlockDuration is changed, need to change cache name and sync the ttl values at both places.
		Long temporaryBlockDuration = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLongProperty("campaign.temporary.block.account.ms", ********l);
		if (blockedAccount != null && BooleanUtils.isTrue(isAccountInBlockWindow(blockedAccount.getBlockedTime().getTime(), temporaryBlockDuration))) {
			logger.info("Account is already present in blocked list {}", accountId);
			return 1l;
		} else if (blockedAccount != null && BooleanUtils.isFalse(isAccountInBlockWindow(blockedAccount.getBlockedTime().getTime(), temporaryBlockDuration))) {
			blockedAccount.setBlockedTime(new Date());
			blockedAccount.setBlockedReason(blockedReason);
			campaignTemporaryAccountBlocklistRepo.saveAndFlush(blockedAccount);
			return 1l;
		}
		CampaignTemporaryAccountBlocklist account = new CampaignTemporaryAccountBlocklist();
		account.setAccountId(accountId);
		account.setBlockedTime(new Date());
		account.setBlockedReason(blockedReason);
		campaignTemporaryAccountBlocklistRepo.saveAndFlush(account);
		return 1l;
		}catch(Exception e) {
			logger.error("Exception while getting account from temporary block list {}: {}", accountId, ExceptionUtils.getStackTrace(e));
			return 0l;
		}
	}
	
	private boolean isAccountInBlockWindow(long blockTime, double temporaryBlockDuration) {
		// ********
		blockTime = blockTime == Long.MIN_VALUE ? 0 : blockTime;
		long currentTime = ZonedDateTime.now(ZoneId.of("UTC")).toInstant().toEpochMilli();
		double diff = currentTime - blockTime;
		return diff <= temporaryBlockDuration;
	}

	@Override
	@CacheEvict(key = "#accountId", value = "temporaryAccountBlockCache")
	public void deleteAccountFromBlockList(Integer accountId) {
		logger.info("received request to delete temporary account from blocklist {}", accountId);
		CampaignTemporaryAccountBlocklist blockedAccount = campaignTemporaryAccountBlocklistRepo.findByAccountIdAndDeleted(accountId, 0);
		if (blockedAccount == null) {
			logger.error("account doesn't exist in blocklist {}", accountId);
			return;
		}
		campaignTemporaryAccountBlocklistRepo.delete(blockedAccount);
		logger.info("account delete from blocklist {}", accountId);
	}

}
