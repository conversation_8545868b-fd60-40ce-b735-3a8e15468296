/**
 * @file_name CommunicationEventServiceImpl.java
 * @created_date 16 Jul 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.communication.CommunicationTypeEnum;
import com.birdeye.campaign.communication.UsageCommunicationEventType;
import com.birdeye.campaign.communication.message.CommClick;
import com.birdeye.campaign.communication.message.CommOpen;
import com.birdeye.campaign.communication.message.UsageCommunicationMessage;
import com.birdeye.campaign.communication.message.UsageCommunicationWrapperMessage;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.KafkaTopicTypeEnum;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.entity.Promotion;
import com.birdeye.campaign.entity.ReviewRequest;
import com.birdeye.campaign.entity.ReviewRequestsLog;
import com.birdeye.campaign.enums.EmailTemplateTypes;
import com.birdeye.campaign.kafka.service.KafkaService;
import com.birdeye.campaign.platform.constant.CampaignTypeEnum;
import com.birdeye.campaign.platform.constant.CommunicationAcitivityEventEnum;
import com.birdeye.campaign.platform.constant.RequestStatusEnum;
import com.birdeye.campaign.repository.PromotionRepo;
import com.birdeye.campaign.repository.ReviewRequestLogRepo;
import com.birdeye.campaign.repository.ReviewRequestRepo;
import com.birdeye.campaign.request.CampaignCommunicationEventRequest;
import com.birdeye.campaign.request.EmailEventInfo;
import com.birdeye.campaign.request.TrackClicksRequest;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.CommunicationActivityService;
import com.birdeye.campaign.service.CommunicationEventService;
import com.birdeye.campaign.service.DataTrackingElasticService;
import com.birdeye.campaign.utils.BusinessUtils;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.ReferralWebhookUtils;
import com.birdeye.referral.request.ReferralWebhookDeliveryEvent;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import net.pieroxy.ua.detection.UserAgentDetectionResult;
import net.pieroxy.ua.detection.UserAgentDetector;

/**
 * @file_name CommunicationEventServiceImpl.java
 * @created_date 16 Jul 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@Service("communicationEventService")
public class CommunicationEventServiceImpl implements CommunicationEventService {
	
	private static final Logger		logger	= LoggerFactory.getLogger(CommunicationEventServiceImpl.class);
	
	@Autowired
	private ReviewRequestLogRepo	rrLogRepo;
	
	@Autowired
	private ReviewRequestRepo		reviewRequestRepo;
	
	@Autowired
	private PromotionRepo		promotionRepo;
	
	@Autowired
	private KafkaService			kafkaService;
	
	@Autowired
	private CacheService				cacheService;
	
	@Autowired
	private DataTrackingElasticService dataTrackingService;
	
	@Autowired
	@Qualifier("campaignObjectMapper")
	private ObjectMapper			campaignObjectMappper;
	
	@Autowired
	private CommunicationActivityService communicationActivityService;
	
	/**
	 * 
	 * Writes RR Log and Update ES doc
	 */
	@Override
	public void logCampaignCommunicationEvent(CampaignCommunicationEventRequest request) {
		logger.info("Received CampaignCommunicationEventRequest {}", request);
		if (request == null) {
			return;
		}
		UsageCommunicationWrapperMessage wrapper = new UsageCommunicationWrapperMessage();
		if ("review_request".equals(request.getRequestType()) || "survey_request".equals(request.getRequestType())) {
			wrapper.setRequestType(CommunicationTypeEnum.REVIEW_REQUEST.getType());
			wrapper.setCommunicationMessage(getUsageCommunicationMessageForRRCXSurvey(request));
		} else if ("customer_experience".equals(request.getRequestType())) {
			wrapper.setRequestType("cx_request");
			wrapper.setCommunicationMessage(getUsageCommunicationMessageForRRCXSurvey(request));
		} else if ("referral".equals(request.getRequestType())) {
			wrapper.setRequestType(request.getRequestType());
			wrapper.setCommunicationMessage(getUsageCommunicationMessageForReferral(request));
		} else if (StringUtils.equalsAnyIgnoreCase(request.getRequestType(), CampaignTypeEnum.APPOINTMENT_REMINDER.getType(), CampaignTypeEnum.APPOINTMENT_RECALL.getType(),
				CampaignTypeEnum.APPOINTMENT_FORM.getType())) {
			wrapper.setRequestType(request.getRequestType());
			wrapper.setCommunicationMessage(getUsageCommunicationMessageForAppointment(request));
		}
		
		logger.info("Pushing click event to kafka topic {} :: {}", KafkaTopicTypeEnum.USAGE_COMM_INSERT_DATA.getType(), wrapper);
		boolean messageSent = kafkaService.pushMessageToKafkaAcknowledged(KafkaTopicTypeEnum.USAGE_COMM_INSERT_DATA, null, wrapper);
		if (BooleanUtils.isFalse(messageSent)) {
			logger.info("Referral Communication Message send to kafka failed for input {} and request {}", request, wrapper);
		}
	}
	
	/**
	 * @param request
	 * @return
	 */
	private UsageCommunicationMessage getUsageCommunicationMessageForReferral(CampaignCommunicationEventRequest request) {
		ReviewRequestsLog log = saveAndGetReviewRequestsLog(generateReviewRequestsLog(request));
		//fetching child RR to publish communication activity
		ReviewRequest reviewRequest = getReviewRequestById(request.getRequestId());
		if (reviewRequest == null) {
			return null;
		}
		ReviewRequest parentReviewRequest = reviewRequest;
		if (reviewRequest.getParentRequestId() != null) {
			parentReviewRequest = getReviewRequestById(reviewRequest.getParentRequestId());
		}
		UsageCommunicationMessage message = new UsageCommunicationMessage();
		message.setReviewRequestLogId(log.getId());
		message.setReviewRequestId(parentReviewRequest.getId());
		message.setReminderCount(parentReviewRequest.getReminderCount());
		if (log.getClickType() != null) {
			if (log.getClickType() == 1) {
				// OPEN for all channels
				message.setCommunicationEvent(UsageCommunicationEventType.OPEN.getQueueType());
				CommOpen commOpen = new CommOpen(log.getClickedOn().equalsIgnoreCase("web") ? 1 : 0, log.getClickedOn().equalsIgnoreCase("mobile") ? 1 : 0,
						log.getClickedOn().equalsIgnoreCase("tablet") ? 1 : 0);
				message.setMessage(getStringFromJson(commOpen));
				communicationActivityService.publishCommunicationAcitivity(CommunicationAcitivityEventEnum.OPENED, reviewRequest, log, null);
			} else if (log.getClickType() == 2) {
				// CLICK for all channels
				message.setCommunicationEvent(UsageCommunicationEventType.SOURCE_CLICK.getQueueType());
				CommClick commClick = new CommClick(request.getSourceId(), request.getSource(), getCurrentDateFormatted());
				message.setMessage(getStringFromJson(commClick));
				communicationActivityService.publishCommunicationAcitivity(CommunicationAcitivityEventEnum.CLICKED, reviewRequest, log, null);
			}
		}
		pushReferralClickEventToMessengerActivity(log, parentReviewRequest);
		return message;
	}

	/**
	 * @param request
	 * @param log
	 * @param rr
	 */
	private void pushReferralClickEventToMessengerActivity(ReviewRequestsLog log, ReviewRequest rr) {
		dataTrackingService.pushClickDataToCustomerActivityIndexReferral(log, rr, cacheService.getBusinessById(rr.getBusinessId()));
	}
	
	private String getStringFromJson(Object object) {
		String val = null;
		
		try {
			val = campaignObjectMappper.writeValueAsString(object);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		return val;
	}
	
	/**
	 * @return
	 */
	private String getCurrentDateFormatted() {
		try {
			return new SimpleDateFormat("MM/dd/yyyy HH:mm:ss").format(new Date());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "01/02/1970 00:00:00";
	}
	
	/**
	 * @param log
	 * @return
	 */
	private ReviewRequestsLog saveAndGetReviewRequestsLog(ReviewRequestsLog log) {
		return rrLogRepo.saveAndFlush(log);
	}
	
	private ReviewRequest getReviewRequestById(Long id) {
		return reviewRequestRepo.getById(id);
	}
	
	private ReviewRequestsLog generateReviewRequestsLog(CampaignCommunicationEventRequest request) {
		ReviewRequest rr = getReviewRequestById(request.getRequestId());
		ReviewRequestsLog log = new ReviewRequestsLog();
		log.setReviewRequestId(rr.getId());
		log.setBusinessId(rr.getBusinessId());
		log.setOs(request.getOs());
		log.setEventId(request.getEventId());
		log.setClickedAt(new Date());
		log.setSourceId(request.getSourceId());
		log.setClickedOn(getRRLogClickedOnSource(request.getSource()));
		log.setBrowser(request.getBrowser());
		log.setUserAgent(request.getUserAgent());
		if (request.getRequestType().equalsIgnoreCase("referral")) {
			log.setClickType(request.getClickType());
			log.setEvent(getEventTypeForReferral(request.getClickType()));
		} else if (request.getRequestType().equalsIgnoreCase(CampaignTypeEnum.APPOINTMENT_REMINDER.getType())) {
			log.setClickType(request.getClickType());
			log.setEvent(getEventTypeForAppointment(request.getClickType()));
		} else if (request.getRequestType().equalsIgnoreCase(CampaignTypeEnum.APPOINTMENT_RECALL.getType())) {
			log.setClickType(request.getClickType());
			log.setEvent(getEventTypeForRecall(request.getClickType()));
		} else {
			log.setEvent(request.getEvent());
		}
		
		return log;
	}
	
	private static String getRRLogClickedOnSource(String requestSource) {
		if(StringUtils.isBlank(requestSource)) {
			return "web";
		}
		return requestSource;
	}
	
	private String getEventTypeForReferral(Integer clickType) {
		if (clickType == null || clickType.intValue() == 1) {
			return "open";
		}
		if (clickType.intValue() == 2) {
			return "click";
		}
		return null;
	}
	
	private String getEventTypeForAppointment(Integer clickType) {
		if (clickType.intValue() == 1) {
			return "open";
		}
		if (clickType.intValue() == 13) {
			return "confirm_click";
		}
		if (clickType.intValue() == 14) {
			return "cancel_click";
		}
		if (clickType.intValue() == 15) {
			return "reschedule_click";
		}
		return null;
	}
	
	private String getEventTypeForRecall(Integer clickType) {
		if (clickType.intValue() == 1) {
			return "open";
		}
		if (clickType.intValue() == 2) {
			return "click";
		}
		return null;
	}
	
	private String getEventTypeForForm(Integer clickType) {
		if (clickType.intValue() == 1) {
			return "open";
		}
		if (clickType.intValue() == 2) {
			return "click";
		}
		if (clickType.intValue() == 17) {
			return "click";
		}
		return null;
	}
	
	/**
	 * Handler for email open events
	 * 
	 */
	@Override
	public void handlerForCampaignEmailOpenEvents(EmailEventInfo eventInfo) {
		logger.info("Received sendgrid open event {}", eventInfo);
		if (eventInfo == null) {
			return;
		}
		try {
			Long reqId = Long.parseLong(eventInfo.getRequestId());
			if ("review_request".equals(eventInfo.getRequestType()) || "customer_experience".equals(eventInfo.getRequestType()) || "survey_request".equals(eventInfo.getRequestType())
					|| "referral".equals(eventInfo.getRequestType()) || "appointment_reminder".equals(eventInfo.getRequestType()) || "appointment_recall".equals(eventInfo.getRequestType())
					|| "appointment_form".equals(eventInfo.getRequestType())) {
				logCampaignEmailClicks(prepareOpenEventUpdateRequestForRRCXSurveyReferral(eventInfo, reqId));
			} else if ("promotion".equals(eventInfo.getRequestType())) {
				BusinessEnterpriseEntity business = cacheService.getBusinessById(Integer.parseInt(eventInfo.getBusinessId()));
				if (business == null) {
					return;
				}
				logCampaignEmailClicksPromotion(prepareOpenEventUpdateRequestForPromotion(eventInfo, reqId, business));
			}
			else {
				logger.info("Invalid requestType : {} data not found.", eventInfo.getRequestType());
			}
		} catch (NumberFormatException e) {
			logger.info("Invalid requestId : {} ", eventInfo.getRequestId());
		}
	}

	/**
	 * @param eventInfo
	 * @param reqId
	 * @return
	 */
	private TrackClicksRequest prepareOpenEventUpdateRequestForPromotion(EmailEventInfo eventInfo, Long reqId, BusinessEnterpriseEntity business) {
		TrackClicksRequest request = new TrackClicksRequest();
		Promotion reviewRequest = promotionRepo.findFirstById(reqId); // to check valid id
		request.setRequestId(reviewRequest.getId());
		request.setRequestType(eventInfo.getRequestType());
		UserAgentDetectionResult userAgentDetail = getUserAgentMeta(eventInfo);
		if (userAgentDetail != null) {
			request.setOs(userAgentDetail.getOperatingSystem().getDescription());
			request.setSource(parseDeviceDetail(userAgentDetail.getDevice().getDeviceType().toString()));
		} else {
			//fallback - if ua not present, treat it as web
			request.setSource("web");
		}
		request.setClickType(1);
		request.setBusinessNumber(business.getBusinessId());
		return request;
	}

	/**
	 * @param eventInfo
	 * @return
	 */
	private UserAgentDetectionResult getUserAgentMeta(EmailEventInfo eventInfo) {
		UserAgentDetectionResult userAgentDetail = null;
		if (StringUtils.isNotBlank(eventInfo.getUserAgent())) {
			userAgentDetail = new UserAgentDetector().parseUserAgent(eventInfo.getUserAgent());
		}
		return userAgentDetail;
	}
	

	/**
	 * Handler for SendGrid Campaign Failure Events
	 * 1. Updates review_request / promotion_request
	 * 2. Push failure update rr request - to update failure in specific index
	 * 
	 */
	@Override
	public void handlerForCampaignEmailFailureEvents(EmailEventInfo request) {
		if (BooleanUtils.isFalse(isValidEmailFailureEvent(request))) {
			return;
		}
		
		if (StringUtils.equalsAnyIgnoreCase(request.getRequestType(), CampaignTypeEnum.REVIEW_REQUEST.getTemplateType(), CampaignTypeEnum.CX_REQUEST.getTemplateType(),
				CampaignTypeEnum.SURVEY_REQUEST.getTemplateType(), CampaignTypeEnum.REFERRAL.getTemplateType(), CampaignTypeEnum.APPOINTMENT_REMINDER.getTemplateType(),
				CampaignTypeEnum.APPOINTMENT_RECALL.getTemplateType(), CampaignTypeEnum.APPOINTMENT_FORM.getTemplateType())) {
			Long reqId = Long.parseLong(request.getRequestId());
			ReviewRequest rr = reviewRequestRepo.findFirstById(reqId);
			if (rr == null) {
				logger.warn("Error in executing handlerForCampaignEmailFailureEvents - review request not found for request id :: {}", reqId);
				return;
			}
			rr = handleEmailFailureForReviewRequest(rr, request);
			communicationActivityService.publishCommunicationAcitivity(CommunicationAcitivityEventEnum.FAILURE, rr, null, null);
			updateDeliveryStatusForRR(rr);
			publishFailureEventForReferralWebhooks(rr, request);
		} else if (StringUtils.equalsAnyIgnoreCase(request.getRequestType(), "promotion")) {
			Long reqId = Long.parseLong(request.getRequestId());
			Promotion rr = promotionRepo.findFirstById(reqId);
			if (rr == null) {
				logger.warn("Error in executing handlerForCampaignEmailFailureEvents - review request not found for request id :: {}", reqId);
				return;
			}
			rr = handleEmailFailureForPromotionRequest(rr, request);
			communicationActivityService.publishPromotionCommunicationAcitivity(CommunicationAcitivityEventEnum.FAILURE, rr, null);
			updateDeliveryStatusForPromotion(rr);
		}
	}

	/*
	 * publish request failure event to topic: 'referral-requests-delivery-webhooks'
	 */
	private void publishFailureEventForReferralWebhooks(ReviewRequest rr, EmailEventInfo request) {
		if (!Objects.isNull(rr) && BooleanUtils.isTrue(
				ReferralWebhookUtils.isValidRequestForReferralWebhook(BusinessUtils.getEnterpriseId(cacheService.getBusinessById(rr.getBusinessId())), request.getRequestType()))) {
			ReferralWebhookDeliveryEvent referralWebhookDeliveryEvent = ReferralWebhookUtils.prepareReferralWebhookDeliveryEvent(Constants.FAILURE_STATUS, rr,
					request.getTimestamp());
			
			kafkaService.pushMessageToKafkaAcknowledged(KafkaTopicTypeEnum.REFERRAL_REQUESTS_DELIVERY_WEBHOOKS.getType(), null, referralWebhookDeliveryEvent);
		}
	}

	private boolean isValidEmailFailureEvent(EmailEventInfo request) {
		if (BooleanUtils.isFalse(CoreUtils.isEmailFailureEvent(request.getEvent())) && BooleanUtils.isFalse(CoreUtils.isEmailConfigErrorEvent(request.getEvent()))) {
			logger.warn("Invalid email failure event :: {}", request.getEvent());
			return false;
		}
		
		if (request.getRequestId() == null || StringUtils.isBlank(request.getRequestType())) {
			logger.warn("Invalid request id :: {} or type :: {}", request.getRequestId(), request.getRequestType());
			return false;
		}
		
		if (StringUtils.equalsIgnoreCase(request.getEvent(), Constants.GROUP_UNSUBSCRIBE)
				&& BooleanUtils.isFalse(StringUtils.equalsIgnoreCase(request.getSentEmailCategory(), request.getEventEmailCategory()))) {
			logger.warn("Sent and Event email category mismatch");
			return false;
		}
		
		return true;
		
	}
	
	public void updateDeliveryStatusForRR(ReviewRequest reviewRequest) {
		String requestType = "review_request";
		if (reviewRequest.getRequestType() != null
				&& (reviewRequest.getRequestType().equalsIgnoreCase(EmailTemplateTypes.CUSTOMER_EXPERIENCE.name()) || reviewRequest.getRequestType().equalsIgnoreCase("cx_request"))) {
			requestType = "cx_request";
		}
		if (reviewRequest.getRequestType() != null
				&& (reviewRequest.getRequestType().equalsIgnoreCase(EmailTemplateTypes.REFERRAL.name()) || reviewRequest.getRequestType().equalsIgnoreCase("referral"))) {
			requestType = "referral";
		}
		if (reviewRequest.getRequestType() != null
				&& (reviewRequest.getRequestType().equalsIgnoreCase(EmailTemplateTypes.APPOINTMENT_REMINDER.name()) || reviewRequest.getRequestType().equalsIgnoreCase("appointment_reminder"))) {
			requestType = "appointment_reminder";
		}
		if (reviewRequest.getRequestType() != null
				&& (reviewRequest.getRequestType().equalsIgnoreCase(EmailTemplateTypes.APPOINTMENT_RECALL.name()) || reviewRequest.getRequestType().equalsIgnoreCase("appointment_recall"))) {
			requestType = "appointment_recall";
		}
		if (reviewRequest.getRequestType() != null
				&& (reviewRequest.getRequestType().equalsIgnoreCase(EmailTemplateTypes.APPOINTMENT_FORM.name()) || reviewRequest.getRequestType().equalsIgnoreCase("appointment_form"))) {
			requestType = "appointment_form";
		}
		
		UsageCommunicationWrapperMessage communicationWrapperMessage = new UsageCommunicationWrapperMessage(requestType);
		UsageCommunicationMessage communicationMessage = new UsageCommunicationMessage();
		communicationWrapperMessage.setCommunicationMessage(communicationMessage);
		communicationMessage.setReviewRequestId(reviewRequest.getId());
		communicationMessage.setCommunicationEvent(UsageCommunicationEventType.UPDATE_STATUS.getQueueType());
		try {
			publishEmailESUpdateEvent(communicationWrapperMessage);
		} catch (Exception exp) {
			logger.error("Error while updating ES index for review request {} :: {}", communicationWrapperMessage, ExceptionUtils.getStackTrace(exp));
		}
	}
	
	// UPDATE_DELIVERY_STATUS
	public void updateDeliveryStatusForPromotion(Promotion promotion) {
		UsageCommunicationWrapperMessage communicationWrapperMessage = new UsageCommunicationWrapperMessage("promotion");
		UsageCommunicationMessage communicationMessage = new UsageCommunicationMessage();
		communicationMessage.setReviewRequestId(promotion.getId());
		communicationMessage.setCommunicationEvent(UsageCommunicationEventType.UPDATE_STATUS.getQueueType());
		communicationWrapperMessage.setCommunicationMessage(communicationMessage);
		try {
			publishEmailESUpdateEvent(communicationWrapperMessage);
		} catch (Exception exp) {
			logger.error("Error while updating ES index for promotion request {} :: {}", communicationWrapperMessage, ExceptionUtils.getStackTrace(exp));
		}
	}
	
	private void publishEmailESUpdateEvent(UsageCommunicationWrapperMessage request) {
		kafkaService.pushMessageToKafkaAcknowledged(KafkaTopicTypeEnum.USAGE_COMM_INSERT_DATA, String.valueOf(request.getCommunicationMessage().getReviewRequestId()), request);
	}

	
	/**
	 * Update ReviewRequest with failure status
	 * 
	 * @param reviewRequest
	 * @param eventInfoMessage
	 */
	private ReviewRequest handleEmailFailureForReviewRequest(ReviewRequest reviewRequest, EmailEventInfo eventInfoMessage) {
		reviewRequest.setDeliveryStatus(RequestStatusEnum.FAILURE.getName());
		reviewRequest.setFailureReason(StringUtils.equalsIgnoreCase(eventInfoMessage.getEvent(), "group_unsubscribe")
				? StringUtils.join("Contact unsubscribed from ", StringUtils.lowerCase(eventInfoMessage.getEventEmailCategory()), " emails")
				: eventInfoMessage.getEvent());
		reviewRequest = reviewRequestRepo.saveAndFlush(reviewRequest);
		return reviewRequest;
	}
	
	/**
	 * Update Promotion with failure status
	 * 
	 * @param reviewRequest
	 * @param eventInfoMessage
	 */
	private Promotion handleEmailFailureForPromotionRequest(Promotion reviewRequest, EmailEventInfo eventInfoMessage) {
		reviewRequest.setDeliveryStatus(RequestStatusEnum.FAILURE.getName());
		reviewRequest.setFailureReason(StringUtils.equalsIgnoreCase(eventInfoMessage.getEvent(), "group_unsubscribe")
				? StringUtils.join("Contact unsubscribed from ", StringUtils.lowerCase(eventInfoMessage.getEventEmailCategory()), " emails")
				: eventInfoMessage.getEvent());
		reviewRequest = promotionRepo.saveAndFlush(reviewRequest);
		return reviewRequest;
	}
	/**
	 * 
	 *  [event=open, timestamp=1595445532, requestId=***********, userAgent=null, requestType=review_request, recipient=<EMAIL>, businessId=618165, ip=null, url=null]
	 * @param emailEventInfo
	 * @param reqId
	 * @return
	 */
	private CampaignCommunicationEventRequest prepareOpenEventUpdateRequestForRRCXSurveyReferral(EmailEventInfo emailEventInfo, Long reqId) {
		CampaignCommunicationEventRequest campaignCommunicationEventRequest = new CampaignCommunicationEventRequest();
		campaignCommunicationEventRequest.setRequestType(emailEventInfo.getRequestType());
		campaignCommunicationEventRequest.setEvent(emailEventInfo.getEvent());
		campaignCommunicationEventRequest.setUserAgent(emailEventInfo.getUserAgent());
		campaignCommunicationEventRequest.setClickType(StringUtils.equalsAnyIgnoreCase(emailEventInfo.getRequestType(), "referral", "appointment_reminder", "appointment_recall","appointment_form") ? 1 : null);
		campaignCommunicationEventRequest.setRequestId(reqId);
		UserAgentDetectionResult userAgentDetail = getUserAgentMeta(emailEventInfo);
		if (userAgentDetail != null) {
			campaignCommunicationEventRequest.setOs(userAgentDetail.getOperatingSystem().getDescription());
			campaignCommunicationEventRequest.setSource(parseDeviceDetail(userAgentDetail.getDevice().getDeviceType().toString()));
		} else {
			//fallback - if ua not present, treat it as web
			campaignCommunicationEventRequest.setSource("web");
		}
		return campaignCommunicationEventRequest;
	}
	
	private static String parseDeviceDetail(String name) {
		if ("COMPUTER".equalsIgnoreCase(name)) {
			return "web";
		} else if ("PHONE".equalsIgnoreCase(name) || "SDK".equalsIgnoreCase(name) || "UNKNOWN_MOBILE".equalsIgnoreCase(name) || "UNKNOWN".equalsIgnoreCase(name)) {
			return "mobile";
		} else if ("TABLET".equalsIgnoreCase(name)) {
			return "tablet";
		} else {
			return "web";
		}
		
	}
	
	/**
	 * Email Open event handler for
	 * 1. RR
	 * 2. CX
	 * 3. Survey
	 * 4. Referral 
	 * 
	 * @param request
	 */
	public void logCampaignEmailClicks(CampaignCommunicationEventRequest request) {
		try {
			logger.info("Pushing click event to kafka topic {} :: {}", KafkaTopicTypeEnum.CAMPAIGN_RRLOG_EVENT.getType(), request);
			boolean messageSent = kafkaService.pushMessageToKafkaAcknowledged(KafkaTopicTypeEnum.CAMPAIGN_RRLOG_EVENT, null, request);
			if (BooleanUtils.isFalse(messageSent)) {
				logger.info(" Communication Message send to kafka failed for input {} and topic {}", request, KafkaTopicTypeEnum.CAMPAIGN_RRLOG_EVENT);
				logger.info("Trigger fallback for  Communication Message {} ", request);
				logCampaignCommunicationEvent(request);
			}
		} catch (Exception e) {
			logger.info("Error while sending click event for campaign {}", request);
		}
	}
	
	/**
	 * Email Open event handler for
	 * 1. Promotion
	 * 
	 * @param request
	 */
	public void logCampaignEmailClicksPromotion(TrackClicksRequest request) {
		try {
			logger.info("Pushing click event to kafka topic {} :: {}", KafkaTopicTypeEnum.CAMPAIGN_CLICK_TRACKING.getType(), request);
			kafkaService.pushMessageToKafkaAcknowledged(KafkaTopicTypeEnum.CAMPAIGN_CLICK_TRACKING, null, request);
		} catch (Exception e) {
			logger.info("Error while sending click event for campaign {}", request);
		}
	}
	
	/**
	 * @param request
	 * @return
	 */
	private UsageCommunicationMessage getUsageCommunicationMessageForRRCXSurvey(CampaignCommunicationEventRequest request) {
		ReviewRequestsLog log = saveAndGetReviewRequestsLog(generateReviewRequestsLog(request));
		//fetching child review request to publish communication activity
		ReviewRequest reviewRequest = getReviewRequestById(request.getRequestId());
		if (reviewRequest == null) {
			return null;
		}
		ReviewRequest parentReviewRequest = reviewRequest;
		if (reviewRequest.getParentRequestId() != null) {
			parentReviewRequest = getReviewRequestById(reviewRequest.getParentRequestId());
		}
		
		UsageCommunicationMessage communicationMessage = new UsageCommunicationMessage();
		communicationMessage.setReviewRequestLogId(log.getId());
		communicationMessage.setCommunicationEvent(UsageCommunicationEventType.OPEN.getQueueType());
		communicationMessage.setReviewRequestId(parentReviewRequest.getId());
		communicationMessage.setReminderCount(parentReviewRequest.getReminderCount());
		
		//IMMEDIATE HACK to handle survey opens in email - because in further consumer flow, survey request goes into review request flow otherwise, and es update fails
		//TODO: Refactor complete usage comm flows
		if (BooleanUtils.isFalse(isSurveyOpen(request))) {
			CommOpen commOpen = new CommOpen(log.getClickedOn().equalsIgnoreCase("web") ? 1 : 0, log.getClickedOn().equalsIgnoreCase("mobile") ? 1 : 0,
					log.getClickedOn().equalsIgnoreCase("tablet") ? 1 : 0);
			communicationMessage.setMessage(getStringFromJson(commOpen));
		}
		communicationActivityService.publishCommunicationAcitivity(CommunicationAcitivityEventEnum.OPENED, reviewRequest, log, null);
		return communicationMessage;
	}
	
	/**
	 * Determines if survey open
	 * 
	 * @param request
	 * @return
	 */
	private static boolean isSurveyOpen(CampaignCommunicationEventRequest request) {
		return StringUtils.equalsIgnoreCase(request.getRequestType(), "survey_request") && StringUtils.equalsIgnoreCase(request.getEvent(), "open");
	}
	
	private UsageCommunicationMessage getUsageCommunicationMessageForAppointment(CampaignCommunicationEventRequest request) {
		ReviewRequestsLog log = saveAndGetReviewRequestsLog(generateReviewRequestsLog(request));
		// fetching child RR to publish communication activity
		ReviewRequest reviewRequest = getReviewRequestById(request.getRequestId());
		if (reviewRequest == null) {
			return null;
		}
		
		UsageCommunicationMessage message = new UsageCommunicationMessage();
		message.setReviewRequestLogId(log.getId());
		message.setReviewRequestId(reviewRequest.getId());
		message.setReminderCount(reviewRequest.getReminderCount());
		if (log.getClickType() != null && log.getClickType() == 1) {
			// OPEN for all channels
			message.setCommunicationEvent(UsageCommunicationEventType.OPEN.getQueueType());
			CommOpen commOpen = new CommOpen(log.getClickedOn().equalsIgnoreCase("web") ? 1 : 0, log.getClickedOn().equalsIgnoreCase("mobile") ? 1 : 0,
					log.getClickedOn().equalsIgnoreCase("tablet") ? 1 : 0);
			message.setMessage(getStringFromJson(commOpen));
			communicationActivityService.publishCommunicationAcitivity(CommunicationAcitivityEventEnum.OPENED, reviewRequest, log, null);
		}
		
		return message;
	}
}
