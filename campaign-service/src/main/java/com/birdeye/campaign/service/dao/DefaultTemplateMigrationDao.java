package com.birdeye.campaign.service.dao;

import java.util.List;

import com.birdeye.campaign.entity.DefaultTemplateMigrationStats;

public interface DefaultTemplateMigrationDao {

	DefaultTemplateMigrationStats saveEntryInDb(DefaultTemplateMigrationStats audit);

	DefaultTemplateMigrationStats fetchEntryByAccountId(Integer accountId);

	List<Integer> fetchDistinctEntriesByAccountId();

	List<Integer> fetchEntriesByAccountId(List<Integer> accountIds);
	
}
