package com.birdeye.campaign.service.referral;

import com.birdeye.campaign.request.LocationLeadFormUrlRequest;
import com.birdeye.campaign.response.LocationLeadFormUrlResponse;

public interface ILocationLeadFormUrlService {
	
	public void setLocationLeadFormUrl(LocationLeadFormUrlRequest request);
	
	public LocationLeadFormUrlResponse getLocationLeadFormUrls(Integer enterpriseId);
	
	public void deleteLocationLeadFormUrl(Integer businessId);
	
	String getCustomLeadFormUrl(Integer businessId, String templateValue);
}
