package com.birdeye.campaign.service;

import com.birdeye.campaign.entity.PromotionRequestsLog;
import com.birdeye.campaign.entity.ReviewRequestsLog;
import com.birdeye.campaign.platform.constant.CommunicationAcitivityEventEnum;
import com.birdeye.campaign.platform.entity.BaseCommunicationEntity;

/**
 * @file_name CommunicationActivityService.java
 * @created_date 8 Sep 2020
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public interface CommunicationActivityService {
	
	/**
	 * Publish Customer communication activity
	 * by event - SENT,OPENED,CLICKED,FAILURE
	 * 1. CAMPAIGN data
	 * 2. Review Request data
	 * 3. Review RequestsLog data
	 * 
	 * @param eventTypeEnum
	 * @param reviewRequest
	 * @param rrLog
	 */
	void publishCommunicationAcitivity(CommunicationAcitivityEventEnum communicationActivityEventTypeEnum, BaseCommunicationEntity reviewRequest, ReviewRequestsLog rrLog, Integer enterpriseId);
	
	/**
	 * Publish Customer communication activity PROMOTION CAMPAIGN ONLY
	 * by event - SENT,OPENED,CLICKED,FAILURE
	 * 1. CAMPAIGN data
	 * 2. Promotion Request data
	 * 3. PromotionRequestsLog data
	 * 
	 * @param communicationActivityEventTypeEnum
	 * @param reviewRequest
	 * @param prLog
	 */
	void publishPromotionCommunicationAcitivity(CommunicationAcitivityEventEnum communicationActivityEventTypeEnum, BaseCommunicationEntity reviewRequest, PromotionRequestsLog prLog);
	
}
