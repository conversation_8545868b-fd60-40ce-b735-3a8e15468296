package com.birdeye.campaign.service;

import java.util.List;
import java.util.Map;

import com.birdeye.campaign.dto.BusinessTemplateEntity;
import com.birdeye.campaign.dto.BusinessTemplateMessage;
import com.birdeye.campaign.dto.CampaignCustomEntity;
import com.birdeye.campaign.dto.TemplateCustomFieldSRO;
import com.birdeye.campaign.entity.BusinessEmailTemplate;
import com.birdeye.campaign.entity.BusinessSmsTemplate;
import com.birdeye.campaign.request.ProductFeatureRequest;
import com.birdeye.template.sms.dto.SmsTemplateMessage;

/**
 * <AUTHOR>
 *
 */
public interface TemplateHelperService {
	
	/**
	 * Populates email templates associated with given enterprise id to all templates list.
	 * 
	 * @param templates
	 * @param emailTemplates
	 * @param enterpriseId
	 * @param request
	 * @param customCampaignMap 
	 */
	public void populateEmailTemplates(List<BusinessTemplateMessage> templates, List<BusinessTemplateEntity> emailTemplates, Integer enterpriseId,
			ProductFeatureRequest request, List<Integer> templateIdList, String listingCallType);
	
	/**
	 * Populates sms templates associated with given enterprise id to all templates list.
	 * 
	 * @param templates
	 * @param smsTemplates
	 * @param enterpriseId
	 * @param request
	 * @param customCampaignMap 
	 */
	public void populateSmsTemplates(List<BusinessTemplateMessage> templates, List<BusinessTemplateEntity> smsTemplates, Integer enterpriseId, ProductFeatureRequest request,
			Map<Integer, CampaignCustomEntity> customCampaignMap, Map<Integer, List<Integer>> templateToBusinessMapping, String listingEnumType);
	
	/**
	 * Soft delete sms template from business_sms_template.
	 * 
	 * @param businessSmsTemplates
	 */
	public void deleteSmsTemplate(List<BusinessSmsTemplate> businessSmsTemplates);
	
	/**
	 * Soft delete email template from business_email_template.
	 * 
	 * @param businessEmailTemplates
	 */
	public void deleteEmailTemplate(List<BusinessEmailTemplate> businessEmailTemplates);
	
	/**
	 * Get custom campaign map for email templates.
	 * 
	 * @param templateIdList
	 * @return
	 */
	public Map<Integer, CampaignCustomEntity> getCustomCampaignEntityMapEmail(List<Integer> templateIdList);
	
	/**
	 * Get custom campaign map for sms templates.
	 * 
	 * @param templateIdList
	 * @return
	 */
	public Map<Integer, CampaignCustomEntity> getCustomCampaignEntityMapSms(List<Integer> templateIdList);

	/**
	 * Save custom field association on template create/edit.
	 * 
	 * @param templateId
	 * @param templateType
	 * @param customFieldsList
	 * @param userId
	 * @param i 
	 */
	public void saveTemplateCustomFieldsAssociation(Integer templateId, String templateType, List<TemplateCustomFieldSRO> customFieldsList, Integer userId, boolean deleteCustomFields);

	public List<TemplateCustomFieldSRO> getTemplateCustomFieldsAssociation(Integer templateId, String templateType);

	public String getDistributionChartTypeForBusinessIds(List<Integer> businessIds, Integer campaignId);

	BusinessSmsTemplate getFreeTextSmsTemplateId();

	List<TemplateCustomFieldSRO> getAppointmentTemplateCustomFieldsAssociation(Integer templateId, String templateType);
	
	void saveAppointmentTemplateCustomFieldsAssociation(Integer templateId, String templateType, List<TemplateCustomFieldSRO> appointmentCustomFields, Integer userId, boolean deleteCustomFields);
	
	List<TemplateCustomFieldSRO> getTemplateLocationCustomFieldsAssociation(Integer templateId, String templateType);
	
	void saveTemplateLocationCustomFieldsAssociation(Integer templateId, String templateType, List<TemplateCustomFieldSRO> locationCustomFields,
			Integer userId, boolean deleteCustomFields);
	
	boolean extractHighCharacterLimitFlagFromResponse(Integer accountId);
	
	public String prepareEmailTemplateCategory(Integer templateId);
	
	/**
	 * @param smsTemplateId
	 * @return
	 * Prepare communication category of SMS template.
	 */
	public String prepareSmsTemplateCategory(Integer smsTemplateId);

	boolean extractTextCategoryEnabledFlag(Integer accountId);
	
	void processUnsubscribeText(Integer enterpriseId, SmsTemplateMessage smsTemplateMessage);
	
	void processUnsubscribeText(Integer enterpriseId, BusinessSmsTemplate smsTemplate);
	
	public String prepareUnsubscribeText(String smsCategory);
	
}
