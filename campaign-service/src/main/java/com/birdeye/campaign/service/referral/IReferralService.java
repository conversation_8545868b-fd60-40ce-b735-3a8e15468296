package com.birdeye.campaign.service.referral;

import com.birdeye.campaign.response.ReferralDashboardResponse;
import com.birdeye.referral.request.ReferralAppointmentListingRequest;
import com.birdeye.referral.request.ReferralCountSFDCRequest;
import com.birdeye.referral.response.ReferralCountSFDCResponse;

public interface IReferralService {

	ReferralCountSFDCResponse getReferralCountsForSFDCUpdate(ReferralCountSFDCRequest request);
	
	ReferralDashboardResponse getReferralDashboardData(Integer enterpriseId, ReferralAppointmentListingRequest request);
	
}
