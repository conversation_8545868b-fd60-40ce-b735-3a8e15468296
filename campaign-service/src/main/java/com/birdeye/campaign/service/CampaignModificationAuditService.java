package com.birdeye.campaign.service;

import java.util.Map;

import com.birdeye.campaign.cdc.dto.AllCampaignDataDTO;
import com.birdeye.campaign.dto.AllTemplateDataDto;
import com.birdeye.campaign.dto.CampaignModificationEvent;
import com.birdeye.campaign.request.ChangeLogsRequest;
import com.birdeye.campaign.response.ChangeLogsResponse;

public interface CampaignModificationAuditService {

	void createAuditForDefaultTemplates(Integer businessId, Integer emailTemplateId, Integer smsTemplateId, Integer campaignId, String templateType);

	void createAuditForDefaultPromotionalTemplates(Integer businessId, Integer emailTemplateId, Integer smsTemplateId, String templateName);
	
	void auditCleanedUpPriority(Integer accountId, Integer templateId, String deviceType, Integer sourceId);
	
	/**
	 * BIRD-72687
	 * Async Method to prepare & publish the campaign modification event.
	 * 
	 * @param oldCampaign
	 * @param onGoingCampaign
	 * @param userId
	 * @param modificationActionType
	 * @param modificationTimeStamp
	 * @param accountId
	 * @param isSplitCampaign
	 */
	public void prepareAndPublishCampaignModificationEvent(AllCampaignDataDTO oldCampaign, AllCampaignDataDTO onGoingCampaign, Integer userId, String modificationActionType,
			Long modificationTimeStamp, Integer accountId, boolean isSplitCampaign);
	
	/**
	 * Prepare & Publish Default Template Update Event.
	 * 
	 * @param oldDefaultTemplateId
	 * @param currentDefaultTemplateId
	 * @param userId
	 * @param enterpriseId
	 * @param modificationTimeStamp
	 * @param templateCategory
	 */
	public void prepareAndPublishDefaultTemplateUpdateEvent(Integer oldDefaultTemplateId, Integer currentDefaultTemplateId, Integer userId, Integer enterpriseId, Long modificationTimeStamp, String templateCategory);
	
	/**
	 * Prepare & Publish Template Delete Event(For Auditing Purpose).
	 * 
	 * @param templateId
	 * @param templateType (sms/email)
	 * @param userId
	 * @param enterpriseId
	 * @param modificationTimeStamp
	 */
	public void prepareAndPublishTemplateDeleteEvent(Integer templateId, String templateType, Integer userId, Integer enterpriseId, Long modificationTimeStamp);

	void detectAndAuditChanges(CampaignModificationEvent modificationEvent);

	/**
	 * 
	 * BIRD-74509 | Prepare and publish change log event for templates
	 *
	 * @param isNewOrGlobalTemplate,resellerId,enterpriseId,oldNewEntitiesDtoMap,userId
	 * 
	 */
	void validateAndSendChangeLogEventForTemplates(Boolean isNewOrGlobalTemplate, Integer resellerId, Integer enterpriseId, Integer templateId, Map<String, AllTemplateDataDto> oldNewEntitiesDtoMap,
			Integer userId, String eventSubType);
	
	
	/**
	 * Method to fetch Change logs of campaigns/templates.
	 * 
	 * @param accountId
	 * @param request
	 * @return
	 */
	public ChangeLogsResponse getChangeLogs(Integer accountId, ChangeLogsRequest request);
	
}
