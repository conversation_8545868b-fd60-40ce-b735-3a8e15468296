package com.birdeye.campaign.service;

import java.util.List;

import com.birdeye.campaign.dto.BusinessTemplateEntity;
import com.birdeye.campaign.dto.DefaultTemplateDTO;
import com.birdeye.campaign.dto.DefaultTemplateUpdateRequestDTO;

public interface DefaultTemplateHelperService {

	BusinessTemplateEntity getDefaultTemplateByEnterpriseIdAndTypeAndSource(Integer enterpriseId, String templateType, String source);

	void validateAndUpdateDefaultTemplate(DefaultTemplateUpdateRequestDTO object);

	void updateDefaultTemplateCacheByAccountIdAndTypeAndSource(List<BusinessTemplateEntity> templateList, String source, Integer enterpriseId);

	Integer isDefaultTemplateNotPresent(Integer enterpriseId, String templateType, String source, Integer isLocationTemplate);

	void evictTemplateListingCache(String source, Integer enterpriseId);
	
	DefaultTemplateDTO getDefaultTemplatesWithBackup(Integer enterpriseId, String templateType, String source, String callerAPI);

	DefaultTemplateDTO getDefaultTemplates(Integer enterpriseId, String templateType, String source);

	void validateAndUpdateEmailAndSmsDefaultTemplateInDb(DefaultTemplateDTO defaultDTO, Boolean isEmailDefaultTemplateRequired, Boolean isSmsDefaultTemplateRequired);

	void migrationHelperApi(DefaultTemplateDTO defaultDTO, List<BusinessTemplateEntity> emailTemplates, List<BusinessTemplateEntity> smsTemplates, String eventType, Integer isOneTimeMigrationRequest);
	
}
