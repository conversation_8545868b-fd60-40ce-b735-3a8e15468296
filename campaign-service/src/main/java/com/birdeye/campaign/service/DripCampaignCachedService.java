package com.birdeye.campaign.service;

import com.birdeye.campaign.entity.DripCampaign;
import com.birdeye.campaign.entity.DripCampaignCondition;

public interface DripCampaignCachedService {
	
	DripCampaignCondition getDripCampaignCondition(Integer campaignId);

	DripCampaign getDripCampaignBatch(Integer batchId);

	void evictDripCampaignBatchFromCache(Integer batchId);

	DripCampaign putToDripCampaignBatchCache(DripCampaign dripCampaign, Integer batchId);
	
	void evictDripCampaignConditionFromCache(Integer campaignId);
	
}
