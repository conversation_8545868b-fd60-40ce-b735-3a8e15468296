package com.birdeye.campaign.service.impl;

import java.util.Optional;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.entity.DripCampaign;
import com.birdeye.campaign.entity.DripCampaignCondition;
import com.birdeye.campaign.external.service.BusinessExternalService;
import com.birdeye.campaign.repository.DripCampaignConditionRepo;
import com.birdeye.campaign.repository.DripCampaignRepo;
import com.birdeye.campaign.service.DripCampaignCachedService;

@Service("dripCampaignCachedService")
public class DripCampaignCachedServiceImpl implements DripCampaignCachedService {
	
	@Autowired
	BusinessExternalService				businessExternalService;
	
	private static final Logger			logger	= LoggerFactory.getLogger(DripCampaignCachedServiceImpl.class);
	
	@Autowired
	private DripCampaignConditionRepo	dripCampaignConditionRepo;
	
	@Autowired
	private DripCampaignRepo			dripCampaignRepo;
	
	@Override
	@Cacheable(key = "#campaignId", value = "dripCampaignConditionCache", unless = "#result == null")
	public DripCampaignCondition getDripCampaignCondition(Integer campaignId) {
		logger.info("getDripCampaignCondition : for campaign id {}", campaignId);
		return dripCampaignConditionRepo.findByCampaignId(campaignId);
	}
	
	@Override
	@Cacheable(key = "#batchId", value = "dripCampaignBatchCache", unless = "#result == null")
	public DripCampaign getDripCampaignBatch(Integer batchId) {
		logger.info("getDripCampaignBatch : for batch id {}", batchId);
		Optional<DripCampaign> dripCampaign = dripCampaignRepo.findById(batchId);
		if (dripCampaign.isPresent())
			return dripCampaign.get();
		
		return null;
	}
	
	@Override
	@CacheEvict(value = "dripCampaignBatchCache", key = "#batchId")
	public void evictDripCampaignBatchFromCache(Integer batchId) {
		logger.info("evicting drip campaign batch for batch Id {} ", batchId);
	}
	
	@Override
	@CachePut(value = "dripCampaignBatchCache", key = "#batchId")
	public DripCampaign putToDripCampaignBatchCache(DripCampaign dripCampaign, Integer batchId) {
		logger.info("putToDripCampaignBatchCache : batch id {}", batchId);
		return dripCampaign;
	}
	
	@Override
	@CacheEvict(value = "dripCampaignConditionCache", key = "#campaignId")
	public void evictDripCampaignConditionFromCache(Integer campaignId) {
		logger.info("evicting drip campaign condition from cache for campaignId {} ", campaignId);
	}
	
}
