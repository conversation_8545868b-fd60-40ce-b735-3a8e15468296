package com.birdeye.campaign.global.templates.service;

import java.util.List;

import com.birdeye.campaign.dto.BusinessTemplateEntity;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.dto.GlobalTemplateValidationDTO;
import com.birdeye.campaign.dto.GlobalTemplatesDTO;
import com.birdeye.campaign.platform.constant.TemplateListingTypeEnum;
import com.birdeye.campaign.response.template.BusinessTemplateResponse;
import com.birdeye.campaign.response.template.GenericCampaignSmsTemplateResponse;

public interface GlobalTemplateHandlerService {
	
	GlobalTemplateValidationDTO validateIfGlobalTemplate(Integer templateId, Boolean isGlobalTemplate, Integer accountId, String source);
	
	GenericCampaignSmsTemplateResponse getGlobalTemplate(Integer enterpriseId, Integer templateId, String type, String source);
	
	void deleteGlobalTemplate(Integer enterpriseId, Integer templateId, GlobalTemplateValidationDTO validationDTO);
	
	Integer migrateAccountGlobalTemplateMapping(Integer fromBusinessId, Integer toBusinessId);
	
	GlobalTemplateValidationDTO validateAndCreateBusinessSmsTemplate(Boolean isGlobalTemplate, Integer smsTemplateId, Integer enterpriseId, String userId);
	
	BusinessTemplateResponse prepareAndSaveBusinessSmsTemplate(Integer enterpriseId, Integer userId, Integer templateId, Boolean isDeleted);
	
	CachedCollectionWrapper<GlobalTemplatesDTO> prepareAndGetGlobalTemplates(Integer enterpriseId, String source);
	
	CachedCollectionWrapper<BusinessTemplateEntity> getBusinessTemplateEntityListFromGlobalTemplates(Integer enterpriseId, List<String> templateType, TemplateListingTypeEnum listingEnum,
			String source, List<GlobalTemplatesDTO> globalTemplatesList);

	/**
	 * save or update the global template id corresponding to enterprise id in account global template mapping table
	 *
	 * 
	 * @param enterpriseId,globalTemplateId,type,source
	 */
	void prepareAndSaveOrUpdateAccountGlobalTemplateMapping(Integer enterpriseId, Integer globalTemplateId, Integer smsTemplateId, String source);
}
