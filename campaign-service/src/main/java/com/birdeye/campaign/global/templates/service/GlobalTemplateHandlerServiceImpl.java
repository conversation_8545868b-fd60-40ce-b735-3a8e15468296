package com.birdeye.campaign.global.templates.service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.birdeye.campaign.service.TemplateHelperService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.async.service.AsyncBrokerService;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.dto.AccountGlobalTemplateMappingDTO;
import com.birdeye.campaign.dto.AllTemplateDataDto;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.BusinessTemplateEntity;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.dto.GlobalTemplateValidationDTO;
import com.birdeye.campaign.dto.GlobalTemplatesDTO;
import com.birdeye.campaign.entity.AccountGlobalTemplateMapping;
import com.birdeye.campaign.entity.BusinessSmsTemplate;
import com.birdeye.campaign.platform.constant.TemplateListingTypeEnum;
import com.birdeye.campaign.response.external.BizappsPrimaryContactResponse;
import com.birdeye.campaign.response.template.BusinessTemplateResponse;
import com.birdeye.campaign.response.template.GenericCampaignSmsTemplateResponse;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.CampaignModificationAuditService;
import com.birdeye.campaign.service.dao.AccountGlobalTemplateMappingDao;
import com.birdeye.campaign.service.dao.BusinessSmsTemplateDao;
import com.birdeye.campaign.utils.CampaignModificationAuditUtils;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.GlobalTemplatesUtils;
import com.birdeye.template.sms.dto.SmsTemplateMessage;

@Service("GlobalTemplateHandlerServiceImpl")
public class GlobalTemplateHandlerServiceImpl implements GlobalTemplateHandlerService {
	
	private static final Logger				logger	= LoggerFactory.getLogger(GlobalTemplateHandlerServiceImpl.class);
	
	@Autowired
	private AccountGlobalTemplateMappingDao	accountGlobalTemplateMappingDao;
	
	@Autowired
	private CacheService					cacheService;
	
	@Autowired
	private BusinessSmsTemplateDao          businessSmsTemplateDao;
	
	@Autowired
	private AsyncBrokerService				asyncBrokerService;
	
	@Autowired
	private CampaignModificationAuditService	campaignModificationAuditService;
	
	@Autowired
	private TemplateHelperService templateHelperService;
	
	private static final Integer			ZERO_VALUE	= 0;
	
	/**
	 * Validate enterprise conditions and fetch global templates list based on source(email,sms)
	 *
	 * 
	 * @param enterpriseId,
	 *            source
	 */
	@Override
	@Cacheable(key = "#enterpriseId.toString().concat('-').concat(#source)", value = "globalTemplatesCache", unless = "#result == null")
	public CachedCollectionWrapper<GlobalTemplatesDTO> prepareAndGetGlobalTemplates(Integer enterpriseId, String source) {
		logger.info("prepareAndGetGlobalTemplates :: Request recieved to fetch global templates for enterprise id {} and source {}", enterpriseId, source);
		
		// Fetch Business from enterprise id
		BusinessEnterpriseEntity business = cacheService.getBusinessById(enterpriseId);
		if (business == null || business.getBusinessId() == null) {
			logger.error("prepareAndGetGlobalTemplates :: Business object found for enterprise id {} is null", enterpriseId);
			return null;
		}
		
		// Validate that whether given business is under a reseller or not.
		String accountType = business.getAccountType();
		if (CoreUtils.isAccountUnderReseller(accountType)) {
			logger.info("prepareAndGetGlobalTemplates :: Given Business with id {} is under a reseller as its account type is {}, No global templates will be fetched", enterpriseId, accountType);
			return null;
		}
		
		// Validate that Business is applicable for global templates based on creation time
		Long businessCreationTime = business.getCreated() != null ? business.getCreated().getTime() : 0l;
		if (BooleanUtils.isFalse(GlobalTemplatesUtils.isValidBusinessBasedOnTime(businessCreationTime))) {
			logger.info("prepareAndGetGlobalTemplates :: Business creation time is less than the allowed time for {} and time {}", enterpriseId, businessCreationTime);
			return null;
		}
		
		// Fetch Industry from BizApps
		BizappsPrimaryContactResponse bizAppResponse = cacheService.getPrimaryContactInfo(business.getBusinessId());
		if (bizAppResponse == null) {
			logger.error("prepareAndGetGlobalTemplates :: Empty response from bizapp service for enterprise id {}", enterpriseId);
			return null;
		}
		
		// Fetch Global Templates List
		List<GlobalTemplatesDTO> globalTemplatesList = GlobalTemplatesUtils.getGlobalTemplateList(bizAppResponse.getIndustry());
		if (CollectionUtils.isEmpty(globalTemplatesList)) {
			logger.error("prepareAndGetGlobalTemplates :: No global templates found for the industry {} and enterprise id {}", bizAppResponse.getIndustry(), enterpriseId);
			return null;
		}
		
		// Fetch Global Templates Mapping Against EnterpriseId, Remove Common Entries And Filter By Source
		CachedCollectionWrapper<AccountGlobalTemplateMappingDTO> accountGlobalTemplateMapping = accountGlobalTemplateMappingDao.fetchGlobalTemplateMappingsListForAccount(enterpriseId);
		List<AccountGlobalTemplateMappingDTO> accountGlobalTemplateMappingDTOList = (accountGlobalTemplateMapping != null) ? accountGlobalTemplateMapping.getElementsList() : null;
		globalTemplatesList = GlobalTemplatesUtils.filterGlobalTemplates(globalTemplatesList, accountGlobalTemplateMappingDTOList, source);
		if (CollectionUtils.isEmpty(globalTemplatesList)) {
			logger.info("prepareAndGetGlobalTemplates :: No global templates found for the industry {} and enterprise id {}", bizAppResponse.getIndustry(), enterpriseId);
			return null;
		}
		
		logger.info("prepareAndGetGlobalTemplates :: Size of templates list found for enterprise id {} is {}", enterpriseId, CollectionUtils.size(globalTemplatesList));
		CachedCollectionWrapper<GlobalTemplatesDTO> globalTemplateCachedCollection = new CachedCollectionWrapper<GlobalTemplatesDTO>(globalTemplatesList);
		return globalTemplateCachedCollection;
	}
	
	/**
	 * Filter all global templates list by available template types and prepare Business Template Entity list
	 *
	 * 
	 * @param enterpriseId,templateType,listingEnum,source,globalTemplateslist
	 */
	@Override
	public CachedCollectionWrapper<BusinessTemplateEntity> getBusinessTemplateEntityListFromGlobalTemplates(Integer enterpriseId, List<String> templateType, TemplateListingTypeEnum listingEnum,
			String source, List<GlobalTemplatesDTO> globalTemplatesList) {
		logger.info("getBusinessTemplateEntityListFromGlobalTemplates :: Request receieved to fetch global templates for enterprise {}, template types {}, source {}", enterpriseId, templateType, source);
		
		//Filter global template list by type
		globalTemplatesList = GlobalTemplatesUtils.filterGlobalTemplatesByType(globalTemplatesList, templateType);
		if (CollectionUtils.isEmpty(globalTemplatesList)) {
			logger.info("getBusinessTemplateEntityListFromGlobalTemplates :: No global templates found for enterprise id {}", enterpriseId);
			return null;
		}
		
		// Prepare BusinessTemplateEntity List to be returned
		CachedCollectionWrapper<BusinessTemplateEntity> businessTemplateEntityList = GlobalTemplatesUtils.prepareBusinessTemplateEntityList(globalTemplatesList, listingEnum, source);
		if (CollectionUtils.isNotEmpty(businessTemplateEntityList.getElementsList())) {
			logger.info("getBusinessTemplateEntityListFromGlobalTemplates :: Size of templates list found for enterprise id {} is {}", enterpriseId, CollectionUtils.size(businessTemplateEntityList.getElementsList()));
			return businessTemplateEntityList;
		}
		
		return null;
	}
	
	/**
	 * This function does the following :
	 * 1. If global template already deleted , return
	 * 2. Else add an entry in account global template mapping table
	 * 3. Evict caches
	 *
	 * 
	 * @param enterpriseId,templateId,validationDTO
	 */
	@Override
	public void deleteGlobalTemplate(Integer enterpriseId, Integer templateId, GlobalTemplateValidationDTO validationDTO) {
		logger.info("[GlobalTempateHandlerService] Deleting the global template : {} corresponding to the enterprise : {}.", templateId, enterpriseId);
		// Checking if the global template is already deleted or not
		if (validationDTO.getIsDeleted()) {
			logger.info("[GlobalTemplateHandlerService] Global Template : {} corresponding to the enterprise : {} is already deleted!");
			return;
		}
		AccountGlobalTemplateMapping accountGlobalTemplateMapping = null;
		if (StringUtils.equalsIgnoreCase(validationDTO.getSource(), Constants.SMS_TYPE)) {
			accountGlobalTemplateMapping = GlobalTemplatesUtils.prepareAccountGlobalTemplateMappingData(enterpriseId, templateId, null, null);
		}
		
		accountGlobalTemplateMapping.setIsDeleted(Constants.TEMPLATE_IS_DELETED);
		accountGlobalTemplateMappingDao.saveOrUpdateAccountGlobalTemplateMapping(accountGlobalTemplateMapping);
		// Evict cache
		asyncBrokerService.evictGlobalTemplatesCaches(enterpriseId, validationDTO.getSource());
		
		return;
	}
	
	/**
	 * This function validates that the template id given represents a global template based on the following :
	 * 1. If global template flag is false, not a global template
	 * 2. If supplied template id doesn't exist in global template cache, not a global template
	 * 3. Fetch mapping for the template id and enterprise id in account global template mapping table
	 * 4. If mapping found but the template is deleted, it is a deleted global template
	 * 5. If mapping found then update the template id in response and it is not a global template
	 * 6. If no mapping found , it is a global template
	 *
	 * 
	 * @param templateId,isGlobalTemplate,enterpriseId,source
	 */
	@Override
	public GlobalTemplateValidationDTO validateIfGlobalTemplate(Integer templateId, Boolean isGlobalTemplate, Integer enterpriseId, String source) {
		if (isGlobalTemplate == null || BooleanUtils.isFalse(isGlobalTemplate) || templateId == null) {
			return new GlobalTemplateValidationDTO(false, null);
		}
		
		Boolean isValidGlobalTemplateId = GlobalTemplatesUtils.validateGlobalTemplate(templateId);
		if (BooleanUtils.isFalse(isValidGlobalTemplateId)) {
			logger.info("[GlobalTemplateHandlerService] validateIfGlobalTemplate :: Validation Failed. The global template with id : {} doesn't exists!", templateId);
			return new GlobalTemplateValidationDTO(false, null);
		}
		
		// Check whether global template's copy is already present for template id specified
		if (StringUtils.equalsIgnoreCase(source, Constants.SMS_TYPE)) {
			AccountGlobalTemplateMapping mapping = accountGlobalTemplateMappingDao.fetchGlobalTemplateMappingForAccountAndTemplateId(enterpriseId, templateId);
			if (mapping != null) {
				// If someone deletes the global template and then tries to get or save template.
				if (mapping.getIsDeleted() == Integer.valueOf(Constants.TEMPLATE_IS_DELETED)) {
					logger.info("[GlobalTemplateHandlerService] validateIfGlobalTemplate :: The global template with id : {} is already deleted!", templateId);
					return new GlobalTemplateValidationDTO(true, null, true);
				}
				
				// If global template is already saved
				logger.info("[GlobalTemplateHandlerService] validateIfGlobalTemplate :: Validation Failed. A copy of global template {} already exists with id : {} ", templateId,
						mapping.getSmsTemplateId());
				return new GlobalTemplateValidationDTO(false, mapping.getSmsTemplateId());
			}
		}
		return new GlobalTemplateValidationDTO(true, null);
	}
	
	/**
	 * Get global template by id
	 *
	 * 
	 * @param enterpriseId,templateId,type,source
	 */
	@Override
	public GenericCampaignSmsTemplateResponse getGlobalTemplate(Integer enterpriseId, Integer templateId, String type, String source) {
		logger.info("getGlobalTemplate :: Request receieved to fetch global template for enterprise {}, template id {}, source {} and type {}", enterpriseId, templateId, source, type);
		if (StringUtils.equalsIgnoreCase(source, Constants.SMS_TYPE)) {
			SmsTemplateMessage smsTemplate = GlobalTemplatesUtils.prepareSmsTemplateMessage(templateId);
			if (smsTemplate == null) {
				logger.info("getGlobalTemplate :: No global template found for enterprise {}, template id {}, source {} and type {}", enterpriseId, templateId, source, type);
				return null;
			}
			templateHelperService.processUnsubscribeText(enterpriseId, smsTemplate);
			GenericCampaignSmsTemplateResponse response = new GenericCampaignSmsTemplateResponse(smsTemplate, null, null, null);
			response.setSelectedCustomFields(Collections.emptyList());
			response.setAppointmentCustomFields(Collections.emptyList());
			response.setIsGlobalTemplate(true);
			return response;
		}
		return null;
	}
	
	/**
	 * save or update the global template id corresponding to enterprise id in account global template mapping table
	 *
	 * 
	 * @param enterpriseId,globalTemplateId,templateId,source
	 */
	@Override
	public void prepareAndSaveOrUpdateAccountGlobalTemplateMapping(Integer enterpriseId, Integer globalTemplateId, Integer templateId, String source) {
		// Step 1 : Check if an entry in the account_global_template_mapping exist corresponding to a global template id & enterprise id
		AccountGlobalTemplateMapping mappingData = accountGlobalTemplateMappingDao.fetchGlobalTemplateMappingForAccountAndTemplateId(enterpriseId, globalTemplateId);
		
		// Step 2 : If no entry exist then prepare the data & save. Else update the same entry
		if (StringUtils.equalsIgnoreCase(source, Constants.SMS_TYPE)) {
			if (mappingData == null) {
				logger.info("[GlobalTemplateHandlerService] No data for global template id : {} & enterprise id : {} exists!", globalTemplateId, enterpriseId);
				mappingData = GlobalTemplatesUtils.prepareAccountGlobalTemplateMappingData(enterpriseId, globalTemplateId, templateId, null);
			} else {
				logger.info("[GlobalTemplateHandlerService] Data corresponding to global template id : {} & enterprise id : {} already exists!", globalTemplateId, enterpriseId);
				mappingData.setIsDeleted(Constants.TEMPLATE_IS_DELETED_DEFAULT);
				mappingData.setSmsTemplateId(templateId);
			}
			accountGlobalTemplateMappingDao.saveOrUpdateAccountGlobalTemplateMapping(mappingData);
		}
		
	}
	
	/**
	 * Update accountId in account global template mapping table if exists and clear caches
	 *
	 * 
	 * @param fromBusinessId,toBusinessId
	 */
	@Override
	public Integer migrateAccountGlobalTemplateMapping(Integer fromBusinessId, Integer toBusinessId) {
		List<AccountGlobalTemplateMapping> mappingData = accountGlobalTemplateMappingDao.fetchGlobalTemplateMappingsForAccount(fromBusinessId);
		if (CollectionUtils.isEmpty(mappingData)) {
			return ZERO_VALUE;
		} else {
			mappingData.stream().forEach(md -> md.setAccountId(toBusinessId));
		}
		accountGlobalTemplateMappingDao.saveOrUpdateAccountGlobalTemplateMappingList(mappingData);
		asyncBrokerService.evictGlobalTemplatesCaches(fromBusinessId, Constants.SMS_TYPE);
		asyncBrokerService.evictGlobalTemplatesCaches(toBusinessId, Constants.SMS_TYPE);
		return CollectionUtils.size(mappingData);
	}
	
	/**
	 * Validate and create business sms template from global template
	 *
	 * 
	 * @param isGlobalTemplate,smsTemplateId,enterpriseId,userId
	 */
	@Override
	public GlobalTemplateValidationDTO validateAndCreateBusinessSmsTemplate(Boolean isGlobalTemplate, Integer smsTemplateId, Integer enterpriseId, String userId) {
		logger.info("validateAndCreateBusinessSmsTemplate ::  Request receievd to save global template for enterprise id {}, template id {}, global template flag {} and user id {}", enterpriseId,
				smsTemplateId, isGlobalTemplate, userId);
		
		//Validate whether the request is valid
		GlobalTemplateValidationDTO globalTemplateValidation = validateIfGlobalTemplate(smsTemplateId, isGlobalTemplate, enterpriseId, Constants.SMS_TYPE);
		
		//If global template, then make a local copy and return the id of business sms template table
		if (globalTemplateValidation.getIsGlobalTemplate()) {
			BusinessTemplateResponse response = prepareAndSaveBusinessSmsTemplate(enterpriseId, GlobalTemplatesUtils.convertStringToInteger(userId), smsTemplateId,
					globalTemplateValidation.getIsDeleted());
			if (response != null) {
				// If template created successfully then mark global template as false and set the newly created entry's id.
				globalTemplateValidation.setIsGlobalTemplate(false);
				globalTemplateValidation.setTemplateId(response.getId());
				logger.info("validateAndCreateBusinessSmsTemplate :: Response returned for enterprise id {} and template id {} is {}", enterpriseId, smsTemplateId, globalTemplateValidation);
				return globalTemplateValidation;
			}
			logger.info("validateAndCreateBusinessSmsTemplate ::  Null response received from save global template api for template id {} and enterprise id {}, returning original template id",
					smsTemplateId, enterpriseId);
			//If business sms template creation failed, then return the original template id
			globalTemplateValidation.setIsGlobalTemplate(false);
			globalTemplateValidation.setTemplateId(smsTemplateId);
			return globalTemplateValidation;
			
		}
		
		//If not a global template, return input template id
		if (globalTemplateValidation.getTemplateId() == null) {
			globalTemplateValidation.setTemplateId(smsTemplateId);
		}
		
		logger.info("validateAndCreateBusinessSmsTemplate :: Response returned for enterprise id {} and template id {} is {}", enterpriseId, smsTemplateId, globalTemplateValidation);
		return globalTemplateValidation;
	}
	
	/**
	 * Prepare and save business template from global template
	 *
	 * 
	 * @param enterpriseId,userId,templateId,isDeleted
	 */
	@Override
	public BusinessTemplateResponse prepareAndSaveBusinessSmsTemplate(Integer enterpriseId, Integer userId, Integer templateId, Boolean isDeleted) {
		//Get GlobalTemplate by id
		GlobalTemplatesDTO globalTemplateObject = GlobalTemplatesUtils.getGlobalTemplateById(templateId);
		
		//Prepare smsTemplateMessage from global template
		SmsTemplateMessage smsTemplateMessage = GlobalTemplatesUtils.prepareSmsTemplateMessage(templateId);
		if (smsTemplateMessage == null) {
			logger.info("prepareAndSaveBusinessSmsTemplate ::  No global template for global template id : {} & enterprise id : {} exists!", templateId, enterpriseId);
			return null;
		}
		
	    //Prepare BusinessSmsTemplate from Global Template
		BusinessSmsTemplate businessSmsTemplate = GlobalTemplatesUtils.prepareBusinessSmsTemplateForGlobalTemplate(enterpriseId, globalTemplateObject.getTemplateType(), smsTemplateMessage, userId,
				null, isDeleted);
		
		//Fetch count of same name business sms templates
		//Integer sameNameCount = businessSmsTemplateDao.getBusinessSmsTemplateByEnterpriseIdAndName(enterpriseId, businessSmsTemplate.getName());
		
		//Update Name of BusinessSmsTemplate
		//GlobalTemplatesUtils.updateBusinessSmsTemplateName(businessSmsTemplate, sameNameCount);
		
		// Save BusinessSmsTemplate in db
		logger.info("prepareAndSaveBusinessSmsTemplate ::  BusinessSmsTemplate object prepared for global template id : {} & enterprise id : {} is {}!", templateId, enterpriseId, businessSmsTemplate);
		businessSmsTemplate = businessSmsTemplateDao.saveBusinessSmsTemplate(businessSmsTemplate);
		
		//Update account global template mapping
		prepareAndSaveOrUpdateAccountGlobalTemplateMapping(enterpriseId, templateId, businessSmsTemplate.getId(), Constants.SMS_TYPE);
		
		//Evict cache
		asyncBrokerService.evictGlobalTemplatesCaches(enterpriseId, Constants.SMS_TYPE);
		asyncBrokerService.evictSmsTemplatesListCache(enterpriseId);
		
		prepareAndSendChangeLogEvent(enterpriseId, userId, businessSmsTemplate);
		
		return new BusinessTemplateResponse(businessSmsTemplate.getId(), businessSmsTemplate.getName());
	}

	private void prepareAndSendChangeLogEvent(Integer enterpriseId, Integer userId, BusinessSmsTemplate businessSmsTemplate) {
		Map<String, AllTemplateDataDto> oldNewEntitiesDataMap = CampaignModificationAuditUtils.initializeDataDiffMap(Boolean.FALSE, Boolean.TRUE, Boolean.FALSE);
		CampaignModificationAuditUtils.addTemplateDataToAllTemplateData(oldNewEntitiesDataMap, businessSmsTemplate, CampaignModificationAuditUtils.NEW_TEMPLATE_DATA);
		campaignModificationAuditService.validateAndSendChangeLogEventForTemplates(Boolean.TRUE, null, enterpriseId, businessSmsTemplate.getId(), oldNewEntitiesDataMap, userId, Constants.SMS_TYPE);
	}
}
