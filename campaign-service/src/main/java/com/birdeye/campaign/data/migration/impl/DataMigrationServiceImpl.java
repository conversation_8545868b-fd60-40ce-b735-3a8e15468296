package com.birdeye.campaign.data.migration.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.birdeye.campaign.service.dao.ReviewGenerationDao;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.businessonboarding.service.BusinessOnboardingService;
import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.commmunication.consumer.ReferralCodeMigrationService;
import com.birdeye.campaign.commmunication.consumer.RequestDateMigrationService;
import com.birdeye.campaign.commmunication.consumer.SegmentCountMigrationService;
import com.birdeye.campaign.commmunication.consumer.UsageCommunicationConsumerService;
import com.birdeye.campaign.communication.message.EventMigrationRequest;
import com.birdeye.campaign.communication.message.RequestIdDTO;
import com.birdeye.campaign.communication.message.referral.ReferralCodeMigrationESResponse;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.constant.KafkaTopicTypeEnum;
import com.birdeye.campaign.data.migration.DataMigrationService;
import com.birdeye.campaign.db.services.CampaignConditionAndSettingDataGateway;
import com.birdeye.campaign.dto.AllTemplateDataDto;
import com.birdeye.campaign.dto.AppointmentScheduleInfo;
import com.birdeye.campaign.dto.AutomationCampaignMigrationResponse;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.BusinessTemplateEntity;
import com.birdeye.campaign.dto.DefaultTemplateDTO;
import com.birdeye.campaign.dto.KafkaMessage;
import com.birdeye.campaign.dto.ReviewRequestDto;
import com.birdeye.campaign.dto.RuleCondition;
import com.birdeye.campaign.dto.RuleExpression;
import com.birdeye.campaign.elasticsearch.request.ElasticSearchBaseRequest;
import com.birdeye.campaign.elasticsearch.service.ElasticQueryTemplateEnum;
import com.birdeye.campaign.elasticsearch.service.ElasticSearchHelperService;
import com.birdeye.campaign.entity.BusinessDeeplinkPriority;
import com.birdeye.campaign.entity.BusinessEmailTemplate;
import com.birdeye.campaign.entity.BusinessSmsTemplate;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.CampaignAccountSettings;
import com.birdeye.campaign.entity.CampaignCondition;
import com.birdeye.campaign.entity.EmailTemplate;
import com.birdeye.campaign.entity.MigratedTemplatesAudit;
import com.birdeye.campaign.entity.Promotion;
import com.birdeye.campaign.entity.ReferralCodeMigrationAudit;
import com.birdeye.campaign.entity.RequestDateMigrationAudit;
import com.birdeye.campaign.entity.ReviewRequest;
import com.birdeye.campaign.entity.SegmentCountMigrationAudit;
import com.birdeye.campaign.enums.BusinessAccountTypeEnum;
import com.birdeye.campaign.enums.BusinessTypeEnum;
import com.birdeye.campaign.enums.CampaignPriorityEnum;
import com.birdeye.campaign.enums.EmailTemplateTypes;
import com.birdeye.campaign.enums.GroupByTimeEnum;
import com.birdeye.campaign.enums.MigrationEventTypeEnum;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.exception.CampaignHTTPException;
import com.birdeye.campaign.external.factory.ElasticSearchClientFactory;
import com.birdeye.campaign.external.service.BusinessExternalService;
import com.birdeye.campaign.external.service.IContactExternalService;
import com.birdeye.campaign.kafka.service.KafkaService;
import com.birdeye.campaign.platform.constant.BusinessEventEnum;
import com.birdeye.campaign.platform.constant.CampaignStatusEnum;
import com.birdeye.campaign.platform.constant.CampaignTypeEnum;
import com.birdeye.campaign.platform.constant.RequestStatusEnum;
import com.birdeye.campaign.platform.constant.TemplateTypeEnum;
import com.birdeye.campaign.platform.entity.AccountFeatures;
import com.birdeye.campaign.platform.entity.Business;
import com.birdeye.campaign.platform.entity.BusinessTemplateConfig;
import com.birdeye.campaign.platform.readonly.repository.BusinessReadOnlyRepo;
import com.birdeye.campaign.platform.repository.AccountFeaturesRepo;
import com.birdeye.campaign.platform.repository.BusinessTemplateConfigRepo;
import com.birdeye.campaign.platformdb.services.CampaignAndTemplateDataGateway;
import com.birdeye.campaign.projections.AppointmentDTO;
import com.birdeye.campaign.repository.BusinessEmailTemplateRepo;
import com.birdeye.campaign.repository.BusinessSMSTemplateRepo;
import com.birdeye.campaign.repository.CampaignAccountSettingsRepo;
import com.birdeye.campaign.repository.CampaignConditionRepo;
import com.birdeye.campaign.repository.CampaignRepo;
import com.birdeye.campaign.repository.EmailTemplateRepo;
import com.birdeye.campaign.repository.MigratedTemplatesAuditRepo;
import com.birdeye.campaign.repository.PromotionRepo;
import com.birdeye.campaign.repository.ReferralCodeMigrationAuditRepo;
import com.birdeye.campaign.repository.RequestDateMigrationAuditRepo;
import com.birdeye.campaign.repository.ReviewRequestRepo;
import com.birdeye.campaign.repository.SegmentCountMigrationAuditRepo;
import com.birdeye.campaign.request.AccessSettingsUpdateRequest;
import com.birdeye.campaign.request.AutomationCampaignRequest;
import com.birdeye.campaign.request.BusinessMigrationEventRequest;
import com.birdeye.campaign.request.ProductFeatureRequest;
import com.birdeye.campaign.request.Tag;
import com.birdeye.campaign.request.template.EnterpriseTemplatesMigrationBasicRO;
import com.birdeye.campaign.request.template.EnterpriseTemplatesMigrationRO;
import com.birdeye.campaign.request.template.MigrationRequest;
import com.birdeye.campaign.request.template.OngoingCampaignByLocationRequest;
import com.birdeye.campaign.request.template.OngoingCampaignByLocationRequest.TemplateByLocationRequest;
import com.birdeye.campaign.response.CustomerCustomFieldResponse;
import com.birdeye.campaign.response.CustomerCustomFields;
import com.birdeye.campaign.response.OngoingEditCampaignResponse;
import com.birdeye.campaign.response.external.DefaultReviewSourceResponse;
import com.birdeye.campaign.response.external.DefaultReviewSourceResponse.DefaultReviewSource;
import com.birdeye.campaign.response.template.BusinessTemplateResponse;
import com.birdeye.campaign.response.template.GenericCampaignSmsTemplateResponse;
import com.birdeye.campaign.response.template.v2.EditTemplateResponse;
import com.birdeye.campaign.response.template.v2.EmailTemplateResponse;
import com.birdeye.campaign.service.AutomationCampaignSetupService;
import com.birdeye.campaign.service.BusinessOptionService;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.CampaignModificationAuditService;
import com.birdeye.campaign.service.CampaignSetupService;
import com.birdeye.campaign.service.DefaultTemplateHelperService;
import com.birdeye.campaign.service.IReviewSourcesService;
import com.birdeye.campaign.service.ReviewRequestService;
import com.birdeye.campaign.service.TemplateService;
import com.birdeye.campaign.service.dao.BusinessDeeplinkPriorityDao;
import com.birdeye.campaign.service.referral.IReferralAppointmentService;
import com.birdeye.campaign.sro.CampaignSmsTemplateSRO;
import com.birdeye.campaign.sro.ReviewSourceSRO;
import com.birdeye.campaign.template.email.cache.v2.IEmailTemplateCacheService;
import com.birdeye.campaign.template.email.v2.IEmailTemplateService;
import com.birdeye.campaign.user.access.settings.service.CampaignUserAccessSettingsService;
import com.birdeye.campaign.utils.BusinessUtils;
import com.birdeye.campaign.utils.CampaignModificationAuditUtils;
import com.birdeye.campaign.utils.CampaignUtils;
import com.birdeye.campaign.utils.CoreUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;;

@Service("dataMigrationService")
public class DataMigrationServiceImpl implements DataMigrationService {
	
	private static final String						AND									= " AND ";
	
	private static final String						FFFFFF								= "#ffffff";
	
	private static final String						SURVEY_BUTTON_COLOR					= "#4caf50";
	
	private static final String						RR_DATE_MIGRATION_BATCH_SIZE		= "rr_date_migration_batch_size";
	
	private static final String						RR_DATE_MIGRATION_BATCH_SPLIT		= "rr_date_migration_batch_split";
	
	private static final String						SEGMENT_COUNT_MIGRATION_BATCH_SIZE	= "segment_count_migration_batch_size";
	
	private static final String						SEGMENT_COUNT_MIGRATION_BATCH_SPLIT	= "segment_count_migration_batch_split";
	
	private static final Integer					APPOINTMENT_MIGRATION_BATCH_SIZE	= 450;
	
	private static final Logger						logger								= LoggerFactory.getLogger(DataMigrationServiceImpl.class);
	
	private static final ObjectMapper				objectMapper						= new ObjectMapper();
	
	@Autowired
	private CampaignRepo							campaignRepo;
	
	@Autowired
	private BusinessReadOnlyRepo businessReadOnlyRepo;
	
	@Autowired
	@Qualifier(Constants.CAMPAIGN_UI_COMPLETABLE_FUTURE_TASK_EXECUTOR)
	private ThreadPoolTaskExecutor					threadPoolTaskExecutor;
	
	@Autowired
	private CampaignAndTemplateDataGateway			campaignTemplateDataGateway;
	
	@Autowired
	private CampaignConditionAndSettingDataGateway	campaignConditionSettingDataGateway;
	
	@Autowired
	private BusinessEmailTemplateRepo				businessEmailTemplateRepo;
	
	@Autowired
	private EmailTemplateRepo						emailTemplateRepo;
	
	@Autowired
	private BusinessSMSTemplateRepo					businessSMSTemplateRepo;
	
	@Autowired
	private IEmailTemplateCacheService				emailTemplateCacheService;
	
	@Autowired
	private IEmailTemplateService					emailTemplateService;
	
	@Autowired
	private TemplateService							templateService;
	
	@Autowired
	private CampaignAccountSettingsRepo				campaignAccountSettionRepo;
	
	@Autowired
	private BusinessOptionService					businessOptionService;
	
	@Autowired
	private BusinessExternalService					businessExternalService;
	
	@Autowired
	private BusinessTemplateConfigRepo				businessTemplateConfigRepo;
	
	@Autowired
	private CampaignSetupService					campaignSetupService;
	
	@Autowired
	private AccountFeaturesRepo						accountFeaturesRepo;
	
	@Autowired
	private MigratedTemplatesAuditRepo				migratedTemplatesAuditRepo;
	
	@Autowired
	private BusinessOnboardingService				businessOnboardingService;
	
	@Autowired
	private UsageCommunicationConsumerService		usageCommunicationConsumerService;
	
	@Autowired
	private PromotionRepo							promotionRepo;
	
	@Autowired
	private ReviewRequestService					reviewRequestService;
	
	@Autowired
	private KafkaService							kafkaService;
	
	@Autowired
	private ReviewRequestRepo						reviewRequestRepo;
	
	@Autowired
	private RequestDateMigrationService				requestDateMigrationService;
	
	@Autowired
	private ReferralCodeMigrationService			referralCodeMigrationService;
	
	@Autowired
	private RequestDateMigrationAuditRepo			requestDateMigrationAuditRepo;
	
	@Autowired
	private ReferralCodeMigrationAuditRepo			referralCodeMigrationAuditRepo;
	
	@Autowired
	private SegmentCountMigrationService			segmentCountMigrationService;
	
	@Autowired
	private SegmentCountMigrationAuditRepo			segmentCountMigrationAuditRepo;
	
	@Autowired
	private AutomationCampaignSetupService			automationCampaignSetupService;
	
	@Autowired
	private CacheService							cacheService;
	
	@Autowired
	private BusinessDeeplinkPriorityDao businessDeeplinkPriorityDaoService;
	
	@Autowired
	private IReviewSourcesService					reviewSourcesService;
	
	@Autowired
	private IContactExternalService					contactExternalService;
	
	@Autowired
	private CampaignConditionRepo					campaignConditionRepo;
	
	@Autowired
	private IReferralAppointmentService				referralAppointmentService;
	
	@Autowired
	private ElasticSearchHelperService				elasticSearchHelperService;

	
	@Autowired
	private ElasticSearchClientFactory				elasticSearchClientFactory;
	
	@Autowired
	private DefaultTemplateHelperService            defaultTemplateService;
	
	@Autowired
	private CampaignUserAccessSettingsService       userAccessSettingsService;
	
	@Autowired
	private CampaignModificationAuditService        campaignModificationAuditService;
	
	@Autowired
	private ReviewGenerationDao 					reviewGenerationDao;
	
	/**
	 * Migrate Campaign Data - SMB upgrade ES index data delete for RR,Cx,Referral,Promotion Re-populate ES for the business
	 */
	@Override
	public void migrateCampaignDataFromOneBusinessToAnotherBusiness(BusinessMigrationEventRequest request) {
		if (request == null) {
			logger.error("migrateCampaignDataFromOneBusinessToAnotherBusiness : invalid BusinessMigration request received");
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "businessMigrationEventRequest cannnot be null");
		}
		if (isSmbToEnterpriseUpgrade(request)) {
			// 1. migrate campaign
			campaignTemplateDataGateway.migrateCampaigns(request.getSourceBusinessId(), request.getTargetEnterpriseId());
			// 2. migrate campaign condition
			campaignConditionSettingDataGateway.migrateCampaignCondition(request.getSourceBusinessId(), request.getTargetEnterpriseId());
			// 3. migrate email template
			campaignTemplateDataGateway.migrateEmailTemplates(request.getSourceBusinessId(), request.getTargetEnterpriseId());
			// 4. migrate sms template
			campaignTemplateDataGateway.migrateSMSTemplates(request.getSourceBusinessId(), request.getTargetEnterpriseId());
			// 5.	
			campaignConditionSettingDataGateway.migrateCampaignSetting(request.getSourceBusinessId(), request.getTargetEnterpriseId());
			// 6. migrate global template mappings
			campaignTemplateDataGateway.migrateAccountGlobalTemplateMapping(request.getSourceBusinessId(), request.getTargetEnterpriseId());
			// 7. migrate split campaign
			campaignTemplateDataGateway.migrateSplitCampaigns(request.getSourceBusinessId(), request.getTargetEnterpriseId());
			// 8. migrate review generation & review generation sources
			reviewGenerationDao.migrateReviewGenerationsAndSources(request.getSourceBusinessId(), request.getTargetEnterpriseId(), request.getEventType());
			
			kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_USAGE_MIGRATE, new KafkaMessage(request.getEventId(), request));
			
		} else if (isBusinessMove(request)) {
			kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_USAGE_MIGRATE, new KafkaMessage(request.getEventId(), request));
		} else if (isBusinessDowngrade(request)) {
			if (BusinessEventEnum.DOWNGRADE.getEventType().equalsIgnoreCase(request.getEventType()) && !request.getSourceEnterpriseNumber().equals(request.getTargetBusinessNumber())) {
				logger.info("Request received to migrate Campaign Data - Enterprise Location to SMB conversion {}", request);
				BusinessEnterpriseEntity business = cacheService.getBusinessByIdForMigration(request.getTargetBusinessId());
				if (business == null || !business.getClosed().equals(Integer.valueOf(0))) {
					logger.error("migrateCampaignDataFromOneBusinessToAnotherBusiness : No valid business found for business id {},eventId {}", request.getTargetBusinessId(), request.getEventId());
					usageCommunicationConsumerService.pushAuditEventToKafka(request.getEventId(), Constants.CAMPAIGN_AUDIT_MODULE_INVALID_EVENT, Constants.FAILURE_STATUS);
					return;
				}
				MDC.put(Constants.BUSINESS_ID, String.valueOf(request.getTargetBusinessId()));
				Integer defaultUserId = Integer.valueOf(1946);
				reviewGenerationDao.migrateReviewGenerationsAndSources(request.getSourceBusinessId(), request.getTargetBusinessId(), request.getEventType());
				businessOnboardingService.createDefaultTemplatesAndCampaignsForABusiness(request.getTargetBusinessId(), request.getUserId() != null ? request.getUserId() : defaultUserId);
				enableCampaignFeatureForBusiness(business, request.getUserId() != null ? request.getUserId() : defaultUserId);
			}
			kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_USAGE_MIGRATE, new KafkaMessage(request.getEventId(), request));
		} else {
			logger.error("Migrate campaigns data for Business {} - invalid event received for eventId {}", request.getEventType(), request.getEventId());
			usageCommunicationConsumerService.pushAuditEventToKafka(request.getEventId(), Constants.CAMPAIGN_AUDIT_MODULE_INVALID_EVENT, Constants.FAILURE_STATUS);
		}
	}
	
	private boolean isBusinessDowngrade(BusinessMigrationEventRequest request) {
		return request.getSourceEnterpriseId() != null
				&& StringUtils.equalsAnyIgnoreCase(request.getSourceBusinessType(), BusinessTypeEnum.BUSINESS.getBusinessType(), BusinessTypeEnum.PRODUCT.getBusinessType())
				&& StringUtils.equalsAnyIgnoreCase(request.getTargetBusinessType(), BusinessTypeEnum.BUSINESS.getBusinessType(), BusinessTypeEnum.PRODUCT.getBusinessType())
				&& BusinessEventEnum.DOWNGRADE.getEventType().equalsIgnoreCase(request.getEventType());
	}
	
	private boolean isSmbToEnterpriseUpgrade(BusinessMigrationEventRequest request) {
		return request.getSourceEnterpriseId() == null && request.getTargetEnterpriseId() != null
				&& StringUtils.equalsAnyIgnoreCase(request.getSourceBusinessType(), BusinessTypeEnum.BUSINESS.getBusinessType(), BusinessTypeEnum.PRODUCT.getBusinessType())
				&& StringUtils.equalsAnyIgnoreCase(request.getTargetBusinessType(), BusinessTypeEnum.ENTERPRISE_LOCATION.getBusinessType(), BusinessTypeEnum.ENTERPRISE_PRODUCT.getBusinessType())
				&& BusinessEventEnum.UPGRADE.getEventType().equalsIgnoreCase(request.getEventType());
	}
	
	private boolean isBusinessMove(BusinessMigrationEventRequest request) {
		return request.getTargetEnterpriseId() != null
				&& StringUtils.equalsAnyIgnoreCase(request.getSourceBusinessType(), BusinessTypeEnum.BUSINESS.getBusinessType(), BusinessTypeEnum.PRODUCT.getBusinessType())
				&& StringUtils.equalsAnyIgnoreCase(request.getTargetBusinessType(), BusinessTypeEnum.ENTERPRISE_LOCATION.getBusinessType(), BusinessTypeEnum.ENTERPRISE_PRODUCT.getBusinessType())
				&& BusinessEventEnum.MOVE.getEventType().equalsIgnoreCase(request.getEventType());
	}
	
	@Override
	public void deleteAndMigrateUsageDataToEsByBusinessId(Integer fromBusinessId, BusinessMigrationEventRequest migrationEventRequest) {
		RequestIdDTO request = getRequestIdByBusinessId(fromBusinessId);
		if (request == null) {
			logger.info("No requestId found to migrate for businessId {}", fromBusinessId);
			return;
		}
		
		usageCommunicationConsumerService.deleteReviewRequestsFromEsIndex(request, migrationEventRequest);
		logger.info("deleteAndMigrateUsageDataToEsByBusinessid : ES doc successfully deleted for businessId {}", fromBusinessId);
		migrateReviewRequestsToEsIndex(request, fromBusinessId);
		if (migrationEventRequest != null) {
			logger.info("push in kafka - migration success event for eventId {}", migrationEventRequest.getEventId());
			usageCommunicationConsumerService.pushAuditEventToKafka(migrationEventRequest.getEventId(), Constants.CAMPAIGN_AUDIT_MODULE, Constants.SUCCESS_STATUS);
		}
	}
	
	private RequestIdDTO getRequestIdByBusinessId(Integer fromBusinessId) {

		List<String> campaignType = Arrays.asList(CampaignTypeEnum.REVIEW_REQUEST.getType(), CampaignTypeEnum.CX_REQUEST.getTemplateType(), CampaignTypeEnum.REFERRAL.getType(), CampaignTypeEnum.APPOINTMENT_REMINDER.getType(), CampaignTypeEnum.APPOINTMENT_RECALL.getType());
		List<String> appointmentCampaignType = Arrays.asList(CampaignTypeEnum.APPOINTMENT_REMINDER.getType(), CampaignTypeEnum.APPOINTMENT_RECALL.getType(),
				CampaignTypeEnum.APPOINTMENT_FORM.getType());
		
		List<ReviewRequestDto> reviewRequest = reviewRequestRepo.findReviewRequestIdsByBusinessId(fromBusinessId, campaignType);
		List<Long> promotionRequestIds = promotionRepo.findPromotionRequestByBusinessId(new ArrayList<>(Arrays.asList(fromBusinessId)));
		List<ReviewRequestDto> appointmentReviewRequest = reviewRequestRepo.findAppointmentReviewRequestIdsByBusinessId(fromBusinessId, appointmentCampaignType);
		if (CollectionUtils.isEmpty(reviewRequest) && CollectionUtils.isEmpty(promotionRequestIds) && CollectionUtils.isEmpty(appointmentReviewRequest)) {
			return null;
		}
		List<Long> rrRequestIds = reviewRequest.stream().filter(rr -> rr.getRequestType().equalsIgnoreCase(CampaignTypeEnum.REVIEW_REQUEST.getType())).map(ReviewRequestDto::getReviewRequestId)
				.collect(Collectors.toList());
		List<Long> cxRequestIds = reviewRequest.stream().filter(rr -> rr.getRequestType().equalsIgnoreCase(CampaignTypeEnum.CX_REQUEST.getTemplateType())).map(ReviewRequestDto::getReviewRequestId)
				.collect(Collectors.toList());
		List<Long> referralRequestIds = reviewRequest.stream().filter(rr -> rr.getRequestType().equalsIgnoreCase(CampaignTypeEnum.REFERRAL.getType())).map(ReviewRequestDto::getReviewRequestId)
				.collect(Collectors.toList());
		List<Long> appointmentReminderIds = appointmentReviewRequest.stream().filter(rr -> rr.getRequestType().equalsIgnoreCase(CampaignTypeEnum.APPOINTMENT_REMINDER.getType()))
				.map(ReviewRequestDto::getReviewRequestId).collect(Collectors.toList());
		List<Long> appointmentRecallRequestIds = appointmentReviewRequest.stream().filter(rr -> rr.getRequestType().equalsIgnoreCase(CampaignTypeEnum.APPOINTMENT_RECALL.getType()))
				.map(ReviewRequestDto::getReviewRequestId).collect(Collectors.toList());
		List<Long> appointmentFormRequestIds = appointmentReviewRequest.stream().filter(rr -> rr.getRequestType().equalsIgnoreCase(CampaignTypeEnum.APPOINTMENT_FORM.getType()))
				.map(ReviewRequestDto::getReviewRequestId).collect(Collectors.toList());
		return new RequestIdDTO(rrRequestIds, cxRequestIds, referralRequestIds, promotionRequestIds, appointmentReminderIds, appointmentRecallRequestIds, appointmentFormRequestIds);
	}
	
	private void migrateReviewRequestsToEsIndex(RequestIdDTO request, Integer fromBusinessId) {
		EventMigrationRequest eventMigrationRequest = new EventMigrationRequest();
		eventMigrationRequest.setLocationIds(Arrays.asList(fromBusinessId));
		
		// migrate RR review requests to comm_report ES index
		if (!CollectionUtils.isEmpty(request.getRrRequestIds())) {
			usageCommunicationConsumerService.migrateBusinessUsageReportDataToES(eventMigrationRequest);
		}
		
		// migrating CX review requests to cx_report ES index
		if (!CollectionUtils.isEmpty(request.getCxRequestIds())) {
			usageCommunicationConsumerService.migrateCXCommunicationData(request.getCxRequestIds());
		}
		
		// migrating Promotion requests to promotion_report ES index
		if (!CollectionUtils.isEmpty(request.getPromotionRequestIds())) {
			usageCommunicationConsumerService.migratePromotionCommunicationData(request.getPromotionRequestIds());
		}
		
		// migrate Referral requests to referral_report index
		if (!CollectionUtils.isEmpty(request.getReferralRequestIds())) {
			usageCommunicationConsumerService.migrateReferralCommunicationData(request.getReferralRequestIds());
		}
		
		// migrate Appointment Reminder requests to appointment_reminder index
		if (!CollectionUtils.isEmpty(request.getAppointmentReminderIds())) {
			usageCommunicationConsumerService.migrateAppointmentReminderCommunicationData(request.getAppointmentReminderIds());
		}
		
		// migrate Appointment Recall requests to appointment_recall index
		if (!CollectionUtils.isEmpty(request.getAppointmentRecallRequestIds())) {
			usageCommunicationConsumerService.migrateAppointmentRecallCommunicationData(request.getAppointmentRecallRequestIds());
		}
		
		// migrate Appointment Form requests to appointment_form_report ES index
		if (!CollectionUtils.isEmpty(request.getAppointmentFormRequestIds())) {
			usageCommunicationConsumerService.migrateAppointmentFormCommunicationData(request.getAppointmentFormRequestIds());
		}
	}
	
	@Override
	public void migrateTemplateDataForSMB(List<Integer> businessIds, Integer userId, String emailId, Integer contactUsEnabled, Integer campaignStatus, Integer enableUnsubscribeText, Integer enableMms,
			Integer noReplyEnabled) {
		for (Integer businessId : businessIds) {
			logger.info("Migrating campaign/template data for business : {}", businessId);
			BusinessEnterpriseEntity business = businessReadOnlyRepo.getValidBusinessByBid(businessId);
			// setting bid in MDC since it is used in get email template flow.
			MDC.put("bid", String.valueOf(businessId));
			BusinessTemplateConfig templateConfig = businessTemplateConfigRepo.findFirstByBusinessId(business.getId());
			Integer defaultEmailTemplateId = null;
			Integer defaultSmsTemplateId = null;
			// setting default email template id from template settings. Used while creating default ongoing campaign.
			if (templateConfig != null && templateConfig.getSendReviewEmail() != null && templateConfig.getSendReviewEmail() == 1 && templateConfig.getReviewEmailTemplateId() != null) {
				defaultEmailTemplateId = templateConfig.getReviewEmailTemplateId();
				logger.info("RR Email Template Config is enabled for business {} with emailTemplateId : {}", business.getId(), defaultEmailTemplateId);
			}
			// setting default sms template id from template settings. Used while creating default ongoing campaign.
			if (templateConfig != null && templateConfig.getSendReviewSms() != null && templateConfig.getSendReviewSms() == 1 && templateConfig.getReviewSmsTemplateId() != null) {
				defaultSmsTemplateId = templateConfig.getReviewSmsTemplateId();
				logger.info("RR Sms Template Config is enabled for business {} with smsTemplateId : {}", business.getId(), defaultSmsTemplateId);
			}
			Integer ongoingEmailTemplateId = migrateEmailTemplateData(business, defaultEmailTemplateId, contactUsEnabled, noReplyEnabled);
			
			Integer ongoingSmsTemplateId = migrateSmsTemplateData(business, defaultSmsTemplateId, contactUsEnabled, enableUnsubscribeText, enableMms);
			
			createCampaignSettings(business, business.getEmailId());
			
			createDefaultOngoingCampaign(business, ongoingEmailTemplateId, ongoingSmsTemplateId, userId, campaignStatus, enableUnsubscribeText, enableMms, noReplyEnabled);
			
			enableCampaignFeatureForBusiness(business, userId);
			
			// DNE
			/*
			 * if (defaultEmailTemplateId != null) { NOSONAR // update checkin delay and reminder settings in default ongoing campaign.
			 * updateCheckinDelayReminderSettings(businessId, defaultEmailTemplateId); }
			 */
			
			logger.info("Finished migrating campaign/template data for business : {}", businessId);
		}
	}
	
	@SuppressWarnings("unused")
	private void updateCheckinDelayReminderSettings(Integer businessId, Integer defaultEmailTemplateId) {
		logger.info("Getting checkin delay and reminder settings for smb : {}", businessId);
		List<BusinessEmailTemplate> betList = businessEmailTemplateRepo.getByBusinessIdAndTemplateId(businessId, defaultEmailTemplateId);
		if (CollectionUtils.isNotEmpty(betList)) {
			BusinessEmailTemplate template = betList.get(0);
			Map<String, String> data = new HashMap<>();
			data.put(BID, String.valueOf(businessId));
			data.put(DELAY, String.valueOf(template.getSendMailAfter()));
			data.put(R_COUNT, String.valueOf(template.getMaxReminderCount()));
			data.put(R_FREQUENCY, String.valueOf(template.getReminderFrequency()));
			logger.info("Calling update campaign method to update checkin delay and reminder settings for data : {}", data);
			updateCampaign(data);
		}
		
	}
	
	@Override
	public void migrateEnterpriseToCampaignsV3(List<Integer> enterpriseIds, Integer userId, Integer contactUsEnabled, Integer campaignStatus, Integer enableUnsubscribeText,
			Integer useExistingReviewSources, Integer enableLocationBranding, Integer enableMms, Integer noReplyEnabled) {
		if (CollectionUtils.isEmpty(enterpriseIds) || CollectionUtils.size(enterpriseIds) > 500) {
			logger.info("Either enterprise id {} are empty or more than the 500", enterpriseIds);
			return;
		}
		enterpriseIds.stream().forEach(enterpriseId -> migrateEnterpriseToCampaignsV2(enterpriseId, userId, contactUsEnabled, campaignStatus, enableUnsubscribeText, useExistingReviewSources,
				enableLocationBranding, enableMms, noReplyEnabled));
	}
	
	@Override
	public void migrateEnterpriseToCampaignsV2(Integer enterpriseId, Integer userId, Integer contactUsEnabled, Integer campaignStatus, Integer enableUnsubscribeText, Integer useExistingReviewSources,
			Integer enableLocationBranding, Integer enableMms, Integer noReplyEnabled) {
		try {
			MDC.put("bid", String.valueOf(enterpriseId));
			logger.info("STARTED Migrating campaign/template data for Enterprise : {}", enterpriseId);
			BusinessEnterpriseEntity business = businessReadOnlyRepo.getValidBusinessByBid(enterpriseId);
			
			List<Integer> locationIds = businessReadOnlyRepo.findBusinessIdByBusinessIdOrEnterpriseId(enterpriseId);
			if (CollectionUtils.isEmpty(locationIds)) {
				logger.info("Migrating campaign/template data for Enterprise : Empty location Ids for enterprise {}", enterpriseId);
				return;
			}
			boolean isCampaignFeatureFlagEnabled = isCampaignFeatureFlagPresent(business);
			if (BooleanUtils.isTrue(isCampaignFeatureFlagEnabled)) {
				logger.info("Migrating campaign/template data for Enterprise : Feature flag already present for enterprise {}", enterpriseId);
				return;
			}
			// creating new ongoing campaign with given status.
			CampaignStatusEnum status = CampaignStatusEnum.PAUSED;
			if (campaignStatus != null && campaignStatus.equals(1)) {
				status = CampaignStatusEnum.ACTIVE;
			}
			businessOnboardingService.createDefaultTemplatesAndCampaignsForMigratedBusiness(enterpriseId, userId, status, contactUsEnabled, enableUnsubscribeText, useExistingReviewSources,
					enableLocationBranding, enableMms, noReplyEnabled);
			enableCampaignFeatureForBusiness(business, userId);
			disableOldEmailTemplates(enterpriseId, locationIds);
			disableOldSMSTemplates(enterpriseId, locationIds);
			createCampaignSettingsForEnterprise(business, business.getEmailId());
			
			logger.info("ENDED Migrating campaign/template data for Enterprise : {}", enterpriseId);
		} catch (Exception e) {
			logger.error("Error Migrating Campaign for Enterprise : {} with Error Message {} ", enterpriseId, e.getMessage());
		}
	}
	
	public void disableOldEmailTemplates(Integer enterpriseId, List<Integer> businessIds) {
		logger.info("STARTED SOFT DELETING email templates for enterprise id {} AND location ids {}", enterpriseId, businessIds);
		int rowsAffected = businessEmailTemplateRepo.updateBusinessEmailTemplatesDeletedFlag(businessIds, 1);
		logger.info("ENDED SOFT DELETING email templates for enterprise id {} AND location ids {} :: templates affected {}", enterpriseId, businessIds, rowsAffected);
	}
	
	public void disableOldSMSTemplates(Integer enterpriseId, List<Integer> businessIds) {
		logger.info("STARTED SOFT DELETING sms templates for enterprise id {} AND location ids {}", enterpriseId, businessIds);
		int rowsAffected = businessSMSTemplateRepo.updateBusinessSmsTemplateIsDeletedByBusinessIds(businessIds, 1);
		logger.info("ENDED SOFT DELETING sms templates for enterprise id {} AND location ids {} :: templates affected {}", enterpriseId, businessIds, rowsAffected);
	}
	
	/**
	 * Migrating email template data. Updates email_template, business_email_template tables.
	 * 
	 * @param business
	 * @param defaultEmailTemplateId
	 * @param contactUsEnabled
	 * @param noReplyEnabled
	 * @return
	 */
	private Integer migrateEmailTemplateData(BusinessEnterpriseEntity business, Integer defaultEmailTemplateId, Integer contactUsEnabled, Integer noReplyEnabled) {
		logger.info("Migrating email template data from businessId : {}", business.getId());
		List<BusinessTemplateEntity> businessTemplateEntities = businessEmailTemplateRepo.getByBusinessIdAndIsDeleted(business.getId());
		
		EmailTemplateTypes[] emailTemplateTypes = EmailTemplateTypes.values();
		Map<String, EmailTemplate> emailTemplateByTypeMap = new HashMap<>();
		for (EmailTemplateTypes type : emailTemplateTypes) {
			emailTemplateByTypeMap.put(StringUtils.lowerCase(type.name()), emailTemplateCacheService.getDefaultEmailTemplateByType(StringUtils.lowerCase(type.name())));
		}
		Integer ongoingTemplateId = null;
		for (BusinessTemplateEntity bte : businessTemplateEntities) {
			
			EmailTemplateResponse response = emailTemplateService.getEmailTemplateResponseById(bte.getTemplateId(), bte.getTemplateType());
			response.setSurveyButtonColor(SURVEY_BUTTON_COLOR);
			response.setSurveyButtonTextColor(FFFFFF);
			response.setDefaultReplyToEnabled(1);
			// setting locationBrandingEnabled to 0 for SMB since there is no location.
			response.setLocationBrandingEnabled(0);
			if (contactUsEnabled != null) {
				response.setContactUsEnabled(contactUsEnabled);
			}
			if (noReplyEnabled != null) {
				response.setDefaultReplyToEnabled(noReplyEnabled);
			}
			EmailTemplate defaultEmailTemplate = emailTemplateByTypeMap.get(bte.getTemplateType());
			if (defaultEmailTemplate != null) {
				response.setReviewSiteButtonColor(defaultEmailTemplate.getReviewSiteButtonColor());
				response.setReviewSiteButtonTextColor(defaultEmailTemplate.getReviewSiteButtonTextColor());
				response.setFeedbackCallbackMessage(defaultEmailTemplate.getFeedbackMessage());
				response.setFeedbackCallbackEnabled(defaultEmailTemplate.getFeedbackShowCallbackOption());
				response.setFeedbackCheckboxEnabled(defaultEmailTemplate.getFeedbackDefaultCheckboxChecked());
				response.setThankYouHeading(defaultEmailTemplate.getThankyouHeading());
				response.setThankYouMessage(defaultEmailTemplate.getThankyouMessage());
				response.setSignature(defaultEmailTemplate.getSignature());
			}
			if (bte.getUserId() != null) {
				MDC.put("uid", bte.getUserId().toString());
			}
			// creating new email template
			EditTemplateResponse templateResponse = emailTemplateService.createOrUpdateBusinessEmailTemplate(0, business.getId(), bte.getTemplateType(), response);
			logger.info("######################## Created new email template with id : {} from templateId : {} ########################", templateResponse.getId(), bte.getTemplateId());
			// setting isDeleted flag to true for migrated templates.
			businessEmailTemplateRepo.updateBusinessEmailTemplateDeletedFlag(business.getId(), bte.getTemplateId(), 1);
			if (defaultEmailTemplateId != null && defaultEmailTemplateId.equals(bte.getTemplateId())) {
				ongoingTemplateId = templateResponse.getId();
			}
		}
		logger.info("Finished migrating email template data from businessId : {}", business.getId());
		return ongoingTemplateId;
	}
	
	/**
	 * Migrating sms template data. Updates business_sms_template table.
	 * 
	 * @param business
	 * @param defaultSmsTemplateId
	 * @param contactUsEnabled
	 * @param enableUnsubscribeText
	 * @param enableMms
	 * @return
	 */
	private Integer migrateSmsTemplateData(BusinessEnterpriseEntity business, Integer defaultSmsTemplateId, Integer contactUsEnabled, Integer enableUnsubscribeText, Integer enableMms) {
		logger.info("Migrating sms template data from businessId : {}", business.getId());
		Set<String> smsTemplatesTypes = new HashSet<>();
		smsTemplatesTypes.add(TemplateTypeEnum.REVIEW_REQUEST_SMS.getName());
		smsTemplatesTypes.add(TemplateTypeEnum.CUSTOMER_EXPERIENCE_SMS.getName());
		smsTemplatesTypes.add(TemplateTypeEnum.SURVEY_REQUEST_SMS.getName());
		
		List<BusinessSmsTemplate> defaultBusinessSmsTemplates = businessSMSTemplateRepo.getDefaultBusinessSmsTemplates(smsTemplatesTypes);
		Map<String, BusinessSmsTemplate> defaultSmsTemplatesMap = new HashMap<>();
		for (BusinessSmsTemplate dbst : defaultBusinessSmsTemplates) {
			defaultSmsTemplatesMap.put(dbst.getType(), dbst);
		}
		Integer ongoingTemplateId = null;
		List<BusinessSmsTemplate> businessSmsTemplates = businessSMSTemplateRepo.getByBusinessIdAndIsDeleted(business.getId());
		for (BusinessSmsTemplate bst : businessSmsTemplates) {
			
			GenericCampaignSmsTemplateResponse genericSmsTemplateResponse = templateService.getSMSTemplate(business.getId(), bst.getId(), bst.getType(), Constants.IS_GLOBAL_TEMPLATE);
			BusinessSmsTemplate defaultBusinessSmsTemplate = defaultSmsTemplatesMap.get(bst.getType());
			if (defaultBusinessSmsTemplate != null) {
				genericSmsTemplateResponse.setFeedbackCallbackEnabled(defaultBusinessSmsTemplate.getEnableFeedbackMessage());
				genericSmsTemplateResponse.setFeedbackCallbackMessage(defaultBusinessSmsTemplate.getFeedbackCallbackMessage());
				genericSmsTemplateResponse.setFeedbackCheckboxEnabled(defaultBusinessSmsTemplate.getEnableFeedbackCheckbox());
				genericSmsTemplateResponse.setThankYouHeading(defaultBusinessSmsTemplate.getThankyouHeading());
				genericSmsTemplateResponse.setThankYouMessage(defaultBusinessSmsTemplate.getThankyouMessage());
				genericSmsTemplateResponse.setReviewSiteButtonColor(defaultBusinessSmsTemplate.getReviewSiteButtonColor());
				genericSmsTemplateResponse.setReviewSiteButtonTextColor(defaultBusinessSmsTemplate.getReviewSiteButtonTextColor());
				genericSmsTemplateResponse.setReviewEnabled(defaultBusinessSmsTemplate.getReviewEnable());
			}
			// setting locationBrandingEnabled to 0 for SMB since there is no location.
			genericSmsTemplateResponse.setLocationBrandingEnabled(0);
			if (contactUsEnabled != null) {
				genericSmsTemplateResponse.setContactUsEnabled(contactUsEnabled);
			}
			if (enableUnsubscribeText != null) {
				genericSmsTemplateResponse.setIncludeUnsubscribeText(enableUnsubscribeText);
			}
			// BIRDEYE-55798
			if (enableMms != null) {
				genericSmsTemplateResponse.setIncludeImageWithText(enableMms);
			}
			
			// creating new sms template
			BusinessTemplateResponse templateResponse = templateService.createCampaignSmsTemplate(business.getId(), bst.getCreatedBy(),
					new CampaignSmsTemplateSRO(genericSmsTemplateResponse));
			logger.info("######################## Created new sms template with id : {} from templateId : {} ########################", templateResponse.getId(), bst.getId());
			// setting isDeleted flag to true for migrated templates.
			businessSMSTemplateRepo.updateBusinessSmsTemplateIsDeleted(bst.getId(), 1);
			if (defaultSmsTemplateId != null && defaultSmsTemplateId.equals(bst.getId())) {
				ongoingTemplateId = templateResponse.getId();
			}
		}
		logger.info("Finished migrating sms template data from businessId : {}", business.getId());
		return ongoingTemplateId;
	}
	
	/**
	 * Updates campaign_account_settings table.
	 * 
	 * @param business
	 * @param emailId
	 */
	private void createCampaignSettings(BusinessEnterpriseEntity business, String emailId) {
		logger.info("Migrating campaign settings from businessId : {}", business.getId());
		CampaignAccountSettings settings = new CampaignAccountSettings();
		settings.setAccountId(business.getId());
		settings.setMailResendFrequency(business.getMailResendFrequency());
		String birdeyeAsReview = businessOptionService.getBirdeyeAsReview(business);
		if (birdeyeAsReview != null) {
			settings.setIsDirectGoogleReviews(Integer.parseInt(birdeyeAsReview));
		}
		settings.setUpdatedBy(emailId);
		campaignAccountSettionRepo.saveAndFlush(settings);
		
	}
	
	/**
	 * Updates campaign_account_settings table.
	 * 
	 * @param business
	 * @param emailId
	 */
	private void createCampaignSettingsForEnterprise(BusinessEnterpriseEntity business, String emailId) {
		logger.info("STARTED creating campaign settings from enterprise id : {}", business.getId());
		List<CampaignAccountSettings> accountSettings = campaignAccountSettionRepo.getCampaignAccountSettingsByAccountId(business.getId());
		if (CollectionUtils.isNotEmpty(accountSettings)) {
			logger.info("Campaign settings found forenterprise id : {}", business.getId());
			return;
		}
		// Create new setting.
		CampaignAccountSettings settings = new CampaignAccountSettings();
		settings.setAccountId(business.getId());
		settings.setMailResendFrequency(30);
		settings.setIsDirectGoogleReviews(1);
		settings.setUpdatedBy(emailId);
		campaignAccountSettionRepo.saveAndFlush(settings);
		logger.info("ENDED creating campaign settings from enterprise id : {}", business.getId());
	}
	
	/**
	 * 
	 * @param business
	 * @param ongoingEmailTemplateId
	 * @param ongoingSmsTemplateId
	 * @param userId
	 * @param campaignStatus
	 * @param enableUnsubscribeText
	 * @param enableMms
	 * @param noReplyEnabled
	 */
	private void createDefaultOngoingCampaign(BusinessEnterpriseEntity business, Integer ongoingEmailTemplateId, Integer ongoingSmsTemplateId, Integer userId, Integer campaignStatus,
			Integer enableUnsubscribeText, Integer enableMms, Integer noReplyEnabled) { // NOSONAR
		logger.info("Creating default ongoing campaign for businessId : {} with emailTemplateId : {} and smsTemplateId : {}", business.getId(), ongoingEmailTemplateId, ongoingSmsTemplateId);
		if (ongoingEmailTemplateId == null) {
			Pageable pageable = PageRequest.of(0, 1, Sort.by("updatedAt").descending());
			Page<BusinessTemplateEntity> page = businessEmailTemplateRepo.getLastUpdatedTemplateByEnterpriseIdAndType(business.getId(), TemplateTypeEnum.REVIEW_REQUEST_NEW.getName(), pageable);
			List<BusinessTemplateEntity> content = page.getContent();
			if (CollectionUtils.isNotEmpty(content)) {
				ongoingEmailTemplateId = content.get(0).getTemplateId();
			}
		}
		if (ongoingEmailTemplateId == null) {
			EditTemplateResponse emailResponse = emailTemplateService.createDefaultBusinessEmailTemplate(business.getId());
			ongoingEmailTemplateId = emailResponse.getId();
			// updating noReply enabled flag for migration. BIRDEYE-56881
			if (noReplyEnabled != null) {
				campaignSetupService.updateNoReplyEnabledFlagForTemplates(emailResponse.getId(), noReplyEnabled);
			}
		}
		if (ongoingSmsTemplateId == null) {
			Pageable pageable = PageRequest.of(0, 1, Sort.by("updatedAt").descending());
			Page<BusinessSmsTemplate> page = businessSMSTemplateRepo.getLastUpdatedTemplateByEnterpriseIdAndType(business.getId(), TemplateTypeEnum.REVIEW_REQUEST_SMS.getName(), pageable);
			List<BusinessSmsTemplate> content = page.getContent();
			if (CollectionUtils.isNotEmpty(content)) {
				ongoingSmsTemplateId = content.get(0).getId();
			}
		}
		if (ongoingSmsTemplateId == null) {
			BusinessTemplateResponse smsResponse = templateService.createDefaultSMSTemplateForABusiness(business.getId(), userId);
			ongoingSmsTemplateId = smsResponse.getId();
			// setting enableUnsubscribeText flag for SMS template
			if (enableUnsubscribeText != null) {
				campaignSetupService.updateUnsubscribeTextEnabledFlagForTemplates(ongoingSmsTemplateId, enableUnsubscribeText);
			}
			// BIRDEYE-55798
			if (enableMms != null) {
				campaignSetupService.updateMmsEnabledFlagForTemplates(ongoingSmsTemplateId, enableMms);
			}
		}
		if (ongoingEmailTemplateId != null && ongoingSmsTemplateId != null) {
			logger.info("Creating default ongoing campaign for email template {} and sms template {}", ongoingEmailTemplateId, ongoingSmsTemplateId);
			// safe check
			// if default ongoing is already exists on enterprise id we will ignore the new migration
			List<Campaign> existingCampaigns = campaignRepo.getDefaultOngoingCampaignByEnterpriseId(business.getId());
			logger.info("Existing default campaign count for enterprise id {} is {}", business.getId(), (existingCampaigns != null ? existingCampaigns.size() : 0));
			if (CollectionUtils.isEmpty(existingCampaigns)) {
				CampaignStatusEnum status = CampaignStatusEnum.PAUSED;
				if (campaignStatus != null && campaignStatus.equals(1)) {
					status = CampaignStatusEnum.ACTIVE;
				}
				// creating email only type of default ongoing campaign if SMS is not enabled for this business.
				if (isNotSMSEnabledForSMB(business)) {
					campaignSetupService.createDefaultOnGoingCampaign(business.getId(), business.getAlias1(), userId, ongoingEmailTemplateId, null, status, CampaignPriorityEnum.EMAIL.getType());
				} else {
					campaignSetupService.createDefaultOnGoingCampaign(business.getId(), business.getAlias1(), userId, ongoingEmailTemplateId, ongoingSmsTemplateId, status,
							CampaignPriorityEnum.EMAIL_AND_SMS.getType());
				}
			} else {
				logger.info("For enterprise id :: {} existing default campaign :: {} ", business.getId(), (existingCampaigns.get(0) != null ? existingCampaigns.get(0).getId() : null));
			}
		}
	}
	
	private boolean isNotSMSEnabledForSMB(BusinessEnterpriseEntity business) {
		return (business.getSmsEnabled() != null && business.getSmsEnabled().equals(0)) || isNotSmsOpted(business);
	}
	
	private boolean isNotSmsOpted(BusinessEnterpriseEntity business) {
		Integer smsOpted = businessOptionService.getSmsOptedForSmb(business);
		return smsOpted != null && smsOpted.equals(0);
	}
	
	private boolean isCampaignFeatureFlagPresent(BusinessEnterpriseEntity business) {
		AccountFeatures accountFeatures = accountFeaturesRepo.findFirstByBusinessId(business.getBusinessId());
		return accountFeatures != null && accountFeatures.getCampaignEnabled().equals(1);
	}
	
	/**
	 * Enabling campaign flag in account_features for given business.
	 * 
	 * @param business
	 * @param userId
	 */
	private void enableCampaignFeatureForBusiness(BusinessEnterpriseEntity business, Integer userId) {
		logger.info("Adding campaign_enabled flag in account_features table for business : {} business number {} and user : {}", business.getId(), business.getBusinessId(), userId);
		AccountFeatures accountFeatures = accountFeaturesRepo.findFirstByBusinessId(business.getBusinessId());
		if (accountFeatures == null) {
			accountFeatures = new AccountFeatures(business.getBusinessId(), userId, 1);
			accountFeatures.setCreatedAt(new Date());
		}
		accountFeatures.setCampaignEnabled(1);
		accountFeatures.setUpdatedAt(new Date());
		accountFeaturesRepo.saveAndFlush(accountFeatures);
		logger.info("Added campaign_enabled flag in account_features table with id {} for business : {} and user : {}", accountFeatures.getId(), business.getId(), userId);
	}
	
	@SuppressWarnings("unused")
	private boolean isInvalidSMBMigrationRequest(Integer businessId, BusinessEnterpriseEntity business) {
		
		if (!isValidSMBDirectBusiness(business)) {
			logger.info("Business is not valid SMB : {}", businessId);
			return true;
		}
		if (isCampaignFlagEnableForBusiness(business)) {
			logger.info("Templates/Campaign data has already been migrated for business : {}", businessId);
			return true;
		}
		return StringUtils.equalsAnyIgnoreCase(business.getType(), BusinessTypeEnum.BUSINESS.getBusinessType(), BusinessTypeEnum.PRODUCT.getBusinessType());
	}
	
	private boolean isValidSMBDirectBusiness(BusinessEnterpriseEntity business) {
		if (business == null || StringUtils.isBlank(business.getType()) || StringUtils.isBlank(business.getAccountType()) || (business.getClosed() != null && business.getClosed().intValue() == 1)
				|| business.getEnterpriseId() != null) {
			return false;
		}
		return StringUtils.equalsAnyIgnoreCase(business.getType(), BusinessTypeEnum.BUSINESS.getBusinessType(), BusinessTypeEnum.PRODUCT.getBusinessType())
				
				&& StringUtils.equalsIgnoreCase(business.getAccountType(), BusinessAccountTypeEnum.DIRECT.getType());
	}
	
	@SuppressWarnings("unused")
	private boolean isValidDirectOrCobrandedEnterprise(BusinessEnterpriseEntity business) {
		if (business == null || StringUtils.isBlank(business.getType()) || StringUtils.isBlank(business.getAccountType()) || (business.getClosed() != null && business.getClosed().intValue() == 1)) {
			return false;
		}
		return StringUtils.equalsAnyIgnoreCase(business.getType(), BusinessTypeEnum.ENTERPRISE_LOCATION.getBusinessType(), BusinessTypeEnum.ENTERPRISE_PRODUCT.getBusinessType())
				&& (StringUtils.equalsIgnoreCase(business.getAccountType(), BusinessAccountTypeEnum.DIRECT.getType())
						|| StringUtils.equalsIgnoreCase(business.getAccountType(), BusinessAccountTypeEnum.COBRANDED.getType()));
	}
	
	private boolean isCampaignFlagEnableForBusiness(BusinessEnterpriseEntity business) {
		logger.info("Checking the campaign flag for businessId {}", business.getId());
		boolean campaignFlag = false;
		try {
			AccountFeatures accountFeatures = accountFeaturesRepo.findFirstByBusinessId(business.getBusinessId());
			if (accountFeatures != null) {
				campaignFlag = (accountFeatures.getCampaignEnabled() == 1);
			}
			logger.info("Campaign flag for businessId {} is {}", business.getId(), campaignFlag);
		} catch (Exception exe) {
			logger.error("Error while checking the campaign flag for a business {} error :: {} ", business.getId(), exe);
		}
		return campaignFlag;
	}
	
	@Override
	public void migrateTemplateDataForEnterprise(EnterpriseTemplatesMigrationRO request, Integer userId) {
		
		BusinessEnterpriseEntity business = businessReadOnlyRepo.getValidBusinessByBid(request.getEnterpriseId());
		if (!isValidEntTemplatesMigrationRequest(business, request)) {
			return;
		}
		
		MDC.put("bid", String.valueOf(business.getId()));
		List<Integer> locationIds = businessReadOnlyRepo.findEnterpriseLocations(business.getId());
		
		// creating new ongoing campaign with given status.
		createDefaultOngoingCampaignForMigratedEnterprise(request, userId);
		
		Integer defaultEmailTemplateId = null;
		Integer defaultSmsTemplateId = null;
		// setting default email template id from template settings. Used while creating default ongoing campaign.
		if (request.getOngoingEmailTemplateId() != null) {
			defaultEmailTemplateId = request.getOngoingEmailTemplateId();
			logger.info("RR Email Template Config is enabled for business {} with emailTemplateId : {}", business.getId(), defaultEmailTemplateId);
		}
		// setting default sms template id from template settings. Used while creating default ongoing campaign.
		if (request.getOngoingSmsTemplateId() != null) {
			defaultSmsTemplateId = request.getOngoingSmsTemplateId();
			logger.info("RR Sms Template Config is enabled for business {} with smsTemplateId : {}", business.getId(), defaultSmsTemplateId);
		}
		BusinessTemplateEntity ongoingEmailTemplate = migrateEmailTemplatesForEnterprise(business, request.getEmailTemplateIds(), locationIds, userId, defaultEmailTemplateId, request);
		
		BusinessTemplateEntity ongoingSmsTemplate = migrateSmsTemplatesForEnterprise(business, request.getSmsTemplateIds(), locationIds, userId, defaultSmsTemplateId, request);
		
		createOngoingCampaignForEnterprise(business, ongoingEmailTemplate, ongoingSmsTemplate, userId, request);
		
		createCampaignSettingsForEnterprise(business, business.getEmailId());
		
		enableCampaignFeatureForBusiness(business, userId);
		
	}
	
	@Override
	public void migrateTemplateOnlyDataForEnterprise(EnterpriseTemplatesMigrationBasicRO inputRequest, Integer userId) {
		
		// convert the object to new Object
		EnterpriseTemplatesMigrationRO request = new EnterpriseTemplatesMigrationRO();
		request.setEnterpriseId(inputRequest.getEnterpriseId());
		request.setEmailTemplateIds(inputRequest.getEmailTemplateIds());
		request.setSmsTemplateIds(inputRequest.getSmsTemplateIds());
		// rest flow
		BusinessEnterpriseEntity business = businessReadOnlyRepo.getValidBusinessByBid(request.getEnterpriseId());
		if (!isValidEntTemplatesMigrationRequest(business, request)) {
			return;
		}
		if (!isCampaignFeatureFlagPresent(business)) {
			throw new CampaignHTTPException(HttpStatus.BAD_REQUEST, "This business has not been migrated to Campaign 2.0");
		}
		
		MDC.put("bid", String.valueOf(business.getId()));
		List<Integer> locationIds = businessReadOnlyRepo.findEnterpriseLocations(business.getId());
		
		migrateEmailTemplatesOnlyForEnterprise(business, request.getEmailTemplateIds(), locationIds, userId, null, request);
		
		migrateSmsTemplatesOnlyForEnterprise(business, request.getSmsTemplateIds(), locationIds, userId, null, request);
		
	}
	
	@Override
	public void prepareRequestDateMigrationBatch() {
		Long offsetId = prepareRequestMigrationOffset(MigrationEventTypeEnum.REQUEST_DATE_MIGRATION_BATCH_EVENT.getEvent());
		reviewRequestService.updateRequestDateMigrationOffset(offsetId);
	}
	
	@Override
	public void prepareSegmentCountMigrationBatch() {
		Long offSetId = prepareRequestMigrationOffset(MigrationEventTypeEnum.SEGMENT_COUNT_MIGRATION_BATCH_EVENT.getEvent());
		reviewRequestService.updateSegmentCountMigrationOffset(offSetId);
	}
	
	private Long prepareRequestMigrationOffset(String migrationEvent) {
		String cachedOffsetId = null;
		Long offsetId = 0l;
		Integer pageSize = null;
		Integer totalPages = null;
		KafkaTopicTypeEnum kafkaTopic = null;
		MigrationEventTypeEnum event = MigrationEventTypeEnum.getEnum(migrationEvent);
		switch (event) {
			case REQUEST_DATE_MIGRATION_BATCH_EVENT:
				cachedOffsetId = reviewRequestService.getRequestDateMigrationOffset();
				if (StringUtils.isNotEmpty(cachedOffsetId)) {
					offsetId = Long.valueOf(cachedOffsetId);
				}
				logger.info("[prepareRequestMigrationOffset] : fetched offset id {} for event : {}", offsetId, event.getEvent());
				pageSize = Integer.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(RR_DATE_MIGRATION_BATCH_SIZE, "10000"));
				totalPages = Integer.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(RR_DATE_MIGRATION_BATCH_SPLIT, "100"));
				logger.info("[prepareRequestMigrationOffset] : page size - {} and total pages {} for event : {}", pageSize, totalPages, event.getEvent());
				kafkaTopic = KafkaTopicTypeEnum.REQUEST_DATE_MIGRATION_BATCH_EVENT;
				break;
			case SEGMENT_COUNT_MIGRATION_BATCH_EVENT:
				cachedOffsetId = reviewRequestService.getSegmentCountOffsetId();
				if (StringUtils.isNotEmpty(cachedOffsetId)) {
					offsetId = Long.valueOf(cachedOffsetId);
				}
				logger.info("[prepareRequestMigrationOffset] : fetched offset id {} for event : {}", offsetId, event.getEvent());
				pageSize = Integer.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(SEGMENT_COUNT_MIGRATION_BATCH_SIZE, "10000"));
				totalPages = Integer.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(SEGMENT_COUNT_MIGRATION_BATCH_SPLIT, "100"));
				logger.info("[prepareRequestMigrationOffset] : page size - {} and total pages {} for event : {}", pageSize, totalPages, event.getEvent());
				kafkaTopic = KafkaTopicTypeEnum.SEGMENT_COUNT_MIGRATION_BATCH_EVENT;
				break;
			default:
				break;
		}
		List<KafkaMessage> kafkaMessages = new ArrayList<>();
		for (int i = 0; i < totalPages; i++) {
			kafkaMessages.add(new KafkaMessage(new MigrationRequest(offsetId + 1, offsetId + pageSize)));
			offsetId += pageSize;
		}
		logger.info("[prepareRequestMigrationOffset] : updated offsetId {}", offsetId);
		kafkaService.pushMessagesListToKafka(kafkaTopic, kafkaMessages);
		return offsetId;
	}
	
	@Override
	public void executeRequestDateMigrationBatch(MigrationRequest requestDateMigrationRequest) {
		logger.info("[executeRequestDateMigrationBatch] : starting execution for startID {} and endID {}", requestDateMigrationRequest.getStartId(), requestDateMigrationRequest.getEndId());
		List<ReviewRequest> reviewRequests = reviewRequestRepo.getReviewRequestsBetweenRange(requestDateMigrationRequest.getStartId(), requestDateMigrationRequest.getEndId());
		if (CollectionUtils.isEmpty(reviewRequests)) {
			logger.info("no requests found between startID {} and endID {}", requestDateMigrationRequest.getStartId(), requestDateMigrationRequest.getEndId());
			return;
		}
		
		EnumMap<CampaignTypeEnum, List<ReviewRequest>> reqTypeByRequestsMap = new EnumMap<CampaignTypeEnum, List<ReviewRequest>>(CampaignTypeEnum.class);
		for (ReviewRequest rr : reviewRequests) {
			CampaignTypeEnum campaignType = CampaignTypeEnum.getSupportedType(rr.getRequestType());
			if (campaignType == null) {
				campaignType = CampaignTypeEnum.REVIEW_REQUEST;
				if (rr.getSurveyId() != null)
					campaignType = CampaignTypeEnum.SURVEY_REQUEST;
			}
			reqTypeByRequestsMap.putIfAbsent(campaignType, new ArrayList<>());
			reqTypeByRequestsMap.get(campaignType).add(rr);
		}
		
		for (CampaignTypeEnum reqType : reqTypeByRequestsMap.keySet()) {
			switch (reqType) {
				case REVIEW_REQUEST:
					requestDateMigrationService.migrateRRCommDateInBatch(reqTypeByRequestsMap.get(reqType));
					break;
				case CX_REQUEST:
					requestDateMigrationService.migrateCXCommDateInBatch(reqTypeByRequestsMap.get(reqType));
					break;
				case SURVEY_REQUEST:
					requestDateMigrationService.migrateSurveyCommDateInBatch(reqTypeByRequestsMap.get(reqType));
					break;
				case REFERRAL:
					requestDateMigrationService.migrateReferralCommDateInBatch(reqTypeByRequestsMap.get(reqType));
					break;
				default:
					break;
			}
		}
		
		createAudit(requestDateMigrationRequest, reviewRequests.size());
		logger.info("[executeRequestDateMigrationBatch] : finished execution for startID {} and endID {}", requestDateMigrationRequest.getStartId(), requestDateMigrationRequest.getEndId());
	}
	
	/**
	 * Exceute referral code migration
	 */
	@Override
	public void executeReferralCodeMigrationBatch(Long startRequestId) {
		logger.info("[executeRequestCodeMigrationBatch] : starting execution from startID {} and batch size 10000", startRequestId);
		// preparing token map
		Map<String, Object> tokenMap = new HashMap<>();
		tokenMap.put("startId", String.valueOf(startRequestId));
		
		ElasticSearchBaseRequest request = elasticSearchHelperService.prepareElasticSearchRequest(null, tokenMap, ElasticQueryTemplateEnum.REFERRAL_RANGE.getQueryName(),
				Constants.REFERRAL_REPORT_INDEX, Constants.REFERRAL_REPORT_TYPE);
		SearchResponse searchresponse = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.REFERRAL_REPORT_INDEX).execute(request);
		SearchHits searchHits = searchresponse.getHits();
		if (searchHits == null || searchHits.getHits() == null || searchHits.getHits().length == 0) {
			logger.error("No referral records found for request {} and batch size 10000", startRequestId);
			return;
		}
		
		List<ReferralCodeMigrationESResponse> referralRequests = new ArrayList<>();
		SearchHit[] referralMessages = searchHits.getHits();
		for (SearchHit message : referralMessages) {
			ReferralCodeMigrationESResponse referralResponse = objectMapper.convertValue(message.getSourceAsMap(), ReferralCodeMigrationESResponse.class);
			referralRequests.add(referralResponse);
		}
		Long endRequestId = referralRequests.get(referralRequests.size() - 1).getRequestId();
		reviewRequestService.updateReferralCodeMigrationOffset(endRequestId);
		
		createReferralCodeMigrationAudit(startRequestId, endRequestId, referralRequests.size());
		
		kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.REFERRAL_CODE_MIGRATION_BATCH_EVENT,
				new KafkaMessage(new MigrationRequest(referralRequests.get(referralRequests.size() - 1).getRequestId() + 1, null)));
		
		referralCodeMigrationService.migrateReferralCodeInBatch(referralRequests);
		logger.info("[executeRequestCodeMigrationBatch] : finished execution for startID {} and endID {}", startRequestId, endRequestId);
	}


	private void createAudit(MigrationRequest requestDateMigrationRequest, Integer batchSize) {
		RequestDateMigrationAudit audit = new RequestDateMigrationAudit();
		audit.setStartRequestId(requestDateMigrationRequest.getStartId());
		audit.setEndRequestId(requestDateMigrationRequest.getEndId());
		audit.setStatus(1);
		audit.setBatchSize(batchSize);
		requestDateMigrationAuditRepo.saveAndFlush(audit);
	}
	
	private void createReferralCodeMigrationAudit(Long startId, Long endId, Integer batchSize) {
		ReferralCodeMigrationAudit audit = new ReferralCodeMigrationAudit();
		audit.setStartRequestId(startId);
		audit.setEndRequestId(endId);
		audit.setStatus(1);
		audit.setBatchSize(batchSize);
		referralCodeMigrationAuditRepo.saveAndFlush(audit);
	}
	
	private void createDefaultOngoingCampaignForMigratedEnterprise(EnterpriseTemplatesMigrationRO request, Integer userId) {
		// setting ongoing campaign status to PAUSED if status is not passed in request.
		if (request.getCampaignStatus() == null || !request.getCampaignStatus().equals(1)) {
			request.setCampaignStatus(CampaignStatusEnum.PAUSED.getStatus());
		}
		// if default ongoing campaign already exists, then we skip the new default campaign creation.
		List<Campaign> defaultOngoingCampaigns = campaignRepo.getDefaultOngoingCampaignByEnterpriseId(request.getEnterpriseId());
		if (CollectionUtils.isEmpty(defaultOngoingCampaigns)) {
			businessOnboardingService.createDefaultTemplatesAndCampaignsForMigratedBusiness(request.getEnterpriseId(), userId, CampaignStatusEnum.getStatus(request.getCampaignStatus()),
					request.getContactUsEnabled(), request.getEnableUnsubscribeText(), null, request.getEnableLocationBranding(), request.getEnableMms(), request.getNoReplyEnabled());
		}
	}
	
	private void createOngoingCampaignForEnterprise(BusinessEnterpriseEntity business, BusinessTemplateEntity ongoingEmailTemplate, BusinessTemplateEntity ongoingSmsTemplate, Integer userId,
			EnterpriseTemplatesMigrationRO request) {
		
		if (ongoingEmailTemplate != null && ongoingSmsTemplate != null) {
			if (!CampaignUtils.isSameTemplatesType(ongoingEmailTemplate.getTemplateType(), ongoingSmsTemplate.getTemplateType())) {
				logger.info("No ongoing campaign will be created since email and sms templates are of different types.");
			} else {
				// create ongoing campaign with email_and_sms priority.
				campaignSetupService.createOnGoingCampaignForMigration(business.getId(), userId, ongoingEmailTemplate.getTemplateId(), ongoingSmsTemplate.getTemplateId(),
						CampaignStatusEnum.getStatus(request.getCampaignStatus()), CampaignPriorityEnum.EMAIL_AND_SMS.getType(),
						CampaignUtils.getCampaignTypeByTemplateType(CampaignUtils.getTemplateTypeEnumByEmailTemplateType(ongoingEmailTemplate.getTemplateType())).getType(), request.getSurveyId(),
						Collections.emptyList(), StringUtils.EMPTY);
			}
		} else if (ongoingEmailTemplate != null) {
			// create ongoing campaign with email only priority.
			campaignSetupService.createOnGoingCampaignForMigration(business.getId(), userId, ongoingEmailTemplate.getTemplateId(), null, CampaignStatusEnum.getStatus(request.getCampaignStatus()),
					CampaignPriorityEnum.EMAIL.getType(),
					CampaignUtils.getCampaignTypeByTemplateType(CampaignUtils.getTemplateTypeEnumByEmailTemplateType(ongoingEmailTemplate.getTemplateType())).getType(), request.getSurveyId(),
					Collections.emptyList(), StringUtils.EMPTY);
		} else if (ongoingSmsTemplate != null) {
			// create ongoing campaign with sms only priority.
			campaignSetupService.createOnGoingCampaignForMigration(business.getId(), userId, null, ongoingSmsTemplate.getTemplateId(), CampaignStatusEnum.getStatus(request.getCampaignStatus()),
					CampaignPriorityEnum.SMS.getType(), CampaignUtils.getCampaignTypeByTemplateType(CampaignUtils.getTemplateTypeEnumBySmsTemplateType(ongoingSmsTemplate.getTemplateType())).getType(),
					request.getSurveyId(), Collections.emptyList(), StringUtils.EMPTY);
		}
		
	}
	
	/**
	 * Migrate the given list of email templates to Campaign V2.
	 * 
	 * @param business
	 * @param emailTemplateIds
	 * @param locationIds
	 * @param userId
	 */
	private BusinessTemplateEntity migrateEmailTemplatesForEnterprise(BusinessEnterpriseEntity business, Set<Integer> emailTemplateIds, List<Integer> locationIds, Integer userId,
			Integer defaultEmailTemplateId, EnterpriseTemplatesMigrationRO request) {
		logger.info("Started migrating email templates for enterprise {}", business.getId());
		if (CollectionUtils.isEmpty(emailTemplateIds)) {
			return null;
		}
		List<BusinessTemplateEntity> businessTemplateEntities = businessEmailTemplateRepo.getByBusinessIdsAndTemplateIdsAndIsDeleted(locationIds, emailTemplateIds);
		Map<Integer, BusinessTemplateEntity> bTEMap = businessTemplateEntities.stream()
				.collect(Collectors.toMap(BusinessTemplateEntity::getTemplateId, businessTemplateEntity -> businessTemplateEntity, (first, second) -> second));
		
		EmailTemplateTypes[] emailTemplateTypes = EmailTemplateTypes.values();
		Map<String, EmailTemplate> emailTemplateByTypeMap = new HashMap<>();
		for (EmailTemplateTypes type : emailTemplateTypes) {
			emailTemplateByTypeMap.put(StringUtils.lowerCase(type.name()), emailTemplateCacheService.getDefaultEmailTemplateByType(StringUtils.lowerCase(type.name())));
		}
		BusinessTemplateEntity ongoingEmailTemplate = null;
		for (BusinessTemplateEntity bte : bTEMap.values()) {
			logger.info("Migrating email template : {} for enterprise {}", bte.getTemplateId(), business.getId());
			EmailTemplateResponse response = emailTemplateService.getEmailTemplateResponseById(bte.getTemplateId(), bte.getTemplateType());
			response.setSurveyButtonColor(SURVEY_BUTTON_COLOR);
			response.setSurveyButtonTextColor(FFFFFF);
			response.setDefaultReplyToEnabled(1);
			// enabling location level branding.
			response.setLocationBrandingEnabled(0);
			if (request.getContactUsEnabled() != null) {
				response.setContactUsEnabled(request.getContactUsEnabled());
			}
			if (request.getEnableLocationBranding() != null) {
				response.setLocationBrandingEnabled(request.getEnableLocationBranding());
			}
			if (request.getNoReplyEnabled() != null) {
				response.setDefaultReplyToEnabled(request.getNoReplyEnabled());
			}
			EmailTemplate defaultEmailTemplate = emailTemplateByTypeMap.get(bte.getTemplateType());
			if (defaultEmailTemplate != null) {
				response.setReviewSiteButtonColor(defaultEmailTemplate.getReviewSiteButtonColor());
				response.setReviewSiteButtonTextColor(defaultEmailTemplate.getReviewSiteButtonTextColor());
				response.setFeedbackCallbackMessage(defaultEmailTemplate.getFeedbackMessage());
				response.setFeedbackCallbackEnabled(defaultEmailTemplate.getFeedbackShowCallbackOption());
				response.setFeedbackCheckboxEnabled(defaultEmailTemplate.getFeedbackDefaultCheckboxChecked());
				response.setThankYouHeading(defaultEmailTemplate.getThankyouHeading());
				response.setThankYouMessage(defaultEmailTemplate.getThankyouMessage());
				response.setSignature(defaultEmailTemplate.getSignature());
			}
			if (bte.getUserId() != null) {
				MDC.put("uid", bte.getUserId().toString());
			}
			// creating new email template
			EditTemplateResponse templateResponse = emailTemplateService.createOrUpdateBusinessEmailTemplate(0, business.getId(), bte.getTemplateType(), response);
			logger.info("######################## Created new email template with id : {} from templateId : {} for enterprise {} ########################", templateResponse.getId(),
					bte.getTemplateId(), business.getId());
			// setting isDeleted flag to true for migrated templates.
			businessEmailTemplateRepo.updateBusinessEmailTemplateDeletedFlag(bte.getBusinessId(), bte.getTemplateId(), 1);
			
			emailTemplateIds.remove(bte.getTemplateId());
			// creating audit for migrated template
			migratedTemplatesAuditRepo.saveAndFlush(new MigratedTemplatesAudit(business.getId(), bte.getBusinessId(), bte.getTemplateId(), templateResponse.getId(), userId, "email"));
			
			if (defaultEmailTemplateId != null && defaultEmailTemplateId.equals(bte.getTemplateId())) {
				ongoingEmailTemplate = new BusinessTemplateEntity(templateResponse.getId(), bte.getTemplateType());
			}
		}
		if (CollectionUtils.isNotEmpty(emailTemplateIds)) {
			logger.info("Following email template ids were either not found or already migrated under any location of this enterprise {} : {}", business.getId(), emailTemplateIds);
		}
		logger.info("Finished migrating email templates for enterprise {}", business.getId());
		return ongoingEmailTemplate;
	}
	
	/**
	 * Migrate the given list of email templates to Campaign V2.
	 * 
	 * @param business
	 * @param emailTemplateIds
	 * @param locationIds
	 * @param userId
	 */
	private BusinessTemplateEntity migrateEmailTemplatesOnlyForEnterprise(BusinessEnterpriseEntity business, Set<Integer> emailTemplateIds, List<Integer> locationIds, Integer userId,
			Integer defaultEmailTemplateId, EnterpriseTemplatesMigrationRO request) {
		logger.info("Started migrating email templates for enterprise {}", business.getId());
		if (CollectionUtils.isEmpty(emailTemplateIds)) {
			return null;
		}
		List<BusinessTemplateEntity> businessTemplateEntities = businessEmailTemplateRepo.getByBusinessIdsAndTemplateIds(locationIds, emailTemplateIds);
		Map<Integer, BusinessTemplateEntity> bTEMap = businessTemplateEntities.stream()
				.collect(Collectors.toMap(BusinessTemplateEntity::getTemplateId, businessTemplateEntity -> businessTemplateEntity, (first, second) -> second));
		
		EmailTemplateTypes[] emailTemplateTypes = EmailTemplateTypes.values();
		Map<String, EmailTemplate> emailTemplateByTypeMap = new HashMap<>();
		for (EmailTemplateTypes type : emailTemplateTypes) {
			emailTemplateByTypeMap.put(StringUtils.lowerCase(type.name()), emailTemplateCacheService.getDefaultEmailTemplateByType(StringUtils.lowerCase(type.name())));
		}
		BusinessTemplateEntity ongoingEmailTemplate = null;
		for (BusinessTemplateEntity bte : bTEMap.values()) {
			logger.info("Migrating email template : {} for enterprise {}", bte.getTemplateId(), business.getId());
			EmailTemplateResponse response = emailTemplateService.getEmailTemplateResponseById(bte.getTemplateId(), bte.getTemplateType());
			response.setSurveyButtonColor(SURVEY_BUTTON_COLOR);
			response.setSurveyButtonTextColor(FFFFFF);
			response.setDefaultReplyToEnabled(1);
			// enabling location level branding.
			response.setLocationBrandingEnabled(0);
			if (request.getContactUsEnabled() != null) {
				response.setContactUsEnabled(request.getContactUsEnabled());
			}
			EmailTemplate defaultEmailTemplate = emailTemplateByTypeMap.get(bte.getTemplateType());
			if (defaultEmailTemplate != null) {
				response.setReviewSiteButtonColor(defaultEmailTemplate.getReviewSiteButtonColor());
				response.setReviewSiteButtonTextColor(defaultEmailTemplate.getReviewSiteButtonTextColor());
				response.setFeedbackCallbackMessage(defaultEmailTemplate.getFeedbackMessage());
				response.setFeedbackCallbackEnabled(defaultEmailTemplate.getFeedbackShowCallbackOption());
				response.setFeedbackCheckboxEnabled(defaultEmailTemplate.getFeedbackDefaultCheckboxChecked());
				response.setThankYouHeading(defaultEmailTemplate.getThankyouHeading());
				response.setThankYouMessage(defaultEmailTemplate.getThankyouMessage());
				response.setSignature(defaultEmailTemplate.getSignature());
			}
			if (bte.getUserId() != null) {
				MDC.put("uid", bte.getUserId().toString());
			}
			// creating new email template
			EditTemplateResponse templateResponse = emailTemplateService.createOrUpdateBusinessEmailTemplate(0, business.getId(), bte.getTemplateType(), response);
			logger.info("######################## Created new email template with id : {} from templateId : {} for enterprise {} ########################", templateResponse.getId(),
					bte.getTemplateId(), business.getId());
			// setting isDeleted flag to true for migrated templates.
			businessEmailTemplateRepo.updateBusinessEmailTemplateDeletedFlag(bte.getBusinessId(), bte.getTemplateId(), 1);
			
			emailTemplateIds.remove(bte.getTemplateId());
			// creating audit for migrated template
			migratedTemplatesAuditRepo.saveAndFlush(new MigratedTemplatesAudit(business.getId(), bte.getBusinessId(), bte.getTemplateId(), templateResponse.getId(), userId, "email"));
			
			if (defaultEmailTemplateId != null && defaultEmailTemplateId.equals(bte.getTemplateId())) {
				ongoingEmailTemplate = new BusinessTemplateEntity(templateResponse.getId(), bte.getTemplateType());
			}
		}
		if (CollectionUtils.isNotEmpty(emailTemplateIds)) {
			logger.info("Following email template ids were either not found or already migrated under any location of this enterprise {} : {}", business.getId(), emailTemplateIds);
		}
		logger.info("Finished migrating email templates for enterprise {}", business.getId());
		return ongoingEmailTemplate;
	}
	
	/**
	 * Migrate the given list of sms templates to Campaign V2.
	 * 
	 * @param business
	 * @param smsTemplateIds
	 * @param locationIds
	 * @param userId
	 * @param defaultSmsTemplateId
	 * @param contactUsEnabled
	 * @param request
	 * @return
	 */
	private BusinessTemplateEntity migrateSmsTemplatesForEnterprise(BusinessEnterpriseEntity business, Set<Integer> smsTemplateIds, List<Integer> locationIds, Integer userId,
			Integer defaultSmsTemplateId, EnterpriseTemplatesMigrationRO request) {
		logger.info("Started migrating sms templates for enterprise : {}", business.getId());
		if (CollectionUtils.isEmpty(smsTemplateIds)) {
			return null;
		}
		Set<String> smsTemplatesTypes = new HashSet<>();
		smsTemplatesTypes.add(TemplateTypeEnum.REVIEW_REQUEST_SMS.getName());
		smsTemplatesTypes.add(TemplateTypeEnum.CUSTOMER_EXPERIENCE_SMS.getName());
		smsTemplatesTypes.add(TemplateTypeEnum.SURVEY_REQUEST_SMS.getName());
		
		List<BusinessSmsTemplate> defaultBusinessSmsTemplates = businessSMSTemplateRepo.getDefaultBusinessSmsTemplates(smsTemplatesTypes);
		Map<String, BusinessSmsTemplate> defaultSmsTemplatesMap = new HashMap<>();
		for (BusinessSmsTemplate dbst : defaultBusinessSmsTemplates) {
			defaultSmsTemplatesMap.put(dbst.getType(), dbst);
		}
		List<BusinessSmsTemplate> businessSmsTemplates = businessSMSTemplateRepo.getByBusinessIdsAndTemplateIdsAndIsDeleted(locationIds, smsTemplateIds);
		BusinessTemplateEntity ongoingSmsTemplate = null;
		for (BusinessSmsTemplate bst : businessSmsTemplates) {
			logger.info("Migrating sms template : {} for enterprise {}", bst.getId(), business.getId());
			GenericCampaignSmsTemplateResponse genericSmsTemplateResponse = templateService.getSMSTemplate(business.getId(), bst.getId(), bst.getType(), Constants.IS_GLOBAL_TEMPLATE);
			BusinessSmsTemplate defaultBusinessSmsTemplate = defaultSmsTemplatesMap.get(bst.getType());
			if (defaultBusinessSmsTemplate != null) {
				genericSmsTemplateResponse.setFeedbackCallbackEnabled(defaultBusinessSmsTemplate.getEnableFeedbackMessage());
				genericSmsTemplateResponse.setFeedbackCallbackMessage(defaultBusinessSmsTemplate.getFeedbackCallbackMessage());
				genericSmsTemplateResponse.setFeedbackCheckboxEnabled(defaultBusinessSmsTemplate.getEnableFeedbackCheckbox());
				genericSmsTemplateResponse.setThankYouHeading(defaultBusinessSmsTemplate.getThankyouHeading());
				genericSmsTemplateResponse.setThankYouMessage(defaultBusinessSmsTemplate.getThankyouMessage());
				genericSmsTemplateResponse.setReviewSiteButtonColor(defaultBusinessSmsTemplate.getReviewSiteButtonColor());
				genericSmsTemplateResponse.setReviewSiteButtonTextColor(defaultBusinessSmsTemplate.getReviewSiteButtonTextColor());
				genericSmsTemplateResponse.setReviewEnabled(defaultBusinessSmsTemplate.getReviewEnable());
			}
			// enabling location level branding.
			genericSmsTemplateResponse.setLocationBrandingEnabled(0);
			if (request.getContactUsEnabled() != null) {
				genericSmsTemplateResponse.setContactUsEnabled(request.getContactUsEnabled());
			}
			if (request.getEnableUnsubscribeText() != null) {
				genericSmsTemplateResponse.setIncludeUnsubscribeText(request.getEnableUnsubscribeText());
			}
			// BIRDEYE-55798
			if (request.getEnableLocationBranding() != null) {
				genericSmsTemplateResponse.setLocationBrandingEnabled(request.getEnableLocationBranding());
			}
			if (request.getEnableMms() != null) {
				genericSmsTemplateResponse.setIncludeImageWithText(request.getEnableMms());
			}
			
			// creating new sms template
			BusinessTemplateResponse templateResponse = templateService.createCampaignSmsTemplate(business.getId(), bst.getCreatedBy(),
					new CampaignSmsTemplateSRO(genericSmsTemplateResponse));
			logger.info("######################## Created new sms template with id : {} from templateId : {} for enterprise {} ########################", templateResponse.getId(), bst.getId(),
					business.getId());
			// setting isDeleted flag to true for migrated templates.
			businessSMSTemplateRepo.updateBusinessSmsTemplateIsDeleted(bst.getId(), 1);
			
			smsTemplateIds.remove(bst.getId());
			// creating audit for migrated template
			migratedTemplatesAuditRepo.saveAndFlush(new MigratedTemplatesAudit(business.getId(), bst.getBusinessId(), bst.getId(), templateResponse.getId(), userId, "sms"));
			
			if (defaultSmsTemplateId != null && defaultSmsTemplateId.equals(bst.getId())) {
				ongoingSmsTemplate = new BusinessTemplateEntity(templateResponse.getId(), bst.getType());
			}
		}
		if (CollectionUtils.isNotEmpty(smsTemplateIds)) {
			logger.info("Following sms template ids were either not found or already migrated under any location of this enterprise {} : {}", business.getId(), smsTemplateIds);
		}
		logger.info("Finished migrating sms templates from enterprise : {}", business.getId());
		return ongoingSmsTemplate;
	}
	
	private BusinessTemplateEntity migrateSmsTemplatesOnlyForEnterprise(BusinessEnterpriseEntity business, Set<Integer> smsTemplateIds, List<Integer> locationIds, Integer userId,
			Integer defaultSmsTemplateId, EnterpriseTemplatesMigrationRO request) {
		logger.info("Started migrating sms templates for enterprise : {}", business.getId());
		if (CollectionUtils.isEmpty(smsTemplateIds)) {
			return null;
		}
		Set<String> smsTemplatesTypes = new HashSet<>();
		smsTemplatesTypes.add(TemplateTypeEnum.REVIEW_REQUEST_SMS.getName());
		smsTemplatesTypes.add(TemplateTypeEnum.CUSTOMER_EXPERIENCE_SMS.getName());
		smsTemplatesTypes.add(TemplateTypeEnum.SURVEY_REQUEST_SMS.getName());
		
		List<BusinessSmsTemplate> defaultBusinessSmsTemplates = businessSMSTemplateRepo.getDefaultBusinessSmsTemplates(smsTemplatesTypes);
		Map<String, BusinessSmsTemplate> defaultSmsTemplatesMap = new HashMap<>();
		for (BusinessSmsTemplate dbst : defaultBusinessSmsTemplates) {
			defaultSmsTemplatesMap.put(dbst.getType(), dbst);
		}
		List<BusinessSmsTemplate> businessSmsTemplates = businessSMSTemplateRepo.getByBusinessIdsAndTemplateIds(locationIds, smsTemplateIds);
		BusinessTemplateEntity ongoingSmsTemplate = null;
		for (BusinessSmsTemplate bst : businessSmsTemplates) {
			logger.info("Migrating sms template : {} for enterprise {}", bst.getId(), business.getId());
			GenericCampaignSmsTemplateResponse genericSmsTemplateResponse = templateService.getSMSTemplate(business.getId(), bst.getId(), bst.getType(), Constants.IS_GLOBAL_TEMPLATE);
			BusinessSmsTemplate defaultBusinessSmsTemplate = defaultSmsTemplatesMap.get(bst.getType());
			if (defaultBusinessSmsTemplate != null) {
				genericSmsTemplateResponse.setFeedbackCallbackEnabled(defaultBusinessSmsTemplate.getEnableFeedbackMessage());
				genericSmsTemplateResponse.setFeedbackCallbackMessage(defaultBusinessSmsTemplate.getFeedbackCallbackMessage());
				genericSmsTemplateResponse.setFeedbackCheckboxEnabled(defaultBusinessSmsTemplate.getEnableFeedbackCheckbox());
				genericSmsTemplateResponse.setThankYouHeading(defaultBusinessSmsTemplate.getThankyouHeading());
				genericSmsTemplateResponse.setThankYouMessage(defaultBusinessSmsTemplate.getThankyouMessage());
				genericSmsTemplateResponse.setReviewSiteButtonColor(defaultBusinessSmsTemplate.getReviewSiteButtonColor());
				genericSmsTemplateResponse.setReviewSiteButtonTextColor(defaultBusinessSmsTemplate.getReviewSiteButtonTextColor());
				genericSmsTemplateResponse.setReviewEnabled(defaultBusinessSmsTemplate.getReviewEnable());
			}
			// enabling location level branding.
			genericSmsTemplateResponse.setLocationBrandingEnabled(0);
			if (request.getContactUsEnabled() != null) {
				genericSmsTemplateResponse.setContactUsEnabled(request.getContactUsEnabled());
			}
			if (request.getEnableUnsubscribeText() != null) {
				genericSmsTemplateResponse.setIncludeUnsubscribeText(request.getEnableUnsubscribeText());
			}
			// creating new sms template
			BusinessTemplateResponse templateResponse = templateService.createCampaignSmsTemplate(business.getId(), bst.getCreatedBy(),
					new CampaignSmsTemplateSRO(genericSmsTemplateResponse));
			logger.info("######################## Created new sms template with id : {} from templateId : {} for enterprise {} ########################", templateResponse.getId(), bst.getId(),
					business.getId());
			// setting isDeleted flag to true for migrated templates.
			businessSMSTemplateRepo.updateBusinessSmsTemplateIsDeleted(bst.getId(), 1);
			
			smsTemplateIds.remove(bst.getId());
			// creating audit for migrated template
			migratedTemplatesAuditRepo.saveAndFlush(new MigratedTemplatesAudit(business.getId(), bst.getBusinessId(), bst.getId(), templateResponse.getId(), userId, "sms"));
			
			if (defaultSmsTemplateId != null && defaultSmsTemplateId.equals(bst.getId())) {
				ongoingSmsTemplate = new BusinessTemplateEntity(templateResponse.getId(), bst.getType());
			}
		}
		if (CollectionUtils.isNotEmpty(smsTemplateIds)) {
			logger.info("Following sms template ids were either not found or already migrated under any location of this enterprise {} : {}", business.getId(), smsTemplateIds);
		}
		logger.info("Finished migrating sms templates from enterprise : {}", business.getId());
		return ongoingSmsTemplate;
	}
	
	private boolean isValidEntTemplatesMigrationRequest(BusinessEnterpriseEntity business, EnterpriseTemplatesMigrationRO request) {
		
		// Not checking account features flag since this api can only migrate 100 templates (email and sms each) at a time. Remaining templates will be
		// migrated in new request.
		
		if (business == null) {
			throw new CampaignHTTPException(HttpStatus.BAD_REQUEST, "Invalid enterprise id.");
		}
		
		if (CollectionUtils.isNotEmpty(request.getEmailTemplateIds()) && request.getEmailTemplateIds().size() > 100) {
			throw new CampaignHTTPException(HttpStatus.BAD_REQUEST, "Email templates list can not contain more than 100 ids at a time.");
		}
		if (CollectionUtils.isNotEmpty(request.getSmsTemplateIds()) && request.getSmsTemplateIds().size() > 100) {
			throw new CampaignHTTPException(HttpStatus.BAD_REQUEST, "Sms templates list can not contain more than 100 ids at a time.");
		}
		return true;
	}
	
	@SuppressWarnings("unused")
	private boolean isValidEnterpriseForMigration(BusinessEnterpriseEntity business) {
		if (business == null || StringUtils.isBlank(business.getType()) || StringUtils.isBlank(business.getAccountType()) || (business.getClosed() != null && business.getClosed().intValue() == 1)
				|| business.getEnterpriseId() != null) {
			return false;
		}
		return (StringUtils.equalsIgnoreCase(business.getType(), BusinessTypeEnum.ENTERPRISE_PRODUCT.getBusinessType())
				|| StringUtils.equalsIgnoreCase(business.getType(), BusinessTypeEnum.ENTERPRISE_LOCATION.getBusinessType()));
	}
	
	// Excel values are getting read as float.
	private static Integer getCellValueAsInt(String value) {
		int result = 0;
		try {
			result = (int) Double.parseDouble(value);
		} catch (Exception e) {
			// Ignore
		}
		return result;
	}
	
	/**
	 * Update Ongoing Campaign Reminder,Delay and setting
	 */
	@Async
	public void updateCampaign(Map<String, String> data) {
		String bid = data.get(BID);
		if (StringUtils.isNotBlank(bid)) {
			Integer businessId = getCellValueAsInt(bid);
			BusinessEnterpriseEntity business = businessReadOnlyRepo.getValidBusinessByBid(businessId);
			logger.info("********** Processing Campaign update for enterprise id : {}", business.getId());
			String limit = data.get(DAYS_LIMIT);
			if (limit != null) {
				List<CampaignAccountSettings> accountSettings = campaignAccountSettionRepo.getCampaignAccountSettingsByAccountId(business.getId());
				if (CollectionUtils.isNotEmpty(accountSettings)) {
					logger.info("Campaign settings found for enterprise id : {}", business.getId());
					CampaignAccountSettings bcs = accountSettings.stream().findFirst().get();
					Integer daysLimit = getCellValueAsInt(limit);
					bcs.setMailResendFrequency(daysLimit);
					campaignAccountSettionRepo.save(bcs);
					cacheService.evictCampaignAccountSettingsCache(business.getId());
					logger.info("Campaign settings updated for enterprise id : {} with value {} ", business.getId(), daysLimit);
				} else {
					logger.warn("********** No Campaign settings found for enterprise id : {}", business.getId());
				}
			}
			
			List<Campaign> ongoingCampaigns = campaignRepo.getOngoingCampaignsByEnterpriseId(businessId);
			if (ongoingCampaigns.isEmpty()) {
				logger.warn("********** No Ongoing Campaign found for enterprise id : {}", business.getId());
			} else {
				logger.info("Updating settings of all ongoing campaigns for enterprise : {}", business.getId());
				String delayInHours = data.get(DELAY);
				String reminderCount = data.get(R_COUNT);
				String reminderFrequency = data.get(R_FREQUENCY);
				ongoingCampaigns.stream().forEach(campaign -> {
					if (delayInHours != null) {
						campaign.setSchedule(getCellValueAsInt(delayInHours));
					}
					if (reminderCount != null) {
						campaign.setReminderCount(getCellValueAsInt(reminderCount));
						if (getCellValueAsInt(reminderCount).equals(0)) {
							campaign.setSendReminder(0);
						} else {
							campaign.setSendReminder(1);
						}
					}
					if (reminderFrequency != null) {
						campaign.setReminderFrequency(getCellValueAsInt(reminderFrequency));
					}
				});
				campaignRepo.saveAll(ongoingCampaigns);
				logger.info("Ongoing Campaign settings updated for enterprise id : {} with data {} ", business.getId(), data);
			}
		}
		
	}
	
	@Override
	public void createOngoingCampaignsForLocation(OngoingCampaignByLocationRequest request, Integer userId) {
		
		if (request == null || request.getEnterpriseId() == null || CollectionUtils.isEmpty(request.getTemplateByLocationRequestList())) {
			logger.error("Invalid request to create ongoing campaigns for given locations : {}", request);
			return;
		}
		
		BusinessEnterpriseEntity enterprise = businessReadOnlyRepo.getValidBusinessByBid(request.getEnterpriseId());
		if (enterprise == null) {
			throw new CampaignHTTPException(HttpStatus.BAD_REQUEST, "Invalid enterprise id.");
		}
		
		createOngoingCampaignsForLocation(request, userId, enterprise);
		
	}
	
	private void createOngoingCampaignsForLocation(OngoingCampaignByLocationRequest request, Integer userId, BusinessEnterpriseEntity enterprise) {
		
		// get new template ids from migrated template audit
		List<MigratedTemplatesAudit> migrationAudit = migratedTemplatesAuditRepo.findAllByEnterpriseId(enterprise.getId());
		if (CollectionUtils.isEmpty(migrationAudit)) {
			logger.warn("No migration audit found for enterprise : {}", enterprise.getId());
			return;
		}
		
		if (request.getCampaignStatus().equals(0)) {
			request.setCampaignStatus(CampaignStatusEnum.PAUSED.getStatus());
		}
		
		Map<Boolean, List<MigratedTemplatesAudit>> migrationAuditByType = migrationAudit.stream().collect(Collectors.partitioningBy(audit -> "email".equalsIgnoreCase(audit.getType())));
		
		Map<Integer, Integer> emailTemplateIdsMap = migrationAuditByType.get(Boolean.TRUE).stream()
				.collect(Collectors.toMap(MigratedTemplatesAudit::getOldTemplateId, MigratedTemplatesAudit::getNewTemplateId, (first, second) -> second));
		
		Map<Integer, Integer> smsTemplateIdsMap = migrationAuditByType.get(Boolean.FALSE).stream()
				.collect(Collectors.toMap(MigratedTemplatesAudit::getOldTemplateId, MigratedTemplatesAudit::getNewTemplateId, (first, second) -> second));
		
		for (TemplateByLocationRequest templateByLocReq : request.getTemplateByLocationRequestList()) {
			logger.info("Starting creation of ongoign campaign for request : {}", templateByLocReq);
			Business location = businessReadOnlyRepo.findFirstByIdAndClosed(templateByLocReq.getLocationId(), 0);
			if (location == null || !enterprise.getId().equals(location.getEnterpriseId())) {
				logger.warn("LocationId : {} is not an active location for enterprise : {}", templateByLocReq.getLocationId(), enterprise.getId());
				continue;
			}
			Integer ongoingEmailTemplateId = emailTemplateIdsMap.get(templateByLocReq.getEmailTemplateId());
			EmailTemplate ongoingEmailTemplate = emailTemplateRepo.findFirstById(ongoingEmailTemplateId);
			Integer ongoingSmsTemplateId = smsTemplateIdsMap.get(templateByLocReq.getSmsTemplateId());
			BusinessSmsTemplate ongoingSmsTemplate = businessSMSTemplateRepo.findFirstById(ongoingSmsTemplateId);
			
			// create ongoing campaign.
			if (ongoingEmailTemplate != null && ongoingSmsTemplate != null) {
				if (!CampaignUtils.isSameTemplatesType(ongoingEmailTemplate.getType(), ongoingSmsTemplate.getType())) {
					logger.info("No ongoing campaign will be created since email and sms templates are of different types.");
				} else {
					// create ongoing campaign with email_and_sms priority.
					campaignSetupService.createOnGoingCampaignForMigration(enterprise.getId(), userId, ongoingEmailTemplateId, ongoingSmsTemplateId,
							CampaignStatusEnum.getStatus(request.getCampaignStatus()), CampaignPriorityEnum.EMAIL_AND_SMS.getType(),
							CampaignUtils.getCampaignTypeByTemplateType(CampaignUtils.getTemplateTypeEnumByEmailTemplateType(ongoingEmailTemplate.getType())).getType(), templateByLocReq.getSurveyId(),
							Collections.singletonList(location.getId().toString()), location.getBusinessId().toString());
				}
			} else if (ongoingEmailTemplate != null) {
				// create ongoing campaign with email only priority.
				campaignSetupService.createOnGoingCampaignForMigration(enterprise.getId(), userId, ongoingEmailTemplateId, null, CampaignStatusEnum.getStatus(request.getCampaignStatus()),
						CampaignPriorityEnum.EMAIL.getType(),
						CampaignUtils.getCampaignTypeByTemplateType(CampaignUtils.getTemplateTypeEnumByEmailTemplateType(ongoingEmailTemplate.getType())).getType(), templateByLocReq.getSurveyId(),
						Collections.singletonList(location.getId().toString()), location.getBusinessId().toString());
			} else if (ongoingSmsTemplate != null) {
				// create ongoing campaign with sms only priority.
				campaignSetupService.createOnGoingCampaignForMigration(enterprise.getId(), userId, null, ongoingSmsTemplateId, CampaignStatusEnum.getStatus(request.getCampaignStatus()),
						CampaignPriorityEnum.SMS.getType(), CampaignUtils.getCampaignTypeByTemplateType(CampaignUtils.getTemplateTypeEnumBySmsTemplateType(ongoingSmsTemplate.getType())).getType(),
						templateByLocReq.getSurveyId(), Collections.singletonList(location.getId().toString()), location.getBusinessId().toString());
			}
		}
	}
	
	@Override
	public void createDefaultPulseSurveyTemplate(List<Integer> accountIds, Integer userId) {
		logger.info("Received request to create pulse survey templates for account Ids : {} by userId : {}", accountIds, userId);
		if (CollectionUtils.isEmpty(accountIds) || CollectionUtils.size(accountIds) > 1000) {
			logger.warn("Either account id {} are empty or more than the 1000", accountIds);
			return;
		}
		accountIds.parallelStream().forEach(accountId -> createDefaultPulseSurveyTemplate(accountId, userId));
	}
	
	private void createDefaultPulseSurveyTemplate(Integer accountId, Integer userId) {
		logger.info("Creating pulse survey template for account Id : {} by userId : {}", accountId, userId);
		
		if (!businessOptionService.isSurveyEnabledForBusiness(accountId)) {
			logger.warn("Can't create pulse survey template as Survey is not enabled for business : {}", accountId);
			return;
		}
		
		if (!CoreUtils.isTrueForInteger(businessExternalService.isMessengerEnabled(accountId))) {
			logger.warn("Can't create pulse survey template as Messenger is not enabled for business : {}", accountId);
			return;
		}
		
		templateService.createPulseSurveySMSTemplate(accountId, userId, "Pulse survey");
		
	}
	
	@Override
	public void executeSegmentCountMigrationBatch(MigrationRequest segmentCountMigrationRequest) {
		logger.info("[executeRequestDateMigrationBatch] : starting execution for startID {} and endID {}", segmentCountMigrationRequest.getStartId(), segmentCountMigrationRequest.getEndId());
		List<Promotion> promotionRequests = promotionRepo.getPromotionRequestsBetweenRange(segmentCountMigrationRequest.getStartId(), segmentCountMigrationRequest.getEndId());
		if (CollectionUtils.isEmpty(promotionRequests)) {
			logger.info("no requests found between startID {} and endID {}", segmentCountMigrationRequest.getStartId(), segmentCountMigrationRequest.getEndId());
			return;
		}
		List<Promotion> smsPromotionRequests = promotionRequests.stream().filter(
				request -> StringUtils.equalsIgnoreCase(request.getDeliveryStatus(), RequestStatusEnum.SUCCESS.getName()) && StringUtils.equals(request.getSource(), Constants.TEMPLATE_BASE_TYPE_SMS))
				.collect(Collectors.toList());
		segmentCountMigrationService.migratePromotionCommSegmentCountInBatch(smsPromotionRequests);
		
		createSegmentCountMigrationAudit(segmentCountMigrationRequest, promotionRequests.size());
		logger.info("[executeSegmentCountMigrationBatch] : finished execution for startID {} and endID {}", segmentCountMigrationRequest.getStartId(), segmentCountMigrationRequest.getEndId());
	}
	
	private void createSegmentCountMigrationAudit(MigrationRequest segmentCountMigrationRequest, Integer batchSize) {
		SegmentCountMigrationAudit audit = new SegmentCountMigrationAudit();
		audit.setStartRequestId(segmentCountMigrationRequest.getStartId());
		audit.setEndRequestId(segmentCountMigrationRequest.getEndId());
		audit.setStatus(1);
		audit.setBatchSize(batchSize);
		segmentCountMigrationAuditRepo.saveAndFlush(audit);
	}
	
	@Override
	public void executeSegmentCountMigrationForEnterprise(EventMigrationRequest segmentCountMigrationRequest) {
		logger.info("[executeSegmentCountMigrationForEnterprise] : starting execution for request{}", segmentCountMigrationRequest);
		List<Integer> businessIds = businessReadOnlyRepo.findBusinessIdsForEnterpriseId(segmentCountMigrationRequest.getEnterpriseIds());
		List<Promotion> promotionRequests = promotionRepo.findPromotionRequestsByBusinessIds(businessIds);
		if (CollectionUtils.isEmpty(promotionRequests)) {
			logger.info("no requests found for enterpriseIds {}", segmentCountMigrationRequest.getEnterpriseIds());
			return;
		}
		List<Promotion> smsPromotionRequests = promotionRequests.stream().filter(
				request -> StringUtils.equalsIgnoreCase(request.getDeliveryStatus(), RequestStatusEnum.SUCCESS.getName()) && StringUtils.equals(request.getSource(), Constants.TEMPLATE_BASE_TYPE_SMS))
				.collect(Collectors.toList());
		
		segmentCountMigrationService.migratePromotionCommSegmentCountInBatch(smsPromotionRequests);
		if (CollectionUtils.isEmpty(smsPromotionRequests)) {
			logger.info("no requests found for enterpriseIds {}", segmentCountMigrationRequest.getEnterpriseIds());
			return;
		}
		createSegmentCountMigrationAudit(new MigrationRequest(smsPromotionRequests.get(0).getId(), smsPromotionRequests.get(smsPromotionRequests.size() - 1).getId()), promotionRequests.size());
		logger.info("[executeSegmentCountMigrationForEnterprise] : finished execution for for enterpriseIds {}", segmentCountMigrationRequest.getEnterpriseIds());
	}
	
	public void executeSegmentCountMigrationForRequestIds(EventMigrationRequest segmentCountMigrationRequest) {
		logger.info("[executeSegmentCountMigrationForRequestIds] : starting execution for request{}", segmentCountMigrationRequest.getReviewRequestIds());
		List<Promotion> promotionRequests = promotionRepo.findPromotionByIds(segmentCountMigrationRequest.getReviewRequestIds());
		if (CollectionUtils.isEmpty(promotionRequests)) {
			logger.info("no requests found for requestIds {}", segmentCountMigrationRequest.getReviewRequestIds());
			return;
		}
		List<Promotion> smsPromotionRequests = promotionRequests.stream().filter(
				request -> StringUtils.equalsIgnoreCase(request.getDeliveryStatus(), RequestStatusEnum.SUCCESS.getName()) && StringUtils.equals(request.getSource(), Constants.TEMPLATE_BASE_TYPE_SMS))
				.collect(Collectors.toList());
		
		segmentCountMigrationService.migratePromotionCommSegmentCountInBatch(smsPromotionRequests);
		if (CollectionUtils.isEmpty(smsPromotionRequests)) {
			logger.info("no requests found for requestIds {}", segmentCountMigrationRequest.getReviewRequestIds());
			return;
		}
		createSegmentCountMigrationAudit(new MigrationRequest(smsPromotionRequests.get(0).getId(), smsPromotionRequests.get(smsPromotionRequests.size() - 1).getId()), promotionRequests.size());
		logger.info("[executeSegmentCountMigrationForRequestIds] : finished execution for for requestIds {}", segmentCountMigrationRequest.getReviewRequestIds());
	}
	
	@Override
	public void setDefaultReviewSourcesToDefaultTemplates(List<Long> businessIds) {
		if (CollectionUtils.isEmpty(businessIds) || CollectionUtils.size(businessIds) > 500) {
			logger.info("Either business ids {} are empty or more than the 500", businessIds);
			return;
		}
		List<Integer> bIds = businessReadOnlyRepo.getValidBusinessIdByBusinessNumbers(businessIds);
		bIds.stream().forEach(bId -> updateBusinessDeeplinkPriority(bId));
	}
	
	@Override
	public void updateBusinessDeeplinkPriority(List<Integer> businessIds) {
		if (CollectionUtils.isEmpty(businessIds) || CollectionUtils.size(businessIds) > 500) {
			logger.info("Either business ids {} are empty or more than the 500", businessIds);
			return;
		}
		businessIds.stream().forEach(businessId -> updateBusinessDeeplinkPriority(businessId));
	}
	
	private void updateBusinessDeeplinkPriority(Integer businessId) {
		
		List<ReviewSourceSRO> allReviewSources = reviewSourcesService.getAllReviewSources(businessId);
		if (CollectionUtils.isEmpty(allReviewSources)) {
			logger.error("No aggregated review source found for business : {}", businessId);
			return;
		}
		Map<Integer, ReviewSourceSRO> filteredSourcesMap = allReviewSources.stream().filter(r -> (Arrays.asList(2, 110).contains(r.getSourceId())))
				.collect(Collectors.toMap(ReviewSourceSRO::getSourceId, p -> p, (first, second) -> second));
		//List<Campaign> defaultCampaigns = campaignRepo.getDefaultOngoingCampaignByEnterpriseId(businessId);
		DefaultTemplateDTO defaultTemplateDTO = defaultTemplateService.getDefaultTemplates(businessId, TemplateTypeEnum.REVIEW_REQUEST_NEW.getName(), Constants.EMAIL_AND_SMS);
		if (defaultTemplateDTO == null) {
			logger.error("No default templates found for business : {}", businessId);
			return;
		}
		//Campaign defaultCampaign = defaultCampaigns.get(0);
		List<ReviewSourceSRO> reviewSources = new ArrayList<>();
		if (filteredSourcesMap != null && filteredSourcesMap.containsKey(2)) {
			reviewSources.add(new ReviewSourceSRO(2, "Google", 1, StringUtils.join("Review us on ", "Google"), 1));
		}
		if (filteredSourcesMap != null && filteredSourcesMap.containsKey(110)) {
			reviewSources.add(new ReviewSourceSRO(110, "Facebook", 2, StringUtils.join("Review us on ", "Facebook"), 1));
		}
		if (CollectionUtils.isEmpty(reviewSources)) {
			//logger.error("No final review sources found for business : {} & campaignId : {}", businessId, defaultCampaign.getId());
			logger.error("No final review sources found for business : {} & email template id : {} & sms template id : {}", defaultTemplateDTO.getEmailTemplateId(), defaultTemplateDTO.getSmsTemplateId());
			return;
		}
		
		logger.info("Update business deeplink priorities for email template id : {} and sms template id : {}", defaultTemplateDTO.getEmailTemplateId(), defaultTemplateDTO.getSmsTemplateId());
		if (defaultTemplateDTO.getEmailTemplateId() != null) {
			updateEmailTemplateSourcesAndSendEvent(businessId, defaultTemplateDTO, reviewSources);
		}
		if (defaultTemplateDTO.getSmsTemplateId() != null) {
			updateSmsTemplateSourcesAndSendEvent(businessId, defaultTemplateDTO, reviewSources);
		}
		
		
//		logger.info("Update business deeplink priorities for email template id : {} and sms template id : {}", defaultCampaign.getTemplateId(), defaultCampaign.getSmsTemplateId());
//		
//		businessDeeplinkPriorityDaoService.updateAvailableDeeplinksForATemplate(defaultCampaign.getTemplateId(), Constants.DEVICE_TYPE_WEB, reviewSources);
//
//		businessDeeplinkPriorityDaoService.updateAvailableDeeplinksForATemplate(defaultCampaign.getSmsTemplateId(), Constants.DEEPLINK_DEVICETYPE_IOS, reviewSources);
//
//		businessDeeplinkPriorityDaoService.updateAvailableDeeplinksForATemplate(defaultCampaign.getSmsTemplateId(), Constants.DEEPLINK_DEVICETYPE_ANDROID, reviewSources);

	}

	private void updateSmsTemplateSourcesAndSendEvent(Integer businessId, DefaultTemplateDTO defaultTemplateDTO, List<ReviewSourceSRO> reviewSources) {
		Map<String, AllTemplateDataDto> dataDiffMap = CampaignModificationAuditUtils.initializeDataDiffMap(Boolean.FALSE, Boolean.FALSE, Boolean.FALSE);
		CampaignModificationAuditUtils.addSourcesToAllTemplateData(dataDiffMap,
				businessDeeplinkPriorityDaoService.updateAvailableDeeplinksForATemplate(defaultTemplateDTO.getSmsTemplateId(), Constants.DEEPLINK_DEVICETYPE_IOS, reviewSources),
				CampaignModificationAuditUtils.SELECTED_SOURCES);
		CampaignModificationAuditUtils.addSourcesToAllTemplateData(dataDiffMap,
				businessDeeplinkPriorityDaoService.updateAvailableDeeplinksForATemplate(defaultTemplateDTO.getSmsTemplateId(), Constants.DEEPLINK_DEVICETYPE_ANDROID, reviewSources),
				CampaignModificationAuditUtils.SELECTED_ANDROID_SOURCES);
		campaignModificationAuditService.validateAndSendChangeLogEventForTemplates(Boolean.FALSE, null, businessId, defaultTemplateDTO.getSmsTemplateId(), dataDiffMap, null, Constants.SMS_TYPE);
	}

	private void updateEmailTemplateSourcesAndSendEvent(Integer businessId, DefaultTemplateDTO defaultTemplateDTO, List<ReviewSourceSRO> reviewSources) {
		Map<String, AllTemplateDataDto> dataDiffMap = CampaignModificationAuditUtils.initializeDataDiffMap(Boolean.FALSE, Boolean.FALSE, Boolean.FALSE);
		CampaignModificationAuditUtils.addSourcesToAllTemplateData(dataDiffMap,
				businessDeeplinkPriorityDaoService.updateAvailableDeeplinksForATemplate(defaultTemplateDTO.getEmailTemplateId(), Constants.DEVICE_TYPE_WEB, reviewSources),
				CampaignModificationAuditUtils.SELECTED_SOURCES);
		campaignModificationAuditService.validateAndSendChangeLogEventForTemplates(Boolean.FALSE, null, businessId, defaultTemplateDTO.getEmailTemplateId(), dataDiffMap, null, Constants.EMAIL_TYPE);
	}
	
	public AutomationCampaignMigrationResponse migrateAutomationCampaignForEnterprise(Integer enterpriseId, Integer campaignId, boolean checkCampaign,
			CustomerCustomFieldResponse customFieldResponse) {
		// extra check for deleted campaign
		AutomationCampaignMigrationResponse response = new AutomationCampaignMigrationResponse(enterpriseId, campaignId);
		if (checkCampaign) {
			Campaign campaign = campaignRepo.getValidCampaignById(campaignId);
			if (campaign == null || campaign.getIsDeleted() == 1) {
				logger.info("For EnterpriseId {} the campaign {} is deleted", campaignId, campaignId);
				response.setStatus(false);
				response.setFailureReason("Campaign is deleted");
				return response;
			}
			// add campaign condition check
			CampaignCondition condition = campaignConditionRepo.getByCampaignIdAndEnterpriseId(campaignId, enterpriseId);
			if (condition == null) {
				logger.info("For EnterpriseId {} the campaign {}. there is no campaign condition", campaignId, campaignId);
				response.setStatus(false);
				response.setFailureReason("Campaign doesn't have any conditions");
				return response;
			}
		}
		// check for campaign having upload contact in tags
		OngoingEditCampaignResponse editCampaignResponse = campaignSetupService.getEditOngoingCampaign(enterpriseId, campaignId);
		if (editCampaignResponse == null || editCampaignResponse.getId() == null) {
			logger.error("No campaign found for campaignId {} and enterpriseId {}", campaignId, enterpriseId);
			response.setStatus(false);
			response.setFailureReason("Campaign data not found");
			return response;
		}
		// filter the upload contact campaigns
		if (isUploadContactTag(editCampaignResponse.getTags()) || isUploadContactTag(editCampaignResponse.getExcludeTags())) {
			logger.error("campaign {} is having [upload contact] tag so ignoring this campaign for enterpriseId {}", campaignId, enterpriseId);
			response.setStatus(false);
			response.setFailureReason("Campaign is having [upload contact] tag so ignoring this campaign");
			boolean status = pauseUploadTagsCampaignIfRunning(campaignId);
			if (status) {
				response.setFailureReason("Campaign is having [upload contact] tag was running we marked it paused");
			}
			return response;
		}
		if (customFieldResponse == null) {
			customFieldResponse = getCustomerCustomFieldsData(enterpriseId);
		}
		if (customFieldResponse == null) {
			logger.error("For Enterprise {} No data found for custom fields in Kontacto. So ignoring this campaign", enterpriseId);
			response.setStatus(false);
			response.setFailureReason("Enterprise is not having custom fields in Kontacto so ignoring this campaign");
			return response;
		}
		logger.info("Kontact response tags {}", customFieldResponse);
		Map<String, CustomerCustomFields> nameToFields = customFieldResponse.getCustomFields().stream().collect(Collectors.toMap(CustomerCustomFields::getFieldName, field -> field, (x, y) -> x));
		List<Tag> inclusionTags = convertJsonToTagList(editCampaignResponse.getTags());
		List<Tag> exclusionTags = convertJsonToTagList(editCampaignResponse.getExcludeTags());
		
		// validate the tag ids with CustomerCustomFields
		if (!isTagIdsPresentWithCustomerCustomFields(inclusionTags, nameToFields) || !isTagIdsPresentWithCustomerCustomFields(exclusionTags, nameToFields)) {
			logger.error("For Campaign {} the tag mismatch between Kontacto. So ignoring this campaign for enterprise {}", campaignId, enterpriseId);
			response.setStatus(false);
			response.setFailureReason("The tag mismatch between Kontacto and campaign so ignoring this campaign");
			return response;
		}
		
		// create edit Automation campaign response
		AutomationCampaignRequest campaignRequest = prepareAutomationCampaignRequest(editCampaignResponse, nameToFields, inclusionTags, exclusionTags);
		
		try {
			Boolean result = populateMigratedCampaignData(campaignRequest, enterpriseId, campaignId, editCampaignResponse.getCreatedBy());
			response.setStatus(result);
			logger.info("For campaignId {} and enterpriseId {} the migration done {}", campaignId, campaignId, result);
		} catch (Exception exe) {
			logger.error("error occur while processing the campaign {} for enterprise {}", campaignId, enterpriseId);
			throw exe;
		}
		logger.info("migrateAutomationCampaignForEnterprise response {}", response);
		return response;
	}
	
	private boolean pauseUploadTagsCampaignIfRunning(Integer campaignId) {
		Campaign campaign = campaignRepo.getValidCampaignById(campaignId);
		if (campaign != null && campaign.getStatus() != null && campaign.getStatus() == 1) {
			int row = campaignRepo.pauseAutomationCampaign(campaignId, 2);
			logger.info("For campaign {} row updated {}", campaignId, row);
			return row > 0;
		}
		return false;
	}
	
	private boolean isTagIdsPresentWithCustomerCustomFields(List<Tag> tags, Map<String, CustomerCustomFields> nameToFields) {
		if (CollectionUtils.isEmpty(tags))
			return true;
		for (Tag tag : tags) {
			if (!nameToFields.containsKey(tag.getName())) {
				logger.error("The tag {} not present in kontacto {} ", tag.getName(), nameToFields.keySet());
				return false;
			}
		}
		return true;
	}
	
	private CustomerCustomFieldResponse getCustomerCustomFieldsData(Integer enterpriseId) {
		CustomerCustomFieldResponse customFieldResponse = contactExternalService.getCustomerCustomFields(enterpriseId);
		if (customFieldResponse == null || customFieldResponse.getCount() == 0 || CollectionUtils.isEmpty(customFieldResponse.getCustomFields())) {
			logger.error("For Enterprise {} No data found for custom fields in Kontacto", enterpriseId);
			return null;
		}
		return customFieldResponse;
	}
	
	@SuppressWarnings("rawtypes")
	private boolean isUploadContactTag(List<Tag> tags) {
		if (CollectionUtils.isEmpty(tags))
			return false;
		for (int i = 0; i < tags.size(); i++) {
			Map entry = (Map) tags.get(i);
			if ("Upload contact".equalsIgnoreCase(entry.get("name").toString()))
				return true;
		}
		return false;
	}
	
	private boolean populateMigratedCampaignData(AutomationCampaignRequest campaignRequest, Integer enterpriseId, Integer campaignId, String createdBy) {
		if (campaignRequest == null)
			return false;
		automationCampaignSetupService.createOrUpdateAutomationCampaign(campaignRequest, enterpriseId, campaignId, createdBy);
		return true;
	}
	
	private AutomationCampaignRequest prepareAutomationCampaignRequest(OngoingEditCampaignResponse from, Map<String, CustomerCustomFields> nameToFields, List<Tag> inclusionTags,
			List<Tag> exclusionTags) {
		AutomationCampaignRequest to = new AutomationCampaignRequest();
		to.setCampaignName(from.getCampaignName());
		to.setCampaignType(from.getCampaignType());
		to.setReminderCount(from.getReminderCount());
		to.setEmailTemplateId(from.getEmailTemplateId());
		to.setTriggerType(from.getEvent());
		to.setExpression(prepareExpression(from.getAnyTagFilter(), from.getNoTagFilter(), inclusionTags, exclusionTags, nameToFields));
		to.setIsDraft(from.getIsDraft());
		to.setLvlAlias(from.getLvlAlias());
		to.setLvlAliasId(from.getLvlAliasId());
		to.setLvlIds(from.getLvlIds());
		to.setPriority(from.getPriority());
		to.setReminderInterval(from.getReminderInterval());
		to.setReminderSubject(from.getReminderSubject());
		to.setSendReminder(from.isSendReminder());
		to.setScheduled(from.getScheduled());
		to.setSchedulingInHours(from.getSchedulingInHours());
		to.setSelectAll(from.isSelectAll());
		to.setSmsTemplateId(from.getSmsTemplateId());
		to.setSources(from.getSources());
		to.setSurveyId(from.getSurveyId());
		to.setStatusId(from.getStatusId());
		return to;
	}
	
	private RuleExpression prepareExpression(Integer anyTagFilter, Integer noTagFilter, List<Tag> inclusionTags, List<Tag> excludeTags, Map<String, CustomerCustomFields> nameToFields) {
		if (noTagFilter != null && noTagFilter == 1) {
			return prepareAnyTagOrNoTagExpression(false, nameToFields);
		} else if (anyTagFilter != null && anyTagFilter == 1) {
			return prepareAnyTagOrNoTagExpression(true, nameToFields);
		} else {
			// prepare inclusion expression
			RuleExpression expression = new RuleExpression();
			int[] conditionCounter = new int[1];
			conditionCounter[0] = 1;
			RuleExpression inclusionExpression = prepareInclusionOrExclusionExpression(inclusionTags, conditionCounter, nameToFields, false);
			RuleExpression exclusionExpression = prepareInclusionOrExclusionExpression(excludeTags, conditionCounter, nameToFields, true);
			
			if (inclusionExpression != null && exclusionExpression != null) {
				Map<String, RuleCondition> inConditions = inclusionExpression.getConditions();
				Map<String, RuleCondition> exConditions = exclusionExpression.getConditions();
				// merge both condition
				Map<String, RuleCondition> conditions = Stream.concat(inConditions.entrySet().stream(), exConditions.entrySet().stream())
						.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
				expression.setConditions(conditions);
				StringBuilder finalExpression = new StringBuilder(inclusionExpression.getExpression());
				finalExpression.append(AND).append(exclusionExpression.getExpression());
				expression.setExpression(finalExpression.toString());
			} else if (inclusionExpression != null) {
				expression.setConditions(inclusionExpression.getConditions());
				expression.setExpression(inclusionExpression.getExpression());
			} else if (exclusionExpression != null) {
				expression.setConditions(exclusionExpression.getConditions());
				StringBuilder finalExpression = new StringBuilder(exclusionExpression.getExpression());
				expression.setExpression(finalExpression.toString());
			} else {
				expression = null;
			}
			return expression;
		}
	}
	
	// [{"id": 972, "name": "Gender", "value": "Male"}, {"id": 1505, "name": "Gender", "value": "female"}, {"id": 1746, "name": "New Group", "value":
	// "abc"}]
	private RuleExpression prepareInclusionOrExclusionExpression(List<Tag> tags, int[] conditionCounter, Map<String, CustomerCustomFields> nameToFields, boolean isExclude) {
		if (CollectionUtils.isEmpty(tags))
			return null;
		
		RuleExpression expression = new RuleExpression();
		Map<String, List<Tag>> tagsByName = tags.stream().collect(Collectors.groupingBy(Tag::getName));
		int size = tagsByName.size();
		StringBuilder exp = new StringBuilder();
		boolean addedOpenBracket = false;
		if (size > 1) {
			addedOpenBracket = true;
			exp.append("(");
		}
		Map<String, RuleCondition> conditions = new HashMap<>();
		for (Entry<String, List<Tag>> entry : tagsByName.entrySet()) {
			Integer fieldId = nameToFields.get(entry.getKey()).getId();
			String dataType = nameToFields.get(entry.getKey()).getType();
			// if we have Tags as field then we have to take the ids for tags
			List<?> values = null;
			if ("Tags".equalsIgnoreCase(entry.getKey())) {
				values = new ArrayList<>(Arrays.asList(fieldId));
			} else {
				Map<String, String> valuesMap = entry.getValue().stream().collect(Collectors.toMap(t -> t.getValue().toLowerCase(), Tag::getValue, (x, y) -> x));
				values = new ArrayList<>(valuesMap.values());
			}
			RuleCondition condition;
			if (values.size() == 1) {
				if (isExclude) {
					condition = new RuleCondition(fieldId, entry.getKey(), "NOT_EQUALS_TO", dataType, values);
				} else {
					condition = new RuleCondition(fieldId, entry.getKey(), "EQUALS_TO", dataType, values);
				}
			} else {
				if (isExclude) {
					condition = new RuleCondition(fieldId, entry.getKey(), "NOT_IN", dataType, values);
				} else {
					condition = new RuleCondition(fieldId, entry.getKey(), "IN", dataType, values);
				}
			}
			exp.append(conditionCounter[0]);
			if (size > 1) {
				if (isExclude) {
					exp.append(AND);
				} else {
					exp.append(" OR ");
				}
			}
			conditions.put(String.valueOf(conditionCounter[0]), condition);
			conditionCounter[0]++;
			size--;
		}
		if (addedOpenBracket) {
			exp.append(")");
		}
		expression.setExpression(exp.toString());
		expression.setConditions(conditions);
		return expression;
	}
	
	private RuleExpression prepareAnyTagOrNoTagExpression(boolean anyTag, Map<String, CustomerCustomFields> nameToFields) {
		if (MapUtils.isEmpty(nameToFields))
			return null;
		
		RuleExpression expression = new RuleExpression();
		int size = nameToFields.size();
		StringBuilder exp = new StringBuilder();
		boolean addedOpenBracket = false;
		if (size > 1) {
			addedOpenBracket = true;
			exp.append("(");
		}
		Map<String, RuleCondition> conditions = new HashMap<>();
		int counter = 1;
		for (Entry<String, CustomerCustomFields> entry : nameToFields.entrySet()) {
			RuleCondition condition;
			if (anyTag) {
				condition = new RuleCondition(entry.getValue().getId(), entry.getKey(), "NOT_BLANK", entry.getValue().getType(), new String[] { null });
			} else {
				condition = new RuleCondition(entry.getValue().getId(), entry.getKey(), "BLANK", entry.getValue().getType(), new String[] { null });
			}
			exp.append(counter);
			if (size > 1) {
				if (anyTag) {
					exp.append(" OR ");
				} else {
					exp.append(AND);
				}
			}
			conditions.put(String.valueOf(counter), condition);
			counter++;
			size--;
		}
		if (addedOpenBracket) {
			exp.append(")");
		}
		expression.setExpression(exp.toString());
		expression.setConditions(conditions);
		return expression;
	}
	
	@SuppressWarnings("rawtypes")
	private List<Tag> convertJsonToTagList(List<Tag> inputTags) {
		if (CollectionUtils.isEmpty(inputTags)) {
			return Collections.emptyList();
		}
		List<Tag> tags = new ArrayList<>();
		for (int i = 0; i < inputTags.size(); i++) {
			Map entry = (Map) inputTags.get(i);
			// filter upload contact
			if (!"Upload contact".equalsIgnoreCase(entry.get("name").toString())) {
				// to handle the duplicate values like // male, MALE
				Tag tag = new Tag((int) entry.get("id"), entry.get("name").toString(), entry.get("value").toString());
				tags.add(tag);
			}
		}
		return tags;
	}
	
	@Override
	public List<AutomationCampaignMigrationResponse> migrateAutomationCampaignsForEnterprise(Integer enterpriseId) {
		if (enterpriseId == null)
			return Collections.emptyList();
		List<AutomationCampaignMigrationResponse> response = new ArrayList<>();
		List<Integer> platformcampaignIds = campaignRepo.getOngoingCampaignsIdsByEnterpriseId(enterpriseId);
		if (CollectionUtils.isEmpty(platformcampaignIds)) {
			AutomationCampaignMigrationResponse obj = new AutomationCampaignMigrationResponse();
			obj.setEnterpriseId(enterpriseId);
			obj.setStatus(false);
			obj.setFailureReason("No campaign Found for this enterprise");
			response.add(obj);
			return response;
		}
		// filter these campaign ids based on the comapign conditions
		List<Integer> campaignIds = campaignConditionRepo.getCampaignIdsByEnterpriseId(platformcampaignIds, enterpriseId);
		if (CollectionUtils.isEmpty(campaignIds)) {
			AutomationCampaignMigrationResponse obj = new AutomationCampaignMigrationResponse();
			obj.setEnterpriseId(enterpriseId);
			obj.setStatus(false);
			obj.setFailureReason("No campaign Found for this enterprise");
			response.add(obj);
			return response;
		}
		CustomerCustomFieldResponse customFieldResponse = getCustomerCustomFieldsData(enterpriseId);
		campaignIds.stream().forEach(campaignId -> response.add(migrateAutomationCampaignForEnterprise(enterpriseId, campaignId, false, customFieldResponse)));
		logger.info("migrateAutomationCampaignsForEnterprise response {}", response);
		return response;
	}
	
	@Override
	public Map<Integer, List<AutomationCampaignMigrationResponse>> migrateBulkAutomationCampaignsForEnterpriseIds(List<Integer> enterpriseIds) {
		if (CollectionUtils.isEmpty(enterpriseIds)) {
			Collections.emptyMap();
		}
		if (enterpriseIds.size() > 200) {
			logger.error("Size {} should be less than 200", enterpriseIds.size());
			Collections.emptyMap();
		}
		Map<Integer, List<AutomationCampaignMigrationResponse>> response = new HashMap<>();
		Set<Integer> accounts = new HashSet<>(enterpriseIds);
		accounts.stream().forEach(enterpriseId -> response.put(enterpriseId, migrateAutomationCampaignsForEnterprise(enterpriseId)));
		try {
			logger.info("migrateBulkAutomationCampaignsForEnterpriseIds response {}", (new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).writeValueAsString(response)));
		} catch (JsonProcessingException e) {
			logger.error("error while logging the response in JSON {}", response);
		}
		return response;
	}
	
	@Async
	@Override
	public void updateDefaultReviewSourcesForBusinessIds(List<Long> longBusinessIds, List<Integer> sourceIds) {
		if (CollectionUtils.isEmpty(sourceIds) || CollectionUtils.size(sourceIds) > 3) {
			logger.info("Either source ids {} are empty or more than the 3", sourceIds);
			return;
		}
		if (CollectionUtils.isEmpty(longBusinessIds) || CollectionUtils.size(longBusinessIds) > 100) {
			logger.info("Either business ids {} are empty or more than the 100", longBusinessIds);
			return;
		}
		longBusinessIds.stream().forEach(longBusinessId -> updateDefaultReviewSourcesForBusinessIds(longBusinessId, sourceIds));
	}
	
	private void updateDefaultReviewSourcesForBusinessIds(Long longBusinessId, List<Integer> sourceIds) {
		BusinessEnterpriseEntity business = businessReadOnlyRepo.getBusinessByBusinessLongId(longBusinessId);
		if (business == null) {
			logger.error("Invalid business number {}", longBusinessId);
			return;
		}
		List<ReviewSourceSRO> allReviewSources = reviewSourcesService.getAllReviewSources(business.getId());
		if (CollectionUtils.isEmpty(allReviewSources)) {
			logger.error("No aggregated review source found for business : {}", business.getId());
			return;
		}
		Map<Integer, ReviewSourceSRO> filteredSourcesMap = allReviewSources.stream().filter(r -> (sourceIds.contains(r.getSourceId())))
				.collect(Collectors.toMap(ReviewSourceSRO::getSourceId, p -> p, (first, second) -> second));
		//List<Campaign> defaultCampaigns = campaignRepo.getDefaultOngoingCampaignByEnterpriseId(business.getId());
		DefaultTemplateDTO defaultTemplateDTO = defaultTemplateService.getDefaultTemplates(business.getId(), TemplateTypeEnum.REVIEW_REQUEST_NEW.getName(), Constants.EMAIL_AND_SMS);
		if (defaultTemplateDTO == null) {
			logger.error("No default template found for business : {}", business.getId());
			return;
		}
		//Campaign defaultCampaign = defaultCampaigns.get(0);
		Integer priority = 0;
		List<ReviewSourceSRO> reviewSources = new ArrayList<>();
		for (Integer key : filteredSourcesMap.keySet()) {
			ReviewSourceSRO reviewSource = filteredSourcesMap.get(key);
			reviewSource.setPriority(++priority);
			reviewSources.add(reviewSource);
		}
		if (CollectionUtils.isEmpty(reviewSources)) {
			//logger.error("No final review sources found for business : {} & campaignId : {}", business.getId(), defaultCampaign.getId());
			logger.error("No final review sources found for business : {} {}", business.getId());
			return;
		}
		
		if (defaultTemplateDTO.getEmailTemplateId() != null) {
			logger.info("Update business deeplink priorities for email template id : {}", defaultTemplateDTO.getEmailTemplateId());
			updateEmailTemplateSourcesAndSendEvent(business.getId(), defaultTemplateDTO, reviewSources);
		}
		if (defaultTemplateDTO.getSmsTemplateId() != null) {
			logger.info("Update business deeplink priorities for sms template id : {}", defaultTemplateDTO.getSmsTemplateId());
			updateSmsTemplateSourcesAndSendEvent(business.getId(), defaultTemplateDTO, reviewSources);
		}
	}
	
	@Override
	public DefaultReviewSourceResponse getDefaultReviewSourcesForBusinessIds(List<Long> longBusinessIds) {
		if (CollectionUtils.isEmpty(longBusinessIds) || CollectionUtils.size(longBusinessIds) > 100) {
			logger.info("Either business ids {} are empty or more than the 100", longBusinessIds);
			return null;
		}
		Map<Long, DefaultReviewSource> businessToSourceMap = new HashMap<Long, DefaultReviewSource>();
		longBusinessIds.stream().forEach(businessId -> getDefaultReviewSourcesForBusiness(businessId, businessToSourceMap));
		DefaultReviewSourceResponse response = new DefaultReviewSourceResponse();
		response.setReviewSources(businessToSourceMap);
		return response;
	}
	
	private void getDefaultReviewSourcesForBusiness(Long businessId, Map<Long, DefaultReviewSource> businessToSourceMap) {
		BusinessEnterpriseEntity business = cacheService.getBusinessByBusinessNumber(businessId);
		if (business == null) {
			logger.error("Invalid business number {}", businessId);
			return;
		}
		
		//List<Campaign> defaultCampaigns = cacheService.getDefaultOngoingCampaignByEnterpriseId(business.getId());
		DefaultTemplateDTO defaultTemplateDTO = defaultTemplateService.getDefaultTemplates(business.getId(), TemplateTypeEnum.REVIEW_REQUEST_NEW.getName(), Constants.EMAIL_AND_SMS);
		if (defaultTemplateDTO == null) {
			logger.error("No default templates found for business : {}", business.getId());
			return;
		}
		//Campaign defaultCampaign = defaultCampaigns.get(0);
		List<BusinessDeeplinkPriority> emailSources = null;
		List<BusinessDeeplinkPriority> smsSourcesAndroid = null;
		List<BusinessDeeplinkPriority> smsSourcesIOS = null;
		if (defaultTemplateDTO.getEmailTemplateId() != null) {
			logger.info("Getting business deeplink priorities for email template id : {}", defaultTemplateDTO.getEmailTemplateId());
			emailSources = businessDeeplinkPriorityDaoService.getDeeplinksByTemplateIdAndSourceCached(defaultTemplateDTO.getEmailTemplateId(), Constants.DEVICE_TYPE_WEB);
		}
		if (defaultTemplateDTO.getSmsTemplateId() != null) {
			logger.info("Getting business deeplink priorities for sms template id : {}",  defaultTemplateDTO.getSmsTemplateId());
			smsSourcesIOS = businessDeeplinkPriorityDaoService.getDeeplinksByTemplateIdAndSourceCached(defaultTemplateDTO.getSmsTemplateId(), Constants.DEEPLINK_DEVICETYPE_IOS);
			smsSourcesAndroid = businessDeeplinkPriorityDaoService.getDeeplinksByTemplateIdAndSourceCached(defaultTemplateDTO.getSmsTemplateId(), Constants.DEEPLINK_DEVICETYPE_ANDROID);
		}
		
		DefaultReviewSource defaultReviewSource = createDefaultReviewSource(emailSources, smsSourcesAndroid, smsSourcesIOS);
		businessToSourceMap.put(businessId, defaultReviewSource);
	}
	
	private DefaultReviewSource createDefaultReviewSource(List<BusinessDeeplinkPriority> emailSources, List<BusinessDeeplinkPriority> smsSourcesAndroid, List<BusinessDeeplinkPriority> smsSourcesIOS) {
		
		DefaultReviewSource defaultReviewSource = new DefaultReviewSource();
		List<Integer> emailSourceIds = (CollectionUtils.isNotEmpty(emailSources)) ? emailSources.stream().map(source -> source.getSourceId()).collect(Collectors.toList()) : null;
		List<Integer> smsSourceAndroidIds = (CollectionUtils.isNotEmpty(smsSourcesAndroid)) ? smsSourcesAndroid.stream().map(source -> source.getSourceId()).collect(Collectors.toList()) : null;
		List<Integer> smsSourceIOSIds = (CollectionUtils.isNotEmpty(smsSourcesIOS)) ? smsSourcesIOS.stream().map(source -> source.getSourceId()).collect(Collectors.toList()) : null;
		defaultReviewSource.setEmailSources(emailSourceIds);
		defaultReviewSource.setTextAndroidSources(smsSourceAndroidIds);
		defaultReviewSource.setTextIOSources(smsSourceIOSIds);
		return defaultReviewSource;
	}
	
	@Override
	public void updateInvalidAppointmentData(Integer enterpriseId) {
		if (enterpriseId != null) {
			updateInvalidAppointmentDataUtil(enterpriseId);
		} else {
			List<Integer> enterpriseIds = referralAppointmentService.getEnterpriseIdsForInvalidAppointments();
			logger.info("{} enterprise id(s) found with invalid appointments", enterpriseIds.size());
			enterpriseIds.forEach(id -> updateInvalidAppointmentDataUtil(id));
		}
	}
	
	private void updateInvalidAppointmentDataUtil(Integer enterpriseId) {
		List<AppointmentDTO> appointments = referralAppointmentService.getInvalidAppointmentsByEnterpriseId(enterpriseId);
		if (!appointments.isEmpty()) {
			logger.info("Updating invalid data of {} appointments for enterpriseId : {}", appointments.size(), enterpriseId);
			
			List<List<Integer>> referrerIdBatches = Lists.partition(appointments.stream().map(AppointmentDTO::getReferrerId).distinct().collect(Collectors.toList()), APPOINTMENT_MIGRATION_BATCH_SIZE);
			referrerIdBatches.forEach(referrerIds -> referralAppointmentService.updateReferrerInfo(referrerIds));
			List<List<Integer>> cidBatches = Lists.partition(appointments.stream().map(AppointmentDTO::getCid).distinct().collect(Collectors.toList()), APPOINTMENT_MIGRATION_BATCH_SIZE);
			cidBatches.forEach(cids -> referralAppointmentService.updateLeadReferralCode(cids));
		}
	}
	
	@Override
	public void sendLeadsToKontacto(Integer startId, Integer endId) {
		Integer maxAppointmentId = referralAppointmentService.getMaxAppointmentId();
		if (startId == null && endId == null) {
			logger.info("Sending details for all leads to Kontacto");
			startId = 1;
			endId = Integer.MAX_VALUE;
		} else if ((startId != null && endId == null) || (startId == null) || (startId > endId) || (startId > maxAppointmentId)) {
			logger.error("Received invalid range for sending leads to Kontacto - startId : {}, endId : {}", startId, endId);
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, ErrorCodes.INVALID_REQUEST.getMessage());
		}
		endId = Math.min(endId, maxAppointmentId);
		Integer batchSize = APPOINTMENT_MIGRATION_BATCH_SIZE;
		Integer batchStartId = startId;
		Integer batchEndId = Math.min(startId + batchSize - 1, endId);
		while (batchStartId <= endId) {
			referralAppointmentService.sendLeadsToKontactoInBatches(batchStartId, batchEndId);
			batchStartId = batchEndId + 1;
			batchEndId = Math.min(batchStartId + batchSize - 1, endId);
		}
	}
	
	@Override
	public void migrateAppointmentDataToES(Integer startId, Integer endId) {
		Integer maxAppointmentId = referralAppointmentService.getMaxAppointmentId();
		if (startId == null && endId == null) {
			logger.info("Migrating all appointment data to ES");
			startId = 1;
			endId = Integer.MAX_VALUE;
		} else if ((startId != null && endId == null) || (startId == null) || (startId > endId) || (startId > maxAppointmentId)) {
			logger.error("Received invalid range for migrating appointment data - startId : {}, endId : {}", startId, endId);
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, ErrorCodes.INVALID_REQUEST.getMessage());
		}
		endId = Math.min(endId, maxAppointmentId);
		Integer batchSize = APPOINTMENT_MIGRATION_BATCH_SIZE;
		Integer batchStartId = startId;
		Integer batchEndId = Math.min(startId + batchSize - 1, endId);
		while (batchStartId <= endId) {
			referralAppointmentService.migrateAppointmentDataToESInBatches(batchStartId, batchEndId);
			batchStartId = batchEndId + 1;
			batchEndId = Math.min(batchStartId + batchSize - 1, endId);
		}
	}
	@Override
	public void migrateQRDataFromOneBusinessToAnotherBusiness(BusinessMigrationEventRequest request) {
		if (request == null) {
			logger.error("migrate QR Config DataFromOneBusinessToAnotherBusiness : invalid BusinessMigration request received");
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "businessMigrationEventRequest cannnot be null");
		}
		if (isSmbToEnterpriseUpgrade(request)) {
			// 1. migrate email template
			campaignTemplateDataGateway.migrateEmailTemplates(request.getSourceBusinessId(), request.getTargetEnterpriseId());
			// 2. migrate QR Config mapping for template review sources 
			campaignTemplateDataGateway.migrateQRCodeConfigMapping(request.getSourceBusinessId(), request.getTargetEnterpriseId());
			
		}
		// TODO Need Discussion with Amit Kushwaha 
		else if (isBusinessDowngrade(request)) {
			if (BusinessEventEnum.DOWNGRADE.getEventType().equalsIgnoreCase(request.getEventType()) && !request.getSourceEnterpriseNumber().equals(request.getTargetBusinessNumber())) {
				logger.info("Request received to migrate QR Config Data - Enterprise Location to SMB conversion {}", request);
				BusinessEnterpriseEntity business = cacheService.getBusinessById(request.getTargetBusinessId());
				if (business == null || !business.getClosed().equals(Integer.valueOf(0))) {
					logger.error("migrateQRConfigDataFromOneBusinessToAnotherBusiness : No valid business found for business id {},eventId {}", request.getTargetBusinessId(), request.getEventId());
					usageCommunicationConsumerService.pushAuditEventToKafka(request.getEventId(), Constants.CAMPAIGN_AUDIT_MODULE_INVALID_EVENT, Constants.FAILURE_STATUS);
					return;
				}
				Integer defaultUserId = Integer.valueOf(1946);
				businessOnboardingService.createDefaultTemplatesAndCampaignsForABusiness(request.getTargetBusinessId(), request.getUserId() != null ? request.getUserId() : defaultUserId);
				enableCampaignFeatureForBusiness(business, request.getUserId() != null ? request.getUserId() : defaultUserId);
			}
			kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CAMPAIGN_USAGE_MIGRATE, new KafkaMessage(request.getEventId(), request));
		} else {
			logger.error("Migrate QR Config data for Business {} - invalid event received for eventId {}", request.getEventType(), request.getEventId());
			usageCommunicationConsumerService.pushAuditEventToKafka(request.getEventId(), Constants.CAMPAIGN_AUDIT_MODULE_INVALID_EVENT, Constants.FAILURE_STATUS);
		}
	}

	/**
	 * 
	 */
	@Override
	public void migrateAutomationScheduling(List<Integer> campaignIds) {
		List<Campaign> campaigns = campaignRepo.findAllById(campaignIds);
		for(Campaign campaign : campaigns) {
			if (StringUtils.equalsIgnoreCase(campaign.getRunType(), "ongoing") && campaign.getSchedule() > 100) {
				CampaignCondition campaignCondition = campaignConditionRepo.getByCampaignId(campaign.getId());
				List<AppointmentScheduleInfo> appointmentScheduleInfos = new ArrayList<>();
				AppointmentScheduleInfo appointmentScheduleInfo = new AppointmentScheduleInfo();
				if (campaign.getSchedulingInHours() != null) {
					if (campaign.getSchedulingInHours() == 0) {
						Integer days = campaign.getSchedule() / 24;
						if (days <= 100) {
							appointmentScheduleInfo.setScheduled(days);
							appointmentScheduleInfo.setScheduleBy(GroupByTimeEnum.DAYS.getGroupBy());
						}
						Integer weekModulo = Math.min(days % 7, (7 - days) % 7);
						Integer monthModulo = Math.min(days % 30, (7 - days) % 30);
						if (weekModulo < monthModulo) {
							appointmentScheduleInfo.setScheduled(Math.round(days/7f));
							appointmentScheduleInfo.setScheduleBy(GroupByTimeEnum.WEEKS.getGroupBy());
						} else {
							appointmentScheduleInfo.setScheduled(Math.round(days/30f));
							appointmentScheduleInfo.setScheduleBy(GroupByTimeEnum.MONTHS.getGroupBy());
						}
					} else {
						appointmentScheduleInfo.setScheduled(campaign.getSchedule());
						appointmentScheduleInfo.setScheduleBy(GroupByTimeEnum.DAYS.getGroupBy());
					}
				} else {
					Integer days = campaign.getSchedule();
					Integer weekModulo = Math.min(days % 7, (7 - days) % 7);
					Integer monthModulo = Math.min(days % 30, (7 - days) % 30);
					if (weekModulo < monthModulo) {
						appointmentScheduleInfo.setScheduled(Math.round(days/7f));
						appointmentScheduleInfo.setScheduleBy(GroupByTimeEnum.WEEKS.getGroupBy());
					} else {
						appointmentScheduleInfo.setScheduled(Math.round(days/30f));
						appointmentScheduleInfo.setScheduleBy(GroupByTimeEnum.MONTHS.getGroupBy());
					}
				}
				appointmentScheduleInfo.setSendOrder("after");
				appointmentScheduleInfos.add(appointmentScheduleInfo);
				campaignCondition.setAppointmentScheduleInfo(appointmentScheduleInfos);
				campaignConditionRepo.saveAndFlush(campaignCondition);
			}
		}
	}
	
	/**
	 *
	 * Validate request and update owner for given automations and split campaigns for the given account
	 * 
	 * @param request
	 */
	@Override
	public void updateCampaignAccessSettingsForAccount(AccessSettingsUpdateRequest request) {
		if (request == null || request.getAccountId() == null) {
			logger.error("updateCampaignAccessSettingsForAccount :: Invalid request");
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "Invalid request");
		}
		
		BusinessEnterpriseEntity business = cacheService.getValidBusinessById(request.getAccountId());
		if (BooleanUtils.isFalse(BusinessUtils.isValidEnterprise(business)) && BooleanUtils.isFalse(BusinessUtils.isValidSMBBusiness(business))) {
			logger.error("updateCampaignAccessSettingsForAccount :: Invalid account id in request {}", request);
			throw new CampaignException(ErrorCodes.INVALID_BUSINESS, "Invalid business as business is inactive");
		}
		
		ProductFeatureRequest productFeatureRequest = cacheService.getProductFeatureForBusiness(request.getAccountId());
		if (BooleanUtils.isFalse(CoreUtils.getBooleanValueFromInteger(productFeatureRequest.getCampaignLocationUserAccess()))) {
			logger.error("updateCampaignAccessSettingsForAccount :: Invalid account id in request, as location user access setting not enabled for this account {}", request);
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "Invalid request as user access flag is not enabled");
		}
		
		userAccessSettingsService.migrateAccessSettingsForAccount(request);
	}
}