package com.birdeye.campaign.utils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.OperandDataType;
import com.birdeye.campaign.dto.CustomField;
import com.birdeye.campaign.dto.CustomFieldEvalRequest;
import com.birdeye.campaign.dto.CustomFieldEvalRequest.CustomFieldDetails;
import com.birdeye.campaign.dto.RuleCondition;
import com.birdeye.campaign.dto.RuleExpression;
import com.birdeye.campaign.dto.TriggerFilter;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.CampaignCondition;
import com.birdeye.campaign.enums.CampaignRunTypeEnum;
import com.birdeye.campaign.platform.constant.CampaignTypeEnum;
import com.birdeye.campaign.request.Tag;
import com.birdeye.campaign.rule.utils.RuleEngine;

public class MvelEvaluationUtils {
	private static final Logger logger = LoggerFactory .getLogger(MvelEvaluationUtils.class);
	
	private static final String	TAGS							= "$Tags";
	
	private static final String	CURRENCY_SYMBOL					= "$currencySymbol";
	
	private static final String	LIST_TEXT						= "list_text";
	
	private static final String	CURRENCY_WORD					= "currency";
	
	public static final  String WROTE_A_REVIEW_OPERAND          = "$Wrote_a_review";
		
	private MvelEvaluationUtils() {
		
	}
	
	public static boolean evaluateTriggerMvelExpression(String mvelExpression, Map<String, String> triggerMvelParamsAndTypes, List<TriggerFilter> triggerFilters, boolean pmsRecallRequest, RuleExpression ruleExpression) {
		// if we don't have any condition then whatever is the input we will pass
		if (StringUtils.isBlank(mvelExpression)) {
			return true;
		}
		// get custom field data
		// get source data
		
		if (StringUtils.isNotBlank(mvelExpression) && MapUtils.isNotEmpty(triggerMvelParamsAndTypes) && (CollectionUtils.isNotEmpty(triggerFilters))) {
			
			// getting trigger filters
			Map<String, TriggerFilter> triggerFilterMap = null;
			if (CollectionUtils.isNotEmpty(triggerFilters)) {
				triggerFilterMap = triggerFilters.stream().collect(Collectors.toMap(field -> CoreUtils.convertToValidIdentifier(field.getName()), field -> field, (x, y) -> x));
			}
			final Map<String, Object> mvelTokens = new HashMap<>();
			for (String param : triggerMvelParamsAndTypes.keySet()) {
				// Ignore validation of appointment status if the request is a PMS Recall
				if(pmsRecallRequest && StringUtils.equalsIgnoreCase(param, "$Appointment_status")) {
					logger.info("Ignoring campaign appointment status for PMS recall : {}", pmsRecallRequest);
					if(containsOtherThanAnyOfOperatorForAppointmentStatusFilter(ruleExpression)) {
						// Set 'ignoreStatus' if the rule expression contains operators other than 'ANY_OF'
						TriggerFilter triggerFilter = triggerFilterMap.get(param);
						triggerFilter.setValue(Arrays.asList("ignoreStatus"));
						triggerFilterMap.put("$Appointment_status", triggerFilter);
					} else {
						String appointmentStatus = getCampaignConditionAppointmentStatus(ruleExpression);
						if(StringUtils.isNotEmpty(appointmentStatus)) {
							TriggerFilter triggerFilter = triggerFilterMap.get(param);
							triggerFilter.setValue(Arrays.asList(appointmentStatus));
							triggerFilterMap.put("$Appointment_status", triggerFilter);
						}
					}
					
				}
				if (triggerFilterMap != null && triggerFilterMap.containsKey(param)) {
					setDataValue(param, triggerFilterMap.get(param).getValue(), triggerFilterMap.get(param).getType(), mvelTokens, true);
					// Special Handling for type -> list_text
					if (LIST_TEXT.equalsIgnoreCase(triggerMvelParamsAndTypes.get(param)) && triggerFilterMap.get(param).getValue() == null) {
						mvelTokens.put(param, Collections.emptyList());
					}
				} else {
					setEmptyValue(param, triggerMvelParamsAndTypes.get(param), mvelTokens);
				}
			}
			// for all tag ids check expression and if result become true break and then return that result
			return RuleEngine.evalMvelExpression(mvelExpression, mvelTokens);
		}
		return false;
	}
	
	private static void setDataValue(String param, Object values, String type, Map<String, Object> mvelTokens, Boolean isTriggerMvelExpressionEval) {
		if (values == null) {
			mvelTokens.put(param, null);
			return;
		}
		if (values instanceof List) {
			Object val = getSingleObject(values);
			switch (OperandDataType.getOperatorDataType(type)) {
			case TEXT:
			case TEXT_MULTI:
				setStringValues(param, val, mvelTokens);
				break;
			case NUMBER:
			case NUMBER_SINGLE:
			case TIME:
				setNumberValues(param, val, mvelTokens);
				break;
			case CURRENCY:
				setCurrencyValues(param, val, mvelTokens);
				break;
			case DATE:
				setDateValues(param, val, mvelTokens);
				break;
			case YESNO:
				setYesNoValues(param, val, mvelTokens);
				break;
			case LIST_TEXT:
				setListTextValues(param, val, mvelTokens, isTriggerMvelExpressionEval);
			default:
				break;
			}
		}
	}

	private static void setStringValues(String param, Object val, Map<String, Object> mvelTokens) {
		mvelTokens.put(param, StringUtils.lowerCase(String.valueOf(val)));
	}
	
	private static void setNumberValues(String param, Object val, Map<String, Object> mvelTokens) {
		// if (val instanceof Integer) {
		// mvelTokens.put(param, (Integer) val);
		// } else {
		mvelTokens.put(param, val);
		// }
	}
	
	private static void setDateValues(String param, Object val, Map<String, Object> mvelTokens) {
		// if (val instanceof Long) {
		// mvelTokens.put(param, (Long) val);
		// } else {
		//Extracting epochTime in ms from val
		Long epochTimeInMs = getValidLongValue(val);
		//Converting the epochTime to start of the day thus matching the format as received from UI
		val = convertToStartOfDay(epochTimeInMs);
		mvelTokens.put(param, val);
		// }
	}
	
	public static boolean evaluateMvelExpression(String mvelExpression, Map<String, String> mvelParamsAndTypes, List<CustomField> inputCustomFields, List<Tag> inputTags,
			List<Integer> reviewSourceIds) {// NOSONAR
		// if we don't have any condition then whatever is the input we will pass
		if (StringUtils.isBlank(mvelExpression)) {
			return true;
		}
		// get all tags data
		// get custom field data
		// get source data
		if (MapUtils.isNotEmpty(mvelParamsAndTypes)) {
			
			// getting custom fields
			Map<String, CustomField> customFields = null;
			if (CollectionUtils.isNotEmpty(inputCustomFields)) {
				customFields = inputCustomFields.stream().collect(Collectors.toMap(field -> CoreUtils.convertToValidIdentifier(field.getName()), field -> field, (x, y) -> x));
			}
			List<Integer> tagIds = null;
			final Map<String, Object> mvelTokens = new HashMap<>();
			for (String param : mvelParamsAndTypes.keySet()) {
				// check for tags
				if (TAGS.equalsIgnoreCase(param)) {
					if (CollectionUtils.isNotEmpty(inputTags)) {
						tagIds = inputTags.stream().map(Tag::getId).collect(Collectors.toList());
					} else {
						mvelTokens.put(TAGS, null);
					}
				} else if (customFields != null && customFields.containsKey(param)) {
					setDataValue(param, customFields.get(param).getValue(), customFields.get(param).getType(), mvelTokens, false);
					// special Handling for Currency
					if (CURRENCY_WORD.equalsIgnoreCase(customFields.get(param).getType())) {
						mvelTokens.put(CURRENCY_SYMBOL, customFields.get(param).getSubType() == null ? "USD" : customFields.get(param).getSubType());
					}
					// Special Handling for type -> list_text
					if (LIST_TEXT.equalsIgnoreCase(mvelParamsAndTypes.get(param)) && customFields.get(param).getValue() == null) {
						mvelTokens.put(param, Collections.emptyList());
					}
				} else if (StringUtils.equals(param, WROTE_A_REVIEW_OPERAND) && CollectionUtils.isNotEmpty(reviewSourceIds)) {
					// BIRD-32475 | Match wrote a review param with review sources in event request and based on it, if condition matches, campaign will be sent
					setDataValue(param, CampaignUtils.prepareValueForListStringDataType(reviewSourceIds), mvelParamsAndTypes.get(param), mvelTokens, false);
				} else {
					if (CURRENCY_WORD.equalsIgnoreCase(mvelParamsAndTypes.get(param))) {
						mvelTokens.put(CURRENCY_SYMBOL, null);
					}
					mvelTokens.put(param, null);
					// Special Handling for type -> list_text
					if (LIST_TEXT.equalsIgnoreCase(mvelParamsAndTypes.get(param))) {
						mvelTokens.put(param, Collections.emptyList());
					}
				}
			}
			
			// for all tag ids check expression and if result become true break and then return that result
			if (CollectionUtils.isEmpty(tagIds)) {
				return RuleEngine.evalMvelExpression(mvelExpression, mvelTokens);
			} else {
				for (Integer tagId : tagIds) {
					mvelTokens.put(TAGS, tagId);
					if (RuleEngine.evalMvelExpression(mvelExpression, mvelTokens)) {
						return true;
					}
				}
			}
		}
		return false;
	}

	public static Long getValidLongValue(Object val) {
		Long epochTimeInMs = null;
		
		if(val!=null && val instanceof Number) {
			epochTimeInMs = ((Number) val).longValue();
			
		}else if(val!=null && val instanceof String) {
			epochTimeInMs = Long.parseLong((String) val);
		} else {
			throw new IllegalArgumentException("Input date is not a Number or a String. Date : "+ val);
		}
		return epochTimeInMs;
	}
	
	public static Object convertToStartOfDay(Long epochTimeInMs) {
		logger.info("[MvelEvaluationUtils] Changing the epoch date time: {} to start of the day!", epochTimeInMs);
		LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(epochTimeInMs), ZoneId.of("UTC"));
		LocalDateTime startOfDay = LocalDateTime.of(dateTime.toLocalDate(), LocalDateTime.MIN.toLocalTime());
		Instant startOfDayInstant = startOfDay.atZone(ZoneId.of("UTC")).toInstant();
		return startOfDayInstant.toEpochMilli();
	}

	private static void setYesNoValues(String param, Object value, Map<String, Object> mvelTokens) {
		if (value instanceof Boolean) {
			mvelTokens.put(param, value);
		} else if (value instanceof String) {
			String val = (String) value;
			if ("Yes".equalsIgnoreCase(val) || "ON".equalsIgnoreCase(val) || "1".equalsIgnoreCase(val)) {
				mvelTokens.put(param, true);
			} else if ("No".equalsIgnoreCase(val) || "Off".equalsIgnoreCase(val) || "0".equalsIgnoreCase(val)) {
				mvelTokens.put(param, false);
			} else {
				mvelTokens.put(param, value);
			}
		} else if (value instanceof Integer) {
			Integer val = (Integer) value;
			if (1 == val) {
				mvelTokens.put(param, true);
			} else {
				mvelTokens.put(param, false);
			}
		}
	}
	
	private static Object getSingleObject(Object values) {
		if (values instanceof List) {
			return ((List<?>) values).get(0);
		}
		return values;
	}
	
	private static void setCurrencyValues(String param, Object value, Map<String, Object> mvelTokens) {
		if (value instanceof Integer) {
			double val = (Integer) value;
			mvelTokens.put(param, Double.valueOf(val));
		} else if (value instanceof String) {
			String val = (String) value;
			mvelTokens.put(param, Double.valueOf(val));
		} else {
			mvelTokens.put(param, value);
		}
	}
	
	private static void setListTextValues(String param, Object val, Map<String, Object> mvelTokens) {
		if (val instanceof List<?>) { // Check if val is a list
            List<?> originalList = (List<?>) val;

            // Convert the list elements to lowercase using Java Streams
            List<String> lowercaseList = originalList.stream()
                    .map(obj -> (String) obj) // Cast each object to String
                    .map(String::toLowerCase)
                    .collect(Collectors.toList());

            // Put the lowercase list in the MVEL tokens map
            mvelTokens.put(param, lowercaseList);
        }
	}
	
	/**
	 * 
	 * splits the input string on basis of delimiter and add the elements to the list.
	 * 
	 * @param String
	 * @return List<String>
	 */
	private static List<String> prepareListFromStringObject(String inputStringToSplit) {
		List<String> elements = Arrays.stream(inputStringToSplit.split(";")).collect(Collectors.toList());
		return elements;
	}
	
	/**
	 * Prepare and Set values for type ListText in mvelTokens map
	 * 
	 * @param param,val,
	 *            mvelTokens
	 * 
	 */
	private static void setListTextValuesForCustomFields(String param, Object val, Map<String, Object> mvelTokens) {
		val = prepareListFromStringObject(StringUtils.lowerCase(String.valueOf(val)));
		mvelTokens.put(param, val);
	}
	
	/**
	 * 
	 * This was created as a part of BIRD-100846
	 * Earlier Mvel Evaluation Logic was in OngoingCampaignEval Rule for Custom Fields.
	 * Since for Trigger Expressions, list_text type fields have a separate handling,
	 * this has been done to avoid any change in existing functionality.
	 * 
	 * 
	 */
	private static void setListTextValues(String param, Object val, Map<String, Object> mvelTokens, Boolean isTriggerMvelExpressionEval) {
		if (BooleanUtils.isTrue(isTriggerMvelExpressionEval)) {
			setListTextValues(param, val, mvelTokens);
			return;
		}
		setListTextValuesForCustomFields(param, val, mvelTokens);
	}
	
	/**
	 * In case of list text set empty list as disjoint doesn't accept null values, otherwise set null
	 * 
	 * @param param
	 * @param type
	 * @param mvelTokens
	 */
	private static void setEmptyValue(String param, String type, Map<String, Object> mvelTokens) {
		switch (OperandDataType.getOperatorDataType(type)) {
			case LIST_TEXT:
				mvelTokens.put(param, Collections.emptyList());
				break;
			default:
				mvelTokens.put(param, null);
				break;
		}
		
	}
	
	/**
	 * Retrieves the appointment status from the campaign condition within the provided rule expression.
	 *
	 * @param ruleExpression The rule expression containing campaign conditions.
	 * @return The appointment status extracted from the campaign condition, or null if not found or invalid.
	 */
	public static String getCampaignConditionAppointmentStatus(RuleExpression ruleExpression) {
        Map<String, RuleCondition> conditions = ruleExpression.getConditions();
        for (Map.Entry<String, RuleCondition> entry : conditions.entrySet()) {
            RuleCondition condition = entry.getValue();
            if (StringUtils.equalsIgnoreCase("Appointment status", condition.getOperand())) {
                Object value = condition.getValue();
                if (value instanceof String) {
                    return (String) value;
                } else if (value instanceof List<?>) {
                    List<?> valueList = (List<?>) value;
                    if (!valueList.isEmpty() && valueList.get(0) instanceof String) {
                        return (String) valueList.get(0);
                    }
                }
            }
        }
        // If no condition matches or if appointment status value is not found, return null or appropriate default value
        return null;
    }
	
	/**
	 * Checks if there is any condition in the given rule expression that filters 
	 * on the 'Appointment status' operand using an operator other than 'IS_ANY_OF'.
	 * 
	 * @param ruleExpression The RuleExpression object containing conditions to check.
	 * @return true if a other than-'IS_ANY_OF' operator is used with the 'Appointment status' operand, false otherwise.
	 */
	public static boolean containsOtherThanAnyOfOperatorForAppointmentStatusFilter(RuleExpression ruleExpression) {
        Map<String, RuleCondition> conditions = ruleExpression.getConditions();
        for (Map.Entry<String, RuleCondition> entry : conditions.entrySet()) {
            RuleCondition condition = entry.getValue();
            if(StringUtils.equalsIgnoreCase("Appointment status", condition.getOperand()) && !StringUtils.equalsIgnoreCase("IS_ANY_OF", condition.getOperator())) {
            	return true;
            }
        }
        return false;
    }
	
	///// Custom Field Evaluation At Execution Flow
	
	/**
	 * 
	 * BIRD-100846
	 * Fetch Allowed Custom Fields List For Which Re-Evaluation Needs to be done
	 * Currently Hardcoding as scope of this Project only covers WAR
	 * In Future, new fields can be fetched from table with account specific config
	 * 
	 */
	public static List<String> fetchAllowedCFForReEval() {
		return new ArrayList<>(Collections.singletonList(WROTE_A_REVIEW_OPERAND));
	}
	
	/**
	 * 
	 * BIRD-100846
	 * Fetch Allowed Campaign Types For Which CF Re-Evaluation Needs to be done
	 * 
	 */
	public static List<String> fetchAllowedCampaignTypesForCFEval() {
		return CacheManager.getInstance().getCache(SystemPropertiesCache.class).getCommaSeparatedPropertiesList("allowed.campaign.types.cf.re.eval",
				StringUtils.join(CampaignTypeEnum.REVIEW_REQUEST.getType(), ",", CampaignTypeEnum.CX_REQUEST.getType()));
	}
	
	/**
	 * 
	 * BIRD-100846
	 * Checks if the campaign is eligible for wrote a review custom field evaluation by
	 * 1. Campaign should be not null and should not be a manual campaign.
	 * 2. Either it should be a split automation or type of campaign should be in allowed types.
	 * 
	 * @param campaign
	 * 
	 */
	public static boolean isCampaignEligibleForWARCFEval(Campaign campaign) {
		List<String> allowedCampaignTypes = fetchAllowedCampaignTypesForCFEval();
		return (campaign != null && StringUtils.equalsIgnoreCase(campaign.getRunType(), CampaignRunTypeEnum.ONGOING.getRunType())
				&& (BooleanUtils.isTrue(CoreUtils.getBooleanValueFromInteger(campaign.getIsSplitCampaign()))
						|| (CollectionUtils.isNotEmpty(allowedCampaignTypes) && allowedCampaignTypes.stream().anyMatch(type -> StringUtils.equalsIgnoreCase(type, campaign.getType())))));
	}
	
	/**
	 * 
	 * BIRD-100846
	 * Checks if the campaign is eligible for wrote a review custom field evaluation by
	 * 1. Checking if the Campaign Condition contains wrote a review custom field filter.
	 * 2. Campaign should be scheduled.
	 * 
	 * @param condition,
	 *            campaign
	 * 
	 */
	public static boolean validateWARCFEvalBasedOnConditionAndScheduling(CampaignCondition condition, Campaign campaign) {
		if (condition == null || MapUtils.isEmpty(condition.getMvelParamsAndTypes())) {
			return false;
		}
		return (condition.getMvelParamsAndTypes().entrySet().stream().anyMatch(param -> StringUtils.equals(param.getKey(), WROTE_A_REVIEW_OPERAND))
				&& (CollectionUtils.isNotEmpty(condition.getAppointmentScheduleInfo()) || (campaign.getSchedule() != null && BooleanUtils.isFalse(Objects.equals(campaign.getSchedule(), 0)))));
	}
	
	/**
	 * 
	 * BIRD-100846
	 * Add WARCF to Custom fields evaluation request
	 * 
	 * @param cfEvalRequest
	 * 
	 */
	public static void addWARCFToCFEvalRequest(CustomFieldEvalRequest cfEvalRequest) {
		if (cfEvalRequest == null || cfEvalRequest.getCustomFieldData() == null) {
			return;
		}
		cfEvalRequest.getCustomFieldData().add(new CustomFieldDetails(-100, WROTE_A_REVIEW_OPERAND));
		cfEvalRequest.setEvalRequired(true);
	}
	
	/**
	 * 
	 * BIRD-100846
	 * Checks if Custom Field Evaluation Request correspond to Wrote a Review Custom field evaluation.
	 * 
	 * @param request
	 * 
	 */
	public static boolean isWARCFEvalRequest(CustomFieldEvalRequest request) {
		return (isCFEvalRequest(request) && request.getCustomFieldData().stream().anyMatch(cf -> StringUtils.equals(cf.getCustomFieldName(), WROTE_A_REVIEW_OPERAND)));
	}
	
	/**
	 * 
	 * BIRD-100846
	 * Checks if Custom Field Evaluation Request.
	 * 
	 * @param request
	 * 
	 */
	public static boolean isCFEvalRequest(CustomFieldEvalRequest request) {
		return (request != null && BooleanUtils.isTrue(request.isEvalRequired()) && CollectionUtils.isNotEmpty(request.getCustomFieldData()));
	}
}
