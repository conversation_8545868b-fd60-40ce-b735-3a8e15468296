package com.birdeye.campaign.appointment.form.service;

import com.birdeye.campaign.entity.BusinessEmailTemplate;
import com.birdeye.campaign.response.template.v2.EmailTemplateResponse;

public interface AppointmentFormTemplateService {
	
	/**
	 * GET default appointment form email template
	 * @return
	 */
	EmailTemplateResponse getNewAppointmentFormEmailTemplate();
	
	/**
	 * GET existing appointment template by ID (BET + ET + Appointment form)
	 * 
	 * @param bet
	 * @return
	 */
	EmailTemplateResponse getExistingAppointmentFormEmailTemplate(BusinessEmailTemplate bet);
}
