package com.birdeye.campaign.appointment.reminder.service;

import java.util.ArrayList;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.entity.BusinessEmailTemplate;
import com.birdeye.campaign.entity.EmailTemplate;
import com.birdeye.campaign.enums.EmailTemplateTypes;
import com.birdeye.campaign.platform.constant.TemplateTypeEnum;
import com.birdeye.campaign.response.template.v2.EmailTemplateResponse;
import com.birdeye.campaign.service.TemplateHelperService;
import com.birdeye.campaign.sro.EmailTemplateMetadataSRO;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.TemplateUtils;

@Service
public class AppointmentReminderTemplateServiceImpl extends AsbtractTemplatesHandlerService implements AppointmentReminderTemplateService {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(AppointmentReminderTemplateServiceImpl.class);
	
	@Autowired
	private TemplateHelperService templateHelperService;
	
	/**
	 * GET default appointment reminder email template
	 * 
	 * @return
	 */
	@Override
	public EmailTemplateResponse getNewAppointmentReminderEmailTemplate() {
		
		EmailTemplateResponse response = new EmailTemplateResponse();
		
		BusinessEmailTemplate bet = new BusinessEmailTemplate();
		response.setDefaultReplyToEnabled(bet.getDefaultReplyToEnabled());
		response.setCustomHtmlEnabled(bet.getCustomHtmlTemplate());
		response.setLocationBrandingEnabled(bet.getLocationBrandingEnabled());	
		
		EmailTemplate et = getDefaultEmailTemplateByType(TemplateTypeEnum.APPOINTMENT_REMINDER.getName());
		response.setTemplateId(String.valueOf(0));
		response.setName(et.getName());
		response.setType(et.getType());
		response.setSubject(et.getSubject());
		response.setSignature(et.getSignature());
		response.setDefaultReplyToEmail(getDefaultReplyToEmailId(CoreUtils.getNullSafeInteger(MDC.get("bid"))));
		response.setReviewHeading(et.getReviewHeading());
		response.setReviewMessage(et.getReviewMessage());
		response.setCustomHtml(getDefaultCustomHtmlContentByType(StringUtils.lowerCase(EmailTemplateTypes.APPOINTMENT_REMINDER.name())));
		populateBrandingInfo(CoreUtils.getNullSafeInteger(MDC.get("bid")), response);
		response.setMeta(getAppointmentReminderMetaData());
		response.setMediaUrls(CoreUtils.parseDelimitedStringsWithDefaultAsEmptyList(et.getMediaUrls(), ","));
		response.setConfirmButtonEnabled(1);
		response.setRescheduleButtonEnabled(1);
		response.setCancelButtonEnabled(1);
		response.setAppointmentCustomFields(new ArrayList<>());
		response.setTemplateCategory(TemplateUtils.getUnsubscriptionCategoryWithDefault(et.getEmailCategory(), et.getType()));
		
		return response;
	}
	
	protected EmailTemplateMetadataSRO getAppointmentReminderMetaData() {
		return EmailTemplateMetadataSRO.builder().withGeneralSectionEnabled(1).withReviewSectionEnabled(1).withBrandingSectionEnabled(1).build();
	}

	
	/**
	 *GET existing appointment template by ID (BET + ET + Appointment reminder)
	 *
	 */
	@Override
	public EmailTemplateResponse getExistingAppointmentEmailTemplate(BusinessEmailTemplate bet) {
		EmailTemplateResponse response = existingAppointmentEmailTemplateData(bet);
		populateBrandingInfo(getEnterpriseId(bet), response);
		response.setMeta(getAppointmentReminderMetaData());
		return response;
	}

	private EmailTemplateResponse existingAppointmentEmailTemplateData(BusinessEmailTemplate bet) {
		EmailTemplateResponse response = new EmailTemplateResponse();
		
		EmailTemplate emailTemplate = bet.getTemplateId();
		
		response.setTemplateId(String.valueOf(bet.getEmailTemplateId()));
		response.setName(emailTemplate.getName());
		response.setSubject(emailTemplate.getSubject());
		response.setSignature(emailTemplate.getSignature());
		response.setDefaultReplyToEmail(getDefaultReplyToEmailId(CoreUtils.getNullSafeInteger(MDC.get("bid"))));
		response.setReviewHeading(emailTemplate.getReviewHeading());
		response.setReviewMessage(emailTemplate.getReviewMessage());
		response.setCustomHtml(emailTemplate.getEmailContent());
		response.setDefaultHtml(getDefaultCustomHtmlContentByType(StringUtils.lowerCase(EmailTemplateTypes.APPOINTMENT_REMINDER.name())));
		response.setMediaUrls(CoreUtils.parseDelimitedStringsWithDefaultAsEmptyList(emailTemplate.getMediaUrls(), ","));

		response.setDefaultReplyToEnabled(bet.getDefaultReplyToEnabled());
		response.setCustomHtmlEnabled(bet.getCustomHtmlTemplate());
		response.setLocationBrandingEnabled(bet.getLocationBrandingEnabled());
		
		response.setConfirmButtonEnabled(bet.getConfirmButtonEnabled() != null ? bet.getConfirmButtonEnabled() : 0);
		response.setRescheduleButtonEnabled(bet.getRescheduleButtonEnabled() != null ? bet.getRescheduleButtonEnabled() : 0);
		response.setCancelButtonEnabled(bet.getCancelButtonEnabled() != null ? bet.getCancelButtonEnabled() : 0);
		response.setEnableReplyToInbox(bet.getEnableReplyToInbox());
		response.setAppointmentCustomFields(templateHelperService.getAppointmentTemplateCustomFieldsAssociation(
					bet.getEmailTemplateId(), Constants.TEMPLATE_BASE_TYPE_EMAIL));
		response.setLocationCustomFields(templateHelperService.getTemplateLocationCustomFieldsAssociation(
				bet.getEmailTemplateId(), Constants.TEMPLATE_BASE_TYPE_EMAIL));
		response.setTemplateCategory(TemplateUtils.getUnsubscriptionCategoryWithDefault(emailTemplate.getEmailCategory(), emailTemplate.getType()));
		
		return response;
	}
	
}
