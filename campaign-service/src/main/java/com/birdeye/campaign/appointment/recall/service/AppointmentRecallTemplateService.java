package com.birdeye.campaign.appointment.recall.service;

import com.birdeye.campaign.entity.BusinessEmailTemplate;
import com.birdeye.campaign.response.template.v2.EmailTemplateResponse;

/**
 * 
 * <AUTHOR>
 *
 */
public interface AppointmentRecallTemplateService {
	
	/**
	 * GET default appointment recall email template
	 * 
	 * @return
	 */
	EmailTemplateResponse getDefaultAppointmentRecallEmailTemplate();
	
	/**
	 * GET existing appointment template by ID (BET + ET + Appointment recall)
	 * 
	 * @param bet
	 * @return
	 */
	EmailTemplateResponse getExistingAppointmentRecallEmailTemplate(BusinessEmailTemplate bet);
	
}
