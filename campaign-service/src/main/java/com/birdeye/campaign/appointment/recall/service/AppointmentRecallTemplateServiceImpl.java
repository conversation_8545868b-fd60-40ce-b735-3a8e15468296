package com.birdeye.campaign.appointment.recall.service;

import java.util.ArrayList;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.dto.BrandingInfo;
import com.birdeye.campaign.entity.BusinessEmailTemplate;
import com.birdeye.campaign.entity.EmailTemplate;
import com.birdeye.campaign.enums.EmailTemplateTypes;
import com.birdeye.campaign.external.service.BusinessExternalService;
import com.birdeye.campaign.platform.constant.TemplateTypeEnum;
import com.birdeye.campaign.response.template.v2.EmailTemplateResponse;
import com.birdeye.campaign.service.TemplateHelperService;
import com.birdeye.campaign.sro.EmailTemplateMetadataSRO;
import com.birdeye.campaign.template.email.cache.v2.IEmailTemplateCacheService;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.TemplateUtils;

@Service
public class AppointmentRecallTemplateServiceImpl implements AppointmentRecallTemplateService {
	
	private static final Logger			LOGGER	= LoggerFactory.getLogger(AppointmentRecallTemplateServiceImpl.class);
	
	@Autowired
	private IEmailTemplateCacheService	emailTemplateCacheService;
	
	@Autowired
	private BusinessExternalService		businessExternalService;
	
	@Autowired
	private TemplateHelperService 		templateHelperService;
	
	/**
	 * GET default appointment recall email template
	 * 
	 * @return
	 */
	@Override
	public EmailTemplateResponse getDefaultAppointmentRecallEmailTemplate() {
		EmailTemplateResponse response = new EmailTemplateResponse();
		
		BusinessEmailTemplate bet = new BusinessEmailTemplate();
		response.setDefaultReplyToEnabled(bet.getDefaultReplyToEnabled());
		response.setCustomHtmlEnabled(bet.getCustomHtmlTemplate());
		response.setLocationBrandingEnabled(bet.getLocationBrandingEnabled());
		
		EmailTemplate et = getDefaultEmailTemplateByType(TemplateTypeEnum.APPOINTMENT_RECALL.getName());
		response.setTemplateId(String.valueOf(0));
		response.setName(et.getName());
		response.setType(et.getType());
		response.setSubject(et.getSubject());
		response.setSignature(et.getSignature());
		response.setDefaultReplyToEmail(getDefaultReplyToEmailId(CoreUtils.getNullSafeInteger(MDC.get("bid"))));
		response.setReviewHeading(et.getReviewHeading());
		response.setReviewMessage(et.getReviewMessage());
		response.setCustomHtml(getDefaultCustomHtmlContentByType(StringUtils.lowerCase(EmailTemplateTypes.APPOINTMENT_RECALL.name())));
		populateBrandingInfo(CoreUtils.getNullSafeInteger(MDC.get("bid")), response);
		response.setMeta(getAppointmentRecallMetaData());
		response.setBookAppointmentButtonEnabled(1);
		response.setAppointmentCustomFields(new ArrayList<>());
		response.setTemplateCategory(TemplateUtils.getUnsubscriptionCategoryWithDefault(et.getEmailCategory(), et.getType()));
		
		return response;
	}
	
	/**
	 * GET appointment recall metadata
	 * 
	 * @return
	 */
	private EmailTemplateMetadataSRO getAppointmentRecallMetaData() {
		return EmailTemplateMetadataSRO.builder().withGeneralSectionEnabled(1).withReviewSectionEnabled(1).withBrandingSectionEnabled(1).build();
	}
	
	/**
	 * GET appointment recall email template by Id
	 * 
	 * @param BusinessEmailTemplate
	 * @return
	 */
	@Override
	public EmailTemplateResponse getExistingAppointmentRecallEmailTemplate(BusinessEmailTemplate bet) {
		LOGGER.info("getExistingAppointmentRecallEmailTemplate for businessId : {}", bet.getBusinessId());
		EmailTemplateResponse response = existingAppointmentRecallEmailTemplateData(bet);
		populateBrandingInfo(getEnterpriseId(bet), response);
		response.setMeta(getAppointmentRecallMetaData());
		return response;
	}
	
	/**
	 * GET and PREPARE email template response for appointment recall email template by Id
	 * 
	 * @param BusinessEmailTemplate
	 * @return EmailTemplateResponse
	 */
	private EmailTemplateResponse existingAppointmentRecallEmailTemplateData(BusinessEmailTemplate bet) {
		EmailTemplateResponse response = new EmailTemplateResponse();
		
		EmailTemplate emailTemplate = bet.getTemplateId();
		
		response.setTemplateId(String.valueOf(bet.getEmailTemplateId()));
		response.setName(emailTemplate.getName());
		response.setSubject(emailTemplate.getSubject());
		response.setSignature(emailTemplate.getSignature());
		response.setDefaultReplyToEmail(getDefaultReplyToEmailId(CoreUtils.getNullSafeInteger(MDC.get("bid"))));
		response.setReviewHeading(emailTemplate.getReviewHeading());
		response.setReviewMessage(emailTemplate.getReviewMessage());
		response.setCustomHtml(emailTemplate.getEmailContent());
		response.setDefaultHtml(getDefaultCustomHtmlContentByType(StringUtils.lowerCase(EmailTemplateTypes.APPOINTMENT_RECALL.name())));
		
		response.setDefaultReplyToEnabled(bet.getDefaultReplyToEnabled());
		response.setCustomHtmlEnabled(bet.getCustomHtmlTemplate());
		response.setLocationBrandingEnabled(bet.getLocationBrandingEnabled());
		
		response.setBookAppointmentButtonEnabled(bet.getBookAppointmentButtonEnabled() != null ? bet.getBookAppointmentButtonEnabled() : 0);
		response.setEnableReplyToInbox(bet.getEnableReplyToInbox());
		response.setAppointmentCustomFields(templateHelperService.getAppointmentTemplateCustomFieldsAssociation(
					bet.getEmailTemplateId(), Constants.TEMPLATE_BASE_TYPE_EMAIL));
		response.setLocationCustomFields(templateHelperService.getTemplateLocationCustomFieldsAssociation(
				bet.getEmailTemplateId(), Constants.TEMPLATE_BASE_TYPE_EMAIL));
		response.setTemplateCategory(TemplateUtils.getUnsubscriptionCategoryWithDefault(emailTemplate.getEmailCategory(), emailTemplate.getType()));

		return response;
	}
	
	/**
	 * gets BrandingInfo
	 * 
	 * @param enterpriseId,response
	 */
	protected void populateBrandingInfo(Integer enterpriseId, EmailTemplateResponse response) {
		
		try {
			BrandingInfo brandingInfo = businessExternalService.getBrandingInfoByEnterpriseId(enterpriseId);
			
			response.setBrandingHeaderColor(brandingInfo.getBrandingHeaderColor());
			response.setBrandingHeaderTextColor(brandingInfo.getBrandingHeaderTextColor());
			response.setBrandingLogo(brandingInfo.getBrandingLogo());
		} catch (Exception e) {
			LOGGER.error("Error ocurred while fetching branding info.", e);
		}
		
	}
	
	/**
	 * Returns default template by type
	 *
	 * @param type
	 * @return
	 */
	private EmailTemplate getDefaultEmailTemplateByType(String type) {
		return emailTemplateCacheService.getDefaultEmailTemplateByType(type);
	}
	
	/**
	 * Returns default emailId to reply to
	 *
	 * @param businessId
	 * @return
	 */
	private String getDefaultReplyToEmailId(Integer businessId) {
		return emailTemplateCacheService.getDefaultReplyToEmailId(businessId);
	}
	
	/**
	 * Returns default custom html content by type
	 *
	 * @param type
	 * @return
	 */
	private String getDefaultCustomHtmlContentByType(String type) {
		return emailTemplateCacheService.getDefaultCustomHtmlContentByType(type);
	}
	
	/**
	 * Returns enterpriseId for BusinessEmailTemplate
	 *
	 * @param BusinessEmailTemplate
	 * @return
	 */
	private Integer getEnterpriseId(BusinessEmailTemplate bet) {
		return bet.getEnterpriseId() != null ? bet.getEnterpriseId() : CoreUtils.getNullSafeInteger(MDC.get("bid"));
	}
	
}
