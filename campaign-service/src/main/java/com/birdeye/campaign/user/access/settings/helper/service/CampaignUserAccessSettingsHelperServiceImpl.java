package com.birdeye.campaign.user.access.settings.helper.service;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.dto.CampaignUserAccessDTO;
import com.birdeye.campaign.dto.SplitCampaignVariantData;
import com.birdeye.campaign.response.external.UserLocationAccessResponse;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.SplitCampaignHelperService;
import com.birdeye.campaign.service.dao.CampaignUserMappingDao;
import com.birdeye.campaign.utils.BusinessUtils;
import com.birdeye.campaign.utils.CampaignUserAccessUtils;

@Service("CampaignUserAccessSettingsHelperService")
public class CampaignUserAccessSettingsHelperServiceImpl implements CampaignUserAccessSettingsHelperService {
	
	private static final Logger			logger	= LoggerFactory.getLogger(CampaignUserAccessSettingsHelperServiceImpl.class);
	
	@Autowired
	private CampaignUserMappingDao		campaignUserMappingDao;
	
	@Autowired
	private SplitCampaignHelperService	splitCampaignHelperService;
	
	@Autowired
	private CacheService				cacheService;
	
	@Override
	@Cacheable(key = "#userId.toString().concat('-campaign-').concat(#campaignId)", value = "userCampaignAccessCache", unless = "#result == null")
	public CampaignUserAccessDTO fetchUserCampaignMapping(Integer userId, Integer campaignId) {
		return CampaignUserAccessUtils.prepareUserCampaignAccessFromCampaignUserMapping(campaignUserMappingDao.fetchCampaignUserMapping(userId, campaignId));
	}
	
	@Override
	@Cacheable(key = "#userId.toString().concat('-split_campaign-').concat(#splitCampaignId)", value = "userCampaignAccessCache", unless = "#result == null")
	public CampaignUserAccessDTO fetchUserSplitCampaignMapping(Integer userId, Integer splitCampaignId) {
		return CampaignUserAccessUtils.prepareUserCampaignAccessFromCampaignUserMapping(campaignUserMappingDao.fetchSplitCampaignUserMapping(userId, splitCampaignId));
	}
	
	@Override
	@Cacheable(key = "#userId.toString().concat('-campaign-').concat(#accountId.toString())", value = "userAllCampaignAccessCache", unless = "#result == null")
	public CachedCollectionWrapper<CampaignUserAccessDTO> fetchUserCampaignAccess(Integer userId, Integer accountId) {
		List<CampaignUserAccessDTO> userCampaignAcessDTOList = campaignUserMappingDao.fetchCampaignUserAccessForUserAndAccountId(userId, accountId);
		return (CollectionUtils.isNotEmpty(userCampaignAcessDTOList)) ? new CachedCollectionWrapper<>(userCampaignAcessDTOList) : null;
	}
	
	@Override
	@Cacheable(key = "#userId.toString().concat('-split_campaign-').concat(#accountId.toString())", value = "userAllCampaignAccessCache", unless = "#result == null")
	public CachedCollectionWrapper<CampaignUserAccessDTO> fetchUserSplitCampaignAccess(Integer userId, Integer accountId) {
		List<CampaignUserAccessDTO> userCampaignAcessDTOList = campaignUserMappingDao.fetchSplitCampaignUserAccessForUserAndAccountId(userId, accountId);
		return (CollectionUtils.isNotEmpty(userCampaignAcessDTOList)) ? new CachedCollectionWrapper<>(userCampaignAcessDTOList) : null;
	}
	
	@Override
	@CacheEvict(value = "userAllCampaignAccessCache", key = "#userId.toString().concat('-campaign-').concat(#accountId.toString())")
	public void evictUserAllCampaignAccessCache(Integer userId, Integer accountId) {
		logger.info("evicting user all campaign access cache for user Id {} and account id {}", userId, accountId);
	}
	
	@Override
	@CacheEvict(value = "userAllCampaignAccessCache", key = "#userId.toString().concat('-split_campaign-').concat(#accountId.toString())")
	public void evictUserAllSplitCampaignAccessCache(Integer userId, Integer accountId) {
		logger.info("evicting user all split campaign access cache for user Id {} and account id {}", userId, accountId);
	}
	
	@Override
	@CacheEvict(key = "#userId.toString().concat('-campaign-').concat(#campaignId)", value = "userCampaignAccessCache")
	public void evictUserCampaignAccessCache(Integer userId, Integer campaignId) {
		logger.info("evicting user campaign access cache for user Id {} and campaign id {}", userId, campaignId);
	}
	
	@Override
	@CacheEvict(key = "#userId.toString().concat('-split_campaign-').concat(#splitCampaignId)", value = "userCampaignAccessCache")
	public void evictUserSplitCampaignAccessCache(Integer userId, Integer splitCampaignId) {
		logger.info("evicting user split campaign access cache for user Id {} and split campaign id {}", userId, splitCampaignId);
	}
	
	@Override
	public List<SplitCampaignVariantData> validateAndFetchVariantsForSplitCampaign(Integer splitCampaignId, Integer accountId) {
		if (BooleanUtils.isTrue(CampaignUserAccessUtils.isInvalidVariantFetchRequest(splitCampaignId, accountId))) {
			logger.info("validateAndFetchVariantsForSplitCampaign :: Invalid request received to fetch variants as split campaign id or account id can't be null");
			return null;
		}
		List<SplitCampaignVariantData> variantsDataList = splitCampaignHelperService.fetchAndPrepareVariantsDataForAccessSettings(splitCampaignId, accountId);
		return (CollectionUtils.isNotEmpty(variantsDataList)) ? variantsDataList : null;
	}
	
	@Override
	@Cacheable(key = "#campaignId", value = "campaignOwnerCache", unless = "#result == null")
	public CampaignUserAccessDTO fetchCampaignOwner(Integer campaignId) {
		List<CampaignUserAccessDTO> owners = CampaignUserAccessUtils.prepareUserCampaignAccessListFromCampaignUserMapping(campaignUserMappingDao.getCampaignOwner(campaignId));
		return (CollectionUtils.isNotEmpty(owners)) ? owners.get(0) : null;
	}
	
	@Override
	@Cacheable(key = "#splitCampaignId", value = "splitCampaignOwnerCache", unless = "#result == null")
	public CampaignUserAccessDTO fetchSplitCampaignOwner(Integer splitCampaignId) {
		List<CampaignUserAccessDTO> owners = CampaignUserAccessUtils.prepareUserCampaignAccessListFromCampaignUserMapping(campaignUserMappingDao.getSplitCampaignOwner(splitCampaignId));
		return (CollectionUtils.isNotEmpty(owners)) ? owners.get(0) : null;
	}
	
	/**
	 * 
	 * This method checks if the given user id corresponding to the given account id is an enterprise user or not.
	 * 1. Basic Validation of the Request
	 * 2. SMB Validation - Fetch business
	 * 3. If business not found, return false(Business closed or account id invalid)
	 * 4. If business is SMB, return false, as in case of SMB, explicit access required for user other than owner.
	 * 5. Call Core API to check if current user is enterprise user.
	 * 
	 * @param userId,
	 *            accountId
	 * 
	 */
	@Override
	@Cacheable(key = "#userId.toString().concat('-').concat(#accountId)", value = "enterpriseUserCache", unless = "#result == null")
	public Long isEnterpriseUser(Integer userId, Integer accountId) {
		if (userId == null || accountId == null) {
			logger.error("isEnterpriseUser :: Invalid request received to fetch user details as account id {} or user id {} can't be null", accountId, userId);
			return 0l;
		}
		try {
			logger.info("isEnterpriseUser :: Request received to check for user id {} and account id {}", userId, accountId);
			// Handling for SMB Business Users - Apart From Owner, Every other User requires explicit Access
			BusinessEnterpriseEntity business = cacheService.getValidBusinessById(accountId);
			if (BooleanUtils.isTrue(BusinessUtils.isValidSMBBusiness(business))) {
				logger.info("isEnterpriseUser :: Account given is a SMB for user id {} and account id {}", userId, accountId);
				return 0l;
			}
			if (BooleanUtils.isFalse(BusinessUtils.isValidEnterprise(business))) {
				logger.error("isEnterpriseUser :: Invalid business for user id {} and account id {}", userId, accountId);
				return 0l;
			}
			
			// For Enterprise Users - Automatic Edit Access
			UserLocationAccessResponse response = cacheService.getUserInfoAndLocationAccessCached(userId, accountId);
			if (response == null || CollectionUtils.isEmpty(response.getUsers()) || response.getUsers().get(0) == null) {
				return 0l;
			}
			return BooleanUtils.isTrue(response.getUsers().get(0).getEnterpriseUser()) ? 1l : 0l;
		} catch (Exception e) {
			logger.error("isEnterpriseUser :: Exception occurred while checking is enteprise user for user id {} and account {} and exception {}", userId, accountId, ExceptionUtils.getStackTrace(e));
		}
		return 0l;
	}
	
	@Override
	@CacheEvict(key = "#campaignId", value = "campaignOwnerCache")
	public void evictCampaignOwnerCache(Integer campaignId) {
		logger.info("evicting user split campaign access cache for campaign Id {}", campaignId);
	}
	
	@Override
	@CacheEvict(key = "#splitCampaignId", value = "splitCampaignOwnerCache")
	public void evictSpliCampaignOwnerCache(Integer splitCampaignId) {
		logger.info("evicting user split campaign access cache for split campaign Id {}", splitCampaignId);
	}
}
