package com.birdeye.campaign.user.access.settings.helper.service;

import java.util.List;

import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.dto.SplitCampaignVariantData;
import com.birdeye.campaign.dto.CampaignUserAccessDTO;

public interface CampaignUserAccessSettingsHelperService {
	
	CampaignUserAccessDTO fetchUserCampaignMapping(Integer userId, Integer campaignId);
	
	CampaignUserAccessDTO fetchUserSplitCampaignMapping(Integer userId, Integer splitCampaignId);
	
	CachedCollectionWrapper<CampaignUserAccessDTO> fetchUserCampaignAccess(Integer userId, Integer accountId);
	
	CachedCollectionWrapper<CampaignUserAccessDTO> fetchUserSplitCampaignAccess(Integer userId, Integer accountId);

	void evictUserAllCampaignAccessCache(Integer userId, Integer accountId);

	void evictUserAllSplitCampaignAccessCache(Integer userId, Integer accountId);

	List<SplitCampaignVariantData> validateAndFetchVariantsForSplitCampaign(Integer splitCampaignId, Integer accountId);

	void evictUserSplitCampaignAccessCache(Integer userId, Integer splitCampaignId);

	void evictUserCampaignAccessCache(Integer userId, Integer campaignId);

	CampaignUserAccessDTO fetchCampaignOwner(Integer campaignId);

	CampaignUserAccessDTO fetchSplitCampaignOwner(Integer splitCampaignId);

	void evictCampaignOwnerCache(Integer campaignId);

	void evictSpliCampaignOwnerCache(Integer splitCampaignId);

	/**
	 * 
	 * This method checks if the given user id corresponding to the given account id is an enterprise user or not.
	 * 1. Basic Validation of the Request
	 * 2. SMB Validation - Fetch business
	 * 3. If business not found, return false(Business closed or account id invalid)
	 * 4. If business is SMB, return false, as in case of SMB, explicit access required for user other than owner.
	 * 5. Call Core API to check if current user is enterprise user.
	 * 
	 * @param userId,
	 *            accountId
	 * 
	 */
	Long isEnterpriseUser(Integer userId, Integer accountId);
	
}
