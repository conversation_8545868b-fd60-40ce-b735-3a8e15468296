package com.birdeye.campaign.user.access.settings.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.birdeye.campaign.aspect.annotation.Profiled;
import com.birdeye.campaign.business.service.BusinessService;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.dto.AccessSettingMigrateDTO;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.dto.CampaignUserAccessDTO;
import com.birdeye.campaign.entity.CampaignUserMapping;
import com.birdeye.campaign.platform.constant.CampaignAccessSettingEnum;
import com.birdeye.campaign.platform.constant.CampaignUserActionEnum;
import com.birdeye.campaign.repository.CampaignRepo;
import com.birdeye.campaign.request.AccessSettingsUpdateRequest;
import com.birdeye.campaign.request.CampaignUserAccessInfoDetails;
import com.birdeye.campaign.request.CampaignUserAccessRequest;
import com.birdeye.campaign.request.CampaignUserAccessSaveRequest;
import com.birdeye.campaign.request.CampaignUsersListAccess;
import com.birdeye.campaign.request.ProductFeatureRequest;
import com.birdeye.campaign.response.CampaignUserAccessResponse;
import com.birdeye.campaign.response.CampaignUserOwnershipResponse;
import com.birdeye.campaign.response.external.BulkUserDetailsResponse;
import com.birdeye.campaign.response.external.UserLocationAccessResponse;
import com.birdeye.campaign.response.external.UserLocationAccessResponse.UserLocationInfo;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.dao.CampaignUserMappingDao;
import com.birdeye.campaign.service.dao.SplitCampaignDao;
import com.birdeye.campaign.user.access.settings.helper.service.CampaignUserAccessSettingsHelperService;
import com.birdeye.campaign.utils.BusinessUtils;
import com.birdeye.campaign.utils.CampaignUserAccessUtils;
import com.birdeye.campaign.utils.CoreUtils;

@Service("CampaignUserAccessSettingsService")
public class CampaignUserAccessSettingsServiceImpl implements CampaignUserAccessSettingsService {
	
	private static final Logger					logger	= LoggerFactory.getLogger(CampaignUserAccessSettingsServiceImpl.class);
	
	@Autowired
	private CampaignUserMappingDao				campaignUserMappingDao;
	
	@Autowired
	private CacheService						cacheService;
	
	@Autowired
	private CampaignUserAccessSettingsHelperService	userAccessSettingHelper;
	
	@Autowired
	private BusinessService						businessService;
	
	@Autowired
	private CampaignRepo						campaignRepo;
	
	@Autowired
	private SplitCampaignDao					splitCampaignDao;
	
	/**
	 * 
	 * This method fetches the access status for the given user id , account id and campaign category -> automation or split
	 * 1. Basic Validation of the Request, account id, user id and campaign category fields
	 * 2. Check if Enterprise User, no need to fetch explicit access.
	 * 3. Based upon category, fetch data corresponding to campaign or split campaign.
	 * 
	 * @param CampaignUserAccessRequest
	 * 
	 */
	@Override
	public CachedCollectionWrapper<CampaignUserAccessDTO> fetchUserCampaignsAccess(CampaignUserAccessRequest request) {
		if (BooleanUtils.isTrue(CampaignUserAccessUtils.isInvalidFetchUserCampaignsAccessRequest(request))) {
			logger.error("fetchUserAccessedCampaignsList :: Invalid request received to fetch user accessed campaign list as account id, user id or campaign category can't be null {}", request);
			return null;
		}
		try {
			logger.info("fetchUserAccessedCampaigns :: Request received to fetch user access campaigns for user id {} , account id {} and campaign category {}", request.getUserId(),
					request.getAccountId(), request.getCampaignCategory());
			if (Objects.equals(userAccessSettingHelper.isEnterpriseUser(request.getUserId(), request.getAccountId()), 1l)) {
				return new CachedCollectionWrapper<>(new ArrayList<>(
						Collections.singletonList(new CampaignUserAccessDTO(request.getUserId(), CampaignUserAccessUtils.fetchAccessSettingForEnterpriseUser(), request.getAccountId(), true))));
			}
			if (StringUtils.equalsIgnoreCase(request.getCampaignCategory(), Constants.USER_ACCESS_CAMPAIGN)) {
				return userAccessSettingHelper.fetchUserCampaignAccess(request.getUserId(), request.getAccountId());
			} else {
				return userAccessSettingHelper.fetchUserSplitCampaignAccess(request.getUserId(), request.getAccountId());
			}
		} catch (Exception e) {
			logger.error("fetchUserAccessedCampaigns :: Exception occurred while fetching campaigns for given user {} and account {} and exception {}", request.getUserId(), request.getAccountId(),
					ExceptionUtils.getStackTrace(e));
		}
		return null;
	}
	
	/**
	 * 
	 * This method fetches the access status for the current user for given campaign or split campaign id
	 * 1. If the user is enterprise user , By default he/she would have edit access to all campaigns.
	 * 2. Else if a location user, depends upon what access he has been given. Default access is no-access.
	 * 
	 * @param CampaignUserAccessRequest
	 * 
	 */
	@Override
	@Profiled
	public CampaignUserAccessDTO fetchAccessSettings(CampaignUserAccessRequest request) {
		if (BooleanUtils.isTrue(CampaignUserAccessUtils.isInvalidFetchAccessSettingRequest(request))) {
			logger.error("fetchAccessSettings :: Invalid request received for accountId {}, userId {}, campaignId {} and splitCampaignId {}", request.getAccountId(), request.getUserId(),
					request.getCampaignId(), request.getSplitCampaignId());
			return null;
		}
		try {
			if (Objects.equals(userAccessSettingHelper.isEnterpriseUser(request.getUserId(), request.getAccountId()), 1l)) {
				return new CampaignUserAccessDTO(request.getCampaignId(), request.getSplitCampaignId(), request.getUserId(), CampaignUserAccessUtils.fetchAccessSettingForEnterpriseUser(),
						request.getAccountId(), true);
			}
			CampaignUserAccessDTO userCampaignAccess = (request.getCampaignId() != null)
					? userCampaignAccess = userAccessSettingHelper.fetchUserCampaignMapping(request.getUserId(), request.getCampaignId())
					: userAccessSettingHelper.fetchUserSplitCampaignMapping(request.getUserId(), request.getSplitCampaignId());
			
			if (userCampaignAccess != null) {
				// For all Dashboard pages, other than campaign access settings page, the access should be edit in case of owner.
				userCampaignAccess.setAccess(CampaignUserAccessUtils.fetchAccessModifierForCampaign(userCampaignAccess.getAccess()));
				return userCampaignAccess;
			}
		} catch (Exception e) {
			logger.error("fetchAccessSettings :: Exception occurred while fetching access settings for user id {} and account {} and exception {}", request.getUserId(), request.getAccountId(),
					ExceptionUtils.getStackTrace(e));
		}
		return new CampaignUserAccessDTO(request.getCampaignId(), request.getSplitCampaignId(), request.getUserId(), CampaignUserAccessUtils.fetchDefaultAccessSettingForAUser(),
				request.getAccountId());
	}
	
	/**
	 * 
	 * This method fetches user permissible locations
	 * 1. Basic Validation of the Request
	 * 2. SMB Validation - Fetch business
	 * 3. If business not found, return empty list(Business closed or account id invalid)
	 * 4. If business is SMB, return smb account id, as in case of SMB, only one location is there.
	 * 5. Call Core API to fetch user accessible locations in case of enterprise.
	 * 
	 * @param userId,
	 *            accountId
	 * 
	 */
	@Override
	@Profiled
	public List<Integer> fetchUserPermissibleLocations(Integer userId, Integer accountId) {
		if (userId == null || accountId == null) {
			logger.error("fetchUserPermissibleLocations :: Invalid request received for accountId {} and userId {}", accountId, userId);
			return new ArrayList<>();
		}
		try {
			logger.info("fetchUserPermissibleLocations :: Request received to fetch user permissible locations for accountId {} and userId {}", accountId, userId);
			BusinessEnterpriseEntity business = cacheService.getValidBusinessById(accountId);
			if (BooleanUtils.isTrue(BusinessUtils.isValidSMBBusiness(business))) {
				logger.info("fetchUserPermissibleLocations :: Account is SMB for user id {} and account id {}", userId, accountId);
				return new ArrayList<>(Collections.singletonList(accountId));
			}
			if (BooleanUtils.isFalse(BusinessUtils.isValidEnterprise(business))) {
				logger.error("fetchUserPermissibleLocations :: Error while fetching business for user id {} and account id {}", userId, accountId);
				return new ArrayList<>();
			}
			
			UserLocationAccessResponse response = cacheService.getUserInfoAndLocationAccessCached(userId, accountId);
			if (response != null && CollectionUtils.isNotEmpty(response.getUsers()) && response.getUsers().get(0) != null) {
				UserLocationInfo user = response.getUsers().get(0);
				return (CollectionUtils.isNotEmpty(user.getLocationIds())) ? user.getLocationIds() : new ArrayList<>();
			}
		} catch (Exception e) {
			logger.error("fetchUserPermissibleLocations :: Exception occurred while fetching user permissible locations for user id {} and account {} and exception {}", userId, accountId,
					ExceptionUtils.getStackTrace(e));
		}
		return new ArrayList<>();
	}
	
	/**
	 * 
	 * This method fetches owner and owner permissible locations
	 * 1. Basic Validation of the Request
	 * 2. Fetch Campaign/Split Campaign Owner
	 * 3. If Owner Found, Call Function to fetch permissible locations
	 * 
	 * @param CampaignUserAccessRequest
	 * 
	 */
	@Override
	@Profiled
	public List<Integer> fetchOwnerPermissibleLocations(CampaignUserAccessRequest request) {
		if (BooleanUtils.isTrue(CampaignUserAccessUtils.isInvalidFetchOwnerAndPermissibleLocationsRequest(request))) {
			logger.info("fetchOwnerAndOwnerPermissibleLocations :: Invalid request received {}", request);
			return new ArrayList<>();
		}
		try {
			logger.info("fetchOwnerAndOwnerPermissibleLocations :: Request received to fetch owner and user permissible locations for request {}", request);
			CampaignUserAccessDTO userCampaignAccess = getCampaignOwner(request.getCampaignId(), request.getSplitCampaignId(), null);
			if (userCampaignAccess != null) {
				return fetchUserPermissibleLocations(userCampaignAccess.getUserId(), request.getAccountId());
			}
		} catch (Exception e) {
			logger.error("fetchUserPermissibleLocations :: Exception occurred while fetching user permissible locations for user id {} and account {} and exception {}", request.getUserId(),
					request.getAccountId(), ExceptionUtils.getStackTrace(e));
		}
		return new ArrayList<>();
	}
	
	/**
	 * 
	 * This method fetches access settings for the current user and owner permissible locations
	 * 
	 * @param CampaignUserAccessRequest
	 * 
	 */
	@Override
	@Profiled
	public CampaignUserAccessDTO fetchAccessSettingsWithOwnerLocationAccess(CampaignUserAccessRequest request) {
		logger.info("fetchOwnerAndOwnerPermissibleLocations :: Request received to fetch access settings and owner permissible locations for request {}", request);
		CampaignUserAccessDTO userCampaignAccess = fetchAccessSettings(request);
		if (userCampaignAccess != null) {
			userCampaignAccess.setOwnerPermissibleLocations(fetchOwnerPermissibleLocations(request));
			return userCampaignAccess;
		}
		return null;
	}
	
	/**
	 * 
	 * This method checks if access settings are switched on for given account id
	 * 1. Basic Validation of the Request
	 * 2. Call ProductFeature API to fetch required flag to validate access settings
	 * 
	 * @param accountId
	 * 
	 */
	@Override
	public Boolean isAccessSettingApplicable(Integer accountId) {
		if (accountId == null) {
			logger.warn("Invalid Request received to check if access settings applicable for account id {}", accountId);
			return false;
		}
		try {
			logger.info("isAccessSettingApplicable :: Received Request to check applicability of access settings for account id {}", accountId);
			ProductFeatureRequest featureRequest = cacheService.getProductFeatureForBusiness(accountId);
			logger.info("isAccessSettingApplicable :: Feature Flag fetched for campaign access setting for account id {} is {}", accountId,
					featureRequest != null ? featureRequest.getCampaignLocationUserAccess() : null);
			return (featureRequest != null && BooleanUtils.isTrue(CoreUtils.getBooleanValueFromInteger(featureRequest.getCampaignLocationUserAccess())));
		} catch (Exception e) {
			logger.error("isAccessSettingApplicable :: Exception occurred while fetching access settings for account {} and exception {}", accountId, ExceptionUtils.getStackTrace(e));
		}
		return false;
	}
	
	/**
	 * 
	 * Convert DTO object to table entity and save object to database.
	 * 
	 * @param userCampaignAccess
	 * 
	 */
	private CampaignUserMapping prepareAndSaveCampaignUserMapping(CampaignUserAccessDTO userCampaignAccess) {
		logger.info("Request received to save campaign user mapping {}", userCampaignAccess);
		return campaignUserMappingDao.saveorUpdateCampaignUserMapping(CampaignUserAccessUtils.prepareCampaignUserMappingFromUserAccessDTO(userCampaignAccess));
	}
	
	/**
	 * 
	 * This method checks if access settings are switched on for given account id
	 * 1. Validation of the Request
	 * 2. Check if Access Settings applicable for given account id
	 * 3. Prepare DTO object from Request and call function to update owner in database.
	 * 4. Evict Concerned Cache
	 * 
	 * @param CampaignUserAccessRequest
	 * 
	 */
	@Override
	@Profiled
	public void validateAndUpdateOwnerForCampaignAccessSetting(CampaignUserAccessRequest request) {
		if (BooleanUtils.isTrue(CampaignUserAccessUtils.isInvalidUpdateOwnerRequest(request))) {
			logger.info("validateAndUpdateOwnerForCampaignAccessSetting :: Invalid request received {}", request);
			return;
		}
		if (BooleanUtils.isFalse(isAccessSettingApplicable(request.getAccountId()))) {
			logger.info("validateAndUpdateOwnerForCampaignAccessSetting :: For request {}, account ineligible for access setting updation", request);
			return;
		}
		CampaignUserMapping campaignUserMapping = prepareAndSaveCampaignUserMapping(new CampaignUserAccessDTO(request.getCampaignId(), request.getSplitCampaignId(), request.getOwnerId(),
				CampaignAccessSettingEnum.OWNER.getAccess(), request.getAccountId(), request.getOwnerId()));
		logger.info("Campaign user mapping created for campaign id {}, owner id {} and split campaign id {} with id {}", request.getCampaignId(), request.getOwnerId(), request.getSplitCampaignId(),
				campaignUserMapping != null ? campaignUserMapping.getId() : null);
		
		// Evict Cache
		evictUserAllCampaignCache(request.getOwnerId(), request.getAccountId(), (request.getCampaignId() != null) ? Constants.USER_ACCESS_CAMPAIGN : Constants.USER_ACCESS_SPLIT_CAMPAIGN);
	}
	
	private void evictUserAllCampaignCache(Integer userId, Integer accountId, String campaignCategory) {
		if (StringUtils.equalsIgnoreCase(campaignCategory, Constants.USER_ACCESS_CAMPAIGN)) {
			userAccessSettingHelper.evictUserAllCampaignAccessCache(userId, accountId);
		} else {
			userAccessSettingHelper.evictUserAllSplitCampaignAccessCache(userId, accountId);
		}
	}
	
	private void evictUserCampaignCache(Integer userId, Integer campaignId, Integer splitCampaignId) {
		if (campaignId != null) {
			userAccessSettingHelper.evictUserCampaignAccessCache(userId, campaignId);
		} else if (splitCampaignId != null) {
			userAccessSettingHelper.evictUserSplitCampaignAccessCache(userId, splitCampaignId);
		}
	}
	
	/**
	 * 
	 * This method deletes campaign user mappings
	 * 1.Based on campaign id or split campaign is delete mappings
	 * 
	 * @param accountId
	 * 
	 */
	@Override
	@Profiled
	public void deleteCampaignAccessSetting(CampaignUserAccessRequest request) {
		if (BooleanUtils.isTrue(CampaignUserAccessUtils.isInvalidDeleteCampaignAccessSettingRequest(request))) {
			logger.error("deleteCampaignAccessSetting :: Invalid Request received to delete campaign access setting for campaign id {} or split campaign id {}", request.getCampaignId(),
					request.getSplitCampaignId());
			return;
		}
		logger.info("deleteCampaignAccessSetting :: Request received to delete campaign access setting for campaign id {} or split campaign id {}", request.getCampaignId(),
				request.getSplitCampaignId());
		Integer updatedRows = null;
		if (request.getCampaignId() != null) {
			updatedRows = campaignUserMappingDao.deleteUserMappingForCampaign(request.getCampaignId());
			logger.info("deleteCampaignAccessSetting :: Updated rows for campaign id {}, are {}", request.getCampaignId(), updatedRows);
		} else {
			updatedRows = campaignUserMappingDao.deleteUserMappingForSplitCampaign(request.getSplitCampaignId());
			logger.info("deleteCampaignAccessSetting :: Updated rows for split campaign id {}, are {}", request.getSplitCampaignId(), updatedRows);
		}
	}
	
	/**
	 * 
	 * For Given user id associated with account id, get user campaign mappings
	 * 1. Basic Validation of the Request
	 * 2. Fetch mappings corresponding to campaign and split campaign.
	 * 
	 * @param userId,accountId
	 * 
	 */
	@Override
	public CampaignUserOwnershipResponse getUserCampaignOwnership(Integer userId, Integer accountId) {
		if (BooleanUtils.isFalse(CoreUtils.isTrueForInteger(userId)) || BooleanUtils.isFalse(CoreUtils.isTrueForInteger(accountId))) {
			logger.error("getUserCampaignOwnership :: Invalid request received to get user campaign ownership as user id or account id can't be null");
			return new CampaignUserOwnershipResponse();
		}
		logger.info("getUserCampaignOwnership :: Request received to get user campaign ownership for user id {} and account id {}", userId, accountId);
		Integer campaignCount = campaignUserMappingDao.getUserCampaignMappingCountWithAccess(userId, accountId, CampaignAccessSettingEnum.OWNER.getAccess());
		Integer splitCampaignCount = campaignUserMappingDao.getUserSplitCampaignMappingCountWithAccess(userId, accountId, CampaignAccessSettingEnum.OWNER.getAccess());
		return new CampaignUserOwnershipResponse(campaignCount, splitCampaignCount, CampaignUserAccessUtils.hasCampaignCountFlagEnabled(campaignCount, splitCampaignCount));
	}
	
	/**
	 * This function will fetch details of the users associated the campaign
	 * 
	 */
	@Override
	public CampaignUserAccessResponse getUsersForTheCampaign(CampaignUsersListAccess request) {
		CampaignUserAccessResponse userAccessResponse = new CampaignUserAccessResponse();
		if (BooleanUtils.isTrue(CampaignUserAccessUtils.isInvalidFetchAccessSettingRequest(request))) {
			logger.info("Invalid request to fetch users for request: {}", request);
			return userAccessResponse;
		}
		logger.info("getUsersForTheCampaign :: Request received to get users for campaign id {} and account id {}", request.getCampaignId(), request.getAccountId());
		
		List<CampaignUserMapping> campaignUserMapping = campaignUserMappingDao.getUsersForTheCampaign(request.getAccountId(), request.getCampaignId(), request.getSplitCampaignId());
		if (CollectionUtils.isEmpty(campaignUserMapping)) {
			userAccessResponse.setCampaignId(request.getCampaignId());
			userAccessResponse.setSplitCampaignId(request.getSplitCampaignId());
			return userAccessResponse;
		}
		userAccessResponse.setCampaignId(request.getCampaignId());
		userAccessResponse.setSplitCampaignId(request.getSplitCampaignId());
		
		try {
			// map of userid vs access
			Map<Integer, String> userAccessMap = campaignUserMapping.stream()
					.collect(Collectors.toMap(CampaignUserMapping::getUserId, CampaignUserMapping::getAccess, (oldValue, newValue) -> oldValue));
			List<Integer> userIds = campaignUserMapping.stream().map(userMapping -> userMapping.getUserId()).collect(Collectors.toList());
			// logged in user details and access
			fetchLoggedInUserDetails(campaignUserMapping, request.getUserId(), request.getAccountId(), userAccessMap, userAccessResponse);
			
			List<CampaignUserAccessInfoDetails> userAccessDetails = getBulkUserInfoWithLocationAccess(userIds, request.getAccountId(), userAccessMap);
			if (CollectionUtils.isNotEmpty(userAccessDetails)) {
				userAccessResponse.setUserAccessList(userAccessDetails);
				// need owner id and permissible location ids
				fetchOwnerDetails(campaignUserMapping, userAccessDetails, userAccessResponse);
			} else {
				logger.info("received empty response for users info from core service for account id {} and user ids {}", request.getAccountId(), userIds);
			}
			
			userAccessResponse.setVariants(userAccessSettingHelper.validateAndFetchVariantsForSplitCampaign(request.getSplitCampaignId(), request.getAccountId()));
			return userAccessResponse;
		} catch (Exception e) {
			logger.error("received error while fetching users list for request {}: {}", request, ExceptionUtils.getStackTrace(e));
			return userAccessResponse;
		}
		
	}
	
	private void fetchLoggedInUserDetails(List<CampaignUserMapping> campaignUserMapping, Integer userId, Integer accountId, Map<Integer, String> userAccessMap,
			CampaignUserAccessResponse userAccessResponse) {
		List<CampaignUserMapping> currentUsers = campaignUserMapping.stream().filter(userMapping -> userMapping.getUserId().equals(userId.intValue())).collect(Collectors.toList());
		
		if (CollectionUtils.isEmpty(currentUsers)) {
			if (Objects.equals(userAccessSettingHelper.isEnterpriseUser(userId, accountId), 1l)) {
				userAccessResponse.setUserPermissions(Collections.singletonList(CampaignUserAccessUtils.fetchAccessSettingForEnterpriseUser()));
			} else {
				userAccessResponse.setUserPermissions(Collections.singletonList(CampaignUserAccessUtils.fetchDefaultAccessSettingForAUser()));
			}
		} else {
			userAccessResponse.setUserPermissions(Collections.singletonList(CampaignUserAccessUtils.fetchAccessModifierForCampaign(userAccessMap.get(userId))));
		}
	}
	
	private void fetchOwnerDetails(List<CampaignUserMapping> campaignUserMapping, List<CampaignUserAccessInfoDetails> userAccessDetails, CampaignUserAccessResponse userAccessResponse) {
		List<CampaignUserMapping> owners = campaignUserMapping.stream().filter(mapping -> StringUtils.equalsIgnoreCase(CampaignAccessSettingEnum.OWNER.getAccess(), mapping.getAccess()))
				.collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(owners)) {
			userAccessResponse.setOwner(owners.get(0).getUserId());
			userAccessResponse.setOwnerPermissibleLocations(fetchUserPermissibleLocations(owners.get(0).getUserId(), owners.get(0).getAccountId()));
		}
	}
	
	@Override
	public CampaignUserAccessDTO getCampaignOwner(Integer campaignId, Integer splitCampaignId, String cacheKey) {
		if (campaignId == null && splitCampaignId == null) {
			logger.info("invalid request received to fetch owner");
			return null;
		}
		return campaignId != null ? userAccessSettingHelper.fetchCampaignOwner(campaignId) : userAccessSettingHelper.fetchSplitCampaignOwner(splitCampaignId);
	}
	
	public void deleteCampaignUser(List<Integer> userIds, Integer campaignId) {
		logger.info("deleting campaign access users for campaign id {} and user ids {}", campaignId, userIds);
		campaignUserMappingDao.deleteUsers(userIds, campaignId);
	}
	
	/***
	 * Save users entry for automation
	 *  
	 */
	@Override
	@Transactional
	public void saveUsersForCampaign(Integer accountId, Integer userId, CampaignUserAccessSaveRequest usersRequest) {
		
		if (CampaignUserAccessUtils.isInvalidRequestToSaveCampaignUsers(accountId, usersRequest)) {
			logger.info("received invalid request for account id {} and request {}", accountId, usersRequest);
			return;
		}
		
		Map<String, List<CampaignUserAccessInfoDetails>> actionMap = usersRequest.getUserAccessList().stream().collect(Collectors.groupingBy(CampaignUserAccessInfoDetails::getAction));
		List<CampaignUserAccessInfoDetails> editUserInfoList = actionMap.get(CampaignUserActionEnum.UPDATE.getType()) != null ? actionMap.get(CampaignUserActionEnum.UPDATE.getType()) : new ArrayList<>();
		List<CampaignUserAccessInfoDetails> newUserInfoList = actionMap.get(CampaignUserActionEnum.ADD.getType()) != null ? actionMap.get(CampaignUserActionEnum.ADD.getType()) : new ArrayList<>();
		List<CampaignUserAccessInfoDetails> deleteUserInfoList = actionMap.get(CampaignUserActionEnum.DELETE.getType()) != null ? actionMap.get(CampaignUserActionEnum.DELETE.getType()) : new ArrayList<>();
		
		List<CampaignUserMapping> campaignUserMapping = CampaignUserAccessUtils.convertUserAccessInfoToCampaignUserMapping(newUserInfoList, usersRequest.getCampaignId(),
				usersRequest.getSplitCampaignId(), accountId, userId);
		
		// create new entries for the edited users and delete the existing one
		if (CollectionUtils.isNotEmpty(editUserInfoList)) {
			campaignUserMapping
					.addAll(CampaignUserAccessUtils.convertUserAccessInfoToCampaignUserMapping(editUserInfoList, usersRequest.getCampaignId(), usersRequest.getSplitCampaignId(), accountId, userId));
			deleteUserInfoList.addAll(editUserInfoList);
		}
		
		try {
			if (CollectionUtils.isNotEmpty(deleteUserInfoList)) {
				List<Integer> usersIdsToDelete = deleteUserInfoList.stream().map(user -> user.getUserId()).collect(Collectors.toList());
				
				campaignUserMappingDao.deleteUsers(usersIdsToDelete, usersRequest.getCampaignId());
			}
			// add new/updated users
			campaignUserMappingDao.addCampaignAccessUsers(campaignUserMapping);
		} catch (Exception e) {
			logger.error("exception while updating user mapping for account id {} : {}", accountId, ExceptionUtils.getStackTrace(e));
		}
		// evict caches
		List<Integer> userIds = usersRequest.getUserAccessList().stream().map(userRequest -> userRequest.getUserId()).collect(Collectors.toList());
		evictCachesForMultipleUsers(userIds, accountId, usersRequest.getSplitCampaignId(), usersRequest.getCampaignId());
	}
	
	/**
	 * 
	 * This function is used to evict userAllCampaignAccessCache and userCampaignAccessCache for multiple Users
	 * 
	 * @param userIds,accountId,splitCampaignId,campaignId
	 * 
	 */
	private void evictCachesForMultipleUsers(List<Integer> userIds, Integer accountId, Integer splitCampaignId, Integer campaignId) {
		userIds.stream().forEach(userId -> {
			evictUserAllCampaignCache(userId, accountId, campaignId != null ? Constants.USER_ACCESS_CAMPAIGN : Constants.USER_ACCESS_SPLIT_CAMPAIGN);
			evictUserCampaignCache(userId, campaignId, splitCampaignId);
		});
	}
	
	private void populateNewOwnerMappingForCampaigns(List<CampaignUserMapping> userAccessMappings, List<Integer> ownerCampaignIds, List<Integer> ownerSplitCampaignIds,
			List<CampaignUserMapping> newOwnerMapping, Integer loggedInUserId, Integer newOwnerId) {
		for (CampaignUserMapping userAccess : userAccessMappings) {
			if (StringUtils.equalsIgnoreCase(userAccess.getAccess(), CampaignAccessSettingEnum.OWNER.getAccess())) {
				newOwnerMapping.add(new CampaignUserMapping(userAccess.getAccountId(), userAccess.getCampaignId(), userAccess.getSplitCampaignId(), newOwnerId,
						CampaignAccessSettingEnum.OWNER.getAccess(), loggedInUserId));
				if (CoreUtils.isTrueForInteger(userAccess.getCampaignId())) {
					ownerCampaignIds.add(userAccess.getCampaignId());
				} else if (CoreUtils.isTrueForInteger(userAccess.getSplitCampaignId())) {
					ownerSplitCampaignIds.add(userAccess.getSplitCampaignId());
				}
			}
		}
	}
	
	/***
	 * transfer ownership of campaign to new owner when existing owner is getting deleted from the account
	 * delete the already present entries for new user in the automation
	 * create new entries for automation with new owner id as owner
	 *   
	 */
	@Async
	@Transactional
	@Override
	public void updateCampaignAccessOfDeletedUserWithNewUser(Integer accountId, Integer existingOwnerId, Integer newOwnerId, Integer loggedInUserId) {
		if (accountId == null || existingOwnerId == null || newOwnerId == null) {
			logger.info("invalid request received to transfer ownership for account {} with new owner id {} and old owner id {}", accountId, newOwnerId, existingOwnerId);
			return;
		}
		try {
			List<CampaignUserMapping> userAccessMappings = campaignUserMappingDao.fetchAllCampaignUserAccessForUserAndAccountId(existingOwnerId, accountId);
			if (CollectionUtils.isEmpty(userAccessMappings)) {
				logger.info("no automation access present for the user {} and account id {}", existingOwnerId, accountId);
				return;
			}
			List<Integer> existingOwnerAccessCampaignIds = userAccessMappings.stream().filter(e -> e.getCampaignId() != null).map(e -> e.getCampaignId()).collect(Collectors.toList());
			List<Integer> existingOwnerAccessSplitCampaignIds = userAccessMappings.stream().filter(e -> e.getSplitCampaignId() != null).map(e -> e.getSplitCampaignId()).collect(Collectors.toList());
			
			List<CampaignUserMapping> newOwnerMapping = new ArrayList<>();
			List<Integer> ownerCampaignIds = new ArrayList<>();
			List<Integer> ownerSplitCampaignIds = new ArrayList<>();
			populateNewOwnerMappingForCampaigns(userAccessMappings, ownerCampaignIds, ownerSplitCampaignIds, newOwnerMapping, loggedInUserId, newOwnerId);
			List<CampaignUserMapping> newUserEntryToDeleteInExistingCampaigns = campaignUserMappingDao.fetchCampaignsForUser(newOwnerId, ownerCampaignIds, ownerSplitCampaignIds);
			if (CollectionUtils.isNotEmpty(newUserEntryToDeleteInExistingCampaigns))
					campaignUserMappingDao.deleteUsers(newUserEntryToDeleteInExistingCampaigns);

			campaignUserMappingDao.deleteUsers(userAccessMappings);
			campaignUserMappingDao.addCampaignAccessUsers(newOwnerMapping);
			evictCampaignAccessSettingsCaches(ownerCampaignIds, ownerSplitCampaignIds, newOwnerId, accountId);
			evictCampaignAccessSettingsCaches(existingOwnerAccessCampaignIds, existingOwnerAccessSplitCampaignIds, existingOwnerId, accountId);
		} catch (Exception e) {
			logger.error("error while transfering owner for account {} with new user id {} and old user id {} : {}", accountId, newOwnerId, existingOwnerId, ExceptionUtils.getStackTrace(e));
		}
	}
	
	/**
	 * 
	 * This function is used to evict all user campaign mapping caches
	 * 
	 * @param campaignIds,splitCampaignIds,userId,accountId
	 * 
	 */
	private void evictCampaignAccessSettingsCaches(List<Integer> campaignIds, List<Integer> splitCampaignIds, Integer userId, Integer accountId) {
		if (CollectionUtils.isNotEmpty(campaignIds)) {
			campaignIds.stream().forEach(campaignId -> {
				userAccessSettingHelper.evictCampaignOwnerCache(campaignId);
				evictUserCampaignCache(userId, campaignId, null);
			});
		}
		
		if (CollectionUtils.isNotEmpty(splitCampaignIds)) {
			splitCampaignIds.stream().forEach(splitCampaignId -> {
				userAccessSettingHelper.evictSpliCampaignOwnerCache(splitCampaignId);
				evictUserCampaignCache(userId, null, splitCampaignId);
			});
		}
		
		evictUserAllCampaignCache(userId, accountId, Constants.USER_ACCESS_CAMPAIGN);
		evictUserAllCampaignCache(userId, accountId, Constants.USER_ACCESS_SPLIT_CAMPAIGN);
	}
	
	/**
	 * 
	 * @param userIds
	 * @param accountId
	 * @param userAccessMap
	 * @return
	 *         This function will return the details of the users
	 */
	public List<CampaignUserAccessInfoDetails> getBulkUserInfoWithLocationAccess(List<Integer> userIds, Integer accountId, Map<Integer, String> userAccessMap) {
		
		if (accountId == null || CollectionUtils.isEmpty(userIds)) {
			return null;
		}
		
		BusinessEnterpriseEntity business = cacheService.getValidBusinessById(accountId);
		if (BooleanUtils.isTrue(BusinessUtils.isValidSMBBusiness(business))) {
			BulkUserDetailsResponse response = businessService.getUserDetailsForUserListAndAccountId(userIds, accountId);
			if (response == null || CollectionUtils.isEmpty(response.getUsers())) {
				return null;
			}
			return response.getUsers().stream()
					.map(userInfo -> new CampaignUserAccessInfoDetails(userInfo.getId(), userInfo.getName(), userInfo.getEmailId(), userAccessMap.get(userInfo.getId()), Collections.singletonList(accountId)))
					.collect(Collectors.toList());
		}
		
		else {
			UserLocationAccessResponse response = businessService.getBulkUserInfoWithLocationAccess(userIds, accountId);
			if (response == null || CollectionUtils.isEmpty(response.getUsers())) {
				return null;
			}
			return response.getUsers().stream()
					.map(userInfo -> new CampaignUserAccessInfoDetails(userInfo.getId(), userInfo.getName(), userInfo.getEmailId(), userAccessMap.get(userInfo.getId()), userInfo.getLocationIds()))
					.collect(Collectors.toList());
		}
		
	}
	// Migration Access Setting Flow
	
	/**
	 * 
	 * Migrate Access Settings for Existing automations And Split automations in the requested account.
	 * 1. Basic Validation of the Request
	 * 2. Fetch Applicable status
	 * 3. Fetch existing access mappings for given account and prepare separate maps for campaign and split campaign
	 * 4. Fetch non-deleted automations for given account and status
	 * 5. Filter Out Invalid Entries.
	 * 6. Create Access Setting object and add to list
	 * 7. Repeat points 3 -> 5 for split Automation
	 * 8. Save Entries in Database
	 * 
	 * @param request
	 * 
	 */
	@Override
	public void migrateAccessSettingsForAccount(AccessSettingsUpdateRequest request) {
		if (BooleanUtils.isTrue(CampaignUserAccessUtils.isInvalidAcessSettingMigrationRequest(request))) {
			logger.warn("fetchAndUpdateOwnersForCampaignsInAccount :: Invalid request received to update owner for existing campaigns as account id can't be null");
			return;
		}
		
		List<CampaignUserMapping> newOwnerMapping = new ArrayList<>();
		
		List<CampaignUserMapping> existingCampaignUserMappingList = campaignUserMappingDao.fetchAllCampaignUserAccessForAccountId(request.getAccountId());
		Map<Integer, CampaignUserMapping> existingCampaignIdToCampaignUserMappingMap = CampaignUserAccessUtils.prepareCampaignIdToCampaignUserMappingMap(existingCampaignUserMappingList,
				Constants.USER_ACCESS_CAMPAIGN);
		Map<Integer, CampaignUserMapping> existingSplitCampaignIdToCampaignUserMappingMap = CampaignUserAccessUtils.prepareCampaignIdToCampaignUserMappingMap(existingCampaignUserMappingList,
				Constants.USER_ACCESS_SPLIT_CAMPAIGN);
		
		List<AccessSettingMigrateDTO> automationDataList = CampaignUserAccessUtils.filterCampaignAccessDataList(campaignRepo.getCampaignDataByAccountId(request.getAccountId()),
				existingCampaignIdToCampaignUserMappingMap);
		logger.info("fetchAndUpdateOwnersForCampaignsInAccount :: Existing Automation size fetched for account id {} is {}", request.getAccountId(), CollectionUtils.size(automationDataList));
		automationDataList.stream().forEach(accessSettingData -> newOwnerMapping
				.add(new CampaignUserMapping(request.getAccountId(), accessSettingData.getId(), null, accessSettingData.getOwnerId(), CampaignAccessSettingEnum.OWNER.getAccess(), null)));
		
		List<AccessSettingMigrateDTO> splitAutomationDataList = CampaignUserAccessUtils.filterCampaignAccessDataList(splitCampaignDao.getSplitCampaignDataByAccountId(request.getAccountId()),
				existingSplitCampaignIdToCampaignUserMappingMap);
		logger.info("fetchAndUpdateOwnersForCampaignsInAccount :: Existing Split Automation size fetched for account id {} is {}", request.getAccountId(),
				CollectionUtils.size(splitAutomationDataList));
		splitAutomationDataList.stream().forEach(accessSettingData -> newOwnerMapping
				.add(new CampaignUserMapping(request.getAccountId(), null, accessSettingData.getId(), accessSettingData.getOwnerId(), CampaignAccessSettingEnum.OWNER.getAccess(), null)));
		
		campaignUserMappingDao.addCampaignAccessUsers(newOwnerMapping);
		logger.info("fetchAndUpdateOwnersForCampaignsInAccount :: For account id {}, total access settings updated for campaign list size {}", request.getAccountId(),
				CollectionUtils.size(newOwnerMapping));
	}
	
}
