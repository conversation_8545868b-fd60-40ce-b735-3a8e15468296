package com.birdeye.campaign.ai.template.handler;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.validation.constraints.NotNull;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.birdeye.campaign.ai.constant.AIAssociatedEntityEnum;
import com.birdeye.campaign.ai.constant.AITemplateTypeEnum;
import com.birdeye.campaign.ai.dto.EmailTemplateAICreateRespDto;
import com.birdeye.campaign.ai.dto.TemplateAISourceDto;
import com.birdeye.campaign.ai.response.GetEmailTemplateAIResponse;
import com.birdeye.campaign.ai.sro.CustomFieldSRO;
import com.birdeye.campaign.ai.utils.TemplateAIUtils;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.entity.EmailTemplate;
import com.birdeye.campaign.entity.EmailTemplateAI;
import com.birdeye.campaign.entity.TemplateAICustomFieldAssociation;
import com.birdeye.campaign.entity.TemplateReviewSourceMapping;
import com.birdeye.campaign.enums.CustomFieldSourceEnum;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.platform.constant.CustomFieldAssociatedObjectTypeEnum;
import com.birdeye.campaign.service.dao.BusinessEmailTemplateDao;
import com.birdeye.campaign.template.ai.dao.service.EmailTemplateAIDaoService;
import com.birdeye.campaign.template.ai.dao.service.ITemplateAICustomFieldAssociationDao;
import com.birdeye.campaign.template.ai.dao.service.ITemplateReviewSourceMappingDao;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.email.templates.ai.request.EmailTemplateAISaveRequest;

public abstract class AbstractEmailAITemplateTypeHandler implements AIEmailTemplateTypeHandler {
	
	private static final Logger						logger	= LoggerFactory.getLogger(AbstractEmailAITemplateTypeHandler.class);
	
	@Autowired
	private EmailTemplateAIDaoService				emailTemplateAIDaoService;
	
	@Autowired
	private BusinessEmailTemplateDao				businessEmailTemplateDao;
	
	@Autowired
	private ITemplateReviewSourceMappingDao			reviewSourceMappingDao;
	
	@Autowired
	private ITemplateAICustomFieldAssociationDao	customFieldAssociationDao;
	
	/**
	 * Retrieves the EmailTemplateAI object based on the provided templateId.
	 * 
	 * @param templateId
	 *            The ID of the email template.
	 * @param userId
	 *            The ID of the user requesting the template.
	 * @param accountId
	 *            The ID of the account associated with the request.
	 * @return EmailTemplateAI object if found, otherwise throws CampaignException.
	 */
	protected EmailTemplateAI getEmailAITemplateById(Integer templateId, Integer userId, Integer accountId) {
//		EmailTemplate emailTemplate = businessEmailTemplateDao.getEmailTemplateById(templateId);
//		if (emailTemplate == null || emailTemplate.getAiTemplateId() == null || emailTemplate.getAiTemplateId() == 0) {
//			logger.error("Email template not found or AI template ID is null for templateId: {}, userId: {}, accountId: {}", templateId, userId, accountId);
//			throw new CampaignException(ErrorCodes.INVALID_TEMPLATE, "No email template found for the given templateId");
//		}
		
		Integer aiTemplateId = templateId;
		EmailTemplateAI emailTemplateAI = emailTemplateAIDaoService.getEmailTemplateById(aiTemplateId);
		if (emailTemplateAI == null) {
			logger.error("Email template AI not found for aiTemplateId: {}, userId: {}, accountId: {}", aiTemplateId, userId, accountId);
			throw new CampaignException(ErrorCodes.INVALID_TEMPLATE, "No email template AI found for the given TemplateId");
		}
		
		return emailTemplateAI;
	}
	
	/**
	 * Converts EmailTemplateAI to GetEmailTemplateAIResponse.EmailTemplateMetaDataResponse.
	 * 
	 * @param emailTemplateAI
	 *            The EmailTemplateAI object to convert.
	 * @return GetEmailTemplateAIResponse.EmailTemplateMetaDataResponse object with metadata.
	 */
	protected static GetEmailTemplateAIResponse.EmailTemplateMetaDataResponse getEmailTemplateMetaDataResponse(@NotNull EmailTemplateAI emailTemplateAI) {
		GetEmailTemplateAIResponse.EmailTemplateMetaDataResponse metaDataResponse = new GetEmailTemplateAIResponse.EmailTemplateMetaDataResponse();
		if (emailTemplateAI.getMetadata() == null) {
			return metaDataResponse; // Return empty metadata if null
		}
		
		metaDataResponse.setShowReviewCount(emailTemplateAI.getMetadata().getShowReviewCount());
		metaDataResponse.setLocationBrandingEnabled(emailTemplateAI.getMetadata().getLocationBrandingEnabled());
		metaDataResponse.setShowRating(emailTemplateAI.getMetadata().getShowRating());
		return metaDataResponse;
	}
	
	/**
	 * Saves the provided email template.
	 *
	 * @param template
	 *            The email template to be saved.
	 * @return The saved EmailTemplateAI object.
	 * @throws CampaignException
	 *             if the save operation fails.
	 */
	protected EmailTemplateAI saveEmailTemplate(EmailTemplateAI template) {
		if (template == null) {
			return null;
		}
		EmailTemplateAI emailTemplateai = emailTemplateAIDaoService.saveTemplate(template);
		if (emailTemplateai == null) {
			logger.error("Email template AI create/update failed for template object : {}", template);
			throw new CampaignException(ErrorCodes.ERROR_CREATING_AI_TEMPLATE, ErrorCodes.ERROR_CREATING_AI_TEMPLATE.getMessage());
		}
		return emailTemplateai;
	}
	
	/**
	 * Saves a legacy email template using the provided AI template ID.
	 *
	 * @param emailTemplateAIId
	 *            The ID of the AI email template.
	 * @return The ID of the saved legacy email template.
	 * @throws CampaignException
	 *             if the save operation fails.
	 */
	protected Integer saveNonAIEmailTemplate(Integer emailTemplateAIId) {
		if (emailTemplateAIId == null) {
			throw new CampaignException(ErrorCodes.ERROR_CREATING_AI_TEMPLATE, ErrorCodes.ERROR_CREATING_AI_TEMPLATE.getMessage());
		}
		EmailTemplate et = businessEmailTemplateDao.saveEmailTemplateWithAITemplateId(emailTemplateAIId);
		if (et == null) {
			logger.error("Legacy Email template create/update failed for ai template id : {}", emailTemplateAIId);
			throw new CampaignException(ErrorCodes.ERROR_CREATING_AI_TEMPLATE, ErrorCodes.ERROR_CREATING_AI_TEMPLATE.getMessage());
		}
		logger.info("saveLegacyEmailTemplate :: Email Template Id saved for ai template id {} is {}", emailTemplateAIId, et.getId());
		return et.getId();
	}
	
	/**
	 * Generates a unique name for an email template by appending a count if a template with the same name exists.
	 *
	 * @param name
	 *            The base name of the email template.
	 * @param accountId
	 *            The ID of the account associated with the template.
	 * @return A unique name for the email template.
	 */
	protected String getSameNameCountForEmailTemplate(String name, Integer accountId) {
		if (StringUtils.isBlank(name) || accountId == null) {
			return name;
		}
		
		Long sameNameCount = emailTemplateAIDaoService.getSameNameCountForTemplate(name, accountId);
		return (sameNameCount == null || sameNameCount.intValue() == 0l) ? name : StringUtils.join(name, "(", sameNameCount, ")");
	}
	
	/**
	 * Determines the appropriate name for an updated email template.
	 *
	 * @param accountId
	 *            The ID of the account associated with the template.
	 * @param newName
	 *            The new name for the email template.
	 * @param oldName
	 *            The current name of the email template.
	 * @return The name to be used for the updated template.
	 */
	protected String getTemplateNameForUpdate(Integer accountId, String newName, String oldName) {
		if (StringUtils.equalsIgnoreCase(newName, oldName)) {
			return oldName;
		}
		
		String baseTemplateName = StringUtils.substringBeforeLast(newName, "(");
		Long count = emailTemplateAIDaoService.getNameCountForTemplateUpdate(baseTemplateName, oldName, accountId);
		return (count == null || count.intValue() == 0l) ? newName : StringUtils.join(baseTemplateName, "(", count, ")");
	}
	
	/**
	 * Fetches the review source mappings for a given template.
	 *
	 * @param assObjId
	 *            The associated object ID.
	 * @param deviceType
	 *            The type of device (e.g., web, mobile).
	 * @param associatedEntityEnum
	 *            The associated entity type.
	 * @return A list of TemplateReviewSourceMapping objects.
	 */
	protected List<TemplateReviewSourceMapping> fetchReviewSourceMapping(Integer assObjId, String deviceType, AIAssociatedEntityEnum associatedEntityEnum) {
		if (assObjId == null || StringUtils.isBlank(deviceType) || associatedEntityEnum == null) {
			return new ArrayList<>();
		}
		return reviewSourceMappingDao.getReviewSourcesMapping(assObjId, deviceType, associatedEntityEnum.getAssociatedEntityType());
	}
	
	/**
	 * Saves a list of review source mappings.
	 *
	 * @param mappingList
	 *            The list of TemplateReviewSourceMapping objects to be saved.
	 */
	public void saveReviewSourceMappingList(List<TemplateReviewSourceMapping> mappingList) {
		if (CollectionUtils.isEmpty(mappingList)) {
			return;
		}
		reviewSourceMappingDao.saveReviewSourceMappingList(mappingList);
	}
	
	/**
	 * Deletes a list of review source mappings.
	 *
	 * @param mappingList
	 *            The list of TemplateReviewSourceMapping objects to be deleted.
	 */
	protected void deleteReviewSourceMappingList(List<TemplateReviewSourceMapping> mappingList) {
		if (CollectionUtils.isEmpty(mappingList)) {
			return;
		}
		mappingList.stream().filter(mapping -> (mapping != null && mapping.getId() != null)).forEach(mapping -> reviewSourceMappingDao.deleteReviewSourceMapping(mapping));
	}
	
	/**
	 * Saves custom field associations for a template.
	 *
	 * @param templateId
	 *            The ID of the template.
	 * @param templateType
	 *            The type of the template.
	 * @param customFieldsList
	 *            The list of custom fields to associate with the template.
	 * @param userId
	 *            The ID of the user making the request.
	 * @param deleteCustomFields
	 *            Whether to delete existing custom fields before saving.
	 * @param customFieldSourceEnum
	 *            The source of the custom fields.
	 */
	protected void saveTemplateCustomFieldsAssociation(Integer templateId, String templateType, List<CustomFieldSRO> customFieldsList, Integer userId, boolean deleteCustomFields,
			CustomFieldSourceEnum customFieldSourceEnum) {
		// first delete all custom fields for that object id and type
		if (deleteCustomFields) {
			int count = customFieldAssociationDao.deleteCustomField(templateId, CustomFieldAssociatedObjectTypeEnum.getObjectTypeByCommType(templateType), customFieldSourceEnum);
			logger.info("for template id {} and type {} the fields {} has been deleted", templateId, CustomFieldAssociatedObjectTypeEnum.getObjectTypeByCommType(templateType), count);
		}
		
		if (CollectionUtils.isEmpty(customFieldsList)) {
			return;
		}
		
		customFieldAssociationDao.saveCustomFieldList(TemplateAIUtils.prepareCustomFieldAssociationEntityFromSro(customFieldsList, templateId, templateType, userId, customFieldSourceEnum));
	}
	
	/**
	 * Prepares and updates review sources for a template.
	 *
	 * @param accountId
	 *            The ID of the account associated with the template.
	 * @param templateId
	 *            The ID of the template.
	 * @param request
	 *            The request payload containing the selected sources.
	 * @throws CampaignException
	 *             if the update operation fails.
	 */
	protected void prepareAndUpdateReviewSources(Integer templateId, EmailTemplateAISaveRequest request) {
		Map<String, List<TemplateAISourceDto>> resultantMap = TemplateAIUtils.prepareAndGetUpdatedReviewSourceForTemplate(
				request.getAccountId(), templateId, Constants.DEVICE_TYPE_WEB,
				request.getSelectedSources(), fetchReviewSourceMapping(templateId, Constants.DEVICE_TYPE_WEB, AIAssociatedEntityEnum.EMAIL_TEMPLATE), AIAssociatedEntityEnum.EMAIL_TEMPLATE);
		
		if (resultantMap.containsKey("addSourceList")) {
			saveReviewSourceMappingList(TemplateAIUtils.getEntityFromDto(resultantMap.get("addSourceList")));
		}
		
		if (resultantMap.containsKey("removeSourceList")) {
			deleteReviewSourceMappingList(TemplateAIUtils.getEntityFromDto(resultantMap.get("removeSourceList")));
		}
	}
	
	protected List<CustomFieldSRO> getActiveCustomFieldsFromRequest(Integer objectId, CustomFieldAssociatedObjectTypeEnum associatedObjectTypeEnum, CustomFieldSourceEnum customFieldSourceEnum) {
		List<TemplateAICustomFieldAssociation> customFieldAssociationList = customFieldAssociationDao.getActiveCustomFields(objectId,associatedObjectTypeEnum.getObjectType(),customFieldSourceEnum);
		return customFieldAssociationList.stream().map(customFieldAssociation -> new CustomFieldSRO(customFieldAssociation.getId(),customFieldAssociation.getCustomFieldName())).collect(Collectors.toList());
	}

	/**
	 * Executes the common flow for creating or updating an email template.
	 *
	 * @param request
	 *            The request payload containing template details.
	 * @param typeEnum
	 *            The type of the email template.
	 * @return EmailTemplateAICreateRespDto containing the details of the saved template.
	 * @throws CampaignException
	 *             if an error occurs during processing.
	 */
	protected EmailTemplateAICreateRespDto createUpdateTemplateCommonFlow(EmailTemplateAISaveRequest request, AITemplateTypeEnum typeEnum) {
		
		// 1.1 Get Existing Template or New Template
		EmailTemplateAI template = (TemplateAIUtils.isNewTemplate(request.getTemplateId())) ? new EmailTemplateAI(typeEnum.getName(), request.getAccountId()) : getEmailAITemplateById(request.getTemplateId(), request.getUserId(), request.getAccountId());
		
		// 1.2 Generic Data Preparation
		TemplateAIUtils.prepareGenericEmailTemplateBody(template, request, typeEnum.getName());
		
		// 1.3 Name Preparation
		template.setName((TemplateAIUtils.isNewTemplate(request.getTemplateId())) ? getSameNameCountForEmailTemplate(request.getName(), request.getAccountId())
				: getTemplateNameForUpdate(request.getAccountId(), request.getName(), template.getName()));
		
		// 1.4 Save AI Email Template
		template = saveEmailTemplate(template);
		
		// 1.5 Save Legacy Email Template
		if ((TemplateAIUtils.isNewTemplate(request.getTemplateId()))) {
			saveNonAIEmailTemplate(template.getId());
		}
		
		// Default template handling & Location Level Template Handling
		
		// 1.6 Save/Update Custom Field Association
		saveTemplateCustomFieldsAssociation(template.getId(), Constants.TEMPLATE_BASE_TYPE_EMAIL, request.getContactCustomFields(), request.getUserId(), true, CustomFieldSourceEnum.CONTACT);
		saveTemplateCustomFieldsAssociation(template.getId(), Constants.TEMPLATE_BASE_TYPE_EMAIL, request.getLocationCustomFields(), request.getUserId(), true, CustomFieldSourceEnum.LOCATION);
		
		return new EmailTemplateAICreateRespDto(template.getId(), template.getName());
	}
	
	protected static void populateCommonFieldsInGetTemplateResponse(EmailTemplateAI emailTemplateAI, GetEmailTemplateAIResponse getEmailTemplateAIResponse,String templateType) {
        getEmailTemplateAIResponse.setName(emailTemplateAI.getName());
        getEmailTemplateAIResponse.setType(templateType);
        getEmailTemplateAIResponse.setEnableReplyToInbox(emailTemplateAI.getEnableReplyToInbox());
        getEmailTemplateAIResponse.setHtml(emailTemplateAI.getHtmlContent());
        getEmailTemplateAIResponse.setIsDefault(emailTemplateAI.getIsDefaultTemplate());
        getEmailTemplateAIResponse.setAttachmentUrls(CoreUtils.parseDelimitedStringsWithDefaultAsEmptyList(emailTemplateAI.getAttachmentUrls(), ","));
        getEmailTemplateAIResponse.setPreviewText(emailTemplateAI.getPreviewText());
        getEmailTemplateAIResponse.setSubject(emailTemplateAI.getSubject());
        getEmailTemplateAIResponse.setThumbnailUrl(emailTemplateAI.getThumbnailUrl());
        getEmailTemplateAIResponse.setBrandKitId(emailTemplateAI.getBrandkitId());
        getEmailTemplateAIResponse.setTemplateCategory(emailTemplateAI.getEmailCategory());
        getEmailTemplateAIResponse.setMetadata(getEmailTemplateMetaDataResponse(emailTemplateAI));
    }
	
}
