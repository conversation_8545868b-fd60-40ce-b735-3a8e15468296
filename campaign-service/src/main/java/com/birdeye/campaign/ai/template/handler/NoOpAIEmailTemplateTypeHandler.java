package com.birdeye.campaign.ai.template.handler;

import org.springframework.stereotype.Service;

import com.birdeye.campaign.ai.response.GetEmailTemplateAIResponse;
import com.birdeye.email.templates.ai.request.EmailTemplateAISaveRequest;
import com.birdeye.email.templates.ai.response.EmailTemplateAISaveResponse;

@Service(value = "noOpEmailAITemplateTypeHandler")
public class NoOpAIEmailTemplateTypeHandler extends AbstractEmailAITemplateTypeHandler {
    /**
     * Retrieves an email template based on the provided type, template ID, user ID, and business ID.
     *
     * @param templateId The unique identifier for the email template.
     * @param userId     The ID of the user requesting the template.
     * @param businessId The ID of the business associated with the request.
     * @return A response object containing the email template details.
     */
    @Override
    public GetEmailTemplateAIResponse getEmailTemplate(Integer templateId, Integer userId, Integer businessId) {
        return new GetEmailTemplateAIResponse();
    }

	@Override
	public EmailTemplateAISaveResponse saveTemplate(EmailTemplateAISaveRequest request) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}
}
