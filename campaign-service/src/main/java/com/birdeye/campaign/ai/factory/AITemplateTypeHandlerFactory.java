package com.birdeye.campaign.ai.factory;

import com.birdeye.campaign.ai.constant.AITemplateTypeEnum;
import com.birdeye.campaign.ai.template.handler.AIEmailTemplateTypeHandler;
import com.birdeye.campaign.platform.constant.TemplateTypeEnum;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class AITemplateTypeHandlerFactory {
	
	private static final String					NO_OP_TEMPLATE_TYPE	= "NO_OP_TEMPLATE_HANDLER";
	
	private final AIEmailTemplateTypeHandler	rrEmailAITemplateTypeHandler;
	
	private final AIEmailTemplateTypeHandler	promotionEmailAITemplateTypeHandler;
	
	private final AIEmailTemplateTypeHandler	noOpEmailAITemplateTypeHandler;					// Fallback handler for unsupported types
	
	public AITemplateTypeHandlerFactory(@Qualifier(value = "rrEmailAITemplateTypeHandler") AIEmailTemplateTypeHandler rrEmailAITemplateTypeHandler,
			@Qualifier(value = "noOpEmailAITemplateTypeHandler") AIEmailTemplateTypeHandler noOpEmailAITemplateTypeHandler,
			@Qualifier(value = "promotionEmailAITemplateTypeHandler") AIEmailTemplateTypeHandler promotionEmailAITemplateTypeHandler) {
		this.rrEmailAITemplateTypeHandler = rrEmailAITemplateTypeHandler;
		this.noOpEmailAITemplateTypeHandler = noOpEmailAITemplateTypeHandler;
		this.promotionEmailAITemplateTypeHandler = promotionEmailAITemplateTypeHandler;
	}
	
	private static final Map<String, AIEmailTemplateTypeHandler> strategies = new ConcurrentHashMap<>();
	
	@PostConstruct
	private void init() {
		strategies.put(AITemplateTypeEnum.REVIEW_REQUEST.getName(), rrEmailAITemplateTypeHandler);
		strategies.put(AITemplateTypeEnum.PROMOTION.getName(), promotionEmailAITemplateTypeHandler);
		strategies.put(NO_OP_TEMPLATE_TYPE, noOpEmailAITemplateTypeHandler); // Registering the NoOp handler
	}
	
	public static AIEmailTemplateTypeHandler getStrategy(String type) {
		AIEmailTemplateTypeHandler aiEmailTemplateTypeHandler = strategies.get(type);
		if (aiEmailTemplateTypeHandler == null) {
			aiEmailTemplateTypeHandler = strategies.get(NO_OP_TEMPLATE_TYPE); // Fallback to NoOp handler if type not found
		}
		
		return aiEmailTemplateTypeHandler;
	}
}