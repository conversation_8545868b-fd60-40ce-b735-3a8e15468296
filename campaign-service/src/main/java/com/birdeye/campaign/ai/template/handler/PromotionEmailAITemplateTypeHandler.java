package com.birdeye.campaign.ai.template.handler;

import java.util.List;

import com.birdeye.campaign.ai.constant.AITemplateTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.birdeye.campaign.ai.constant.AITemplateTypeEnum;
import com.birdeye.campaign.ai.dto.EmailTemplateAICreateRespDto;
import com.birdeye.campaign.ai.response.GetEmailTemplateAIResponse;
import com.birdeye.campaign.ai.utils.TemplateAIUtils;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.entity.EmailTemplateAI;
import com.birdeye.campaign.enums.CustomFieldSourceEnum;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.platform.constant.CustomFieldAssociatedObjectTypeEnum;
import com.birdeye.campaign.platform.constant.FileFormat;
import com.birdeye.campaign.platform.constant.TemplateTypeEnum;
import com.birdeye.campaign.utils.PublicMediaUrlUtils;
import com.birdeye.email.templates.ai.request.EmailTemplateAISaveRequest;
import com.birdeye.email.templates.ai.response.EmailTemplateAISaveResponse;

@Service(value = "promotionEmailAITemplateTypeHandler")
public class PromotionEmailAITemplateTypeHandler extends AbstractEmailAITemplateTypeHandler {
	
    private static final String PROMOTION_TEMPLATE_NAME = AITemplateTypeEnum.PROMOTION.name();

    private static final String PROMOTION_TEMPLATE_LABEL = AITemplateTypeEnum.PROMOTION.getLabel();
	
	@Override
	public GetEmailTemplateAIResponse getEmailTemplate(Integer templateId, Integer userId, Integer accountId) {
		if (TemplateAIUtils.isNewTemplate(templateId)) {
            return defaultPromotionEmailTemplateResponse(templateId, userId, accountId);
        }

        return getEmailAITemplateByIdResponse(templateId, userId, accountId);
	}

	private GetEmailTemplateAIResponse defaultPromotionEmailTemplateResponse(Integer templateId, Integer userId, Integer accountId) {
		// TODO : review if we need to fetch the global default template for promotion
		return new GetEmailTemplateAIResponse();
	}
	
	/**
     * Retrieves the EmailTemplateAI object based on the provided templateId.
     *
     * @param templateId The ID of the email template.
     * @param userId           The ID of the user requesting the template.
     * @param businessId       The ID of the business associated with the request.
     * @return EmailTemplateAI object if found, otherwise throws CampaignException.
     */
    private GetEmailTemplateAIResponse getEmailAITemplateByIdResponse(Integer templateId, Integer userId, Integer businessId) {
        EmailTemplateAI emailTemplateAI = getEmailAITemplateById(templateId, userId, businessId);
        return getGetEmailTemplateAIResponseForExistingTemplate(templateId, emailTemplateAI, businessId);
    }

	private GetEmailTemplateAIResponse getGetEmailTemplateAIResponseForExistingTemplate(Integer templateId, EmailTemplateAI emailTemplateAI, Integer businessId) {
		GetEmailTemplateAIResponse getEmailTemplateAIResponse = new GetEmailTemplateAIResponse();
		populateCommonFieldsInGetTemplateResponse(emailTemplateAI, getEmailTemplateAIResponse, PROMOTION_TEMPLATE_NAME);
		
        getEmailTemplateAIResponse.setContactCustomFields(getActiveCustomFieldsFromRequest(templateId, CustomFieldAssociatedObjectTypeEnum.EMAIL_TEMPLATE, CustomFieldSourceEnum.CONTACT));
        getEmailTemplateAIResponse.setLocationCustomFields(getActiveCustomFieldsFromRequest(templateId, CustomFieldAssociatedObjectTypeEnum.EMAIL_TEMPLATE, CustomFieldSourceEnum.LOCATION));

        return getEmailTemplateAIResponse;
	}

	/**
	 * Creates or updates a promotion email template.
	 *
	 * @param userId
	 *            The ID of the user making the request.
	 * @param accountId
	 *            The ID of the account associated with the request.
	 * @param templateId
	 *            The ID of the email template to be created or updated.
	 * @param request
	 *            The request payload containing template details.
	 * @return EmailTemplateAISaveResponse containing the details of the saved template.
	 * @throws CampaignException
	 *             if the request is invalid or an error occurs during processing.
	 */
	@Override
	@Transactional(value = "campaignTransactionManager", rollbackFor = Exception.class)
	public EmailTemplateAISaveResponse saveTemplate(EmailTemplateAISaveRequest request) throws Exception {
		// 1.1 Validate Media Urls
		validateMediaUrls(request);
		
		// 1.2 Execute Basic Create/Update Template Flow
		EmailTemplateAICreateRespDto respDto = createUpdateTemplateCommonFlow(request, AITemplateTypeEnum.PROMOTION);
		
		return new EmailTemplateAISaveResponse(respDto.getAiTemplateId(), respDto.getName());
	}
	
	/**
	 * Validates the media URLs provided in the email template request.
	 *
	 * @param request
	 *            The request payload containing the media URLs to validate.
	 * @throws CampaignException
	 *             if any of the media URLs are invalid or inaccessible.
	 */
	private boolean validateMediaUrls(EmailTemplateAISaveRequest request) {
		List<String> attachmentUrls = request.getAttachmentUrls();
		if (CollectionUtils.isEmpty(attachmentUrls)) {
			return true;
		}
		
		if (attachmentUrls.size() > 1) {
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "Only one attachment is allowed per email template.");
		}
		
		String fileType = PublicMediaUrlUtils.getFileExtensionFromUrl(attachmentUrls.get(0));
		if (!FileFormat.isValidFormat(fileType)) {
			throw new CampaignException(ErrorCodes.INVALID_REQUEST, "Only PDF, JPEG, JPG, and PNG files are allowed.");
		}
		
		return true;
	}
	
}
