package com.birdeye.campaign.report.impl;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.WordUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.bucket.nested.Nested;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.Sum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.aspect.annotation.Profiled;
import com.birdeye.campaign.business.service.BusinessService;
import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.communication.message.AppointmentFormCommMessage;
import com.birdeye.campaign.communication.message.AppointmentRecallCommMessage;
import com.birdeye.campaign.communication.message.AppointmentReminderCommMessage;
import com.birdeye.campaign.communication.message.CXCommMessage;
import com.birdeye.campaign.communication.message.CommMessage;
import com.birdeye.campaign.communication.message.ReviewRequstToCheckInDetailsMapResponse;
import com.birdeye.campaign.communication.message.SurveyCommMessage;
import com.birdeye.campaign.communication.message.referral.ReferralClickSourceCount;
import com.birdeye.campaign.communication.message.referral.ReferralCommMessage;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.customer.service.CustomerService;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.BusinessTemplateEntity;
import com.birdeye.campaign.dto.BusinessTemplateMessage;
import com.birdeye.campaign.dto.CampaignStatusReportData;
import com.birdeye.campaign.dto.CampaignStatusReportMessage;
import com.birdeye.campaign.dto.CommunicationUsageStatsMessage;
import com.birdeye.campaign.dto.DoupDownloadResponse;
import com.birdeye.campaign.dto.PromotionCustomerMessage;
import com.birdeye.campaign.dto.ReferralSummaryReportResponse;
import com.birdeye.campaign.dto.ReferralSummaryReportResponse.ReferralShareInfo;
import com.birdeye.campaign.dto.ReferralUsageSentReportMessage;
import com.birdeye.campaign.dto.ReferralUsageSharedReportDTO;
import com.birdeye.campaign.dto.ReferralUsageSharedReportResponse;
import com.birdeye.campaign.dto.ResellerWidgetCampaignsUsageSummary;
import com.birdeye.campaign.dto.ResellerWidgetGalleryCampaignResponse;
import com.birdeye.campaign.dto.SplitCampaignUsageRequest;
import com.birdeye.campaign.dto.TemplateUsageRequest;
import com.birdeye.campaign.dto.ViewRecipientsSummaryDTO;
import com.birdeye.campaign.elasticsearch.request.ElasticSearchBaseRequest;
import com.birdeye.campaign.elasticsearch.service.ElasticQueryTemplateEnum;
import com.birdeye.campaign.elasticsearch.service.ElasticSearchHelperService;
import com.birdeye.campaign.elasticsearch.service.ElasticSearchService;
import com.birdeye.campaign.elasticsearch.service.ElasticsearchHighLevelClientService;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.executor.services.CampaignCallable;
import com.birdeye.campaign.executor.services.CampaignExecutorService;
import com.birdeye.campaign.executor.services.ExecutorCommonService;
import com.birdeye.campaign.external.factory.ElasticSearchClientFactory;
import com.birdeye.campaign.external.utils.ESUtils;
import com.birdeye.campaign.platform.constant.CampaignTypeEnum;
import com.birdeye.campaign.platform.constant.ReferralSourceTypeEnum;
import com.birdeye.campaign.platform.constant.RequestStatusEnum;
import com.birdeye.campaign.platform.constant.TemplateTypeEnum;
import com.birdeye.campaign.platform.entity.User;
import com.birdeye.campaign.platform.readonly.repository.UserReadOnlyRepo;
import com.birdeye.campaign.report.ReportService;
import com.birdeye.campaign.report.ReportServiceUtils;
import com.birdeye.campaign.report.utils.UsageReportsUtil;
import com.birdeye.campaign.repository.PromotionRepo;
import com.birdeye.campaign.request.CampaignUsageInfo;
import com.birdeye.campaign.request.ReferralReportRequest;
import com.birdeye.campaign.request.ReferralSummaryReportRequest;
import com.birdeye.campaign.response.ExternalReferralSentReportResponse;
import com.birdeye.campaign.response.ExternalReferralSharedReportResponse;
import com.birdeye.campaign.response.ExternalReferralUsageSentReportResponse;
import com.birdeye.campaign.response.ExternalReferralUsageSharedReportResponse;
import com.birdeye.campaign.response.external.CheckinIdDetails;
import com.birdeye.campaign.response.external.CheckinIdDetails.AssistedByDetails;
import com.birdeye.campaign.response.external.CustomerInfoResponse;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.CampaignStatusReportService;
import com.birdeye.campaign.service.ReviewRequestHelperService;
import com.birdeye.campaign.service.referral.IReferralAppointmentService;
import com.birdeye.campaign.service.viewrecipients.IViewRecipientsService;
import com.birdeye.campaign.utils.CampaignUtils;
import com.birdeye.campaign.utils.ControllerUtil;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.DateTimeUtils;
import com.birdeye.referral.request.ReferralLeadsCountRequest;
import com.birdeye.referral.response.ReferralLeadsCountResponse;
import com.birdeye.referral.response.ReferralUsageSentReportResponse;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonObject;

import io.searchbox.core.search.aggregation.TermsAggregation;

@Service("reportService")
public class ReportServiceImpl implements ReportService {
	
	private static final String					CAMPAIGN_ID							= "campaignId";
	
	private static final String					GRP_BY_TEMPLATE						= "grp_by_template";
	
	private static final String					TEMPLATE_IDS						= "templateIds";
	
	private static final String					GRP_BY_CAMPAIGN						= "grp_by_campaign";
	
	private static final String					AGGREGATION_SIZE					= "aggregationSize";
	
	private static final String					CAMPAIGN_IDS						= "campaignIds";
	
	private static final String					ENTERPRISE_ID						= "enterpriseId";
	
	private static final String					TYPE								= "type";
	
	private static final String					INDEX								= "index";
	
	private static final String					QUERY								= "query";
	
	private static final String					BUSINESS_IDS						= "businessIds";
	
	private static final Integer				ELASTIC_MAX_SIZE					= 10000;
	
	private static final DateTimeFormatter 		DATE_TIME_FORMATTER					= DateTimeFormatter.ofPattern("uuuu-MM-dd HH:mm:ss");
	
	private static final String 				CAMPAIGN_COUNT 						= "campaignCount";
	
	private static final String 				TOTAL_CAMPAIGN_COUNT				= "totalCampaignCount";
	
	private static final Logger					logger								= LoggerFactory.getLogger(ReportServiceImpl.class);
	
	@Autowired
	private ElasticSearchHelperService			elasticSearchHelperService;
	
	@Autowired
	private ElasticSearchClientFactory		elasticSearchClientFactory;
	
	@Autowired
	private ElasticSearchService				elasticSearchService;
	
	@Autowired
	@Qualifier(Constants.CAMPAIGN_UI_COMPLETABLE_FUTURE_TASK_EXECUTOR)
	private ThreadPoolTaskExecutor				threadPoolTaskExecutor;
	
	@Autowired
	private ExecutorCommonService				executorCommonService;
	
	@Autowired
	private CustomerService						customerService;
	
	@Autowired
	private BusinessService						businessService;
	
	@Autowired
	private PromotionRepo						promotionRepo;
	
	@Autowired
	private UserReadOnlyRepo							userRepo;
	
	@Autowired
	private CampaignStatusReportService			campaignStatusReportService;
	
	@Autowired
	private IViewRecipientsService				viewRecipientsService;
	
	@Autowired
	IReferralAppointmentService					referralAppointmentService;
	
	@Autowired
	ReviewRequestHelperService					reviewRequestHelperService;
	
	@Autowired
	@Qualifier("elasticHighLevelC3ClientService")
	private ElasticsearchHighLevelClientService	elasticsearchHighLevelC3Client;
	
	@Autowired
	private CacheService			cacheService;
	
	private static final String				REFERRAL_USAGE_REPORT_DATE_FORMAT	= "MMM dd, yyyy";
	
	private static final SimpleDateFormat		sdf									= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	
	private static final ObjectMapper			OBJECT_MAPPER						= new ObjectMapper();
	
	static {
		OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
	}
	
	@Override
	public Map<Integer, CommunicationUsageStatsMessage> getCampaignsCommunicationUsageData(int enterpriseId, List<Campaign> campaigns) {
		logger.info("Received request for Campaign report for enterpriseId : {}", enterpriseId);
		Map<Integer, CommunicationUsageStatsMessage> campaignIdToUsageMap = new ConcurrentHashMap<>();
		List<Integer> cxCampaignIds = new ArrayList<>();
		List<Integer> rrCampaignIds = new ArrayList<>();
		List<Integer> surveyCampaignIds = new ArrayList<>();
		List<Integer> referralCampaignIds = new ArrayList<>();
		List<Integer> promotionalCampaignIds = new ArrayList<>();
		List<Integer> appointmentReminderCampaignIds = new ArrayList<>();
		List<Integer> appointmentRecallCampaignIds = new ArrayList<>();
		List<Integer> appointmentFormCampaignIds = new ArrayList<>();
		for (Campaign campaign : campaigns) {
			if (campaign.getCampaignType().equalsIgnoreCase(CampaignTypeEnum.CX_REQUEST.getType())) {
				cxCampaignIds.add(campaign.getId());
			} else if (campaign.getCampaignType().equalsIgnoreCase(CampaignTypeEnum.REVIEW_REQUEST.getType())) {
				rrCampaignIds.add(campaign.getId());
			} else if (campaign.getCampaignType().equalsIgnoreCase(CampaignTypeEnum.SURVEY_REQUEST.getType())) {
				surveyCampaignIds.add(campaign.getId());
			} else if (campaign.getCampaignType().equalsIgnoreCase(CampaignTypeEnum.REFERRAL.getType())) {
				referralCampaignIds.add(campaign.getId());
			} else if (campaign.getCampaignType().equalsIgnoreCase(CampaignTypeEnum.PROMOTIONAL.getType())) {
				promotionalCampaignIds.add(campaign.getId());
			} else if (StringUtils.equalsIgnoreCase(campaign.getCampaignType(), CampaignTypeEnum.APPOINTMENT_REMINDER.getType())) {
				appointmentReminderCampaignIds.add(campaign.getId());
			} else if (StringUtils.equalsIgnoreCase(campaign.getCampaignType(), CampaignTypeEnum.APPOINTMENT_RECALL.getType())) {
				appointmentRecallCampaignIds.add(campaign.getId());
			} else if (StringUtils.equalsIgnoreCase(campaign.getCampaignType(), CampaignTypeEnum.APPOINTMENT_FORM.getType())) {
				appointmentFormCampaignIds.add(campaign.getId());
			}
			
		}
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
		// Submit CX task
		if (CollectionUtils.isNotEmpty(cxCampaignIds)) {
			executorService.submit(getCxCampaignCommunicationUsage(enterpriseId, cxCampaignIds, campaignIdToUsageMap));
		}
		// Submit RR task
		if (CollectionUtils.isNotEmpty(rrCampaignIds)) {
			executorService.submit(getRRCampaignCommunicationUsage(enterpriseId, rrCampaignIds, campaignIdToUsageMap));
		}
		// Submit Survey task
		if (CollectionUtils.isNotEmpty(surveyCampaignIds)) {
			executorService.submit(getSurveyCampaignCommunicationUsage(enterpriseId, surveyCampaignIds, campaignIdToUsageMap));
		}
		// // Submit REFERRAL task
		if (CollectionUtils.isNotEmpty(referralCampaignIds)) {
			executorService.submit(getReferralCampaignCommunicationUsage(enterpriseId, referralCampaignIds, campaignIdToUsageMap));
		}
		// submit PROMOTION task
		if (CollectionUtils.isNotEmpty(promotionalCampaignIds)) {
			executorService.submit(getPromotionalCampaignCommunicationUsage(enterpriseId, promotionalCampaignIds, campaignIdToUsageMap));
		}
		// submit APPOINTMENT REMINDER TASK
		if (CollectionUtils.isNotEmpty(appointmentReminderCampaignIds)) {
			executorService.submit(getAppointmentReminderCampaignCommunicationUsage(enterpriseId, appointmentReminderCampaignIds, campaignIdToUsageMap));
		}
		// submit APPOINTMENT RECALL TASK
		if (CollectionUtils.isNotEmpty(appointmentRecallCampaignIds)) {
			executorService.submit(getAppointmentRecallCampaignCommunicationUsage(enterpriseId, appointmentRecallCampaignIds, campaignIdToUsageMap));
		}
		// submit APPOINTMENT FORM TASK
		if (CollectionUtils.isNotEmpty(appointmentFormCampaignIds)) {
			executorService.submit(getAppointmentFormCampaignCommunicationUsage(enterpriseId, appointmentFormCampaignIds, campaignIdToUsageMap));
		}
		// Getting all Task from Executor
		try {
			logger.info("Executing campaign data fetch task for enterprise : {}", enterpriseId);
			executorCommonService.executeTasks(executorService);
			logger.info("Executed campaign data fetch task for enterprise : {}", enterpriseId);
		} catch (Exception exception) {
			logger.error("Exception occurred while executing campaign data fetch task for enterprise : {}", enterpriseId, exception);
		}
		return campaignIdToUsageMap;
	}
	
	@Override
	public Map<Integer, CommunicationUsageStatsMessage> getSplitCampaignsCommunicationUsageData(int enterpriseId, List<SplitCampaignUsageRequest> splitUsageRequestList) {
		if (CollectionUtils.isEmpty(splitUsageRequestList)) {
			logger.error("Empty request received to fetch Split Campaign usage report for enterpriseId : {}", enterpriseId);
			return new ConcurrentHashMap<>();
		}
		
		logger.info("Received request to fetch Split Campaign usage report for enterpriseId : {} and split data list size {}", enterpriseId, CollectionUtils.size(splitUsageRequestList));
		Map<Integer, CommunicationUsageStatsMessage> campaignIdToUsageMap = new ConcurrentHashMap<>();
		List<Integer> cxCampaignIds = new ArrayList<>();
		List<Integer> rrCampaignIds = new ArrayList<>();
		List<Integer> surveyCampaignIds = new ArrayList<>();
		List<Integer> referralCampaignIds = new ArrayList<>();
		List<Integer> promotionalCampaignIds = new ArrayList<>();
		for (SplitCampaignUsageRequest splitRequestData : splitUsageRequestList) {
			if (splitRequestData.getCampaignType().equalsIgnoreCase(CampaignTypeEnum.CX_REQUEST.getType())) {
				cxCampaignIds.add(splitRequestData.getCampaignId());
			} else if (splitRequestData.getCampaignType().equalsIgnoreCase(CampaignTypeEnum.REVIEW_REQUEST.getType())) {
				rrCampaignIds.add(splitRequestData.getCampaignId());
			} else if (splitRequestData.getCampaignType().equalsIgnoreCase(CampaignTypeEnum.SURVEY_REQUEST.getType())) {
				surveyCampaignIds.add(splitRequestData.getCampaignId());
			} else if (splitRequestData.getCampaignType().equalsIgnoreCase(CampaignTypeEnum.REFERRAL.getType())) {
				referralCampaignIds.add(splitRequestData.getCampaignId());
			} else if (splitRequestData.getCampaignType().equalsIgnoreCase(CampaignTypeEnum.PROMOTIONAL.getType())) {
				promotionalCampaignIds.add(splitRequestData.getCampaignId());
			}
			
		}
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
		// Submit CX task
		if (CollectionUtils.isNotEmpty(cxCampaignIds)) {
			executorService.submit(getCxCampaignCommunicationUsage(enterpriseId, cxCampaignIds, campaignIdToUsageMap));
		}
		// Submit RR task
		if (CollectionUtils.isNotEmpty(rrCampaignIds)) {
			executorService.submit(getRRCampaignCommunicationUsage(enterpriseId, rrCampaignIds, campaignIdToUsageMap));
		}
		// Submit Survey task
		if (CollectionUtils.isNotEmpty(surveyCampaignIds)) {
			executorService.submit(getSurveyCampaignCommunicationUsage(enterpriseId, surveyCampaignIds, campaignIdToUsageMap));
		}
		// // Submit REFERRAL task
		if (CollectionUtils.isNotEmpty(referralCampaignIds)) {
			executorService.submit(getReferralCampaignCommunicationUsage(enterpriseId, referralCampaignIds, campaignIdToUsageMap));
		}
		// submit PROMOTION task
		if (CollectionUtils.isNotEmpty(promotionalCampaignIds)) {
			executorService.submit(getPromotionalCampaignCommunicationUsage(enterpriseId, promotionalCampaignIds, campaignIdToUsageMap));
		}
		
		// Getting all Task from Executor
		try {
			logger.info("Executing split campaign data fetch task for enterprise : {}", enterpriseId);
			executorCommonService.executeTasks(executorService);
			logger.info("Executed split campaign data fetch task for enterprise : {}", enterpriseId);
		} catch (Exception exception) {
			logger.error("Exception occurred while executing split campaign data fetch task for enterprise : {} is {}", enterpriseId, ExceptionUtils.getStackTrace(exception));
		}
		return campaignIdToUsageMap;
	}
	
	private CampaignCallable<Boolean> getAppointmentReminderCampaignCommunicationUsage(int enterpriseId, List<Integer> campaignIds, Map<Integer, CommunicationUsageStatsMessage> campaignIdToUsageMap) {
		logger.info("Received request for Appointment Reminder Campaign report for enterpriseId : {} : {}", enterpriseId);
		return new CampaignCallable<Boolean>("APPOINTMENT_REMINDER_CAMPAIGN") {
			@Override
			public Boolean doCall() throws IOException {
				Map<String, Object> tokenData = new HashMap<>();
				tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
				if (CollectionUtils.isNotEmpty(campaignIds)) {
					tokenData.put(CAMPAIGN_IDS, campaignIds.toString());
					tokenData.put(AGGREGATION_SIZE, String.valueOf(campaignIds.size()));
				}
				try {
					ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData,
							ElasticQueryTemplateEnum.APPOINTMENT_REMINDER_CAMPAIGN_COMMUNICATION_V1.getQueryName(), Constants.APPOINTMENT_REMINDER_REPORT_INDEX,
							Constants.APPOINTMENT_REMINDER_REPORT_TYPE);
					Terms aggregation = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.APPOINTMENT_REMINDER_REPORT_INDEX).executeAndFetchAggregations(esRequest,
							GRP_BY_CAMPAIGN);
					if (aggregation != null) {
						ReportServiceUtils.processAppointmentReminderCampaignAggregationData(aggregation, campaignIdToUsageMap);
					}
				} catch (Exception exp) {
					logger.error("Exception occurred while fetching Appointment Reminder campaign data for enterpriseId : {} and tokenData : {}", enterpriseId, tokenData,
							ExceptionUtils.getStackTrace(exp));
				}
				return true;
			}
		};
	}
	
	private CampaignCallable<Boolean> getAppointmentRecallCampaignCommunicationUsage(int enterpriseId, List<Integer> campaignIds, Map<Integer, CommunicationUsageStatsMessage> campaignIdToUsageMap) {
		logger.info("Received request for Appointment Recall Campaign report for enterpriseId : {} : {}", enterpriseId);
		return new CampaignCallable<Boolean>("APPOINTMENT_RECALL_CAMPAIGN") {
			@Override
			public Boolean doCall() throws IOException {
				Map<String, Object> tokenData = new HashMap<>();
				tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
				if (CollectionUtils.isNotEmpty(campaignIds)) {
					tokenData.put(CAMPAIGN_IDS, campaignIds.toString());
					tokenData.put(AGGREGATION_SIZE, String.valueOf(campaignIds.size()));
				}
				try {
					ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData,
							ElasticQueryTemplateEnum.APPOINTMENT_RECALL_CAMPAIGN_COMMUNICATION.getQueryName(), Constants.APPOINTMENT_RECALL_REPORT_INDEX, Constants.APPOINTMENT_RECALL_REPORT_TYPE);
					Terms aggregation = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.APPOINTMENT_RECALL_REPORT_INDEX).executeAndFetchAggregations(esRequest, GRP_BY_CAMPAIGN);
					if (aggregation != null) {
						ReportServiceUtils.processAppointmentRecallCampaignAggregationData(aggregation, campaignIdToUsageMap);
					}
				} catch (Exception exp) {
					logger.error("Exception occurred while fetching Appointment Recall campaign data for enterpriseId : {} and tokenData : {}", enterpriseId, tokenData,
							ExceptionUtils.getStackTrace(exp));
				}
				return true;
			}
		};
	}
	
	private CampaignCallable<Boolean> getAppointmentFormCampaignCommunicationUsage(int enterpriseId, List<Integer> campaignIds, Map<Integer, CommunicationUsageStatsMessage> campaignIdToUsageMap) {
		logger.info("Received request for Appointment Form Campaign report for enterpriseId : {} : {}", enterpriseId);
		return new CampaignCallable<Boolean>("APPOINTMENT_FORM_CAMPAIGN") {
			@Override
			public Boolean doCall() throws IOException {
				Map<String, Object> tokenData = new HashMap<>();
				tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
				if (CollectionUtils.isNotEmpty(campaignIds)) {
					tokenData.put(CAMPAIGN_IDS, campaignIds.toString());
					tokenData.put(AGGREGATION_SIZE, String.valueOf(campaignIds.size()));
				}
				try {
					ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData,
							ElasticQueryTemplateEnum.APPOINTMENT_FORM_CAMPAIGN_COMMUNICATION.getQueryName(), Constants.APPOINTMENT_FORM_REPORT_INDEX, Constants.APPOINTMENT_FORM_REPORT_TYPE);
					Terms aggregation = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.APPOINTMENT_FORM_REPORT_INDEX).executeAndFetchAggregations(esRequest, GRP_BY_CAMPAIGN);
					if (aggregation != null) {
						ReportServiceUtils.processAppointmentFormCampaignAggregationData(aggregation, campaignIdToUsageMap);
					}
				} catch (Exception exp) {
					logger.error("Exception occurred while fetching Appointment Recall campaign data for enterpriseId : {} and tokenData : {}", enterpriseId, tokenData,
							ExceptionUtils.getStackTrace(exp));
				}
				return true;
			}
		};
	}
	
	@Override
	public CampaignCallable<Boolean> getPromotionalCampaignCommunicationUsage(int enterpriseId, List<Integer> campaignIds, Map<Integer, CommunicationUsageStatsMessage> campaignIdToUsageMap) {
		logger.info("Received request for Promotion Campaign report for enterpriseId : {}", enterpriseId);
		return new CampaignCallable<Boolean>("PROMOTION_CAMPAIGN_REPORT") {
			@Override
			public Boolean doCall() throws IOException {
				Map<String, Object> tokenData = new HashMap<>();
				tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
				if (CollectionUtils.isNotEmpty(campaignIds)) {
					tokenData.put(CAMPAIGN_IDS, campaignIds.toString());
					tokenData.put(AGGREGATION_SIZE, String.valueOf(campaignIds.size()));
				}
				try {
					ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData,
							ElasticQueryTemplateEnum.PROMOTION_CAMPAIGN_COMMUNICATION_USAGE_V1.getQueryName(), Constants.PROMOTION_REPORT_INDEX, Constants.PROMOTION_REPORT_TYPE);
					Terms aggregation = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.PROMOTION_REPORT_INDEX).executeAndFetchAggregations(esRequest, GRP_BY_CAMPAIGN);
					if (aggregation != null) {
						ReportServiceUtils.processPromotionAggregationDataV1(aggregation, campaignIdToUsageMap);
					}
				} catch (Exception exp) {
					logger.error("Exception occurred while fetching Promotion campaign data for enterpriseId : {} and tokenData : {}", enterpriseId, tokenData, exp);
				}
				return true;
			}
		};
	}
	
	@Override
	public CampaignCallable<Boolean> getCxCampaignCommunicationUsage(int enterpriseId, List<Integer> campaignIds,
			Map<Integer, CommunicationUsageStatsMessage> campaignIdToUsageMap) {
		logger.info("Received request for CX Campaign report for enterpriseId : {}", enterpriseId);
		return new CampaignCallable<Boolean>("CX_CAMPAIGN_REPORT") {
			@Override
			public Boolean doCall() throws IOException {
				Map<String, Object> tokenData = new HashMap<>();
				tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
				if (CollectionUtils.isNotEmpty(campaignIds)) {
					tokenData.put(CAMPAIGN_IDS, campaignIds.toString());
					tokenData.put(AGGREGATION_SIZE, String.valueOf(campaignIds.size()));
				}
				try {
					ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData,
							ElasticQueryTemplateEnum.CX_CAMPAIGN_COMMUNICATION_TEMPLATE_V1.getQueryName(), Constants.CX_REPORT_INDEX, Constants.CX_REPORT_TYPE);
					Terms aggregation = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.CX_REPORT_INDEX).executeAndFetchAggregations(esRequest, GRP_BY_CAMPAIGN);
					if (aggregation != null) {
						 
						if (CollectionUtils.isNotEmpty(aggregation.getBuckets())) {
							ReportServiceUtils.processCXAggregationDataV1(aggregation, campaignIdToUsageMap);
						}
					}
				} catch (Exception exp) {
					logger.error("Exception occurred while fetching cx campaign data for enterpriseId : {} and tokenData : {}", enterpriseId, tokenData, exp);
				}
				return true;
			}
		};
	}
	
	@Override
	public CampaignCallable<Boolean> getReferralCampaignCommunicationUsage(int enterpriseId, List<Integer> campaignIds,
			Map<Integer, CommunicationUsageStatsMessage> campaignIdToUsageMap) {
		logger.info("Received request for Referral Campaign report for enterpriseId : {}", enterpriseId);
		return new CampaignCallable<Boolean>("REFERRAL_CAMPAIGN_REPORT") {
			@Override
			public Boolean doCall() throws IOException {
				Map<String, Object> tokenData = new HashMap<>();
				tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
				if (CollectionUtils.isNotEmpty(campaignIds)) {
					tokenData.put(CAMPAIGN_IDS, campaignIds.toString());
					tokenData.put(AGGREGATION_SIZE, String.valueOf(campaignIds.size()));
				}
				try {
					ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData,
							ElasticQueryTemplateEnum.REFERRAL_CAMPAIGN_COMMUNICATION_TEMPLATE_V1.getQueryName(), Constants.REFERRAL_REPORT_INDEX, Constants.REFERRAL_REPORT_TYPE);
					Terms aggregation = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.REFERRAL_REPORT_INDEX).executeAndFetchAggregations(esRequest, GRP_BY_CAMPAIGN);
					if (aggregation != null) {
						ReportServiceUtils.processReferralAggregationDataV1(aggregation, campaignIdToUsageMap);
					}
				} catch (Exception exp) {
					logger.error("Exception occurred while fetching Referral campaign data for enterpriseId : {} and tokenData : {}", enterpriseId, tokenData, exp);
				}
				return true;
			}
		};
	}
	
	@Override
	public CampaignCallable<Boolean> getRRCampaignCommunicationUsage(int enterpriseId, List<Integer> campaignIds,
			Map<Integer, CommunicationUsageStatsMessage> campaignIdToUsageMap) {
		logger.info("Received request for RR Campaign report for enterpriseId : {}", enterpriseId);
		return new CampaignCallable<Boolean>("RR_CAMPAIGN_REPORT") {
			@Override
			public Boolean doCall() throws IOException {
				Map<String, Object> tokenData = new HashMap<>();
				tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
				if (CollectionUtils.isNotEmpty(campaignIds)) {
					tokenData.put(CAMPAIGN_IDS, campaignIds.toString());
					tokenData.put(AGGREGATION_SIZE, String.valueOf(campaignIds.size()));
				}
				try {
					ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData,
							ElasticQueryTemplateEnum.RR_CAMPAIGN_COMMUNICATION_TEMPLATE_V2.getQueryName(), Constants.RR_REPORT_INDEX, Constants.RR_REPORT_TYPE);
					Terms aggregation = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.RR_REPORT_INDEX).executeAndFetchAggregations(esRequest, GRP_BY_CAMPAIGN);
					if (aggregation != null) {
						ReportServiceUtils.processRRAggregationDataV1(aggregation, campaignIdToUsageMap);
					}
				} catch (Exception exp) {
					logger.error("Exception occurred while fetching rr campaign data for enterpriseId : {} and tokenData : {}", enterpriseId, tokenData, exp);
				}
				return true;
			}

		};
	}
	
	@Override
	public CampaignCallable<Boolean> getSurveyCampaignCommunicationUsage(int enterpriseId, List<Integer> campaignIds,
			Map<Integer, CommunicationUsageStatsMessage> campaignIdToUsageMap) {
		logger.info("Received request for Survey Campaign report for enterpriseId : {}", enterpriseId);
		return new CampaignCallable<Boolean>("SURVEY_CAMPAIGN_REPORT") {
			@Override
			public Boolean doCall() throws IOException {
				getSurveyCampaignUsage(enterpriseId, campaignIds, campaignIdToUsageMap);
				return true;
			}
		};
	}
	
	@Override
	@Profiled
	public Map<Integer, CommunicationUsageStatsMessage> getTemplatesCommunicationUsageData(Integer enterpriseId, List<TemplateUsageRequest> templates) {
		Map<Integer, CommunicationUsageStatsMessage> templateIdToUsageMap = new ConcurrentHashMap<>();
		List<Integer> cxTemplateIds = new ArrayList<>();
		List<Integer> rrTemplateIds = new ArrayList<>();
		List<Integer> surveyTemplateIds = new ArrayList<>();
		List<Integer> referralTemplateIds = new ArrayList<>();
		List<Integer> promotionalTemplateIds = new ArrayList<>();
		List<Integer> appointmentReminderTemplateIds = new ArrayList<>();
		List<Integer> appointmentRecallTemplateIds = new ArrayList<>();
		List<Integer> appointmentFormTemplateIds = new ArrayList<>();
		for (TemplateUsageRequest template : templates) {
			if (template.getTemplateType().contains(TemplateTypeEnum.CUSTOMER_EXPERIENCE.getName())) {
				cxTemplateIds.add(template.getId());
			} else if (template.getTemplateType().contains("review_request")) {
				rrTemplateIds.add(template.getId());
			} else if (template.getTemplateType().contains(TemplateTypeEnum.SURVEY_REQUEST.getName())) {
				surveyTemplateIds.add(template.getId());
			} else if (template.getTemplateType().contains(TemplateTypeEnum.REFERRAL.getName())) {
				referralTemplateIds.add(template.getId());
			} else if (template.getTemplateType().contains(TemplateTypeEnum.PROMOTION.getName())) {
				promotionalTemplateIds.add(template.getId());
			} else if (template.getTemplateType().contains(TemplateTypeEnum.APPOINTMENT_REMINDER.getName())) {
				appointmentReminderTemplateIds.add(template.getId());
			} else if (template.getTemplateType().contains(TemplateTypeEnum.APPOINTMENT_RECALL.getName())) {
				appointmentRecallTemplateIds.add(template.getId());
			} else if (template.getTemplateType().contains(TemplateTypeEnum.APPOINTMENT_FORM.getName())) {
				appointmentFormTemplateIds.add(template.getId());
			}
		}
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
		// Submit CX task
		if (CollectionUtils.isNotEmpty(cxTemplateIds)) {
			executorService.submit(getCxTemplateCommunicationUsage(enterpriseId, cxTemplateIds, templateIdToUsageMap));
		}
		// Submit RR task
		if (CollectionUtils.isNotEmpty(rrTemplateIds)) {
			executorService.submit(getRRTemplateCommunicationUsage(enterpriseId, rrTemplateIds, templateIdToUsageMap));
		}
		// Submit Survey task
		if (CollectionUtils.isNotEmpty(surveyTemplateIds)) {
			executorService.submit(getSurveyTemplateCommunicationUsage(enterpriseId, surveyTemplateIds, templateIdToUsageMap));
		}
		// Submit Referral task
		if (CollectionUtils.isNotEmpty(referralTemplateIds)) {
			executorService.submit(getReferralTemplateCommunicationUsage(enterpriseId, referralTemplateIds, templateIdToUsageMap));
		}
		
		// Submit Promotion task
		if (CollectionUtils.isNotEmpty(promotionalTemplateIds)) {
			executorService.submit(getPromotionTemplateCommunicationUsage(enterpriseId, promotionalTemplateIds, templateIdToUsageMap));
		}
		
		//submit appointment reminder task
		if (CollectionUtils.isNotEmpty(appointmentReminderTemplateIds)) {
			executorService.submit(getAppointmentReminderTemplateCommunicationUsage(enterpriseId, appointmentReminderTemplateIds, templateIdToUsageMap));
		}
		
		//submit appointment recall task
		if (CollectionUtils.isNotEmpty(appointmentRecallTemplateIds)) {
			executorService.submit(getAppointmentRecallTemplateCommunicationUsage(enterpriseId, appointmentRecallTemplateIds, templateIdToUsageMap));
		}
		
		// submit appointment form task
		if (CollectionUtils.isNotEmpty(appointmentFormTemplateIds)) {
			executorService.submit(getAppointmentFormTemplateCommunicationUsage(enterpriseId, appointmentFormTemplateIds, templateIdToUsageMap));
		}
				
		// Getting all Task from Executor
		try {
			logger.info("Executing template data fetch task for enterprise : {}", enterpriseId);
			executorCommonService.executeTasks(executorService);
			logger.info("Executed template data fetch task for enterprise : {}", enterpriseId);
		} catch (Exception exception) {
			logger.error("Exception occurred while executing template usage data fetch task for enterprise : {}", enterpriseId, exception);
		}
		return templateIdToUsageMap;
	}
	
	@Override
	public CampaignCallable<Boolean> getCxTemplateCommunicationUsage(Integer enterpriseId, List<Integer> cxTemplateIds,
			Map<Integer, CommunicationUsageStatsMessage> templateIdToUsageMap) {
		logger.info("Getting communication usage stats for CX templateIds and enterpriseId {}", enterpriseId);
		return new CampaignCallable<Boolean>("CX_TEMPLATE_USAGE") {
			@Override
			public Boolean doCall() throws IOException {
				Map<String, Object> tokenData = new HashMap<>();
				tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
				if (CollectionUtils.isNotEmpty(cxTemplateIds)) {
					tokenData.put(TEMPLATE_IDS, cxTemplateIds.toString());
					tokenData.put(AGGREGATION_SIZE, String.valueOf(cxTemplateIds.size()));
				}
				try {
					ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData,
							ElasticQueryTemplateEnum.CX_TEMPLATE_COMMUNICATION_USAGE.getQueryName(), Constants.CX_REPORT_INDEX, Constants.CX_REPORT_TYPE);
					Terms aggregation =  elasticSearchClientFactory.getElasticSearchHighClientService(Constants.CX_REPORT_INDEX).executeAndFetchAggregations(esRequest, GRP_BY_TEMPLATE);
					if (aggregation != null) {
						
						if (CollectionUtils.isNotEmpty( aggregation.getBuckets())) {
							ReportServiceUtils.processCXAggragationData(aggregation, templateIdToUsageMap);
						}
					}
				} catch (Exception exp) {
					logger.error("Exception occurred while fetching cx template usage data for enterpriseId : {} and tokenData : {} {}", enterpriseId, tokenData, exp);
				}
				return true;
			}
		};
	}
	
	@Override
	public CampaignCallable<Boolean> getReferralTemplateCommunicationUsage(Integer enterpriseId, List<Integer> referralTemplateIds,
			Map<Integer, CommunicationUsageStatsMessage> templateIdToUsageMap) {
		logger.info("Getting communication usage stats for Referral templateIds and enterpriseId {}", enterpriseId);
		return new CampaignCallable<Boolean>("REFERRAL_TEMPLATE_USAGE") {
			@Override
			public Boolean doCall() throws IOException {
				Map<String, Object> tokenData = new HashMap<>();
				tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
				if (CollectionUtils.isNotEmpty(referralTemplateIds)) {
					tokenData.put(TEMPLATE_IDS, referralTemplateIds.toString());
					tokenData.put(AGGREGATION_SIZE, String.valueOf(referralTemplateIds.size()));
				}
				try {
					ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData,
							ElasticQueryTemplateEnum.REFERRAL_TEMPLATE_COMMUNICATION_USAGE.getQueryName(), Constants.REFERRAL_REPORT_INDEX, Constants.REFERRAL_REPORT_TYPE);
					Terms aggregation = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.REFERRAL_REPORT_INDEX).executeAndFetchAggregations(esRequest, GRP_BY_TEMPLATE);
					if (aggregation != null) {
						ReportServiceUtils.processReferralAggragationData(aggregation, templateIdToUsageMap);
					}
				} catch (Exception exp) {
					logger.error("Exception occurred while fetching Referral template usage data for enterpriseId : {} and tokenData : {} {}", enterpriseId, tokenData, exp);
				}
				return true;
			}
		};
	}
	
	@Override
	public CampaignCallable<Boolean> getPromotionTemplateCommunicationUsage(Integer enterpriseId, List<Integer> promotionTemplateIds,
			Map<Integer, CommunicationUsageStatsMessage> templateIdToUsageMap) {
		logger.info("Getting communication usage stats for Promotion templateIds and enterpriseId {}", enterpriseId);
		return new CampaignCallable<Boolean>("PROMOTION_TEMPLATE_USAGE") {
			@Override
			public Boolean doCall() throws IOException {
				Map<String, Object> tokenData = new HashMap<>();
				tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
				if (CollectionUtils.isNotEmpty(promotionTemplateIds)) {
					tokenData.put(TEMPLATE_IDS, promotionTemplateIds.toString());
					tokenData.put(AGGREGATION_SIZE, String.valueOf(promotionTemplateIds.size()));
				}
				try {
					ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData,
							ElasticQueryTemplateEnum.PROMOTION_TEMPLATE_COMMUNICATION_USAGE.getQueryName(), Constants.PROMOTION_REPORT_INDEX, Constants.PROMOTION_REPORT_TYPE);
					Terms aggregation = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.PROMOTION_REPORT_INDEX).executeAndFetchAggregations(esRequest, GRP_BY_TEMPLATE);
					if (aggregation != null) {
						ReportServiceUtils.processPromotionAggregationData(aggregation, templateIdToUsageMap);
					}
				} catch (Exception exp) {
					logger.error("Exception occurred while fetching promotion template usage data for enterpriseId : {} and tokenData : {} {}", enterpriseId, tokenData, exp);
				}
				return true;
			}
		};
	}
	
	private CampaignCallable<Boolean> getAppointmentReminderTemplateCommunicationUsage(Integer enterpriseId, List<Integer> appointmentReminderTemplateIds,
			Map<Integer, CommunicationUsageStatsMessage> templateIdToUsageMap) {
		logger.info("Getting communication usage stats for appointment reminder templateIds and enterpriseId {}", enterpriseId);
		return new CampaignCallable<Boolean>("APPOINTMENT_REMINDER_TEMPLATE_USAGE_TASK") {
			@Override
			public Boolean doCall() throws IOException {
				Map<String, Object> tokenData = new HashMap<>();
				tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
				if (CollectionUtils.isNotEmpty(appointmentReminderTemplateIds)) {
					tokenData.put(TEMPLATE_IDS, appointmentReminderTemplateIds.toString());
					tokenData.put(AGGREGATION_SIZE, String.valueOf(appointmentReminderTemplateIds.size()));
				}
				try {
					ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData,
							ElasticQueryTemplateEnum.APPOINTMENT_REMINDER_TEMPLATE_COMMUNICATION_USAGE.getQueryName(), Constants.APPOINTMENT_REMINDER_REPORT_INDEX,
							Constants.APPOINTMENT_REMINDER_REPORT_TYPE);
					Terms aggregation = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.APPOINTMENT_REMINDER_REPORT_INDEX).executeAndFetchAggregations(esRequest,
							GRP_BY_TEMPLATE);
					if (aggregation != null) {
						ReportServiceUtils.processAppointmentReminderAggregationData(aggregation, templateIdToUsageMap);
					}
				} catch (Exception exp) {
					logger.error("Exception occurred while fetching appointment reminder template usage data for enterpriseId : {} and tokenData : {} {}", enterpriseId, tokenData, exp);
				}
				return true;
			}
		};
	}
	
	private CampaignCallable<Boolean> getAppointmentRecallTemplateCommunicationUsage(Integer enterpriseId, List<Integer> appointmentRecallTemplateIds,
			Map<Integer, CommunicationUsageStatsMessage> templateIdToUsageMap) {
		logger.info("Getting communication usage stats for appointment recall templateIds and enterpriseId {}", enterpriseId);
		return new CampaignCallable<Boolean>("APPOINTMENT_RECALL_TEMPLATE_USAGE_TASK") {
			@Override
			public Boolean doCall() throws IOException {
				Map<String, Object> tokenData = new HashMap<>();
				tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
				if (CollectionUtils.isNotEmpty(appointmentRecallTemplateIds)) {
					tokenData.put(TEMPLATE_IDS, appointmentRecallTemplateIds.toString());
					tokenData.put(AGGREGATION_SIZE, String.valueOf(appointmentRecallTemplateIds.size()));
				}
				try {
					ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData,
							ElasticQueryTemplateEnum.APPOINTMENT_RECALL_TEMPLATE_COMMUNICATION_USAGE.getQueryName(), Constants.APPOINTMENT_RECALL_REPORT_INDEX,
							Constants.APPOINTMENT_RECALL_REPORT_TYPE);
					Terms aggregation = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.APPOINTMENT_RECALL_REPORT_INDEX).executeAndFetchAggregations(esRequest, GRP_BY_TEMPLATE);
					if (aggregation != null) {
						ReportServiceUtils.processAppointmentRecallAggregationData(aggregation, templateIdToUsageMap);
					}
				} catch (Exception exp) {
					logger.error("Exception occurred while fetching appointment recall template usage data for enterpriseId : {} and tokenData : {} {}", enterpriseId, tokenData, exp);
				}
				return true;
			}
		};
	}
	
	@Override
	public CampaignCallable<Boolean> getRRTemplateCommunicationUsage(Integer enterpriseId, List<Integer> rrTemplateIds,
			Map<Integer, CommunicationUsageStatsMessage> templateIdToUsageMap) {
		logger.info("Getting communication usage stats for RR templateIds and enterpriseId {}", enterpriseId);
		return new CampaignCallable<Boolean>("RR_TEMPLATE_USAGE") {
			@Override
			public Boolean doCall() throws IOException {
				Map<String, Object> tokenData = new HashMap<>();
				tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
				if (CollectionUtils.isNotEmpty(rrTemplateIds)) {
					tokenData.put(TEMPLATE_IDS, rrTemplateIds.toString());
					tokenData.put(AGGREGATION_SIZE, String.valueOf(rrTemplateIds.size()));
				}
				try {
					ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData,
							ElasticQueryTemplateEnum.RR_TEMPLATE_COMMUNICATION_USAGE.getQueryName(), Constants.RR_REPORT_INDEX, Constants.RR_REPORT_TYPE);
					Terms aggregation = (Terms) elasticSearchClientFactory.getElasticSearchHighClientService(Constants.RR_REPORT_INDEX).executeAndFetchAggregations(esRequest, GRP_BY_TEMPLATE);
					if (aggregation != null && CollectionUtils.isNotEmpty(aggregation.getBuckets())) {
						ReportServiceUtils.processRRAggragationData(aggregation, templateIdToUsageMap);
					}
				} catch (Exception exp) {
					logger.error("Exception occurred while fetching rr template usage data for enterpriseId : {} and tokenData : {} {}", enterpriseId, tokenData, exp);
				}
				return true;
			}
		};
	}
	
	@Override
	public CampaignCallable<Boolean> getSurveyTemplateCommunicationUsage(int enterpriseId, List<Integer> templateIds,
			Map<Integer, CommunicationUsageStatsMessage> templateIdToUsageMap) {
		logger.info("Received request for Survey Campaign report for enterpriseId : {}", enterpriseId);
		return new CampaignCallable<Boolean>("SURVEY_TEMPLATE_USAGE") {
			@Override
			public Boolean doCall() throws IOException {
				Map<String, Object> tokenData = new HashMap<>();
				tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
				if (CollectionUtils.isNotEmpty(templateIds)) {
					tokenData.put(TEMPLATE_IDS, templateIds.toString());
					tokenData.put(AGGREGATION_SIZE, String.valueOf(templateIds.size()));
				}
				try {
					ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData,
							ElasticQueryTemplateEnum.SURVEY_TEMPLATE_COMMUNICATION_USAGE.getQueryName(), Constants.SURVEY_REPORT_INDEX, Constants.SURVEY_REPORT_TYPE);
					Terms aggregation = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.SURVEY_REPORT_INDEX).executeAndFetchAggregations(esRequest, GRP_BY_TEMPLATE);
					if (aggregation != null) {
						if (CollectionUtils.isNotEmpty(aggregation.getBuckets())) {
							ReportServiceUtils.processSurveyTemplateUsageData(aggregation, templateIdToUsageMap);
						}
					}
				} catch (Exception exp) {
					logger.error("Exception occurred while fetching survey template usage data for enterpriseId : {} and tokenData : {}", enterpriseId, tokenData, exp);
				}
				return true;
			}
		};
	}
	
	private void prepareTokenDataForSortingAndSearching(Map<String, Object> requestParams, Map<String, Object> tokenData) {
		Integer resultSize = 25;
		if (requestParams.containsKey(ReportServiceUtils.RESULT_SIZE)) {
			resultSize = (Integer) requestParams.get(ReportServiceUtils.RESULT_SIZE);
		}
		Integer resultPage = 0;
		if (requestParams.containsKey(ReportServiceUtils.RESULT_PAGE)) {
			resultPage = (Integer) requestParams.get(ReportServiceUtils.RESULT_PAGE);
		}
		String resultOrder = "desc";
		if (requestParams.containsKey(ReportServiceUtils.RESULT_ORDER)) {
			resultOrder = (String) requestParams.get(ReportServiceUtils.RESULT_ORDER);
		}
		String sortBy = null;
		if (requestParams.containsKey(ReportServiceUtils.SORT_BY)) {
			sortBy = (String) requestParams.get(ReportServiceUtils.SORT_BY);
		}
		String searchBy = null;
		if (requestParams.containsKey(ReportServiceUtils.SEARCH_BY)) {
			searchBy = (String) requestParams.get(ReportServiceUtils.SEARCH_BY);
		}
		if (StringUtils.isNotBlank(searchBy)) {
			tokenData.put(ReportServiceUtils.SEARCH_BY, searchBy);
			tokenData.put(ReportServiceUtils.SEARCH_BY_WILDCARD, WordUtils.capitalize(searchBy));
			
		}
		tokenData.put("size", String.valueOf(resultSize));
		tokenData.put("from", String.valueOf(resultPage * resultSize));
		tokenData.put("sortOrder", resultOrder);
		tokenData.put(ReportServiceUtils.SORT_BY, sortBy);
	}
	
	@Override
	public void prepareCampaignStatusReport(Campaign campaign) {
		logger.info("generating campaign status report for campaign id {}", campaign.getId());
		CampaignStatusReportMessage reportMessage = new CampaignStatusReportMessage();
		reportMessage.setCampaignName(campaign.getName());
		reportMessage.setEnterpriseName(getEnterpriseName(campaign.getEnterpriseId()));
		reportMessage.setPriority(campaign.getPriority());
		User user = userRepo.findFirstById(campaign.getCreatedBy());
		reportMessage.setUserEmail(user.getEmailId());
		reportMessage.setCampaignId(campaign.getId());
		reportMessage.setBusinessId(campaign.getEnterpriseId());
		CampaignTypeEnum campaignType = CampaignTypeEnum.getEnum(campaign.getType());
		try {
			switch (campaignType) {
				case CX_REQUEST:
					generateCXCampaignReportData(reportMessage, campaign.getEnterpriseId(), campaign);
					break;
				
				case REVIEW_REQUEST:
					generateRRCampaignReportData(reportMessage, campaign.getEnterpriseId(), campaign);
					break;
				
				case SURVEY_REQUEST:
					generateSurveyCampaignReportData(reportMessage, campaign.getEnterpriseId(), campaign);
					break;
				
				case PROMOTIONAL:
					generatePromotionCampaignReportData(reportMessage, campaign);
					break;
				
				case REFERRAL:
					generateReferralCampaignReportData(reportMessage, campaign.getEnterpriseId(), campaign);
					break;
				
				default:
					break;
			}
		} catch (Exception e) {
			logger.error("aborting campaign summary email for campaign {} due to error {}", campaign.getId(), e.getMessage());
			return;
		}
		campaignStatusReportService.sendCampaignStatusMail(reportMessage);
	}
	
	private void generateCXCampaignReportData(CampaignStatusReportMessage reportMessage, Integer enterpriseId, Campaign campaign) {
		Map<String, Object> tokenData = new HashMap<>();
		tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
		tokenData.put(CAMPAIGN_ID, String.valueOf(campaign.getId()));
		tokenData.put(ReportServiceUtils.SOURCES, CampaignUtils.getESQueryableStringFromList(Arrays.asList(Constants.RR_SOURCE_EMAIL, Constants.RR_SOURCE_SMS)));
		tokenData.put("size", ELASTIC_MAX_SIZE);
		ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData,
				ElasticQueryTemplateEnum.CX_CAMPAIGN_SUMMARY_AUDIENCE.getQueryName(), Constants.CX_REPORT_INDEX, Constants.CX_REPORT_TYPE);
		SearchResponse searchResponse = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.CX_REPORT_INDEX).execute(esRequest);
		
		if (searchResponse != null && searchResponse.getHits() != null && searchResponse.getHits().getTotalHits().value != 0l) {
			List<CXCommMessage> customerDocsForCampaign = new ArrayList<>();
			for (SearchHit hit : searchResponse.getHits().getHits()) {
				customerDocsForCampaign.add(OBJECT_MAPPER.convertValue(hit.getSourceAsMap(), CXCommMessage.class));
			}
		
			if (CollectionUtils.isNotEmpty(customerDocsForCampaign)) {
				/* will only be fetching failed data now, to support large audience (elastic limit on data size) - so no validation */
				// checkIfAudienceDataInSync(customerDocsForCampaign.size(), campaign);
				Set<Integer> customerIds = new HashSet<>();
				Set<Integer> businessIdsForRequest = new HashSet<>();
				for (CXCommMessage customerCommMessage : customerDocsForCampaign) {
					String failureReason = ReportServiceUtils.getFailureMessageForSummaryReason(customerCommMessage.getFailureReason());
					CampaignStatusReportData reportData = new CampaignStatusReportData(customerCommMessage.getSrc(), customerCommMessage.getBusinessId(),
							customerCommMessage.getCustomerId(), failureReason);
					customerIds.add(customerCommMessage.getCustomerId());
					businessIdsForRequest.add(customerCommMessage.getBusinessId());
					reportMessage.getReportData().add(reportData);
				}
				addCustomerAndBusinessDataForCustomer(reportMessage.getReportData(), customerIds, businessIdsForRequest);
			}
		}
		populateStats(viewRecipientsService.getSummaryByCampaignType(CampaignTypeEnum.getEnum(campaign.getCampaignType()), campaign.getEnterpriseId(), tokenData), reportMessage);
	}
	//
	private void generateReferralCampaignReportData(CampaignStatusReportMessage reportMessage, Integer enterpriseId, Campaign campaign) {
		Map<String, Object> tokenData = new HashMap<>();
		tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
		tokenData.put(CAMPAIGN_ID, String.valueOf(campaign.getId()));
		tokenData.put(ReportServiceUtils.SOURCES, CampaignUtils.getESQueryableStringFromList(Arrays.asList(Constants.RR_SOURCE_EMAIL, Constants.RR_SOURCE_SMS)));
		tokenData.put("size", ELASTIC_MAX_SIZE);
		ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData, ElasticQueryTemplateEnum.REFERRAL_CAMPAIGN_SUMMARY_AUDIENCE.getQueryName(),
				Constants.REFERRAL_REPORT_INDEX, Constants.REFERRAL_REPORT_TYPE);
		SearchResponse searchResponse = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.REFERRAL_REPORT_INDEX).execute(esRequest);
		if (searchResponse != null && searchResponse.getHits() != null && searchResponse.getHits().getTotalHits().value != 0l) {
			List<ReferralCommMessage> customerDocsForCampaign = new ArrayList<>();
			for (SearchHit hit : searchResponse.getHits().getHits()) {
				customerDocsForCampaign.add(OBJECT_MAPPER.convertValue(hit.getSourceAsMap(), ReferralCommMessage.class));
			}
			if (CollectionUtils.isNotEmpty(customerDocsForCampaign)) {
				Set<Integer> customerIds = new HashSet<>();
				Set<Integer> businessIdsForRequest = new HashSet<>();
				for (ReferralCommMessage referralCommMessage : customerDocsForCampaign) {
					String failureReason = ReportServiceUtils.getFailureMessageForSummaryReason(referralCommMessage.getFailureReason());
					
					CampaignStatusReportData reportData = new CampaignStatusReportData(referralCommMessage.getSource(), referralCommMessage.getBusinessId(),
							referralCommMessage.getCustomerId(), failureReason);
					
					customerIds.add(referralCommMessage.getCustomerId());
					businessIdsForRequest.add(referralCommMessage.getBusinessId());
					reportMessage.getReportData().add(reportData);
				}
				addCustomerAndBusinessDataForCustomer(reportMessage.getReportData(), customerIds, businessIdsForRequest);
			}
		}
		populateStats(viewRecipientsService.getSummaryByCampaignType(CampaignTypeEnum.getEnum(campaign.getCampaignType()), campaign.getEnterpriseId(), tokenData), reportMessage);
	}
	
	private void generateRRCampaignReportData(CampaignStatusReportMessage reportMessage, Integer enterpriseId, Campaign campaign) {
		Map<String, Object> tokenData = new HashMap<>();
		tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
		tokenData.put(CAMPAIGN_ID, String.valueOf(campaign.getId()));
		tokenData.put(ReportServiceUtils.SOURCES, CampaignUtils.getESQueryableStringFromList(Arrays.asList(Constants.RR_SOURCE_EMAIL, Constants.RR_SOURCE_SMS)));
		tokenData.put("size", ELASTIC_MAX_SIZE);
		ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData, ElasticQueryTemplateEnum.RR_CAMPAIGN_SUMMARY_AUDIENCE.getQueryName(),
				Constants.RR_REPORT_INDEX, Constants.RR_REPORT_TYPE);
		SearchResponse response = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.RR_REPORT_INDEX).execute(esRequest);
		if (response == null || response.getHits() == null || response.getHits().getHits().length == 0) {
			populateStats(viewRecipientsService.getSummaryByCampaignType(CampaignTypeEnum.getEnum(campaign.getCampaignType()), campaign.getEnterpriseId(), tokenData), reportMessage);
			return;
		}
		
		List<CommMessage> customerDocsForCampaign = ESUtils.mapESSearchResponse(response, CommMessage.class);
		if (CollectionUtils.isNotEmpty(customerDocsForCampaign)) {
			Set<Integer> customerIds = new HashSet<>();
			Set<Integer> businessIdsForRequest = new HashSet<>();
			for (CommMessage customerCommMessage : customerDocsForCampaign) {
				String failureReason = ReportServiceUtils.getFailureMessageForSummaryReason(customerCommMessage.getFailureReason());
				CampaignStatusReportData reportData = new CampaignStatusReportData(customerCommMessage.getSrc(), customerCommMessage.getBusinessId(), customerCommMessage.getCustomerId(),
						failureReason);
				customerIds.add(customerCommMessage.getCustomerId());
				businessIdsForRequest.add(customerCommMessage.getBusinessId());
				reportMessage.getReportData().add(reportData);
			}
			addCustomerAndBusinessDataForCustomer(reportMessage.getReportData(), customerIds, businessIdsForRequest);
		}
		populateStats(viewRecipientsService.getSummaryByCampaignType(CampaignTypeEnum.getEnum(campaign.getCampaignType()), campaign.getEnterpriseId(), tokenData), reportMessage);
	}
	
	private void generateSurveyCampaignReportData(CampaignStatusReportMessage reportMessage, Integer enterpriseId, Campaign campaign) {
		Map<String, Object> tokenData = new HashMap<>();
		tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
		tokenData.put(CAMPAIGN_ID, String.valueOf(campaign.getId()));
		tokenData.put(ReportServiceUtils.SOURCES, CampaignUtils.getESQueryableStringFromList(Arrays.asList(Constants.RR_SOURCE_EMAIL, Constants.RR_SOURCE_SMS)));
		tokenData.put("size", ELASTIC_MAX_SIZE);
		ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData,
				ElasticQueryTemplateEnum.SURVEY_CAMPAIGN_SUMMARY_AUDIENCE.getQueryName(), Constants.SURVEY_REPORT_INDEX, Constants.SURVEY_REPORT_TYPE);
		SearchResponse searchResponse = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.SURVEY_REPORT_INDEX).execute(esRequest);
		if (searchResponse != null && searchResponse.getHits() != null && searchResponse.getHits().getTotalHits().value != 0l) {
			List<SurveyCommMessage> customerDocsForCampaign = new ArrayList<>();
			for (SearchHit hit : searchResponse.getHits().getHits()) {
				customerDocsForCampaign.add(OBJECT_MAPPER.convertValue(hit.getSourceAsMap(), SurveyCommMessage.class));
			}

			if (CollectionUtils.isNotEmpty(customerDocsForCampaign)) {
				Set<Integer> customerIds = new HashSet<>();
				Set<Integer> businessIdsForRequest = new HashSet<>();
				for (SurveyCommMessage customerCommMessage : customerDocsForCampaign) {
					String failureReason = ReportServiceUtils.getFailureMessageForSummaryReason(customerCommMessage.getFailureReason());
					CampaignStatusReportData reportData = new CampaignStatusReportData(customerCommMessage.getSrc(), customerCommMessage.getBusinessId(),
							customerCommMessage.getCustomerId(), failureReason);
					customerIds.add(customerCommMessage.getCustomerId());
					businessIdsForRequest.add(customerCommMessage.getBusinessId());
					reportMessage.getReportData().add(reportData);
				}
				addCustomerAndBusinessDataForCustomer(reportMessage.getReportData(), customerIds, businessIdsForRequest);
			}
		}
		populateStats(viewRecipientsService.getSummaryByCampaignType(CampaignTypeEnum.getEnum(campaign.getCampaignType()), campaign.getEnterpriseId(), tokenData), reportMessage);
	}
	
	private void populateStats(ViewRecipientsSummaryDTO summaryByCampaignType, CampaignStatusReportMessage reportMessage) {
		if (summaryByCampaignType == null) {
			return;
		}
		
		reportMessage.setEmailFailedCount(summaryByCampaignType.getEmailFailed() != null ? summaryByCampaignType.getEmailFailed() : 0l);
		reportMessage.setEmailSentCount(summaryByCampaignType.getEmailDelivered() != null ? summaryByCampaignType.getEmailDelivered() : 0l);
		reportMessage.setSmsFailedCount(summaryByCampaignType.getTextFailed() != null ? summaryByCampaignType.getTextFailed() : 0l);
		reportMessage.setSmsSentCount(summaryByCampaignType.getTextDelivered() != null ? summaryByCampaignType.getTextDelivered() : 0l);
		reportMessage.setTotalCount(summaryByCampaignType.getSent() != null ? summaryByCampaignType.getSent() : 0l);
	}
	
	/**
	 * private int getSummaryMailAudienceSize(Campaign campaign) {
	 * if (CampaignPriorityEnum.EMAIL_AND_SMS.getType().equalsIgnoreCase(campaign.getPriority()))
	 * return 2 * campaign.getCustomerCount();
	 * 
	 * return campaign.getCustomerCount();
	 * }
	 */
	
	private void generatePromotionCampaignReportData(CampaignStatusReportMessage reportMessage, Campaign campaign) {
		List<PromotionCustomerMessage> promotionRequests = promotionRepo.getByCampaignId(campaign.getId());
		if (CollectionUtils.isNotEmpty(promotionRequests)) {
			Set<Integer> customerIds = new HashSet<>();
			Set<Integer> businessIdsForRequest = new HashSet<>();
			for (PromotionCustomerMessage promotion : promotionRequests) {
				if (promotion.getDeliveryStatus().equalsIgnoreCase(RequestStatusEnum.FAILURE.getName())) {
					customerIds.add(promotion.getCustomerId());
					businessIdsForRequest.add(promotion.getBusinessId());
					CampaignStatusReportData reportData = new CampaignStatusReportData(promotion.getSource(), promotion.getBusinessId(), promotion.getCustomerId(),
							promotion.getFailureReason());
					reportMessage.getReportData().add(reportData);
				}
				updateSentCount(reportMessage, promotion.getDeliveryStatus().equalsIgnoreCase(RequestStatusEnum.FAILURE.getName()) ? 0 : 1, promotion.getSource());
			}
			reportMessage.setTotalCount((long) promotionRequests.size());
			addCustomerAndBusinessDataForCustomer(reportMessage.getReportData(), customerIds, businessIdsForRequest);
		}
	}
	
	private void updateSentCount(CampaignStatusReportMessage reportMessage, Integer status, String source) {
		if (status == 0) {
			if ((Constants.RR_SOURCE_EMAIL).equalsIgnoreCase(source))
				reportMessage.setEmailFailedCount(reportMessage.getEmailFailedCount() + 1);
			else
				reportMessage.setSmsFailedCount(reportMessage.getSmsFailedCount() + 1);
		}
		
		else {
			if ((Constants.RR_SOURCE_EMAIL).equalsIgnoreCase(source))
				reportMessage.setEmailSentCount(reportMessage.getEmailSentCount() + 1);
			else
				reportMessage.setSmsSentCount(reportMessage.getSmsSentCount() + 1);
		}
		
	}
	
	private String getEnterpriseName(Integer enterpriseId) {
		BusinessEnterpriseEntity enterprise = businessService.getBusinessById(enterpriseId);
		
		return StringUtils.isNotBlank(enterprise.getAlias1()) ? enterprise.getAlias1() : enterprise.getName();
		
	}
	
	private void addCustomerAndBusinessDataForCustomer(List<CampaignStatusReportData> reportDataSet, Set<Integer> customerIds, Set<Integer> businessIds) {
		
		if (CollectionUtils.isEmpty(reportDataSet)) {
			return;
		}
		
		if (CollectionUtils.isNotEmpty(customerIds)) {
			reportDataSet.removeAll(addCustomerDataAndReturnInvalid(reportDataSet, customerIds));
		}
		
		if (CollectionUtils.isNotEmpty(businessIds)) {
			reportDataSet.removeAll(addBusinessDataAndReturnInvalid(reportDataSet, businessIds));
		}
		
	}
	
	private List<CampaignStatusReportData> addCustomerDataAndReturnInvalid(List<CampaignStatusReportData> reportDataSet, Set<Integer> customerIds) {
		
		Map<Integer, CustomerInfoResponse> idToCustomerMap = customerService.getCustomersMap(customerIds);
		
		List<CampaignStatusReportData> invalidData = new ArrayList<>();
		for (CampaignStatusReportData reportDataPoint : reportDataSet) {
			if (!idToCustomerMap.containsKey(reportDataPoint.getCustomerId())) {
				invalidData.add(reportDataPoint);
				continue;
			}
			
			CustomerInfoResponse customer = idToCustomerMap.get(reportDataPoint.getCustomerId());
			String emailElsePhone = StringUtils.isNotBlank(customer.getEmail()) ? customer.getEmail() : customer.getPhone();
			String phoneElseEmail = StringUtils.isNotBlank(customer.getPhone()) ? customer.getPhone() : customer.getEmail();
			reportDataPoint.setCustomerName(customer.getCustomerName());
			reportDataPoint.setEmailOrPhone(reportDataPoint.getSource().equalsIgnoreCase(Constants.RR_SOURCE_EMAIL) ? emailElsePhone : phoneElseEmail);
		}
		return invalidData;
	}
	
	private List<CampaignStatusReportData> addBusinessDataAndReturnInvalid(List<CampaignStatusReportData> reportDataSet, Set<Integer> businessIds) {
		Map<Integer, BusinessEnterpriseEntity> idToBusinessMap = businessService.getIdToEnterpriseMapForIds(businessIds);
		
		List<CampaignStatusReportData> invalidData = new ArrayList<>();
		for (CampaignStatusReportData reportDataPoint : reportDataSet) {
			if (!idToBusinessMap.containsKey(reportDataPoint.getBusinessId())) {
				invalidData.add(reportDataPoint);
				continue;
			}
			
			reportDataPoint.setBusinessName(idToBusinessMap.get(reportDataPoint.getBusinessId()).getBusinessAlias());
			reportDataPoint.setBusinessNumber(idToBusinessMap.get(reportDataPoint.getBusinessId()).getBusinessId());
		}
		return invalidData;
	}
	
	/**
	 * Listing API for SENT tab - REFERRALS - with filters and pagination
	 * 
	 */
	@Override
	public ReferralUsageSentReportResponse getReferralUsageSentReport(Map<String, Object> requestParams, Integer enterpriseId, ReferralReportRequest referralReportRequest) {
		Map<String, Object> tokenData = prepareTokenDataForReferralUsageSentReport(enterpriseId, referralReportRequest, requestParams);
		ReferralUsageSentReportResponse usageSentResponse = new ReferralUsageSentReportResponse();
		ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData, ElasticQueryTemplateEnum.REFERRAL_USAGE_SENT_TEMPLATE_V2.getQueryName(),
				Constants.REFERRAL_REPORT_INDEX, Constants.REFERRAL_REPORT_TYPE);
		SearchResponse response = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.REFERRAL_REPORT_INDEX).execute(esRequest);
		if (response != null && response.getHits() != null && response.getHits().getTotalHits().value != 0l) {
			usageSentResponse.setTotalCount((int) response.getHits().getTotalHits().value);
			List<ReferralCommMessage> referralCommMessages = new ArrayList<>();
			for (SearchHit hit : response.getHits().getHits()) {
				referralCommMessages.add(OBJECT_MAPPER.convertValue(hit.getSourceAsMap(), ReferralCommMessage.class));
			}
			usageSentResponse.setData(prepareReferralUsageResult(referralCommMessages, null));
		}
		
		return usageSentResponse;
	}
	
	private Integer getTotalCount(JsonObject jsonObject) {
		if (jsonObject != null && jsonObject.get("hits") != null && jsonObject.get("hits").getAsJsonObject() != null
				&& jsonObject.get("hits").getAsJsonObject().getAsJsonPrimitive("total") != null) {
			return jsonObject.get("hits").getAsJsonObject().getAsJsonPrimitive("total").getAsInt();
		}
		return 0;
	}
	
	private List<ReferralUsageSentReportMessage> prepareReferralUsageResult(List<ReferralCommMessage> customerDocsForCampaign,  Map<Long, CheckinIdDetails> reviewRequestToCheckInIdMap) {
		if (CollectionUtils.isEmpty(customerDocsForCampaign)) {
			return Collections.emptyList();
		}
		List<ReferralUsageSentReportMessage> result = new ArrayList<>();
		for (ReferralCommMessage referralCommMessage : customerDocsForCampaign) {
			ReferralUsageSentReportMessage referralUsageSentReportMessage = new ReferralUsageSentReportMessage(referralCommMessage.getCustomerId(),
					referralCommMessage.getCustomerName(), referralCommMessage.getSource(), referralCommMessage.getReferralCode());
			if (referralCommMessage.getRequestDate() != null) {
				referralUsageSentReportMessage.setSentOn(prepareDateAsPerTimeZone(convertFromStringToDate(referralCommMessage.getRequestDate()), REFERRAL_USAGE_REPORT_DATE_FORMAT, referralCommMessage.getEnterpriseId()));
				referralUsageSentReportMessage.setSentOnUTC(DateTimeUtils.prepareFormattedDate(convertFromStringToDate(referralCommMessage.getRequestDate()), REFERRAL_USAGE_REPORT_DATE_FORMAT, "UTC"));
			}
			referralUsageSentReportMessage.setLeadGenerated(referralCommMessage.getAppointmentIds() != null && referralCommMessage.getAppointmentIds().length > 0 ? 1 : 0);
			referralUsageSentReportMessage.setBusinessAlias(referralCommMessage.getBusinessAlias());
			if (MapUtils.isNotEmpty(reviewRequestToCheckInIdMap)) {
				CheckinIdDetails checkinData = reviewRequestToCheckInIdMap.get(referralCommMessage.getRequestId());
				if (checkinData != null) {
					List<AssistedByDetails> assistedByDetails = reviewRequestToCheckInIdMap.get(referralCommMessage.getRequestId()).getAssistedBy();
					if (CollectionUtils.isNotEmpty(assistedByDetails)) {
						AssistedByDetails assistBy = assistedByDetails.get(0);
						referralUsageSentReportMessage.setSentByName(assistBy.getName());
						referralUsageSentReportMessage.setSentByEmail(assistBy.getEmail());
					}
					
				}
			}
			 
			result.add(referralUsageSentReportMessage);
		}
		return result;
	}
	Date convertFromStringToDate(String date) {
		LocalDateTime localDateTime = LocalDateTime.parse(date, DATE_TIME_FORMATTER);
		Date convertedDate = Date.from(localDateTime.atZone(ZoneId.of("UTC")).toInstant());
		return convertedDate;
	}
	private String prepareDateAsPerTimeZone(Date date, String dateFormat, Integer businessId){
		String timeZoneId = businessService.getBusinessTimezoneId(businessId);
		return DateTimeUtils.prepareFormattedDate(date ,dateFormat, timeZoneId);
	}
	private Map<String, Object> prepareTokenDataForReferralUsageSentReport(Integer enterpriseId, ReferralReportRequest referralReportRequest, Map<String, Object> requestParams) {
		Map<String, Object> tokenData = new HashMap<>();
		prepareTokenDataForSortingAndSearching(requestParams, tokenData);
		tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
		tokenData.put(ReportServiceUtils.BUSINESS_IDS, referralReportRequest.getBusinessIds().toString());
		if (StringUtils.isNotBlank(referralReportRequest.getCustomerNameSearchStr())) {
			tokenData.put(ReportServiceUtils.SEARCH_BY, referralReportRequest.getCustomerNameSearchStr());
			tokenData.put(ReportServiceUtils.SEARCH_BY_WILDCARD, WordUtils.capitalize(referralReportRequest.getCustomerNameSearchStr()));
		}
		tokenData.put(ReportServiceUtils.START_DATE, sdf.format(referralReportRequest.getStartDate()));
		tokenData.put(ReportServiceUtils.END_DATE, sdf.format(referralReportRequest.getEndDate()));
		// handling for sort by for sent report
		if (requestParams.get(ReportServiceUtils.SORT_BY) != null) {
			tokenData.put(ReportServiceUtils.SORT_BY, prepareSortByForSentReport((String) requestParams.get(ReportServiceUtils.SORT_BY)));
		}
		return tokenData;
	}
	
	private Object prepareSortByForSentReport(String sortBy) {
		if ("sentTo".equalsIgnoreCase(sortBy)) {
			return "customerName.keyword";
		} else if ("sentVia".equalsIgnoreCase(sortBy)) {
			return "source.keyword";
		} else if ("sentOn".equalsIgnoreCase(sortBy)) {
			return "requestDate";
		}
		return sortBy;
	}
	
	/**
	 * Listing API for SHARED tab - REFERRALS - with filters and pagination
	 * 
	 */
	@Override
	public ReferralUsageSharedReportResponse getReferralUsageSharedReport(Map<String, Object> requestParams, Integer enterpriseId, ReferralReportRequest referralReportRequest) {
		logger.info("getReferralUsageSharedReport : Received request for Referral Usage Shared Tab report for enterpriseId : {}", enterpriseId);
		Map<String, Object> tokenData = prepareTokenDataForReferralUsageSharedReport(enterpriseId, referralReportRequest, requestParams);
		ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData, ElasticQueryTemplateEnum.REFERRAL_USAGE_SHARED_TEMPLATE_V2.getQueryName(),
				Constants.REFERRAL_REPORT_INDEX, Constants.REFERRAL_REPORT_TYPE);
		SearchResponse response = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.REFERRAL_REPORT_INDEX).execute(esRequest);
		ReferralUsageSharedReportResponse result = null;
		
		if (response != null && response.getHits() != null && response.getHits().getTotalHits().value != 0l) {
			List<ReferralCommMessage> customerDocsForCampaign = new ArrayList<>();
			
			for (SearchHit hit : response.getHits().getHits()) {
				customerDocsForCampaign.add(OBJECT_MAPPER.convertValue(hit.getSourceAsMap(), ReferralCommMessage.class));
			}
			result = new ReferralUsageSharedReportResponse((int) response.getHits().getTotalHits().value, prepareReferralSharedUsageResult(customerDocsForCampaign, null));
		}
		logger.info("getReferralUsageSharedReport : Completed processing request for Referral Usage Shared Tab report for enterpriseId : {}", enterpriseId);
		return result;
	}
	
	@Override
	public ReferralSummaryReportResponse getReferralSummaryReport(Integer enterpriseId, ReferralSummaryReportRequest request) {
		if (CollectionUtils.isEmpty(request.getBusinessIds())) {
			logger.warn("No business ids found Referral Summary request : {} and enterpriseId {}", request, enterpriseId);
			return new ReferralSummaryReportResponse();
		}
		// getting token map for es query
		Map<String, Object> tokenData = getTokenData(request);
		ReferralSummaryReportResponse response = new ReferralSummaryReportResponse();
		try {
			
			ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData, ElasticQueryTemplateEnum.REFERRAL_CONVERSION_REPORT_V2.getQueryName(),
					Constants.REFERRAL_REPORT_INDEX, Constants.REFERRAL_REPORT_TYPE);
			SearchResponse searchResponse = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.REFERRAL_REPORT_INDEX).execute(esRequest);
			if (searchResponse == null) {
				logger.info("Recieved null response for referral summary request for enterprise {}", enterpriseId);
				return null;
			}
			logger.info("Referral summary report search response is {} ", searchResponse);
			if (searchResponse.getAggregations() != null) {
				addReferralReportChannelData(searchResponse.getAggregations().get("sourceAggregation"), response);
				addReferralReportClickData(searchResponse.getAggregations().get("sourceClicks"), response);
				addReferralReportReferralData(searchResponse.getAggregations().get("referralSourceCount"), response);
				addReferralLeadsCountData(request.getBusinessIds(), request.getStartDate(), request.getEndDate(), response);
				response.setReferralPercentage(CoreUtils.getPercentile(response.getReferralCount(), response.getTotalSent()));
				response.setSharedPercentage(CoreUtils.getPercentile(response.getTotalShared(), response.getTotalSent()));
			}
			return response;
		} catch (Exception exp) {
			logger.error("Exception occurred while fetching Referral summary for Business : {} and tokenData : {}", enterpriseId, tokenData, exp);
		}
		return null;
	}
	
	private void addReferralReportChannelData(Terms referralFunnelSourceAggregation, ReferralSummaryReportResponse response) {
		if (referralFunnelSourceAggregation != null && CollectionUtils.isNotEmpty(referralFunnelSourceAggregation.getBuckets())) {
			long totalSent = 0l;
			for (Terms.Bucket bucket : referralFunnelSourceAggregation.getBuckets()) {
				totalSent = totalSent + bucket.getDocCount();
			}
			response.setTotalSent(totalSent);
			for (Terms.Bucket bucket : referralFunnelSourceAggregation.getBuckets()) {
				if ((StringUtils.equalsIgnoreCase("email", String.valueOf(bucket.getKey())))) {
					response.setEmailSent(bucket.getDocCount());
					response.setEmailSentPercent(CoreUtils.getPercentile(bucket.getDocCount(), totalSent));
				}
				
				else if (StringUtils.equalsIgnoreCase("sms", String.valueOf(bucket.getKey()))) {
					response.setSmsSent(bucket.getDocCount());
					response.setSmsSentPercent(CoreUtils.getPercentile(bucket.getDocCount(), totalSent));
				}
			}
		}
	}
	
	private static void addReferralReportClickData(Nested nestedSourceClicks, ReferralSummaryReportResponse response) {
		long totalShared = 0;
		Terms sourceClicks = nestedSourceClicks.getAggregations().get("sourceClickAggregation");
		for (Terms.Bucket bucket : sourceClicks.getBuckets()) {
			totalShared = totalShared + bucket.getDocCount();
		}
		response.setTotalShared(totalShared);
		List<ReferralShareInfo> sourceClickInfoList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(sourceClicks.getBuckets())) {
			for (Terms.Bucket sourceClickBucket : sourceClicks.getBuckets()) {
				ReferralShareInfo sourceClickInfo = new ReferralShareInfo();
				sourceClickInfo.setSourceId(Integer.valueOf(String.valueOf(sourceClickBucket.getKey())));
				sourceClickInfo.setSharedCount(sourceClickBucket.getDocCount());
				sourceClickInfo.setSharedPercent(CoreUtils.getPercentile(sourceClickBucket.getDocCount(), totalShared));
				sourceClickInfoList.add(sourceClickInfo);
			}
		}
		response.setSharedSourceInfo(sourceClickInfoList);
	}
	
	private void addReferralReportReferralData(Nested nestedReferralSourceCount, ReferralSummaryReportResponse response) {
		Sum referralSourceCount = nestedReferralSourceCount.getAggregations().get("referralCountAggregation");
		response.setReferralCount(Math.round(referralSourceCount.getValue()));
	}
	
	private void addReferralLeadsCountData(List<Integer> businessIds, Date startDate, Date endDate, ReferralSummaryReportResponse response) {
		ReferralLeadsCountRequest request = ReportServiceUtils.getReferralLeadsCountRequest(businessIds, startDate, endDate);
		ReferralLeadsCountResponse leadCount = referralAppointmentService.getReferralLeadsCount(request);
		response.setLeadsCount(leadCount.getTotalCount());
	}
	
	private Map<String, Object> prepareTokenDataForReferralUsageSharedReport(Integer enterpriseId, ReferralReportRequest referralReportRequest, Map<String, Object> requestParams) {
		Map<String, Object> tokenData = new HashMap<>();
		tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
		tokenData.put(BUSINESS_IDS, ControllerUtil.toCommaSeparatedString(referralReportRequest.getBusinessIds()));
		if (StringUtils.isNotBlank(referralReportRequest.getCustomerNameSearchStr())) {
			tokenData.put(ReportServiceUtils.SEARCH_BY, referralReportRequest.getCustomerNameSearchStr());
			tokenData.put(ReportServiceUtils.SEARCH_BY_WILDCARD, WordUtils.capitalize(referralReportRequest.getCustomerNameSearchStr()));
		}
		tokenData.put(ReportServiceUtils.START_DATE, sdf.format(referralReportRequest.getStartDate()));
		tokenData.put(ReportServiceUtils.END_DATE, sdf.format(referralReportRequest.getEndDate()));
		if (CollectionUtils.isNotEmpty(referralReportRequest.getReferralSources())) {
			tokenData.put("sourceIds", ControllerUtil.toCommaSeparatedString(referralReportRequest.getReferralSources()));
		}
		prepareTokenDataForSortingAndSearchingSharedUsage(requestParams, tokenData);
		return tokenData;
	}
	
	private List<ReferralUsageSharedReportDTO> prepareReferralSharedUsageResult(List<ReferralCommMessage> customerDocsForCampaign, Map<Long, CheckinIdDetails> reviewRequestToCheckInIdMap) {
		List<ReferralUsageSharedReportDTO> referralUsageSharedReportDTOs = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(customerDocsForCampaign)) {
			for (ReferralCommMessage referralCommMessage : customerDocsForCampaign) {
				prepareReferralSharedDTO(referralUsageSharedReportDTOs, referralCommMessage, reviewRequestToCheckInIdMap);
			}
		}
		return referralUsageSharedReportDTOs;
	}
	
	private void prepareReferralSharedDTO(List<ReferralUsageSharedReportDTO> referralUsageSharedReportDTOs, ReferralCommMessage referralCommMessage, Map<Long, CheckinIdDetails> reviewRequestToCheckInIdMap) {
		Set<Integer> sharedVia = new HashSet<>();
		Set<String> sharedViaNameSet = new HashSet<>();
		if (null != referralCommMessage.getSourceClicks()) {
			for (ReferralClickSourceCount sourceClick : referralCommMessage.getSourceClicks()) {
				sharedVia.add(sourceClick.getSourceId());
				sharedViaNameSet.add(ReferralSourceTypeEnum.getReferralSourceType(sourceClick.getSourceId()));
			}
		}
		String sharedViaName = StringUtils.join(sharedViaNameSet, ",");
		
		ReferralUsageSharedReportDTO referralUsageSharedReportMessage = new ReferralUsageSharedReportDTO(referralCommMessage.getCustomerId(), referralCommMessage.getCustomerName(),
				sharedVia, referralCommMessage.getReferralCode(), sharedViaName);
		if (referralCommMessage.getRequestDate() != null) {
			String lastSharedOn = prepareDateAsPerTimeZone(new Date(referralCommMessage.getClickTime()), REFERRAL_USAGE_REPORT_DATE_FORMAT, referralCommMessage.getEnterpriseId());
			String lastSharedOnUTC = DateTimeUtils.prepareFormattedDate(new Date(referralCommMessage.getClickTime()) ,REFERRAL_USAGE_REPORT_DATE_FORMAT, "UTC");
			
			referralUsageSharedReportMessage.setLastSharedOn(lastSharedOn);
			referralUsageSharedReportMessage.setLastSharedOnUTC(lastSharedOnUTC);
		}
		referralUsageSharedReportMessage.setLeadGenerated(referralCommMessage.getAppointmentIds() != null && referralCommMessage.getAppointmentIds().length > 0 ? 1 : 0);
		referralUsageSharedReportMessage.setBusinessAlias(referralCommMessage.getBusinessAlias());
		if (MapUtils.isNotEmpty(reviewRequestToCheckInIdMap)) {
			CheckinIdDetails checkinData = reviewRequestToCheckInIdMap.get(referralCommMessage.getRequestId());
			if (checkinData != null) {
				List<AssistedByDetails> assistedByDetails = reviewRequestToCheckInIdMap.get(referralCommMessage.getRequestId()).getAssistedBy();
				if (CollectionUtils.isNotEmpty(assistedByDetails)) {
					AssistedByDetails assistBy = assistedByDetails.get(0);
					referralUsageSharedReportMessage.setSentByName(assistBy.getName());
					referralUsageSharedReportMessage.setSentByEmail(assistBy.getEmail());
				}
				
			}
		}
		
		referralUsageSharedReportDTOs.add(referralUsageSharedReportMessage);
	}
	
	private void prepareTokenDataForSortingAndSearchingSharedUsage(Map<String, Object> requestParams, Map<String, Object> tokenData) {
		Integer resultSize = 25;
		if (requestParams.containsKey(ReportServiceUtils.RESULT_SIZE)) {
			resultSize = (Integer) requestParams.get(ReportServiceUtils.RESULT_SIZE);
		}
		Integer resultPage = 0;
		if (requestParams.containsKey(ReportServiceUtils.RESULT_PAGE)) {
			resultPage = (Integer) requestParams.get(ReportServiceUtils.RESULT_PAGE);
		}
		String resultOrder = Constants.SORT_ORDER_DESC;
		if (requestParams.containsKey(ReportServiceUtils.RESULT_ORDER)) {
			resultOrder = (String) requestParams.get(ReportServiceUtils.RESULT_ORDER);
		}
		tokenData.put("size", String.valueOf(resultSize));
		tokenData.put("from", String.valueOf(resultPage * resultSize));
		if (requestParams.get(ReportServiceUtils.SORT_BY) != null) {
			tokenData.put(ReportServiceUtils.SORT_BY, prepareSortByForSharedReport((String) requestParams.get(ReportServiceUtils.SORT_BY)));
			tokenData.put(ReportServiceUtils.SORT_ORDER, resultOrder);
		}
	}
	
	private String prepareSortByForSharedReport(String sortBy) {
		if (Constants.REFERRAL_SORT_BY_SHARED_BY_PARAM.equalsIgnoreCase(sortBy)) {
			return "customerName.keyword";
		} else if (Constants.REFERRAL_SORT_BY_SHARED_ON_PARAM.equalsIgnoreCase(sortBy)) {
			return "clickTime";
		}
		return sortBy;
	}
	
	@Override
	public void getTemplateLastUsage(Integer enterpriseId, List<BusinessTemplateMessage> templates, String templateType) {
		logger.info("Getting template last usage stats for email templateIds and enterpriseId {}", enterpriseId);
		List<Integer> emailTemplates = new ArrayList<>();
		List<Integer> smsTemplates = new ArrayList<>();
		for (BusinessTemplateMessage template : templates) {
			if ("sms".equalsIgnoreCase(template.getType())) {
				smsTemplates.add(template.getId());
			} else {
				emailTemplates.add(template.getId());
			}
		}
		Map<Integer, Long> emailTemplatesLastUsage = new HashMap<>();
		Map<Integer, Long> smsTemplatesLastUsage = new HashMap<>();
		Map<String, String> esQueryData = populateESQueryDetails(templateType);
		
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
		// Submit CX task
		if (CollectionUtils.isNotEmpty(emailTemplates)) {
			executorService.submit(populateEmailTemplateLastUsage(enterpriseId, emailTemplates, emailTemplatesLastUsage, esQueryData));
		}
		if (CollectionUtils.isNotEmpty(smsTemplates)) {
			executorService.submit(populateSMSTemplateLastUsage(enterpriseId, smsTemplates, smsTemplatesLastUsage, esQueryData));
		}
		// Getting all Task from Executor
		try {
			logger.info("Executing template data fetch task for enterprise : {}", enterpriseId);
			executorCommonService.executeTasks(executorService);
			logger.info("Executed template data fetch task for enterprise : {}", enterpriseId);
		} catch (Exception exception) {
			logger.error("Exception occurred while executing template usage data fetch task for enterprise : {}", enterpriseId, exception);
		}
		// populate data
		for (BusinessTemplateMessage template : templates) {
			if ("sms".equalsIgnoreCase(template.getType())) {
				template.setLastUsedTime(smsTemplatesLastUsage.getOrDefault(template.getId(), 0L));
			} else {
				template.setLastUsedTime(emailTemplatesLastUsage.getOrDefault(template.getId(), 0L));
			}
			// setting usage data to NULL as it is not required
			template.setUsageStats(null);
		}
	}
	
	@Override
	public Map<String, Integer> getMostRecentlyUsedTemplate(Integer enterpriseId, List<BusinessTemplateEntity> templates, String templateType) {
		logger.info("Getting most recently used template for email and sms templateIds and enterpriseId {}", enterpriseId);
		List<Integer> emailTemplates = new ArrayList<>();
		List<Integer> smsTemplates = new ArrayList<>();
		for (BusinessTemplateEntity template : templates) {
			if (StringUtils.contains(template.getTemplateType(), Constants.SMS_TYPE)) {
				smsTemplates.add(template.getTemplateId());
			} else {
				emailTemplates.add(template.getTemplateId());
			}
		}
		Map<String, Integer> emailToLastTemplateIdUsage = new HashMap<>();
		Map<String, Integer> smsToLastTemplateIdUsage = new HashMap<>();
		Map<String, String> esQueryData = populateESQueryDetailsForMostRecentlyUsedTemplate(templateType);
		
		CampaignExecutorService<Boolean> executorService = new CampaignExecutorService<>(threadPoolTaskExecutor);
		if (CollectionUtils.isNotEmpty(emailTemplates)) {
			executorService.submit(populateEmailTemplateLastUsageForSingleTemplate(enterpriseId, emailTemplates, emailToLastTemplateIdUsage, esQueryData));
		}
		if (CollectionUtils.isNotEmpty(smsTemplates)) {
			executorService.submit(populateSMSTemplateLastUsageForSingleTemplate(enterpriseId, smsTemplates, smsToLastTemplateIdUsage, esQueryData));
		}
		// Getting all Task from Executor
		try {
			logger.info("Executing fetch most recently used template tasks for enterprise : {}", enterpriseId);
			executorCommonService.executeTasks(executorService);
			logger.info("Executed fetch most recently used template tasks for enterprise : {}", enterpriseId);
		} catch (Exception exception) {
			logger.error("Exception occurred while executing fetch most recently used template tasks for enterprise : {}", enterpriseId, ExceptionUtils.getStackTrace(exception));
		}
		
		Map<String, Integer> sourceToMostRecentlyUsedTemplateMap = new HashMap<>();
		if (emailToLastTemplateIdUsage.containsKey(Constants.EMAIL_TYPE)) {
			sourceToMostRecentlyUsedTemplateMap.put(Constants.EMAIL_TYPE, emailToLastTemplateIdUsage.get(Constants.EMAIL_TYPE));
		}
		if (smsToLastTemplateIdUsage.containsKey(Constants.SMS_TYPE)) {
			sourceToMostRecentlyUsedTemplateMap.put(Constants.SMS_TYPE, smsToLastTemplateIdUsage.get(Constants.SMS_TYPE));
		}
		return sourceToMostRecentlyUsedTemplateMap;
	}
	
	private Map<String, String> populateESQueryDetails(String templateType) {
		Map<String, String> esQuery = new HashMap<>();
		if (CampaignTypeEnum.CX_REQUEST.getType().equalsIgnoreCase(templateType) || Constants.CX_TEMPLATE_TYPE.equalsIgnoreCase(templateType)) {
			esQuery.put(QUERY, ElasticQueryTemplateEnum.CX_TEMPLATE_COMMUNICATION_LAST_USAGE.getQueryName());
			esQuery.put(INDEX, Constants.CX_REPORT_INDEX);
			esQuery.put(TYPE, Constants.CX_REPORT_TYPE);
		} else if ("review_request".equalsIgnoreCase(templateType)) {
			esQuery.put(QUERY, ElasticQueryTemplateEnum.RR_TEMPLATE_COMMUNICATION_LAST_USAGE.getQueryName());
			esQuery.put(INDEX, Constants.RR_REPORT_INDEX);
			esQuery.put(TYPE, Constants.RR_REPORT_TYPE);
		} else if (TemplateTypeEnum.SURVEY_REQUEST.getName().equalsIgnoreCase(templateType)) {
			esQuery.put(QUERY, ElasticQueryTemplateEnum.SURVEY_TEMPLATE_COMMUNICATION_LAST_USAGE.getQueryName());
			esQuery.put(INDEX, Constants.SURVEY_REPORT_INDEX);
			esQuery.put(TYPE, Constants.SURVEY_REPORT_TYPE);
		} else if (TemplateTypeEnum.REFERRAL.getName().equalsIgnoreCase(templateType)) {
			esQuery.put(QUERY, ElasticQueryTemplateEnum.REFERRAL_TEMPLATE_COMMUNICATION_LAST_USAGE.getQueryName());
			esQuery.put(INDEX, Constants.REFERRAL_REPORT_INDEX);
			esQuery.put(TYPE, Constants.REFERRAL_REPORT_TYPE);
		} else if (TemplateTypeEnum.APPOINTMENT_REMINDER.getName().equalsIgnoreCase(templateType)) {
			esQuery.put(QUERY, ElasticQueryTemplateEnum.APPOINTMENT_REMINDER_TEMPLATE_COMMUNICATION_LAST_USAGE.getQueryName());
			esQuery.put(INDEX, Constants.APPOINTMENT_REMINDER_REPORT_INDEX);
			esQuery.put(TYPE, Constants.APPOINTMENT_REMINDER_REPORT_TYPE);
		} else if (TemplateTypeEnum.APPOINTMENT_FORM.getName().equalsIgnoreCase(templateType)) {
			esQuery.put(QUERY, ElasticQueryTemplateEnum.APPOINTMENT_FORM_TEMPLATE_COMMUNICATION_LAST_USAGE.getQueryName());
			esQuery.put(INDEX, Constants.APPOINTMENT_FORM_REPORT_INDEX);
			esQuery.put(TYPE, Constants.APPOINTMENT_FORM_REPORT_TYPE);
		}
		return esQuery;
	}
	
	private Map<String, String> populateESQueryDetailsForMostRecentlyUsedTemplate(String templateType) {
		Map<String, String> esQuery = new HashMap<>();
		if (TemplateTypeEnum.REVIEW_REQUEST_SMS.getName().equalsIgnoreCase(templateType) || TemplateTypeEnum.REVIEW_REQUEST_NEW.getName().equalsIgnoreCase(templateType)) {
			esQuery.put(QUERY, ElasticQueryTemplateEnum.RR_SINGLE_TEMPLATE_LAST_USAGE.getQueryName());
			esQuery.put(INDEX, Constants.RR_REPORT_INDEX);
			esQuery.put(TYPE, Constants.RR_REPORT_TYPE);
		} else if (TemplateTypeEnum.SURVEY_REQUEST.getName().equalsIgnoreCase(templateType)) {
			esQuery.put(QUERY, ElasticQueryTemplateEnum.SURVEY_SINGLE_TEMPLATE_LAST_USAGE.getQueryName());
			esQuery.put(INDEX, Constants.SURVEY_REPORT_INDEX);
			esQuery.put(TYPE, Constants.SURVEY_REPORT_TYPE);
		} else if (TemplateTypeEnum.REFERRAL.getName().equalsIgnoreCase(templateType)) {
			esQuery.put(QUERY, ElasticQueryTemplateEnum.REFERRAL_SINGLE_TEMPLATE_LAST_USAGE.getQueryName());
			esQuery.put(INDEX, Constants.REFERRAL_REPORT_INDEX);
			esQuery.put(TYPE, Constants.REFERRAL_REPORT_TYPE);
		} else if (TemplateTypeEnum.APPOINTMENT_REMINDER.getName().equalsIgnoreCase(templateType)) {
			esQuery.put(QUERY, ElasticQueryTemplateEnum.APPOINTMENT_REMINDER_SINGLE_TEMPLATE_LAST_USAGE.getQueryName());
			esQuery.put(INDEX, Constants.APPOINTMENT_REMINDER_REPORT_INDEX);
			esQuery.put(TYPE, Constants.APPOINTMENT_REMINDER_REPORT_TYPE);
		} else if (TemplateTypeEnum.APPOINTMENT_FORM.getName().equalsIgnoreCase(templateType)) {
			esQuery.put(QUERY, ElasticQueryTemplateEnum.APPOINTMENT_FORM_SINGLE_TEMPLATE_LAST_USAGE.getQueryName());
			esQuery.put(INDEX, Constants.APPOINTMENT_FORM_REPORT_INDEX);
			esQuery.put(TYPE, Constants.APPOINTMENT_FORM_REPORT_TYPE);
		} else if (TemplateTypeEnum.CUSTOMER_EXPERIENCE.getName().equalsIgnoreCase(templateType)) {
			esQuery.put(QUERY, ElasticQueryTemplateEnum.CX_SINGLE_TEMPLATE_LAST_USAGE.getQueryName());
			esQuery.put(INDEX, Constants.CX_REPORT_INDEX);
			esQuery.put(TYPE, Constants.CX_REPORT_TYPE);
		}  else if (TemplateTypeEnum.APPOINTMENT_RECALL.getName().equalsIgnoreCase(templateType)) {
			esQuery.put(QUERY, ElasticQueryTemplateEnum.APPOINTMENT_RECALL_SINGLE_TEMPLATE_LAST_USAGE.getQueryName());
			esQuery.put(INDEX, Constants.APPOINTMENT_RECALL_REPORT_INDEX);
			esQuery.put(TYPE, Constants.APPOINTMENT_RECALL_REPORT_TYPE);
		}
		return esQuery;
	}
	
	private CampaignCallable<Boolean> populateEmailTemplateLastUsage(Integer enterpriseId, List<Integer> templates, Map<Integer, Long> templatesIdToLastUsage,
			Map<String, String> esQueryData) {
		logger.info("Getting Email Templates last usage enterpriseId {}", enterpriseId);
		return new CampaignCallable<Boolean>("EMAIL TEMPLATEs LAST USAGE") {
			@Override
			public Boolean doCall() throws IOException {
				populateTemplateLastUsage(enterpriseId, templates, templatesIdToLastUsage, Constants.RR_SOURCE_EMAIL, esQueryData);
				return true;
			}
		};
	}
	
	private CampaignCallable<Boolean> populateEmailTemplateLastUsageForSingleTemplate(Integer enterpriseId, List<Integer> templates, Map<String, Integer> emailToLastTemplateIdUsage,
			Map<String, String> esQueryData) {
		logger.info("Getting most recently used Email Template for enterpriseId {}", enterpriseId);
		return new CampaignCallable<Boolean>("MOST RECENTLY USED EMAIL TEMPLATE") {
			@Override
			public Boolean doCall() throws IOException {
				populateMostRecentlyUsedTemplate(enterpriseId, templates, emailToLastTemplateIdUsage, Constants.EMAIL_TYPE, esQueryData);
				return true;
			}
		};
	}
	
	private CampaignCallable<Boolean> populateSMSTemplateLastUsage(Integer enterpriseId, List<Integer> templates, Map<Integer, Long> templatesIdToLastUsage,
			Map<String, String> esQueryData) {
		logger.info("Getting SMS Templates last usage enterpriseId {}", enterpriseId);
		return new CampaignCallable<Boolean>("SMS TEMPLATEs LAST USAGE") {
			@Override
			public Boolean doCall() throws IOException {
				populateTemplateLastUsage(enterpriseId, templates, templatesIdToLastUsage, "sms", esQueryData);
				return true;
			}
		};
	}
	
	private CampaignCallable<Boolean> populateSMSTemplateLastUsageForSingleTemplate(Integer enterpriseId, List<Integer> templates, Map<String, Integer> smsToLastTemplateIdUsage,
			Map<String, String> esQueryData) {
		logger.info("Getting most recently used Sms Template for enterpriseId {}", enterpriseId);
		return new CampaignCallable<Boolean>("MOST RECENTLY USED SMS TEMPLATE") {
			@Override
			public Boolean doCall() throws IOException {
				populateMostRecentlyUsedTemplate(enterpriseId, templates, smsToLastTemplateIdUsage, Constants.SMS_TYPE, esQueryData);
				return true;
			}
		};
	}
	
	private void populateTemplateLastUsage(Integer enterpriseId, List<Integer> templates, Map<Integer, Long> templatesIdToLastUsage, String srcType, Map<String, String> esQueryData) {
		logger.info("Getting template last usage stats for email templateIds and enterpriseId {}", enterpriseId);
		Map<String, Object> tokenData = new HashMap<>();
		tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
		if (CollectionUtils.isNotEmpty(templates)) {
			tokenData.put(TEMPLATE_IDS, templates.toString());
			tokenData.put(AGGREGATION_SIZE, String.valueOf(templates.size()));
			tokenData.put("srcType", srcType);
			try {
				ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData, esQueryData.get(QUERY), esQueryData.get(INDEX),
						esQueryData.get(TYPE));
				//TODO : remove Temporary IF condition to handle ES upgrade activity. Once all indices move to newer version, remove IF.
				if (StringUtils.equalsAnyIgnoreCase(esQueryData.get(INDEX), Constants.REFERRAL_REPORT_INDEX, Constants.RR_REPORT_INDEX, Constants.CX_REPORT_INDEX, Constants.PROMOTION_REPORT_INDEX,
						Constants.SURVEY_REPORT_INDEX,Constants.APPOINTMENT_RECALL_REPORT_INDEX,Constants.APPOINTMENT_REMINDER_REPORT_INDEX, Constants.APPOINTMENT_FORM_REPORT_INDEX)) {
					Terms aggregation = elasticSearchClientFactory.getElasticSearchHighClientService(esQueryData.get(INDEX)).executeAndFetchAggregations(esRequest, GRP_BY_TEMPLATE);
					if (aggregation != null) {
						ReportServiceUtils.processTemplateLastUsageDataV1(aggregation, templatesIdToLastUsage);
					}
				} else {
					TermsAggregation aggregation = elasticSearchService.executeAndFetchAggregations(esRequest, GRP_BY_TEMPLATE, TermsAggregation.class);
					if (aggregation != null) {
						List<TermsAggregation.Entry> buckets = aggregation.getBuckets();
						if (CollectionUtils.isNotEmpty(buckets)) {
							ReportServiceUtils.processTemplateLastUsageData(aggregation, templatesIdToLastUsage);
						}
					}
				}
			} catch (Exception exp) {
				logger.error("Exception occurred while getting template last usage data for enterpriseId : {} and tokenData : {} {}", enterpriseId, tokenData, exp);
			}
		}
	}
	
	private void mapESResponseWithIndexAndFetchResult(String index, SearchResponse result, Map<String, Integer> sourceToLastTemplateIdUsage, String srcType) {
		if (StringUtils.equalsIgnoreCase(Constants.RR_REPORT_INDEX, index)) {
			List<CommMessage> messages = ESUtils.mapESSearchResponse(result, CommMessage.class);
			if (CollectionUtils.isNotEmpty(messages)) {
				sourceToLastTemplateIdUsage.put(srcType, messages.get(0).getTemplateId());
			}
		} else if (StringUtils.equalsIgnoreCase(Constants.REFERRAL_REPORT_INDEX, index)) {
			List<ReferralCommMessage> messages = ESUtils.mapESSearchResponse(result, ReferralCommMessage.class);
			if (CollectionUtils.isNotEmpty(messages)) {
				sourceToLastTemplateIdUsage.put(srcType, messages.get(0).getTemplateId());
			}
		} else if (StringUtils.equalsIgnoreCase(Constants.CX_REPORT_INDEX, index)) {
			List<CXCommMessage> messages = ESUtils.mapESSearchResponse(result, CXCommMessage.class);
			if (CollectionUtils.isNotEmpty(messages)) {
				sourceToLastTemplateIdUsage.put(srcType, messages.get(0).getTemplateId());
			}
		} else if (StringUtils.equalsIgnoreCase(Constants.SURVEY_REPORT_INDEX, index)) {
			List<SurveyCommMessage> messages = ESUtils.mapESSearchResponse(result, SurveyCommMessage.class);
			if (CollectionUtils.isNotEmpty(messages)) {
				sourceToLastTemplateIdUsage.put(srcType, messages.get(0).getTemplateId());
			}
		} else if (StringUtils.equalsIgnoreCase(Constants.APPOINTMENT_REMINDER_REPORT_INDEX, index)) {
			List<AppointmentReminderCommMessage> messages = ESUtils.mapESSearchResponse(result, AppointmentReminderCommMessage.class);
			if (CollectionUtils.isNotEmpty(messages)) {
				sourceToLastTemplateIdUsage.put(srcType, messages.get(0).getTemplateId());
			}
		} else if (StringUtils.equalsIgnoreCase(Constants.APPOINTMENT_RECALL_REPORT_INDEX, index)) {
			List<AppointmentRecallCommMessage> messages = ESUtils.mapESSearchResponse(result, AppointmentRecallCommMessage.class);
			if (CollectionUtils.isNotEmpty(messages)) {
				sourceToLastTemplateIdUsage.put(srcType, messages.get(0).getTemplateId());
			}
		} else if (StringUtils.equalsIgnoreCase(Constants.APPOINTMENT_FORM_REPORT_INDEX, index)) {
			List<AppointmentFormCommMessage> messages = ESUtils.mapESSearchResponse(result, AppointmentFormCommMessage.class);
			if (CollectionUtils.isNotEmpty(messages)) {
				sourceToLastTemplateIdUsage.put(srcType, messages.get(0).getTemplateId());
			}
		}
	}
	
	private void populateMostRecentlyUsedTemplate(Integer enterpriseId, List<Integer> templates, Map<String, Integer> sourceToLastTemplateIdUsage, String srcType, Map<String, String> esQueryData) {
		logger.info("Getting most recently used template for email and sms templateIds and enterpriseId {}", enterpriseId);
		Map<String, Object> tokenData = new HashMap<>();
		tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
		if (CollectionUtils.isNotEmpty(templates)) {
			tokenData.put(TEMPLATE_IDS, templates.toString());
			tokenData.put("srcType", srcType);
			try {
				ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData, esQueryData.get(QUERY), esQueryData.get(INDEX),
						esQueryData.get(TYPE));
				if (StringUtils.equalsAnyIgnoreCase(esQueryData.get(INDEX), Constants.REFERRAL_REPORT_INDEX, Constants.RR_REPORT_INDEX, Constants.CX_REPORT_INDEX, Constants.PROMOTION_REPORT_INDEX,
						Constants.SURVEY_REPORT_INDEX, Constants.APPOINTMENT_RECALL_REPORT_INDEX, Constants.APPOINTMENT_REMINDER_REPORT_INDEX, Constants.APPOINTMENT_FORM_REPORT_INDEX)) {
					SearchResponse result = elasticSearchClientFactory.getElasticSearchHighClientService(esQueryData.get(INDEX)).execute(esRequest);
					if (result == null || result.getHits() == null || result.getHits().getHits().length == 0l) {
						return;
					}
					mapESResponseWithIndexAndFetchResult(esQueryData.get(INDEX), result, sourceToLastTemplateIdUsage, srcType);
				}
			} catch (Exception exp) {
				logger.error("Exception occurred while getting most recently used template data for enterpriseId : {} and tokenData : {} {}", enterpriseId, tokenData, exp);
			}
		}
	}
	
	@Override
	public void getSurveyCampaignUsage(int enterpriseId, List<Integer> campaignIds, Map<Integer, CommunicationUsageStatsMessage> campaignIdToUsageMap) {
		Map<String, Object> tokenData = new HashMap<>();
		tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
		if (CollectionUtils.isNotEmpty(campaignIds)) {
			tokenData.put(CAMPAIGN_IDS, campaignIds.toString());
			tokenData.put(AGGREGATION_SIZE, String.valueOf(campaignIds.size()));
		}
		try {
			ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData,
					ElasticQueryTemplateEnum.SURVEY_CAMPAIGN_COMMUNICATION_TEMPLATE_V1.getQueryName(), Constants.SURVEY_REPORT_INDEX, Constants.SURVEY_REPORT_TYPE);
			Terms aggregation = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.SURVEY_REPORT_INDEX).executeAndFetchAggregations(esRequest, GRP_BY_CAMPAIGN);
			if (aggregation != null) {
				 
				if (CollectionUtils.isNotEmpty(aggregation.getBuckets())) {
					ReportServiceUtils.processSurveyCampaignUsageData(aggregation, campaignIdToUsageMap);
				}
			}
		} catch (Exception exp) {
			logger.error("Exception occurred while fetching survey campaign data for enterpriseId : {} and tokenData : {}", enterpriseId, tokenData, exp);
		}
	}
	
	@Override
	@Profiled
	public void getSurveyRecipientUsageAndDeliveryStats(Integer enterpriseId, List<Integer> campaignIds, CampaignUsageInfo usageInfo) {
		if (CollectionUtils.isEmpty(campaignIds) || enterpriseId==null) {
			logger.warn("No campaign ids or enterprise id found in Survey Campaign unique recipients count request : {} & {}", campaignIds, enterpriseId);
			return;
		}
		
		// Preparing token map for ES query
		Map<String, Object> tokenData = new HashMap<>();
		tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
		if (CollectionUtils.isNotEmpty(campaignIds)) {
			tokenData.put(CAMPAIGN_IDS, campaignIds.toString());
		}
		
		try {
			ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData,
					ElasticQueryTemplateEnum.SURVEY_USAGE_CONTACT_SENT_COUNT.getQueryName(), Constants.SURVEY_REPORT_INDEX, Constants.SURVEY_REPORT_TYPE);
			SearchResponse searchResult = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.SURVEY_REPORT_INDEX).executeWithoutHitsTracking(esRequest);
			
			if (searchResult != null) {
				logger.info("Generating Survey Campaign unique recipients count & email deliverability stats for enterprise : {}", enterpriseId);
				UsageReportsUtil.processCampaignReportContactSentCountAndDeliveryStats(searchResult.getAggregations(), usageInfo, null);
			}
		} catch (Exception exp) {
			logger.error("Exception occurred while fetching survey usage unique recipients count for Business : {} and tokenData : {}", enterpriseId, tokenData,
					exp);
		}
		return;
	}
	
	
	private Map<String, Object> getTokenData(ReferralSummaryReportRequest request) {
		Map<String, Object> tokenData = new HashMap<>();
		tokenData.put(ReportServiceUtils.BUSINESS_IDS, request.getBusinessIds().toString());
		tokenData.put(ReportServiceUtils.START_DATE, sdf.format(request.getStartDate()));
		tokenData.put(ReportServiceUtils.END_DATE, sdf.format(request.getEndDate()));
		return tokenData;
	}
	
	private CampaignCallable<Boolean> getAppointmentFormTemplateCommunicationUsage(Integer enterpriseId, List<Integer> appointmentFormTemplateIds,
			Map<Integer, CommunicationUsageStatsMessage> templateIdToUsageMap) {
		logger.info("Getting communication usage stats for appointment form templateIds and enterpriseId {}", enterpriseId);
		return new CampaignCallable<Boolean>("APPOINTMENT_FORM_TEMPLATE_USAGE_TASK") {
			@Override
			public Boolean doCall() throws IOException {
				Map<String, Object> tokenData = new HashMap<>();
				tokenData.put(ENTERPRISE_ID, String.valueOf(enterpriseId));
				if (CollectionUtils.isNotEmpty(appointmentFormTemplateIds)) {
					tokenData.put(TEMPLATE_IDS, appointmentFormTemplateIds.toString());
					tokenData.put(AGGREGATION_SIZE, String.valueOf(appointmentFormTemplateIds.size()));
				}
				try {
					
					ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData,
							ElasticQueryTemplateEnum.APPOINTMENT_FORM_TEMPLATE_COMMUNICATION_USAGE.getQueryName(), Constants.APPOINTMENT_FORM_REPORT_INDEX,
							Constants.APPOINTMENT_FORM_REPORT_TYPE);
					Terms aggregation = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.APPOINTMENT_FORM_REPORT_INDEX).executeAndFetchAggregations(esRequest, GRP_BY_TEMPLATE);
					if (aggregation != null) {
						ReportServiceUtils.processAppointmentFormAggregationData(aggregation, templateIdToUsageMap);
					}
				} catch (Exception exp) {
					logger.error("Exception occurred while fetching appointment form template usage data for enterpriseId : {} and tokenData : {} {}", enterpriseId, tokenData, exp);
				}
				return true;
			}
		};
	}
	@Override
	public DoupDownloadResponse<ReferralUsageSentReportMessage> getReferralDownloadSentReport(Map<String, Object> requestParams, Integer enterpriseId, ReferralReportRequest referralReportRequest) {
		logger.info("getReferralDownloadSentReport : Received request for Download Referral Usage Sent Tab report for enterpriseId : {} and request {}", enterpriseId, referralReportRequest);
		Map<String, Object> tokenData = prepareTokenDataForReferralDownloadReport(enterpriseId, referralReportRequest);
		ReferralUsageSentReportResponse usageSentResponse = new ReferralUsageSentReportResponse();
		ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData, ElasticQueryTemplateEnum.REFERRAL_SENT_DOWNLOAD_TEMPLATE.getQueryName(),
				Constants.REFERRAL_REPORT_INDEX, Constants.REFERRAL_REPORT_TYPE);
		SearchResponse response = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.REFERRAL_REPORT_INDEX).execute(esRequest);
		if (response != null && response.getHits() != null && response.getHits().getTotalHits().value != 0l) {
			usageSentResponse.setTotalCount((int) response.getHits().getTotalHits().value);
			List<ReferralCommMessage> referralCommMessages = new ArrayList<>();
			List<Long> requestIds = new ArrayList<Long>();
			for (SearchHit hit : response.getHits().getHits()) {
				
				ReferralCommMessage message = OBJECT_MAPPER.convertValue(hit.getSourceAsMap(), ReferralCommMessage.class);
				referralCommMessages.add(message);
				if (message != null && message.getRequestId() != null) {
					requestIds.add(message.getRequestId());
				}
			}
			ReviewRequstToCheckInDetailsMapResponse reviewRequestToCheckInIdMap = reviewRequestHelperService.getCheckinDetailsForReviewRequestIds(requestIds, referralReportRequest.getUniqueKey());
			usageSentResponse.setData(prepareReferralUsageResult(referralCommMessages, reviewRequestToCheckInIdMap == null ? null : reviewRequestToCheckInIdMap.getReviewRequestToCheckInDetailsMap()));
		}
		
		DoupDownloadResponse<ReferralUsageSentReportMessage> downloadResult = new DoupDownloadResponse<>();
		// this is added because in referral tab we send combined report for sent shared and leads
		// DOUP fails to send email if any of the report return empty data
		// added empty response, so it doen't get break
		if (CollectionUtils.isEmpty(usageSentResponse.getData())) {
			List<ReferralUsageSentReportMessage> emptyData = new ArrayList<ReferralUsageSentReportMessage>();
			emptyData.add(new ReferralUsageSentReportMessage(null, " ", " ", " ", " ", " "));
			downloadResult.setData(emptyData);
		} else {
			downloadResult.setData(usageSentResponse.getData());
		}
		logger.info("getReferralDownloadSentReport : Completed processing Download request for Referral Usage Sent Tab report for enterpriseId : {}", enterpriseId);
		return downloadResult;
	}
	
	
	private Map<String, Object> prepareTokenDataForReferralDownloadReport(Integer enterpriseId, ReferralReportRequest referralReportRequest) {
		Map<String, Object> tokenData = new HashMap<String, Object>();
		tokenData.put(ReportServiceUtils.BUSINESS_IDS, referralReportRequest.getBusinessIds().toString());
		tokenData.put("enterpriseId", enterpriseId.toString());
		tokenData.put(ReportServiceUtils.START_DATE, sdf.format(referralReportRequest.getStartDate()));
		tokenData.put(ReportServiceUtils.END_DATE, sdf.format(referralReportRequest.getEndDate()));		
		return tokenData;
	}
	
	@Override
	public DoupDownloadResponse<ReferralUsageSharedReportDTO> getReferralDownloadSharedReport(Map<String, Object> requestParams, Integer enterpriseId, ReferralReportRequest referralReportRequest) {
		logger.info("getReferralDownloadSharedReport : Received request for Download Referral Usage Shared Tab report for enterpriseId : {}", enterpriseId);
		Map<String, Object> tokenData = prepareTokenDataForReferralDownloadReport(enterpriseId, referralReportRequest);
		ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(enterpriseId, tokenData, ElasticQueryTemplateEnum.REFERRAL_USAGE_SHARED_DOWNLOAD_TEMPLATE.getQueryName(),
				Constants.REFERRAL_REPORT_INDEX, Constants.REFERRAL_REPORT_TYPE);
		SearchResponse response = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.REFERRAL_REPORT_INDEX).execute(esRequest);
		ReferralUsageSharedReportResponse result = null;
		
		if (response != null && response.getHits() != null && response.getHits().getTotalHits().value != 0l) {
			List<Long> requestIds = new ArrayList<Long>();
			List<ReferralCommMessage> customerDocsForCampaign = new ArrayList<>();
			for (SearchHit hit : response.getHits().getHits()) {
				
				ReferralCommMessage message = OBJECT_MAPPER.convertValue(hit.getSourceAsMap(), ReferralCommMessage.class);
				customerDocsForCampaign.add(message);
				if(message!=null && message.getRequestId() != null) {
					requestIds.add(message.getRequestId());	
				}
			}
			
			ReviewRequstToCheckInDetailsMapResponse reviewRequestToCheckInIdMap = reviewRequestHelperService.getCheckinDetailsForReviewRequestIds(requestIds, referralReportRequest.getUniqueKey());
			
			List<ReferralUsageSharedReportDTO> referralSharedDataDetails = prepareReferralSharedUsageResult(customerDocsForCampaign, reviewRequestToCheckInIdMap == null ? null : reviewRequestToCheckInIdMap.getReviewRequestToCheckInDetailsMap());
			
			result = new ReferralUsageSharedReportResponse((int) response.getHits().getTotalHits().value, referralSharedDataDetails);
		}
		DoupDownloadResponse<ReferralUsageSharedReportDTO> downloadResponse =  new DoupDownloadResponse<>();
		
		if (result != null && CollectionUtils.isNotEmpty(result.getData())) {
			for (ReferralUsageSharedReportDTO data : result.getData()) {
				// DoUp don't support collections
				data.setSharedVia(null);
			}
			downloadResponse.setData(result.getData());
		} else {
			// this is added because in referral tab we send combined report for sent shared and leads
			// DOUP fails to send email if any of the report return empty data
			// added empty response, so it doen't get break
			ReferralUsageSharedReportDTO emptyDataRespone = new ReferralUsageSharedReportDTO(null, " ", " ", " ", " ", " ");
			List<ReferralUsageSharedReportDTO> listWithEmptyResponse = new ArrayList<>();
			listWithEmptyResponse.add(emptyDataRespone);
			downloadResponse.setData(listWithEmptyResponse);
			
		}
		
		logger.info("getReferralUsageSharedReport : Completed processing Download request for Referral Usage Shared Tab report for enterpriseId : {}", enterpriseId);
		return downloadResponse;
	}
	
	/**
	 * This method fetches top campaigns based on sortBy from ES index reseller_campaign_widget 
	 */
	@Profiled
	@Override
	public ResellerWidgetGalleryCampaignResponse getTopRRCampaignListForResellerCampaignWidget(List<Integer> campaignIds, String sortBy) {
		Map<String, Object> tokenData = getTokenDataForResellerCampaignWidget(campaignIds, sortBy);
		
		ResellerWidgetGalleryCampaignResponse campaignWidgetResponse = new ResellerWidgetGalleryCampaignResponse();
		try {
			
			ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(null, tokenData, ElasticQueryTemplateEnum.REVIEWS_CAMPAIGN_LIST_WIDGET_GALLERY.getQueryName(),
					Constants.RESELLER_CAMPAIGN_WIDGET_INDEX, Constants.RESELLER_CAMPAIGN_WIDGET_REPORT_TYPE);

			SearchResponse searchResult = elasticsearchHighLevelC3Client.execute(esRequest);
			
			if (searchResult != null) {
				logger.info("Processing top campaign list for widget gallery from campaignIds: {}", campaignIds);
				ReportServiceUtils.processTopRRCampaignForResellerWidgetGallery(searchResult, campaignWidgetResponse);
			}
			
		} catch (Exception exp) {
			logger.error("Exception occurred while getting top RR campaign for reseller widget for tokenData : {} and exception: {}", tokenData, ExceptionUtils.getStackTrace(exp));
		}
		return campaignWidgetResponse;
	}

	
	private Map<String, Object> getTokenDataForResellerCampaignWidget(List<Integer> campaignIds, String sortBy) {
		Map<String, Object> tokenData = new HashMap<>();
		
		// campaignIds.add(18198);
		// campaignIds.add(17996);
		// campaignIds.add(18002);
		
		tokenData.put(CAMPAIGN_IDS, campaignIds.toString());
		Integer campaignCount = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty(Constants.RESELLER_CAMPAIGN_WIDGET_CAMPAIGN_COUNT,
				Constants.RESELLER_CAMPAIGN_WIDGET_DEFAULT_CAMPAIGN_COUNT);
		tokenData.put(CAMPAIGN_COUNT, campaignCount);
		tokenData.put("sortBy", sortBy);
		tokenData.put("sortOrder", "desc");
		
		return tokenData;
	}

	@Override
	public ResellerWidgetCampaignsUsageSummary getResellerReviewsCampaignsUsageSummary(List<Integer> campaignIds) {
		Map<String, Object> tokenData = new HashMap<>();
		
		tokenData.put(CAMPAIGN_IDS, campaignIds.toString());
		tokenData.put(TOTAL_CAMPAIGN_COUNT, campaignIds.size());
		
		ResellerWidgetCampaignsUsageSummary campaignsUsageSummary = new ResellerWidgetCampaignsUsageSummary();
		try {
			
			ElasticSearchBaseRequest esRequest = elasticSearchHelperService.prepareElasticSearchRequest(null, tokenData, ElasticQueryTemplateEnum.REVIEWS_WIDGET_CAMPAIGNS_USAGE_SUMMARY.getQueryName(),
					Constants.RR_REPORT_INDEX, Constants.RR_REPORT_TYPE);
			
			SearchResponse searchResult = elasticSearchClientFactory.getElasticSearchHighClientService(Constants.RR_REPORT_INDEX).execute(esRequest);
			
			if (searchResult != null) {
				logger.info("Processing reseller reviews campaign usage summary for widget gallery from campaignIds: {}", campaignIds);
				ReportServiceUtils.processRRCampaignsUsageSummaryForResellerWidgetGallery(searchResult.getAggregations(), campaignsUsageSummary);
			}
			
		} catch (Exception exp) {
			logger.error("Exception occurred while processing reseller reviews campaign usage summary for widget gallery for tokenData : {} and exception: {}", tokenData,
					ExceptionUtils.getStackTrace(exp));
		}
		return campaignsUsageSummary;
	}
	
	/**
	 * Generates the external referral usage sent report for a given enterprise.
	 * It first converts the epoch time in the request to Date objects based on the enterprise's timezone,
	 * then logs the request and delegates the actual report generation.
	 *
	 * @param requestParams A map of additional request parameters used to generate the report.
	 * @param enterpriseId The ID of the enterprise for which the report is generated.
	 * @param referralReportRequest The report request object containing time range and other filters.
	 * @return ReferralUsageSentReportResponse containing the generated report data.
	 */
	@Override
	public ExternalReferralSentReportResponse generateExternalReferralSentReport(Map<String, Object> requestParams, Integer enterpriseId, ReferralReportRequest referralReportRequest) {
		convertEpochToDateFormat(referralReportRequest);
		logger.info("Fetching external referral usage sent report for Request: {}, Params: {}", referralReportRequest, requestParams);
		
		ReferralUsageSentReportResponse response = getReferralUsageSentReport(requestParams, enterpriseId, referralReportRequest);
		return transformToExternalReferralSentReport(response);
	}
	
	/**
	 * Transforms a ReferralUsageSentReportResponse into an ExternalReferralSentReportResponse.
	 *
	 * This method converts internal referral usage report data into an external report format
	 * by mapping each message from the internal structure to the external structure and determining
	 * whether a lead was generated.
	 *
	 * @param response the internal referral usage sent report response to be transformed
	 * @return an external referral sent report response with transformed data
	 */
	public ExternalReferralSentReportResponse transformToExternalReferralSentReport(ReferralUsageSentReportResponse response) {
		if(response == null || CollectionUtils.isEmpty(response.getData())) {
			return new ExternalReferralSentReportResponse();
		}
		ExternalReferralSentReportResponse sentReport = new ExternalReferralSentReportResponse();
		List<ExternalReferralUsageSentReportResponse> sentReportData = sentReport.getData();
		
		for (ReferralUsageSentReportMessage message : response.getData()) {
			boolean isLeadGenerated = message.getLeadGenerated() != null && message.getLeadGenerated() == 1;
			sentReportData.add(new ExternalReferralUsageSentReportResponse(message.getSentTo(), message.getSentVia(), message.getSentOnUTC(), isLeadGenerated,
					message.getReferralCode(), message.getBusinessAlias()));
		}
		sentReport.setData(sentReportData);
		return sentReport;
	}
	
	/**
	 * Converts epoch timestamps in the ReferralReportRequest object to Date objects 
	 * using the timezone of the specified enterprise. The converted dates are then 
	 * set in the request object.
	 *
	 * @param referralReportRequest The request object containing epoch timestamps.
	 * @param enterpriseId The ID of the enterprise whose timezone should be used for conversion.
	 */
	private void convertEpochToDateFormat(ReferralReportRequest referralReportRequest) {
		Date startDate = Date.from(Instant.ofEpochMilli(referralReportRequest.getStartTime()).atZone(ZoneId.of("UTC")).toInstant());
		Date endDate = Date.from(Instant.ofEpochMilli(referralReportRequest.getEndTime()).atZone(ZoneId.of("UTC")).toInstant());
		
		referralReportRequest.setStartDate(startDate);
		referralReportRequest.setEndDate(endDate);
	}
	
	 /* Generates the external referral usage shared report for a given enterprise.
	 * It first converts the epoch time in the request to Date objects based on the enterprise's timezone,
	 * then logs the request and delegates the actual report generation.
	 *
	 * @param requestParams A map of additional request parameters used to generate the report.
	 * @param enterpriseId The ID of the enterprise for which the report is generated.
	 * @param referralReportRequest The report request object containing time range and other filters.
	 * @return ReferralUsageSentReportResponse containing the generated report data.
	 */
	@Override
	public ExternalReferralSharedReportResponse generateExternalReferralSharedReport(Map<String, Object> requestParams, Integer enterpriseId, ReferralReportRequest referralReportRequest) {
		convertEpochToDateFormat(referralReportRequest);
		logger.info("Fetching external referral usage shared report for Request: {}, Params: {}", referralReportRequest, requestParams);
		ReferralUsageSharedReportResponse response = getReferralUsageSharedReport(requestParams, enterpriseId, referralReportRequest);
		return transformToExternalReferralSharedReport(response);
	}
	
	/**
	 * Transforms a {@link ReferralUsageSharedReportResponse} object into an 
	 * {@link ExternalReferralSharedReportResponse} object by mapping its internal data
	 * to a new structure suitable for external usage.
	 *
	 * @param response the input referral usage shared report response to be transformed
	 * @return a new {@link ExternalReferralSharedReportResponse} with the mapped data,
	 *         or an empty object if the input is null or contains no data
	 */
	private ExternalReferralSharedReportResponse transformToExternalReferralSharedReport(ReferralUsageSharedReportResponse response) {
		if(response == null || CollectionUtils.isEmpty(response.getData())) {
			return new ExternalReferralSharedReportResponse();
		}
		ExternalReferralSharedReportResponse sharedReport = new ExternalReferralSharedReportResponse();
		List<ExternalReferralUsageSharedReportResponse> sharedReportData = sharedReport.getData();
		
		for(ReferralUsageSharedReportDTO data : response.getData()) {
			boolean isLeadGenerated = data.getLeadGenerated() != null && data.getLeadGenerated() == 1;
			sharedReportData.add(new ExternalReferralUsageSharedReportResponse(data.getSharedBy(), data.getSharedViaName(), data.getLastSharedOnUTC(), isLeadGenerated, data.getReferralCode(), data.getBusinessAlias()));
			
		}
		sharedReport.setData(sharedReportData);
		return sharedReport;
	}
}