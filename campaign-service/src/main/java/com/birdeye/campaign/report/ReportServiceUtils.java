package com.birdeye.campaign.report;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.nested.ParsedNested;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms.Bucket;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.elasticsearch.search.aggregations.metrics.ParsedValueCount;
import org.elasticsearch.search.aggregations.metrics.TopHits;
import org.elasticsearch.search.aggregations.metrics.ValueCount;

import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.CampaignFailureReasonCache;
import com.birdeye.campaign.communication.CampaignCustomerActivityEvent;
import com.birdeye.campaign.communication.SendGridEventToMessageEnum;
import com.birdeye.campaign.communication.message.AppointmentFormCommMessage;
import com.birdeye.campaign.communication.message.AppointmentRecallCommMessage;
import com.birdeye.campaign.communication.message.AppointmentReminderCommMessage;
import com.birdeye.campaign.communication.message.CXCommMessage;
import com.birdeye.campaign.communication.message.CommMessage;
import com.birdeye.campaign.communication.message.PromotionCommMessage;
import com.birdeye.campaign.communication.message.SurveyCommMessage;
import com.birdeye.campaign.communication.message.referral.ReferralCommMessage;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.dto.CampaignSentCountMessage;
import com.birdeye.campaign.dto.CommunicationUsageStatsMessage;
import com.birdeye.campaign.dto.CustomTemplateSentCountMessage;
import com.birdeye.campaign.dto.PromotionTemplateSentCountMessage;
import com.birdeye.campaign.dto.ResellerWidgetCampaignsUsageSummary;
import com.birdeye.campaign.dto.ResellerWidgetCampaignsUsageSummary.CampaignUsageSummary;
import com.birdeye.campaign.dto.ResellerWidgetGalleryCampaignResponse;
import com.birdeye.campaign.dto.ResellerWidgetGalleryCampaignResponse.CampaignWidgetResponse;
import com.birdeye.campaign.entity.CampaignFailureReason;
import com.birdeye.campaign.enums.AppointmentClickTypeEnum;
import com.birdeye.campaign.external.utils.ESUtils;
import com.birdeye.campaign.reseller.widget.message.ResellerCampaignWidgetMessage;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.referral.request.ReferralLeadsCountRequest;

import io.searchbox.core.search.aggregation.TermsAggregation;
import io.searchbox.core.search.aggregation.TopHitsAggregation;
import io.searchbox.core.search.aggregation.ValueCountAggregation;

/**
 * Utility for processing report data
 * <AUTHOR>
 *
 */
public class ReportServiceUtils {
	
	private static final String REQUEST_COUNT = "request_count";
	public static final String RESULT_ORDER = "resultOrder";
	public static final String RESULT_SIZE = "resultSize";
	public static final String RESULT_PAGE = "resultPage";
	public static final String SORT_BY = "sortBy";
	public static final String SORT_ORDER = "sortOrder";
	public static final String SEARCH_BY = "searchBy";
	public static final String START_DATE = "startDate";
	public static final String END_DATE = "endDate";
	public static final String SEARCH_BY_WILDCARD = "searchByWildcard";
	
	public static final String STATUS = "status";
	public static final String SOURCES = "sources";
	public static final String BUSINESS_IDS = "businessIds";

	
	
	private ReportServiceUtils() {
		
	}

	/**
	 * Process Campaign updated usablility  CX Campaign/Template Aggregation Data
	 *
	 * @param aggregation
	 * @param campaignIdToUsageMap
	 */
	public static void processCXAggregationDataV1(Terms aggregation, Map<Integer, CommunicationUsageStatsMessage> keyIdToUsageMap) {
		 
		if (CollectionUtils.isNotEmpty(aggregation.getBuckets())) {
			CommunicationUsageStatsMessage usageStatsMessage = null;
			for (Terms.Bucket termEntry : aggregation.getBuckets()) {
				Integer keyId = Integer.parseInt(termEntry.getKey().toString());
				Long sentCount = termEntry.getDocCount();
				usageStatsMessage = new CommunicationUsageStatsMessage();
				usageStatsMessage.setSentSuccess(sentCount);
				keyIdToUsageMap.put(keyId, usageStatsMessage);
				Terms campaignStatusTermAgg = termEntry.getAggregations().get(STATUS);
				setCxCampaignUsageStat(campaignStatusTermAgg, usageStatsMessage);
			}
		}
	}
	
	/**
	 * Setting stats for delivered CX Campaigns
	 * 
	 * @param campaignBuckets
	 * @param usageStatsMessage
	 */
	private static void setCxCampaignUsageStat(Terms campaignStatusTermAgg, CommunicationUsageStatsMessage usageStatsMessage) {
		if(campaignStatusTermAgg== null) {
			return;
		}
		
		if (CollectionUtils.isNotEmpty(campaignStatusTermAgg.getBuckets())) {
			for (Terms.Bucket entry : campaignStatusTermAgg.getBuckets()) {
				Integer statusKeyId = Integer.parseInt(entry.getKey().toString());
				if (statusKeyId == 1) {
					Long deliveredCount = setDeliveredOpenCount(usageStatsMessage, entry);
					ParsedFilter  sentimentFilterAggregation = entry.getAggregations().get("sentiment_click_count");
					if (sentimentFilterAggregation != null) {
						usageStatsMessage.setClickedCompletedCount(sentimentFilterAggregation.getDocCount());
						if (deliveredCount != null) {
							usageStatsMessage.setClickedCompletedPercent(CoreUtils.round(CoreUtils.getPercentile(sentimentFilterAggregation.getDocCount(), deliveredCount)));
						}
					}
				}
			}
		}
	}

	/**
	 * Process Campaign updated usablility  Referral Campaign/Template Aggregation Data
	 *
	 * @param aggregation
	 * @param campaignIdToUsageMap
	 */
	public static void processReferralAggregationDataV1(Terms aggregation, Map<Integer, CommunicationUsageStatsMessage> keyIdToUsageMap) {
		if (CollectionUtils.isNotEmpty(aggregation.getBuckets())) {
			CommunicationUsageStatsMessage usageStatsMessage = null;
			for (Terms.Bucket termEntry : aggregation.getBuckets()) {
				Integer keyId = Integer.parseInt(termEntry.getKey().toString());
				Long count = termEntry.getDocCount();
				usageStatsMessage = new CommunicationUsageStatsMessage();
				usageStatsMessage.setSentSuccess(count);
				keyIdToUsageMap.put(keyId, usageStatsMessage);
				setReferralCampaignUsageStats(usageStatsMessage, termEntry.getAggregations().get(STATUS));
			}
		}
	}

	private static void setReferralCampaignUsageStats(CommunicationUsageStatsMessage usageStatsMessage, Terms statusAgg) {
		if (CollectionUtils.isNotEmpty(statusAgg.getBuckets())) {
			for (Terms.Bucket entry : statusAgg.getBuckets()) {
				Integer statusKeyId = Integer.parseInt(entry.getKey().toString());
				if (statusKeyId == 1) {
					Long deliveredCount = setDeliveredOpenCountV1(usageStatsMessage, entry);
//					Filter clickCount = entry.getAggregations().get("sourceClicks");
					ParsedNested clickCount = entry.getAggregations().get("sourceClicks");
					if (clickCount != null) {
						usageStatsMessage.setClickedCompletedCount(clickCount.getDocCount());
						if (deliveredCount != null) {
							usageStatsMessage.setClickedCompletedPercent(CoreUtils.round(CoreUtils.getPercentile(clickCount.getDocCount(), deliveredCount)));
						}
					}
				}
			}
		}
	}
	
	/**
	 * Process Campaign updated usablility  RR Campaign/Template Aggregation Data
	 *
	 * @param aggregation
	 * @param campaignIdToUsageMap
	 */
	public static void processRRAggregationDataV1(Terms aggregation, Map<Integer, CommunicationUsageStatsMessage> keyIdToUsageMap) {
		List<Terms.Bucket> buckets = (List<Bucket>) aggregation.getBuckets();
		if (CollectionUtils.isNotEmpty(buckets)) {
			CommunicationUsageStatsMessage usageStatsMessage = null;
			for (Bucket campaignEntry : buckets) {
				Integer campaignId = Integer.parseInt(String.valueOf(campaignEntry.getKey()));
				
				usageStatsMessage = new CommunicationUsageStatsMessage();
				keyIdToUsageMap.put(campaignId, usageStatsMessage);
				
				setRRCampaignUsageStats(usageStatsMessage, campaignEntry);
			}
		}
	}

	private static void setRRCampaignUsageStats(CommunicationUsageStatsMessage usageStatsMessage, Terms.Bucket campaignEntry) {
		
		Long deliveredCount = campaignEntry.getDocCount();
		usageStatsMessage.setDelivered(deliveredCount);
		
		ValueCount openCount = ((ValueCount) campaignEntry.getAggregations().get("open_count"));
		if (openCount != null) {
			usageStatsMessage.setOpenStartedCount(openCount.getValue());
			usageStatsMessage.setOpenStartedPercent(CoreUtils.round(CoreUtils.getPercentile(openCount.getValue(), deliveredCount)));
		}
		ValueCount clickCount = ((ValueCount) campaignEntry.getAggregations().get("click_count"));
		if (clickCount != null) {
			usageStatsMessage.setClickedCompletedCount(clickCount.getValue());
			usageStatsMessage.setClickedCompletedPercent(CoreUtils.round(CoreUtils.getPercentile(clickCount.getValue(), deliveredCount)));
		}
	}

	/**
	 * Process Campaign updated usablility  Survey Campaign/Template Aggregation Data
	 *
	 * @param aggregation
	 * @param campaignIdToUsageMap
	 */
	public static void processSurveyCampaignUsageData(Terms aggregation, Map<Integer, CommunicationUsageStatsMessage> keyIdToUsageMap) {
		if (CollectionUtils.isNotEmpty(aggregation.getBuckets())) {
			CommunicationUsageStatsMessage usageStatsMessage = null;
			for (Bucket termEntry : aggregation.getBuckets()) {
				Integer keyId = Integer.parseInt(termEntry.getKey().toString());
				if(keyId == 4908) {
					System.err.println();
				}
				Long count = termEntry.getDocCount();
				usageStatsMessage = new CommunicationUsageStatsMessage();
				usageStatsMessage.setSentSuccess(count);
				keyIdToUsageMap.put(keyId, usageStatsMessage);
				
				Terms statusAgg  = termEntry.getAggregations().get(STATUS);
				if (statusAgg != null && CollectionUtils.isNotEmpty(statusAgg.getBuckets())) {
					setSurveyCampaignUsageStats(usageStatsMessage, statusAgg);
				}
			}
		}
	}

	private static void setSurveyCampaignUsageStats(CommunicationUsageStatsMessage usageStatsMessage, Terms statusAggregation) {
		if (statusAggregation == null || CollectionUtils.isEmpty(statusAggregation.getBuckets())) {
			return;
		}
		for (Bucket entry : statusAggregation.getBuckets()) {
			Integer statusKeyId = Integer.parseInt(entry.getKey().toString());
			if (statusKeyId == 1) {
				ParsedValueCount requestCount = entry.getAggregations().get(REQUEST_COUNT);
				Long sentSuccessCount = null;
				if(requestCount != null) {
					sentSuccessCount = requestCount.getValue();
					usageStatsMessage.setDelivered(requestCount.getValue());
					ParsedSum startedCountAggr = entry.getAggregations().get("survey_started_count");
					if(startedCountAggr != null) {
						long startedCount = (long) startedCountAggr.getValue();
						usageStatsMessage.setOpenStartedCount(startedCount);
						if(sentSuccessCount != null) {
							usageStatsMessage.setOpenStartedPercent(CoreUtils.round(CoreUtils.getPercentile(startedCount, sentSuccessCount)));
						}
					}
					ParsedSum completedCountAggr = entry.getAggregations().get("survey_completed_count");
					if(completedCountAggr != null) {
						long completedCount = (long)completedCountAggr.getValue();
						usageStatsMessage.setClickedCompletedCount(completedCount);
						if(sentSuccessCount != null) {
							usageStatsMessage.setClickedCompletedPercent(CoreUtils.round(CoreUtils.getPercentile(completedCount, sentSuccessCount)));
						}
					}
				}
			}
		}
	}



	
	
	private static Long setDeliveredOpenCount(CommunicationUsageStatsMessage usageStatsMessage, TermsAggregation.Entry  entry) {
		ValueCountAggregation requestCount = entry.getValueCountAggregation(REQUEST_COUNT);
		Long deliveredCount = null;
		if (requestCount != null) {
			deliveredCount = requestCount.getValueCount();
			usageStatsMessage.setDelivered(requestCount.getValueCount());
		}
		ValueCountAggregation openCount = entry.getValueCountAggregation("open_count");
		if (openCount != null) {
			usageStatsMessage.setOpenStartedCount(openCount.getValueCount());
			if (deliveredCount != null) {
				usageStatsMessage.setOpenStartedPercent(CoreUtils.round(CoreUtils.getPercentile(openCount.getValueCount(), deliveredCount)));
			}
		}
		return deliveredCount;
	}
	
	
	/**
	 * Setting delivered count and open count/percentage in CX, Referral and RR commUsageMessage.
	 *
	 * @param usageStatsMessage
	 * @param entry
	 * @return
	 */
	private static Long setDeliveredOpenCount(CommunicationUsageStatsMessage usageStatsMessage, Terms.Bucket entry) {
		ParsedValueCount requestCount =  entry.getAggregations().get(REQUEST_COUNT);
//		ValueCountAggregation requestCount = entry.getValueCountAggregation(REQUEST_COUNT);
		Long deliveredCount = null;
		if (requestCount != null) {
			deliveredCount = requestCount.getValue();
			usageStatsMessage.setDelivered(requestCount.getValue());
		}
		ParsedValueCount openCount = entry.getAggregations().get("open_count");
//		ValueCountAggregation openCount = entry.getValueCountAggregation("open_count");
		if (openCount != null) {
			usageStatsMessage.setOpenStartedCount(openCount.getValue());
			if (deliveredCount != null) {
				usageStatsMessage.setOpenStartedPercent(CoreUtils.round(CoreUtils.getPercentile(openCount.getValue(), deliveredCount)));
			}
		}
		return deliveredCount;
	}
	
	/**
	 * Setting delivered count and open count/percentage in CX, Referral and RR commUsageMessage.
	 *
	 * @param usageStatsMessage
	 * @param entry
	 * @return
	 */
	private static Long setDeliveredOpenCountV1(CommunicationUsageStatsMessage usageStatsMessage, Terms.Bucket entry) {
		ParsedValueCount requestCount =  entry.getAggregations().get(REQUEST_COUNT);
//		ValueCount requestCount = entry.getAggregations().get(REQUEST_COUNT);
		Long deliveredCount = null;
		if (requestCount != null) {
			deliveredCount = requestCount.getValue();
			usageStatsMessage.setDelivered(deliveredCount);
		}
//		ValueCount openCount = entry.getAggregations().get("open_count");
		ParsedValueCount openCount = entry.getAggregations().get("open_count");
		if (openCount != null) {
			usageStatsMessage.setOpenStartedCount(openCount.getValue());
			if (deliveredCount != null) {
				usageStatsMessage.setOpenStartedPercent(CoreUtils.round(CoreUtils.getPercentile(openCount.getValue(), deliveredCount)));
			}
		}
		return deliveredCount;
	}


	/**
	 * Process CX Campaign/Template Aggregation Data
	 * @param aggregation
	 * @param campaignIdToUsageMap
	 */
	public static void processCXAggragationData(Terms aggregation , Map<Integer,CommunicationUsageStatsMessage> keyIdToUsageMap) {
		 
		if(CollectionUtils.isNotEmpty(aggregation.getBuckets())) {
			CommunicationUsageStatsMessage usageStatsMessage = null;
			for(Terms.Bucket bucket : aggregation.getBuckets()) {
				Integer keyId = Integer.parseInt(bucket.getKey().toString());
				 usageStatsMessage = new CommunicationUsageStatsMessage();
				 keyIdToUsageMap.put(keyId, usageStatsMessage);
				 Long sentSuccessCount = setSentOpenCount(usageStatsMessage, bucket);
				 ParsedFilter sentimentFilterAggregation = bucket.getAggregations().get("sentiment_click_count");
				 if(sentimentFilterAggregation != null) {
					 usageStatsMessage.setClickedCompletedCount(sentimentFilterAggregation.getDocCount());
					 if(sentSuccessCount != null) {
						 usageStatsMessage.setClickedCompletedPercent(CoreUtils.round(CoreUtils.getPercentile(sentimentFilterAggregation.getDocCount(), sentSuccessCount)));
					 }
				 }
			}
		}
	}
	
	/**
	 * Process PROMOTION Campaign/Template Aggregation Data
	 * @param aggregation
	 * @param campaignIdToUsageMap
	 */
	public static void processPromotionAggregationData(Terms aggregation , Map<Integer,CommunicationUsageStatsMessage> keyIdToUsageMap) {
		if(CollectionUtils.isNotEmpty(aggregation.getBuckets())) {
			CommunicationUsageStatsMessage usageStatsMessage = null;
			for(Terms.Bucket entry : aggregation.getBuckets()) {
				Integer keyId = Integer.parseInt(entry.getKey().toString());
				 usageStatsMessage = new CommunicationUsageStatsMessage();
				 keyIdToUsageMap.put(keyId, usageStatsMessage);
				 setSentOpenCountV1(usageStatsMessage, entry);
			}
		}
	}
	
	/**
	 * Process PROMOTION Campaign Aggregation Data
	 * @param aggregation
	 * @param campaignIdToUsageMap
	 */
	public static void processPromotionAggregationDataV1(Terms aggregation, Map<Integer, CommunicationUsageStatsMessage> keyIdToUsageMap) {
		if (CollectionUtils.isNotEmpty(aggregation.getBuckets())) {
			CommunicationUsageStatsMessage usageStatsMessage = null;
			for (Terms.Bucket entry : aggregation.getBuckets()) {
				Integer keyId = Integer.parseInt(entry.getKey().toString());
				usageStatsMessage = new CommunicationUsageStatsMessage();
				usageStatsMessage.setSentSuccess(entry.getDocCount());
				keyIdToUsageMap.put(keyId, usageStatsMessage);
				setPromotionCampaignUsageStat(entry.getAggregations().get(STATUS), usageStatsMessage);
			}
		}
	}
	
	/**
	 * Process APPOINTMENT REMINDER Template Aggregation Data
	 * @param aggregation
	 * @param campaignIdToUsageMap
	 */
	public static void processAppointmentReminderAggregationData(Terms aggregation , Map<Integer,CommunicationUsageStatsMessage> keyIdToUsageMap) {
		if(CollectionUtils.isNotEmpty(aggregation.getBuckets())) {
			CommunicationUsageStatsMessage usageStatsMessage = null;
			for(Terms.Bucket entry : aggregation.getBuckets()) {
				Integer keyId = Integer.parseInt(entry.getKey().toString());
				 usageStatsMessage = new CommunicationUsageStatsMessage();
				 keyIdToUsageMap.put(keyId, usageStatsMessage);
				 setSentOpenCountV1(usageStatsMessage, entry);
			}
		}
	}
	
	/**
	 * Process APPOINTMENT RECALL Template Aggregation Data
	 * @param aggregation
	 * @param campaignIdToUsageMap
	 */
	public static void processAppointmentRecallAggregationData(Terms aggregation , Map<Integer,CommunicationUsageStatsMessage> keyIdToUsageMap) {
		if(CollectionUtils.isNotEmpty(aggregation.getBuckets())) {
			CommunicationUsageStatsMessage usageStatsMessage = null;
			for(Terms.Bucket entry : aggregation.getBuckets()) {
				Integer keyId = Integer.parseInt(entry.getKey().toString());
				 usageStatsMessage = new CommunicationUsageStatsMessage();
				 keyIdToUsageMap.put(keyId, usageStatsMessage);
				 setSentOpenCountV1(usageStatsMessage, entry);
			}
		}
	}
	
	/**
	 * Process APPOINTMENT REMINDER Campaigns Aggregation Data
	 * 
	 * @param aggregation
	 * @param campaignIdToUsageMap
	 */
	public static void processAppointmentReminderCampaignAggregationData(Terms aggregation, Map<Integer, CommunicationUsageStatsMessage> keyIdToUsageMap) {
		if (CollectionUtils.isNotEmpty(aggregation.getBuckets())) {
			for (Terms.Bucket entry : aggregation.getBuckets()) {
				CommunicationUsageStatsMessage usageStatsMessage = new CommunicationUsageStatsMessage();
				Integer keyId = Integer.parseInt(entry.getKey().toString());
				
				Long delivered = entry.getDocCount();
				usageStatsMessage.setDelivered(delivered);
				
				ParsedFilter openCountAgg = entry.getAggregations().get("open_count");
				if (openCountAgg != null) {
					Long openCount = openCountAgg.getDocCount();
					usageStatsMessage.setOpenStartedCount(openCount);
					usageStatsMessage.setOpenStartedPercent(CoreUtils.round(CoreUtils.getPercentile(openCount, delivered)));
					
				}
				
				ParsedFilter clickCountAgg = entry.getAggregations().get("click_count");
				if (clickCountAgg != null) {
					Long clickCount = clickCountAgg.getDocCount();
					usageStatsMessage.setClickedCompletedCount(clickCount);
					usageStatsMessage.setClickedCompletedPercent(CoreUtils.round(CoreUtils.getPercentile(clickCount, delivered)));
				}
				
				keyIdToUsageMap.put(keyId, usageStatsMessage);
			}
		}
	}
	
	/**
	 * Process APPOINTMENT RECALL Campaigns Aggregation Data
	 * 
	 * @param aggregation
	 * @param campaignIdToUsageMap
	 */
	public static void processAppointmentRecallCampaignAggregationData(Terms aggregation, Map<Integer, CommunicationUsageStatsMessage> keyIdToUsageMap) {
		if (CollectionUtils.isNotEmpty(aggregation.getBuckets())) {
			for (Terms.Bucket entry : aggregation.getBuckets()) {
				CommunicationUsageStatsMessage usageStatsMessage = new CommunicationUsageStatsMessage();
				Integer keyId = Integer.parseInt(entry.getKey().toString());
				
				Long delivered = entry.getDocCount();
				usageStatsMessage.setDelivered(delivered);
				
				ParsedFilter openCountAgg = entry.getAggregations().get("open_count");
				if (openCountAgg != null) {
					Long openCount = openCountAgg.getDocCount();
					usageStatsMessage.setOpenStartedCount(openCount);
					usageStatsMessage.setOpenStartedPercent(CoreUtils.round(CoreUtils.getPercentile(openCount, delivered)));
					
				}
				
				ParsedFilter clickCountAgg = entry.getAggregations().get("click_count");
				if (clickCountAgg != null) {
					Long clickCount = clickCountAgg.getDocCount();
					usageStatsMessage.setClickedCompletedCount(clickCount);
					usageStatsMessage.setClickedCompletedPercent(CoreUtils.round(CoreUtils.getPercentile(clickCount, delivered)));
				}
				
				keyIdToUsageMap.put(keyId, usageStatsMessage);
			}
		}
	}
	
	/**
	 * Process APPOINTMENT FORM Campaigns Aggregation Data
	 * 
	 * @param aggregation
	 * @param campaignIdToUsageMap
	 */
	public static void processAppointmentFormCampaignAggregationData(Terms aggregation, Map<Integer, CommunicationUsageStatsMessage> keyIdToUsageMap) {
		if (CollectionUtils.isNotEmpty(aggregation.getBuckets())) {
			for (Terms.Bucket entry : aggregation.getBuckets()) {
				CommunicationUsageStatsMessage usageStatsMessage = new CommunicationUsageStatsMessage();
				Integer keyId = Integer.parseInt(entry.getKey().toString());
				
				Long delivered = entry.getDocCount();
				usageStatsMessage.setDelivered(delivered);
				
				ParsedFilter openCountAgg = entry.getAggregations().get("open_count");
				if (openCountAgg != null) {
					Long openCount = openCountAgg.getDocCount();
					usageStatsMessage.setOpenStartedCount(openCount);
					usageStatsMessage.setOpenStartedPercent(CoreUtils.round(CoreUtils.getPercentile(openCount, delivered)));
				}
				
				ParsedFilter clickCountAgg = entry.getAggregations().get("click_count");
				if (clickCountAgg != null) {
					Long clickCount = clickCountAgg.getDocCount();
					usageStatsMessage.setClickedCompletedCount(clickCount);
					usageStatsMessage.setClickedCompletedPercent(CoreUtils.round(CoreUtils.getPercentile(clickCount, delivered)));
				}
				
				keyIdToUsageMap.put(keyId, usageStatsMessage);
			}
		}
	}
	
	private static void setPromotionCampaignUsageStat(Terms statusAgg, CommunicationUsageStatsMessage usageStatsMessage) {
		if (CollectionUtils.isNotEmpty(statusAgg.getBuckets())) {
			for (Terms.Bucket entry : statusAgg.getBuckets()) {
				Integer statusKeyId = Integer.parseInt(entry.getKey().toString());
				if (statusKeyId == 1) {
					setDeliveredOpenCountV1(usageStatsMessage, entry);
				}
			}
		}
	}
	
	/**
	 * Process REFERRAL Campaign/Template Aggregation Data
	 * 
	 * @param aggregation
	 * @param campaignIdToUsageMap
	 */
	public static void processReferralAggragationData(Terms aggregation, Map<Integer, CommunicationUsageStatsMessage> keyIdToUsageMap) {
		if (CollectionUtils.isNotEmpty(aggregation.getBuckets())) {
			CommunicationUsageStatsMessage usageStatsMessage = null;
			for (Terms.Bucket entry : aggregation.getBuckets()) {
				Integer keyId = Integer.parseInt(entry.getKey().toString());
				usageStatsMessage = new CommunicationUsageStatsMessage();
				keyIdToUsageMap.put(keyId, usageStatsMessage);
				Long sentSuccessCount = setSentOpenCountV1(usageStatsMessage, entry);
				ParsedNested clickCount = entry.getAggregations().get("sourceClicks");
				if (clickCount != null) {
					usageStatsMessage.setClickedCompletedCount(clickCount.getDocCount());
					if (sentSuccessCount != null) {
						usageStatsMessage.setClickedCompletedPercent(CoreUtils.round(CoreUtils.getPercentile(clickCount.getDocCount(), sentSuccessCount)));
					}
				}
			}
		}
	}
	
	/**
	 * Get usage stats for custom templates
	 * 
	 * @param templateIdVsSentCount
	 * @param keyIdToUsageMap
	 */
	public static void getCommunicationUsageStatsMessageForCustomTemplates(List<CustomTemplateSentCountMessage> templateIdVsSentCount, Map<Integer, CommunicationUsageStatsMessage> keyIdToUsageMap){
		if(CollectionUtils.isEmpty(templateIdVsSentCount)) {
			return;
		}
		for(CustomTemplateSentCountMessage msg : templateIdVsSentCount) {
			keyIdToUsageMap.put(msg.getTemplateId(), getCommunicationUsageStatsMessageForCustomTemplate(msg));
		}
	}
	
	/**
	 * Get usage stats for custom template
	 * 
	 * @param message
	 * @return
	 */
	private static CommunicationUsageStatsMessage getCommunicationUsageStatsMessageForCustomTemplate(CustomTemplateSentCountMessage message) {
		if (message == null) {
			return null;
		}
		CommunicationUsageStatsMessage msg = new CommunicationUsageStatsMessage();
		msg.setSentSuccess(message.getSentCount());
		msg.setClickedCompletedCount(0l);
		msg.setClickedCompletedPercent(0d);
		msg.setOpenStartedCount(0l);
		msg.setOpenStartedPercent(0d);
		return msg;
	}

	private static Long setSentOpenCount(CommunicationUsageStatsMessage usageStatsMessage, TermsAggregation.Entry entry) {
		ValueCountAggregation requestCount = entry.getValueCountAggregation(REQUEST_COUNT);
		Long sentSuccessCount = null;
		if (requestCount != null) {
			sentSuccessCount = requestCount.getValueCount();
			usageStatsMessage.setSentSuccess(requestCount.getValueCount());
		}
		ValueCountAggregation openCount = entry.getValueCountAggregation("open_count");
		if (openCount != null) {
			usageStatsMessage.setOpenStartedCount(openCount.getValueCount());
			if (sentSuccessCount != null) {
				usageStatsMessage.setOpenStartedPercent(CoreUtils.round(CoreUtils.getPercentile(openCount.getValueCount(), sentSuccessCount)));
			}
		}
		return sentSuccessCount;
	}
	
	/**
	 * Setting sent count and open count/percentage in CX, Referral, RR and Promotion,Appointment reminder, Appointment recall commUsageMessage.
	 * 
	 * @param usageStatsMessage
	 * @param bucket
	 * @return
	 */
	private static Long setSentOpenCount(CommunicationUsageStatsMessage usageStatsMessage, Terms.Bucket bucket) {
		ParsedValueCount requestCount = bucket.getAggregations().get(REQUEST_COUNT);
		Long sentSuccessCount = null;
		if (requestCount != null) {
			sentSuccessCount = requestCount.getValue();
			usageStatsMessage.setSentSuccess(requestCount.getValue());
		}
		ParsedValueCount openCount = bucket.getAggregations().get("open_count");
		if (openCount != null) {
			usageStatsMessage.setOpenStartedCount(openCount.getValue());
			if (sentSuccessCount != null) {
				usageStatsMessage.setOpenStartedPercent(CoreUtils.round(CoreUtils.getPercentile(openCount.getValue(), sentSuccessCount)));
			}
		}
		return sentSuccessCount;
	}
	
	/**
	 * Setting sent count and open count/percentage in CX, Referral, RR and Promotion commUsageMessage.
	 * 
	 * @param usageStatsMessage
	 * @param entry
	 * @return
	 */
	private static Long setSentOpenCountV1(CommunicationUsageStatsMessage usageStatsMessage, Terms.Bucket entry) {
		ParsedValueCount requestCount = entry.getAggregations().get(REQUEST_COUNT);
		Long sentSuccessCount = null;
		if (requestCount != null) {
			sentSuccessCount = requestCount.getValue();
			usageStatsMessage.setSentSuccess(requestCount.getValue());
		}
		ParsedValueCount openCount = entry.getAggregations().get("open_count");
		if (openCount != null) {
			usageStatsMessage.setOpenStartedCount(openCount.getValue());
			if (sentSuccessCount != null) {
				usageStatsMessage.setOpenStartedPercent(CoreUtils.round(CoreUtils.getPercentile(openCount.getValue(), sentSuccessCount)));
			}
		}
		return sentSuccessCount;
	}
	
	/**
	 * Process RR Campaign/Template Aggregation Data
	 * @param aggregation
	 * @param campaignIdToUsageMap
	 */
	public static void processRRAggragationData(Terms aggregation , Map<Integer,CommunicationUsageStatsMessage> keyIdToUsageMap) {
		List<Bucket> buckets = (List<Bucket>) aggregation.getBuckets();
		if(CollectionUtils.isNotEmpty(buckets)) {
			CommunicationUsageStatsMessage usageStatsMessage = null;
			for(Bucket entry : buckets) {
				Integer keyId = Integer.parseInt(String.valueOf(entry.getKey()));
				 usageStatsMessage = new CommunicationUsageStatsMessage();
				 keyIdToUsageMap.put(keyId, usageStatsMessage);
				 
				 Long sentSuccessCount = setSentOpenCountV1(usageStatsMessage, entry);
				 Terms clickTermsAggregation = (Terms) entry.getAggregations().get("click_count");
				 if(clickTermsAggregation != null) {
					 long totalClickCount = 0;
					 for(Bucket clickBucket : clickTermsAggregation.getBuckets()){
							totalClickCount+=clickBucket.getDocCount();
						}
					 usageStatsMessage.setClickedCompletedCount(totalClickCount);
					 if(sentSuccessCount != null) {
						 usageStatsMessage.setClickedCompletedPercent(CoreUtils.round(CoreUtils.getPercentile(totalClickCount, sentSuccessCount)));
					 }
				 }
			}
		}
	}
	
	/**
	 * Process Survey Campaign/Template Aggregation Data
	 * @param aggregation
	 * @param campaignIdToUsageMap
	 */
	public static void processSurveyTemplateUsageData(Terms aggregation , Map<Integer,CommunicationUsageStatsMessage> keyIdToUsageMap) {
		
		if(CollectionUtils.isNotEmpty(aggregation.getBuckets())) {
			CommunicationUsageStatsMessage usageStatsMessage = null;
			for(Terms.Bucket entry : aggregation.getBuckets()) {
				Integer keyId = Integer.parseInt(entry.getKey().toString());
				if(keyId == 4908) {
					System.err.println();
				}
				 usageStatsMessage = new CommunicationUsageStatsMessage();
				 keyIdToUsageMap.put(keyId, usageStatsMessage);
				 
				 ParsedValueCount requestCount = entry.getAggregations().get(REQUEST_COUNT);
				 Long sentSuccessCount = null;
				 if(requestCount != null) {
					 sentSuccessCount = requestCount.getValue();
					 usageStatsMessage.setSentSuccess(requestCount.getValue());
				 }
				 ParsedSum startedCountAggr = entry.getAggregations().get("survey_started_count");
				 if(startedCountAggr != null) {
					 long startedCount = (long)startedCountAggr.getValue();
					 usageStatsMessage.setOpenStartedCount(startedCount);
					 if(sentSuccessCount != null) {
						 usageStatsMessage.setOpenStartedPercent(CoreUtils.round(CoreUtils.getPercentile(startedCount, sentSuccessCount)));
					 }
				 }
				 ParsedSum completedCountAggr = entry.getAggregations().get("survey_completed_count");
				 if(completedCountAggr != null) {
					 long completedCount = (long)completedCountAggr.getValue();
					 usageStatsMessage.setClickedCompletedCount(completedCount);
					 if(sentSuccessCount != null) {
						 usageStatsMessage.setClickedCompletedPercent(CoreUtils.round(CoreUtils.getPercentile(completedCount, sentSuccessCount)));
					 }
				 }
			}
		}
	}
	
	/**
	 * Get latest status for customer for cx campaign
	 * @param commMessage
	 * @return
	 */
	public static String getLatestStatusForCustomerForCxCampaign(CXCommMessage commMessage) {
		String status = null;
		if(commMessage.getStatus() == 0) {
			status = CampaignCustomerActivityEvent.FAILED.getMessage();
		}else if(commMessage.getStatus() == 1) {
			if(commMessage.getReviewId()  != null && commMessage.getReviewId() > 0) {
				status = CampaignCustomerActivityEvent.REVIEWED.getMessage();
			}
			else if(commMessage.getClicked() != null && commMessage.getClicked() > 0) {
				status = CampaignCustomerActivityEvent.CLICKED.getMessage();
			}
			else if(commMessage.getOpened() != null && commMessage.getOpened() > 0) {
				status = CampaignCustomerActivityEvent.OPEN.getMessage();
			}else {
				status = CampaignCustomerActivityEvent.DELIVERED.getMessage();
			}
		}
		else if (isScheduledRequest(commMessage.getStatus())) {
			// status 2 for scheduled (DND)
			status = CampaignCustomerActivityEvent.SCHEDULED.getMessage();
		}
		return status;
	}
	
	/**
	 * Get latest status for customer for Referral campaign
	 * 
	 * @param commMessage
	 * @return
	 */
	public static String getLatestStatusForCustomerForReferralCampaign(ReferralCommMessage commMessage) {
		String status = null;
		if (commMessage.getStatus() == 0) {
			status = CampaignCustomerActivityEvent.FAILED.getMessage();
		}
		else if(commMessage.getStatus() == 1){
			if (commMessage.getClicked() != null && commMessage.getClicked() > 0) {
				status = CampaignCustomerActivityEvent.SHARED.getMessage();
			}
			else if (commMessage.getOpened() != null && commMessage.getOpened() > 0) {
				status = CampaignCustomerActivityEvent.OPEN.getMessage();
			}
			else {
				status = CampaignCustomerActivityEvent.DELIVERED.getMessage();
			}
		}
		else if (isScheduledRequest(commMessage.getStatus())) {
			// DND case with status 2 shown as Scheduled on UI
			status = CampaignCustomerActivityEvent.SCHEDULED.getMessage();
		}
		return status;
	}

	/**
	 * Get failure Message from failure reason
	 * @param failureReason
	 * @return
	 */
	public static String getFailureMessageForReason(String failureReason) {
		String status = CampaignCustomerActivityEvent.FAILED.getMessage();
		if(StringUtils.isNotBlank(failureReason)) {
			SendGridEventToMessageEnum event = SendGridEventToMessageEnum.getEvent(failureReason);
			if(event != null) { 
				status = event.getEventMessage();
			}
		}
		return status;
	}
	
	/**
	 * Get failure Message from failure reason for campaign status summary mail report
	 * @param failureReason
	 * @return
	 */
	public static String getFailureMessageForSummaryReason(String failureReason) {
		if(StringUtils.isNotBlank(failureReason)) {
			SendGridEventToMessageEnum event = SendGridEventToMessageEnum.getEvent(failureReason);
			if(event != null) { 
				return event.getEventMessage();
			}
			return failureReason;
		}
		return CampaignCustomerActivityEvent.FAILED.getMessage();
	}
	
	/**
	 * Get latest status for customer for RR campaign
	 * @param commMessage
	 * @return
	 */
	public static String getLatestStatusForCustomerForRRCampaign(CommMessage commMessage) {
		String status = null;
		if(commMessage.getStatus() == 0) {
			status = CampaignCustomerActivityEvent.FAILED.getMessage();
		} else if (commMessage.getStatus() == 1) {
			if(commMessage.getReviews() != null && commMessage.getReviews().length > 0) {
				status = CampaignCustomerActivityEvent.REVIEWED.getMessage();
			}
			else if(commMessage.getSourceClicks() != null && commMessage.getSourceClicks().length > 0) {
				status = CampaignCustomerActivityEvent.CLICKED.getMessage();
			}
			else if(commMessage.getOpenCount() != null && commMessage.getOpenCount() > 0) {
				status = CampaignCustomerActivityEvent.OPEN.getMessage();
			}else {
				status = CampaignCustomerActivityEvent.DELIVERED.getMessage();
			}}
		else if (isScheduledRequest(commMessage.getStatus())) {
			// DND case with status 2 shown as Scheduled on UI
			status = CampaignCustomerActivityEvent.SCHEDULED.getMessage();
		}
		return status;
	}
	
		
		
		
	/**
	 * Get latest status for customer for Survey campaign
	 * @param commMessage
	 * @return
	 */
	public static String getLatestStatusForCustomerForSurveyCampaign(SurveyCommMessage commMessage) {
		String status = null;
		if(commMessage.getStatus() == 0) {
			status = CampaignCustomerActivityEvent.FAILED.getMessage();
		} else if (commMessage.getStatus() == 1) {
			if(commMessage.getIsSurveyCompleted() != null && commMessage.getIsSurveyCompleted() == 1) {
				status = CampaignCustomerActivityEvent.SURVEY_COMPLETED.getMessage();
			}
			else if(commMessage.getClicked()!= null && commMessage.getClicked() > 0) {
				status = CampaignCustomerActivityEvent.CLICKED.getMessage();
			}
			else if(commMessage.getOpenCount() != null && commMessage.getOpenCount() > 0) {
				status = CampaignCustomerActivityEvent.OPEN.getMessage();
			}else {
				status = CampaignCustomerActivityEvent.DELIVERED.getMessage();
			}
		}
		else if (isScheduledRequest(commMessage.getStatus())) {
			// DND case with status 2 shown as Scheduled on UI
			status = CampaignCustomerActivityEvent.SCHEDULED.getMessage();
		}
		return status;
	}
	
	/**
	 * Get latest status for customer for RR campaign
	 * @param commMessage
	 * @return
	 */
	public static String getLatestStatusForCustomerForPromotionCampaign(PromotionCommMessage promotionMessage, boolean showInfluenceMetric) {

		String status = null;
		if (promotionMessage.getStatus() == 1) {
			if (promotionMessage.getOpened() != null && promotionMessage.getOpened() == 1) {
				status = CampaignCustomerActivityEvent.OPEN.getMessage();
			} else {
				status = CampaignCustomerActivityEvent.DELIVERED.getMessage();
			}
			if(BooleanUtils.isTrue(promotionMessage.getInfluenced()) && BooleanUtils.isTrue(showInfluenceMetric)) {
				status = CampaignCustomerActivityEvent.INFLUENCED.getMessage();
			}
		} else if (promotionMessage.getStatus() == 0) {
			if (StringUtils.isNotBlank(promotionMessage.getFailureReason())) {
				status = CampaignCustomerActivityEvent.FAILED.getMessage();
			} else {
				status = "Unknown error";
			}
		} else if (isScheduledRequest(promotionMessage.getStatus())) {
			// DND case with status 2 shown as Scheduled on UI
			status = CampaignCustomerActivityEvent.SCHEDULED.getMessage();
		}  
		return status;
	}
	
	/**
	 * Get latest  click status for customer for AppointmentReminder campaign
	 * @param lastClick
	 * @return
	 */
	private static String getClickStatusForAppointmentReminderCampaign(String lastClick) {
		if (StringUtils.equalsIgnoreCase(lastClick, AppointmentClickTypeEnum.CONFIRMATION.getType())) {
			return CampaignCustomerActivityEvent.CONFIRMATION_CLICK.getMessage();
		} else if (StringUtils.equalsIgnoreCase(lastClick, AppointmentClickTypeEnum.CANCELLATION.getType())) {
			return CampaignCustomerActivityEvent.CANCELED_CLICK.getMessage();
		} else if (StringUtils.equalsIgnoreCase(lastClick, AppointmentClickTypeEnum.RESCHEDULE_CLICK.getType())) {
			return CampaignCustomerActivityEvent.RESCHEDULE_CLICK.getMessage();
		}
		return StringUtils.EMPTY;
	}
	
	/**
	 * Get latest status for customer for AppointmentReminder campaign
	 * @param commMessage
	 * @return
	 */
	public static String getLatestStatusForCustomerForAppointmentReminderCampaign(AppointmentReminderCommMessage commMessage) {
		String status = null;
		if (commMessage.getStatus() == 0) {
			status = CampaignCustomerActivityEvent.FAILED.getMessage();
		} else if (commMessage.getStatus() == 1) {
			if (BooleanUtils.isTrue(commMessage.getClicked()) && commMessage.getClicked() == true && StringUtils.isNotBlank(commMessage.getLastClick())) {
				status = getClickStatusForAppointmentReminderCampaign(commMessage.getLastClick());
			} else if (BooleanUtils.isTrue(commMessage.getOpened()) && commMessage.getOpened() == true) {
				status = CampaignCustomerActivityEvent.OPEN.getMessage();
			} else {
				status = CampaignCustomerActivityEvent.DELIVERED.getMessage();
			}
		} else if (isScheduledRequest(commMessage.getStatus())) {
			// DND case with status 2 shown as Scheduled on UI
			status = CampaignCustomerActivityEvent.SCHEDULED.getMessage();
		} else if (commMessage.getStatus() == 4) {
			status = CampaignCustomerActivityEvent.DROPPED.getMessage();
		}
		return status;
	}
	
	/**
	 * Get latest status for customer for AppointmentRecall campaign
	 * 
	 * @param commMessage
	 * @return
	 */
	public static String getLatestStatusForCustomerForAppointmentRecallCampaign(AppointmentRecallCommMessage commMessage) {
		String status = null;
		if (commMessage.getStatus() == 0) {
			status = CampaignCustomerActivityEvent.FAILED.getMessage();
		} else if (commMessage.getStatus() == 1) {
			if (BooleanUtils.isTrue(commMessage.getBooked())) {
				status = CampaignCustomerActivityEvent.BOOKED.getMessage();
			} else if (BooleanUtils.isTrue(commMessage.getClicked())) {
				status = CampaignCustomerActivityEvent.CLICKED.getMessage();
			} else if (BooleanUtils.isTrue(commMessage.getOpened())) {
				status = CampaignCustomerActivityEvent.OPEN.getMessage();
			} else {
				status = CampaignCustomerActivityEvent.DELIVERED.getMessage();
			}
		}
		return status;
	}
	
	/**
	 * Get latest status for customer for AppointmentForm campaign
	 * 
	 * @param commMessage
	 * @return
	 */
	public static String getLatestStatusForCustomerForAppointmentFormCampaign(AppointmentFormCommMessage commMessage) {
		String status = null;
		if (commMessage.getStatus() == 0) {
			status = CampaignCustomerActivityEvent.FAILED.getMessage();
		} else if (commMessage.getStatus() == 1) {
			if (BooleanUtils.isTrue(commMessage.getCompleteFormFilled()) && commMessage.getCompleteFormFilled() == true) {
				status = CampaignCustomerActivityEvent.FORM_FILLED.getMessage();
			} else if (BooleanUtils.isTrue(commMessage.getClicked()) && commMessage.getClicked() == true) {
				status = CampaignCustomerActivityEvent.CLICKED.getMessage();
			} else if (BooleanUtils.isTrue(commMessage.getOpened()) && commMessage.getOpened() == true) {
				status = CampaignCustomerActivityEvent.OPEN.getMessage();
			} else {
				status = CampaignCustomerActivityEvent.DELIVERED.getMessage();
			}
		} else if (isScheduledRequest(commMessage.getStatus())) {
			// DND case with status 2 shown as Scheduled on UI
			status = CampaignCustomerActivityEvent.SCHEDULED.getMessage();
		} else if (commMessage.getStatus() == 4) {
			status = CampaignCustomerActivityEvent.DROPPED.getMessage();
		}
		return status;
	}
	
	private static boolean isScheduledRequest(Integer status) {
		return status != null && (status.intValue() == 2 || status.intValue() == 3);
	}
	
	/**
	 * Get sorting attributes for CX and RR Es docs
	 * @param sortBy
	 * @param requestType
	 * @return
	 */
	public static String getSortingAttributeForViewRecipientsListing(String sortBy , String requestType) {
		String resultSortBy = "updatedAt";
		if(requestType.equalsIgnoreCase("review_request") || requestType.equalsIgnoreCase("survey_request")) {
			resultSortBy = "c_time";
		}
		if (StringUtils.isNotBlank(sortBy)) {
			if (sortBy.equalsIgnoreCase("location")) {
				if (requestType.equalsIgnoreCase(Constants.APPOINTMENT_REMINDER_TYPE)) {
					resultSortBy = "businessAlias.raw";
				} else {
					resultSortBy = "businessAlias.keyword";
				}
			} else if (sortBy.equalsIgnoreCase("customerName")) {
				if (requestType.equalsIgnoreCase(Constants.APPOINTMENT_REMINDER_TYPE)) {
					resultSortBy = "customerName.raw";
				} else {
					resultSortBy = "customerName.keyword";
				}
			}
		}
		return resultSortBy;
		
	}
	
	public static void getCommunicationUsageStatsMessageForPromotionTemplates(List<PromotionTemplateSentCountMessage> templateIdVsSentCount,
			Map<Integer, CommunicationUsageStatsMessage> templateIdToUsageMap) {
		if(CollectionUtils.isEmpty(templateIdVsSentCount)) {
			return;
		}
		for(PromotionTemplateSentCountMessage message : templateIdVsSentCount) {
			templateIdToUsageMap.put(message.getTemplateId(), getPromotionalTemplateUsageStats(message));
		}
		
	}

	private static CommunicationUsageStatsMessage getPromotionalTemplateUsageStats(PromotionTemplateSentCountMessage templateIdVsSentCount) {
		CommunicationUsageStatsMessage usageStatus = new CommunicationUsageStatsMessage();
		usageStatus.setSentSuccess(templateIdVsSentCount.getSentCount());
		usageStatus.setClickedCompletedCount(0l);
		usageStatus.setClickedCompletedPercent(0d);
		usageStatus.setOpenStartedCount(0l);
		usageStatus.setOpenStartedPercent(0d);
		return usageStatus;
	}

	public static void processCampaignData(List<CampaignSentCountMessage> campaignIdVsSentCount, Map<Integer, CommunicationUsageStatsMessage> campaignIdToUsageMap) {
		if(CollectionUtils.isEmpty(campaignIdVsSentCount)) {
			return;
		}
		for(CampaignSentCountMessage message : campaignIdVsSentCount) {
			campaignIdToUsageMap.put(message.getCampaignId(), getCampaignUsageStats(message));
		}		
	}

	private static CommunicationUsageStatsMessage getCampaignUsageStats(CampaignSentCountMessage message) {
		if(message==null) {
			return null;
		}
		CommunicationUsageStatsMessage usageStatsMessage = new CommunicationUsageStatsMessage();
		usageStatsMessage.setSentSuccess(message.getSentCount());
		usageStatsMessage.setOpenStartedCount(0l);
		usageStatsMessage.setOpenStartedPercent(0d);
		usageStatsMessage.setClickedCompletedCount(0l);
		usageStatsMessage.setClickedCompletedPercent(0d);
		return usageStatsMessage;
	}
	
	public static void processTemplateLastUsageData(TermsAggregation aggregation, Map<Integer, Long> keyIdToLastUsageMap) {
		List<TermsAggregation.Entry> buckets = aggregation.getBuckets();
		if (CollectionUtils.isNotEmpty(buckets)) {
			for (TermsAggregation.Entry entry : buckets) {
				Integer keyId = Integer.parseInt(entry.getKey());
				TopHitsAggregation agg = entry.getTopHitsAggregation("template_top_hits");
				if (agg != null && agg.getFirstHit(Object.class) != null && agg.getFirstHit(Object.class).sort != null && agg.getFirstHit(Object.class).sort.get(0) != null) {
					Long usg = Long.valueOf(agg.getFirstHit(Object.class).sort.get(0));
					keyIdToLastUsageMap.put(keyId, usg);
				}
			}
		}
	}
	
	public static void processTemplateLastUsageDataV1(Terms aggregation, Map<Integer, Long> keyIdToLastUsageMap) {
		if (CollectionUtils.isNotEmpty(aggregation.getBuckets())) {
			for (Terms.Bucket entry : aggregation.getBuckets()) {
				Integer keyId = Integer.parseInt(entry.getKey().toString());
				TopHits agg = entry.getAggregations().get("template_top_hits");
				if (agg != null && agg.getHits() != null && agg.getHits().getHits() != null && agg.getHits().getHits()[0] != null
						&& agg.getHits().getHits()[0].getSortValues() != null && agg.getHits().getHits()[0].getSortValues()[0] != null) {
					Long usg = Long.valueOf(agg.getHits().getHits()[0].getSortValues()[0].toString());
					keyIdToLastUsageMap.put(keyId, usg);
				}
			}
		}
	}
	
	public static Integer getFailureIdForMessage(String failureReason) {
		if (StringUtils.isBlank(failureReason)) {
			return null;
		}
		for (CampaignFailureReason failureReasonEntity : getAllFailureReasons()) {
			if (Pattern.compile(failureReasonEntity.getMessageRegex(), Pattern.CASE_INSENSITIVE).matcher(failureReason).matches())
				return failureReasonEntity.getFailureId();
		}
		return 0;
	}
	
	private static List<CampaignFailureReason> getAllFailureReasons(){
		return CacheManager.getInstance().getCache(CampaignFailureReasonCache.class).getAllFailureReasons();
	}
	
	/**
	 * status 2 = dnd scheduled
	 * 
	 * @param status
	 * @return
	 */
	public static Integer isDNDScheduled(Integer status) {
		if (status != null && status.intValue() == 2) {
			return 1;
		}
		return null;
	}
	

	public static ReferralLeadsCountRequest getReferralLeadsCountRequest(List<Integer> businessIds, Date startDate,
			Date endDate) {
		ReferralLeadsCountRequest request = new ReferralLeadsCountRequest();
		request.setBusinessIds(businessIds);
		request.setStartDate(startDate);
		request.setEndDate(endDate);
		return request;
	}
	
	public static String getClickReportLabel(Integer rating, String type) {
		switch (type) {
		case "nps":
			return getNpsClickReportLabel(rating);

		case "sentiment":
			return getSentimentClickReportLabel(rating);

		case "star":
			return getStarClickReportLabel(rating);

		default:
			break;
		}
		return null;
	}

	private static String getStarClickReportLabel(Integer rating) {
		switch (rating) {
		case 1:
			return rating + " star";

		case 2:
			return rating + " stars";

		case 3:
			return rating + " stars";

		case 4:
			return rating + " stars";

		case 5:
			return rating + " stars";

		default:
			break;
		}
		return null;
	}

	private static String getSentimentClickReportLabel(Integer rating) {
		switch (rating) {
		case 0:
			return "Neutral";

		case 1:
			return "Positive";

		case -1:
			return "Negative";

		default:
			break;
		}
		return null;
	}

	private static String getNpsClickReportLabel(Integer rating) {
		return String.valueOf(rating);
	}
	
	/**
	 * Process APPOINTMENT FORM Template Aggregation Data
	 * @param aggregation
	 * @param campaignIdToUsageMap
	 */
	public static void processAppointmentFormAggregationData(Terms aggregation , Map<Integer,CommunicationUsageStatsMessage> keyIdToUsageMap) {
		if(CollectionUtils.isNotEmpty(aggregation.getBuckets())) {
			CommunicationUsageStatsMessage usageStatsMessage = null;
			for(Terms.Bucket entry : aggregation.getBuckets()) {
				Integer keyId = Integer.parseInt(entry.getKey().toString());
				 usageStatsMessage = new CommunicationUsageStatsMessage();
				 keyIdToUsageMap.put(keyId, usageStatsMessage);
				 setSentOpenCountV1(usageStatsMessage, entry);
			}
		}
	}
	
	public static void processRRCampaignsUsageSummaryForResellerWidgetGallery(Aggregations aggregations, ResellerWidgetCampaignsUsageSummary campaignsUsageSummary) {
		
		List<Terms.Bucket> bucketList =  (List<Bucket>) ((ParsedLongTerms) aggregations.get("campaigns")).getBuckets();
		
		for (Terms.Bucket bucket : bucketList) {
			populateCampaignUsageInfo(bucket, campaignsUsageSummary);
		}
	}

	private static void populateCampaignUsageInfo(Bucket bucket, ResellerWidgetCampaignsUsageSummary campaignsUsageSummary) {
		CampaignUsageSummary campaignUsageSummary = campaignsUsageSummary.new CampaignUsageSummary();
		campaignUsageSummary.setCampaignId(CoreUtils.getIntegerValueFromLong((Long) bucket.getKey()));
		
		campaignUsageSummary.setSentCount(CoreUtils.getIntegerValueFromLong(bucket.getDocCount()));
		
		ParsedFilter deliveredAgg = (ParsedFilter) bucket.getAggregations().get("delivered_comm_filter");
		campaignUsageSummary.setDeliveredCount(CoreUtils.getIntegerValueFromLong(deliveredAgg.getDocCount()));
		
		ParsedValueCount openCountAgg = (ParsedValueCount) deliveredAgg.getAggregations().get("openCountAgg");
		campaignUsageSummary.setOpenedCount(CoreUtils.getIntegerValueFromLong(openCountAgg.getValue()));
		double openedPercentage = CoreUtils.getPercentile((double)openCountAgg.getValue(), (double)deliveredAgg.getDocCount());
		campaignUsageSummary.setOpenedPercentage(openedPercentage);
		
		ParsedValueCount clickedAgg = (ParsedValueCount) deliveredAgg.getAggregations().get("clickCountAgg");
		campaignUsageSummary.setClickedCount(CoreUtils.getIntegerValueFromLong(clickedAgg.getValue()));
		double clickedPercentage = CoreUtils.getPercentile((double)clickedAgg.getValue(), (double)deliveredAgg.getDocCount());
		campaignUsageSummary.setClickedPercentage(clickedPercentage);
		
		campaignsUsageSummary.getCampaignsUsageSummaryList().add(campaignUsageSummary);
	}

	/**
	 * @param searchResponse
	 * @param resellerCampaignWidgetResponse
	 * This method processes the search response from ES to return the top campaigns
	 */
	public static void processTopRRCampaignForResellerWidgetGallery(SearchResponse searchResponse, ResellerWidgetGalleryCampaignResponse resellerCampaignWidgetResponse) {
		
		List<ResellerCampaignWidgetMessage> resellerCampaignWidgetMessageList = ESUtils.mapESSearchResponse(searchResponse, ResellerCampaignWidgetMessage.class);
		if (CollectionUtils.isEmpty(resellerCampaignWidgetMessageList)) {
			return;
		}
		
		resellerCampaignWidgetMessageList.forEach(resellerCampaignWidgetMessage -> {
			CampaignWidgetResponse campaignWidgetResponse = resellerCampaignWidgetResponse.new CampaignWidgetResponse();
			
			campaignWidgetResponse.setCampaignId(resellerCampaignWidgetMessage.getCampaignId());
			campaignWidgetResponse.setAccountId(resellerCampaignWidgetMessage.getAccountId());
			campaignWidgetResponse.setSentCount(resellerCampaignWidgetMessage.getSentCount());
			campaignWidgetResponse.setDeliveredCount(resellerCampaignWidgetMessage.getDeliveredCount());
			campaignWidgetResponse.setOpenedPercentage(resellerCampaignWidgetMessage.getOpenedPercentage());
			campaignWidgetResponse.setClickedPercentage(resellerCampaignWidgetMessage.getClickedPercentage());
			
			resellerCampaignWidgetResponse.getCampaignList().add(campaignWidgetResponse);
		});
		
	}

}
