package com.birdeye.campaign.report;

import java.util.List;
import java.util.Map;

import com.birdeye.campaign.dto.BusinessTemplateEntity;
import com.birdeye.campaign.dto.BusinessTemplateMessage;
import com.birdeye.campaign.dto.CommunicationUsageStatsMessage;
import com.birdeye.campaign.dto.DoupDownloadResponse;
import com.birdeye.campaign.dto.ReferralSummaryReportResponse;
import com.birdeye.campaign.dto.ReferralUsageSentReportMessage;
import com.birdeye.campaign.dto.ReferralUsageSharedReportDTO;
import com.birdeye.campaign.dto.ReferralUsageSharedReportResponse;
import com.birdeye.campaign.dto.ResellerWidgetCampaignsUsageSummary;
import com.birdeye.campaign.dto.ResellerWidgetGalleryCampaignResponse;
import com.birdeye.campaign.dto.SplitCampaignDTO;
import com.birdeye.campaign.dto.SplitCampaignUsageRequest;
import com.birdeye.campaign.dto.TemplateUsageRequest;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.executor.services.CampaignCallable;
import com.birdeye.campaign.request.CampaignUsageInfo;
import com.birdeye.campaign.request.ReferralReportRequest;
import com.birdeye.campaign.request.ReferralSummaryReportRequest;
import com.birdeye.campaign.response.ExternalReferralSentReportResponse;
import com.birdeye.campaign.response.ExternalReferralSharedReportResponse;
import com.birdeye.referral.response.ReferralUsageSentReportResponse;

/**
 * Service for processing reporting requirements in Campaign
 * <AUTHOR>
 *
 */
public interface ReportService {
	
	
	/**
	 * Get campaignId to communication usage map for specified campaigns
	 * Runs parallel calls for CX  , RR and Survey data from ES index
	 * @param enterpriseId
	 * @param campaignIds
	 * @return
	 */
	public Map<Integer , CommunicationUsageStatsMessage> getCampaignsCommunicationUsageData(int enterpriseId , List<Campaign> campaignIds);

	/**
	 * Callable Worker thread to get communication usage stats for CX campaignIds and enterpriseId
	 * @param enterpriseId
	 * @param campaignIds
	 * @return
	 */
	public CampaignCallable<Boolean> getCxCampaignCommunicationUsage(int enterpriseId, List<Integer> campaignIds , Map<Integer , CommunicationUsageStatsMessage> campaignIdToUsageMap);
	
	/**
	 * Callable Worker thread to get communication usage stats for RR campaignIds and enterpriseId
	 * @param enterpriseId
	 * @param campaignIds
	 * @return
	 */
	public CampaignCallable<Boolean> getRRCampaignCommunicationUsage(int enterpriseId, List<Integer> campaignIds , Map<Integer , CommunicationUsageStatsMessage> campaignIdToUsageMap);
	
	/**
	 * Callable Worker thread to get communication usage stats for Survey campaignIds and enterpriseId
	 * @param enterpriseId
	 * @param campaignIds
	 * @return
	 */
	public CampaignCallable<Boolean> getSurveyCampaignCommunicationUsage(int enterpriseId, List<Integer> campaignIds , Map<Integer , CommunicationUsageStatsMessage> campaignIdToUsageMap);
	
	
	/**
	 * Callable Worker thread to get communication usage stats for CX templateIds and enterpriseId.
	 * 
	 * @param enterpriseId
	 * @param cxTemplateIds
	 * @param templateIdToUsageMap
	 * @return
	 */
	public CampaignCallable<Boolean> getCxTemplateCommunicationUsage(Integer enterpriseId, List<Integer> cxTemplateIds, Map<Integer, CommunicationUsageStatsMessage> templateIdToUsageMap);
	
	/**
	 * Callable Worker thread to get communication usage stats for RR campaignIds and enterpriseId.
	 * 
	 * @param enterpriseId
	 * @param rrTemplateIds
	 * @param templateIdToUsageMap
	 * @return
	 */
	public CampaignCallable<Boolean> getRRTemplateCommunicationUsage(Integer enterpriseId, List<Integer> rrTemplateIds, Map<Integer, CommunicationUsageStatsMessage> templateIdToUsageMap);
	
	/**
	 * Callable Worker thread to get communication usage stats for Survey templateIds and enterpriseId
	 * @param enterpriseId
	 * @param campaignIds
	 * @return
	 */
	public CampaignCallable<Boolean> getSurveyTemplateCommunicationUsage(int enterpriseId, List<Integer> templateIds , Map<Integer , CommunicationUsageStatsMessage> templateIdToUsageMap);

	/**
	 * Get templateId to communication usage map for specified templates
	 * Runs parallel calls for CX  , RR and Survey data from ES index
	 * @param enterpriseId
	 * @param templates
	 * @return
	 */
	public Map<Integer, CommunicationUsageStatsMessage> getTemplatesCommunicationUsageData(Integer enterpriseId, List<TemplateUsageRequest> templates);
	
	public void prepareCampaignStatusReport(Campaign campaign);

	/**
	 * Callable Worker thread to get communication usage stats for Referral templateIds and enterpriseId.
	 * 
	 * @param enterpriseId
	 * @param referralTemplateIds
	 * @param templateIdToUsageMap
	 * @return
	 */
	public CampaignCallable<Boolean> getReferralTemplateCommunicationUsage(Integer enterpriseId, List<Integer> referralTemplateIds, Map<Integer, CommunicationUsageStatsMessage> templateIdToUsageMap);

	/**
	 * Callable Worker thread to get communication usage stats for Referral campaignIds and enterpriseId
	 * @param enterpriseId
	 * @param campaignIds
	 * @return
	 */
	public CampaignCallable<Boolean> getReferralCampaignCommunicationUsage(int enterpriseId, List<Integer> campaignIds, Map<Integer, CommunicationUsageStatsMessage> campaignIdToUsageMap);

	/**
	 * Callable Worker thread to get communication usage stats for Promotional campaignIds and enterpriseId
	 * @param enterpriseId
	 * @param promotionTemplateIds
	 * @param templateIdToUsageMap
	 * @return
	 */
	public CampaignCallable<Boolean> getPromotionTemplateCommunicationUsage(Integer enterpriseId, List<Integer> promotionTemplateIds, Map<Integer, CommunicationUsageStatsMessage> templateIdToUsageMap);
	
	/**
	 * Method to get Promotional campaign usage statistics for enterpriseId and campaign Ids
	 * @param enterpriseId
	 * @param promotionalCampaignIds
	 * @param campaignIdToUsageMap
	 * @return
	 */
	public CampaignCallable<Boolean> getPromotionalCampaignCommunicationUsage(int enterpriseId, List<Integer> promotionalCampaignIds,
			Map<Integer, CommunicationUsageStatsMessage> campaignIdToUsageMap);

	public ReferralUsageSentReportResponse getReferralUsageSentReport(Map<String, Object> requestParams, Integer enterpriseId, ReferralReportRequest referralReportRequest);
	
	public ReferralUsageSharedReportResponse getReferralUsageSharedReport(Map<String, Object> requestParams,
			Integer enterpriseId, ReferralReportRequest referralReportRequest);

	void getTemplateLastUsage(Integer enterpriseId, List<BusinessTemplateMessage> templates,String templateType);
	
	void getSurveyCampaignUsage(int enterpriseId, List<Integer> campaignIds, Map<Integer, CommunicationUsageStatsMessage> campaignIdToUsageMap);
	
	/**
	 * Get Survey campaign unique recipients count, unsubscribe rate, email bounce rate & email spam rate
	 * 
	 */
	public void getSurveyRecipientUsageAndDeliveryStats(Integer enterpriseId, List<Integer> campaignIds, CampaignUsageInfo usageInfo);

	ReferralSummaryReportResponse getReferralSummaryReport(Integer enterpriseId, ReferralSummaryReportRequest request);
	
	DoupDownloadResponse<ReferralUsageSentReportMessage> getReferralDownloadSentReport(Map<String, Object> requestParams, Integer enterpriseId, ReferralReportRequest referralReportRequest);
	
	DoupDownloadResponse<ReferralUsageSharedReportDTO> getReferralDownloadSharedReport(Map<String, Object> requestParams, Integer enterpriseId, ReferralReportRequest referralReportRequest);

	Map<String, Integer> getMostRecentlyUsedTemplate(Integer enterpriseId, List<BusinessTemplateEntity> templates, String templateType);

	Map<Integer, CommunicationUsageStatsMessage> getSplitCampaignsCommunicationUsageData(int enterpriseId, List<SplitCampaignUsageRequest> splitUsageRequestList);

	ResellerWidgetCampaignsUsageSummary getResellerReviewsCampaignsUsageSummary(List<Integer> campaignIds);

	ResellerWidgetGalleryCampaignResponse getTopRRCampaignListForResellerCampaignWidget(List<Integer> campaignIds, String sortBy);
	
	ExternalReferralSentReportResponse generateExternalReferralSentReport(Map<String, Object> requestParams, Integer enterpriseId, ReferralReportRequest referralReportRequest);
	
	ExternalReferralSharedReportResponse generateExternalReferralSharedReport(Map<String, Object> requestParams, Integer enterpriseId, ReferralReportRequest referralReportRequest);
	
}
