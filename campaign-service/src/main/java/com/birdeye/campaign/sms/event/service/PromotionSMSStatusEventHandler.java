package com.birdeye.campaign.sms.event.service;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.communication.UsageCommunicationService;
import com.birdeye.campaign.communication.message.SMSDeliveryStatusRequest;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.entity.Promotion;
import com.birdeye.campaign.platform.constant.CommunicationAcitivityEventEnum;
import com.birdeye.campaign.platform.constant.RequestStatusEnum;
import com.birdeye.campaign.repository.PromotionRepo;
import com.birdeye.campaign.service.CommunicationActivityService;
import com.birdeye.campaign.utils.DateTimeUtils;
import com.birdeye.campaign.utils.TwilioAPIUtils;

@Service("promotionSMSStatusEventHandler")
public class PromotionSMSStatusEventHandler extends AbstractSMSStatusEventHandler {
	
	private static final Logger			logger	= LoggerFactory.getLogger(PromotionSMSStatusEventHandler.class);
	
	@Autowired
	private PromotionRepo				promotionRepo;
	
	@Autowired
	private UsageCommunicationService	usageCommunicationService;
	
	@Autowired
	private CommunicationActivityService communicationActivityService;
	
	@Override
	public void handleEvent(SMSDeliveryStatusRequest request) {
		logger.info("Handling promotion sms delivery status request : {}", request);
		
		Long requestId = Long.parseLong(request.getRequestId());
		String queuedStatus = request.getInternalStatus();
		Promotion promotion = promotionRepo.findFirstById(requestId);
		if (promotion == null) {
			logger.info("eventHandlerForPromotionRequests : Request not found for Id {}", requestId);
			return;
		}
		
		if (StringUtils.equalsIgnoreCase(request.getExternalStatus(), Constants.NEXUS_SUCCESS_ACK_STATUS)) {
			// SUCCESS handler
			handleSuccesEvent(promotion, request.getSentAt());
		} else if ("queued".equalsIgnoreCase(queuedStatus) || StringUtils.equalsIgnoreCase(request.getExternalStatus(), Constants.NEXUS_FAILURE_ACK_STATUS)) {
			// FAILURE and SQS Queue handler
			// 1. id internalStatus=queue then mark it schedule
			// 2. go for failure
			if ("queued".equalsIgnoreCase(queuedStatus)) {
				handleQueuedEvent(promotion, request);
			} else {
				handleFailureEvent(promotion, request);
			}
		}
		
	}
	
	/**
	 * Success Status Event Handler for Promotion Requests
	 * 
	 * @param promotion
	 * @param smsTemplate
	 * @param sms
	 */
	private void handleSuccesEvent(Promotion promotion, long sentAt) {
		logger.info("Handling Promotion SMS delivery status Success event for request id {}", promotion.getId());
		promotion.setDeliveryStatus(RequestStatusEnum.SUCCESS.getName());
		promotion.setSentOn(new Date(sentAt));
		promotion.setFailureReason(null);
		promotionRepo.saveAndFlush(promotion);
		
		usageCommunicationService.pushPromotionSentRequest(promotion, new Date());
		
	}
	
	private void handleQueuedEvent(Promotion promotion, SMSDeliveryStatusRequest request) {
		logger.info("Handling Promotion SMS delivery status Failure event for request id {}", promotion.getId());
		updateStatusInPromotionRequest(promotion, request.getErrorCode(), request.getFailureReason(), RequestStatusEnum.INPROGRESS);
		usageCommunicationService.updateQueuedDeliveryStatusForPromotion(promotion);
	}
	
	private void handleFailureEvent(Promotion promotion, SMSDeliveryStatusRequest request) {
		logger.info("Handling Promotion SMS delivery status Failure event for request id {}", promotion.getId());
		
		updateStatusInPromotionRequest(promotion, request.getErrorCode(), request.getFailureReason(), RequestStatusEnum.FAILURE);
		
		usageCommunicationService.updateDeliveryStatusForPromotion(promotion);
		
		unsubscribeCustomerOnSMSFailure(promotion.getCustId(), request.getErrorCode());
		
		pushEventToMessengerService(promotion.getId(), promotion.getCustId(), promotion.getRequestType(), request);
		
		// publish communication activity
		communicationActivityService.publishCommunicationAcitivity(CommunicationAcitivityEventEnum.FAILURE, promotion, null, null);
				
	}
	
	private void updateStatusInPromotionRequest(Promotion promotion, Integer errorCode, String failureReason, RequestStatusEnum status) {
		promotion.setDeliveryStatus(status.getName());
		if (errorCode != null) {
			failureReason = TwilioAPIUtils.getErrorMessageWithDefaultIfNotFound(errorCode, failureReason);
			promotion.setFailureReason(failureReason);
		} else if (StringUtils.isNotBlank(failureReason)) {
			promotion.setFailureReason(StringUtils.trim(failureReason));
		}
		promotionRepo.saveAndFlush(promotion);
		
	}
	
}
