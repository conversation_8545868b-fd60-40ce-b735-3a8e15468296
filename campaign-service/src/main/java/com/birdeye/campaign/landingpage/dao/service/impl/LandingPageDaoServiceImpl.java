package com.birdeye.campaign.landingpage.dao.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.ai.entity.LandingPage;
import com.birdeye.campaign.dto.BusinessLandingPageEntity;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.landingpage.dao.service.LandingPageDaoService;
import com.birdeye.campaign.repository.LandingPageRepo;

@Service("landingPageDaoServiceImpl")
public class LandingPageDaoServiceImpl implements LandingPageDaoService {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(LandingPageDaoServiceImpl.class);
	
	@Autowired
	private LandingPageRepo landingPageRepo;
	
	/**
	 * This method is used to get the landing page by its ID.
	 *
	 * @param landingPageId the ID of the landing page
	 * @return LandingPage object corresponding to the provided ID
	 */
	@Override
	public LandingPage getLandingPageById(Integer landingPageId) {
		Optional<LandingPage> landingPage = landingPageRepo.findById(landingPageId);
		return landingPage.orElse(null);
	}
	
	@Override
	public LandingPage saveLandingPage(LandingPage landingPageObject) {
		return landingPageRepo.saveAndFlush(landingPageObject);
	}
	
	@Override
	public Long getSameNameCountForLandingPage(String name, Integer accountId) {
		return landingPageRepo.countByNameStartingWithAndAccountIdAndIsDeleted(name, accountId, 0);
	}
	
	@Override
	public Long getNameCountForLandingPageUpdate(String baseName, String oldName, Integer accountId) {
		return landingPageRepo.countByNameStartingWithAndNameNotEqualsAndAccountIdAndIsDeleted(baseName, oldName, accountId, 0);
	}
	
	@Override
	public CachedCollectionWrapper<BusinessLandingPageEntity> getAccountLandingPagesListByAccountId(Integer accountId) {
		if (Objects.isNull(accountId)) {
			LOGGER.warn("Received null value for accountId");
			return new CachedCollectionWrapper<BusinessLandingPageEntity>(new ArrayList<BusinessLandingPageEntity>());
		}
		
		List<BusinessLandingPageEntity> landingPages = landingPageRepo.findAllActiveAccountLandingPagesByAccountId(accountId);
		if (CollectionUtils.isEmpty(landingPages)) {
			landingPages = new ArrayList<BusinessLandingPageEntity>();
		}
		return new CachedCollectionWrapper<BusinessLandingPageEntity>(landingPages);
	}
	
	@Override
	public List<BusinessLandingPageEntity> getLandingPagesByType(Integer accountId, List<String> landingPageType) {
		if (Objects.isNull(accountId)) {
			LOGGER.warn("Received null value for accountId");
			return new ArrayList<BusinessLandingPageEntity>();
		}
		return landingPageRepo.findAllActiveAccountLandingPagesByAccountIdAndType(accountId, landingPageType);
	}
}
