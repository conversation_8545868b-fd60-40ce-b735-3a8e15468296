package com.birdeye.campaign.landingpage.dao.service;

import java.util.List;

import com.birdeye.campaign.ai.entity.LandingPage;
import com.birdeye.campaign.dto.BusinessLandingPageEntity;
import com.birdeye.campaign.dto.CachedCollectionWrapper;

public interface LandingPageDaoService {
	
	LandingPage getLandingPageById(Integer landingPageId);
	
	LandingPage saveLandingPage(LandingPage landingPageObject);
	
	Long getSameNameCountForLandingPage(String name, Integer accountId);
	
	Long getNameCountForLandingPageUpdate(String baseName, String oldName, Integer accountId);
	
	CachedCollectionWrapper<BusinessLandingPageEntity> getAccountLandingPagesListByAccountId(Integer accountId);
	
	List<BusinessLandingPageEntity> getLandingPagesByType(Integer accountId, List<String> landingPageType);
}
