package com.birdeye.campaign.reminder.events;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import com.birdeye.campaign.business.service.BusinessService;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.constant.KafkaTopicTypeEnum;
import com.birdeye.campaign.dto.BusinessCustomHierarchyLocMappingDetails;
import com.birdeye.campaign.dto.BusinessCustomHierarchyLocMappingDetails.BusinessCustomHierarchyLocMapping.BusinessHierarchySubLevelMapping;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.dto.KafkaMessage;
import com.birdeye.campaign.dto.LocationInfo;
import com.birdeye.campaign.dto.ReviewRequestListDTO;
import com.birdeye.campaign.dto.SplitCampaignMappingDTO;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.CampaignCondition;
import com.birdeye.campaign.entity.RecurringReminderEvents;
import com.birdeye.campaign.entity.RecurringReminderEventsAudit;
import com.birdeye.campaign.event.service.CampaignEventService;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.kafka.service.KafkaService;
import com.birdeye.campaign.platform.constant.CampaignTriggerTypeEnum;
import com.birdeye.campaign.recurring.reminder.audit.service.RecurringReminderAuditService;
import com.birdeye.campaign.repository.RecurringReminderEventsRepo;
import com.birdeye.campaign.request.CampaignUpdateEvent;
import com.birdeye.campaign.request.ContactReminderAuditEvent;
import com.birdeye.campaign.request.ContactReminderEventsRequest;
import com.birdeye.campaign.request.core.BusinessDataBatchGetRequest;
import com.birdeye.campaign.response.external.GetBusinessLiteResponse;
import com.birdeye.campaign.service.CacheService;
import com.birdeye.campaign.service.CampaignSetupCachingService;
import com.birdeye.campaign.service.SplitCampaignHelperService;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.workflow.enums.ActivityStatus;

@Service("recurringReminderEventsService")
public class RecurringReminderEventsServiceImpl implements RecurringReminderEventsService {
	
	@Autowired
	private CampaignSetupCachingService	campaignSetupCachingService;
	
	@Autowired
	private BusinessService				businessService;
	
	@Autowired
	private RecurringReminderEventsRepo	recurringReminderEventsRepo;
	
	// @Autowired
	// private RecurringReminderEventsAuditRepo recurringReminderEventsAuditRepo;
	//
	@Autowired
	private KafkaService			kafkaService;
	
	@Autowired
	private CacheService			cacheService;
	
	@Autowired
	private CampaignEventService	campaignEventService;
	
	@Autowired
	RecurringReminderAuditService	recurringReminderAuditService;
	
	@Autowired
	private SplitCampaignHelperService	splitCampaignHelperService;
	
	private static final Logger		logger	= LoggerFactory.getLogger(RecurringReminderEventsServiceImpl.class);
	
	/***
	 * Triggers on campaign crud operations
	 * Create new events for custom reminder Automation
	 * delete if any existing unprocessed records present for the campaign
	 * create an event on the basis of campaign id and timezones of the account
	 * events will be stored in reminder_events table
	 *
	 */
	
	@Override
	public void addNewEvent(CampaignUpdateEvent updateEvent) {
		// fetch active ongoing campaign
		Campaign ongoingCampaign = campaignSetupCachingService.getOngoingCampaignByEnterpriseAndCampaignId(updateEvent.getEnterpriseId(), updateEvent.getCampaignId());
		BusinessEnterpriseEntity business = cacheService.getBusinessById(updateEvent.getEnterpriseId());
		
		if (ongoingCampaign == null || business == null) {
			logger.warn("campaign or business is not available for reminder events {}", updateEvent);
			return;
		}
		
		CampaignCondition campaignCondition = new CampaignCondition(campaignSetupCachingService.getCampaignConditionByCampaign(updateEvent.getCampaignId(), updateEvent.getEnterpriseId()));
		String triggerType = ongoingCampaign.getTriggerType();
		if (StringUtils.equalsIgnoreCase(triggerType, CampaignTriggerTypeEnum.CONTACT_EVENT.getType())) {
			try {
				deleteExistingReccurringEvents(ongoingCampaign, business);
				logger.info("preparing contact event for enterprise id {} and campaign id {}", updateEvent.getEnterpriseId(), updateEvent.getCampaignId());
				Map<String, List<Integer>> timeZoneToBusinessIds = fetchLocationsTimeZoneInfo(business, campaignCondition);
				if (MapUtils.isNotEmpty(timeZoneToBusinessIds)) {
					prepareRecurringEventInfo(updateEvent.getCampaignId(), updateEvent.getEnterpriseId(), timeZoneToBusinessIds, campaignCondition);
				}
				
			} catch (Exception e) {
				logger.warn("exception occurred while preparing contact event for account id {} and campaign id {} {}", updateEvent.getEnterpriseId(), updateEvent.getCampaignId(),
						ExceptionUtils.getStackTrace(e));
			}
		}
		
	}
	
	/**
	 * 
	 * Check if the campaign id provided belongs to split automation.
	 * Fetch mapping campaign ids and delete existing reccurring events for each of them.
	 */
	private void deleteExistingReccurringEvents(Campaign onGoingCampaign, BusinessEnterpriseEntity business) {
		if (BooleanUtils.isTrue(CoreUtils.getBooleanValueFromInteger(onGoingCampaign.getIsSplitCampaign()))) {
			List<SplitCampaignMappingDTO> mappingList = splitCampaignHelperService.validateAndFetchSplitCampaignIdAndMappings(onGoingCampaign.getId(), business.getId());
			if (CollectionUtils.isEmpty(mappingList)) {
				logger.error("deleteExistingReccurringEvents :: Invalid sub campaign id list found while deleting reccurring reminder events for sub campaign id {}", onGoingCampaign.getId());
				throw new CampaignException(ErrorCodes.INVALID_SPLIT_MAPPING, ErrorCodes.INVALID_SPLIT_MAPPING.getMessage());
			}
			
			mappingList.stream().forEach(e -> {
				deleteExistingRecurringEventsForCampaign(e.getCampaignId(), business.getId());
			});
		} else {
			deleteExistingRecurringEventsForCampaign(onGoingCampaign.getId(), business.getId());
		}
	}
	
	/**
	 * 
	 * delete the unprocessed records of the campaign, before adding new event
	 * update campaign_event table if promotion request is scheduled for this campaign
	 * delete  review requests 
	 */
	private void deleteExistingRecurringEventsForCampaign(Integer campaignId, Integer accountId) {
		List<RecurringReminderEvents> existingEvent = recurringReminderEventsRepo.findByCampaignIdAndIsProcessed(campaignId, 0);
		if (CollectionUtils.isNotEmpty(existingEvent)) {
			recurringReminderEventsRepo.deleteAll(existingEvent);
			List<Integer> recurringReminderEventId = existingEvent.stream().map(event -> event.getId()).collect(Collectors.toList());
			recurringReminderAuditService.updateRecurringReminderEventAudit(recurringReminderEventId, ActivityStatus.CANCELLED.getStatus(), Constants.RECURRING_EVENT_AUTOMATION_UPDATED);
		}
		
		// delete review requets
		List<Long> reviewRequestIds = campaignEventService.fetchReviewRequestIdsFromCampaignEvent(campaignId, ActivityStatus.INIT.getStatus(), new Date(0l));
		// cancel all the events of this campaign
		campaignEventService.updateCampaignEventStatusByCampaignId(campaignId, ActivityStatus.INIT.getStatus(), new Date(0l), ActivityStatus.CANCELLED.getStatus());
		sendReviewRequestsToCancellationKafka(campaignId, accountId, reviewRequestIds);
	}
	
	private void sendReviewRequestsToCancellationKafka(Integer campaignId, Integer accountId, List<Long> reviewRequestIds) {
		logger.info("Sending review request ids for cancellation to kafka for campaign id :: {} and rr ids :: {}", campaignId, reviewRequestIds);
		if(CollectionUtils.isEmpty(reviewRequestIds)) {
			return;
		}
		int batchSize = 1000;
		List<List<Long>> rrIds = ListUtils.partition(reviewRequestIds, batchSize);
		List<KafkaMessage> rrKafkaMessages = new ArrayList<>();
		
		for (int batchNumber = 0; batchNumber < rrIds.size(); batchNumber++) {
			rrKafkaMessages.add(new KafkaMessage(new ReviewRequestListDTO(campaignId, accountId, rrIds.get(batchNumber), batchNumber, batchSize)));
		}
		kafkaService.pushMessagesListToKafka(KafkaTopicTypeEnum.RR_EVENTS_DELETE, rrKafkaMessages);
	}
	
	// create a map with key as time zone and value as list of location ids
	private Map<String, List<Integer>> fetchLocationsTimeZoneInfo(BusinessEnterpriseEntity account, CampaignCondition campaignCondition) {
		Integer accountId = account.getId();
		if (CoreUtils.isSMB(account)) {
			Map<String, List<Integer>> smbTimeZoneMap = new HashMap<>();
			smbTimeZoneMap.put(account.getTimezoneId(), Collections.singletonList(account.getId()));
			return smbTimeZoneMap;
		}
		
		// fetch for selected locations
		if (StringUtils.equalsIgnoreCase(campaignCondition.getLvlAlias(), "loc") && CollectionUtils.isNotEmpty(campaignCondition.getLvlIds())) {
			BusinessDataBatchGetRequest request = new BusinessDataBatchGetRequest();
			List<Integer> businessIds = campaignCondition.getLvlIds().stream().map(Integer::parseInt).collect(Collectors.toList());
			logger.info("fetching details of some locations for account id {} locations {} ", accountId, businessIds);
			request.setBusinessIds(businessIds);
			return businessService.getBusinessDataInBatch(request, false).stream()
					.collect(Collectors.groupingBy(GetBusinessLiteResponse::getTimeZone, Collectors.mapping(GetBusinessLiteResponse::getBusinessId, Collectors.toList())));
		} // fetch for all locations
		else if (StringUtils.equalsIgnoreCase(campaignCondition.getLvlAlias(), "loc") && CollectionUtils.isEmpty(campaignCondition.getLvlIds())) {
			try {
				List<LocationInfo> locationDetails = businessService.getLocationsInfo(accountId);
				logger.info("fetching details of all locations for account id {} {} ", accountId, locationDetails);
				return locationDetails.stream().collect(Collectors.groupingBy(LocationInfo::getTimeZone, Collectors.mapping(LocationInfo::getBusinessId, Collectors.toList())));
				
			} catch (Exception e) {
				logger.info("fetching exception details of all locations for account id {} ", accountId);
				e.printStackTrace();
			}
		} // fetch according to selected hierarchy
		else {
			logger.info("fetching details of locations for account id and level alias  {} {} ", accountId, campaignCondition.getLvlAlias());
			return getTimeZoneLocationMapFromBusinessheirarchy(accountId, campaignCondition);
			
		}
		logger.info("fetching exception null exception details of all locations for account id {} ", accountId);
		// return empty map
		return new HashMap<>();
	}
	
	private Map<String, List<Integer>> getTimeZoneLocationMapFromBusinessheirarchy(Integer accountId, CampaignCondition campaignCondition) {
		// fetch business hierarchy to locations info
		BusinessCustomHierarchyLocMappingDetails heirarchyMapping = businessService.getBusinessHeirarchyData(accountId, campaignCondition.getLvlAlias());
		// filter selected hierarchy
		if (CollectionUtils.isNotEmpty(campaignCondition.getLvlIds()) && CollectionUtils.isNotEmpty(heirarchyMapping.getBusinessCustomHierarchyLocMappings())) {
			
			
			List<BusinessHierarchySubLevelMapping> filterdSubLevelALiasBusinesses =  heirarchyMapping.getBusinessCustomHierarchyLocMappings().get(0).getBusinessHierarchySubLevelsLocMappings().stream()
					.filter(mapping -> campaignCondition.getLvlIds().contains(mapping.getSubLevel())).collect(Collectors.toList());
			
			BusinessDataBatchGetRequest request = new BusinessDataBatchGetRequest();
			List<Integer> locationIds = filterdSubLevelALiasBusinesses.stream().flatMap(mapping -> mapping.getLocationIds().stream()).distinct().collect(Collectors.toList());
			request.setBusinessIds(locationIds);
			// fetch locations info from core
			return businessService.getBusinessDataInBatch(request, false).stream()
					.collect(Collectors.groupingBy(GetBusinessLiteResponse::getTimeZone, Collectors.mapping(GetBusinessLiteResponse::getBusinessId, Collectors.toList())));
		}
		return new HashMap<>();
		
	}
	
	/***
	 * cron will run every minute
	 * first ten events will be picked whose schedule time is less than current time
	 * events will be pushed to contacto service
	 * then new events will be added for next dates
	 */
	@Override
	public void pollRecurringReminderEvents(Integer size) {
		Date currentTimeUTC = new Date();
		// add empty list check
		Page<RecurringReminderEvents> reminderEvents = recurringReminderEventsRepo.findByScheduleTimeLessThanAndIsProcessed(currentTimeUTC.getTime(), 0, PageRequest.of(0, size));
		
		logger.info("reminder events {} ", reminderEvents);
		if (reminderEvents.isEmpty()) {
			logger.info("no reminder events are fetched for contact events trigger");
		}
		reminderEvents.getContent().forEach(event -> {
			pushEventToKontacto(event);
			event.setIsProcessed(1);
			prepareNewEvents(event);
		});
		logger.info("reminder events for saving  is {}", reminderEvents);
		recurringReminderEventsRepo.saveAll(reminderEvents);
	}
	
	private void prepareNewEvents(RecurringReminderEvents event) {
		logger.info("creating new reminder event for id {}", event.getId());
		Campaign campaign = campaignSetupCachingService.getCampaignById(event.getCampaignId());
		if(CoreUtils.isTrueForInteger(campaign.getIsDeleted())) {
			return;
		}
		Long newScheduleTime = event.getScheduleTime() + Constants.EPOCHS_IN_A_DAY + Constants.EPOCHS_IN_TWO_HOURS;
		RecurringReminderEvents newEvent = new RecurringReminderEvents();
		newEvent.setAccountId(event.getAccountId());
		newEvent.setCampaignId(event.getCampaignId());
		newEvent.setEventDates(fetchEventDate(newScheduleTime, event.getTimezoneId()));
		newEvent.setLocationIds(event.getLocationIds());
		newEvent.setTimezoneId(event.getTimezoneId());
		newEvent.setScheduleTime(newScheduleTime);
		newEvent.setIsProcessed(0);
		newEvent.setEventType(event.getEventType());
		recurringReminderEventsRepo.saveAndFlush(newEvent);
		recurringReminderAuditService.createRecurringReminderAudit(newEvent.getId(), newEvent.getCampaignId());
		
	}
	
	private void pushEventToKontacto(RecurringReminderEvents event) {
		ContactReminderEventsRequest contactRequest = new ContactReminderEventsRequest();
		contactRequest.setAccountId(event.getAccountId());
		contactRequest.setBusinessIds(Arrays.stream(event.getLocationIds().split(", ")).map(Integer::parseInt).collect(Collectors.toList()));
		contactRequest.setDateList(Arrays.stream(event.getEventDates().split(", ")).collect(Collectors.toList()));
		contactRequest.setEventType(event.getEventType());
		Map<String, Object> metaData = new HashMap<>();
		metaData.put("campaignId", event.getCampaignId());
		metaData.put("reminderEventId", event.getId());
		contactRequest.setMetaData(metaData);
		logger.info("sending campaign reminder event to contact service for campaign id {} {}", event.getCampaignId(), contactRequest);
		kafkaService.pushMessageToKafka(KafkaTopicTypeEnum.CONTACT_EVENT, new KafkaMessage(contactRequest));
	}
	
	public void prepareRecurringEventInfo(Integer campaignId, Integer accountId, Map<String, List<Integer>> timeZoneToBusinessIds, CampaignCondition campaignCondition) {
		// create reminder event for each time zone and location ids
		timeZoneToBusinessIds.forEach((timeZone, businessIds) -> {
			logger.info("preparing reminder event for campaign id {} with location id {} and time zone {}", campaignId, businessIds, timeZone);
			createRecurringReminderEvent(campaignId, accountId, timeZone, campaignCondition, businessIds);
		});
		
	}
	
	private void createRecurringReminderEvent(Integer campaignId, Integer accountId, String timeZone, CampaignCondition campaignCondition, List<Integer> businessIds) {
		Long reminderProcessingJobTimeMillis = getCampaignReminderProcessingJobTime(timeZone, campaignCondition.getAppointmentScheduleInfo().get(0).getSendTime());
		prepareRecurringReminderEvent(campaignId, accountId, timeZone, reminderProcessingJobTimeMillis, businessIds, campaignCondition.getRecurringReminderEventType());
	}
	
	private void prepareRecurringReminderEvent(Integer campaignId, Integer accountId, String timeZone, Long scheduleTimeMillis, List<Integer> locationIds, String reminderEventType) {
		RecurringReminderEvents reminderEvent = new RecurringReminderEvents();
		reminderEvent.setAccountId(accountId);
		reminderEvent.setCampaignId(campaignId);
		reminderEvent.setLocationIds(locationIds.stream().map(Object::toString).collect(Collectors.joining(", ")));
		reminderEvent.setScheduleTime(scheduleTimeMillis);
		reminderEvent.setTimezoneId(timeZone);
		reminderEvent.setIsProcessed(0);
		reminderEvent.setEventDates(fetchEventDate(scheduleTimeMillis, timeZone));
		reminderEvent.setEventType(reminderEventType);
		recurringReminderEventsRepo.saveAndFlush(reminderEvent);
//		addRecurringReminderEventToAudit(reminderEvent.getId(), reminderEvent.getCampaignId());
		recurringReminderAuditService.createRecurringReminderAudit(reminderEvent.getId(), reminderEvent.getCampaignId());
	}
	
	private Long getCampaignReminderProcessingJobTime(String timeZone, String time) {
		LocalTime sendTime = LocalTime.parse(time, DateTimeFormatter.ofPattern("h:mm a"));
		ZonedDateTime campaignSendTimeForToday = ZonedDateTime.now(ZoneId.of(timeZone)).withHour(sendTime.getHour()).withMinute(sendTime.getMinute()).withSecond(0).withNano(0)
				.withZoneSameInstant(ZoneId.of(timeZone));
		ZonedDateTime timezoneCurrentTime = ZonedDateTime.now().withZoneSameInstant(ZoneId.of(timeZone));
		
		if (timezoneCurrentTime.compareTo(campaignSendTimeForToday) >= 0) {
			ZonedDateTime scheduleTimeCampaign = campaignSendTimeForToday.plusDays(1).withHour(0).withMinute(sendTime.getMinute()).withSecond(0).withNano(0).withZoneSameInstant(ZoneId.of("UTC"));
			return scheduleTimeCampaign.toInstant().toEpochMilli();
		} else {
			return campaignSendTimeForToday.withHour(0).withMinute(sendTime.getMinute()).withSecond(0).withNano(0).withZoneSameInstant(ZoneId.of("UTC")).toInstant().toEpochMilli();
		}
		
	}
	
	// prepare date, which will be sent to contact team
	private String fetchEventDate(long epochTimestamp, String timeZoneId) {
		Instant instant = Instant.ofEpochMilli(epochTimestamp);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
		return LocalDateTime.ofInstant(instant, ZoneId.of(timeZoneId)).format(formatter);
	}
	
	/***
	 * audit events received from contact service in table recurring_reminder_events_audit
	 */
	@Override
	public void auditContactEvents(ContactReminderAuditEvent auditEvent) {
		if (auditEvent == null || MapUtils.isEmpty(auditEvent.getMetaData())) {
			logger.info("received inavalid audit events from contacto {}", auditEvent);
			return;
		}
		List<RecurringReminderEventsAudit> eventsAudits = recurringReminderAuditService.findByReminderEventId((Integer) auditEvent.getMetaData().get("reminderEventId"));
		if (CollectionUtils.isEmpty(eventsAudits)) {
			logger.info("no reminder audit found for audit events received from contacto {}", auditEvent);
			return;
		}
		RecurringReminderEventsAudit eventsAudit = eventsAudits.get(0);
		eventsAudit.setFailureReason(auditEvent.getFailureReason());
		if (MapUtils.isNotEmpty(auditEvent.getAuditMap())) {
			eventsAudit.setContactAuditMap(auditEvent.getAuditMap().toString());
		}
		eventsAudit.setStatus(auditEvent.getStatus());
		recurringReminderAuditService.saveRecurringReminderEventsAudit(eventsAudit);
	}
	
}
