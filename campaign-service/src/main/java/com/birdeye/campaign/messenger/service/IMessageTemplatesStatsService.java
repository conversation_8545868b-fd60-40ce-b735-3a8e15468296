/**
 * @file_name IMessageTemplatesStatsService.java
 * @created_date 27 Jan 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.messenger.service;

import com.birdeye.campaign.request.MessengerSendEventRequest;

/**
 * @file_name IMessageTemplatesStatsService.java
 * @created_date 27 Jan 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public interface IMessageTemplatesStatsService {

	/**
	 * @param event
	 */
	void updateMessageTemplateStats(MessengerSendEventRequest event);
	
}
