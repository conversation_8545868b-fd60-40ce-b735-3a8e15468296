package com.birdeye.campaign.rule.utils;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.mvel2.MVEL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.birdeye.campaign.constant.OperandDataType;
import com.birdeye.campaign.constant.Operator;
import com.birdeye.campaign.dto.RuleCondition;
import com.birdeye.campaign.dto.RuleExpression;
import com.birdeye.campaign.dto.RuleTree;
import com.birdeye.campaign.entity.CampaignCondition;
import com.birdeye.campaign.exception.RuleCreationException;
import com.birdeye.campaign.utils.CoreUtils;

/**
 * 
 * <AUTHOR>
 *
 */
public class RuleEngine {

	private static final Logger logger = LoggerFactory.getLogger(RuleEngine.class);

	private RuleEngine() {
	}

	// this is to support the review response existing expression
	public static String buildRuleExpression(RuleTree ruleTree) {
		if (ruleTree == null)
			return "";
		if (ruleTree.getLeft() == null && ruleTree.getRight() == null) {
			validateLeafCondition(ruleTree.getCondition());
			Operator operator = Operator.getOperator(ruleTree.getCondition().getOperator());
			return operator.getMvelExpression(convertToValidIdentifier(ruleTree.getCondition().getId(), ruleTree.getCondition().getOperand()), ruleTree.getCondition().getValue(),
					OperandDataType.getOperatorDataType(ruleTree.getCondition().getType()));
		}
		String leftExpr = buildRuleExpression(ruleTree.getLeft());
		String rightExpr = buildRuleExpression(ruleTree.getRight());
		if (StringUtils.isNotBlank(leftExpr) && StringUtils.isNotBlank(rightExpr)) {
			if (StringUtils.isBlank(ruleTree.getLogicalOptr()))
				throw new RuleCreationException("Invalid rule" + ruleTree.toString());
			Operator operator = Operator.getOperator(ruleTree.getLogicalOptr());
			// for logical we don't need any operandType
			return operator.getMvelExpression(leftExpr, rightExpr, null);
		}
		
		if (StringUtils.isBlank(leftExpr) && StringUtils.isNotBlank(rightExpr)) {
			return rightExpr;
		}
		
		return leftExpr;
	}

	private static void validateLeafCondition(RuleCondition condition) {
		if (StringUtils.isBlank(condition.getOperator()) || StringUtils.isBlank(condition.getOperand()) || Objects.isNull(condition.getValue())) {
			throw new RuleCreationException("RuleMessage: " + condition.toString());
		}
	}

	public static String buildRuleExpression(RuleExpression ruleExpression) {
		if (ruleExpression == null || StringUtils.isBlank(ruleExpression.getExpression()) || ruleExpression.getConditions() == null || ruleExpression.getConditions().isEmpty()) {
			throw new RuleCreationException(MessageFormat.format("Invalid Request ::  {0}  ", ruleExpression));
		}

		// validate input expression
		if (!validateInputExpression(ruleExpression.getExpression())) {
			throw new RuleCreationException(MessageFormat.format("Invalid Expression ::  {0} ", ruleExpression.getExpression()));
		}
		String mvelExpression = ruleExpression.getExpression();
		// replace Logical operators
		mvelExpression = StringUtils.replace(mvelExpression, "AND", " && ");
		mvelExpression = StringUtils.replace(mvelExpression, "OR", " || ");
		mvelExpression = StringUtils.replace(mvelExpression, "and", " && ");
		mvelExpression = StringUtils.replace(mvelExpression, "or", " || ");
		mvelExpression = validateAndGetMvelExpression(mvelExpression, ruleExpression.getConditions());
		// validate final expression
		try {
			MVEL.compileExpression(mvelExpression);
		} catch (Exception exe) {
			throw new RuleCreationException(MessageFormat.format("error occur while validating the expression: {0} and Error :: {1} ", mvelExpression, exe.getMessage()));
		}
		return mvelExpression;

	}

	private static boolean validateInputExpression(String inputExpression) {
		int bracketCount = 0;
		for (int i = 0; i < inputExpression.length(); ++i) {
			char c = inputExpression.charAt(i);
			if (c == '(')
				++bracketCount;
			else if (c == ')') {
				--bracketCount;
				if (bracketCount < 0)
					return false;
			}
		}
		return bracketCount == 0;
	}

	private static String validateAndGetMvelExpression(String expression, Map<String, RuleCondition> conditions) { // NOSONAR
		StringBuilder mvelExpression = new StringBuilder();
		int start = 0;
		while (start < expression.length()) {
			// check for token
			if (expression.charAt(start) >= '0' && expression.charAt(start) <= '9') {
				StringBuilder sb = new StringBuilder();
				// There may be more than one digits in number
				while (start < expression.length() && expression.charAt(start) >= '0' && expression.charAt(start) <= '9') {
					sb.append(expression.charAt(start));
					start++;
				}
				mvelExpression.append(getCondition(sb.toString(), conditions));
			} else {
				mvelExpression.append(expression.charAt(start));
				start++;
			}
		}
		return mvelExpression.toString();
	}

	private static String getCondition(String key, Map<String, RuleCondition> conditions) {
		RuleCondition cond = conditions.get(key);
		if (cond == null) {
			throw new RuleCreationException(MessageFormat.format("condition {0} not found", key));
		}
		Operator operator = Operator.getOperator(cond.getOperator());
		return operator.getMvelExpression(convertToValidIdentifier(cond.getId(), cond.getOperand()), cond.getValue(), OperandDataType.getOperatorDataType(cond.getType()));
	}

	private static String convertToValidIdentifier(Integer conditionId, String operand) {
		if (operand == null || operand.isEmpty()) {
			throw new RuleCreationException(MessageFormat.format("Condition operand is [{0}] for conditionId [{1}]. Which is not valid", operand, conditionId));
		}
		StringBuilder sb = new StringBuilder("$");
		for (char c : operand.toCharArray()) {
			if (!Character.isJavaIdentifierPart(c)) {
				sb.append("_");
			} else {
				sb.append(c);
			}
		}
		return sb.toString();
	}

	public static boolean evalMvelExpression(String expression, final Map<String, Object> tokens) {
		logger.info("evaluating the mvel exp {} with data {}", expression, tokens);
		return MVEL.evalToBoolean(expression, tokens);
	}
	
	public static Map<String, String> getMvelParamsFromConditions(Map<String, RuleCondition> conditions) {
		Map<String, String> paramsAndTypes = new HashMap<>();
		for (RuleCondition condition : conditions.values()) {
			paramsAndTypes.put(CoreUtils.convertToValidIdentifier(condition.getOperand()), condition.getType());
		}
		return paramsAndTypes;
	}
	
	/**
	 * 
	 * BIRD-100846
	 * This function prepares expression condition using existing expression, current condition and expression operator(AND/OR)
	 * 
	 * @param expression,
	 *            currentCondition, expressionOperator
	 * 
	 */
	private static String prepareExpressionCondition(String expression, Integer currentCondition, String expressionOperator) {
		if (StringUtils.isBlank(expression) || Objects.equals(currentCondition, 1)) {
			return String.valueOf(currentCondition);
		}
		return StringUtils.join(expression, StringUtils.SPACE, expressionOperator, StringUtils.SPACE, currentCondition);
	}
	
	/**
	 * 
	 * BIRD-100846
	 * This function does the following
	 * 1. Validates Request
	 * 2. Prepare Rule Expression With the given cfIds and Joining Operator
	 * 
	 * @param condition,
	 *            cfIdList, expressionOperator
	 * 
	 */
	public static RuleExpression extractAndPrepareCFCondition(CampaignCondition condition, List<String> cfNameList, String expressionOperator) {
		if (CollectionUtils.isEmpty(cfNameList) || condition == null || condition.getRuleExpression() == null || StringUtils.isBlank(condition.getRuleExpression().getExpression())
				|| MapUtils.isEmpty(condition.getRuleExpression().getConditions()) || BooleanUtils.isFalse(StringUtils.equalsAny(expressionOperator, "OR", "AND"))) {
			return new RuleExpression(StringUtils.EMPTY, new HashMap<>());
		}
		
		Set<String> cfNameSet = new HashSet<>(cfNameList);
		RuleExpression resultantRuleExpression = new RuleExpression(StringUtils.EMPTY, new HashMap<>());
		int currentConditionNo = 1;
		for (Map.Entry<String, RuleCondition> entry : condition.getRuleExpression().getConditions().entrySet()) {
			RuleCondition ruleCondition = entry.getValue();
			if (ruleCondition != null && cfNameSet.contains(CoreUtils.convertToValidIdentifier(ruleCondition.getOperand()))) {
				resultantRuleExpression.setExpression(prepareExpressionCondition(resultantRuleExpression.getExpression(), currentConditionNo, expressionOperator));
				resultantRuleExpression.getConditions().put(String.valueOf(currentConditionNo),
						new RuleCondition(ruleCondition.getId(), ruleCondition.getOperand(), ruleCondition.getOperator(), ruleCondition.getType(), ruleCondition.getValue()));
				currentConditionNo++;
			}
		}
		return resultantRuleExpression;
	}
	
	/**
	 * 
	 * BIRD-100846
	 * This function does the following
	 * 1. Validates Request
	 * 2. Match condition operator in request with condition
	 * 
	 * @param ruleCondition,
	 *            conditionOperator, key
	 * 
	 */
	public static boolean matchOperatorInRuleExpressionCondition(Map<String, RuleCondition> ruleConditions, String conditionOperator, String key) {
		if (MapUtils.isEmpty(ruleConditions) || StringUtils.isAnyBlank(conditionOperator, key)) {
			return false;
		}
		if (ruleConditions.containsKey(key)) {
			RuleCondition ruleCondition = ruleConditions.get(key);
			return (ruleCondition != null && StringUtils.equalsIgnoreCase(conditionOperator, ruleCondition.getOperator()));
		}
		return false;
	}
	
}
