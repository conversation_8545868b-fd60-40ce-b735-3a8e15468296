#Specify the TTL for caches in milli seconds
#Specify the TTL for caches in seconds (for aerospike)
businessCache1=60
kafkaConfigCache1=3600
defaultReplyToEmailCache1=86400
reviewUrlCache1=3600
campaignByIdCache=3600
campaignConditionCache1=3600
accountAutomationsCache=1800
productNameCache1=86400
#Aggregation source name cache - 30 mins
sourceCache1=1800
#Review Generation source name cache - 30 mins
reviewGenSourceCache1=1800

#10 min
emailTemplateCache1=600
#10 min
brandingEnterpriseCache1=600
#10 min
brandingLocationCache1=600
#10 min
businessHtmlTemplateCache1=600
#10 min
averageRatingAndReviewCountCache1=21600
#10 min
selectedReviewSourcesCache1=600
#10 min
directGoogleReviewFlagCache1=600
#15min
deepLinksForTemplateAndSource=900
#24hrs
defaultOngoingCampaign=86400
# Default Biz Aggregation name cache - 60 mins
bizAggNameCache1=3600

#short deeplink url for sms (sent via messenger) cache
smsShortDeeplinkUrlCache1=3600

#ReviewGen source
enterpriseReviewGenSourceCache1=3600



#Custom Token Cache - 1Hour
customTokenCache=3600

userNameCache=3600

userEmailCache=3600

bulkSmsCampaignCache=3600

customCampaignUrlCache=3600

freeTextCampaignUrlCache=3600

businessCache=3600

publicProfileUrlCache=3600

publicProfileUrlCacheNoRedirect=3600

businessLocationCache=3600

enterpriseBrandingEnterpriseCache=60

rrQuickSendUserCache=86400

#templates list cache
emailTemplatesListCache=1800

smsTemplatesListCache=1800

productFeatureCache=600

businessTimezoneCache=86400

dripCampaignConditionCache=3600

dripCampaignBatchCache=3600

rrDateMigrationOffset=86400

referralSettingDefaultCache=86400

#BusinessInfo By External Reference Id Cache
businessInfoByReferenceCache=86400
getCustomerByIdCache=1800
campaignsBrandingCache=3600
getEncryptedCustomerIdCache=3600
reviewRequestCache=1800
domainBaseURLPrefixCache=3600
campaignReferralSourcesCache=3600
segmentCountMigrationOffset=86400
businessEnterpriseIdCache=7200
rrCustomerCache=10800
customerWithCustomFieldReferralCode=300

businessNumberCache=86400
#Old ES to New ES Data Migration caches.
ESToESDataMigrationOffset=86400
businessLiteCache=3600

referralCodeMigrationOffset=86400
campaignAccountSettingsCache=86400

businessOptionsShowRatingAndCount=3600

surveyEmbeddedFieldCache=600

businessLocationByLongIdCache=3600

businessAccountDetailsCache=600
enterpriseLocationDetailsCache=3600
reviewGenerationSourceCache=86400
selectedReviewSourcesCache2022=300

businessTimezoneIdCache=86400
appointmentReminderAuditCache=864000
businessLiteCacheWithCustomData=3600
placeUrlForBusiness=3600
accountPrivacyPolicyCache=86400
domainForBusinessCache=86400
locationInfoCache=3600
reviewRequestToCheckInDetails=300
qrConfigSlugCache=7200
selectedQRReviewSourcesCache=3600
validBusinessCache=900
enterpriseUserLocationTemplateMapping=900
smsLocationTemplatesListCache=1800
userLocationsAccessForEnterprise=300
appointmentBackfillEventCache=86400
enterpriseCampaignIdCache1=86400
businessMigrationCache=1800
bzAppsIndustryCache=86400
globalTemplateMappingCache=3600
globalTemplatesCache=3600
businessProfileCache=3600
businessHeirarchyData = 3600
defaultTemplateCache = 86400
ongoingActiveCampaignCache = 3600
earliestAppointmentCache = 300
businessPageCache=86400
googleMapUrlCache=86400

splitMappingCache = 3600
splitCampaignCache = 3600
temporaryAccountBlockCache=86400
appointmentSettingsCache=3600
businessStaticToken=900 
userAllCampaignAccessCache=7200
userCampaignAccessCache=7200
userInfoAndLocationAccessCache=7200
campaignDetailsByIdsCache=3600
businessBatchnDetailsByIdsCache=3600
userCache=3600
campaignOwnerCache=7200
splitCampaignOwnerCache=7200
enterpriseUserCache=7200
productFeatureCacheForAsyncReports=36000
conditionCache=1800
customerCacheWithRS=3600
businessBrandingWithResellerInfoCache=14400
userInfoCache=21600
modificationAuditCache=3600
businessFeaturesCache=600
campaignListWithLocInfoCache=1800
splitCampaignListWithLocInfoCache=1800
communicationCategoryCache=600
businessOptionsCache=600
accountTemplatesCache = 86400
emailTemplateCategoryCache=3600
smsTemplateCategoryCache=3600
# Free Trial Account Communication Limits Cache - 24 hours
freeTrialAccountCommLimitsCache=86400


#emailTemplatesAIListCache = 1800
