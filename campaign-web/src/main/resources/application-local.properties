
#platform master data source
spring.datasource.platform.jdbcUrl=**********************************************************************************************************************
spring.datasource.platform.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.platform.username=root
spring.datasource.platform.password=bazaar360
spring.datasource.platform.poolName=platform-Hikari-Pool
spring.datasource.platform.maximumPoolSize=200
spring.datasource.platform.minimumIdle=5
spring.datasource.platform.maxLifetime=2000000
spring.datasource.platform.connectionTimeout=60000
spring.datasource.platform.idleTimeout=30000
spring.datasource.platform.test-while-idle = true
spring.datasource.platform.test-on-borrow = true
spring.datasource.platform.time-between-eviction-runs-millis = 60000
spring.datasource.platform.validation-query = SELECT 1
spring.datasource.platform.time-between-eviction-runs-millis = 60000
spring.datasource.platform.validation-query-timeout = 3
spring.datasource.platform.pool-prepared-statements=true
spring.datasource.platform.max-open-prepared-statements=250
spring.datasource.platform.max-sql-prepared-statements=2048
spring.datasource.platform.use-server-prepared-statements=true
spring.datasource.platform.remove-abandoned = true
spring.datasource.platform.remove-abandoned-timeout = 120

#platform replica data source
spring.datasource.platform.replica.jdbcUrl=**********************************************************************************************************************
spring.datasource.platform.replica.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.platform.replica.username=root
spring.datasource.platform.replica.password=bazaar360
spring.datasource.platform.replica.poolName=platform-replica-Hikari-Pool
spring.datasource.platform.replica.maximumPoolSize=20
spring.datasource.platform.replica.minimumIdle=5
spring.datasource.platform.replica.maxLifetime=2000000
spring.datasource.platform.replica.connectionTimeout=60000
spring.datasource.platform.replica.idleTimeout=30000
spring.datasource.platform.replica.test-while-idle = true
spring.datasource.platform.replica.test-on-borrow = true
spring.datasource.platform.replica.time-between-eviction-runs-millis = 60000
spring.datasource.platform.replica.validation-query = SELECT 1
spring.datasource.platform.replica.time-between-eviction-runs-millis = 60000
spring.datasource.platform.replica.validation-query-timeout = 3
spring.datasource.platform.replica.pool-prepared-statements=true
spring.datasource.platform.replica.max-open-prepared-statements=250
spring.datasource.platform.replica.max-sql-prepared-statements=2048
spring.datasource.platform.replica.use-server-prepared-statements=true
spring.datasource.platform.replica.remove-abandoned = true
spring.datasource.platform.replica.remove-abandoned-timeout = 120

#campaign data source
spring.datasource.campaign.jdbcUrl=****************************************************************************************************************************
spring.datasource.campaign.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.campaign.username=app_campaign_usr
spring.datasource.campaign.password=m9#6KQPv#HEx
spring.datasource.campaign.poolName=campaign-Hikari-Pool
spring.datasource.campaign.maximumPoolSize=10
spring.datasource.campaign.minimumIdle=5
spring.datasource.campaign.maxLifetime=2000000
spring.datasource.campaign.connectionTimeout=60000
spring.datasource.campaign.idleTimeout=30000
spring.datasource.campaign.test-while-idle = true
spring.datasource.campaign.test-on-borrow = true
spring.datasource.campaign.time-between-eviction-runs-millis = 60000
spring.datasource.campaign.validation-query = SELECT 1
spring.datasource.campaign.time-between-eviction-runs-millis = 60000
spring.datasource.campaign.validation-query-timeout = 3
spring.datasource.campaign.pool-prepared-statements=true
spring.datasource.campaign.max-open-prepared-statements=250
spring.datasource.campaign.max-sql-prepared-statements=2048
spring.datasource.campaign.use-server-prepared-statements=true
spring.datasource.campaign.remove-abandoned = true
spring.datasource.campaign.remove-abandoned-timeout = 120

#campaign replica data source
spring.datasource.campaign.replica.jdbcUrl=*********************************************************************************************************************************
spring.datasource.campaign.replica.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.campaign.replica.username=appusrcampaign
spring.datasource.campaign.replica.password=Admin#Cam4302111
spring.datasource.campaign.replica.poolName=campaign-replica-Hikari-Pool
spring.datasource.campaign.replica.maximumPoolSize=10
spring.datasource.campaign.replica.minimumIdle=5
spring.datasource.campaign.replica.maxLifetime=2000000
spring.datasource.campaign.replica.connectionTimeout=60000
spring.datasource.campaign.replica.idleTimeout=30000
spring.datasource.campaign.replica.test-while-idle = true
spring.datasource.campaign.replica.test-on-borrow = true
spring.datasource.campaign.replica.time-between-eviction-runs-millis = 60000
spring.datasource.campaign.replica.validation-query = SELECT 1
spring.datasource.campaign.replica.time-between-eviction-runs-millis = 60000
spring.datasource.campaign.replica.validation-query-timeout = 3
spring.datasource.campaign.replica.pool-prepared-statements=true
spring.datasource.campaign.replica.max-open-prepared-statements=250
spring.datasource.campaign.replica.max-sql-prepared-statements=2048
spring.datasource.campaign.replica.use-server-prepared-statements=true
spring.datasource.campaign.replica.remove-abandoned = true
spring.datasource.campaign.replica.remove-abandoned-timeout = 120

# Spring JPA
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=false
spring.jpa.open-in-view=false
#Query Plan Cache
spring.jpa.properties.hibernate.query.plan_cache_max_size=128
spring.jpa.properties.hibernate.query.plan_parameter_metadata_max_size=64

#batch size for insert/update saveAll
spring.jpa.properties.hibernate.jdbc.batch_size=100

# for sql print 
#debug=true
#logging.level.org.hibernate.SQL=debug
#logging.level.org.hibernate.type.descriptor.sql=trace
# For cache trace
#logging.level.org.springframework.cache=trace
# Server property
server.port=8089
security.basic.enabled=false

spring.jmx.enabled=true
endpoints.jmx.enabled=true

## for pretty printing of json when endpoints accessed over HTTP
http.mappers.jsonPrettyPrint=true

#define character encoding
#spring.mandatory-file-encoding=UTF-8

####### actuator
management.endpoints.web.exposure.include=*
management.security.enabled=false
# basic auth username for actuator endpoints
#security.user.name=campaign
# basic auth password for actuator endpoints
#security.user.password=campaign
# sets health endpoint as non sensitive - can be accesed without auth
endpoints.sensitive=false
endpoints.enabled=true

# Swagger Property
enable.campaign.swagger=true

#Javamelody configuration
enable.campaign.javamelody=true

#kafka configuration
kafka.hostname=**********:9092
kafka.hostname.V2=**********:9092

#Review service endpoint
#review.service.endpoint=http://**************:8080
review.service.endpoint=http://dev-paid-review-2-api.birdeye.internal:8080/

#branding serview endpoint
business.service.endpoint=http://dev-paid-core-business-api-1.birdeye.internal/

#survey service endpoint
survey.new.service.endpoint=http://dev-paid-survey-api-1.birdeye.internal:8080/


# nexus service endpoint
nexus.service.endpoint=http://dev-paid-nexus-api.birdeye.internal:8080

#appointment service endpoint
appointment.service.endpoint=http://dev-paid-appointment-api.birdeye.internal:8080/

bam.service.endpoint=http://dev-paid-bam-api-1.birdeye.internal:8080/

#rest template config
rest.connection.timeout.in.ms=30000
rest.request.timeout.in.ms=80000
rest.socket.timeout.in.ms=80000
rest.max.total.connection=100
rest.max.per.route=100

#kontacto rest template config
kontacto.rest.connection.timeout.in.ms=20000
kontacto.rest.request.timeout.in.ms=10000
kontacto.rest.socket.timeout.in.ms=10000
kontacto.rest.max.total.connection=100
kontacto.rest.max.per.route=100

#platform api endpoint - demo
platform.api.endpoint=http://api.demo9.birdeye.com:8080/


## Redis Config
#spring.cache.type=aerospike
#spring.redis.host=***********
#spring.redis.port=6379
#spring.redis.password=foobird
#spring.redis.database=9
#spring.redis.timeout=10000
#spring.cache.redis.use-key-prefix=true
#
##Jedis Pool
#spring.redis.jedis.pool.max-active=128
#spring.redis.jedis.pool.max-idle=128
#spring.redis.jedis.pool.min-idle=16
#spring.redis.jedis.pool.test-on-borrow=true
#spring.redis.jedis.pool.test-on-return=true
#spring.redis.jedis.pool.test-while-idle=true
#spring.redis.jedis.pool.num-tests-per-eviction-run=3
#spring.redis.jedis.pool.set-block-when-exhausted=true

#Elastic search jest client configurations
#demo http://*************:9200
#prod http://vpc-prod-insight-review-avdq7v4pvq22qg75gfqjeh4qwy.us-west-1.es.amazonaws.com:8443
spring.elasticsearch.jest.uris=http://demoes.birdeye.internal:9200
# new ES 6.8
#spring.elasticsearch.jest.uris=http://***********:5601:9200
spring.elasticsearch.jest.connection-timeout=5000
spring.elasticsearch.jest.read-timeout=20000
spring.elasticsearch.jest.multi-threaded=true

#spring.elasticsearch.host=http://demoes.birdeye.internal:9200
aws.es.host=https://demo-es.birdeye.com
aws.es.service.name=es
aws.region=us-east-1


aws.es.host.C3=https://demo-es.birdeye.com
aws.es.service.name.C3=es
aws.region.C3=us-east-1

spring.freemarker.cache=false

aerospike.hosts=demo-aerospike.birdeye.internal:3000
aerospike.namespace=campaigns

genAI_api_base_url=http://dev-gen-ai.birdeye.internal:8080/

#true - enable aerospike cache
#false - enable no op cache
#spring.cache.type=none
aerospike.cache.enabled=true

#fb_app_id
fb.app.id=153234334813069
### health check disable 
management.health.elasticsearch.enabled=false

amazon.S3.client.enabled=true




#AWS-SM
aws.secret.enabled=false