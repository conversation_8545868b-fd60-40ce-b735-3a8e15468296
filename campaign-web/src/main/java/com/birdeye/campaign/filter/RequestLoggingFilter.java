package com.birdeye.campaign.filter;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.web.servlet.HandlerMapping;

import com.birdeye.campaign.constant.Constants;

import net.logstash.logback.marker.LogstashMarker;
import net.logstash.logback.marker.Markers;

/*
 * Simple request tracing filter.
 * CommonsRequestLoggingFilter is verbose and requires request to be cached.
 */
@WebFilter("/*")
public class RequestLoggingFilter implements Filter {
	
	private static final Logger	LOGGER				= LoggerFactory.getLogger("jsonLogger");
	
	private static final String	REQUEST_ID			= "requestId";
	private static final String	URI					= "uri";
	private static final String	HTTP_METHOD			= "httpMethod";
	private static final String	QUERY				= "queryParams";
	private static final String	PATH_PARAMS			= "pathParams";
	private static final String	CLIENT_IP			= "clientIp";
	private static final String	STATUS				= "httpStatus";
	private static final String	RESPONSE_TIME		= "responseTimeMillis";
	private static final String	ACCOUNT_ID			= "accountId";
	private static final String	USER_ID				= "userId";
	private static final String	API_KEY				= "apiKey";
	private static final String	ARMOR_REQUEST_ID	= "armor-request-id";
	
	public static final String	HEADER_USER_ID		= "uid";
	public static final String	HEADER_ACCOUNT_ID	= "x-account-id";
	public static final String	HEADER_API_KEY		= "api-key";
	
	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
		// empty
	}
	
	@Override
	public void doFilter(ServletRequest req, ServletResponse resp, FilterChain chain) throws IOException, ServletException {
		long time = System.currentTimeMillis();
		
		HttpServletRequest request = (HttpServletRequest) req;
		HttpServletResponse response = (HttpServletResponse) resp;
		if (req != null && request.getHeaderNames() != null) {
			String armorRequestId = request.getHeader(RequestLoggingFilter.ARMOR_REQUEST_ID);
			if (StringUtils.isNotEmpty(armorRequestId)) {
				MDC.put(REQUEST_ID, armorRequestId);
			} else {
				MDC.put(REQUEST_ID, UUID.randomUUID().toString());
			}
			
			MDC.put(Constants.BUSINESS_ID, request.getHeader("bid"));
			MDC.put(Constants.USER_ID, request.getHeader("uid"));
			MDC.put(Constants.EMAIL_ID, request.getHeader("emailId"));
		} else {
			MDC.put(REQUEST_ID, UUID.randomUUID().toString());
		}
		try {
			chain.doFilter(req, resp);
		} finally {
			time = System.currentTimeMillis() - time;
			
			LOGGER.info(requestWrapper(request, response, time), "Url:{}, time:{} ms, status:{}", request.getRequestURI(), time, ((HttpServletResponse) resp).getStatus());
			MDC.clear();
		}
	}
	
	@Override
	public void destroy() {
		// empty
	}
	
	private String getClientIP(final HttpServletRequest request) {
		final String clientIP;
		if (request.getHeader("X-Forwarded-For") != null) {
			clientIP = request.getHeader("X-Forwarded-For").split(",")[0];
		} else {
			clientIP = request.getRemoteAddr();
		}
		return clientIP;
	}
	
	@SuppressWarnings("unchecked")
	private LogstashMarker requestWrapper(final HttpServletRequest httpServletRequest, final HttpServletResponse httpServletResponse, long time) {
		LogstashMarker marker = Markers.append(HTTP_METHOD, httpServletRequest.getMethod());
		marker.and(Markers.append(URI, (String) httpServletRequest.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE))).and(Markers.append(CLIENT_IP, getClientIP(httpServletRequest)))
				.and(Markers.append(STATUS, httpServletResponse.getStatus())).and(Markers.append(RESPONSE_TIME, time)).and(Markers.append(QUERY, getQueryMap(httpServletRequest.getQueryString())))
				.and(Markers.append(PATH_PARAMS, (Map<String, Object>) httpServletRequest.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE)))
				.and(Markers.append(ACCOUNT_ID, httpServletRequest.getHeader(HEADER_ACCOUNT_ID))).and(Markers.append(USER_ID, httpServletRequest.getHeader(HEADER_USER_ID)))
				.and(Markers.append(API_KEY, httpServletRequest.getHeader(HEADER_API_KEY)));
		
		return marker;
	}
	
	private Map<String, String> getQueryMap(String query) {
		if (StringUtils.isNotBlank(query)) {
			Map<String, String> queryParamsMap = new HashMap<>();
			String[] queryParams = query.split("&");
			for (String param : queryParams) {
				String[] keyValuePair = param.split("=");
				queryParamsMap.put(keyValuePair[0], keyValuePair[1]);
			}
			return queryParamsMap;
		}
		return null;
		
	}
	
}