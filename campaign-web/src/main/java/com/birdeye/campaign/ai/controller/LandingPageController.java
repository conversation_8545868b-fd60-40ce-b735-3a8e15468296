package com.birdeye.campaign.ai.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.campaign.ai.request.LandingPageFilters;
import com.birdeye.campaign.ai.request.LandingPageListingFilter;
import com.birdeye.campaign.ai.request.LandingPageSaveRequest;
import com.birdeye.campaign.ai.response.BusinessAllLandingPagesResponse;
import com.birdeye.campaign.ai.response.GetLandingPageResponse;
import com.birdeye.campaign.ai.response.LandingPageSaveResponse;
import com.birdeye.campaign.aspect.annotation.Profiled;
import com.birdeye.campaign.landingpage.service.ILandingPageAIService;

@RestController
@RequestMapping("campaign/v1/landing-pages")
public class LandingPageController {
	
	@Autowired
	private ILandingPageAIService landingPageAIService;
	
	private static final Logger LOGGER = LoggerFactory.getLogger(LandingPageController.class);
	
	/**
	 * Endpoint to get a landing page based on type and landingPageId.
	 *
	 * @param type the type of the landing page
	 * @param landingPageId the ID of the landing page
	 * @param userId the ID of the user making the request
	 * @param accountId the ID of the account
	 * @return ResponseEntity containing the landing page
	 */
	@GetMapping(path = "/{type}/{landingPageId}")
	public ResponseEntity<GetLandingPageResponse> getLandingPage(@PathVariable(name = "type") String type, 
			@PathVariable(name = "landingPageId") Integer landingPageId,
			@RequestHeader(name = "user-id") Integer userId, 
			@RequestHeader(name = "account-id") Integer accountId) {
		LOGGER.info("Request received to fetch landing page for type: {}, landingPageId: {}, userId: {}, accountId: {}", type, landingPageId, userId, accountId);
		return ResponseEntity.ok(landingPageAIService.getLandingPage(type, landingPageId, userId, accountId));
	}

	
	/**
	 * Endpoint to update an existing landing page.
	 *
	 * @param type the type of the landing page
	 * @param landingPageId the ID of the landing page to update
	 * @param userId the ID of the user making the request
	 * @param accountId the ID of the account
	 * @param request the landing page update request
	 * @return ResponseEntity containing the updated landing page details
	 */
	@PostMapping(path = "save/{type}/{landingPageId}")
	public ResponseEntity<LandingPageSaveResponse> saveLandingPage(@PathVariable(name = "type") String type,
			@PathVariable(name = "landingPageId") Integer landingPageId,
			@RequestHeader(name = "user-id") String userId,
			@RequestHeader(name = "account-id") String accountId,
			@RequestBody LandingPageSaveRequest request) {
		LOGGER.info("Request received to save landing page for type: {}, landingPageId: {}, userId: {}, accountId: {}", type, landingPageId, userId, accountId);
		return ResponseEntity.ok(landingPageAIService.saveLandingPage(userId, accountId, type, landingPageId, request));
	}
	
	/**
	 * API to fetch all landing pages based on input filters.
	 * Used for landing pages listing in 'Landing Pages' tab.
	 *
	 * @param accountId the ID of the account
	 * @param userId the ID of the logged-in user
	 * @param channel the channel (email, sms, etc.)
	 * @param landingPageFilters the input filters for fetching landing pages
	 * @return ResponseEntity containing the filtered landing pages
	 */
	@PostMapping(path = "/list")
	public ResponseEntity<BusinessAllLandingPagesResponse> getAllLandingPagesByFilters(@RequestHeader(value = "account-id") Integer accountId,
			@RequestHeader(value = "user-id") Integer userId, 
			@RequestParam(value = "channel", required = false, defaultValue = "email") String channel,
			@RequestBody LandingPageFilters landingPageFilters) {
		LOGGER.info("Request received to fetch all landing pages for accountId: {} and channel: {}", accountId, channel);
		BusinessAllLandingPagesResponse businessAllLandingPagesResponse = landingPageAIService.getAllLandingPagesByFilters(accountId, userId, channel, landingPageFilters);
		return ResponseEntity.ok(businessAllLandingPagesResponse);
	}
	
	/**
	 * API to fetch all landing pages by type.
	 *
	 * @param accountId the ID of the account
	 * @param userId the ID of the logged-in user
	 * @param landingPageFilters the input filters for fetching landing pages
	 * @return ResponseEntity containing the landing pages by type
	 */
	@PostMapping(path = "/list-by-type")
	@Profiled
	public ResponseEntity<BusinessAllLandingPagesResponse> getAllLandingPagesByType(@RequestHeader("account-id") Integer accountId, 
			@RequestHeader("user-id") Integer userId,
			@RequestBody LandingPageListingFilter landingPageFilters) {
		LOGGER.info("Request received to fetch all landing pages for accountId: {} and landingPageTypes: {}", accountId, landingPageFilters.getLandingPageTypes());
		BusinessAllLandingPagesResponse businessAllLandingPagesResponse = landingPageAIService.getAllLandingPagesByType(accountId, userId, landingPageFilters);
		return ResponseEntity.ok(businessAllLandingPagesResponse);
	}
	
	/**
	 * Endpoint to delete a landing page.
	 *
	 * @param landingPageId the ID of the landing page to delete
	 * @param accountId the ID of the account
	 * @param userId the ID of the user making the request
	 * @param channel the channel (email, sms, etc.)
	 * @return ResponseEntity containing the deletion status
	 */
	@DeleteMapping(path = "/{landingPageId}")
	public ResponseEntity<Boolean> deleteLandingPage(@PathVariable(name = "landingPageId") Integer landingPageId,
			@RequestHeader(name = "account-id") Integer accountId,
			@RequestHeader(name = "user-id") Integer userId,
			@RequestParam(value = "channel", required = false, defaultValue = "email") String channel) {
		LOGGER.info("Request received to delete landing page for landingPageId: {}, accountId: {}, userId: {}, channel: {}", landingPageId, accountId, userId, channel);
		Boolean isDeleted = landingPageAIService.deleteLandingPage(landingPageId, channel, accountId, userId);
		return ResponseEntity.ok(isDeleted);
	}
}
