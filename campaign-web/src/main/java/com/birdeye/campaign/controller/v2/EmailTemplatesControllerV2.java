/**
 * @file_name EmailTemplatesControllerV2.java
 * @created_date 13 Feb 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.controller.v2;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.request.GenAIPhishingDetectionRequest;
import com.birdeye.campaign.response.template.v2.EditTemplateResponse;
import com.birdeye.campaign.response.template.v2.EmailTemplateResponse;
import com.birdeye.campaign.template.email.v2.IEmailTemplateService;

import io.swagger.annotations.ApiParam;

/**
 * @file_name EmailTemplatesControllerV2.java
 * @created_date 13 Feb 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@RestController
@RequestMapping("/campaign/templates/v2/email")
public class EmailTemplatesControllerV2 {
	
	@Autowired
	private IEmailTemplateService	emailTemplateService;
	
	private static final Logger		LOGGER	= LoggerFactory.getLogger(EmailTemplatesControllerV2.class);
	
	@GetMapping("/{type}/{templateId}")
	public ResponseEntity<EmailTemplateResponse> getEmailTemplate(@ApiParam(value = "ID of user table") @RequestHeader(value = "uid") String uid,
			@ApiParam(value = "ID of business table") @RequestHeader(value = "bid") String bid,
			@ApiParam(value = "0 for new template, email_template id for old") @PathVariable("templateId") Integer templateId,
			@ApiParam(allowableValues = Constants.EMAIL_TEMPLATE_TYPES) @PathVariable("type") String type) {
		LOGGER.info("Fetching template details for business id {} and type {} and template id {}", bid, type, templateId);
		EmailTemplateResponse response = emailTemplateService.getEmailTemplateResponseById(templateId, type);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
	@PostMapping("/{type}/{templateId}")
	public ResponseEntity<EditTemplateResponse> saveOrUpdateRRTemplate(@ApiParam(value = "ID of user table") @RequestHeader(value = "uid") String uid,
			@ApiParam(value = "ID of business table") @RequestHeader(value = "bid") String bid,
			@ApiParam(value = "0 for new template, email_template id for old") @PathVariable("templateId") Integer templateId,
			@ApiParam(allowableValues = Constants.EMAIL_TEMPLATE_TYPES) @PathVariable("type") String type, @RequestBody EmailTemplateResponse request) {
		EditTemplateResponse response = emailTemplateService.createOrUpdateBusinessEmailTemplate(templateId, Integer.parseInt(bid), type, request);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
	@PostMapping("/reseller/{resellerId}/{type}")
	public ResponseEntity<EditTemplateResponse> saveOrUpdateResellerTemplate(@ApiParam(allowableValues = Constants.EMAIL_TEMPLATE_TYPES) @PathVariable("type") String type,
			@PathVariable("resellerId") Integer resellerId, @RequestBody EmailTemplateResponse request) {
		EditTemplateResponse response = emailTemplateService.createOrUpdateResellerEmailTemplate(type, request, resellerId);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
	/**
	 * 
	 * API to detect phishing contents in bulk for email templates
	 * 
	 * @param emailTemplateIds
	 * @return
	 */
	@PostMapping("/bulk/detect-phishing")
	public ResponseEntity<Void> detectBulkPhishingTemplates(@RequestBody List<Integer> emailTemplateIds) {
		emailTemplateService.bulkDetectPhishingTemplates(emailTemplateIds);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * 
	 * Internal API to get genAI payload for a template id
	 * 
	 * @param emailTemplateIds
	 * @return
	 */
	@GetMapping("/genai-payload/{templateId}")
	public ResponseEntity<GenAIPhishingDetectionRequest> getGenAIPayload(@PathVariable("templateId") Integer templateId) {
		return new ResponseEntity<>(emailTemplateService.getGenAIPayload(templateId), HttpStatus.OK);
	}
	
	@GetMapping("/free-trial/{type}/{templateId}")
	public ResponseEntity<EmailTemplateResponse> getFreeTrialEmailTemplate(
			@ApiParam(value = "ID of business table") @RequestHeader(value = "accountId") String accountId, @RequestHeader(value = "bid", required = false) String bid,
			@ApiParam(value = "0 for new template, email_template id for old") @PathVariable("templateId") Integer templateId,
			@ApiParam(allowableValues = Constants.EMAIL_TEMPLATE_TYPES) @PathVariable("type") String type) {
		LOGGER.info("Fetching template details for business id {} and type {} and template id {}", accountId, type, templateId);
		
		EmailTemplateResponse response = emailTemplateService.getFreeTrialEmailTemplate(Integer.valueOf(accountId), type);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}	
	
}
