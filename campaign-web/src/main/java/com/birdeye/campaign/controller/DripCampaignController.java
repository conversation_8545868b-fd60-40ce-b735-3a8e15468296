package com.birdeye.campaign.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.campaign.request.TestDripCampaignRequest;
import com.birdeye.campaign.response.TestDripCampaignResponse;
import com.birdeye.campaign.service.DripCampaignExecutionService;
import com.birdeye.campaign.service.TestDripCampaignService;

/**
 * Consumer Apis for Drip Campaign Events
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/v1/drip/campaign")
public class DripCampaignController {
	
	private static final Logger				logger	= LoggerFactory.getLogger(DripCampaignController.class);
	
	@Autowired
	private DripCampaignExecutionService	dripCampaignExecutionService;
	
	@Autowired
	private TestDripCampaignService			testDripCampaignService;
	
	/**
	 * create drip campaign batches, push them to poller to execute at a specified time, schedule next same job at the poller to create further batches
	 * for next valid day of execution
	 * 
	 * @param campaignId
	 * @throws Exception
	 */
	@PostMapping("/batch/create/{campaign_id}")
	public void createDripCampaignBatch(@PathVariable("campaign_id") Integer campaignId) {
		logger.info("request to create drip campaign batch for drip campaign id {}", campaignId);
		dripCampaignExecutionService.createDripCampaignBatch(campaignId);
	}
	
	/**
	 * execute drip campaign batch (send campaign to all the customers of the batch)
	 * 
	 * @param bathcId
	 * @throws Exception
	 */
	@PostMapping("/batch/run/{batch_id}")
	public void runDripCampaignBatch(@PathVariable("batch_id") Integer batchId) {
		logger.info("request to run drip campaign batch id {}", batchId);
		dripCampaignExecutionService.runDripCampaignBatch(batchId);
		
	}
	
	/**
	 * test execute drip campaign - generate the date wise distribution of drip batches
	 * 
	 * @param bathcId
	 * @throws Exception
	 */
	@PostMapping("/test/run")
	public TestDripCampaignResponse testDripCampaign(@RequestBody TestDripCampaignRequest testDripCampaignRequest) {
		logger.info("request to test run drip campaign for request data", testDripCampaignRequest);
		return testDripCampaignService.testDripCampaign(testDripCampaignRequest);
	}
}
