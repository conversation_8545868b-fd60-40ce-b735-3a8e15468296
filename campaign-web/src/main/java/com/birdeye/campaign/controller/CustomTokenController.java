package com.birdeye.campaign.controller;

import org.slf4j.Logger;

import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.campaign.request.CustomTokenInsertRequest;
import com.birdeye.campaign.service.CustomTokenService;

@RestController
@RequestMapping("/v1/custom/token")
public class CustomTokenController {
	
	private static final Logger	logger	= LoggerFactory.getLogger(CustomTokenController.class);
	
	@Autowired
	private CustomTokenService	customTokenService;
	
	@PostMapping(value = "/insert")
	public void insertCustomTokens(@RequestBody CustomTokenInsertRequest request) {
		logger.info("received request to insert custom token for business {}", request.getBusinessId());
		customTokenService.insertCustomTokens(request.getTokenMap(), request.getBusinessId());
	}
}
