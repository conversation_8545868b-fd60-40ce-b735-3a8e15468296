package com.birdeye.campaign.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.birdeye.campaign.aspect.annotation.Profiled;
import com.birdeye.campaign.commmunication.consumer.UsageCommunicationConsumerService;
import com.birdeye.campaign.communication.message.DefaultReviewSourceRequest;
import com.birdeye.campaign.communication.message.DefaultReviewSourceUpdateRequest;
import com.birdeye.campaign.communication.message.EventMigrationRequest;
import com.birdeye.campaign.data.migration.DataMigrationService;
import com.birdeye.campaign.dto.AutomationCampaignMigrationResponse;
import com.birdeye.campaign.request.AccessSettingsUpdateRequest;
import com.birdeye.campaign.request.BulkDTUpdateExecuteRequest;
import com.birdeye.campaign.request.BulkDTUpdatePrepRequest;
import com.birdeye.campaign.request.BusinessMigrationEventRequest;
import com.birdeye.campaign.request.DTUpdateExecuteRequest;
import com.birdeye.campaign.request.template.EnterpriseTemplatesMigrationBasicRO;
import com.birdeye.campaign.request.template.EnterpriseTemplatesMigrationRO;
import com.birdeye.campaign.request.template.MigrationRequest;
import com.birdeye.campaign.request.template.OngoingCampaignByLocationRequest;
import com.birdeye.campaign.response.external.DefaultReviewSourceResponse;
import com.birdeye.campaign.service.DefaultTemplateMigrationService;
import com.birdeye.campaign.workbook.service.WorkbookUtil;
import com.birdeye.campaign.workbook.service.WorkbookUtil.RowMapper;

import io.swagger.annotations.ApiParam;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/data/migration")
public class DataMigrationController {
	
	private static final Logger					LOGGER	= LoggerFactory.getLogger(DataMigrationController.class);
	
	@Autowired
	private DataMigrationService				dataMigrationService;
	
	@Autowired
	private UsageCommunicationConsumerService	communicationConsumerService;
	
	@Autowired
	private DefaultTemplateMigrationService     defaultTemplateMigrationService;
	
	@PostMapping("/migrateCampaignData")
	public ResponseEntity<String> migrateCampaignData(@RequestBody BusinessMigrationEventRequest request) {
		LOGGER.info("Migrate Campaign Data request {}", request);
		dataMigrationService.migrateCampaignDataFromOneBusinessToAnotherBusiness(request);
		try {
			return new ResponseEntity<>(HttpStatus.OK);
		} catch (Exception e) {
			LOGGER.error("Exception occured while migrating Campaign Data for request {}", request);
			return new ResponseEntity<>(HttpStatus.EXPECTATION_FAILED);
		}
		
	}
	
	@PostMapping(value = "/enterprise/cx/migrateDataToES")
	public ResponseEntity<String> migrateCXDataForEnterprise(@RequestBody EventMigrationRequest request) {
		LOGGER.info("Migrate CX Data for enterpriseIds : {}", request.getEnterpriseIds());
		try {
			communicationConsumerService.migrateCXEnterpriseDataToCommunicationIndex(request.getEnterpriseIds());
			return new ResponseEntity<>(HttpStatus.OK);
		} catch (Exception e) {
			LOGGER.error("Exception occured while migrating CX Data for enterpriseIds : {}", request.getEnterpriseIds());
			return new ResponseEntity<>(HttpStatus.EXPECTATION_FAILED);
		}
		
	}
	
	@PostMapping(value = "/business/cx/migrateCXDataToES")
	public ResponseEntity<String> migrateCXDataForBusiness(@RequestBody EventMigrationRequest request) {
		LOGGER.info("Migrate CX Data for locationIds : {}", request.getLocationIds());
		try {
			communicationConsumerService.migrateCXBusinessDataToCommunicationIndex(request.getLocationIds());
			return new ResponseEntity<>(HttpStatus.OK);
		} catch (Exception e) {
			LOGGER.error("Exception occured while migrating CX Data for locationIds : {}", request.getLocationIds());
			return new ResponseEntity<>(HttpStatus.EXPECTATION_FAILED);
		}
		
	}
	
	@PostMapping(value = "/requests/cx/migrateCXDataToES")
	public ResponseEntity<String> migrateCXDataForRequestIds(@RequestBody EventMigrationRequest request) {
		LOGGER.info("Migrate CX Data for request ids : {}", request.getReviewRequestIds());
		try {
			communicationConsumerService.migrateCXRequestDataToCommunicationIndex(request.getReviewRequestIds());
			return new ResponseEntity<>(HttpStatus.OK);
		} catch (Exception e) {
			LOGGER.error("Exception occured while migrating CX Data for request ids : {}", request.getEnterpriseIds());
			return new ResponseEntity<>(HttpStatus.EXPECTATION_FAILED);
		}
	}
	
	/**
	 * api to migrate promotional Campaign data to ES for enterprise
	 * 
	 * @param request
	 * @return
	 */
	@PostMapping(value = "/enterprise/promotion/migrateDataToES")
	public ResponseEntity<String> migratePromotionDataForEnterprise(@RequestBody EventMigrationRequest request) {
		LOGGER.info("Request Received to migrate promotion campaign Data to ES for enterpriseIds{}", request.getEnterpriseIds());
		communicationConsumerService.migratePromotionEnterpriseDataToCommunicationIndex(request);
		LOGGER.info("Promotion campaign Data successfully migrated to ES for enterpriseIds{}", request.getEnterpriseIds());
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@PostMapping(value = "/location/promotion/migrateDataToES")
	public ResponseEntity<String> migratePromotionDataForLocation(@RequestBody EventMigrationRequest request) {
		LOGGER.info("Request Received to migrate promotion campaign Data to ES for locationIds{}", request.getLocationIds());
		communicationConsumerService.migratePromotionLocationDataToCommunicationIndex(request);
		LOGGER.info("Promotion campaign Data successfully migrated to ES for locationIds{}", request.getLocationIds());
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@PostMapping(value = "/requests/promotion/migrateDataToES")
	public ResponseEntity<String> migratePromotionDataForRequestIds(@RequestBody EventMigrationRequest request) {
		communicationConsumerService.migratePromotionRequestDataToEs(request);
		LOGGER.info("Promotion campaign Data successfully migrated to ES");
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * ########### Pass Parent request ids only. Don't pass reminder request ids. ###########
	 * @param request
	 * @return
	 */
	@PostMapping(value = "/requests/rr/migrateURDataToES")
	public ResponseEntity<String> migrateUsagesReportDataToES(@RequestBody EventMigrationRequest request) {
		LOGGER.info("Migrate RR Data for request ids : {}", request.getReviewRequestIds());
		try {
			communicationConsumerService.migrateUsagesReportDataToES(request);
			return new ResponseEntity<>(HttpStatus.OK);
		} catch (Exception e) {
			LOGGER.error("Exception occured while migrating UR Data for request ids : {}", request.getReviewRequestIds());
			return new ResponseEntity<>(HttpStatus.EXPECTATION_FAILED);
		}
		
	}
	
	@PostMapping(value = "/enterprise/rr/migrateURDataToES")
	public ResponseEntity<String> migrateEnterpriseUsagesReportDataToES(@RequestBody EventMigrationRequest request) {
		LOGGER.info("Migrate RR Data for enterpriseIds : {}", request.getEnterpriseIds());
		try {
			communicationConsumerService.migrateEnterpriseUsageReportDataToES(request);
			return new ResponseEntity<>(HttpStatus.OK);
		} catch (Exception e) {
			LOGGER.error("Exception occured while migrating UR Data for enterpriseIds : {}", request.getEnterpriseIds());
			return new ResponseEntity<>(HttpStatus.EXPECTATION_FAILED);
		}
	}
	
	@PostMapping(value = "/business/rr/migrateURDataToES")
	public ResponseEntity<String> migrateBusinessUsagesReportDataToES(@RequestBody EventMigrationRequest request) {
		LOGGER.info("Migrate RR Data for businessIds : {}", request.getLocationIds());
		try {
			communicationConsumerService.migrateBusinessUsageReportDataToES(request);
			return new ResponseEntity<>(HttpStatus.OK);
		} catch (Exception e) {
			LOGGER.error("Exception occured while migrating UR Data for enterpriseIds : {}", request.getLocationIds());
			return new ResponseEntity<>(HttpStatus.EXPECTATION_FAILED);
		}
		
	}
	
	/**
	 * API to migrate campaign/template data of SMBs.
	 * Tables updated are : business_email_template, email_template, campaign, business_sms_template, business_deeplink_priority, campaign_settings.
	 * 
	 * NOTE: Made businessId request header because in getEmailTemplateResponse, businessId is being fetched from MDC.(Eg :
	 * EmailTemplateServiceImpl.getEnterpriseId())
	 * Default userId is 1946 (<EMAIL>).
	 * 
	 * @param businessIds
	 */
	@PostMapping(path = "/templates/smb")
	@Profiled
	public void migrateTemplateDataForSMB(@ApiParam(value = "Business Ids") @RequestHeader("bIds") List<Integer> businessIds,
			@ApiParam(value = "User Id") @RequestHeader(value = "uid", required = false, defaultValue = "1946") Integer userId, @RequestHeader(value = "emailId", required = false) String emailId,
			@ApiParam(value = "0/1 (Blank in case of default behaviour)") @RequestHeader(value = "contactUsEnabled", required = false) Integer contactUsEnabled,
			@ApiParam(value = "1 -> active, 'empty or any value except 1' -> paused") @RequestHeader(value = "campaignStatus", required = false) Integer campaignStatus,
			@ApiParam(value = "0/1 to turn off/on “Include unsubscribe text” while migrating SMS template") @RequestHeader(value = "enableUnsubscribeText", required = false) Integer enableUnsubscribeText,
			@ApiParam(value = "0/1 To exclude/include image with message") @RequestHeader(value = "enableMms", required = false) Integer enableMms,
			@ApiParam(value = "0/1 To select/unselect no-reply radio button in email template.") @RequestHeader(value = "noReplyEnabled", required = false, defaultValue = "1") Integer noReplyEnabled) {
		LOGGER.info("[DataMigrationController] Received request to migrate campaign/template related data for businessIds : {}.", businessIds);
		dataMigrationService.migrateTemplateDataForSMB(businessIds, userId, emailId, contactUsEnabled, campaignStatus, enableUnsubscribeText, enableMms, noReplyEnabled);
	}
	
	@GetMapping(path = "/rrmigrate/temp")
	public void syncCheckinData() {
		communicationConsumerService.syncCheckinData();
	}
	
	/**
	 * API to migrate Enterprise to new campaign flow.
	 * 
	 * @param enterpriseId
	 * @param userId
	 */
	@PostMapping(path = "/enterprise/{enterpriseId}")
	@Profiled
	public void migrateTemplateDataForEnterprise(@ApiParam(value = "Enterprise ID") @PathVariable("enterpriseId") Integer enterpriseId,
			@ApiParam(value = "User ID") @RequestHeader(value = "uid", required = false, defaultValue = "1946") Integer userId,
			@ApiParam(value = "0/1 (Blank in case of default behaviour)") @RequestHeader(value = "contactUsEnabled", required = false) Integer contactUsEnabled,
			@ApiParam(value = "1 -> active, 'empty or any value except 1' -> paused") @RequestHeader(value = "campaignStatus", required = false) Integer campaignStatus,
			@ApiParam(value = "1 -> existing deeplink priorities, '0/blank' -> default priorities") @RequestHeader(value = "useExistingReviewSources", required = false) Integer useExistingReviewSources,
			@ApiParam(value = "0/1 to turn off/on “Include unsubscribe text” while migrating SMS template") @RequestHeader(value = "enableUnsubscribeText", required = false) Integer enableUnsubscribeText,
			@ApiParam(value = "0/1 To disable/enable different headers/image for each location") @RequestHeader(value = "enableLocationBranding", required = false) Integer enableLocationBranding,
			@ApiParam(value = "0/1 To exclude/include image with message") @RequestHeader(value = "enableMms", required = false) Integer enableMms,
			@ApiParam(value = "0/1 To select/unselect no-reply radio button in email template.") @RequestHeader(value = "noReplyEnabled", required = false, defaultValue = "1") Integer noReplyEnabled) {
		LOGGER.info("[DataMigrationController] Received request to migrate to Campaigns V2 for enterprise ID : {} by user {}", enterpriseId, userId);
		dataMigrationService.migrateEnterpriseToCampaignsV2(enterpriseId, userId, contactUsEnabled, campaignStatus, enableUnsubscribeText, useExistingReviewSources, enableLocationBranding, enableMms,
				noReplyEnabled);
	}
	
	/**
	 * API to migrate Enterprise with given list of template ids to new campaign flow.
	 * 
	 * @param enterpriseId
	 * @param userId
	 */
	@PostMapping(path = "/templates/enterprise")
	@Profiled
	public void migrateTemplatesByIdForEnterprise(@RequestBody EnterpriseTemplatesMigrationRO request,
			@ApiParam(value = "User ID") @RequestHeader(value = "uid", required = false, defaultValue = "1946") Integer userId) {
		LOGGER.info("[DataMigrationController] Received request to migrate Enterprise templates to Campaign V2 : {} by user {}", request, userId);
		dataMigrationService.migrateTemplateDataForEnterprise(request, userId);
		LOGGER.info("[DataMigrationController] Finished migrating Enterprise templates to Campaign V2 : {} by user {}", request, userId);
		
	}
	
	/**
	 * API to create ongoing campaigns for given locations under an enterprise.
	 * 
	 * @param enterpriseId
	 * @param userId
	 */
	@PostMapping(path = "/create/ongoingcampaign")
	@Profiled
	public void createOngoingCampaignsForLocation(@RequestBody OngoingCampaignByLocationRequest request,
			@ApiParam(value = "User ID") @RequestHeader(value = "uid", required = false, defaultValue = "1946") Integer userId) {
		LOGGER.info("[DataMigrationController] Received request to create ongoing campaigns for request : {} by user {}", request, userId);
		dataMigrationService.createOngoingCampaignsForLocation(request, userId);
		LOGGER.info("[DataMigrationController] Finished creating ongoing campaigns for request : {} by user {}", request, userId);
		
	}
	
	/**
	 * API to migrate templates of an enterprise. Templates to be migrated will be passed in request. This api will not create any :
	 * 1. Ongoing Campaign (default or explicit)
	 * 2. Campaign settings
	 * 3. Account feature entry
	 * 
	 * @param enterpriseId
	 * @param userId
	 */
	@PostMapping(path = "/templatesonly/enterprise")
	@Profiled
	public void migrateTemplatesOnlyForEnterprise(@RequestBody EnterpriseTemplatesMigrationBasicRO request,
			@ApiParam(value = "User ID") @RequestHeader(value = "uid", required = false, defaultValue = "1946") Integer userId) {
		LOGGER.info("[DataMigrationController] Received request to migrate Enterprise templates only to Campaign V2 : {} by user {}", request, userId);
		dataMigrationService.migrateTemplateOnlyDataForEnterprise(request, userId);
		LOGGER.info("[DataMigrationController] Finished migrating Enterprise templates only to Campaign V2 : {} by user {}", request, userId);
		
	}
	
	@PostMapping(path = "/enterprise")
	@Profiled
	public void migrateTemplateDataForEnterpriseIds(@RequestHeader("enterpriseIds") List<Integer> enterpriseIds,
			@ApiParam(value = "User ID") @RequestHeader(value = "uid", required = false, defaultValue = "1946") Integer userId,
			@ApiParam(value = "0/1 (Blank in case of default behaviour)") @RequestHeader(value = "contactUsEnabled", required = false) Integer contactUsEnabled,
			@ApiParam(value = "1 -> active, 'empty or any value except 1' -> paused") @RequestHeader(value = "campaignStatus", required = false) Integer campaignStatus,
			@ApiParam(value = "1 -> existing deeplink priorities, '0/blank' -> default priorities") @RequestHeader(value = "useExistingReviewSources", required = false) Integer useExistingReviewSources,
			@ApiParam(value = "0/1 to turn off/on “Include unsubscribe text” while migrating SMS template") @RequestHeader(value = "enableUnsubscribeText", required = false) Integer enableUnsubscribeText,
			@ApiParam(value = "0/1 To disable/enable different headers/image for each location") @RequestHeader(value = "enableLocationBranding", required = false) Integer enableLocationBranding,
			@ApiParam(value = "0/1 To exclude/include image with message") @RequestHeader(value = "enableMms", required = false) Integer enableMms,
			@ApiParam(value = "0/1 To select/unselect no-reply radio button in email template.") @RequestHeader(value = "noReplyEnabled", required = false, defaultValue = "1") Integer noReplyEnabled) {
		LOGGER.info("[DataMigrationController] Received request to migrate to Campaigns V3 for enterprise ID : {} by user {}", enterpriseIds, userId);
		dataMigrationService.migrateEnterpriseToCampaignsV3(enterpriseIds, userId, contactUsEnabled, campaignStatus, enableUnsubscribeText, useExistingReviewSources, enableLocationBranding, enableMms,
				noReplyEnabled);
	}
	
	/**
	 * API to migrate Enterprise with given list of template ids to new campaign flow.
	 * 
	 * @param enterpriseId
	 * @param userId
	 */
	@PostMapping(path = "/campaign/update")
	@Profiled
	public void updateCampaignDelaySettings(@RequestParam("file") MultipartFile file) {
		LOGGER.info("[DataMigrationController] Received request to update Campaign Settings");
		try {
			Map<Integer, Map<String, String>> data = WorkbookUtil.readXSSFWorkbook(file.getInputStream(), new RowMapper<Map<String, String>>() {
				@Override
				public Map<String, String> mapRow(List<String> cells) {
					Map<String, String> map = new HashMap<>();
					if (cells.size() > 4) {
						map.put(DataMigrationService.BID, cells.get(0));
						map.put(DataMigrationService.DELAY, cells.get(1));
						map.put(DataMigrationService.R_COUNT, cells.get(2));
						map.put(DataMigrationService.R_FREQUENCY, cells.get(3));
						map.put(DataMigrationService.DAYS_LIMIT, cells.get(4));
					}
					return map;
				}
				
			});
			data.entrySet().stream().filter(e -> e.getKey() > 0).map(e -> e.getValue()).forEach(row -> {
				// Asyc call to handle campaign
				LOGGER.info("Submitting Campaign update task {} ", row);
				dataMigrationService.updateCampaign(row);
			});
		} catch (Exception e) {
			LOGGER.error("Error in reading File {}", file.getName(), e);
		}
		LOGGER.info("[DataMigrationController] Finished request to update Campaign Settings");
	}
	
	@PostMapping(path = "/promotionDataToES")
	public ResponseEntity<String> migratePromotionDataToES(@RequestParam(required = false) Long startRequestId, @RequestParam(required = false) Integer size) {
		communicationConsumerService.migratePromotionRequestIds(startRequestId, size);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@PostMapping(value = "/enterprise/referral/migrateDataToES")
	public ResponseEntity<String> migrateReferralDataForEnterprise(@RequestBody EventMigrationRequest request) {
		LOGGER.info("Request Received to migrate referral campaign Data to ES for businessId {}", request.getEnterpriseIds());
		communicationConsumerService.migrateReferralEnterpriseDataToES(request);
		LOGGER.info("Referral campaign Data successfully migrated to ES for businessIds {}", request.getEnterpriseIds());
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@PostMapping(value = "/location/referral/migrateDataToES")
	public ResponseEntity<String> migrateReferralDataForLocation(@RequestBody EventMigrationRequest request) {
		LOGGER.info("Request Received to migrate referral campaign Data to ES for locationIds {}", request.getLocationIds());
		communicationConsumerService.migrateReferralBusinessDataToES(request);
		LOGGER.info("Referral campaign Data successfully migrated to ES for locationIds {}", request.getLocationIds());
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@PostMapping(value = "/requests/referral/migrateDataToES")
	public ResponseEntity<String> migrateReferralDataForRequests(@RequestBody EventMigrationRequest request) {
		LOGGER.info("Request Received to migrate referral campaign Data to ES for businessId {}", request.getReviewRequestIds());
		communicationConsumerService.migrateReferralCommunicationData(request.getReviewRequestIds());
		LOGGER.info("Referral campaign Data successfully migrated to ES for businessIds {}", request.getReviewRequestIds());
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * Migrate usage data for businessId
	 * 
	 * @param businessId
	 * @return
	 */
	@PostMapping("/migrateUsageData/{businessId}")
	public ResponseEntity<String> migrateUsageDataByBusinessId(@PathVariable("businessId") Integer businessId) {
		LOGGER.info("Migrate Usage Data request received for businessId {}", businessId);
		dataMigrationService.deleteAndMigrateUsageDataToEsByBusinessId(businessId, null);
		LOGGER.info("successfully migrated Usage Data for businessId {}", businessId);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@PostMapping("/migrateUsageData")
	public ResponseEntity<String> migrateUsageDataToES(@RequestBody BusinessMigrationEventRequest migrationEventRequest) {
		LOGGER.info("Migrate Usage Data request received for businessId {}", migrationEventRequest.getSourceBusinessId());
		dataMigrationService.deleteAndMigrateUsageDataToEsByBusinessId(migrationEventRequest.getSourceBusinessId(), migrationEventRequest);
		LOGGER.info("successfully migrated Usage Data for businessId {}", migrationEventRequest.getSourceBusinessId());
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * Creates RR batches (picks an offset from cache and fetches requests - and splits them into equal batch size) for migrating request date fields to
	 * ES
	 * and pushes them to kafka for execution
	 */
	@PostMapping(path = "/prepareRequestDateMigratioBatch")
	@Profiled
	public void prepareRequestDateMigrationBatch() {
		LOGGER.info("[DataMigrationController] Received request to prepare request date migration batches");
		dataMigrationService.prepareRequestDateMigrationBatch();
		LOGGER.info("[DataMigrationController] Finished preparing request date migration batches");
	}
	
	
	/**
	 * Migrates RR batch (based on start request id and end request id) request date fields to ES (fetches review requests - groups them on
	 * request types - pushes ES update commands to kafka in configurable batch size
	 * 
	 * @param requestDateMigrationRequest
	 */
	@PostMapping(path = "/executeRequestDateMigratioBatch")
	@Profiled
	public void executeRequestDateMigrationBatch(@RequestBody MigrationRequest requestDateMigrationRequest) {
		LOGGER.info("[DataMigrationController] Received request to execute request date migration batch {}", requestDateMigrationRequest);
		dataMigrationService.executeRequestDateMigrationBatch(requestDateMigrationRequest);
		LOGGER.info("[DataMigrationController] Finished request to execute request date migration batch {}", requestDateMigrationRequest);
		
	}

	/**
	 * Migration referral code, Pick 10,000 referral requests and update referral code in ES index
	 * Publish event for migration of next 10,000 records
	 * 
	 * @param startRequestId
	 */
	@PostMapping(path = "/referral-code/{startRequestId}")
	@Profiled
	public void executeReferralCodeMigratioBatch(@PathVariable("startRequestId") Long startRequestId) {
		LOGGER.info("[DataMigrationController] Received request to execute referral code from start id {}", startRequestId);
		dataMigrationService.executeReferralCodeMigrationBatch(startRequestId);
		LOGGER.info("[DataMigrationController] Finished request to execute referral code from start id {}", startRequestId);
	}
	
	@PostMapping("/prepareSegmentCountMigrationBatch")
	@Profiled
	public void prepareSegmentCountMigration() {
		LOGGER.info("[prepareSegmentCountMigration] : Received request to prepare segment count batches");
		dataMigrationService.prepareSegmentCountMigrationBatch();
		LOGGER.info("[prepareSegmentCountMigration] : Finished preparing Segment Count Migration Batches");
	}
	
	@PostMapping("excuteSegmentCountMigrationBatch")
	@Profiled
	public void exceuteSegmentCountMigrationBatch(@RequestBody MigrationRequest request) {
		LOGGER.info("exceuteSegmentCountMigrationBatch : Received request to execute segment count migration batch {}", request);
		dataMigrationService.executeSegmentCountMigrationBatch(request);
		LOGGER.info("exceuteSegmentCountMigrationBatch : Segment count migration request successfully executed for batch {}", request);
	}
	
	@PostMapping("/migrateSegmentCountForEnterprise")
	@Profiled
	public void migrateSegmentCountForEnterprise(@RequestBody EventMigrationRequest request) {
		LOGGER.info("[migrateSegmentCountByEnterprise] : Received request to execute segment count migration for enterpriseIds {}", request.getEnterpriseIds());
		dataMigrationService.executeSegmentCountMigrationForEnterprise(request);
		LOGGER.info("migrateSegmentCountByEnterprise : Segment count migration request successfully executed for enterpriseIds {}", request.getEnterpriseIds());
	}
	
	@PostMapping("/migrateSegmentCountForRequestIds")
	@Profiled
	public void migrateSegmentCountForRequestIds(@RequestBody EventMigrationRequest request) {
		LOGGER.info("[migrateSegmentCountFoRequestIds] : Received request to execute segment count migration for requestIds {}", request.getReviewRequestIds());
		dataMigrationService.executeSegmentCountMigrationForRequestIds(request);
		LOGGER.info("migrateSegmentCountFoRequestIds : Segment count migration request successfully executed for requestIds {}", request.getReviewRequestIds());
	}
	
	/**
	 * API to create a sms pulse survey template for given accountIds. It handles a max batch size of 1000 accountIds at a time.
	 * 
	 * @param accountIds
	 * @param userId
	 */
	@PostMapping(path = "/pulseSurveyTemplate")
	@Profiled
	public void createDefaultPulseSurveyTemplate(@ApiParam(value = "Account IDs") @RequestHeader("accountIds") List<Integer> accountIds,
			@ApiParam(value = "User ID") @RequestHeader(value = "uid", required = false, defaultValue = "1946") Integer userId) {
		LOGGER.info("[DataMigrationController] Received request to migrate to Campaigns V3 for enterprise ID : {} by user {}", accountIds, userId);
		dataMigrationService.createDefaultPulseSurveyTemplate(accountIds, userId);
	}
	
	/**
	 * API to set Google/Fb as selected source i.e save in business_deeeplink_priority table.
	 * 
	 * @param enterpriseId
	 * @param userId
	 */
	@PostMapping(path = "/update-default-review-source")
	@Profiled
	public void updateBusinessDeeplinkPriority(@ApiParam(value = "Business Ids") @RequestHeader("bIds") List<Integer> businessIds,
			@ApiParam(value = "User ID") @RequestHeader(value = "uid", required = false, defaultValue = "1946") Integer userId) {
		LOGGER.info("[DataMigrationController] Received request to update deeplink priorities for businessIds : {} by user {}", businessIds, userId);
		dataMigrationService.updateBusinessDeeplinkPriority(businessIds);
		LOGGER.info("[DataMigrationController] Finished updating deeplink priorities for businessIds : {} by user {}", businessIds, userId);
	}
	
	/**
	 * API to set Google/Fb as selected source i.e save in business_deeeplink_priority table.
	 * 
	 * @param enterpriseId
	 * @param userId
	 */
	@PutMapping(path = "/default-templates/default-review-source")
	@Profiled
	public void setDefaultReviewSourcesToDefaultTemplates(@RequestBody DefaultReviewSourceRequest request) {
		LOGGER.info("[DataMigrationController] Received request to set Default Review Sources To Default Templates for businessIds : {}", request.getBusinessIds());
		dataMigrationService.setDefaultReviewSourcesToDefaultTemplates(request.getBusinessIds());
		LOGGER.info("[DataMigrationController] Finished setting Default Review Sources To Default Templates for businessIds : {}", request.getBusinessIds());
	}
	
	// Ongoing/Automation campaign migration
	
	@PutMapping(value = "/{enterpriseId}/automation/campaign/{campaignId}")
	public ResponseEntity<AutomationCampaignMigrationResponse> migrateAutomationCampaign(@PathVariable("enterpriseId") Integer enterpriseId, @PathVariable("campaignId") Integer campaignId) {
		LOGGER.info("Request received to migrate the automation campaign {} for enterprise {}", campaignId, enterpriseId);
		AutomationCampaignMigrationResponse response = dataMigrationService.migrateAutomationCampaignForEnterprise(enterpriseId, campaignId, true, null);
		return new ResponseEntity<>(response, HttpStatus.OK);
		
	}
	
	@PutMapping(value = "/{enterpriseId}/automation/campaigns")
	public ResponseEntity<List<AutomationCampaignMigrationResponse>> migrateEnterpriseAutomationCampaigns(@PathVariable("enterpriseId") Integer enterpriseId) {
		LOGGER.info("Request received to migrate the automation campaigns for enterprise {}", enterpriseId);
		List<AutomationCampaignMigrationResponse> response = dataMigrationService.migrateAutomationCampaignsForEnterprise(enterpriseId);
		return new ResponseEntity<>(response, HttpStatus.OK);
		
	}
	
	@PutMapping(value = "/bulk/automation/campaigns")
	public ResponseEntity<Map<Integer, List<AutomationCampaignMigrationResponse>>> migrateBulkEnterpriseAutomationCampaigns(@RequestHeader("accountIds") List<Integer> enterpriseIds) {
		LOGGER.info("Request received to migrate the bulk automation campaigns for enterpriseIds {}", enterpriseIds);
		Map<Integer, List<AutomationCampaignMigrationResponse>> response = dataMigrationService.migrateBulkAutomationCampaignsForEnterpriseIds(enterpriseIds);
		return new ResponseEntity<>(response, HttpStatus.OK);
		
	}
	
	/**
	 * API to update default review sources for list of long business Ids with source Ids
	 * 
	 * @param longBusinessIds
	 * @param sourceIds
	 */
	@PostMapping(path = "/default-review-sources")
	@Profiled
	public ResponseEntity<String> updateDefaultReviewSources(@RequestBody DefaultReviewSourceUpdateRequest request) {
		LOGGER.info("[DataMigrationController] Received request to update deeplink priorities for businessIds : {} with sourceIds : {}", request.getBusinessIds(), request.getReviewSourceIds());
		dataMigrationService.updateDefaultReviewSourcesForBusinessIds(request.getBusinessIds(), request.getReviewSourceIds());
		LOGGER.info("[DataMigrationController] Finished updating deeplink priorities for businessIds : {} with sourceIds : {}", request.getBusinessIds(), request.getReviewSourceIds());
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@PostMapping(value = "/get-default-review-sources")
	@Profiled
	public ResponseEntity<DefaultReviewSourceResponse> getDefaultReviewSources(@RequestBody DefaultReviewSourceRequest request) {
		LOGGER.info("Request recieved to get default review sources for business id {}", request.getBusinessIds());
		DefaultReviewSourceResponse response = dataMigrationService.getDefaultReviewSourcesForBusinessIds(request.getBusinessIds());
		LOGGER.info("Default Sources for business Ids are {}", response);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
	/**
	 * API to update invalid appointments for a given enterpriseId, leave blank for all enterpriseIds
	 * Invalid criteria - If referrer_code is missing
	 * [Referral code for all]
	 * 
	 * @return
	 */
	@PostMapping("/referral/update/invalid-appointments")
	@Profiled
	public ResponseEntity<Void> updateInvalidAppointmentData(@RequestParam(value = "enterpriseId", required = false) Integer enterpriseId) {
		LOGGER.info("Request received to update referral data for invalid appointments");
		dataMigrationService.updateInvalidAppointmentData(enterpriseId);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * API to send lead data (cid, referrerId) to Kontacto for appointment id(s) in range [startId, endId]
	 * Leave both parameter values blank for sending all leads data
	 * 
	 * @param startId
	 * @param endId
	 * @return
	 */
	@PostMapping("/kontacto/leads")
	@Profiled
	public ResponseEntity<Void> sendLeadsToKontacto(@RequestParam(value = "startId", required = false) Integer startId, @RequestParam(value = "endId", required = false) Integer endId) {
		LOGGER.info("Request received to send leads info to Kontacto from appointment id : {} to : {}", startId, endId);
		dataMigrationService.sendLeadsToKontacto(startId, endId);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * API to migrate appointment data to ES in range [startId, endId]
	 * Leave both parameter values blank for migrating all leads data
	 * 
	 * @param startId
	 * @param endId
	 * @return
	 */
	@PostMapping("/referral/appointments/migrateDataToES")
	@Profiled
	public ResponseEntity<Void> migrateAppointmentDataToES(@RequestParam(value = "startId", required = false) Integer startId, @RequestParam(value = "endId", required = false) Integer endId) {
		LOGGER.info("Request received to migrate appointment data to ES from appointment id : {} to : {}", startId, endId);
		dataMigrationService.migrateAppointmentDataToES(startId, endId);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@PostMapping("/review/qr-code/migration")
	@Profiled
	public ResponseEntity<Void>  migrateReviewQR(@RequestBody BusinessMigrationEventRequest request){
		LOGGER.info("Migrate QR review  Data request {}", request);	
		dataMigrationService.migrateQRDataFromOneBusinessToAnotherBusiness(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * 
	 * @param request
	 * @return
	 */
	@PostMapping("/automation/scheduling/migration")
	@Profiled
	public ResponseEntity<Void> migrateAutomationScheduling(@RequestBody List<Integer> campaignIds) {
		LOGGER.info("Request received to migrate automation old scheduling to new scheduling :: {}", campaignIds);
		dataMigrationService.migrateAutomationScheduling(campaignIds);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * 
	 * Prepare request to update default templates for multiple accounts
	 * 
	 * @throws Exception
	 */
	@PostMapping("/prepare/update-default-templates/multiple-accounts")
	@Profiled
	public ResponseEntity<Void> prepareUpdateDefaultTemplatesForMultipleAccounts(@RequestBody BulkDTUpdatePrepRequest request) throws Exception {
		LOGGER.info("Request received to prepare update for default templates :: {}", request);
		defaultTemplateMigrationService.prepareRequestToUpdateBulkDefaultTemplate(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * 
	 * Support Api to validate and Update default templates for multiple account
	 * 
	 */
	@PostMapping("/update/default-templates/multiple-accounts")
	@Profiled
	public ResponseEntity<Void> updateDefaultTemplatesForMultipleBusiness(@RequestBody BulkDTUpdateExecuteRequest request) {
		LOGGER.info("Request received to update default templates for request :: {}", request);
		defaultTemplateMigrationService.updateDefaultTemplatesForMultipleBusiness(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * 
	 * Api to execute update for default templates for a single account
	 * 
	 */
	@PostMapping("/execute/update-default-templates")
	@Profiled
	public ResponseEntity<Void> executeDefaultTemplatesMigrationForSingleBusiness(@RequestBody DTUpdateExecuteRequest request) {
		LOGGER.info("Request received to execute update default templates for request :: {}", request);
		defaultTemplateMigrationService.executeUpdateDefaultTemplateRequest(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * 
	 * API to validate and migrate campaign access settings for existing automations/split automations for a given account
	 * 
	 */
	@PostMapping("/access-settings/update")
	@Profiled
	public ResponseEntity<Void> updateAccessSettings(@RequestBody AccessSettingsUpdateRequest request) {
		LOGGER.info("Request received to update access settings for request :: {}", request);
		dataMigrationService.updateCampaignAccessSettingsForAccount(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@PostMapping(value = "/requests/appointment-recall/migrateDataToES")
	public ResponseEntity<String> migrateAppointmentRecallDataForRequests(@RequestBody EventMigrationRequest request) {
		LOGGER.info("Request Received to migrate appointment recall campaign Data to ES for request ids {}", request.getReviewRequestIds());
		communicationConsumerService.migrateAppointmentRecallCommunicationData(request.getReviewRequestIds());
		LOGGER.info("Appointment recall campaign Data successfully migrated to ES for request ids {}", request.getReviewRequestIds());
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@PostMapping(value = "/requests/appointment-reminder/migrateDataToES")
	public ResponseEntity<String> migrateAppointmentReminderDataForRequests(@RequestBody EventMigrationRequest request) {
		LOGGER.info("Request Received to migrate appointment reminder campaign Data to ES for request ids {}", request.getReviewRequestIds());
		communicationConsumerService.migrateAppointmentReminderCommunicationData(request.getReviewRequestIds());
		LOGGER.info("Appointment reminder campaign Data successfully migrated to ES for request ids {}", request.getReviewRequestIds());
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@PostMapping(value = "/requests/appointment-form/migrateDataToES")
	public ResponseEntity<String> migrateAppointmentFormDataForRequests(@RequestBody EventMigrationRequest request) {
		LOGGER.info("Request Received to migrate appointment form campaign Data to ES for request ids {}", request.getReviewRequestIds());
		communicationConsumerService.migrateAppointmentFormCommunicationData(request.getReviewRequestIds());
		LOGGER.info("Appointment form campaign Data successfully migrated to ES for request ids {}", request.getReviewRequestIds());
		return new ResponseEntity<>(HttpStatus.OK);
	}
}