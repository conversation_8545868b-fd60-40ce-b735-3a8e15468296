package com.birdeye.campaign.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.campaign.dto.ResellerWidgetGalleryCampaignResponse;
import com.birdeye.campaign.report.ResellerReportsService;
import com.birdeye.campaign.request.GenerateResellerReportingCampaignMetricsRequest;
import com.birdeye.campaign.request.GenerateReviewsCampaignSummaryRequest;
import com.birdeye.campaign.request.GenerateReviewsCampaignUsageSummaryRequest;
import com.birdeye.campaign.request.ResellerReportingCampaignMetricsRequest;
import com.birdeye.campaign.request.ResellerWidgetCheckCampaignEnabledRequest;
import com.birdeye.campaign.request.ResellerWidgetGalleryRequestFilter;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("v1/reseller/reports")
public class ResellerReportsController {
	
	@Autowired
	ResellerReportsService resellerReportsService;
	
	/**
	 * @param request
	 * @return
	 *         This api publishes event to generate 10dlc registration status metric and campaign metrics for reviews, appointment reminders, surveys and
	 *         referrals.
	 */
	@PostMapping("/metrics")
	public ResponseEntity<Void> generateResellerReportingCampaignsMetrics(@RequestBody ResellerReportingCampaignMetricsRequest request) {
		resellerReportsService.generateResellerReportingCampaignsMetrics(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * @param request
	 * @return
	 *         This api generates event with reviews campaign related metrics for reseller reporting
	 */
	@PostMapping("/reviews/metrics")
	public ResponseEntity<Void> generateResellerReportingReviewsMetrics(@RequestBody GenerateResellerReportingCampaignMetricsRequest request) {
		resellerReportsService.generateResellerReportingReviewsMetrics(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * @param request
	 * @return
	 *         This api generates event with manual reviews campaign related metrics for reseller reporting
	 */
	@PostMapping("/manual/reviews/metrics")
	public ResponseEntity<Void> generateResellerReportingManualReviewsCampaignMetrics(@RequestBody GenerateResellerReportingCampaignMetricsRequest request) {
		resellerReportsService.generateResellerReportingManualReviewsCampaignMetrics(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * @param request
	 * @return
	 *         This api generates event with appointment reminder campaign related metrics for reseller reporting
	 */
	@PostMapping("/appointment-reminder/metrics")
	public ResponseEntity<Void> generateResellerReportingAppointmentMetrics(@RequestBody GenerateResellerReportingCampaignMetricsRequest request) {
		resellerReportsService.generateResellerReportingAppointmentMetrics(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * @param request
	 * @return
	 *         This api generates event with referral campaign related metrics for reseller reporting
	 */
	@PostMapping("/referral/metrics")
	public ResponseEntity<Void> generateResellerReportingReferralMetrics(@RequestBody GenerateResellerReportingCampaignMetricsRequest request) {
		resellerReportsService.generateResellerReportingReferralCampaignMetrics(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * @param request
	 * @return
	 *         This api generates event with survey campaign related metrics for reseller reporting
	 */
	@PostMapping("/survey/metrics")
	public ResponseEntity<Void> generateResellerReportingSurveyMetrics(@RequestBody GenerateResellerReportingCampaignMetricsRequest request) {
		resellerReportsService.generateResellerReportingSurveyCampaignMetrics(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * @return
	 * This api initiated reseller reviews campaign report generation for reseller campaign widget.
	 * It fetches all the accounts which are present under any of the resellers and publishes events in
	 * batches of 1000 account ids.
	 */
	@PostMapping("/campaign/widget/initiate")
	public ResponseEntity<Void> initiateResellerReviewsCampaignUsageSummaryGeneration() {
		resellerReportsService.initiateResellerReviewsCampaignSummaryGeneration();
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * @param checkCampaignEnabledRequest
	 * @return
	 * This API checks if the campaign is enabled for the account in the request and then publishes event 
	 * to generate campaign usage summary for campaigns under this account.
	 */
	@PostMapping("/campaign/widget/campaign-enabled")
	public ResponseEntity<Void> checkCampaignEnabledFlagForResellerCampaignWidget(@RequestBody ResellerWidgetCheckCampaignEnabledRequest checkCampaignEnabledRequest) {
		resellerReportsService.checkCampaignEnabledFlagForResellerCampaignWidget(checkCampaignEnabledRequest);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * @param request
	 * @return
	 * This api consumes the batches of account ids produced from campaign/widget/initiate API. It then fetches the active
	 * reviews campaigns for these accounts and creates and publishes these campaign ids in batch of 100 for further processing.
	 */
	@PostMapping("/campaign/widget/process-campaignids")
	public ResponseEntity<Void> fetchAndPublishCampaignIdsForResellerWidget(@RequestBody GenerateReviewsCampaignUsageSummaryRequest request) {
		resellerReportsService.fetchAndPublishCampaignIdsForResellerWidget(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	/**
	 * @param request
	 * @return
	 * This API consumes the batch of campaign ids produced from campaign/widget/process-campaignids API. It then fetches the usage data of all these campaigns 
	 * from commreport index and inserts them in the index created by campaign/widget/create-index api i.e.reseller_campaign_widget_dd_MM_YY
	 */
	@PostMapping("/campaign/widget/generate-summary")
	public ResponseEntity<Void> generateResellerReviewsCampaignUsageSummary(@RequestBody GenerateReviewsCampaignSummaryRequest request) {
		resellerReportsService.generateResellerReviewsCampaignUsageSummary(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * @param request
	 * @return
	 * This API fetches the list of top campaign by criteria received in the request from reseller_campaign_widget_dd_MM_YY ES index 
	 */
	@PostMapping("/campaign/widget")
	public ResponseEntity<ResellerWidgetGalleryCampaignResponse> getTopRRCampaignForResellerWidgetGallery(@RequestBody ResellerWidgetGalleryRequestFilter request){
		ResellerWidgetGalleryCampaignResponse response = resellerReportsService.getTopRRCampaignForResellerWidgetGallery(request);		
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
}
