package com.birdeye.campaign.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.logging.LoggersEndpoint;
import org.springframework.boot.actuate.logging.LoggersEndpoint.LoggerLevels;
import org.springframework.boot.logging.LogLevel;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@RestController
@RequestMapping("/v1/logger/")
public class LoggerController {

	@Autowired
	private LoggersEndpoint loggersEndpoint;

	private static final Logger logger = LoggerFactory.getLogger(LoggerController.class);

	@PostMapping(value = "configure/loglevel")
	public ResponseEntity<Void> configureLogLevel(@RequestBody LogConfiguration logConfiguration) {
		logger.info("enable log leval with request {}", logConfiguration);
		loggersEndpoint.configureLogLevel(logConfiguration.getName(), getLogLevel(logConfiguration.getConfiguredLevel()));
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@GetMapping(value = "loglevel/{name}")
	public ResponseEntity<LoggerLevels> getConfiguredLogLevel(@PathVariable("name") String name) {
		logger.info("Fetching log level on name :{} ", name);
		return new ResponseEntity<>(loggersEndpoint.loggerLevels(name),HttpStatus.OK);
	}

	static class LogConfiguration {

		private String name;

		private String configuredLevel;

		/**
		 * @return the name
		 */
		public String getName() {
			return name;
		}

		/**
		 * @param name
		 *            the name to set
		 */
		public void setName(String name) {
			this.name = name;
		}

		/**
		 * @return the configuredLevel
		 */
		public String getConfiguredLevel() {
			return configuredLevel;
		}

		/**
		 * @param configuredLevel
		 *            the configuredLevel to set
		 */
		public void setConfiguredLevel(String configuredLevel) {
			this.configuredLevel = configuredLevel;
		}

	}
	public LogLevel getLogLevel(String configuredLevel) {
		for (LogLevel level : LogLevel.values()) {
			if (level.name().equalsIgnoreCase(configuredLevel))
				return level;
		}
		return LogLevel.INFO;
	}

}
