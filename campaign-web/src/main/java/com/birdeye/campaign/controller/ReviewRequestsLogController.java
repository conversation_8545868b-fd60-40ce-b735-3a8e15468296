package com.birdeye.campaign.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.campaign.dto.ReviewRequestLogBulkWrapper;
import com.birdeye.campaign.service.ReviewRequestLogService;

@RestController
@RequestMapping("v1/review-requests-log")
public class ReviewRequestsLogController {
	
	@Autowired
	private ReviewRequestLogService reviewRequestLogService;
	
	/**
	 * Retrieves review request logs for multiple review request IDs in bulk.
	 *
	 * @param reviewRequestIds
	 *            The list of review request IDs for which logs are to be retrieved.
	 * @return ResponseEntity containing the ReviewRequestLogBulkWrapper with HttpStatus OK if successful.
	 */
	@PostMapping("/review-request-ids/bulk")
	public ResponseEntity<ReviewRequestLogBulkWrapper> getByReviewRequestids(@RequestBody List<Long> reviewRequestIds) {
		return new ResponseEntity<>(reviewRequestLogService.getRRLogByReviewRequestIds(reviewRequestIds), HttpStatus.OK);
	}
	
}
