package com.birdeye.campaign.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.campaign.dto.CampaignModificationEvent;
import com.birdeye.campaign.request.ChangeLogsRequest;
import com.birdeye.campaign.response.ChangeLogsResponse;
import com.birdeye.campaign.service.CampaignModificationAuditService;

/**
 * REST APIs For Auditing Modifications In Campaign Module
 * 
 */
@RestController
@RequestMapping("/v1/campaign")
public class CampaignModificationAuditController {
	
	private static final Logger LOG = LoggerFactory.getLogger(CampaignModificationAuditController.class);
	
	@Autowired
	private CampaignModificationAuditService	campaignModificationAuditService;
	
	@PostMapping(value = "/audit-changes")
	public ResponseEntity<Void> auditChanges(@RequestBody CampaignModificationEvent modificationEvent) {
		LOG.info("Received request to log modifications for event: {}.", modificationEvent);
		campaignModificationAuditService.detectAndAuditChanges(modificationEvent);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * API to fetch changes logs of 'campaigns'/'templates'.
	 * 
	 * @param accountId
	 * @param request
	 * @return
	 */
	@PostMapping(value = "user-activity/audit/{accountId}")
	public ResponseEntity<ChangeLogsResponse> getChangeLogs(@PathVariable("accountId") Integer accountId, @RequestBody ChangeLogsRequest request) {
		LOG.info("Received request to fetch change logs for {}: {} of account: {}. Request: {}", request.getEntityType(), request.getEntityId(), accountId, request);
		ChangeLogsResponse response = campaignModificationAuditService.getChangeLogs(accountId, request);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
}
