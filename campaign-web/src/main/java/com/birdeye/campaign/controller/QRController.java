package com.birdeye.campaign.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.campaign.aspect.annotation.Profiled;
import com.birdeye.campaign.qr.service.QRService;
import com.birdeye.campaign.request.external.QrReviewMappingRequest;
import com.birdeye.campaign.request.external.QrReviewSourcesResponse;
import com.birdeye.campaign.response.external.QRMostClickedSourceResponse;
import com.birdeye.campaign.response.template.v2.EditTemplateResponse;
import com.birdeye.campaign.response.template.v2.EmailTemplateResponse;
import com.birdeye.campaign.sro.ReviewSourceSRO;

import io.swagger.annotations.ApiParam;

@RestController
@RequestMapping(path = "/qr-code")
public class QRController {
	
	private static final Logger	LOGGER	= LoggerFactory.getLogger(QRController.class);
	
	@Autowired
	QRService					qrService;
	
	/**
	 * create mapping between qr config and review sources and save in qr_config_review_source_mapping table
	 * 
	 * @param qrConfigId
	 * @param userId
	 * @param qrReviewMappingRequest
	 * @return review sources on success
	 */
	@PostMapping(path = "/{qr-config-id}")
	public ResponseEntity<List<ReviewSourceSRO>> createQRConfigReviewSourceMapping(@PathVariable("qr-config-id") Integer qrConfigId, @RequestHeader("uid") Integer userId,
			@RequestBody QrReviewMappingRequest qrReviewMappingRequest) {
		LOGGER.info("Received request to create QR config review source mapping for id {} and request :: {}", qrConfigId, qrReviewMappingRequest);
		return new ResponseEntity<>(qrService.createQRConfigReviewSourceMapping(qrConfigId, qrReviewMappingRequest), HttpStatus.OK);
	}
	
	/**
	 * update mapping between qr config and review sources and save changes in qr_config_review_source_mapping table
	 * 
	 * @param qrConfigId
	 * @param userId
	 * @param qrReviewMappingRequest
	 * @return review sources on success
	 */
	@PutMapping(path = "/{qr-config-id}")
	public ResponseEntity<List<ReviewSourceSRO>> updateQRConfigReviewSourceMapping(@PathVariable("qr-config-id") Integer qrConfigId, @RequestHeader("uid") Integer userId,
			@RequestBody QrReviewMappingRequest qrReviewMappingRequest) {
		LOGGER.info("Received request to update QR config review source mapping for id {} request :: {}", qrConfigId, qrReviewMappingRequest);
		return new ResponseEntity<>(qrService.updateQRConfigReviewSourceMapping(qrConfigId, qrReviewMappingRequest), HttpStatus.OK);
	}
	
	/**
	 * get review sources for the given qr config id from qr_config_review_source_mapping table
	 * 
	 * @param qrConfigId
	 * @param accountId
	 * @return selectedSources, availableSources
	 */
	@GetMapping(path = "/{qr-config-id}")
	public ResponseEntity<QrReviewSourcesResponse> getQRConfigReviewSourceMapping(@PathVariable("qr-config-id") Integer qrConfigId, @RequestParam("account-id") Integer accountId) {
		return new ResponseEntity<>(qrService.getQRReviewSources(qrConfigId, accountId), HttpStatus.OK);
	}
	
	/**
	 * delete review sources mapping for the given qr config id
	 * 
	 * @param qrConfigId
	 * @return success flag
	 */
	@DeleteMapping(path = "/{qr-config-id}")
	public ResponseEntity<Boolean> deleteQRConfigReviewSourceMapping(@PathVariable("qr-config-id") Integer qrConfigId, @RequestParam("accountId") Integer accountId) {
		LOGGER.info("Received request to delete QR config review source mapping for qrConfigId :: {}", qrConfigId);
		return new ResponseEntity<>(qrService.deleteQRConfigReviewSourceMapping(qrConfigId, accountId), HttpStatus.OK);
	}
	/**
	 * 
	 * @param qrConfigId
	 * @param accountId
	 * @param defaultSources
	 * @return
	 */
	
	@PostMapping(path = "/default/{qr-config-id}/{account-id}")
	public ResponseEntity<QRMostClickedSourceResponse> createDefaultQRConfigReviewSourceMapping(@PathVariable("qr-config-id") Integer qrConfigId, @PathVariable("account-id") Integer accountId, @RequestParam("defaultSources") Integer defaultSources) {
		LOGGER.info("received request to create default qr template configuration for account id {} and qr config id {}", accountId, qrConfigId);
		
		return new ResponseEntity<>(qrService.createDefaultQRConfigTemplate(qrConfigId, accountId, defaultSources), HttpStatus.OK);
	}
	/**
	 * 
	 * @param accountId
	 * @param qrConfigId
	 * @param request
	 * @return
	 * 
	 * API to update qr code template
	 */
	
	@PostMapping("update-template/{qr-id}")
	@Profiled
	public ResponseEntity<EditTemplateResponse> saveOrUpdateQRTemplate(@RequestParam(value = "account-id") Integer accountId, @PathVariable("qr-id") Integer qrConfigId,
			@RequestBody EmailTemplateResponse request) {
		LOGGER.info("received request to update qr template configuration for account id {} and qr config id {} : {}", accountId, qrConfigId, request);
		EditTemplateResponse response = qrService.createOrUpdateBusinessEmailTemplate(qrConfigId, accountId, request);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	/**
	 * 
	 * @param accountId
	 * @param qrId
	 * @return
	 * API to get QR code template details
	 */
	
	@GetMapping("get-template/{qr-id}")
	@Profiled
	public ResponseEntity<EmailTemplateResponse> getQREmailTemplate(@RequestParam("account-id") Integer accountId,
			@ApiParam(value = "0 for new template, email_template id for old") @PathVariable("qr-id") Integer qrId) {
		LOGGER.info("received request to get qr template configuration for account id {} and qr config id {}", accountId, qrId);
		EmailTemplateResponse response = qrService.getQRTemplateDetails(qrId, accountId);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
}
