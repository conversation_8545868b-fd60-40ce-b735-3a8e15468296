package com.birdeye.campaign.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.campaign.aerospike.redisclient.AerospikeJedisService;
import com.birdeye.campaign.aerospike.service.AerospikeService;

import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@RestController
@RequestMapping("/v1/aerospike/")
public class AerospikeController {

	@Autowired
	private AerospikeJedisService aerospikeJedisService;

	@Autowired
	private AerospikeService aerospikeService;

	private static final Logger logger = LoggerFactory.getLogger(AerospikeController.class);

	@DeleteMapping("{set}/deleteKey/{key}")
	public ResponseEntity<Long> deleteKeyFromCache(@PathVariable("key") String key, @PathVariable("set") String set) {
		logger.info("Request received to delete the redis key :{} for set :{}", key, set);
		return new ResponseEntity<>(aerospikeJedisService.delete(key, set), HttpStatus.OK);
	}

	@PostMapping(value = "deleteKey")
	@ApiOperation(value = "Delete an entry from a set and key")
	public String deleteEntry(@RequestBody Entry entry) {
		logger.info("Deleting entry on namespace :{} set {} and key {}", entry.getNamespace(), entry.getSet(), entry.getKey());
		return aerospikeService.deleteEntry(entry.getNamespace(), entry.getSet(), entry.getKey());
	}

	@GetMapping(value = "readKey")
	@ApiOperation(value = "Fetch cache data against a key")
	public Object getEntry(@RequestParam(value = "ns") String namespace, @RequestParam(value = "set") String set,
			@RequestParam(value = "key") String key) {
		logger.info("Fetching entry on namespace :{} set {} and key {}", namespace, set, key);
		return aerospikeService.getEntry(namespace, set, key);
	}
	
	@DeleteMapping("/deleteSet/{set}/namespace/{namespace}")
	@ApiOperation(value = "Delete a set from a namespace")
	public ResponseEntity<String> deleteSetFromCache(@PathVariable("set") String set, @PathVariable("namespace") String namespace) {
		logger.info("Request received to delete set: {}, namespace:{}.", set, namespace);
		return new ResponseEntity<>(aerospikeService.deleteSet(namespace, set), HttpStatus.OK);
	}
	
	static class Entry {
		private String namespace;
		private String set;
		private String key;

		public String getNamespace() {
			return namespace;
		}

		public void setNamespace(String namespace) {
			this.namespace = namespace;
		}

		public String getSet() {
			return set;
		}

		public void setSet(String set) {
			this.set = set;
		}

		public String getKey() {
			return key;
		}

		public void setKey(String key) {
			this.key = key;
		}

	}

}
