package com.birdeye.campaign.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.campaign.dto.ReviewRequestAttributionDto;
import com.birdeye.campaign.dto.ReviewRequestDto;
import com.birdeye.campaign.dto.ReviewRequestFilteredResponse;
import com.birdeye.campaign.dto.ReviewRequestListDTO;
import com.birdeye.campaign.dto.ReviewSourceClickDTO;
import com.birdeye.campaign.request.DeleteCheckinDataRequest;
import com.birdeye.campaign.request.GetCustomerLastActivityRequest;
import com.birdeye.campaign.request.GetRRChainRequest;
import com.birdeye.campaign.request.ReviewRequestFilterRequest;
import com.birdeye.campaign.response.GetCustomerLastActivityResponse;
import com.birdeye.campaign.response.GetRRChainResponse;
import com.birdeye.campaign.service.ReviewRequestService;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/v1/reviewrequest")
public class ReviewRequestController {
	
	@Autowired
	private ReviewRequestService reviewRequestService;
	
	@GetMapping("/clickevent/business/{businessId}/source/{sourceId}")
	public ResponseEntity<List<ReviewRequestAttributionDto>> getReviewAttributionDataViaES(@PathVariable("businessId") Integer businessId, @PathVariable("sourceId") Integer sourceId,
			@RequestParam(value = "fromDate", required = false) Long fromDate, @RequestParam(value = "toDate", required = false) Long toDate,
			@RequestParam(value = "page", required = true) Integer page, @RequestParam(value = "size", required = false, defaultValue = "25") Integer size) {
		List<ReviewRequestAttributionDto> reviewAttributionData = reviewRequestService.getReviewAttributionData(businessId, sourceId, fromDate, toDate, page, size);
		return new ResponseEntity<>(reviewAttributionData, HttpStatus.OK);
	}
	
	@PostMapping("/clickevent/business/{businessId}")
	public ResponseEntity<List<ReviewSourceClickDTO>> getReviewRequestDetails(@PathVariable("businessId") Integer businessId, @RequestBody(required = true) List<Integer> sourceIds,
			@RequestParam(value = "fromDate", required = false) Long fromDate, @RequestParam(value = "toDate", required = false) Long toDate,
			@RequestParam(value = "startIndex", required = false, defaultValue = "0") Integer startIndex, @RequestParam(value = "size", required = false, defaultValue = "25") Integer size,
			@RequestParam(value = "customerId", required = false) Integer customerId) {
		List<ReviewSourceClickDTO> reviewAttributionData = reviewRequestService.getReviewAttributionDataViaES(businessId, sourceIds, fromDate, toDate, startIndex, size, customerId);
		return new ResponseEntity<>(reviewAttributionData, HttpStatus.OK);
	}
	
	@PostMapping("/rrchain")
	public ResponseEntity<GetRRChainResponse> getRRChainForAReviewRequest(@RequestBody GetRRChainRequest request) {
		GetRRChainResponse response = reviewRequestService.getReviewRequestChainForARR(request);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	@PostMapping("/rrchain/survey")
	public ResponseEntity<GetRRChainResponse> getRRChainForASurveyRequest(@RequestBody GetRRChainRequest request) {
		GetRRChainResponse response = reviewRequestService.getRRChainForASurveyRequest(request);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
	@GetMapping("/{reviewRequestId}")
	public ResponseEntity<ReviewRequestDto> getReviewRequestDetails(@PathVariable("reviewRequestId") Long reviewRequestId) {
		ReviewRequestDto response = reviewRequestService.getReviewRequestData(reviewRequestId);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
	@PostMapping("/bulk")
	public ResponseEntity<List<ReviewRequestDto>> getReviewRequestDetailsBulk(@RequestBody List<Long> reviewRequestIds) {
		return new ResponseEntity<>(reviewRequestService.getReviewRequestBulkData(reviewRequestIds), HttpStatus.OK);
	}
	

	@PostMapping("/customer/last-activity")
	public ResponseEntity<GetCustomerLastActivityResponse> getLastActivityDateOfACustomer(@RequestBody GetCustomerLastActivityRequest request) {
		GetCustomerLastActivityResponse response = reviewRequestService.getLastActivityDateOfACustomer(request);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
	@PostMapping("/delete/checkin-data")
	public ResponseEntity<Void> deleteCheckinData(@RequestBody DeleteCheckinDataRequest request) {
		reviewRequestService.deleteCheckinData(request);
		return new ResponseEntity<>(HttpStatus.OK);
		
	}
	
	/**
	 * @param enterpriseId
	 * @return
	 * This API returns true if review request(RR) was sent successfully 
	 * for any of business locations under given enterprise
	 */
	@GetMapping(value="review-request-sent/{enterpriseId}")
	public ResponseEntity<Boolean> checkReviewRequestsSent(@PathVariable("enterpriseId") Integer enterpriseId){
		return new ResponseEntity<>(reviewRequestService.checkReviewRequestsSent(enterpriseId), HttpStatus.OK);
	}
	
	@PostMapping("/delete/review-request")
	public ResponseEntity<Void> deleteCheckinData(@RequestBody ReviewRequestListDTO request) {
		reviewRequestService.deleteReviewRequest(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * Retrieves review request details based on the provided filter criteria.
	 *
	 * @param request
	 *            The ReviewRequestFilterRequest object containing filter parameters.
	 * @return ResponseEntity containing the ReviewRequestFilteredResponse with HttpStatus OK if successful.
	 */
	@PostMapping("/paginated/info")
	public ResponseEntity<ReviewRequestFilteredResponse> getReviewRequestByFilter(@RequestBody ReviewRequestFilterRequest request) {
		return new ResponseEntity<>(reviewRequestService.getReviewRequestByFilter(request), HttpStatus.OK);
	}
}
