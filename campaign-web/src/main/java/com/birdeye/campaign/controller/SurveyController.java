package com.birdeye.campaign.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.campaign.request.SurveyRequestFilter;
import com.birdeye.campaign.response.SurveyRequestCountResponse;
import com.birdeye.campaign.response.SurveyUsageStatsResponse;
import com.birdeye.campaign.service.SurveyService;

@RestController
@RequestMapping("v1/survey")
public class SurveyController {
	
	@Autowired
	private SurveyService surveyService;
	
	@PostMapping(value = "/requestcount")
	public ResponseEntity<SurveyRequestCountResponse> getSurveyCounts(@RequestBody SurveyRequestFilter surveyCountRequest) {
		return new ResponseEntity<>(surveyService.getSurveyRequestCounts(surveyCountRequest), HttpStatus.OK);
	}
	
	/*
	 * API to fetch source-level survey usage stats
	 * 
	 * @param request
	 * 
	 * @return survey sent-count, opened-count & clicked-count
	 */
	@PostMapping(value = "/usage/stats")
	public ResponseEntity<SurveyUsageStatsResponse> getSurveyStatsSourceLevel(@RequestBody SurveyRequestFilter request) {
		return new ResponseEntity<>(surveyService.getSourceLevelSurveyUsageStats(request), HttpStatus.OK);
	}
	
	/*
	 * API to fetch location-level survey usage stats
	 * 
	 * @param request
	 * 
	 * @return survey sent-count, opened-count & clicked-count
	 */
	@PostMapping(value = "/usage/location-stats")
	public ResponseEntity<SurveyUsageStatsResponse> getSurveyStatsLocationLevel(@RequestBody SurveyRequestFilter request){
		return new ResponseEntity<>(surveyService.getLocationLevelSurveyUsageStats(request), HttpStatus.OK);
	}
}
