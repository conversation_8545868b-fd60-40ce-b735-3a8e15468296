package com.birdeye.campaign.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.campaign.reminder.events.RecurringReminderEventsService;
import com.birdeye.campaign.request.CampaignUpdateEvent;
import com.birdeye.campaign.request.ContactReminderAuditEvent;

@RestController
@RequestMapping("/v1/reminder/events/")
// change class name
public class RecurringReminderEventsController {
	private static final Logger				logger	= LoggerFactory.getLogger(RecurringReminderEventsController.class);
	
	@Autowired
	private RecurringReminderEventsService	reminderEventsService;
	
	@PostMapping(value = "create")
	public void addEvents(@RequestBody CampaignUpdateEvent request) {
		logger.info("received request to schedule campaign reminder event for request  : {}", request);
		reminderEventsService.addNewEvent(request);
	}
	
	@GetMapping(value = "poll")
	public void pollEvents(@RequestParam("size") Integer pollSize) {
		reminderEventsService.pollRecurringReminderEvents(pollSize);
	}
	
	@PostMapping(value = "audit")
	public void auditEvents(@RequestBody ContactReminderAuditEvent request) {
		logger.info("received request to audit campaign reminder event for request  : {}", request);
		reminderEventsService.auditContactEvents(request);
	}
}
