package com.birdeye.campaign.controller;

import com.birdeye.campaign.request.MassTextingRequest;
import com.birdeye.campaign.response.MassTextingResponse;
import com.birdeye.campaign.service.MassTextingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/v1/campaign-external")
public class MassTextingController {
	
	@Autowired
	private MassTextingService massTextingService;
	
	@PostMapping("/outbound/text")
	public ResponseEntity<MassTextingResponse> sendOutboundText(@RequestHeader(value = "business-id") Integer businessId, @RequestBody MassTextingRequest request) throws Exception {
		MassTextingResponse response = massTextingService.sendOutboundText(request, businessId);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}	

	// request received from kafka for this API
	@PostMapping(value = "/send/{message_id}")
	public ResponseEntity<Void> createAndExecutePromotion(@PathVariable("message_id") Long messageId) {
		massTextingService.createAndExecutePromotion(messageId);
		return new ResponseEntity<>(HttpStatus.OK);
	}
}
