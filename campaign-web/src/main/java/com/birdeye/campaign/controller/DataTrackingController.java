package com.birdeye.campaign.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.campaign.request.TrackClicksRequest;
import com.birdeye.campaign.service.DataTrackingService;

@RestController
@RequestMapping("/v1/track")
public class DataTrackingController {
	
	private static final Logger	logger	= LoggerFactory.getLogger(DataTrackingController.class);
	
	@Autowired
	private DataTrackingService	dataTrackingService;
	
	@PostMapping(value = "/click")
	public void trackEvents(@RequestBody TrackClicksRequest request) {
		logger.info("received request to log click event : {}", request);
		dataTrackingService.trackClickEvent(request);
	}
	
	@PostMapping(value = "/appointment/click")
	public void trackAppointmentClickEvent(@RequestBody TrackClicksRequest request) {
		logger.info("received request to log appointment click event : {}", request);
		dataTrackingService.trackClickEvent(request);
	}
}
