package com.birdeye.campaign.advice;

import java.util.ArrayList;
import java.util.List;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

import org.springframework.beans.TypeMismatchException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import com.birdeye.campaign.exception.CampaignBadRequestException;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.exception.CampaignExternalServiceException;
import com.birdeye.campaign.exception.CampaignQuickSendException;
import com.birdeye.campaign.exception.DeeplinkAuthenticationException;
import com.birdeye.campaign.exception.ReferralPublicFormException;
import com.birdeye.campaign.exception.ReviewGenException;
import com.birdeye.campaign.exception.RuleCreationException;

@ControllerAdvice
public class CampaignExceptionHandler extends ResponseEntityExceptionHandler {

    // 400

    private static final String ERROR_OCCURRED = "error occurred";

	@Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(final MethodArgumentNotValidException ex, final HttpHeaders headers, final HttpStatus status, final WebRequest request) {
        logger.info(ex.getClass().getName());
        //
        final List<String> errors = new ArrayList<>();
        for (final FieldError error : ex.getBindingResult().getFieldErrors()) {
            errors.add(error.getField() + ": " + error.getDefaultMessage());
        }
        for (final ObjectError error : ex.getBindingResult().getGlobalErrors()) {
            errors.add(error.getObjectName() + ": " + error.getDefaultMessage());
        }
        final ApiError apiError = new ApiError(HttpStatus.BAD_REQUEST, ex.getLocalizedMessage(), errors);
        return handleExceptionInternal(ex, apiError, headers, apiError.getStatus(), request);
    }

    @Override
    protected ResponseEntity<Object> handleBindException(final BindException ex, final HttpHeaders headers, final HttpStatus status, final WebRequest request) {
        logger.info(ex.getClass().getName());
        //
        final List<String> errors = new ArrayList<>();
        for (final FieldError error : ex.getBindingResult().getFieldErrors()) {
            errors.add(error.getField() + ": " + error.getDefaultMessage());
        }
        for (final ObjectError error : ex.getBindingResult().getGlobalErrors()) {
            errors.add(error.getObjectName() + ": " + error.getDefaultMessage());
        }
        final ApiError apiError = new ApiError(HttpStatus.BAD_REQUEST, ex.getLocalizedMessage(), errors);
        return handleExceptionInternal(ex, apiError, headers, apiError.getStatus(), request);
    }

    @Override
    protected ResponseEntity<Object> handleTypeMismatch(final TypeMismatchException ex, final HttpHeaders headers, final HttpStatus status, final WebRequest request) {
        logger.info(ex.getClass().getName());
        //
        final String error = ex.getValue() + " value for " + ex.getPropertyName() + " should be of type " + ex.getRequiredType();

        final ApiError apiError = new ApiError(HttpStatus.BAD_REQUEST, ex.getLocalizedMessage(), error);
        return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus());
    }

    @Override
    protected ResponseEntity<Object> handleMissingServletRequestPart(final MissingServletRequestPartException ex, final HttpHeaders headers, final HttpStatus status, final WebRequest request) {
        logger.info(ex.getClass().getName());
        //
        final String error = ex.getRequestPartName() + " part is missing";
        final ApiError apiError = new ApiError(HttpStatus.BAD_REQUEST, ex.getLocalizedMessage(), error);
        return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus());
    }

    @Override
    protected ResponseEntity<Object> handleMissingServletRequestParameter(final MissingServletRequestParameterException ex, final HttpHeaders headers, final HttpStatus status, final WebRequest request) {
        logger.info(ex.getClass().getName());
        //
        final String error = ex.getParameterName() + " parameter is missing";
        final ApiError apiError = new ApiError(HttpStatus.BAD_REQUEST, ex.getLocalizedMessage(), error);
        return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus());
    }

    //

    @ExceptionHandler({ MethodArgumentTypeMismatchException.class })
    public ResponseEntity<Object> handleMethodArgumentTypeMismatch(final MethodArgumentTypeMismatchException ex, final WebRequest request) {
        logger.info(ex.getClass().getName());
        //
        final String error = ex.getName() + " should be of type " + ex.getRequiredType().getName();

        final ApiError apiError = new ApiError(HttpStatus.BAD_REQUEST, ex.getLocalizedMessage(), error);
        return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus());
    }

    @ExceptionHandler({ ConstraintViolationException.class })
    public ResponseEntity<Object> handleConstraintViolation(final ConstraintViolationException ex, final WebRequest request) {
        logger.info(ex.getClass().getName());
        //
        final List<String> errors = new ArrayList<>();
        for (final ConstraintViolation<?> violation : ex.getConstraintViolations()) {
            errors.add(violation.getRootBeanClass().getName() + " " + violation.getPropertyPath() + ": " + violation.getMessage());
        }

        final ApiError apiError = new ApiError(HttpStatus.BAD_REQUEST, ex.getLocalizedMessage(), errors);
        return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus());
    }

    // 404

    @Override
    protected ResponseEntity<Object> handleNoHandlerFoundException(final NoHandlerFoundException ex, final HttpHeaders headers, final HttpStatus status, final WebRequest request) {
        logger.info(ex.getClass().getName());
        //
        final String error = "No handler found for " + ex.getHttpMethod() + " " + ex.getRequestURL();

        final ApiError apiError = new ApiError(HttpStatus.NOT_FOUND, ex.getLocalizedMessage(), error);
        return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus());
    }

    // 405

    @Override
    protected ResponseEntity<Object> handleHttpRequestMethodNotSupported(final HttpRequestMethodNotSupportedException ex, final HttpHeaders headers, final HttpStatus status, final WebRequest request) {
        logger.info(ex.getClass().getName());
        //
        final StringBuilder builder = new StringBuilder();
        builder.append(ex.getMethod());
        builder.append(" method is not supported for this request. Supported methods are ");
        ex.getSupportedHttpMethods().forEach(t -> builder.append(t + " "));

        final ApiError apiError = new ApiError(HttpStatus.METHOD_NOT_ALLOWED, ex.getLocalizedMessage(), builder.toString());
        return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus());
    }

    // 415

    @Override
    protected ResponseEntity<Object> handleHttpMediaTypeNotSupported(final HttpMediaTypeNotSupportedException ex, final HttpHeaders headers, final HttpStatus status, final WebRequest request) {
        logger.info(ex.getClass().getName());
        //
        final StringBuilder builder = new StringBuilder();
        builder.append(ex.getContentType());
        builder.append(" media type is not supported. Supported media types are ");
        ex.getSupportedMediaTypes().forEach(t -> builder.append(t + " "));

        final ApiError apiError = new ApiError(HttpStatus.UNSUPPORTED_MEDIA_TYPE, ex.getLocalizedMessage(), builder.substring(0, builder.length() - 2));
        return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus());
    }

    @ExceptionHandler({ DeeplinkAuthenticationException.class })
    public ResponseEntity<Object> handleAll(final DeeplinkAuthenticationException ex, final WebRequest request) {
        logger.error("authentication error", ex);
        final ApiError apiError = new ApiError(ex.getCode(), ex.getMessage(), ERROR_OCCURRED);
        return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus());
    }
    
    @ExceptionHandler({ CampaignException.class })
    public ResponseEntity<Object> handleAll(final CampaignException ex, final WebRequest request) {
        logger.error("Campaign Exception", ex);
		final ApiError apiError = new ApiError(HttpStatus.INTERNAL_SERVER_ERROR, ex.getCode().getValue(),
				ex.getMessage(), ERROR_OCCURRED);
        return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus());
    }
    
    @ExceptionHandler({ CampaignQuickSendException.class })
	public ResponseEntity<Object> handleQuickSendException(final CampaignQuickSendException ex, final WebRequest request) {
		logger.error("Campaign Quick Send Exception", ex);
		final ApiError apiError = new ApiError(HttpStatus.INTERNAL_SERVER_ERROR, ex.getCode().getValue(), ex.getMessage(), ERROR_OCCURRED);
		return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus());

	}
    @ExceptionHandler({ CampaignExternalServiceException.class })
    public ResponseEntity<Object> handleExternalServiceException(final CampaignExternalServiceException ex, final WebRequest request) {
    	logger.error("External Service Exception", ex);
    	final ApiError apiError = new ApiError(HttpStatus.INTERNAL_SERVER_ERROR, ex.getCode().getValue(), ex.getMessage(), ERROR_OCCURRED);
    	return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus());
    }
    
    @ExceptionHandler({ RuleCreationException.class })
    public ResponseEntity<Object> handleRuleCreationException(final RuleCreationException ex, final WebRequest request) {
    	logger.error("Rule creation Exception", ex);
    	final ApiError apiError = new ApiError(HttpStatus.INTERNAL_SERVER_ERROR, ex.getCode().getValue(),ex.getMessage(), ERROR_OCCURRED);
    	return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus());
    	
    }
    
    // 500
    @ExceptionHandler({ Exception.class })
    public ResponseEntity<Object> handleAll(final Exception ex, final WebRequest request) {
        logger.error("Generic exception", ex);
        //
        final ApiError apiError = new ApiError(HttpStatus.INTERNAL_SERVER_ERROR, ex.getLocalizedMessage(), ERROR_OCCURRED);
        return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus());
    }

    /**
     * https://birdeye.atlassian.net/browse/BIRD-121928
     */
    @ExceptionHandler({ RuntimeException.class })
    public ResponseEntity<Object> handleAll(final RuntimeException ex, final WebRequest request) {
        logger.error("Runtime error", ex);
        // Retrun localised message for exceptions defined in com.birdeye... package
        if (ex.getClass().getPackage() != null && ex.getClass().getPackage().getName().startsWith("com.birdeye")) {
            final ApiError apiError = new ApiError(HttpStatus.INTERNAL_SERVER_ERROR, ex.getLocalizedMessage(), ERROR_OCCURRED);
            return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus());
        }
        // Retrun ISE message for exceptions not defined in com.birdeye... package
        final ApiError apiError = new ApiError(HttpStatus.INTERNAL_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase(), ERROR_OCCURRED);
        return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus());
    }
    
    @ExceptionHandler({ ReviewGenException.class })
    public ResponseEntity<Object> handleAll(final ReviewGenException ex, final WebRequest request) {
        logger.error("ReviewGen error", ex);
        final ApiError apiError = new ApiError(HttpStatus.BAD_REQUEST, ex.getCode(), ex.getMessage(), ERROR_OCCURRED, ex.getCode());
        return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus());
    }

    @ExceptionHandler({ CampaignBadRequestException.class })
    public ResponseEntity<Object> campaignBadRequest(final CampaignBadRequestException ex, final WebRequest request) {
        logger.error("bad request error", ex);
        final ApiError apiError = new ApiError(HttpStatus.BAD_REQUEST, ex.getCode().getValue(),
                ex.getMessage(), ex.getMessage());
        return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus());
    }

    @ExceptionHandler({ ReferralPublicFormException.class })
    public ResponseEntity<Object> referralPublicFormException(final ReferralPublicFormException ex, final WebRequest request) {
        logger.error("Appointment referral form error", ex);
        final ApiError apiError = new ApiError(HttpStatus.BAD_REQUEST, ex.getCode(), ex.getMessage(), ERROR_OCCURRED, ex.getCode());
        return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus());
    }

}