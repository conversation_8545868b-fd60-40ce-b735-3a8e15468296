package com.birdeye.campaign.config;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.stream.Collectors;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.birdeye.campaign.amazon.service.impl.SecretsLoader;
import com.birdeye.campaign.aspect.annotation.ReadOnlyRepository;
import com.birdeye.campaign.constant.Constants;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(entityManagerFactoryRef = "platformReplicaEntityManagerFactory", transactionManagerRef = "platformReplicaTransactionManager", 
	basePackages = "com.birdeye.campaign.platform.readonly.repository", includeFilters = @ComponentScan.Filter(ReadOnlyRepository.class))
public class PlatformReplicaDataSourceConfig {
	
	@Autowired(required = false)
	private SecretsLoader secretsLoader;

	@Value("${aws.secret.enabled}")
	private String enabled;
	
	@Bean("platformReplicaHikariConfig")
	@ConfigurationProperties(prefix = "spring.datasource.platform.replica")
	public HikariConfig hikariConfig() {
		return new HikariConfig();
	}
	
	@Bean("platformReplicaDatasource")
	public DataSource dataSource() {
		return new HikariDataSource(updateHikariConfig());
	}
	
	@Bean(name = "platformReplicaEntityManagerFactory")
	public LocalContainerEntityManagerFactoryBean mysqlEntityManagerFactory(EntityManagerFactoryBuilder builder) {
		return builder.dataSource(dataSource()).properties(hibernateProperties()).packages("com.birdeye.campaign.platform.entity").persistenceUnit("platformReplicaPU").build();
	}
	
	@Bean(name = "platformReplicaTransactionManager")
	public PlatformTransactionManager mysqlTransactionManager(@Qualifier("platformReplicaEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
		return new JpaTransactionManager(entityManagerFactory);
	}
	
	private Map<String, Object> hibernateProperties() {
		Resource resource = new ClassPathResource("hibernate.properties");
		try {
			Properties properties = PropertiesLoaderUtils.loadProperties(resource);
			return properties.entrySet().stream().collect(Collectors.toMap(e -> e.getKey().toString(), e -> e.getValue()));
		} catch (IOException e) {
			return new HashMap<>();
		}
	}
	
//	@ConfigurationProperties(prefix = "spring.datasource.platform")
	private HikariConfig updateHikariConfig() {
		HikariConfig hikariConfig = hikariConfig();
		if (Boolean.valueOf(enabled) && MapUtils.isNotEmpty(secretsLoader.getSecretsMap())) {
			hikariConfig.setJdbcUrl(
					getJdbcUrl(Constants.BAZAARIFY_SLAVE_JDBC_URL, Constants.BAZAARIFY_SLAVE_HOST_NAME, Constants.BAZAARIFY_SLAVE_DB_NAME, Constants.BAZAARIFY_SLAVE_DB_PORT));
			hikariConfig.setUsername(secretsLoader.getSecretsMap().get(Constants.BAZAARIFY_SLAVE_DB_USERNAME));
			hikariConfig.setPassword(secretsLoader.getSecretsMap().get(Constants.BAZAARIFY_SLAVE_DB_PASSWORD));
		}
		return hikariConfig;
	}
	
	private String getJdbcUrl(String jdbcUrl, String host, String dbName, String port) {
		String dbHost = secretsLoader.getSecretsMap().get(host);
		String dataBaseName = secretsLoader.getSecretsMap().get(dbName);
		String dbport = secretsLoader.getSecretsMap().get(port);
		return String.format(jdbcUrl, dbHost, dbport, dataBaseName);
	}	
}
