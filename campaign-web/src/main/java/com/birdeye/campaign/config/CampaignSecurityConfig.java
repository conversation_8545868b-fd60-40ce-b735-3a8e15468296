package com.birdeye.campaign.config;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.firewall.DefaultHttpFirewall;
import org.springframework.security.web.firewall.HttpFirewall;



@EnableWebSecurity
public class CampaignSecurityConfig extends WebSecurityConfigurerAdapter {

	@Autowired 
	DataSource campaignDatasource;
	
	@Override
	protected void configure(AuthenticationManagerBuilder auth) throws Exception{

		auth.jdbcAuthentication().dataSource(campaignDatasource);
	}
	
	@Override
	protected void configure(HttpSecurity http) throws Exception{
		http.authorizeRequests().
		antMatchers("/swagger-ui.html").hasRole("ADMIN").
		antMatchers("/actuator/**").hasRole("ADMIN").
		antMatchers("/monitoring").hasRole("ADMIN").
		antMatchers("/").permitAll().and().formLogin();
		
		http.csrf().disable();
	}
	
	@Bean 
	public PasswordEncoder getPasswordEncoder() {
		return new BCryptPasswordEncoder(); 
	}
	
	@Bean
	public HttpFirewall getHttpFirewall() {
	    return new DefaultHttpFirewall();
	}
	
}
