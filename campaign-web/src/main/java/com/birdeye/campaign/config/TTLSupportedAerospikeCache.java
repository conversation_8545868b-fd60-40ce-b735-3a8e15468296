/**
 * @file_name TTLSupportedAerospikeCache.java
 * @created_date 27 May 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.support.SimpleValueWrapper;
import org.springframework.data.aerospike.cache.AerospikeCache;

import com.aerospike.client.AerospikeClient;
import com.aerospike.client.AerospikeException;
import com.aerospike.client.Bin;
import com.aerospike.client.Record;
import com.aerospike.client.Value.MapValue;
import com.aerospike.client.policy.RecordExistsAction;

/**
 * @file_name TTLSupportedAerospikeCache.java
 * @created_date 27 May 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public class TTLSupportedAerospikeCache extends AerospikeCache {
	
	private static final Logger	LOGGER	= LoggerFactory.getLogger(TTLSupportedAerospikeCache.class);
	
	private static final String	VALUE	= "value";
	
	/**
	 * @param namespace
	 * @param set
	 * @param client
	 * @param expiration
	 */
	public TTLSupportedAerospikeCache(String namespace, String set, AerospikeClient client, long expiration) {
		super(namespace, set, client, expiration);
		this.createOnly.expiration = (int) expiration;
		this.createOnly.recordExistsAction = RecordExistsAction.UPDATE;
	}
	
	@Override
	public void put(Object key, Object value) {
		try {
			client.put(this.createOnly, getKey(key), new Bin(VALUE, MapValue.get(value)));
		} catch (AerospikeException e) {
			LOGGER.error("Aerospike Cache PUT FAILED for set {} key {} VALUE {} and Exception Aerospike {}", this.set, key, value, e.getMessage());
		} catch (Exception e) {
			LOGGER.error("Aerospike Cache PUT FAILED for set {} key {} VALUE {} and Exception {}", this.set, key, value, e.getMessage());
		}
	}
	
	@Override
	public ValueWrapper get(Object key) {
		ValueWrapper vr = null;
		try {
			Record record = client.get(null, getKey(key));
			vr = toWrapper(record);
			if (vr.get() != null) {
				return vr;
			} else {
				//LOGGER.info("Aerospike Cache GET MISS for set {} key {} as value is null", this.set, key);
				
			}
		} catch (AerospikeException e) {
			LOGGER.error("Aerospike Cache GET FAILED for set {} key {} and Exception Aerospike {}", this.set, key, e.getMessage());
		} catch (Exception e) {
			LOGGER.error("Aerospike Cache GET FAILED for set {} key {} and Exception {}", this.set, key, e.getMessage());
		}
		return null;
	}
	
	private ValueWrapper toWrapper(Record record) {
		return (record != null ? new SimpleValueWrapper(record.getValue(VALUE)) : new SimpleValueWrapper(null));
	}
	
	
}
