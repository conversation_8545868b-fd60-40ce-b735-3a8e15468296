package com.birdeye.campaign.config;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.aop.interceptor.SimpleAsyncUncaughtExceptionHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import com.birdeye.campaign.constant.Constants;

@Configuration
@EnableAsync
@PropertySource("classpath:threadpool.properties")
public class AsyncThreadPoolConfig implements AsyncConfigurer {

	@Autowired
	private Environment env;

	// default Async executor
	@Override
	public Executor getAsyncExecutor() 
	{
		return threadPoolTaskExecutor();
	}

	private Executor threadPoolTaskExecutor() 
	{
		ThreadPoolTaskExecutor threadPoolExecutor = new ThreadPoolTaskExecutor();
		threadPoolExecutor.setCorePoolSize(env.getProperty("async.core.pool.size", Integer.class, 50));
		threadPoolExecutor.setMaxPoolSize(env.getProperty("async.max.pool.size", Integer.class, 300));
		threadPoolExecutor.setQueueCapacity(env.getProperty("async.pool.queue.size", Integer.class, 2000));
		threadPoolExecutor.setKeepAliveSeconds(env.getProperty("async.keep.alive.seconds", Integer.class, 60));
		threadPoolExecutor.setTaskDecorator(new MdcTaskDecorator());
		threadPoolExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
		threadPoolExecutor.setThreadNamePrefix("CampaignAsyncTask-");
		threadPoolExecutor.initialize();
		return threadPoolExecutor;
	}
	
	@Override
	public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
		return new SimpleAsyncUncaughtExceptionHandler();
	}
	
	@Bean(name = Constants.CAMPAIGN_AMAZONS3_TASK_EXECUTOR)
	public Executor amazonS3TaskExecutor() 
	{
		ThreadPoolTaskExecutor threadPoolExecutor = new ThreadPoolTaskExecutor();
		threadPoolExecutor.setCorePoolSize(env.getProperty("amazons3.core.pool.size", Integer.class, 20));
		threadPoolExecutor.setMaxPoolSize(env.getProperty("amazons3.max.pool.size", Integer.class, 60));
		threadPoolExecutor.setQueueCapacity(env.getProperty("amazons3.pool.queue.size", Integer.class, 500));
		threadPoolExecutor.setKeepAliveSeconds(env.getProperty("amazons3.keep.alive.seconds", Integer.class, 60));
		threadPoolExecutor.setTaskDecorator(new MdcTaskDecorator());
		threadPoolExecutor.setThreadNamePrefix("CampaignAmazonS3Task-");
		threadPoolExecutor.initialize();
		return threadPoolExecutor;
	}
	
	@Bean(name = Constants.CAMPAIGN_RUN_RR_TASK_EXECUTOR)
	public Executor runRRTaskExecutor() {
		ThreadPoolTaskExecutor threadPoolExecutor = new ThreadPoolTaskExecutor();
		threadPoolExecutor.setCorePoolSize(env.getProperty("runRR.core.pool.size", Integer.class, 50));
		threadPoolExecutor.setMaxPoolSize(env.getProperty("runRR.max.pool.size", Integer.class, 300));
		threadPoolExecutor.setQueueCapacity(env.getProperty("runRR.pool.queue.size", Integer.class, 2000));
		threadPoolExecutor.setKeepAliveSeconds(env.getProperty("runRR.keep.alive.seconds", Integer.class, 60));
		threadPoolExecutor.setTaskDecorator(new MdcTaskDecorator());
		threadPoolExecutor.setThreadNamePrefix("CampaignRunRRTask-");
		threadPoolExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
		threadPoolExecutor.initialize();
		return threadPoolExecutor;
	}
	
	@Bean(name = Constants.QUICK_SEND_RR_TASK_EXECUTOR)
	public Executor quickSendRRTaskExecutor() {
		ThreadPoolTaskExecutor threadPoolExecutor = new ThreadPoolTaskExecutor();
		threadPoolExecutor.setCorePoolSize(env.getProperty("quick-send.core.pool.size", Integer.class, 50));
		threadPoolExecutor.setMaxPoolSize(env.getProperty("quick-send.max.pool.size", Integer.class, 300));
		threadPoolExecutor.setQueueCapacity(env.getProperty("quick-send.pool.queue.size", Integer.class, 2000));
		threadPoolExecutor.setKeepAliveSeconds(env.getProperty("quick-send.keep.alive.seconds", Integer.class, 60));
		threadPoolExecutor.setTaskDecorator(new MdcTaskDecorator());
		threadPoolExecutor.setThreadNamePrefix("QuickSendRRTaskExecutor-");
		threadPoolExecutor.initialize();
		return threadPoolExecutor;
	}
	
}
