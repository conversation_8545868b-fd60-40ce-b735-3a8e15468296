/**
 *
 */
package com.birdeye.campaign.config;

import java.util.concurrent.ThreadFactory;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import com.birdeye.campaign.constant.Constants;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

/**
 ** File: ThreadPoolTaskExecutorConfig.java
 ** Created: 3 Jan 2019
 ** Author: puneetgupta
 **
 ** This code is copyright (c) BirdEye Software India Pvt. Ltd.
 **/
@Configuration
@PropertySource("classpath:threadpool.properties")
public class ThreadPoolTaskExecutorConfig {

	private static final String COMPETABLE_FUTURE_EXECUTOR_NAME = "CampaignCompletableExecutor";
	
	private static final String	DT_COMPETABLE_FUTURE_EXECUTOR_NAME	= "CampaignDefaultTemplateCompletableExecutor";
	
	@Bean(name = Constants.CAMPAIGN_COMPLETABLE_FUTURE_TASK_EXECUTOR)
	public ThreadPoolTaskExecutor threadPoolCompletableFutureTaskExecutor(@Value("${completable.max.pool.size}") Integer maxPoolSize,
			@Value("${completable.keep.alive.seconds}") Integer keepAliveSeconds, @Value("${completable.core.pool.size}") Integer corePoolSize,
			@Value("${completable.threadpool.queue.capacity}") Integer queueCapacity) {
		ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
		ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(COMPETABLE_FUTURE_EXECUTOR_NAME).build();
		threadPoolTaskExecutor.setThreadFactory(threadFactory);
		threadPoolTaskExecutor.setCorePoolSize(corePoolSize);
		threadPoolTaskExecutor.setMaxPoolSize(maxPoolSize);
		threadPoolTaskExecutor.setKeepAliveSeconds(keepAliveSeconds);
		threadPoolTaskExecutor.setTaskDecorator(new MdcTaskDecorator());
		threadPoolTaskExecutor.setQueueCapacity(queueCapacity);
		return threadPoolTaskExecutor;
	}
	
	@Bean(name = Constants.CAMPAIGN_UI_COMPLETABLE_FUTURE_TASK_EXECUTOR)
	public ThreadPoolTaskExecutor threadPoolCompletableFutureForUI(@Value("${completable.ui.max.pool.size}") Integer maxPoolSize,
			@Value("${completable.ui.keep.alive.seconds}") Integer keepAliveSeconds, @Value("${completable.ui.core.pool.size}") Integer corePoolSize,
			@Value("${completable.ui.threadpool.queue.capacity}") Integer queueCapacity) {
		ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
		ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(COMPETABLE_FUTURE_EXECUTOR_NAME).build();
		threadPoolTaskExecutor.setThreadFactory(threadFactory);
		threadPoolTaskExecutor.setCorePoolSize(corePoolSize);
		threadPoolTaskExecutor.setMaxPoolSize(maxPoolSize);
		threadPoolTaskExecutor.setKeepAliveSeconds(keepAliveSeconds);
		threadPoolTaskExecutor.setTaskDecorator(new MdcTaskDecorator());
		threadPoolTaskExecutor.setQueueCapacity(queueCapacity);
		return threadPoolTaskExecutor;
	}
	
	@Bean(name = Constants.CAMPAIGN_REPORTING_TASK_EXECUTOR)
	public ThreadPoolTaskExecutor threadPoolCompletableFutureForReporting(@Value("${reporting.max.pool.size}") Integer maxPoolSize,
			@Value("${reporting.keep.alive.seconds}") Integer keepAliveSeconds, @Value("${reporting.core.pool.size}") Integer corePoolSize,
			@Value("${reporting.threadpool.queue.capacity}") Integer queueCapacity) {
		ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
		ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(COMPETABLE_FUTURE_EXECUTOR_NAME).build();
		threadPoolTaskExecutor.setThreadFactory(threadFactory);
		threadPoolTaskExecutor.setCorePoolSize(corePoolSize);
		threadPoolTaskExecutor.setMaxPoolSize(maxPoolSize);
		threadPoolTaskExecutor.setKeepAliveSeconds(keepAliveSeconds);
		threadPoolTaskExecutor.setTaskDecorator(new MdcTaskDecorator());
		threadPoolTaskExecutor.setQueueCapacity(queueCapacity);
		return threadPoolTaskExecutor;
	}

	@Bean(name = Constants.CAMPAIGN_DT_COMPLETABLE_FUTURE_TASK_EXECUTOR)
	public ThreadPoolTaskExecutor threadPoolCompletableFutureForDT(@Value("${completable.dt.max.pool.size}") Integer maxPoolSize,
			@Value("${completable.dt.keep.alive.seconds}") Integer keepAliveSeconds, @Value("${completable.dt.core.pool.size}") Integer corePoolSize,
			@Value("${completable.dt.threadpool.queue.capacity}") Integer queueCapacity) {
		ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
		ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(DT_COMPETABLE_FUTURE_EXECUTOR_NAME).build();
		threadPoolTaskExecutor.setThreadFactory(threadFactory);
		threadPoolTaskExecutor.setCorePoolSize(corePoolSize);
		threadPoolTaskExecutor.setMaxPoolSize(maxPoolSize);
		threadPoolTaskExecutor.setKeepAliveSeconds(keepAliveSeconds);
		threadPoolTaskExecutor.setTaskDecorator(new MdcTaskDecorator());
		threadPoolTaskExecutor.setQueueCapacity(queueCapacity);
		return threadPoolTaskExecutor;
	}
}
