/**
 * @file_name AerospikeCacheConfig.java
 * @created_date 27 May 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.config;

import java.io.IOException;
import java.util.Collection;
import java.util.Collections;
import java.util.Properties;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.cache.support.AbstractCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.util.ResourceUtils;

import com.aerospike.client.AerospikeClient;
import com.aerospike.client.Host;
import com.aerospike.client.Info;
import com.aerospike.client.Language;
import com.aerospike.client.policy.ClientPolicy;
import com.aerospike.client.policy.Policy;
import com.aerospike.client.policy.WritePolicy;

/**
 * @file_name AerospikeCacheConfig.java
 * @created_date 27 May 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
@Configuration
@PropertySource("classpath:cache.properties")
@ConditionalOnProperty(prefix = "aerospike.cache", value = "enabled", havingValue = "true")
public class AerospikeCacheConfig {
	
	private static final Logger	LOGGER	= LoggerFactory.getLogger(AerospikeCacheConfig.class);
	

	@Autowired
	private Environment env;
	
	@Bean
	public CacheManager aerospikeCacheManager(){
        final String nameSpace = nameSpace();
        AbstractCacheManager cacheManager = new AbstractCacheManager() {
        	@Override
			public Collection<? extends Cache> loadCaches() {
					return cache();
			}
			
			@Override
			public Cache getCache(String name) {
				return lookupCache(nameSpace + ":" + name);
			}

		};
		cacheManager.afterPropertiesSet();
		return cacheManager;
	}
	
	@Bean(name="aerospikeClient",destroyMethod="close")
	public AerospikeClient aerospikeClient() {
		AerospikeClient asClient = new AerospikeClient(clientPolicy(), getHosts());
		checkUdfRegistration(asClient);
		return asClient;
	}
	private void checkUdfRegistration(AerospikeClient asClient) {
		if (asClient == null)
			return;
		String modules = info(asClient, "udf-list");
		if (modules.contains("campaign-udf-v1.lua"))
			return;
		try {
			String file = ResourceUtils.getFile("classpath:udf/campaign-udf-v1.lua").getCanonicalPath();
			asClient.register(null, file, "campaign-udf-v1.lua", Language.LUA);
		} catch (Exception exe) {
			LOGGER.error("error occurs while UDF registration error :: {}", exe);
		}
	}
	private String info(AerospikeClient asClient, String infoString) {
		if (asClient != null && asClient.isConnected()) {
			return Info.request(asClient.getNodes()[0], infoString);
		} else {
			return "Client not connected";
		}
	}
	
	private ClientPolicy clientPolicy() {
		ClientPolicy policy = new ClientPolicy();
		policy.timeout = 10000;
		WritePolicy writePolicy = new WritePolicy();
		writePolicy.socketTimeout=10000;
		writePolicy.maxRetries = 2;
		writePolicy.totalTimeout=15000;
		policy.writePolicyDefault = writePolicy;
		
		Policy readPolicy  = new Policy();
		readPolicy.maxRetries=0; //DB Call will be hit, so opting for no retry
		readPolicy.socketTimeout=5000;
		policy.readPolicyDefault=readPolicy;
		return policy;
	}
	
	private Host[] getHosts() {
		String hostString = env.getProperty("aerospike.hosts", String.class, "localhost:3000");
		String[] hostAndIps = hostString.split(",");
		Host[]  hosts =  new Host[hostAndIps.length];
		int index =0;
		for(String hostAndIp  : hostAndIps) {
			if(StringUtils.isNotEmpty(hostAndIp)){
				String[] host = hostAndIp.split(":");
				if(host.length == 2) {
					hosts[index++] = new Host(host[0],Integer.valueOf(host[1]));
				}
			}
		}
		return hosts;
	}


	
	public String nameSpace() {
		return env.getProperty("aerospike.namespace", "campaigns");
	}
	
	
	private Set<? extends Cache> cache(){
		Resource resource = new ClassPathResource("cache.properties");
		Properties props;
		try {
			props = PropertiesLoaderUtils.loadProperties(resource);
		} catch (IOException e) {
			LOGGER.error("Exception while loading cache properties: {}",e);
			return Collections.emptySet();
		}
		final String namespace = nameSpace();
		AerospikeClient aerospikeClient = aerospikeClient();
		return props.entrySet().stream().filter(entry -> entry.getKey() != null && entry.getValue() != null)
				.map(entry -> new TTLSupportedAerospikeCache(namespace, String.valueOf(entry.getKey()).trim(), aerospikeClient, Long.valueOf(String.valueOf(entry.getValue()).trim())))
				.collect(Collectors.toSet());
	}
	
	@Bean
    public CacheErrorHandler cacheErrorHandler() {
        return new AerospikeCacheErrorHandler();
    }

    public static class AerospikeCacheErrorHandler implements CacheErrorHandler {

        @Override
        public void handleCacheGetError(RuntimeException exception, Cache cache, Object key) {
            LOGGER.info("Unable to get from cache {} : {} : {}", cache.getName(), key, exception.getMessage());
        }

        @Override
        public void handleCachePutError(RuntimeException exception, Cache cache, Object key, Object value) {
        	LOGGER.info("Unable to put into cache {} : {} : {}", cache.getName(), key, exception.getMessage());
        }

        @Override
        public void handleCacheEvictError(RuntimeException exception, Cache cache, Object key) {
        	LOGGER.info("Unable to evict from cache {} : {} : {}", cache.getName(), key, exception.getMessage());
        }

        @Override
        public void handleCacheClearError(RuntimeException exception, Cache cache) {
        	LOGGER.info("Unable to clean cache {} : {}", cache.getName(), exception.getMessage());
        }
    }
	
}
