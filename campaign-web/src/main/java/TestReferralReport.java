import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;

/**
 * Utility to write TMX referral data.
 */
public class TestReferralReport {

	private static final String REF_ID = "Request ID";
	private static final String BIZ_ID = "Business ID";
	private static final String ALIAS = "Business Alias";
	private static final String CMP_ID = "Campaign ID";
	private static final String CID = "Customer ID";
	private static final String CNAME = "Customer Name";
	private static final String CPHONE = "Customer Phone";
	private static final String CEMAIL = "Customer Email";
	private static final String CSTM_VNO = "Vergent Customer ID";
	private static final String CSTM_MDM = "MDM ID";
	private static final String CSTM_REFCODE = "Referral Code";
	private static final String SRC = "Request Source";
	private static final String REF_DATE = "Request Sent Date (PST)";
	private static final String SHARED_SOURCES = "Shared Source (1:FB, 2:FB-MSNGR, 3:EMAIL, 4:TEXT)";
	private static final String SHARED_TIME = "Shared Time (PST)";


	public static void main(String[] args) throws JsonParseException, JsonMappingException, IOException {
		long start = System.currentTimeMillis();
		ObjectMapper mapper = new ObjectMapper();

		Map<?, ?> map = mapper.readValue(new File("/Users/<USER>/Desktop/tmx10nov.json"), Map.class);

		Object obj = map.get("hits");
		final List<Map<String, String>> rows = new ArrayList<>();
		if (obj instanceof List) {
			List<?> docs = (List<?>) obj;

			try {
				docs.forEach(doc -> {
					if (doc instanceof Map) {
						rows.add(processDoc((Map<?, ?>) doc));
					}
				});

			} catch (Exception e) {
				System.err.println("Error in processing tmx data");
			}
		}
		System.out.println("=======================================================================================");
		System.out.println("Total requests: " + rows.size() + " and time taken in seconds: "
				+ (System.currentTimeMillis() - start) / 1000);
		System.out.println(
				"Total unique Shares: " + rows.stream().filter(r -> (r.get("Referral Sources") != null)).count());

		System.out.println("Total Shares: " + rows.stream().map(r -> getShareCount(r.get("Referral Sources")))
				.collect(Collectors.summingInt(Integer::intValue)));
		System.out.println("=======================================================================================");
		start = System.currentTimeMillis();

		List<String> customerIds = rows.stream().map(m -> m.get(CID)).collect(Collectors.toList());
		Map<String, Map<String, String>> kontactoData = getKontactoData(customerIds);

		// Merge data.
		rows.stream().forEach(r -> {
			String cid = r.get(CID);
			Map<String, String> customerInfo = kontactoData.get(cid);
			if (customerInfo != null) {
				r.putAll(customerInfo);
			}
		});
		start = System.currentTimeMillis();
		writeExcelFile(rows);
		System.out.println("Time taken in writting file in seconds: " + ((System.currentTimeMillis() - start) / 1000));
	}

	static void writeExcelFile(List<Map<String, String>> rows) {
		try {
			Workbook workbook = new XSSFWorkbook();

			Sheet sheet = workbook.createSheet("Referrals");
			Row header = sheet.createRow(0);
			CellStyle headerStyle = workbook.createCellStyle();
			createHeaders(header, headerStyle);

			// Data
			CellStyle style = workbook.createCellStyle();
			style.setWrapText(true);
			for (int i = 0; i < rows.size(); i++) {
				Row excelRow = sheet.createRow(i + 1);
				createRow(excelRow, style, rows.get(i));
			}
			File currDir = new File(".");
			String path = currDir.getAbsolutePath();
			String fileLocation = path.substring(0, path.length() - 1) + "tmx1.xlsx";

			FileOutputStream outputStream = new FileOutputStream(fileLocation);
			workbook.write(outputStream);
			workbook.close();
		} catch (Exception e) {
			System.err.println("Error writting excel file");
			e.printStackTrace();
		}
	}

	private static void createRow(Row excelRow, CellStyle cellStyle, Map<String, String> data) {
		for (int i = 0; i < columns.length; i++) {
			Cell cell = excelRow.createCell(i);
			cell.setCellStyle(cellStyle);
			cell.setCellValue(data.get(columns[i]));
		}
	}

	static final String[] columns = new String[] { REF_ID, BIZ_ID, ALIAS, CMP_ID, CID, CNAME, CPHONE, CEMAIL, SRC,
			REF_DATE, SHARED_SOURCES, SHARED_TIME, CSTM_MDM, CSTM_REFCODE, CSTM_VNO };

	private static void createHeaders(Row header, CellStyle headerStyle) {
		headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
		headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		for (int i = 0; i < columns.length; i++) {
			Cell headerCell = header.createCell(i);
			headerCell.setCellStyle(headerStyle);
			headerCell.setCellValue(columns[i]);
		}
	}

	private static int getShareCount(String sources) {
		if (sources != null && !sources.isEmpty()) {
			return sources.split(",").length;
		}
		return 0;
	}

	/**
	 * // Referral Doc
	 * 
	 * "_source": { "requestId": ***********, "businessId": 724679, "enterpriseId":
	 * 717879, "campaignId": 723934, "templateId": 1254098, "requestDate":
	 * "2020-11-04 16:28:51", "customerId": 127053905, "customerName": "JAMIE LEE",
	 * "businessAlias": "30142 -Clayton GA 1 - Hwy 441 S", "updatedAt":
	 * 1604507331000, "reminderCount": 0, "clickTime": 1604507348000, "status": 1,
	 * "source": "sms", "opened": 1, "openMobile": 1, "openTab": 0, "openWeb": 0,
	 * "sourceClicks": [ { "sourceId": 1, "clickCount": 1, "clickTime":
	 * 1604507348000 } ], "clicked": 1, "lastClickedSourceId": 1 }
	 * 
	 */

	@SuppressWarnings("unchecked")
	static Map<String, String> processDoc(Map<?, ?> doc) {
		Map<String, String> row = new HashMap<>();
		Map<?, ?> source = (Map<?, ?>) doc.get("_source");
		row.put(REF_ID, asText(source.get("requestId")));
		row.put(BIZ_ID, asText(source.get("businessId")));
		row.put(ALIAS, asText(source.get("businessAlias")));
		row.put(CMP_ID, asText(source.get("campaignId")));
		row.put(CID, asText(source.get("customerId")));
		row.put(CNAME, asText(source.get("customerName")));
		row.put(SRC, asText(source.get("source")));

		String referralDate = source.get("requestDate") != null ? source.get("requestDate").toString() : null;
		if (referralDate != null) {
			row.put(REF_DATE, getPSTDateFromUTCString(source.get("requestDate").toString()));
		}

		List<Map<?, ?>> sourceClicks = (List<Map<?, ?>>) source.get("sourceClicks");
		if (sourceClicks != null && !sourceClicks.isEmpty()) {
			row.put(SHARED_SOURCES,
					sourceClicks.stream().map(s -> s.get("sourceId").toString()).collect(Collectors.joining(",")));
			String lastSharedTime = source.get("clickTime") != null ? source.get("clickTime").toString() : null;
			if (lastSharedTime != null) {
				row.put(SHARED_TIME, getPSTDate(source.get("clickTime").toString()));
			}
		}
		
		return row;
	}

	private static String asText(Object obj) {
		return obj == null ? "" : obj.toString();
	}

	//Input - 1604531637076
	private static String getPSTDate(String date) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/yyyy - HH:mm:ss");
		ZonedDateTime zonedDateTime = Instant.ofEpochMilli(Long.valueOf(date)).atZone(ZoneId.of("America/Los_Angeles"));
		return zonedDateTime.format(formatter);
	}

	
	//Input - 2020-11-04 23:12:00
	private static String getPSTDateFromUTCString(String date) {
		DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		Instant inputDate = LocalDateTime.from(inputFormatter.parse(date)).toInstant(ZoneOffset.UTC);
		
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/yyyy - HH:mm:ss");
		ZonedDateTime zonedDateTime = inputDate.atZone(ZoneId.of("America/Los_Angeles"));
		return zonedDateTime.format(formatter);
	}
	
	static CloseableHttpClient httpClient = HttpClients.createDefault();

	private static Map<String, Map<String, String>> getKontactoData(List<String> customerIds) {
		int size = 5000;
		long start = System.currentTimeMillis();
		System.out
				.println("Calling Kontacto with batch size of :: " + size + " for total data :: " + customerIds.size());
		Map<String, Map<String, String>> output = new ConcurrentHashMap<>();
		Lists.partition(customerIds, size).forEach(l -> {
			getCustomerDetailsInBatch(l, output);
		});
		System.out
				.println("Total time taken from Kontacto in seconds :: " + (System.currentTimeMillis() - start) / 1000);
		return output;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	private static void getCustomerDetailsInBatch(List<?> customerIds, Map<String, Map<String, String>> result) {
		CloseableHttpResponse response = null;
		try {
			ObjectMapper om = new ObjectMapper();
			HttpPost request = new HttpPost("http://kontacto.birdeye.com/customer/getByIds?customField=true");
			request.setHeader("Accept", "application/json");
			request.setHeader("Content-type", "application/json");
			request.setEntity(new StringEntity(om.writeValueAsString(customerIds)));
			response = httpClient.execute(request);
			HttpEntity entity = response.getEntity();
			if (entity != null) {
				// return it as a String
				String body = EntityUtils.toString(entity);
				List<?> listOfMaps = om.readValue(body, List.class);
				listOfMaps.forEach(element -> {
					if (element instanceof Map) {
						Map<String, String> data = new HashMap<>();
						Map<?, ?> map = (Map<?, ?>) element;
						result.put(asText(map.get("id")), data);
						// process customer data.
						data.put(CPHONE, asText(map.get("phone")));
						data.put(CEMAIL, asText(map.get("email")));
						Object customFields = map.get("customFieldValues");
						if (customFields instanceof List) {
							((List) customFields).stream().forEach(e -> {
								if (e instanceof Map) {
									Map<?, ?> customField = (Map<?, ?>) e;
									if (CSTM_REFCODE.equals(customField.get("fieldName"))) {
										data.put(CSTM_REFCODE, asText(customField.get("fieldValue")));
									} else if (CSTM_MDM.equals(customField.get("fieldName"))) {
										data.put(CSTM_MDM, asText(customField.get("fieldValue")));
									} else if (CSTM_VNO.equals(customField.get("fieldName"))) {
										data.put(CSTM_VNO, asText(customField.get("fieldValue")));
									}
								}
							});
						}
					}
				});
			}
		} catch (Exception e) {
			System.err.println("Error getting data from Kontacto for CustomerIDs" + customerIds);
			e.printStackTrace();
		} finally {
			try {
				if (response != null) {
					response.close();
				}
			} catch (Exception e) {
			}
			System.out.println("Returning data for :: " + customerIds);
		}
	}

	// http://kontacto.birdeye.com/customer/get/126194657?customField=true

	/**
	 * {"id":126194657,"ecid":106227632,"businessId":724971,"firstName":"Johana","lastName":"Jacobo","emailId":"<EMAIL>","phone":"(480)
	 * 395-4615","phoneE164":"+***********","smsEnabled":1,"unsubscribed":0,"lastActivityDate":"2020-11-04T22:15:45.000+0000","createdAt":"2020-10-29T11:50:21.000+0000","updatedAt":"2020-11-04T22:16:47.000+0000","isDeleted":0,"displayName":"Johana
	 * Jacobo","smsUnsubscribedType":null,"countryCode":"US","unsubscribedReason":null,"subscriptionStatus":"both-subscribed","sentimentScore":null,"sentiment":"unknown","state":"customer","source":"sftp","customFieldValues":[{"id":32,"fieldName":"Referral
	 * Code","dataType":"text","fieldDescription":"Referral
	 * Code","hidden":true,"cfvId":29628326,"fieldValue":"MFXNFX"},{"id":17,"fieldName":"MDM
	 * ID","dataType":"text","fieldDescription":"MDM
	 * ID","hidden":true,"cfvId":29628327,"fieldValue":"4597299"},{"id":24,"fieldName":"Loan
	 * Status","dataType":"text","fieldDescription":"Loan
	 * Status","hidden":true,"cfvId":29628329,"fieldValue":"Open"},{"id":31,"fieldName":"Test
	 * Environment","dataType":"text","fieldDescription":"Test
	 * Environment","hidden":true,"cfvId":29628333,"fieldValue":"Production"},{"id":26,"fieldName":"Universal
	 * Silver Status","dataType":"text","fieldDescription":"Universal Silver
	 * Status","hidden":true,"cfvId":29628334,"fieldValue":"Y"},{"id":18,"fieldName":"Text
	 * Enabled","dataType":"text","fieldDescription":"Text
	 * Enabled","hidden":true,"cfvId":29628335,"fieldValue":"Y"},{"id":23,"fieldName":"Referral
	 * Funds Available","dataType":"text","fieldDescription":"Referral Funds
	 * Available","hidden":true,"cfvId":29628337,"fieldValue":"0"},{"id":28,"fieldName":"Brand","dataType":"text","fieldDescription":"Brand","hidden":true,"cfvId":29628339,"fieldValue":"TitleMax"},{"id":21,"fieldName":"Store
	 * Name","dataType":"text","fieldDescription":"Store
	 * Name","hidden":true,"cfvId":29628340,"fieldValue":"TITLEMAX OF GILBERT, AZ
	 * #1"},{"id":19,"fieldName":"Email
	 * Enabled","dataType":"text","fieldDescription":"Email
	 * Enabled","hidden":true,"cfvId":29628342,"fieldValue":"Y"},{"id":27,"fieldName":"Mobile
	 * App Linked","dataType":"text","fieldDescription":"Mobile App
	 * Linked","hidden":true,"cfvId":29628344,"fieldValue":"Y"},{"id":22,"fieldName":"Home
	 * State","dataType":"text","fieldDescription":"Home
	 * State","hidden":true,"cfvId":29628346,"fieldValue":"AZ"},{"id":25,"fieldName":"Historical
	 * Referral Count","dataType":"text","fieldDescription":"Historical Referral
	 * Count","hidden":true,"cfvId":29628349,"fieldValue":"0"}],"op":null}
	 */
//	@SuppressWarnings({ "unchecked", "rawtypes" })
//	static void getCustomerDetails(String customerId, Map<String, String> data) {
//		CloseableHttpResponse response = null;
//		try {
//			HttpGet request = new HttpGet(
//					"http://kontacto.birdeye.com/customer/get/" + customerId + "?customField=true");
//			response = httpClient.execute(request);
//			HttpEntity entity = response.getEntity();
//			if (entity != null) {
//				// return it as a String
//				String result = EntityUtils.toString(entity);
//				ObjectMapper mapper = new ObjectMapper();
//				Map<?, ?> map = mapper.readValue(result, Map.class);
//				data.put(CPHONE, asText(map.get("phone")));
//				data.put(CEMAIL, asText(map.get("emailId")));
//				Object customFields = map.get("customFieldValues");
//				if (customFields instanceof List) {
//					((List) customFields).stream().forEach(e -> {
//						if (e instanceof Map) {
//							Map<?, ?> customField = (Map<?, ?>) e;
//							if (CSTM_REFCODE.equals(customField.get("fieldName"))) {
//								data.put(CSTM_REFCODE, asText(customField.get("fieldValue")));
//							} else if (CSTM_MDM.equals(customField.get("fieldName"))) {
//								data.put(CSTM_MDM, asText(customField.get("fieldValue")));
//							} else if (CSTM_VNO.equals(customField.get("fieldName"))) {
//								data.put(CSTM_VNO, asText(customField.get("fieldValue")));
//							}
//						}
//					});
//				}
//			}
//		} catch (Exception e) {
//			System.err.println("Error getting data from Kontacto for CustomerID " + customerId);
//			e.printStackTrace();
//		} finally {
//			try {
//				if (response != null) {
//					response.close();
//				}
//			} catch (Exception e) {
//			}
//		}
//	}

	/*
	 * ExecutorService executor = Executors.newFixedThreadPool(100); List<Future<?>>
	 * futures = new ArrayList<>();
	 * 
	 * docs.forEach(doc -> { if (doc instanceof Map) {
	 * futures.add(executor.submit(() -> { rows.add(processDoc((Map<?, ?>) doc));
	 * })); } });
	 * 
	 * futures.stream().forEach(f -> { try { f.get(); } catch (Exception e) {
	 * System.err.println("Error with task processing: " + f); } });
	 * executor.shutdown();
	 * 
	 */

}
