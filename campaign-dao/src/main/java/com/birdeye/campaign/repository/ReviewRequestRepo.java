package com.birdeye.campaign.repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.dto.ReviewRequestDto;
import com.birdeye.campaign.entity.ReviewRequest;
import com.birdeye.campaign.projections.ReviewRequestLocation;
import com.birdeye.campaign.response.ReviewRequestResponse;

/**
 * Repo for querying @ReviewRequest.java
 * 
 * <AUTHOR>
 *
 */
@Repository
public interface ReviewRequestRepo extends JpaRepository<ReviewRequest, Long> {

	@Query(value = "select rr from ReviewRequest rr where rr.id = :requestId")
	public ReviewRequest getById(@Param("requestId") Long requestId);
	
	public ReviewRequest findFirstById(Long id);	

	@Query(value = "select rr from ReviewRequest rr where rr.id = :requestId or rr.parentRequestId = :requestId")
	public List<ReviewRequest> findByIdOrParentRequestId(@Param("requestId") Long requestId);
	
	@Query(value = "SELECT r FROM ReviewRequest r where r.custId = :custId and r.source = 'sms' and r.deliveryStatus = 'success' and r.requestDate >= :fromDate and r.requestDate <= :toDate")
	public List<ReviewRequest> findSmsRequestByCustomer(@Param("custId")Integer customerId, @Param("fromDate")Date fromDate, @Param("toDate")Date toDate);
	
	@Query(value = "SELECT r.id FROM ReviewRequest r where r.deliveryStatus = 'success'  and r.source='sms' and r.checkinId = :checkinId")
	public List<Long> findSmsRequestByCheckinId(@Param("checkinId")Integer checkinId);

	List<ReviewRequest> findByParentRequestId(Long parentRequestId);
	
	@Query(value = "select r.id FROM ReviewRequest r where r.businessId = :businessId and ( r.requestType = 'customer_experience') and r.parentRequestId is null order by r.id")
	public List<Long> findCXBusiness(@Param("businessId") int businessId);
	
	@Query(value = "select r FROM ReviewRequest r where ( r.requestType = :campaignType) and (r.id in :reviewRequestIds or r.parentRequestId in :reviewRequestIds)  and r.deliveryStatus = 'success' order by r.id")
	public List<ReviewRequest> findChildAndParentReviewRequestId(@Param("reviewRequestIds") List<Long> reviewRequestIds,@Param("campaignType") String campaignType);

	/** angad **/
	@Query(value =  "select r FROM ReviewRequest r where (r.id in :requestIds or r.parentRequestId in :requestIds) order by r.id")
	public List<ReviewRequest> findRequestAndRemindersByRequestIds(@Param("requestIds") List<Long> RequestIds);
	
	@Query(value = "select r.id FROM ReviewRequest r where r.requestType = 'review_request' and r.campaignId is not null and r.surveyId is null  and r.businessId = :businessId and r.parentRequestId is null order by r.id")
	public List<Long> findURBusinessId(@Param("businessId") int businessId);
	
	@Query(value = "select r.id FROM ReviewRequest r where (r.requestType is null or r.requestType = 'review_request_new' or r.requestType = 'review_request') and r.surveyId is null  and r.businessId = :businessId and r.parentRequestId is null order by r.id")
	public List<Long> findRRIdsByBusinessIdForMigration(@Param("businessId") int businessId);
	
	@Query(value = "select r.id FROM ReviewRequest r where (r.requestType is null or r.requestType = 'review_request_new') and r.surveyId is null  and r.parentRequestId is null  and r.deliveryStatus = 'success' and r.requestDate >= :requestDate order by r.id")
	public List<Long> findRRIdAfter(@Param("requestDate") Date requestDate);
	
	// 
	@Query(value = "SELECT r.id FROM ReviewRequest r where r.custId = :custId and r.source = 'sms' and r.campaignId = :campaignId")
	public List<Long> findSmsRequestByCustomerAndCampaign(@Param("custId")Integer customerId, @Param("campaignId") Integer campaignId);
	
	@Query(value = "SELECT r FROM ReviewRequest r where r.custId = :custId and r.source= :source and r.deliveryStatus = 'success' and r.requestType in :requestTypes order by r.sentOn desc")
	public List<ReviewRequest> findRequestByCustomerAndType(@Param("custId")Integer customerId, @Param("source")String source,@Param("requestTypes") List<String> requestTypes);
	
	public List<ReviewRequest> findByCustIdAndSourceAndDeliveryStatusAndRequestTypeInOrderBySentOnDesc(@Param("custId") Integer customerId, @Param("source") String source,
			@Param("deliveryStatus") String deliveryStatus, @Param("requestTypes") List<String> requestTypes);
	
	@Query("select new com.birdeye.campaign.response.ReviewRequestResponse(r.id,r.custId,r.failureReason) from ReviewRequest r where r.businessId = :businessId and r.deliveryStatus= 'failure' and r.failureReason like '%already sent in last%days'")
	public List<ReviewRequestResponse> findByBusinessIdAndFailure(@Param("businessId") Integer businessId);
	
	@Query(value = "select r FROM ReviewRequest r where r.id in :requestIds ")
	public List<ReviewRequest> findReviewRequestByIds(@Param("requestIds") Set<Long> requestIds);
	
	@Query(value = "select new com.birdeye.campaign.projections.ReviewRequestLocation(r.id,r.businessId) FROM ReviewRequest r where r.id in :requestIds ")
	public List<ReviewRequestLocation> findReviewRequestLocationsByIds(@Param("requestIds") Set<Long> requestIds);
	
	@Query(value = "select r FROM ReviewRequest r where r.campaignId = :campaignId and r.custId = :custId and r.requestType = 'survey_request'")
	public List<ReviewRequest> getReviewRequestForCampaignAndCustomer(@Param("campaignId") Integer campaignId, @Param("custId") Integer custId);
	
	@Query(value = "select r FROM ReviewRequest r where r.checkinId = :checkinId and r.custId = :custId and r.requestType = 'survey_request'")
	public List<ReviewRequest> getReviewRequestForCheckinAndCustomer(@Param("checkinId") Integer checkinId, @Param("custId") Integer custId);

	@Query(value = "select r.id FROM ReviewRequest r where r.id in :requestIds or r.parentRequestId in :requestIds or (r.campaignId = :campaignId and r.custId = :custId) order by r.id asc")
	public List<Long> findReviewRequestChainWithoutCheckin(@Param("requestIds") List<Long> requestIds, @Param("campaignId") Integer campaignId, @Param("custId") Integer custId);
	
	@Query(value = "select r.id FROM ReviewRequest r where r.id in :requestIds or r.parentRequestId in :requestIds or  r.checkinId = :checkinId order by r.id asc")
	public List<Long> findReviewRequestChainWithCheckin(@Param("requestIds") List<Long> requestIds, @Param("checkinId") Integer checkinId);
	
	@Query(value = "select r.id FROM ReviewRequest r where r.businessId = :businessId and ( r.requestType = 'referral') and r.parentRequestId is null order by r.id")
	public List<Long> findReferralBusiness(@Param("businessId") int businessId);

	@Query(value = "select new com.birdeye.campaign.dto.ReviewRequestDto(rr.id, rr.requestType) FROM ReviewRequest rr where rr.requestType in :campaignType and rr.surveyId is null "
			+ "and rr.businessId = :fromBusinessId and rr.parentRequestId is null order by rr.id")
	public List<ReviewRequestDto> findReviewRequestIdsByBusinessId(Integer fromBusinessId, List<String> campaignType);

	@Query(value = "select max(r.sentOn) FROM ReviewRequest r where r.custId in :customerIds and r.deliveryStatus = 'success'")
	public Date findLastActivityDateOfACustomer(List<Integer> customerIds);
	
	@Query(value = "select r FROM ReviewRequest r where r.id between :startId and :endId")
	List<ReviewRequest> getReviewRequestsBetweenRange(@Param("startId") Long startId, @Param("endId") Long endId);
		
	@Query(value = "select new com.birdeye.campaign.dto.ReviewRequestDto(r) FROM ReviewRequest r where r.id in :requestIds")
	List<ReviewRequestDto> getReviewRequestDtoByIds(@Param("requestIds") List<Long> requestIds);
	
	@Query(value = "select new com.birdeye.campaign.dto.ReviewRequestDto(rr.id, rr.requestType) FROM ReviewRequest rr where rr.requestType in :campaignType and rr.surveyId is null "
			+ "and rr.businessId = :fromBusinessId order by rr.id")
	public List<ReviewRequestDto> findAppointmentReviewRequestIdsByBusinessId(Integer fromBusinessId, List<String> campaignType);

	public List<ReviewRequest> findAllByCheckinIdAndCustIdAndDeliveryStatus(Integer checkinId, Integer customerId, String status);
	
	@Modifying
	@Transactional
	@Query("DELETE from ReviewRequest WHERE id in :ids")
	public int deleteByIds(@Param("ids") List<Long> ids);
	
	@Modifying
	@Transactional
	@Query("update ReviewRequest set deliveryStatus = :finalStatus,failureReason = :droppedReason  WHERE id in :ids and deliveryStatus=:initialStatus")
	public int updateDeliveryStatus(@Param("ids") List<Long> ids, @Param("initialStatus") String initialStatus, @Param("finalStatus") String finalStatus,@Param("droppedReason") String droppedReason);
	
	public ReviewRequest findFirstByBusinessIdInAndDeliveryStatusAndRequestType(List<Integer> businessIds,String deliveryStatus,String requestType);
	
	@Query(value = "select r FROM ReviewRequest r where r.id in :reviewRequestIds and deliveryStatus in :deliveryStatus order by id desc")
	public List<ReviewRequest> findAllByIdInAndDeliveryStatusIn(@Param("reviewRequestIds") List<Long> reviewRequestIds, @Param("deliveryStatus") List<String> deliveryStatus);
	
	@Modifying
	@Transactional
	@Query("UPDATE ReviewRequest SET deliveryStatus = :finalStatus, failureReason = :failureReason WHERE (id IN :reviewRequestIds OR parentRequestId IN :reviewRequestIds) AND deliveryStatus = :initialStatus")
	public Integer updateDeliveryStatusAndFailureReason(@Param("reviewRequestIds") List<Long> reviewRequestIds, @Param("initialStatus") String initialStatus,
			@Param("finalStatus") String finalStatus, @Param("failureReason") String failureReason);
	
	@Query(value = "select r.id FROM ReviewRequest r where r.custId = :customerId and r.requestType = :requestType and r.deliveryStatus = :deliveryStatus and r.source = :source order by r.id desc")
	public List<Long> findReviewRequestIdsByCustomerAndTypeAndDeliveryStatus(@Param("customerId") Integer customerId, @Param("requestType") String requestType, @Param("deliveryStatus") String deliveryStatus,  @Param("source") String source);
	
	@Query(value = "select new com.birdeye.campaign.dto.ReviewRequestDto(rr.id, rr.templateId,rr.source) from ReviewRequest rr where rr.id in :rrIds and rr.source=:source")
	List<ReviewRequestDto> findByIdInAndSource(@Param("rrIds") Set<Long> rrIds, @Param("source") String source);

	@Query("SELECT rr.id FROM ReviewRequest rr WHERE " +
			"(:checkinId IS NULL OR rr.checkinId = :checkinId) AND " +
			"(:customerId IS NULL OR rr.custId = :customerId) AND " +
			"(:campaignId IS NULL OR rr.campaignId = :campaignId) AND " +
			"(:surveyId IS NULL OR rr.surveyId = :surveyId)")
	List<Long> getReviewRequestChainForSurveyRequest(@Param("checkinId") Integer checkinId, @Param("customerId") Integer customerId, @Param("surveyId") Integer surveyId,
			@Param("campaignId") Integer campaignId);
	
}
