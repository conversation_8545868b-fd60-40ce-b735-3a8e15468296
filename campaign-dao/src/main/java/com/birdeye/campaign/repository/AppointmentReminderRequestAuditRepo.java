/**
 * 
 */
package com.birdeye.campaign.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;

import com.birdeye.campaign.entity.AppointmentRRMapping;

/**
 * <AUTHOR>
 *
 */
public interface AppointmentReminderRequestAuditRepo extends JpaRepository<AppointmentRRMapping, Integer> {
	// #1
	AppointmentRRMapping findFirstByReviewRequestId(Long reviewRequestId);
	// #4
	List<AppointmentRRMapping> findByRecallIdAndRequestType(Integer recallId, String requestType);
	// #3
	List<AppointmentRRMapping> findByReviewRequestIdIn(List<Long> reviewRequestIds);
	// #2
	List<AppointmentRRMapping> findByAppointmentIdIn(List<Integer> appointmentIds);
}
