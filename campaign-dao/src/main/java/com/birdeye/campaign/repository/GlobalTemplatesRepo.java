package com.birdeye.campaign.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.entity.GlobalTemplates;

@Repository
public interface GlobalTemplatesRepo extends JpaRepository<GlobalTemplates, Integer> {
	

	public List<GlobalTemplates> findByIndustryAndTemplateTypeIn(String Industry, List<String> templateType);

	public List<GlobalTemplates> findByIndustry(String Industry);
	
	public GlobalTemplates findFirstById(Integer id);

}
