package com.birdeye.campaign.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.dto.LocationLeadFormUrlDTO;
import com.birdeye.campaign.entity.LocationLeadFormUrl;

@Repository
public interface LocationLeadFormUrlRepo extends JpaRepository<LocationLeadFormUrl, Integer> {
	
	List<LocationLeadFormUrlDTO> findByDeleted(Integer isDeleted);
	
	List<LocationLeadFormUrlDTO> findByEnterpriseIdAndDeleted(Integer enterpriseId, Integer isDeleted);
	
	LocationLeadFormUrl findByBusinessId(Integer businessId);
	
	LocationLeadFormUrl findByBusinessIdAndDeleted(Integer businessId, Integer isDeleted);
	
}
