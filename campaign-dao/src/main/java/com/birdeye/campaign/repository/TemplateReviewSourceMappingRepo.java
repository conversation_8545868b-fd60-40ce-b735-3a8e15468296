package com.birdeye.campaign.repository;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.entity.TemplateReviewSourceMapping;

@Repository
public interface TemplateReviewSourceMappingRepo extends JpaRepository<TemplateReviewSourceMapping, Integer>  {
	
	List<TemplateReviewSourceMapping> findByAssociatedEntityIdAndDeviceTypeAndAssociatedEntity(Integer templateId, String deviceType, String associatedEntity);
	
	@Transactional
	@Modifying
	@Query("DELETE from TemplateReviewSourceMapping tm where tm.id = :id")
	public int deleteReviewSourceMappingById(@Param("id") Integer id);
	
}
