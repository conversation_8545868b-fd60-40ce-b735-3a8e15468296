package com.birdeye.campaign.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.dto.EmailTemplateMessage;
import com.birdeye.campaign.entity.EmailTemplate;

/**
 * Repo for querying @EmailTemplate.java
 * 
 * <AUTHOR>
 *
 */
@Repository
public interface EmailTemplateRepo extends JpaRepository<EmailTemplate, Integer> {
	
	public static String COLUMNS_WITHOUT_CONTENT="et.id, et.name, et.message, et.sentimentMessage, et.starMessage, et.subject, et.reminderSubject, et.isDefault, et.type,\n" + 
												"et.emailQuestion, et.sentimentHeading, et.starHeading, et.positiveButtonLabel, et.negativeButton<PERSON>abe<PERSON>, et.neutralButtonLabel, et.recommendPageHeading,\n" + 
												"et.recommendPageMessage, et.recommendThankPageMessage, et.recommendThankPageFooter, et.nonRecommendPageMessage, et.nonRecommendThankPageMessage,\n" + 
												"et.nonRecommendThankPageFooter, et.prioritySources, et.writeReviewQuestion, et.writeReviewNegativeText, et.writeReviewPositiveText, et.positiveRatingThreshold,\n" + 
												"et.contactUsMessage, et.contactUsButtonText, et.contactUsButtonTextColor, et.contactUsButtonColor, et.reviewHeading, et.reviewMessage, et.reviewSiteButtonColor,\n" + 
												"et.reviewSiteButtonTextColor, et.feedbackMessage, et.feedbackShowCallbackOption, et.feedbackDefaultCheckboxChecked, et.thankyouHeading, et.thankyouMessage,\n" + 
												"et.signature, et.mediaUrls, et.referralQuestion";
	
	public static String COLUMNS_WITH_CONTENT=COLUMNS_WITHOUT_CONTENT+",et.emailContent";
	
	public EmailTemplate findFirstById(Integer templateId);
	
	public List<EmailTemplate> findByIsDefaultAndTypeIsNotNull(String isDefault);
	
	public List<EmailTemplate> findByIdAndIsDefaultAndTypeIsNotNull(Integer id,String isDefault);
	
	public List<EmailTemplate> findByIdAndIsDefaultAndType(@Param("id") Integer id,@Param("isDefault") String isDefault,@Param("type") String type);
	
	@Query("SELECT et.name FROM EmailTemplate et WHERE et.id = :templateId")
	public String findTemplateNameById(@Param("templateId") Integer templateId);
	
	@Query("SELECT et.emailCategory FROM EmailTemplate et WHERE et.id = :templateId")
	public String findTemplateCategoryById(@Param("templateId") Integer templateId);
	
	@Query("SELECT new com.birdeye.campaign.dto.EmailTemplateMessage("+COLUMNS_WITHOUT_CONTENT+") FROM EmailTemplate et WHERE et.id = :templateId")
	public EmailTemplateMessage findTemplateWithoutContentById(@Param("templateId") Integer templateId);
	
	@Query("SELECT new com.birdeye.campaign.dto.EmailTemplateMessage("+COLUMNS_WITH_CONTENT+") FROM EmailTemplate et WHERE et.id = :templateId")
	public EmailTemplateMessage findTemplateWithContentById(@Param("templateId") Integer templateId);
	
	@Query("SELECT et.type AS TYPE, et.emailCategory AS EMAILCATEGORY FROM EmailTemplate et WHERE et.id = :templateId")
	Optional<TemplateCategoryToTypeProjection> getTemplateTypeAndCategoryById(@Param("templateId") Integer templateId);
	
	public static interface TemplateCategoryToTypeProjection {
		public String getEMAILCATEGORY();
		
		public String getTYPE();
	}
	
}

