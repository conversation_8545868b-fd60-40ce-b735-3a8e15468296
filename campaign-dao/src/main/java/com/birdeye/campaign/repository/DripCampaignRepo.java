package com.birdeye.campaign.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;

import com.birdeye.campaign.entity.DripCampaign;

public interface DripCampaignRepo extends JpaRepository<DripCampaign, Integer> {

	List<DripCampaign> findByCampaignId(Integer campaignId);

	DripCampaign findTopByCampaignIdOrderByDateOfExecutionDesc(Integer campaignId);

	List<DripCampaign> findByCampaignIdAndStatusIn(Integer campaignId, List<Integer> statusList);

	DripCampaign findTopByCampaignIdOrderByLocationScheduleTimeDesc(Integer campaignId);
	
	DripCampaign findTopByCampaignIdAndStatusOrderByActualScheduleTimeAsc(Integer campaignId, Integer status);

}
