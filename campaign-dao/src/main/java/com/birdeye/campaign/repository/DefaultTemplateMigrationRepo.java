package com.birdeye.campaign.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.entity.DefaultTemplateMigrationStats;

@Repository
public interface DefaultTemplateMigrationRepo extends JpaRepository<DefaultTemplateMigrationStats, Integer> {
	
	public DefaultTemplateMigrationStats findFirstByAccountIdOrderByIdDesc(Integer accountId);
	
	@Query("select distinct p.accountId from DefaultTemplateMigrationStats p")
	public List<Integer> getDistinctAccountIds();
	
	@Query("select dtms.accountId from DefaultTemplateMigrationStats dtms where dtms.accountId in :accountIds group by dtms.accountId")
	public List<Integer> getByAccountIdIn(@Param("accountIds") List<Integer> accountId);
	
}
