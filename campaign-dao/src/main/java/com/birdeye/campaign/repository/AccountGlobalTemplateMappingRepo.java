package com.birdeye.campaign.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.dto.AccountGlobalTemplateMappingDTO;
import com.birdeye.campaign.entity.AccountGlobalTemplateMapping;

@Repository
public interface AccountGlobalTemplateMappingRepo extends JpaRepository<AccountGlobalTemplateMapping, Integer> {
	
	static final String	ACCOUNT_GLOBAL_TEMPLATE_MAPPING_BASE_QUERY		= "select new com.birdeye.campaign.dto.AccountGlobalTemplateMappingDTO(a.id, a.accountId, a.globalTemplateId, a.smsTemplateId, a.isDeleted, a.emailTemplateId, a.createdAt, a.updatedAt)"
			+ " FROM AccountGlobalTemplateMapping a WHERE a.globalTemplateId = :globalTemplateId AND a.accountId = :accountId";
	
	static final String	ACCOUNT_GLOBAL_TEMPLATE_MAPPING_BASE_QUERY_V1	= "select new com.birdeye.campaign.dto.AccountGlobalTemplateMappingDTO(a.id, a.accountId, a.globalTemplateId, a.smsTemplateId, a.isDeleted, a.emailTemplateId, a.createdAt, a.updatedAt)"
			+ " FROM AccountGlobalTemplateMapping a WHERE a.accountId = :accountId";
	
	@Query(value = ACCOUNT_GLOBAL_TEMPLATE_MAPPING_BASE_QUERY_V1)
	public List<AccountGlobalTemplateMappingDTO> getByAccountId(Integer accountId);
	
	AccountGlobalTemplateMapping findFirstByAccountIdAndGlobalTemplateIdOrderByIdDesc(Integer accountId, Integer globalTemplateId);
	
	public List<AccountGlobalTemplateMapping> findByAccountId(Integer accountId);
	
}
