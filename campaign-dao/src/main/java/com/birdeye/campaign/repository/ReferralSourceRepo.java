/**
 * @file_name ReferralSourceRepo.java
 * @created_date 8 Jul 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.entity.ReferralSource;

/**
 * @file_name ReferralSourceRepo.java
 * @created_date 8 Jul 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
@Repository
public interface ReferralSourceRepo  extends JpaRepository<ReferralSource, Integer>{
	
}
