package com.birdeye.campaign.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.entity.ReferralTemplateData;

/**
 * 
 * <AUTHOR>
 *
 */
@Repository
public interface ReferralTemplateDataRepo extends JpaRepository<ReferralTemplateData, Integer> {
	
	public ReferralTemplateData findFirstByTemplateIdAndSource(Integer templateId, String source);
	
	public List<ReferralTemplateData> findByEnterpriseIdIsNull();
	
}
