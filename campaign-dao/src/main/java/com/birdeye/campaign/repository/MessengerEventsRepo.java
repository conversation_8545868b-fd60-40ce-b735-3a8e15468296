/**
 * @file_name MessengerEventsRepo.java
 * @created_date 27 Jan 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.dto.PromotionTemplateSentCountMessage;
import com.birdeye.campaign.entity.MessengerEvents;

/**
 * @file_name MessengerEventsRepo.java
 * @created_date 27 Jan 2020
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@Repository
public interface MessengerEventsRepo extends JpaRepository<MessengerEvents, Integer> {
	
	@Query(value = "select new com.birdeye.campaign.dto.PromotionTemplateSentCountMessage(c.templateId, COUNT(c.id)) FROM MessengerEvents c where c.templateId in :templateIds AND event='sent' group by c.templateId")
	public List<PromotionTemplateSentCountMessage> getPromotionTemplateSentCount(@Param("templateIds") List<Integer> templateIds);
}
