package com.birdeye.campaign.repository;

import java.util.Date;
import java.util.List;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.entity.Appointment;
import com.birdeye.campaign.projections.AppointmentDTO;

/**
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@Repository
public interface AppointmentRepository extends JpaRepository<Appointment, Integer> {
	
	@Query("select count(*) from Appointment app where (app.email = :email or app.phoneNumber= :phoneNumber )and app.enterpriseId= :enterpriseId")
	public Integer findCountByAppointmentIdentity(String email, String phoneNumber, Integer enterpriseId);
	
	public Appointment findFirstById(Integer appointmentId);
	
	public Appointment findFirstByIdAndIsSuspect(Integer appointmentId, Integer isSuspect);
	
	@Query("select ap from Appointment ap where ap.reviewRequestId in :reviewRequestIds")
	List<Appointment> findByReviewRequestIds(@Param("reviewRequestIds") List<Long> reviewRequestIds);
	
	@Query(value = "SELECT COUNT(*) FROM Appointment ap where ap.businessId in :businessIds and ap.createdAt >= :startDate and ap.createdAt <= :endDate")
	public Integer getLeadsCount(@Param("businessIds") List<Integer> businessIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate);
	
	@Query(value = "SELECT count(*) FROM Appointment ap where ap.businessId = :businessId and ap.createdAt >= :startDate and ap.createdAt <= :endDate")
	public Integer getListingDataCountInTimePeriodByBusinessId(@Param("businessId") Integer businessId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);
	
	@Query(value = "SELECT count(*) FROM Appointment ap where ap.enterpriseId= :enterpriseId and ap.createdAt >= :startDate and ap.createdAt <= :endDate")
	public Integer getListingDataCountInTimePeriodByEnterpriseId(@Param("enterpriseId") Integer enterpriseId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);
	
	@Query(value = "SELECT count(*) FROM Appointment ap where ap.businessId = :businessId")
	public Integer getListingDataCountAllTimeByBusinessId(@Param("businessId") Integer businessId);
	
	@Query(value = "SELECT count(*) FROM Appointment ap where ap.enterpriseId= :enterpriseId")
	public Integer getListingDataCountAllTimeByEnterpriseId(@Param("enterpriseId") Integer enterpriseId);
	
	public Appointment findFirstByCid(Integer cid);
	
	public Appointment findFirstByEcidAndBusinessId(Integer ecid, Integer businessId);
	
	@Query("select app from Appointment app where (app.email = :email or app.phoneNumber= :phoneNumber )and app.enterpriseId= :enterpriseId")
	public List<Appointment> findByAppointmentIdentity(String email, String phoneNumber, Integer enterpriseId);
	
	@Query("SELECT DISTINCT ap.enterpriseId FROM Appointment ap WHERE referrerCode IS NULL")
	public List<Integer> findDistinctEnterpriseIdsWhereReferrerCodeIsNull();
	
	public List<AppointmentDTO> findByEnterpriseIdAndReferrerCodeIsNull(Integer enterpriseId);
	
	@Transactional
	@Modifying
	@Query("UPDATE Appointment SET referrerEmail = :email, referrerPhoneNumber = :phone, referrerEcid = :ecid, referrerCode = :referralCode, referrerLocationName = :locationName WHERE referrerId = :id")
	public void updateReferrerInfo(@Param("id") Integer id, @Param("email") String email, @Param("phone") String phone, @Param("ecid") Integer ecid, @Param("referralCode") String referralCode,
			@Param("locationName") String locationName);
	
	@Transactional
	@Modifying
	@Query("UPDATE Appointment SET referralCode = :referralCode WHERE cid = :cid")
	public void updateLeadReferralCode(@Param("cid") Integer cid, @Param("referralCode") String referralCode);
	
	@Query("SELECT ap.cid as cid, ap.referrerId as referrerId FROM Appointment ap where ap.id >= :startId AND ap.id <= :endId")
	public List<AppointmentDTO> findByIdInRange(@Param("startId") Integer startId, @Param("endId") Integer endId);
	
	public Appointment findFirstByOrderByIdDesc();
	
	@Query("SELECT ap FROM Appointment ap where ap.id >= :startId AND ap.id <= :endId")
	public List<Appointment> findByIdInRangeV2(@Param("startId") Integer startId, @Param("endId") Integer endId);
	
}