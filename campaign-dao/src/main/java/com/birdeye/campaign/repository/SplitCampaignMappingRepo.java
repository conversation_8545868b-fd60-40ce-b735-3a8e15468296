package com.birdeye.campaign.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.dto.SplitCampaignMappingDTO;
import com.birdeye.campaign.entity.SplitCampaignMapping;

@Repository
public interface SplitCampaignMappingRepo extends JpaRepository<SplitCampaignMapping, Integer> {
	
	static final String	SPLIT_CAMPAIGN_MAPPING_LISTING_BASE_QUERY_V1	= "select new com.birdeye.campaign.dto.SplitCampaignMappingDTO(scm.splitCampaignId,scm.splitPercent,scm.campaignType,scm.campaignId)"
			+ " FROM SplitCampaignMapping scm where scm.splitCampaignId in :splitCampaignIds";
	
	static final String	SPLIT_CAMPAIGN_MAPPING_DATA_QUERY_V1			= "SELECT new com.birdeye.campaign.dto.SplitCampaignMappingDTO(scm.id, scm.splitCampaignId, scm.campaignId, scm.emailTemplateId, scm.smsTemplateId, scm.surveyId, scm.splitPercent, scm.campaignType, scm.createdAt)"
			+ " FROM SplitCampaignMapping scm WHERE scm.splitCampaignId = :splitCampaignId";
	
	@Query(value = SPLIT_CAMPAIGN_MAPPING_LISTING_BASE_QUERY_V1)
	public List<SplitCampaignMappingDTO> getSplitCampaignMappingDataBySplitCampaignIds(@Param("splitCampaignIds") List<Integer> splitCampaignIds);
	
	List<SplitCampaignMapping> findBySplitCampaignId(Integer splitCampaignId);
	
	@Query(value = SPLIT_CAMPAIGN_MAPPING_DATA_QUERY_V1)
	List<SplitCampaignMappingDTO> getSplitCampaignMappingDataBySplitCampaignId(@Param("splitCampaignId") Integer splitCampaignId);
    
	@Query(value = "select scm.splitCampaignId FROM SplitCampaignMapping scm where scm.campaignId = :campaignId")
	public List<Integer> getMappingByCampaignId(Integer campaignId);

}
