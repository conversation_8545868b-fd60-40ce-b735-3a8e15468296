package com.birdeye.campaign.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.entity.StarLabels;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface StarLabelsRepo extends JpaRepository<StarLabels,Integer> {

    @Query(value = "select str.starLabels from StarLabels str where str.businessTemplateId = :id order by labelOrder")
    List<String> getStarLabelByBusinessTemplateID(Integer id);
}
