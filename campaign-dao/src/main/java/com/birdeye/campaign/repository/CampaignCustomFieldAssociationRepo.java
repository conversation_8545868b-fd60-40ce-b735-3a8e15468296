package com.birdeye.campaign.repository;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.entity.CampaignCustomFieldAssociation;
import com.birdeye.campaign.enums.CustomFieldSourceEnum;

@Repository
public interface CampaignCustomFieldAssociationRepo extends JpaRepository<CampaignCustomFieldAssociation, Integer> {
	
	@Query(value = "select cd from CampaignCustomFieldAssociation cd where cd.associatedObjectId = :associatedObjectId and cd.associatedType = :associatedType")
	public List<CampaignCustomFieldAssociation> getCampaignCustomFieldAssociation(@Param("associatedObjectId") Integer assObjectId, @Param("associatedType") String assObjType);
	
	@Transactional
	@Modifying
	@Query("DELETE from CampaignCustomFieldAssociation cd where cd.associatedObjectId = :associatedObjectId and cd.associatedType = :associatedType and cd.customFieldSource = :customFieldSource")
	public int deleteCampaignCustomField(@Param("associatedObjectId") Integer assObjectId, @Param("associatedType") String assObjType,
			@Param("customFieldSource") CustomFieldSourceEnum customFieldSourceEnum);
	
	@Transactional
	@Modifying
	@Query("UPDATE CampaignCustomFieldAssociation cd SET cd.isDeleted = :isDeleted WHERE cd.associatedObjectId = :associatedObjectId and cd.associatedType = :associatedType")
	public int updateDeleteFlag(@Param("isDeleted") Integer isDeleted, @Param("associatedObjectId") Integer assObjectId, @Param("associatedType") String assObjType);
	
	@Transactional
	@Modifying
	@Query("UPDATE CampaignCustomFieldAssociation cd SET cd.isDeleted = :isDeleted WHERE cd.associatedObjectId IN (:associatedObjectIds) AND cd.associatedType = :associatedType")
	public int updateDeleteFlagForCampaigns(@Param("isDeleted") Integer isDeleted, @Param("associatedObjectIds") List<Integer> objectIds, @Param("associatedType") String objectType);
	
	public List<CampaignCustomFieldAssociation> findAllByCustomFieldIdAndIsTagAndIsDeletedAndCustomFieldSource(@Param("customFieldId") Integer customFieldId,
			@Param("isTag") Integer isTag, @Param("isDeleted") Integer isDeleted, @Param("customFieldSource") CustomFieldSourceEnum customFieldSource);
	
	@Query(value = "select cd from CampaignCustomFieldAssociation cd where cd.associatedObjectId = :associatedObjectId and cd.associatedType = :associatedType and cd.customFieldSource = :customFieldSource")
	public List<CampaignCustomFieldAssociation> getCampaignCustomFieldAssociationBySource(@Param("associatedObjectId") Integer assObjectId,
			@Param("associatedType") String assObjType, @Param("customFieldSource") CustomFieldSourceEnum customFieldSource);

	
}
