package com.birdeye.campaign.readonly.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.aspect.annotation.ReadOnlyRepository;
import com.birdeye.campaign.entity.BusinessSmsTemplate;

@ReadOnlyRepository
@Repository
public interface BusinessSMSTemplateReadOnlyRepo extends JpaRepository<BusinessSmsTemplate, Integer> {
	
	@Query(value = "SELECT bst.id FROM BusinessSmsTemplate bst WHERE bst.enterpriseId = :enterpriseId AND bst.type = :type AND (bst.smsCategory IS NULL OR bst.smsCategory = :smsCategory) AND bst.isDeleted = 0")
	public List<Integer> getActiveTemplatesByEnterpriseIdAndTypeAndCategory(@Param("enterpriseId") Integer enterpriseId, @Param("type") String type,
			@Param("smsCategory") String smsCategory);
	
}
