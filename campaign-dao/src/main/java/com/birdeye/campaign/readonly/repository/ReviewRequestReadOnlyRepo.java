package com.birdeye.campaign.readonly.repository;

import java.util.List;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.aspect.annotation.ReadOnlyRepository;
import com.birdeye.campaign.dto.ReviewRequestDto;
import com.birdeye.campaign.dto.ReviewRequestLiteResponse;
import com.birdeye.campaign.entity.ReviewRequest;

/**
 * Read Only Repo for querying @ReviewRequest.java
 * <AUTHOR>
 *
 */
@Repository
@ReadOnlyRepository
public interface ReviewRequestReadOnlyRepo extends JpaRepository<ReviewRequest, Long> {
	
	@Query(value =  "select r FROM ReviewRequest r where r.id in :requestIds or r.parentRequestId in :requestIds")
	public List<ReviewRequest> findRequestAndRemindersByRequestIds(@Param("requestIds") List<Long> requestIds);
	
	@Query(value = "select r.id FROM ReviewRequest r where (r.requestType is null or r.requestType = 'review_request_new' or r.requestType = 'review_request') and r.surveyId is null "
			+ "and r.businessId = :businessId and r.parentRequestId is null")
	public List<Long> findRRIdsByBusinessIdForMigration(@Param("businessId") int businessId);
	
	@Query(value = "Select new com.birdeye.campaign.dto.ReviewRequestLiteResponse(r.id, r.checkinId) FROM ReviewRequest r where r.id in :requestIds and r.checkinId is not null")
	public List<ReviewRequestLiteResponse> getCheckinIdFromReviewRequestId(@Param("requestIds") List<Long> requestIds);
	
	@Query(value = "select new com.birdeye.campaign.dto.ReviewRequestDto(r.id,r.custId,r.source,r.reminderCount,r.deliveryStatus,r.surveyId) from ReviewRequest r where r.custId in :customerIds and r.deliveryStatus in :deliveryStatus")
	public List<ReviewRequestDto> findByCustomerIdInAndDeliveryStatusIn(@Param("customerIds") List<Integer> customerIds, @Param("deliveryStatus") List<String> deliveryStatus, Pageable page);
	
	@Query(value = "select new com.birdeye.campaign.dto.ReviewRequestDto(r.id,r.custId,r.source,r.reminderCount,r.deliveryStatus,r.surveyId) from ReviewRequest r where r.custId in :customerIds")
	public List<ReviewRequestDto> findByCustomerIdIn(List<Integer> customerIds, Pageable page);		
}
