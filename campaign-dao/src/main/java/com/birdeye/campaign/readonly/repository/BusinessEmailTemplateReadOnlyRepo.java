package com.birdeye.campaign.readonly.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.aspect.annotation.ReadOnlyRepository;
import com.birdeye.campaign.entity.BusinessEmailTemplate;

@ReadOnlyRepository
@Repository
public interface BusinessEmailTemplateReadOnlyRepo extends JpaRepository<BusinessEmailTemplate, Integer> {
	
	@Query("SELECT bet.emailTemplateId FROM BusinessEmailTemplate bet INNER JOIN bet.templateId et WHERE bet.enterpriseId = :enterpriseId AND et.type = :type AND (et.emailCategory IS NULL OR et.emailCategory = :emailCategory) AND bet.isDeleted = 0")
	public List<Integer> getActiveTemplatesForAccountByTypeAndCategory(@Param("enterpriseId") Integer enterpriseId, @Param("type") String type,
			@Param("emailCategory") String emailCategory);
	
}
