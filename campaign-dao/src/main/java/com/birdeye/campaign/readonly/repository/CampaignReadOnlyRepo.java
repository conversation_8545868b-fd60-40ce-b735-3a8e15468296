package com.birdeye.campaign.readonly.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.aspect.annotation.ReadOnlyRepository;
import com.birdeye.campaign.dto.CampaignCustomEntity;
import com.birdeye.campaign.entity.Campaign;

@Repository
@ReadOnlyRepository
public interface CampaignReadOnlyRepo extends JpaRepository<Campaign, Integer>{
	
	@Query(value = "select c.id from Campaign c where c.enterpriseId = :enterpriseId and c.runType = :runType and c.type = :type")
	List<Integer> getCampaignIdsByEnterpriseIdAndRunTypeAndType(@Param("enterpriseId") Integer enterpriseId, @Param("runType") String runType, @Param("type") String type);

	boolean existsByEnterpriseIdAndTypeAndRunTypeAndStatus(Integer enterpriseId, String type, String runType, Integer status); 
	
	@Query(value = "select c from Campaign c where c.enterpriseId = :enterpriseId and c.runType='ongoing' and c.isDefault=1")
	List<Campaign> getOngoingDefaultCampaignByEnterpriseId(@Param("enterpriseId") Integer enterpriseId);

	@Query(value = "select c.id from Campaign c where c.enterpriseId in :enterpriseIds and c.type = :campaignType and c.isSplitCampaign = :isSplitCampaign and c.isDeleted = :isDeleted")
	List<Integer> getCampaignIdByEnterpriseIdAndTypeAndIsDeletedAndIsSplitCampaign(@Param("enterpriseIds") List<Integer> enterpriseIds, @Param("campaignType") String campaignType,
			@Param("isSplitCampaign") Integer isSplitCampaign, @Param("isDeleted") Integer isDeleted);
	
	@Query(value = "select c.id as ID, c.enterpriseId as ENTERPRISEID from Campaign c where c.enterpriseId in :enterpriseIds and c.type = :campaignType and c.isSplitCampaign = :isSplitCampaign and c.isDeleted = :isDeleted")
	List<CampaignProjection> getCampaignIdAndEnterpriseIdByEnterpriseIdAndTypeAndIsDeletedAndIsSplitCampaign(@Param("enterpriseIds") List<Integer> enterpriseIds, @Param("campaignType") String campaignType,
			@Param("isSplitCampaign") Integer isSplitCampaign, @Param("isDeleted") Integer isDeleted);
	
	
	@Query(value = "select new com.birdeye.campaign.dto.CampaignCustomEntity(c.id , c.name, c.enterpriseId, c.priority, c.runType) from Campaign c where c.id in (:campaignIds)")
	List<CampaignCustomEntity> getCampaignNameAndEnterpriseId(@Param("campaignIds") List<Integer> campaignIds);
	
	@Query(value = "select COUNT(c.id) from Campaign c where c.enterpriseId = :enterpriseId and c.runType= 'ongoing' and c.status = 1 and c.type= :campaignType and (c.isSplitCampaign = 0 or c.isSplitCampaign is null)")
	Integer getRunningOngoingCampaignCountByEnterpriseIdAndType(@Param("enterpriseId") Integer enterpriseId, @Param("campaignType") String campaignType);
	
	Campaign findFirstByIdAndIsDeleted(Integer campaignId, Integer isDeleted);
	
	public static interface CampaignProjection {
		public Integer getID();
		
		public Integer getENTERPRISEID();
	}
}
