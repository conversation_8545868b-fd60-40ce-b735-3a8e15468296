package com.birdeye.campaign.readonly.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.aspect.annotation.ReadOnlyRepository;
import com.birdeye.campaign.entity.CampaignEvent;

/**
 * Read Only Repository for querying @CampaignEvent.java
 * 
 * Include readonly queries here, keeping into account the replication lags in slave.
 * 
 * <AUTHOR> 
 */
@Repository
@ReadOnlyRepository
public interface CampaignEventReadOnlyRepo extends JpaRepository<CampaignEvent, Integer>{
	
	public List<CampaignEvent> findByCategoryAndStatusAndIsDNDAndScheduledTimeBeforeAndIsReminderAndReviewRequestIdNotNull(String category, String status, Integer isDND,
			Date scheduledTime, Integer isReminder, Pageable page);
	
	List<CampaignEvent> findByCampaignTypeAndStatusAndScheduledTimeBefore(String campaignType, String status, Date scheduledType, Pageable page);
	
	List<CampaignEvent> findByTypeNotInAndCampaignTypeAndStatusAndScheduledTimeBefore(List<String> type, String category, String status, Date scheduledType, Pageable page);
	
	@Query(value = "SELECT DISTINCT reviewRequestId FROM CampaignEvent WHERE campaignId = :campaignId AND status = :initialStatus AND scheduledTime > :scheduledTime")
	public List<Long> findReviewRequestIdByCampaignIdAndStatus(@Param("campaignId") Integer campaignId, @Param("initialStatus") String initialStatus,
			@Param("scheduledTime") Date scheduleTime);
	
	@Query("SELECT id FROM CampaignEvent WHERE campaignId = :campaignId AND status = :initialStatus AND scheduledTime > :scheduledTime")
	public List<Integer> findIdByCampaignIdAndInitialStatusAndScheduledTime(@Param("campaignId") Integer campaignId, @Param("initialStatus") String initialStatus,
			@Param("scheduledTime") Date scheduleTime, Pageable page);
	
	public List<CampaignEvent> findByReviewRequestIdIn(List<Long> reviewRequestIds);
	
	public List<CampaignEvent> findByTypeNotInAndCategoryAndStatusAndIsDNDAndScheduledTimeBeforeAndIsReminderAndReviewRequestIdNotNull(List<String> type, String category,
			String status, Integer isDND, Date scheduledTime, Integer isReminder, Pageable page);
	
	public List<CampaignEvent> findByTypeNotInAndStatusAndIsDNDAndScheduledTimeBeforeAndIsReminderGreaterThan(List<String> type, String status, Integer isDND, Date scheduledTime,
			Integer isReminder, Pageable page);
	
	List<CampaignEvent> findByTypeInAndStatusAndIsDNDAndScheduledTimeBefore(List<String> type, String status, Integer isDND, Date scheduledTime, Pageable page);
}
