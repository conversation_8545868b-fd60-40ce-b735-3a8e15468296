package com.birdeye.campaign.platform.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.dto.BusinessHierarchicalData;
import com.birdeye.campaign.platform.entity.BusinessLevelValueMapping;

@Repository
public interface BusinessLevelValueMappingRepo extends JpaRepository<BusinessLevelValueMapping, Integer> {
	
	@Query(value = "SELECT new com.birdeye.campaign.dto.BusinessHierarchicalData(blvp.levelId, l.name, lv.name) "
			+ "FROM BusinessLevelValueMapping blvp JOIN blvp.levelValue lv JOIN lv.level l "
			+ "WHERE blvp.businessId = :businessId")
	public List<BusinessHierarchicalData> getBusinessHierarchicalData(@Param("businessId") Integer businessId);

}
