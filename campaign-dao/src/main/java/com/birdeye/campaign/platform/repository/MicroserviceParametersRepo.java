package com.birdeye.campaign.platform.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.platform.entity.MicroserviceParameters;

/**
 * Repo for querying @MicroserviceParameters.java
 * 
 * <AUTHOR>
 *
 */
@Repository
public interface MicroserviceParametersRepo extends JpaRepository<MicroserviceParameters, Integer> {
	
	public MicroserviceParameters findFirstByName(String name);
	
}
