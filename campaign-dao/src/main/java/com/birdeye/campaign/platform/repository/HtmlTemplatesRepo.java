package com.birdeye.campaign.platform.repository;

import java.util.List;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.platform.entity.HtmlTemplates;


@Repository
public interface HtmlTemplatesRepo extends JpaRepository<HtmlTemplates, Integer>{

	@Query(value = "SELECT ht FROM HtmlTemplates ht where ht.type = :type and ht.businessId is null and ht.customHtmlTemplate <> 1")
	public List<HtmlTemplates> findByTemplateType(@Param("type")String type);
	
	public List<HtmlTemplates> findByBusinessIdAndCustomHtmlTemplateNotAndTypeIn(Integer businessId, Integer customHtmlTemplate, List<String> type);
	
	@Query(value = "SELECT ht FROM HtmlTemplates ht where ht.type in :templateTypes and ht.businessId is null and ht.customHtmlTemplate <> 1")
	public List<HtmlTemplates> findByTemplateTypes(@Param("templateTypes") List<String> templateTypes);
	
	@Query(value = "SELECT ht FROM HtmlTemplates ht where ht.type = :type and ht.businessId is null and ht.customHtmlTemplate = 1")
	public List<HtmlTemplates> findByTypeDefaultCustomHtml(@Param("type") String type, Pageable pageable);
	
	//GET DEFAULT CUSTOM HTML TEMPLATE BY TYPE
	HtmlTemplates findFirstByTypeAndBusinessIdIsNullAndCustomHtmlTemplateEquals(String type, Integer customHtmlTemplate);
	
	//GET DEFAULT HTML TEMPLATE BY TYPE
	HtmlTemplates findFirstByTypeAndBusinessIdIsNullAndCustomHtmlTemplateNotIn(String type, List<Integer> customHtmlTemplate);
	
	@Query(value = "SELECT ht FROM HtmlTemplates ht where ht.type = :type and ht.businessId is null and ht.customHtmlTemplate <> 1")
	public List<HtmlTemplates> findbyTypeDefaultNonCustomHtml(@Param("type") String type, Pageable pageable);
	
	public List<HtmlTemplates> findByTypeAndBusinessId(String type, Integer businessId);
	
}
