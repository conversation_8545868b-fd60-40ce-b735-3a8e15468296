package com.birdeye.campaign.platform.repository;

import java.util.List;
import java.util.Set;

import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.platform.entity.Review;

/**
 * Repo for querying @Review.java
 * <AUTHOR>
 *
 */
@Repository
public interface ReviewRepo extends JpaRepository<Review, Integer>{
	
	@Query(value = "select r from Review r where r.reviewRequestId in :reviewRequestIds")
	public List<Review> getReviewByParentAndChildReviewRequestIds(@Param("reviewRequestIds")  List<Long> requestIds, Sort sort);
	
	@Query(value = "select ags.id from Review r left join BusinessAggregation ba on r.bizAggregationId = ba.id left join AggregationSource ags on ags.id = ba.sourceIdInt" + 
			" where r.businessId IN :businessIds and ags.id is not null group by ags.id order by count(ags.id) desc")
	public List<Integer> getSourceIdsByCount(@Param("businessIds") List<Integer> businessIds);
	
	@Query("select r from Review r where r.reviewRequestId in :requestIds")
	public List<Review> getReviewByRRIds(@Param("requestIds") Set<Long> requestIds);

}
