package com.birdeye.campaign.platform.readonly.repository;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.campaign.aspect.annotation.ReadOnlyRepository;
import com.birdeye.campaign.platform.entity.BusinessOptions;

@Repository
@ReadOnlyRepository
public interface BusinessOptionsReadOnlyRepo extends JpaRepository<BusinessOptions, Integer> {

	static final String BUSINESS_OPTIONS_BASE_QUERY = "Select bo from BusinessOptions bo";

	@Query(value = "select bo from BusinessOptions bo where bo.businessId = :businessId")
	public List<BusinessOptions> getByBusinessId(@Param("businessId") Integer businessId);

	@Query(value = BUSINESS_OPTIONS_BASE_QUERY + " where bo.businessId = :businessId and bo.business.closed = 0")
	public List<BusinessOptions> getBusinessOptionsByBusinessId(@Param("businessId") Integer businessId);

	@Modifying
	@Transactional
	@Query(value = "UPDATE BusinessOptions bo set bo.birdeyeAsReviewForm = :birdeyeAsReviewForm where bo.businessId = :businessId")
	public void updateBirdeyeAsReviewFormViaBusinessId(@Param("businessId") Integer businessId,
			@Param("birdeyeAsReviewForm") String birdeyeAsReviewForm);
	
	BusinessOptions findFirstByBusinessId(Integer businessId);

}
