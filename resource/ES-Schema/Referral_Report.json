PUT referral_report
{
  "settings": {
    "index": {
      "number_of_shards": "15",
      "number_of_replicas": "3",
      "analysis": {
        "analyzer": {
          "text_search": {
            "filter": "lowercase",
            "type": "custom",
            "tokenizer": "whitespace"
          }
        }
      }
    }
  },
   "mappings": {
      "analytics": {
        "properties": {
          "appointmentIds": {
            "type": "long"
          },
          "businessAlias": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 512
              }
            }
          },
          "businessId": {
            "type": "long"
          },
          "campaignId": {
            "type": "long"
          },
          "clickCount": {
            "type": "long"
          },
          "clickTime": {
            "type": "date",
            "format": "yyyy-MM-dd HH:mm:ss||epoch_millis"
          },
          "clicked": {
            "type": "long"
          },
          "customerId": {
            "type": "long"
          },
          "customerName": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 512
              }
            }
          },
          "enterpriseId": {
            "type": "long"
          },
          "lastClickedSourceId": {
            "type": "long"
          },
          "openCount": {
            "type": "long"
          },
          "openMobile": {
            "type": "long"
          },
          "openTab": {
            "type": "long"
          },
          "openWeb": {
            "type": "long"
          },
          "opened": {
            "type": "long"
          },
          "referralSourceCount": {
            "type": "nested",
            "properties": {
              "lastReferredDate": {
                "type": "date",
                "format": "yyyy-MM-dd HH:mm:ss||epoch_millis"
              },
              "referredCount": {
                "type": "long"
              },
              "sourceId": {
                "type": "long"
              }
            }
          },
          "reminderCount": {
            "type": "long"
          },
          "requestDate": {
            "type": "date",
            "format": "yyyy-MM-dd HH:mm:ss||epoch_millis"
          },
          "requestId": {
            "type": "long"
          },
          "size": {
            "type": "long"
          },
          "source": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          },
          "sourceClicks": {
            "type": "nested",
            "properties": {
              "clickCount": {
                "type": "long"
              },
              "clickTime": {
                "type": "date",
                "format": "yyyy-MM-dd HH:mm:ss||epoch_millis"
              },
              "sourceId": {
                "type": "long"
              }
            }
          },
          "status": {
            "type": "long"
          },
          "templateId": {
            "type": "long"
          },
          "updatedAt": {
            "type": "date",
            "format": "yyyy-MM-dd HH:mm:ss||epoch_millis"
          }
        }
      }
    }
}