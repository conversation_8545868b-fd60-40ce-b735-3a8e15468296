INSERT INTO `elastic_query_template` (`query_name`, `value`)
VALUES
	('PROMOTION_CAMPAIGN_COMMUNICATION_USAGE', '{\n  \"size\": 0,\n  \"query\": {\n    \"bool\": {\n      \"filter\": [\n        {\n          \"term\": {\n            \"enterpriseId\": ${\n              enterpriseId\n            }\n          }\n        },\n        <#if campaignIds??>{\n          \"terms\": {\n            \"campaignId\": ${\n              campaignIds\n            }\n          }\n        },\n        </#if>{\n          \"term\": {\n            \"status\": 1\n          }\n        }\n      ]\n    }\n  },\n  \"aggs\": {\n    \"grp_by_campaign\": {\n      \"terms\": {\n        \"field\": \"campaignId\",\n        \"size\":${\n          aggregationSize\n        }\n      },\n      \"aggs\": {\n        \"request_count\": {\n          \"value_count\": {\n            \"field\": \"requestId\"\n          }\n        },\n        \"open_count\":{\n          \"value_count\": {\n            \"field\": \"opened\"\n          }\n        }\n      }\n    }\n  }\n}');


INSERT INTO `elastic_query_template` (`query_name`, `value`)
VALUES
	('PROMOTION_TEMPLATE_COMMUNICATION_USAGE', '{\n  \"size\": 0,\n  \"query\": {\n    \"bool\": {\n      \"filter\": [\n        {\n          \"term\": {\n            \"enterpriseId\": ${\n              enterpriseId\n            }\n          }\n        },\n        <#if templateIds??>{\n          \"terms\": {\n            \"templateId\": ${\n              templateIds\n            }\n          }\n        },\n        </#if>{\n          \"term\": {\n            \"status\": 1\n          }\n        }\n      ]\n    }\n  },\n  \"aggs\": {\n    \"grp_by_template\": {\n      \"terms\": {\n        \"field\": \"templateId\",\n        \"size\":${\n          aggregationSize\n        }\n      },\n      \"aggs\": {\n        \"request_count\": {\n          \"value_count\": {\n            \"field\": \"requestId\"\n          }\n        },\n        \"open_count\":{\n          \"value_count\": {\n            \"field\": \"opened\"\n          }\n        }\n      }\n    }\n  }\n}');