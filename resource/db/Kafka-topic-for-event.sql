#######KAFKA##########
INSERT INTO `kafka_topic` (`type`,`name`) VALUES ('cx_comm_insert_data','cx-comm-insert-data');
INSERT INTO `kafka_topic` (`type`,`name`) VALUES ('cx_comm_insert_data_test','cx-comm-insert-data-test');
INSERT INTO `kafka_topic` (`type`,`name`) VALUES ('nexus_email','nexus-email');
INSERT INTO `kafka_topic` (`type`,`name`) VALUES ('campaign_request','campaign-request-new');
INSERT INTO `kafka_topic` (`type`,`name`) VALUES ('comm_insert_data_test','comm-insert-data-test');
INSERT INTO `kafka_topic` (`type`,`name`) VALUES ('sms_send','sms-send');
INSERT INTO `kafka_topic` (`type`,`name`) VALUES ('campaign_execute','campaign-execute');
INSERT INTO `kafka_topic` (`type`,`name`) VALUES ('campaign_customer_update','campaign-customer-update');
INSERT INTO `kafka_topic` (`type`,`name`) VALUES ('campaign_rr_create','campaign-rr-create');
INSERT INTO `kafka_topic` (`type`,`name`) VALUES ('survey_comm_insert_data_test','survey-comm-insert-data-test');
INSERT INTO `kafka_topic` (`type`,`name`) VALUES ('ongoing_campaign_rr_create','ongoing-campaign-rr-create');
INSERT INTO `kafka_topic` (`type`,`name`) VALUES ('campaign_customer_update_history','campaign-customer-update-history');
INSERT INTO `kafka_topic` (`type`,`name`) VALUES ('campaign_email_event','campaign-email-event');
INSERT INTO `kafka_topic` (`type`,`name`) VALUES ('campaign_sms_event','campaign-sms-event');
INSERT INTO `kafka_topic` (`type`,`name`) VALUES ('sms_messenger', 'sms-messenger');
INSERT INTO `kafka_topic` (`type`, `name`) VALUES ('cx_comm_migration_data', 'cx-comm-migration-data');


########ES TEMPLATES#######
INSERT INTO `elastic_query_template` (`query_name`,`value`) VALUES ('CX_CAMPAIGN_COMMUNICATION_TEMPLATE','{\n  \"size\": 0,\n  \"query\": {\n    \"bool\": {\n      \"filter\": [\n        {\n          \"term\": {\n            \"enterpriseId\": ${\n              enterpriseId\n            }\n          }\n        },\n        <#if campaignIds??>{\n          \"terms\": {\n            \"campaignId\": ${\n              campaignIds\n            }\n          }\n        },\n        </#if>{\n          \"term\": {\n            \"status\": 1\n          }\n        }\n      ]\n    }\n  },\n  \"aggs\": {\n    \"grp_by_campaign\": {\n      \"terms\": {\n        \"field\": \"campaignId\"<#if aggregationSize??>,\n        \"size\": ${\n          aggregationSize\n        }</#if>\n      },\n      \"aggs\": {\n        \"request_count\": {\n          \"value_count\": {\n            \"field\": \"requestId\"\n          }\n        },\n        \"reminder_count\": {\n          \"sum\": {\n            \"field\": \"reminderCount\"\n          }\n        },\n        \"open_count\": {\n          \"value_count\": {\n            \"field\": \"opened\"\n          }\n        },\n        \"sentiment_click_count\": {\n          \"filter\": {\n            \"exists\": {\n              \"field\": \"clicked\"\n            }\n          },\n          \"aggs\": {\n            \"grp_by_stype\": {\n              \"terms\": {\n                \"field\": \"requestCategory\",\n                \"size\": 3\n              }\n            }\n          }\n        },\n        \"non_recom_count\": {\n          \"filter\": {\n            \"term\": {\n              \"recommended\": 0\n            }\n          }\n        },\n        \"review_click_count\": {\n          \"filter\": {\n            \"exists\": {\n              \"field\": \"sourceId\"\n            }\n          },\n          \"aggs\": {\n            \"grp_by_src\": {\n              \"terms\": {\n                \"field\": \"sourceId\",\n                \"size\": 500\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}');
INSERT INTO `elastic_query_template` (`query_name`,`value`) VALUES ('RR_CAMPAIGN_COMMUNICATION_TEMPLATE','{\n  \"size\": 0,\n  \"query\": {\n    \"bool\": {\n      \"filter\": [\n        {\n          \"term\": {\n            \"e_id\": ${\n              enterpriseId\n            }\n          }\n        },\n        <#if campaignIds??>{\n          \"terms\": {\n            \"campaign_id\": ${\n              campaignIds\n            }\n          }\n        },\n        </#if>{\n          \"terms\": {\n            \"type\": [\n              \"review_request\"\n            ]\n          }\n        },\n        {\n          \"term\": {\n            \"status\": 1\n          }\n        }\n      ]\n    }\n  },\n  \"aggs\": {\n    \"grp_by_campaign\": {\n      \"terms\": {\n        \"field\": \"campaign_id\"<#if aggregationSize??>,\n        \"size\": ${\n          aggregationSize\n        }</#if>\n      },\n      \"aggs\": {\n        \"request_count\": {\n          \"value_count\": {\n            \"field\": \"req_id\"\n          }\n        },\n        \"reminder_count\": {\n          \"sum\": {\n            \"field\": \"rmnd_cnt\"\n          }\n        },\n        \"open_count\": {\n          \"value_count\": {\n            \"field\": \"open_cnt\"\n          }\n        },\n        \"click_count\": {\n          \"terms\": {\n            \"field\": \"s_id\",\n            \"size\": 100\n          }\n        },\n        \"rcmd_click_agg\": {\n          \"filter\": {\n            \"term\": {\n              \"s_id\": 100\n            }\n          },\n          \"aggs\": {\n            \"rcmd_count\": {\n              \"terms\": {\n                \"field\": \"is_promoter\"\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}');
INSERT INTO `elastic_query_template` (`query_name`,`value`) VALUES ('CX_TEMPLATE_COMMUNICATION_USAGE','{   \"size\": 0,   \"query\": {     \"bool\": {       \"filter\": [         {           \"term\": {             \"enterpriseId\": ${               enterpriseId             }           }         },         <#if templateIds??>{           \"terms\": {             \"templateId\": ${               templateIds             }           }         },         </#if>{           \"term\": {             \"status\": 1           }         }       ]     }   },   \"aggs\": {     \"grp_by_template\": {       \"terms\": {         \"field\": \"templateId\"<#if aggregationSize??>,         \"size\": ${           aggregationSize         }</#if>       },       \"aggs\": {         \"request_count\": {           \"value_count\": {             \"field\": \"requestId\"           }         },         \"reminder_count\": {           \"sum\": {             \"field\": \"reminderCount\"           }         },         \"open_count\": {           \"value_count\": {             \"field\": \"opened\"           }         },         \"sentiment_click_count\": {           \"filter\": {             \"exists\": {               \"field\": \"clicked\"             }           },           \"aggs\": {             \"grp_by_stype\": {               \"terms\": {                 \"field\": \"requestCategory\",                 \"size\": 3               }             }           }         },         \"non_recom_count\": {           \"filter\": {             \"term\": {               \"recommended\": 0             }           }         },         \"review_click_count\": {           \"filter\": {             \"exists\": {               \"field\": \"sourceId\"             }           },           \"aggs\": {             \"grp_by_src\": {               \"terms\": {                 \"field\": \"sourceId\",                 \"size\": 500               }             }           }         }       }     }   } }');
INSERT INTO `elastic_query_template` (`query_name`,`value`) VALUES ('RR_TEMPLATE_COMMUNICATION_USAGE','{\"size\":0,\"query\":{\"bool\":{\"filter\":[{\"term\":{\"e_id\":${enterpriseId}}},<#if templateIds??>{\"terms\":{\"template_id\":${templateIds}}},</#if>{\"terms\":{\"type\":[\"review_request\"]}},{\"term\":{\"status\":1}}]}},\"aggs\":{\"grp_by_template\":{\"terms\":{\"field\":\"template_id\"<#if aggregationSize??>,\"size\":${aggregationSize}</#if>},\"aggs\":{\"request_count\":{\"value_count\":{\"field\":\"req_id\"}},\"reminder_count\":{\"sum\":{\"field\":\"rmnd_cnt\"}},\"open_count\":{\"value_count\":{\"field\":\"open_cnt\"}},\"click_count\":{\"terms\":{\"field\":\"s_id\",\"size\":100}},\"rcmd_click_agg\":{\"filter\":{\"term\":{\"s_id\":100}},\"aggs\":{\"rcmd_count\":{\"terms\":{\"field\":\"is_promoter\"}}}}}}}}');
INSERT INTO `elastic_query_template` (`query_name`,`value`) VALUES ('CX_COMMUNICATION_DATA','{ \"from\": 0, \"size\": ${     size   },  \"query\": {     \"bool\": {       \"filter\": [         {           \"term\": {             \"_id\": {               \"value\": ${                 _id               }             }           }         }       ]     }   } }');
INSERT INTO `elastic_query_template` (`query_name`,`value`) VALUES ('RR_COMMUNICATION_DATA','{ \"from\": 0, \"size\": ${     size   },  \"query\": {     \"bool\": {       \"filter\": [         {           \"term\": {             \"_id\": {               \"value\": \"${                 _id               }\"             }           }         }       ]     }   } }');
INSERT INTO `elastic_query_template` (`query_name`,`value`) VALUES ('CX_CAMPAIGN_CUSTOMERS_COUNT','{\n  \"size\": 0,\n  \"query\": {\n    \"bool\": {\n      \"must\": [\n        {\n          \"terms\": {\n            \"businessId\": ${\n              businessIds\n            }\n          }\n        },\n        {\n          \"term\": {\n            \"campaignId\": ${\n              campaignId\n            }\n          }\n        },\n        {\n          \"term\": {\n            \"enterpriseId\": ${\n              enterpriseId\n            }\n          }\n        }<#if searchBy??>,\n        {\n          \"match_phrase_prefix\": {\n            \"customerName\": \"${searchBy}\"\n          }\n        }</#if>\n      ]\n    }\n  },\n  \"aggs\": {\n    \"count\": {\n      \"cardinality\": {\n        \"field\": \"customerId\"\n      }\n    }\n  }\n}');
INSERT INTO `elastic_query_template` (`query_name`,`value`) VALUES ('CX_CAMPAIGN_CUSTOMERS_TEMPLATE','{\n  \"from\": ${\n    from\n  },\n  \"size\": ${\n    size\n  },\n  \"sort\": [\n    {\n      \"${sortBy}\": {\n        \"order\": \"${ sortOrder }\",\n        \"unmapped_type\": \"long\"\n      }\n    },\n    {\n      \"updatedAt\": {\n        \"order\": \"${ sortOrder }\"\n      }\n    }\n  ],\n  \"query\": {\n    \"bool\": {\n      \"must\": [\n        {\n          \"terms\": {\n            \"businessId\": ${\n              businessIds\n            }\n          }\n        },\n        {\n          \"term\": {\n            \"campaignId\": ${\n              campaignId\n            }\n          }\n        },\n        {\n          \"term\": {\n            \"enterpriseId\": ${\n              enterpriseId\n            }\n          }\n        }<#if searchBy??>,\n        {\n          \"match_phrase_prefix\": {\n            \"customerName\": \"${searchBy}\"\n          }\n        }</#if>\n      ]\n    }\n  }\n}');
INSERT INTO `elastic_query_template` (`query_name`,`value`) VALUES ('RR_CAMPAIGN_CUSTOMERS_COUNT','{\n  \"size\": 0,\n  \"query\": {\n    \"bool\": {\n      \"must\": [\n        {\n          \"terms\": {\n            \"b_id\": ${\n              businessIds\n            }\n          }\n        },\n        {\n          \"term\": {\n            \"campaign_id\": ${\n              campaignId\n            }\n          }\n        },\n        {\n          \"term\": {\n            \"e_id\": ${\n              enterpriseId\n            }\n          }\n        }<#if searchBy??>,\n        {\n          \"match_phrase_prefix\": {\n            \"customerName\": \"${searchBy}\"\n          }\n        }</#if>\n      ]\n    }\n  },\n  \"aggs\": {\n    \"count\": {\n      \"cardinality\": {\n        \"field\": \"cust_id\"\n      }\n    }\n  }\n}');
INSERT INTO `elastic_query_template` (`query_name`,`value`) VALUES ('RR_CAMPAIGN_CUSTOMERS_TEMPLATE','{\n  \"from\": ${\n    from\n  },\n  \"size\": ${\n    size\n  },\n  \"sort\": [\n    {\n      \"${sortBy}\": {\n        \"order\": \"${ sortOrder }\",\n        \"unmapped_type\": \"long\"\n      }\n    },\n    {\n      \"c_time\": {\n        \"order\": \"${ sortOrder }\"\n      }\n    }\n  ],\n  \"query\": {\n    \"bool\": {\n      \"must\": [\n        {\n          \"terms\": {\n            \"b_id\": ${\n              businessIds\n            }\n          }\n        },\n        {\n          \"term\": {\n            \"campaign_id\": ${\n              campaignId\n            }\n          }\n        },\n        {\n          \"term\": {\n            \"e_id\": ${\n              enterpriseId\n            }\n          }\n        }<#if searchBy??>,\n        {\n          \"match_phrase_prefix\": {\n            \"customerName\": \"${searchBy}\"\n          }\n        }</#if>\n      ]\n    }\n  }\n}');
INSERT INTO `elastic_query_template` (`query_name`,`value`) VALUES ('CX_CAMPAIGN_COMMUNICATIONS_COUNT','{\n  \"size\": 0,\n  \"query\": {\n    \"bool\": {\n      \"must\": [\n        {\n          \"terms\": {\n            \"businessId\": ${\n              businessIds\n            }\n          }\n        },\n        {\n          \"term\": {\n            \"campaignId\": ${\n              campaignId\n            }\n          }\n        },\n        {\n          \"term\": {\n            \"enterpriseId\": ${\n              enterpriseId\n            }\n          }\n        }<#if searchBy??>,\n        {\n          \"match_phrase_prefix\": {\n            \"customerName\": \"${searchBy}\"\n          }\n        }</#if>\n      ]\n    }\n  },\n  \"aggs\": {\n    \"count\": {\n      \"cardinality\": {\n        \"field\": \"requestId\"\n      }\n    }\n  }\n}');
INSERT INTO `elastic_query_template` (`query_name`,`value`) VALUES ('RR_CAMPAIGN_COMMUNICATIONS_COUNT','{\n  \"size\": 0,\n  \"query\": {\n    \"bool\": {\n      \"must\": [\n        {\n          \"terms\": {\n            \"b_id\": ${\n              businessIds\n            }\n          }\n        },\n        {\n          \"term\": {\n            \"campaign_id\": ${\n              campaignId\n            }\n          }\n        },\n        {\n          \"term\": {\n            \"e_id\": ${\n              enterpriseId\n            }\n          }\n        }<#if searchBy??>,\n        {\n          \"match_phrase_prefix\": {\n            \"customerName\": \"${searchBy}\"\n          }\n        }</#if>\n      ]\n    }\n  },\n  \"aggs\": {\n    \"count\": {\n      \"cardinality\": {\n        \"field\": \"req_id\"\n      }\n    }\n  }\n}');
INSERT INTO `elastic_query_template` (`query_name`,`value`) VALUES ('SURVEY_COMMUNICATION_DATA','{\n  \"from\": 0,\n  \"size\": ${\n    size\n  },\n  \"query\": {\n    \"bool\": {\n      \"filter\": [\n        {\n          \"term\": {\n            \"_id\": {\n              \"value\": \"${                 _id               }\"\n            }\n          }\n        }\n      ]\n    }\n  }\n}');
INSERT INTO `elastic_query_template` (`query_name`,`value`) VALUES ('SURVEY_CAMPAIGN_CUSTOMERS_TEMPLATE','{\n  \"from\": ${\n    from\n  },\n  \"size\": ${\n    size\n  },\n  \"sort\": [\n    {\n      \"${sortBy}\": {\n        \"order\": \"${ sortOrder }\",\n        \"unmapped_type\": \"long\"\n      }\n    },\n    {\n      \"c_time\": {\n        \"order\": \"${ sortOrder }\",\n        \"unmapped_type\": \"long\"\n      }\n    }\n  ],\n  \"query\": {\n    \"bool\": {\n      \"must\": [\n        {\n          \"terms\": {\n            \"b_id\": ${\n              businessIds\n            }\n          }\n        },\n        {\n          \"term\": {\n            \"campaign_id\": ${\n              campaignId\n            }\n          }\n        },\n        {\n          \"term\": {\n            \"e_id\": ${\n              enterpriseId\n            }\n          }\n        }<#if searchBy??>,\n        {\n          \"match_phrase_prefix\": {\n            \"customerName\": \"${searchBy}\"\n          }\n        }</#if>\n      ]\n    }\n  }\n}');
INSERT INTO `elastic_query_template` (`query_name`,`value`) VALUES ('SURVEY_CAMPAIGN_CUSTOMERS_COUNT','{\n  \"size\": 0,\n  \"query\": {\n    \"bool\": {\n      \"must\": [\n        {\n          \"terms\": {\n            \"b_id\": ${\n              businessIds\n            }\n          }\n        },\n        {\n          \"term\": {\n            \"campaign_id\": ${\n              campaignId\n            }\n          }\n        },\n        {\n          \"term\": {\n            \"e_id\": ${\n              enterpriseId\n            }\n          }\n        }<#if searchBy??>,\n        {\n          \"match_phrase_prefix\": {\n            \"customerName\": \"${searchBy}\"\n          }\n        }</#if>\n      ]\n    }\n  },\n  \"aggs\": {\n    \"count\": {\n      \"cardinality\": {\n        \"field\": \"customer_id\"\n      }\n    }\n  }\n}');
INSERT INTO `elastic_query_template` (`query_name`,`value`) VALUES ('SURVEY_CAMPAIGN_COMMUNICATIONS_COUNT','{\n  \"size\": 0,\n  \"query\": {\n    \"bool\": {\n      \"must\": [\n        {\n          \"terms\": {\n            \"b_id\": ${\n              businessIds\n            }\n          }\n        },\n        {\n          \"term\": {\n            \"campaign_id\": ${\n              campaignId\n            }\n          }\n        },\n        {\n          \"term\": {\n            \"e_id\": ${\n              enterpriseId\n            }\n          }\n        }<#if searchBy??>,\n        {\n          \"match_phrase_prefix\": {\n            \"customerName\": \"${searchBy}\"\n          }\n        }</#if>\n      ]\n    }\n  },\n  \"aggs\": {\n    \"count\": {\n      \"cardinality\": {\n        \"field\": \"req_id\"\n      }\n    }\n  }\n}');
INSERT INTO `elastic_query_template` (`query_name`,`value`) VALUES ('SURVEY_CAMPAIGN_COMMUNICATION_TEMPLATE','{\n  \"size\": 0,\n  \"query\": {\n    \"bool\": {\n      \"filter\": [\n        {\n          \"term\": {\n            \"e_id\": ${\n              enterpriseId\n            }\n          }\n        },\n        <#if campaignIds??>{\n          \"terms\": {\n            \"campaign_id\": ${\n              campaignIds\n            }\n          }\n        },\n        </#if>{\n          \"term\": {\n            \"status\": 1\n          }\n        }\n      ]\n    }\n  },\n  \"aggs\": {\n    \"grp_by_campaign\": {\n      \"terms\": {\n        \"field\": \"campaign_id\"<#if aggregationSize??>,\n        \"size\": ${\n          aggregationSize\n        }</#if>\n      },\n      \"aggs\": {\n        \"request_count\": {\n          \"value_count\": {\n            \"field\": \"req_id\"\n          }\n        },\n        \"survey_started_count\": {\n          \"sum\": {\n            \"field\": \"isSurveyStarted\"\n          }\n        },\n        \"survey_completed_count\": {\n          \"sum\": {\n            \"field\": \"isSurveyCompleted\"\n          }\n        }\n      }\n    }\n  }\n}');
INSERT INTO `elastic_query_template` (`query_name`,`value`) VALUES ('SURVEY_TEMPLATE_COMMUNICATION_USAGE','{\n  \"size\": 0,\n  \"query\": {\n    \"bool\": {\n      \"filter\": [\n        {\n          \"term\": {\n            \"e_id\": ${\n              enterpriseId\n            }\n          }\n        },\n        <#if templateIds??>{\n          \"terms\": {\n            \"template_id\": ${\n              templateIds\n            }\n          }\n        },\n        </#if>{\n          \"term\": {\n            \"status\": 1\n          }\n        }\n      ]\n    }\n  },\n  \"aggs\": {\n    \"grp_by_template\": {\n      \"terms\": {\n        \"field\": \"template_id\"<#if aggregationSize??>,\n        \"size\": ${\n          aggregationSize\n        }</#if>\n      },\n      \"aggs\": {\n        \"request_count\": {\n          \"value_count\": {\n            \"field\": \"req_id\"\n          }\n        },\n        \"survey_started_count\": {\n          \"sum\": {\n            \"field\": \"isSurveyStarted\"\n          }\n        },\n        \"survey_completed_count\": {\n          \"sum\": {\n            \"field\": \"isSurveyCompleted\"\n          }\n        }\n      }\n    }\n  }\n}');
