CREATE TABLE `business_onboard_audit` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `business_id` int(11) DEFAULT NULL,
  `business_number` bigint(20) DEFAULT NULL,
  `enterprise_id` int(20) DEFAULT NULL,
  `business_type` varchar(30) DEFAULT NULL,
  `account_type` varchar(30) DEFAULT NULL,
  `reseller_id` int(11) DEFAULT NULL,
  `name` varchar(30) DEFAULT NULL,
  `sms_template_id` int(11) DEFAULT NULL,
  `email_template_id` int(11) DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `email` varchar(400) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `failure_reason` varchar(500) DEFAULT NULL,
  `status` varchar(45) DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `is_smb` int(11) DEFAULT NULL,
  `is_enterprise` int(11) DEFAULT NULL,
  `is_enterprise_Location` int(11) DEFAULT NULL,
  `is_reseller` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `business_id` (`business_id`),
  KEY `enterprise_id` (`enterprise_id`),
  KEY `user_id` (`user_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=latin1;
