/*
 * Author Name   : <PERSON><PERSON>
 * Creation Date : 06-Nov-2023
 * <PERSON>ra Ticket Id: https://birdeye.atlassian.net/browse/BIRD-7690
 */

/*
 * Description   : Create a new table 'account_global_template_mapping' to store data pertaining to global templates
 *				   which are modified by an enterprise.
 */
CREATE TABLE `account_global_template_mapping` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` int(11) DEFAULT NULL,
  `global_temp_id` int(11) DEFAULT NULL,
  `sms_template_id` int(11) DEFAULT NULL,
  `email_template_id` int(11) DEFAULT NULL,
  `is_deleted` int(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  <PERSON><PERSON>Y `account_id_idx` (`account_id`),
  <PERSON><PERSON><PERSON> `global_temp_id_idx` (`global_temp_id`),
  KEY `account_global_temp_idx` (`account_id`,`global_temp_id`),
  KEY `created_at_idx` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

/* 
 * Description   : Create a new table 'global_templates' to store data pertaining to global templates.
 */
CREATE TABLE `global_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `message_body` text COLLATE utf8mb4_unicode_ci,
  `industry` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `template_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `source` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `template_type_idx` (`template_type`),
  KEY `industry_idx` (`industry`),
  KEY `source_type_industry_idx` (`source`,`template_type`,`industry`),
  KEY `type_industry_idx` (`template_type`,`industry`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

/*
 * Description : Inserting global.template.epoch.time.allowed system property, which is to fetch allowed time based on which
 * it can be inferred whether a business is eligible to show global template list
 */
insert into system_property(name,value) values("global.template.epoch.time.allowed","0");


/*
 * Description : Inserting global.templates.supported.types system property, which is to fetch global templates supported template types
 */
insert into system_property(name,value) values("global.templates.supported.types","promotion");


/*
 * Description : Inserting global.templates.mandatory.industry system property, which is to fetch mandatory global templates industry that will be 
 *         applicable for all business.
 */
insert into system_property(name,value) values("global.templates.mandatory.industry","General");
