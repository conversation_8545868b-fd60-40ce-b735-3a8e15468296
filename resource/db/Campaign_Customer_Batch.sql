CREATE TABLE `campaign_customer_batch` (
  `id` bigint(15) NOT NULL AUTO_INCREMENT,
  `batch_id` bigint(15) NOT NULL,
  `campaign_id` int(11) NOT NULL,
  `batch_data` json DEFAULT NULL,
  `status` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `index_batch_id` (`batch_id`),
  KEY `index_campaign_id` (`campaign_id`),
  KEY `index_status` (`status`),
  KEY `index_created_at` (`created_at`),
  KEY `index_updated_at` (`updated_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


INSERT INTO `campaign`.`system_property` (`name`, `value`) VALUES ('campaign.cust.batch.poller.batch.size', '20');
INSERT INTO `campaign`.`system_property` (`name`, `value`) VALUES ('customer.batch.gap.ms', '6000');
INSERT INTO `campaign`.`system_property` (`name`, `value`) VALUES ('customer.batch.threshold', '5000');
INSERT INTO `campaign`.`system_property` (`name`, `value`) VALUES ('customer.batch.size', '5000');

INSERT INTO `kafka_topic` (`type`, `name`) VALUES ('campaign_cust_batch_event', 'campaign-cust-batch-event');