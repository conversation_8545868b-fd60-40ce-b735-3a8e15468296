{
  "from" : ${from},
  "size": ${size},
  "query": {
    "bool": {
        "must": [
        {
          "terms": {
            "businessId": [${businessIds}]
          }
        },
        {
          "range": {
            "requestDate": {
              "gte": "${startDate}",
              "lte": "${endDate}"
            }
          }
        },
        {
          "term": {
            "enterpriseId": ${
              enterpriseId
            }
          }
        },
        {
          "match": {
            "clicked": 1
          }
        }
        <#if searchBy??>,
         {
        "bool": {
        "should": [
              {
                "wildcard": {
                  "customerName.keyword": {
                    "value": "${searchByWildcard}*"
                  }
                }
              },
        {
          "match_phrase_prefix": {
            "customerName": {
            "query":"${searchBy}",
            "max_expansions": 5000
            }
          }
          }
          ]
        }
        }</#if>
        <#if sourceIds??>,
        {
          "nested": {
            "path": "sourceClicks",
            "query": {
              "bool": {
                "must": [
                  {
                    "terms": {
                      "sourceClicks.sourceId": [${sourceIds}]
                    }
                  }
                ]
              }
            }
          }
        }
        </#if>
      ]
    }
  },
 "sort": [
    {
      "${sortBy}": {
        "order": "${ sortOrder }",
        "unmapped_type": "long"
      }
    }
  ]
}