package com.birdeye.email.templates.ai.request;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.ai.sro.CustomFieldSRO;
import com.birdeye.templates.ai.sro.EmailTemplateAiTrackableButtonInfo;
import com.birdeye.templates.ai.sro.TemplateAIReviewSourceSRO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_NULL)
public class EmailTemplateAISaveRequest implements Serializable {
	
	/**
	 * 
	 */
	private static final long							serialVersionUID	= 2118082810004818484L;
	
	private Integer										templateId;									// Field added for Internal Use, Not sent By UI
	
	private Integer										userId;										// Field added for Internal Use, Not sent By UI
	
	private Integer										accountId;									// Field added for Internal Use, Not sent By UI
	
	private String										templateType;								// Field added for Internal Use, Not sent By UI
	
	private String										name;
	
	private String										subject;
	
	private String										html;
	
	private String										thumbnailUrl;
	
	private List<TemplateAIReviewSourceSRO>				selectedSources;
	
	private MetaData									metadata;
	
	private List<String>								attachmentUrls;
	
	private List<CustomFieldSRO>						contactCustomFields;
	
	private Integer										enableReplyToInbox;
	
	private Integer										brandkitId;
	
	private List<CustomFieldSRO>						locationCustomFields;
	
	private List<EmailTemplateAiTrackableButtonInfo>	trackableButtonInfo;
	
	private String										templateCategory;
	
	private Integer										locationTemplate;
	
	private List<Integer>								selectedLocations;
	
	private String										previewText;
	
	@JsonIgnoreProperties(ignoreUnknown = true)
	@JsonInclude(value = Include.NON_NULL)
	public static class MetaData implements Serializable {
		/**
		 * 
		 */
		private static final long	serialVersionUID	= 3649738489067738156L;
		
		private Integer				locationBrandingEnabled;
		
		private Integer				showRating;
		
		private Integer				showReviewCount;
		
		public Integer getLocationBrandingEnabled() {
			return locationBrandingEnabled;
		}
		
		public void setLocationBrandingEnabled(Integer locationBrandingEnabled) {
			this.locationBrandingEnabled = locationBrandingEnabled;
		}
		
		public Integer getShowRating() {
			return showRating;
		}
		
		public void setShowRating(Integer showRating) {
			this.showRating = showRating;
		}
		
		public Integer getShowReviewCount() {
			return showReviewCount;
		}
		
		public void setShowReviewCount(Integer showReviewCount) {
			this.showReviewCount = showReviewCount;
		}
		
		@Override
		public String toString() {
			ReflectionToStringBuilder sb = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
			return sb.toString();
		}
	}
	
	/**
	 * @return the templateId
	 */
	public Integer getTemplateId() {
		return templateId;
	}

	/**
	 * @param templateId the templateId to set
	 */
	public void setTemplateId(Integer templateId) {
		this.templateId = templateId;
	}

	/**
	 * @return the userId
	 */
	public Integer getUserId() {
		return userId;
	}

	/**
	 * @param userId the userId to set
	 */
	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	/**
	 * @return the accountId
	 */
	public Integer getAccountId() {
		return accountId;
	}

	/**
	 * @param accountId the accountId to set
	 */
	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}

	/**
	 * @return the templateType
	 */
	public String getTemplateType() {
		return templateType;
	}

	/**
	 * @param templateType the templateType to set
	 */
	public void setTemplateType(String templateType) {
		this.templateType = templateType;
	}

	/**
	 * @return the name
	 */
	public String getName() {
		return name;
	}
	
	/**
	 * @param name
	 *            the name to set
	 */
	public void setName(String name) {
		this.name = name;
	}
	
	/**
	 * @return the subject
	 */
	public String getSubject() {
		return subject;
	}
	
	/**
	 * @param subject
	 *            the subject to set
	 */
	public void setSubject(String subject) {
		this.subject = subject;
	}
	
	/**
	 * @return the html
	 */
	public String getHtml() {
		return html;
	}
	
	/**
	 * @param html
	 *            the html to set
	 */
	public void setHtml(String html) {
		this.html = html;
	}
	
	/**
	 * @return the thumbnailUrl
	 */
	public String getThumbnailUrl() {
		return thumbnailUrl;
	}
	
	/**
	 * @param thumbnailUrl
	 *            the thumbnailUrl to set
	 */
	public void setThumbnailUrl(String thumbnailUrl) {
		this.thumbnailUrl = thumbnailUrl;
	}
	
	/**
	 * @return the selectedSources
	 */
	public List<TemplateAIReviewSourceSRO> getSelectedSources() {
		return selectedSources;
	}
	
	/**
	 * @param selectedSources
	 *            the selectedSources to set
	 */
	public void setSelectedSources(List<TemplateAIReviewSourceSRO> selectedSources) {
		this.selectedSources = selectedSources;
	}
	
	/**
	 * @return the metadata
	 */
	public MetaData getMetadata() {
		return metadata;
	}
	
	/**
	 * @param metadata
	 *            the metadata to set
	 */
	public void setMetadata(MetaData metadata) {
		this.metadata = metadata;
	}
	
	/**
	 * @return the attachmentUrls
	 */
	public List<String> getAttachmentUrls() {
		return attachmentUrls;
	}
	
	/**
	 * @param attachmentUrls
	 *            the attachmentUrls to set
	 */
	public void setAttachmentUrls(List<String> attachmentUrls) {
		this.attachmentUrls = attachmentUrls;
	}
	
	/**
	 * @return the contactCustomFields
	 */
	public List<CustomFieldSRO> getContactCustomFields() {
		return contactCustomFields;
	}
	
	/**
	 * @param contactCustomFields
	 *            the contactCustomFields to set
	 */
	public void setContactCustomFields(List<CustomFieldSRO> contactCustomFields) {
		this.contactCustomFields = contactCustomFields;
	}
	
	/**
	 * @return the enableReplyToInbox
	 */
	public Integer getEnableReplyToInbox() {
		return enableReplyToInbox;
	}
	
	/**
	 * @param enableReplyToInbox
	 *            the enableReplyToInbox to set
	 */
	public void setEnableReplyToInbox(Integer enableReplyToInbox) {
		this.enableReplyToInbox = enableReplyToInbox;
	}
	
	/**
	 * @return the brandkitId
	 */
	public Integer getBrandkitId() {
		return brandkitId;
	}
	
	/**
	 * @param brandkitId
	 *            the brandkitId to set
	 */
	public void setBrandkitId(Integer brandkitId) {
		this.brandkitId = brandkitId;
	}
	
	/**
	 * @return the locationCustomFields
	 */
	public List<CustomFieldSRO> getLocationCustomFields() {
		return locationCustomFields;
	}
	
	/**
	 * @param locationCustomFields
	 *            the locationCustomFields to set
	 */
	public void setLocationCustomFields(List<CustomFieldSRO> locationCustomFields) {
		this.locationCustomFields = locationCustomFields;
	}
	
	/**
	 * @return the trackableButtonInfo
	 */
	public List<EmailTemplateAiTrackableButtonInfo> getTrackableButtonInfo() {
		return trackableButtonInfo;
	}
	
	/**
	 * @param trackableButtonInfo
	 *            the trackableButtonInfo to set
	 */
	public void setTrackableButtonInfo(List<EmailTemplateAiTrackableButtonInfo> trackableButtonInfo) {
		this.trackableButtonInfo = trackableButtonInfo;
	}
	
	/**
	 * @return the templateCategory
	 */
	public String getTemplateCategory() {
		return templateCategory;
	}
	
	/**
	 * @param templateCategory
	 *            the templateCategory to set
	 */
	public void setTemplateCategory(String templateCategory) {
		this.templateCategory = templateCategory;
	}
	
	/**
	 * @return the locationTemplate
	 */
	public Integer getLocationTemplate() {
		return locationTemplate;
	}
	
	/**
	 * @param locationTemplate
	 *            the locationTemplate to set
	 */
	public void setLocationTemplate(Integer locationTemplate) {
		this.locationTemplate = locationTemplate;
	}
	
	/**
	 * @return the selectedLocations
	 */
	public List<Integer> getSelectedLocations() {
		return selectedLocations;
	}
	
	/**
	 * @param selectedLocations
	 *            the selectedLocations to set
	 */
	public void setSelectedLocations(List<Integer> selectedLocations) {
		this.selectedLocations = selectedLocations;
	}
	
	/**
	 * @return the previewText
	 */
	public String getPreviewText() {
		return previewText;
	}
	
	/**
	 * @param previewText
	 *            the previewText to set
	 */
	public void setPreviewText(String previewText) {
		this.previewText = previewText;
	}
	
	@Override
	public String toString() {
		return new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE).toString();
	}
	
}
