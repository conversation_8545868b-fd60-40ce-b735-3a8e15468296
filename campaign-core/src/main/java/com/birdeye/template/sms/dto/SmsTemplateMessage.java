package com.birdeye.template.sms.dto;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import javax.annotation.Generated;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.dto.TemplateLabelConfigurations;
import com.birdeye.campaign.entity.BusinessSmsTemplate;
import com.birdeye.campaign.enums.EmailTemplateTypes;
import com.birdeye.campaign.platform.constant.TemplateTypeEnum;
import com.birdeye.campaign.utils.BusinessTemplateUtils;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.TemplateUtils;

public class SmsTemplateMessage {
	private Integer id;
	private String name;
	private String messageFrom;
	private String messageBody;
	private String question;
	private String positiveLinkLabel;
	private String negativeLinkLabel;
	private Integer isSentimentCheckEnabled;


	private Integer locationBrandingEnabled;
	
	private String nonRecommendReviewPageMessage;
	private String templateType;
	private Integer mmsEnable;
	private String mediaUrl;
	private String mediaType;
	private Integer isDefault;
	private String nonRecommendedUrl;
	private String sentimentCheckType;
	private Integer starRatingMin;
	private Integer npsRatingMin;
	private List<String> npsLabels = new ArrayList<>();
	private List<String> starLabels = new ArrayList<>();
	private boolean showEmoticon;
	private boolean overrideSentimentCheck = false;
	private String unsubscribeText;
	private Integer unsubscribeTextEnabled;

	private Integer isContactUsCheckEnabled;
	private String contactUsMessage;
	private String contactUsButtonText;
	private String contactButtonTextColor;
	private String contactButtonColor;

	private String recommendPageHeading;
	private String recommendPageMessage;

	private String customImageUrl;

	// cx template fields
	private String neutralLinkLabel;

	private Integer excludeNeutral;

	// using this field as sentiment message for CX template
	private String writeReviewQuestion;

	private String starHeading;

	private String sentimentHeading;

	private String starMessage;

	private String sentimentMessage;

	//new fields
	private Integer enableFeedbackMessage;

	private String feedbackCallbackMessage;

	private Integer enableFeedbackCheckbox;

	private String thankyouMessage;

	private String thankyouHeading;

	private String reviewSiteButtonColor;

	private String reviewSiteButtonTextColor;

	private Integer reviewEnable;
	
	private String referralMessage;
	
	private List<String> mediaUrls;
	
	private String referralQuestion;
	
	private List<String>	referralOptions;
	
	private Integer referralContactOptionEnabled;
	
	//Flag introduced to indicate if a template is a global template or not --> BIRD-52
	private Boolean isGlobalTemplate = false;
	
	private String			referralMessageEmailSubject;
	private Integer			referralMessageCtaUrlEnabled;
	private String			referralMessageCtaUrl;
	private String			referralFormHeading;
	private String			referralFormMessage;
	private String			referralFormCtaLabel;
	private String			tyPageAlternateUrl;
	private Integer			tyPageEnabled;
	private String			thankYouCtaLabel;
	private String			thankYouCtaUrl;
	private Integer			showZipCode;
	
	private Integer			confirmButtonEnabled;
	private Integer			rescheduleButtonEnabled;
	private Integer			cancelButtonEnabled;
	private Integer			bookAppointmentButtonEnabled;
	private String			feedbackTextLabel;
	private String			submitButtonText;
	private String			formUrl;
	private Integer			locationLevelTemplate = 0;
	private List<Integer> 	selectedLocations;
	
	private String			feedbackNamePlaceholder;
	private String			feedbackEmailPlaceholder;
	private String			directFeedbackHeading;
	private String			feedbackMessagePositiveSentiment;
	private List<String>	starsSubText;
	private String			directFeedbackNextButtonText;
	private String			referralLinkLabel;
	private String			referralFirstNamePlaceholder;
	private String			referralLastNamePlaceholder;
	private String			referralEmailPlaceholder;
	private String			referralPhonePlaceholder;
	private String			assistedByPlaceholder;
	private String          smsCategory;
	
	@Generated("SparkTools")
	private SmsTemplateMessage(Builder builder) {
		this.id = builder.id;
		this.name = builder.name;
		this.messageFrom = builder.messageFrom;
		this.messageBody = builder.messageBody;
		this.question = builder.question;
		this.positiveLinkLabel = builder.positiveLinkLabel;
		this.negativeLinkLabel = builder.negativeLinkLabel;
		this.isSentimentCheckEnabled = builder.isSentimentCheckEnabled;
		this.nonRecommendReviewPageMessage = builder.nonRecommendReviewPageMessage;
		this.templateType = builder.templateType;
		this.mmsEnable = builder.mmsEnable;
		this.mediaUrl = builder.mediaUrl;
		this.mediaType = builder.mediaType;
		this.isDefault = builder.isDefault;
		this.nonRecommendedUrl = builder.nonRecommendedUrl;
		this.sentimentCheckType = builder.sentimentCheckType;
		this.starRatingMin = builder.starRatingMin;
		this.npsRatingMin = builder.npsRatingMin;
		this.npsLabels = builder.npsLabels;
		this.starLabels = builder.starLabels;
		this.showEmoticon = builder.showEmoticon;
		this.overrideSentimentCheck = builder.overrideSentimentCheck;
		this.unsubscribeText = builder.unsubscribeText;
		this.unsubscribeTextEnabled = builder.unsubscribeTextEnabled;
		this.isContactUsCheckEnabled = builder.isContactUsCheckEnabled;
		this.contactUsMessage = builder.contactUsMessage;
		this.contactUsButtonText = builder.contactUsButtonText;
		this.contactButtonTextColor = builder.contactButtonTextColor;
		this.contactButtonColor = builder.contactButtonColor;
		this.recommendPageHeading = builder.recommendPageHeading;
		this.recommendPageMessage = builder.recommendPageMessage;
		this.customImageUrl = builder.customImageUrl;
		this.neutralLinkLabel = builder.neutralLinkLabel;
		this.excludeNeutral = builder.excludeNeutral;
		this.writeReviewQuestion = builder.writeReviewQuestion;
		this.starHeading = builder.starHeading;
		this.sentimentHeading = builder.sentimentHeading;
		this.starMessage = builder.starMessage;
		this.sentimentMessage = builder.sentimentMessage;
		this.referralMessage = builder.referralMessage;
		this.feedbackTextLabel = builder.feedbackTextLabel;
		this.submitButtonText = builder.submitButtonText;
		this.formUrl = builder.formUrl;
		this.feedbackNamePlaceholder = builder.feedbackNamePlaceholder;
		this.feedbackEmailPlaceholder = builder.feedbackEmailPlaceholder;
		this.referralLinkLabel = builder.referralLinkLabel;
		this.referralFirstNamePlaceholder = builder.referralFirstNamePlaceholder;
		this.referralLastNamePlaceholder = builder.referralLastNamePlaceholder;
		this.referralEmailPlaceholder = builder.referralEmailPlaceholder;
		this.referralPhonePlaceholder = builder.referralPhonePlaceholder;
		this.directFeedbackHeading = builder.directFeedbackHeading;
		this.feedbackMessagePositiveSentiment = builder.feedbackMessagePositiveSentiment;
		this.starsSubText = builder.starsSubText;
		this.directFeedbackNextButtonText = builder.directFeedbackNextButtonText;
		this.assistedByPlaceholder = builder.assistedByPlaceholder;
		this.smsCategory = builder.smsCategory;
	}
	
	public String getFeedbackTextLabel() {
		return feedbackTextLabel;
	}

	public void setFeedbackTextLabel(String feedbackTextLabel) {
		this.feedbackTextLabel = feedbackTextLabel;
	}

	public String getSubmitButtonText() {
		return submitButtonText;
	}

	public void setSubmitButtonText(String submitButtonText) {
		this.submitButtonText = submitButtonText;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getMessageFrom() {
		return messageFrom;
	}

	public void setMessageFrom(String messageFrom) {
		this.messageFrom = messageFrom;
	}

	public String getMessageBody() {
		return messageBody;
	}

	public void setMessageBody(String messageBody) {
		this.messageBody = messageBody;
	}

	public String getQuestion() {
		return question;
	}

	public void setQuestion(String question) {
		this.question = question;
	}

	public String getPositiveLinkLabel() {
		return positiveLinkLabel;
	}

	public void setPositiveLinkLabel(String positiveLinkLabel) {
		this.positiveLinkLabel = positiveLinkLabel;
	}

	public String getNegativeLinkLabel() {
		return negativeLinkLabel;
	}

	public void setNegativeLinkLabel(String negativeLinkLabel) {
		this.negativeLinkLabel = negativeLinkLabel;
	}

	public Integer getIsSentimentCheckEnabled() {
		return isSentimentCheckEnabled;
	}

	public void setIsSentimentCheckEnabled(Integer isSentimentCheckEnabled) {
		this.isSentimentCheckEnabled = isSentimentCheckEnabled;
	}

	public String getNonRecommendReviewPageMessage() {
		return nonRecommendReviewPageMessage;
	}

	public void setNonRecommendReviewPageMessage(String nonRecommendReviewPageMessage) {
		this.nonRecommendReviewPageMessage = nonRecommendReviewPageMessage;
	}

	public String getTemplateType() {
		return templateType;
	}

	public void setTemplateType(String templateType) {
		this.templateType = templateType;
	}

	public Integer getMmsEnable() {
		return mmsEnable;
	}

	public void setMmsEnable(Integer mmsEnable) {
		this.mmsEnable = mmsEnable;
	}

	public String getMediaUrl() {
		return mediaUrl;
	}

	public void setMediaUrl(String mediaUrl) {
		this.mediaUrl = mediaUrl;
	}

	public String getMediaType() {
		return mediaType;
	}

	public void setMediaType(String mediaType) {
		this.mediaType = mediaType;
	}

	public Integer getIsDefault() {
		return isDefault;
	}

	public void setIsDefault(Integer isDefault) {
		this.isDefault = isDefault;
	}

	public String getNonRecommendedUrl() {
		return nonRecommendedUrl;
	}

	public void setNonRecommendedUrl(String nonRecommendedUrl) {
		this.nonRecommendedUrl = nonRecommendedUrl;
	}

	public String getSentimentCheckType() {
		return sentimentCheckType;
	}

	public void setSentimentCheckType(String sentimentCheckType) {
		this.sentimentCheckType = sentimentCheckType;
	}

	public Integer getStarRatingMin() {
		return starRatingMin;
	}

	public void setStarRatingMin(Integer starRatingMin) {
		this.starRatingMin = starRatingMin;
	}

	public Integer getNpsRatingMin() {
		return npsRatingMin;
	}

	public void setNpsRatingMin(Integer npsRatingMin) {
		this.npsRatingMin = npsRatingMin;
	}

	public List<String> getNpsLabels() {
		return npsLabels;
	}

	public void setNpsLabels(List<String> npsLabels) {
		this.npsLabels = npsLabels;
	}

	public List<String> getStarLabels() {
		return starLabels;
	}

	public void setStarLabels(List<String> starLabels) {
		this.starLabels = starLabels;
	}

	public boolean isShowEmoticon() {
		return showEmoticon;
	}

	public void setShowEmoticon(boolean showEmoticon) {
		this.showEmoticon = showEmoticon;
	}

	public boolean isOverrideSentimentCheck() {
		return overrideSentimentCheck;
	}

	public void setOverrideSentimentCheck(boolean overrideSentimentCheck) {
		this.overrideSentimentCheck = overrideSentimentCheck;
	}

	public String getUnsubscribeText() {
		return unsubscribeText;
	}

	public void setUnsubscribeText(String unsubscribeText) {
		this.unsubscribeText = unsubscribeText;
	}

	public Integer getUnsubscribeTextEnabled() {
		return unsubscribeTextEnabled;
	}

	public void setUnsubscribeTextEnabled(Integer unsubscribeTextEnabled) {
		this.unsubscribeTextEnabled = unsubscribeTextEnabled;
	}

	public Integer getIsContactUsCheckEnabled() {
		return isContactUsCheckEnabled;
	}

	public void setIsContactUsCheckEnabled(Integer isContactUsCheckEnabled) {
		this.isContactUsCheckEnabled = isContactUsCheckEnabled;
	}

	public String getContactUsMessage() {
		return contactUsMessage;
	}

	public void setContactUsMessage(String contactUsMessage) {
		this.contactUsMessage = contactUsMessage;
	}

	public String getContactUsButtonText() {
		return contactUsButtonText;
	}

	public void setContactUsButtonText(String contactUsButtonText) {
		this.contactUsButtonText = contactUsButtonText;
	}

	public String getContactButtonTextColor() {
		return contactButtonTextColor;
	}

	public void setContactButtonTextColor(String contactButtonTextColor) {
		this.contactButtonTextColor = contactButtonTextColor;
	}

	public String getContactButtonColor() {
		return contactButtonColor;
	}

	public void setContactButtonColor(String contactButtonColor) {
		this.contactButtonColor = contactButtonColor;
	}

	public String getRecommendPageHeading() {
		return recommendPageHeading;
	}

	public void setRecommendPageHeading(String recommendPageHeading) {
		this.recommendPageHeading = recommendPageHeading;
	}

	public String getRecommendPageMessage() {
		return recommendPageMessage;
	}

	public void setRecommendPageMessage(String recommendPageMessage) {
		this.recommendPageMessage = recommendPageMessage;
	}

	public String getCustomImageUrl() {
		return customImageUrl;
	}

	public void setCustomImageUrl(String customImageUrl) {
		this.customImageUrl = customImageUrl;
	}

	public String getNeutralLinkLabel() {
		return neutralLinkLabel;
	}

	public void setNeutralLinkLabel(String neutralLinkLabel) {
		this.neutralLinkLabel = neutralLinkLabel;
	}

	public Integer getExcludeNeutral() {
		return excludeNeutral;
	}

	public void setExcludeNeutral(Integer excludeNeutral) {
		this.excludeNeutral = excludeNeutral;
	}

	public String getWriteReviewQuestion() {
		return writeReviewQuestion;
	}

	public void setWriteReviewQuestion(String writeReviewQuestion) {
		this.writeReviewQuestion = writeReviewQuestion;
	}

	public String getStarHeading() {
		return starHeading;
	}

	public void setStarHeading(String starHeading) {
		this.starHeading = starHeading;
	}

	public String getSentimentHeading() {
		return sentimentHeading;
	}

	public void setSentimentHeading(String sentimentHeading) {
		this.sentimentHeading = sentimentHeading;
	}

	public String getStarMessage() {
		return starMessage;
	}

	public void setStarMessage(String starMessage) {
		this.starMessage = starMessage;
	}

	public String getSentimentMessage() {
		return sentimentMessage;
	}

	public void setSentimentMessage(String sentimentMessage) {
		this.sentimentMessage = sentimentMessage;
	}

	public Integer getEnableFeedbackMessage() {
		return enableFeedbackMessage;
	}

	public void setEnableFeedbackMessage(Integer enableFeedbackMessage) {
		this.enableFeedbackMessage = enableFeedbackMessage;
	}

	public String getFeedbackCallbackMessage() {
		return feedbackCallbackMessage;
	}

	public void setFeedbackCallbackMessage(String feedbackCallbackMessage) {
		this.feedbackCallbackMessage = feedbackCallbackMessage;
	}

	public Integer getEnableFeedbackCheckbox() {
		return enableFeedbackCheckbox;
	}

	public void setEnableFeedbackCheckbox(Integer enableFeedbackCheckbox) {
		this.enableFeedbackCheckbox = enableFeedbackCheckbox;
	}

	public String getThankyouMessage() {
		return thankyouMessage;
	}

	public void setThankyouMessage(String thankyouMessage) {
		this.thankyouMessage = thankyouMessage;
	}

	public String getThankyouHeading() {
		return thankyouHeading;
	}

	public void setThankyouHeading(String thankyouHeading) {
		this.thankyouHeading = thankyouHeading;
	}

	public String getReviewSiteButtonColor() {
		return reviewSiteButtonColor;
	}

	public void setReviewSiteButtonColor(String reviewSiteButtonColor) {
		this.reviewSiteButtonColor = reviewSiteButtonColor;
	}

	public String getReviewSiteButtonTextColor() {
		return reviewSiteButtonTextColor;
	}

	public void setReviewSiteButtonTextColor(String reviewSiteButtonTextColor) {
		this.reviewSiteButtonTextColor = reviewSiteButtonTextColor;
	}

	public Integer getReviewEnable() {
		return reviewEnable;
	}

	public void setReviewEnable(Integer reviewEnable) {
		this.reviewEnable = reviewEnable;
	}
	
	public String getSmsCategory() {
		return smsCategory;
	}
	public void setSmsCategory(String smsCategory) {
		this.smsCategory = smsCategory;
	}
	
	public SmsTemplateMessage() {
	}

	/**
	 * Creates builder to build {@link SmsTemplateMessage}.
	 * 
	 * @return created builder
	 */
	@Generated("SparkTools")
	public static Builder builder() {
		return new Builder();
	}

	/**
	 * Builder to build {@link SmsTemplateMessage}.
	 */
	@Generated("SparkTools")
	public static final class Builder {
		private Integer id;
		private String name;
		private String messageFrom;
		private String messageBody;
		private String question;
		private String positiveLinkLabel;
		private String negativeLinkLabel;
		private Integer isSentimentCheckEnabled;
		private String nonRecommendReviewPageMessage;
		private String templateType;
		private Integer mmsEnable;
		private String mediaUrl;
		private String mediaType;
		private Integer isDefault;
		private String nonRecommendedUrl;
		private String sentimentCheckType;
		private Integer starRatingMin;
		private Integer npsRatingMin;
		private List<String> npsLabels = Collections.emptyList();
		private List<String> starLabels = Collections.emptyList();
		private boolean showEmoticon;
		private boolean overrideSentimentCheck;
		private String unsubscribeText;
		private Integer unsubscribeTextEnabled;
		private Integer isContactUsCheckEnabled;
		private String contactUsMessage;
		private String contactUsButtonText;
		private String contactButtonTextColor;
		private String contactButtonColor;
		private String recommendPageHeading;
		private String recommendPageMessage;
		private String customImageUrl;
		private String neutralLinkLabel;
		private Integer excludeNeutral;
		private String writeReviewQuestion;
		private String starHeading;
		private String sentimentHeading;
		private String starMessage;
		private String sentimentMessage;
		private String referralMessage;
		private String feedbackTextLabel;
		private String submitButtonText;
		private String formUrl;
		private String feedbackNamePlaceholder;
		private String feedbackEmailPlaceholder;
		private String referralLinkLabel;
		private String referralFirstNamePlaceholder;
		private String referralLastNamePlaceholder;
		private String referralEmailPlaceholder;
		private String referralPhonePlaceholder;
		private String directFeedbackHeading;
		private String feedbackMessagePositiveSentiment;
		private List<String> starsSubText;
		private String directFeedbackNextButtonText;
		private String assistedByPlaceholder;
		private String smsCategory;

		private Builder() {
		}

		public Builder withFeedbackTextLabel(String feedbackTextLabel) {
			this.feedbackTextLabel = feedbackTextLabel;
			return this;
		}
		
		public Builder withSubmitButtonText(String submitButtonText) {
			this.submitButtonText = submitButtonText;
			return this;
		}
		
		public Builder withId(Integer id) {
			this.id = id;
			return this;
		}

		public Builder withName(String name) {
			this.name = name;
			return this;
		}

		public Builder withMessageFrom(String messageFrom) {
			this.messageFrom = messageFrom;
			return this;
		}

		public Builder withMessageBody(String messageBody) {
			this.messageBody = messageBody;
			return this;
		}

		public Builder withQuestion(String question) {
			this.question = question;
			return this;
		}

		public Builder withPositiveLinkLabel(String positiveLinkLabel) {
			this.positiveLinkLabel = positiveLinkLabel;
			return this;
		}

		public Builder withNegativeLinkLabel(String negativeLinkLabel) {
			this.negativeLinkLabel = negativeLinkLabel;
			return this;
		}

		public Builder withIsSentimentCheckEnabled(Integer isSentimentCheckEnabled) {
			this.isSentimentCheckEnabled = isSentimentCheckEnabled;
			return this;
		}

		public Builder withNonRecommendReviewPageMessage(String nonRecommendReviewPageMessage) {
			this.nonRecommendReviewPageMessage = nonRecommendReviewPageMessage;
			return this;
		}

		public Builder withTemplateType(String templateType) {
			this.templateType = templateType;
			return this;
		}

		public Builder withMmsEnable(Integer mmsEnable) {
			this.mmsEnable = mmsEnable;
			return this;
		}

		public Builder withMediaUrl(String mediaUrl) {
			this.mediaUrl = mediaUrl;
			return this;
		}

		public Builder withMediaType(String mediaType) {
			this.mediaType = mediaType;
			return this;
		}

		public Builder withIsDefault(Integer isDefault) {
			this.isDefault = isDefault;
			return this;
		}

		public Builder withNonRecommendedUrl(String nonRecommendedUrl) {
			this.nonRecommendedUrl = nonRecommendedUrl;
			return this;
		}

		public Builder withSentimentCheckType(String sentimentCheckType) {
			this.sentimentCheckType = sentimentCheckType;
			return this;
		}

		public Builder withStarRatingMin(Integer starRatingMin) {
			this.starRatingMin = starRatingMin;
			return this;
		}

		public Builder withNpsRatingMin(Integer npsRatingMin) {
			this.npsRatingMin = npsRatingMin;
			return this;
		}

		public Builder withNpsLabels(List<String> npsLabels) {
			this.npsLabels = npsLabels;
			return this;
		}

		public Builder withStarLabels(List<String> starLabels) {
			this.starLabels = starLabels;
			return this;
		}

		public Builder withShowEmoticon(boolean showEmoticon) {
			this.showEmoticon = showEmoticon;
			return this;
		}

		public Builder withOverrideSentimentCheck(boolean overrideSentimentCheck) {
			this.overrideSentimentCheck = overrideSentimentCheck;
			return this;
		}

		public Builder withUnsubscribeText(String unsubscribeText) {
			this.unsubscribeText = unsubscribeText;
			return this;
		}

		public Builder withUnsubscribeTextEnabled(Integer unsubscribeTextEnabled) {
			this.unsubscribeTextEnabled = unsubscribeTextEnabled;
			return this;
		}

		public Builder withIsContactUsCheckEnabled(Integer isContactUsCheckEnabled) {
			this.isContactUsCheckEnabled = isContactUsCheckEnabled;
			return this;
		}

		public Builder withContactUsMessage(String contactUsMessage) {
			this.contactUsMessage = contactUsMessage;
			return this;
		}

		public Builder withContactUsButtonText(String contactUsButtonText) {
			this.contactUsButtonText = contactUsButtonText;
			return this;
		}

		public Builder withContactButtonTextColor(String contactButtonTextColor) {
			this.contactButtonTextColor = contactButtonTextColor;
			return this;
		}

		public Builder withContactButtonColor(String contactButtonColor) {
			this.contactButtonColor = contactButtonColor;
			return this;
		}

		public Builder withRecommendPageHeading(String recommendPageHeading) {
			this.recommendPageHeading = recommendPageHeading;
			return this;
		}

		public Builder withRecommendPageMessage(String recommendPageMessage) {
			this.recommendPageMessage = recommendPageMessage;
			return this;
		}

		public Builder withCustomImageUrl(String customImageUrl) {
			this.customImageUrl = customImageUrl;
			return this;
		}

		public Builder withNeutralLinkLabel(String neutralLinkLabel) {
			this.neutralLinkLabel = neutralLinkLabel;
			return this;
		}

		public Builder withExcludeNeutral(Integer excludeNeutral) {
			this.excludeNeutral = excludeNeutral;
			return this;
		}

		public Builder withWriteReviewQuestion(String writeReviewQuestion) {
			this.writeReviewQuestion = writeReviewQuestion;
			return this;
		}

		public Builder withStarHeading(String starHeading) {
			this.starHeading = starHeading;
			return this;
		}

		public Builder withSentimentHeading(String sentimentHeading) {
			this.sentimentHeading = sentimentHeading;
			return this;
		}

		public Builder withStarMessage(String starMessage) {
			this.starMessage = starMessage;
			return this;
		}

		public Builder withSentimentMessage(String sentimentMessage) {
			this.sentimentMessage = sentimentMessage;
			return this;
		}

		public Builder withReferralMessage(String referralMessage) {
			this.referralMessage = referralMessage;
			return this;
		}
		
		public Builder withFormUrl(String formUrl) {
			this.formUrl = formUrl;
			return this;
		}
		
		public Builder withFeedbackNamePlaceholder(String feedbackNamePlaceholder) {
			this.feedbackNamePlaceholder = feedbackNamePlaceholder;
			return this;
		}
		
		public Builder withFeedbackEmailPlaceholder(String feedbackEmailPlaceholder) {
			this.feedbackEmailPlaceholder = feedbackEmailPlaceholder;
			return this;
		}
		
		public Builder withReferralLinkLabel(String referralLinkLabel) {
			this.referralLinkLabel = referralLinkLabel;
			return this;
		}
		
		public Builder withReferralFirstNamePlaceholder(String referralFirstNamePlaceholder) {
			this.referralFirstNamePlaceholder = referralFirstNamePlaceholder;
			return this;
		}
		
		public Builder withReferralLastNamePlaceholder(String referralLastNamePlaceholder) {
			this.referralLastNamePlaceholder = referralLastNamePlaceholder;
			return this;
		}
		
		public Builder withReferralEmailPlaceholder(String referralEmailPlaceholder) {
			this.referralEmailPlaceholder = referralEmailPlaceholder;
			return this;
		}
		
		public Builder withReferralPhonePlaceholder(String referralPhonePlaceholder) {
			this.referralPhonePlaceholder = referralPhonePlaceholder;
			return this;
		}
		
		public Builder withDirectFeedbackHeading(String directFeedbackHeading) {
			this.directFeedbackHeading = directFeedbackHeading;
			return this;
		}
		
		public Builder withFeedbackMessagePositiveSentiment(String feedbackMessagePositiveSentiment) {
			this.feedbackMessagePositiveSentiment = feedbackMessagePositiveSentiment;
			return this;
		}
		
		public Builder withStarsSubText(List<String> starsSubText) {
			this.starsSubText = starsSubText;
			return this;
		}
		
		public Builder withDirectFeedbackNextButtonText(String directFeedbackNextButtonText) {
			this.directFeedbackNextButtonText = directFeedbackNextButtonText;
			return this;
		}
		
		public Builder withAssistedByPlaceholder(String assistedByPlaceholder) {
			this.assistedByPlaceholder = assistedByPlaceholder;
			return this;
		}
		
		public SmsTemplateMessage build() {
			return new SmsTemplateMessage(this);
		}
	}

	public BusinessSmsTemplate getBusinessSmsTemplate(Integer userId) {

		BusinessSmsTemplate smsTemplate = new BusinessSmsTemplate();

		if (StringUtils.isNotBlank(this.getMessageFrom())) {
			smsTemplate.setMessageFrom(this.getMessageFrom());
		}
		if (StringUtils.isNotBlank(this.getMessageBody())) {
			smsTemplate.setMessageBody(this.getMessageBody());
		}
		if (StringUtils.isNotBlank(this.getName())) {
			smsTemplate.setName(this.getName());
		}
		if (StringUtils.isNotBlank(this.getQuestion())) {
			smsTemplate.setQuestion(this.getQuestion());
		}
		if (StringUtils.isNotBlank(this.getPositiveLinkLabel())) {
			smsTemplate.setPositiveLinkLabel(this.getPositiveLinkLabel());
		}
		if (StringUtils.isNotBlank(this.getNegativeLinkLabel())) {
			smsTemplate.setNegativeLinkLabel(this.getNegativeLinkLabel());
		}
		if (StringUtils.isNotBlank(this.getNeutralLinkLabel())) {
			smsTemplate.setNeutralLinkLabel(this.getNeutralLinkLabel());
		}
		smsTemplate.setExcludeNeutral(this.getExcludeNeutral());
		smsTemplate.setWriteReviewQuestion(this.getWriteReviewQuestion());
		smsTemplate.setCustomImageUrl(this.getCustomImageUrl());
		smsTemplate.setNonRecommendReviewPageMessage(this.getNonRecommendReviewPageMessage());
		smsTemplate.setNonRecommendedUrl(this.getNonRecommendedUrl());
		smsTemplate.setUnsubscribeTextEnabled(this.getUnsubscribeTextEnabled());
		smsTemplate.setStarHeading(this.getStarHeading());
		smsTemplate.setSentimentHeading(this.getSentimentHeading());
		smsTemplate.setStarMessage(this.getStarMessage());
		smsTemplate.setSentimentMessage(this.getSentimentMessage());
		if (StringUtils.isNotBlank(this.getUnsubscribeText())) {
			smsTemplate.setUnsubscribeText(this.getUnsubscribeText());
		}
		if (EmailTemplateTypes.CUSTOMER_EXPERIENCE.name().equalsIgnoreCase(this.getTemplateType())) {
			BusinessTemplateUtils.setSentimentCheckOptions(this, smsTemplate);
			if (this.getIsSentimentCheckEnabled() != null) {
				smsTemplate.setEnableSentimentCheck(this.getIsSentimentCheckEnabled());
			}

		} else {
			if (this.getIsSentimentCheckEnabled() != null) {
				smsTemplate.setEnableSentimentCheck(this.getIsSentimentCheckEnabled());
				if (this.getIsSentimentCheckEnabled() == 1) {
					BusinessTemplateUtils.setSentimentCheckOptions(this, smsTemplate);
				}
			}
		}

		if (this.getMmsEnable() != null) {
			smsTemplate.setMmsEnabled(this.getMmsEnable());
		}
		smsTemplate.setUpdatedAt(new Date());
		smsTemplate.setUpdatedBy(userId);
		if (StringUtils.isNotBlank(this.getMediaUrl())) {
			smsTemplate.setMediaUrl(this.getMediaUrl());
		} else {
			smsTemplate.setMediaUrl(null);
		}
		if (StringUtils.isNotBlank(this.getMediaType())) {
			smsTemplate.setMediaType(this.getMediaType());
		} else {
			smsTemplate.setMediaType(null);
		}
		if ("review_request".equalsIgnoreCase(this.getTemplateType())
				|| "survey_request".equalsIgnoreCase(this.getTemplateType())
				|| EmailTemplateTypes.CUSTOMER_EXPERIENCE.name().equalsIgnoreCase(this.getTemplateType())) {
			if (this.getIsContactUsCheckEnabled() != null) {
				smsTemplate.setEnableContactUs(this.getIsContactUsCheckEnabled());
			}

			if (StringUtils.isNotBlank(this.getContactUsMessage())) {
				smsTemplate.setContactUsMessage(this.getContactUsMessage());
			}
			if (StringUtils.isNotBlank(this.getContactUsButtonText())) {
				smsTemplate.setContactUsButtonText(this.getContactUsButtonText());
			}
			if (StringUtils.isNotBlank(this.getContactButtonTextColor())) {
				smsTemplate.setContactUsButtonTextColor(this.getContactButtonTextColor());
			}
			if (StringUtils.isNotBlank(this.getContactButtonColor())) {
				smsTemplate.setContactUsButtonColor(this.getContactButtonColor());
			}
			if (StringUtils.isNotBlank(this.getRecommendPageHeading())) {
				smsTemplate.setRecommendPageHeading(this.getRecommendPageHeading());
			}
			if (StringUtils.isNotBlank(this.getRecommendPageMessage())) {
				smsTemplate.setRecommendPageMessage(this.getRecommendPageMessage());
			}
		}

		//set new fields
		smsTemplate.setEnableFeedbackMessage(this.enableFeedbackMessage);
		smsTemplate.setFeedbackCallbackMessage(this.feedbackCallbackMessage);
		smsTemplate.setEnableFeedbackCheckbox(this.enableFeedbackCheckbox);
		smsTemplate.setThankyouMessage(this.thankyouMessage);
		smsTemplate.setThankyouHeading(this.thankyouHeading);
		smsTemplate.setReviewSiteButtonColor(this.reviewSiteButtonColor);
		smsTemplate.setReviewSiteButtonTextColor(this.reviewSiteButtonTextColor);
		smsTemplate.setReviewEnable(this.reviewEnable);
		smsTemplate.setLocationBrandingEnabled(this.locationBrandingEnabled);
		smsTemplate.setReferralMessage(this.referralMessage);
		
		//New code to handle referral TODO: figure out how it used to work earlier, UI change or what?
		if (this.getTemplateType().equalsIgnoreCase(TemplateTypeEnum.REFERRAL.getName())) {
			if (StringUtils.isNotBlank(this.getRecommendPageHeading())) {
				smsTemplate.setRecommendPageHeading(this.getRecommendPageHeading());
			}
			if (StringUtils.isNotBlank(this.getRecommendPageMessage())) {
				smsTemplate.setRecommendPageMessage(this.getRecommendPageMessage());
			}
			if (CollectionUtils.isNotEmpty(this.mediaUrls)) {
				smsTemplate.setMediaUrls(String.join(",", this.mediaUrls));
				smsTemplate.setMmsEnabled(1);
			} else {
				smsTemplate.setMmsEnabled(0);
				smsTemplate.setMediaUrls(null);
			}
			smsTemplate.setReferralContactOptionEnabled(this.referralContactOptionEnabled);
			smsTemplate.setReferralQuestion(this.referralQuestion);
			smsTemplate.setReferralOptions(CollectionUtils.isNotEmpty(this.referralOptions) ? 
					String.join(Constants.REFERRAL_OPTIONS_DELIMITER, this.referralOptions) : null); 
		}
		
		//set mediaurl List for custom template
		if (this.getTemplateType().equalsIgnoreCase(TemplateTypeEnum.PROMOTION.getName())) {
			if (CollectionUtils.isNotEmpty(this.mediaUrls)) {
				smsTemplate.setMediaUrls(String.join(",", this.mediaUrls));
				smsTemplate.setMmsEnabled(1);
			} else {
				smsTemplate.setMmsEnabled(0);
				smsTemplate.setMediaUrls(null);
			}
			if (StringUtils.isNotBlank(this.getRecommendPageMessage())) {
				smsTemplate.setRecommendPageMessage(this.getRecommendPageMessage());
			}
		}
		
		if (this.getTemplateType().equalsIgnoreCase(TemplateTypeEnum.APPOINTMENT_FORM_SMS.getName())) {
			if (StringUtils.isNotEmpty(this.formUrl)) {
				smsTemplate.setFormUrl(StringUtils.trim(this.formUrl));
			}
			if (CollectionUtils.isNotEmpty(this.mediaUrls)) {
				smsTemplate.setMediaUrls(String.join(",", this.mediaUrls));
				smsTemplate.setMmsEnabled(1);
			} else {
				smsTemplate.setMmsEnabled(0);
				smsTemplate.setMediaUrls(null);
			}
		}
		return smsTemplate;
	}

	public SmsTemplateMessage(BusinessSmsTemplate businessSmsTemplate,List<String> npsLabels,List<String> starLabels) {
		this.id = businessSmsTemplate.getId();
		this.messageFrom = businessSmsTemplate.getMessageFrom();
		this.name = businessSmsTemplate.getName();
		this.messageBody = businessSmsTemplate.getMessageBody();
		this.question = businessSmsTemplate.getQuestion();
		this.positiveLinkLabel = businessSmsTemplate.getPositiveLinkLabel();
		this.negativeLinkLabel = businessSmsTemplate.getNegativeLinkLabel();
		this.nonRecommendReviewPageMessage = businessSmsTemplate.getNonRecommendReviewPageMessage();
		this.templateType = businessSmsTemplate.getType();
		this.mediaUrl = businessSmsTemplate.getMediaUrl();
		this.mediaType = businessSmsTemplate.getMediaType();
		this.nonRecommendedUrl = businessSmsTemplate.getNonRecommendedUrl();
		this.mmsEnable = businessSmsTemplate.getMmsEnabled();
		this.isSentimentCheckEnabled = businessSmsTemplate.getEnableSentimentCheck();
		this.npsLabels.addAll(npsLabels);
		this.starLabels.addAll(starLabels);
		this.sentimentCheckType = businessSmsTemplate.getSentimentCheckType();
		this.showEmoticon = (businessSmsTemplate.getShowEmoticon() != null ? businessSmsTemplate.getShowEmoticon() == 1
				: false);
		this.npsRatingMin = businessSmsTemplate.getNpsRatingMin();
		this.starRatingMin = businessSmsTemplate.getStarRatingMin();
		this.unsubscribeTextEnabled = businessSmsTemplate.getUnsubscribeTextEnabled();
		this.isContactUsCheckEnabled = businessSmsTemplate.getEnableContactUs();
		this.contactUsMessage = businessSmsTemplate.getContactUsMessage();
		this.contactUsButtonText = businessSmsTemplate.getContactUsButtonText();
		this.contactButtonColor = businessSmsTemplate.getContactUsButtonColor();
		this.contactButtonTextColor = businessSmsTemplate.getContactUsButtonTextColor();
		this.recommendPageHeading = businessSmsTemplate.getRecommendPageHeading();
		this.recommendPageMessage = businessSmsTemplate.getRecommendPageMessage();
		this.unsubscribeText = StringUtils.isNotBlank(businessSmsTemplate.getUnsubscribeText())
				? businessSmsTemplate.getUnsubscribeText()
				: "Txt STOP to unsub.";
		this.customImageUrl = businessSmsTemplate.getCustomImageUrl();
		this.excludeNeutral = businessSmsTemplate.getExcludeNeutral();
		this.neutralLinkLabel = businessSmsTemplate.getNeutralLinkLabel();
		// using write review question column as sentiment message for CX template
		// instead of creating a new template
		this.writeReviewQuestion = businessSmsTemplate.getWriteReviewQuestion();
		this.sentimentHeading = businessSmsTemplate.getSentimentHeading();
		this.starHeading = businessSmsTemplate.getStarHeading();
		this.sentimentMessage = businessSmsTemplate.getSentimentMessage();
		this.starMessage = businessSmsTemplate.getStarMessage();
		// new field mapping
		this.enableFeedbackMessage = businessSmsTemplate.getEnableFeedbackMessage();
		this.feedbackCallbackMessage = businessSmsTemplate.getFeedbackCallbackMessage();
		this.enableFeedbackCheckbox = businessSmsTemplate.getEnableFeedbackCheckbox();
		this.thankyouMessage = businessSmsTemplate.getThankyouMessage();
		this.thankyouHeading = businessSmsTemplate.getThankyouHeading();
		this.reviewSiteButtonColor = businessSmsTemplate.getReviewSiteButtonColor();
		this.reviewSiteButtonTextColor = businessSmsTemplate.getReviewSiteButtonTextColor();
		this.reviewEnable = businessSmsTemplate.getReviewEnable();
		this.locationBrandingEnabled = businessSmsTemplate.getLocationBrandingEnabled();
		this.referralMessage = businessSmsTemplate.getReferralMessage();
		this.mediaUrls = CoreUtils.parseDelimitedStringsWithDefaultAsEmptyList(businessSmsTemplate.getMediaUrls(), ",");
		this.referralContactOptionEnabled = businessSmsTemplate.getReferralContactOptionEnabled();
		this.referralQuestion = businessSmsTemplate.getReferralQuestion();
		this.referralOptions = CoreUtils.parseDelimitedStringsWithDefaultAsEmptyList(businessSmsTemplate.getReferralOptions(), Constants.REFERRAL_OPTIONS_DELIMITER);
		this.confirmButtonEnabled = businessSmsTemplate.getConfirmButtonEnabled() != null ? businessSmsTemplate.getConfirmButtonEnabled() : 0;
		this.rescheduleButtonEnabled = businessSmsTemplate.getRescheduleButtonEnabled() != null ? businessSmsTemplate.getRescheduleButtonEnabled() : 0;
		this.cancelButtonEnabled = businessSmsTemplate.getCancelButtonEnabled() != null ? businessSmsTemplate.getCancelButtonEnabled() : 0;
		this.bookAppointmentButtonEnabled = businessSmsTemplate.getBookAppointmentButtonEnabled() != null ? businessSmsTemplate.getBookAppointmentButtonEnabled() : 0;
		this.feedbackTextLabel = businessSmsTemplate.getFeedbackTextLabel();
		this.submitButtonText = businessSmsTemplate.getSubmitButtonText();
		this.formUrl = businessSmsTemplate.getFormUrl();
		this.smsCategory = TemplateUtils.getUnsubscriptionCategoryWithDefault(businessSmsTemplate.getSmsCategory(),businessSmsTemplate.getType());
		
		prepareLabelConfigurationsForRRCXReferral(this, businessSmsTemplate.getLabelConfigs(), businessSmsTemplate.getType());
	}

	// BIRD-72154 - Set placeholder related configurations of review, cx & referral templates.
	private void prepareLabelConfigurationsForRRCXReferral(SmsTemplateMessage smsTemplateMessage, String labelConfigs, String templateType) {
		TemplateLabelConfigurations configurations = TemplateUtils.prepareTemplateLableConfigurationsFromSerializedString(labelConfigs);
		
		if (StringUtils.equalsIgnoreCase(templateType, Constants.REVIEW_REQUEST_TEMPLATE_TYPE) || StringUtils.equalsIgnoreCase(templateType, Constants.CX_TEMPLATE_TYPE)) {
			smsTemplateMessage.setFeedbackNamePlaceholder(
					StringUtils.isBlank(configurations.getFeedbackNameLabel()) ? Constants.DEFAULT_FEEDBACK_NAME_LABEL : configurations.getFeedbackNameLabel());
			smsTemplateMessage.setFeedbackEmailPlaceholder(
					StringUtils.isBlank(configurations.getFeedbackEmailLabel()) ? Constants.DEFAULT_FEEBACK_EMAIL_LABEL : configurations.getFeedbackEmailLabel());
			smsTemplateMessage.setDirectFeedbackHeading(StringUtils.isBlank(configurations.getDirectFeedbackHeading()) ? Constants.DEFAULT_DIRECT_FEEDBACK_HEADING
					: configurations.getDirectFeedbackHeading());
			smsTemplateMessage.setFeedbackMessagePositiveSentiment(
					StringUtils.isBlank(configurations.getDirectFeedbackPositiveSentimentMessage()) ? Constants.DEFAULT_DIRECT_FEEDBACK_POSITIVE_SENTIMENT_MESSAGE
							: configurations.getDirectFeedbackPositiveSentimentMessage());
			smsTemplateMessage.setDirectFeedbackNextButtonText(
					StringUtils.isBlank(configurations.getNextButtonText()) ? Constants.DEFAULT_DIRECT_FEEDBACK_NEXT_BUTTON_TEXT : configurations.getNextButtonText());
			smsTemplateMessage.setAssistedByPlaceholder(
					StringUtils.isBlank(configurations.getAssistedByLabel()) ? Constants.DEFAULT_ASSISTED_BY_LABEL : configurations.getAssistedByLabel());
			
			prepareReviewStarRatingsSubtext(smsTemplateMessage, configurations);
		}
		
		if (StringUtils.equalsIgnoreCase(templateType, Constants.REFERRAL_TEMPLATE_TYPE)) {
			smsTemplateMessage.setReferralLinkLabel(
					StringUtils.isBlank(configurations.getReferralLinkLabel()) ? Constants.DEFAULT_REFERRAL_LINK_LABEL : configurations.getReferralLinkLabel());
			smsTemplateMessage.setReferralFirstNamePlaceholder(
					StringUtils.isBlank(configurations.getReferralFirstNameLabel()) ? Constants.DEFAULT_REFERRAL_FIRST_NAME_LABEL : configurations.getReferralFirstNameLabel());
			smsTemplateMessage.setReferralLastNamePlaceholder(
					StringUtils.isBlank(configurations.getReferralLastNameLabel()) ? Constants.DEFAULT_REFERRAL_LAST_NAME_LABEL : configurations.getReferralLastNameLabel());
			smsTemplateMessage.setReferralEmailPlaceholder(
					StringUtils.isBlank(configurations.getReferralEmailLabel()) ? Constants.DEFAULT_REFERRAL_EMAIL_LABEL : configurations.getReferralEmailLabel());
			smsTemplateMessage.setReferralPhonePlaceholder(
					StringUtils.isBlank(configurations.getReferralPhoneLabel()) ? Constants.DEFAULT_REFERRAL_PHONE_LABEL : configurations.getReferralPhoneLabel());
		}
	}

	private void prepareReviewStarRatingsSubtext(SmsTemplateMessage smsTemplateMessage, TemplateLabelConfigurations labelConfigurations) {
		List<String> starsRatingSubtexts = new ArrayList<>();
		starsRatingSubtexts.add(StringUtils.isBlank(labelConfigurations.getOneStarSubtext()) ? Constants.DEFAULT_ONE_STAR_SUBTEXT : labelConfigurations.getOneStarSubtext());
		starsRatingSubtexts.add(StringUtils.isBlank(labelConfigurations.getTwoStarSubtext()) ? Constants.DEFAULT_TWO_STAR_SUBTEXT : labelConfigurations.getTwoStarSubtext());
		starsRatingSubtexts.add(StringUtils.isBlank(labelConfigurations.getThreeStarSubtext()) ? Constants.DEFAULT_THREE_STAR_SUBTEXT : labelConfigurations.getThreeStarSubtext());
		starsRatingSubtexts.add(StringUtils.isBlank(labelConfigurations.getFourStarSubtext()) ? Constants.DEFAULT_FOUR_STAR_SUBTEXT : labelConfigurations.getFourStarSubtext());
		starsRatingSubtexts.add(StringUtils.isBlank(labelConfigurations.getFiveStarSubtext()) ? Constants.DEFAULT_FIVE_STAR_SUBTEXT : labelConfigurations.getFiveStarSubtext());
		
		smsTemplateMessage.setStarsSubText(starsRatingSubtexts);
	}

	/**
	 * @return the locationBrandingEnabled
	 */
	public Integer getLocationBrandingEnabled() {
		return locationBrandingEnabled;
	}

	/**
	 * @param locationBrandingEnabled the locationBrandingEnabled to set
	 */
	public void setLocationBrandingEnabled(Integer locationBrandingEnabled) {
		this.locationBrandingEnabled = locationBrandingEnabled;
	}

	/**
	 * @return the referralMessage
	 */
	public String getReferralMessage() {
		return referralMessage;
	}

	/**
	 * @param referralMessage the referralMessage to set
	 */
	public void setReferralMessage(String referralMessage) {
		this.referralMessage = referralMessage;
	}

	/**
	 * @return the mediaUrls
	 */
	public List<String> getMediaUrls() {
		return mediaUrls;
	}

	/**
	 * @param mediaUrls the mediaUrls to set
	 */
	public void setMediaUrls(List<String> mediaUrls) {
		this.mediaUrls = mediaUrls;
	}

	public String getReferralQuestion() {
		return referralQuestion;
	}

	public void setReferralQuestion(String referralQuestion) {
		this.referralQuestion = referralQuestion;
	}

	public List<String> getReferralOptions() {
		return referralOptions;
	}

	public void setReferralOptions(List<String> referralOptions) {
		this.referralOptions = referralOptions;
	}

	public Integer getReferralContactOptionEnabled() {
		return referralContactOptionEnabled;
	}

	public void setReferralContactOptionEnabled(Integer referralContactOptionEnabled) {
		this.referralContactOptionEnabled = referralContactOptionEnabled;
	}

	public String getReferralMessageEmailSubject() {
		return referralMessageEmailSubject;
	}

	public void setReferralMessageEmailSubject(String referralMessageEmailSubject) {
		this.referralMessageEmailSubject = referralMessageEmailSubject;
	}

	public String getReferralFormHeading() {
		return referralFormHeading;
	}

	public void setReferralFormHeading(String referralFormHeading) {
		this.referralFormHeading = referralFormHeading;
	}

	public String getReferralFormMessage() {
		return referralFormMessage;
	}

	public void setReferralFormMessage(String referralFormMessage) {
		this.referralFormMessage = referralFormMessage;
	}

	public String getReferralFormCtaLabel() {
		return referralFormCtaLabel;
	}

	public void setReferralFormCtaLabel(String referralFormCtaLabel) {
		this.referralFormCtaLabel = referralFormCtaLabel;
	}

	public String getThankYouCtaLabel() {
		return thankYouCtaLabel;
	}

	public void setThankYouCtaLabel(String thankYouCtaLabel) {
		this.thankYouCtaLabel = thankYouCtaLabel;
	}

	public String getThankYouCtaUrl() {
		return thankYouCtaUrl;
	}

	public void setThankYouCtaUrl(String thankYouCtaUrl) {
		this.thankYouCtaUrl = thankYouCtaUrl;
	}

	public String getReferralMessageCtaUrl() {
		return referralMessageCtaUrl;
	}

	public void setReferralMessageCtaUrl(String referralMessageCtaUrl) {
		this.referralMessageCtaUrl = referralMessageCtaUrl;
	}

	public Integer getReferralMessageCtaUrlEnabled() {
		return referralMessageCtaUrlEnabled;
	}

	public void setReferralMessageCtaUrlEnabled(Integer referralMessageCtaUrlEnabled) {
		this.referralMessageCtaUrlEnabled = referralMessageCtaUrlEnabled;
	}

	public String getTyPageAlternateUrl() {
		return tyPageAlternateUrl;
	}

	public void setTyPageAlternateUrl(String tyPageAlternateUrl) {
		this.tyPageAlternateUrl = tyPageAlternateUrl;
	}

	public Integer getTyPageEnabled() {
		return tyPageEnabled;
	}

	public void setTyPageEnabled(Integer tyPageEnabled) {
		this.tyPageEnabled = tyPageEnabled;
	}

	public Integer getShowZipCode() {
		return showZipCode;
	}

	public void setShowZipCode(Integer showZipCode) {
		this.showZipCode = showZipCode;
	}

	public Integer getConfirmButtonEnabled() {
		return confirmButtonEnabled;
	}

	public void setConfirmButtonEnabled(Integer confirmButtonEnabled) {
		this.confirmButtonEnabled = confirmButtonEnabled;
	}

	public Integer getRescheduleButtonEnabled() {
		return rescheduleButtonEnabled;
	}

	public void setRescheduleButtonEnabled(Integer rescheduleButtonEnabled) {
		this.rescheduleButtonEnabled = rescheduleButtonEnabled;
	}

	public Integer getCancelButtonEnabled() {
		return cancelButtonEnabled;
	}

	public void setCancelButtonEnabled(Integer cancelButtonEnabled) {
		this.cancelButtonEnabled = cancelButtonEnabled;
	}

	public Integer getBookAppointmentButtonEnabled() {
		return bookAppointmentButtonEnabled;
	}

	public void setBookAppointmentButtonEnabled(Integer bookAppointmentButtonEnabled) {
		this.bookAppointmentButtonEnabled = bookAppointmentButtonEnabled;
	}
	
	/**
	 * @return the formUrl
	 */
	public String getFormUrl() {
		return formUrl;
	}
	
	/**
	 * @param formUrl
	 *            the formUrl to set
	 */
	public void setFormUrl(String formUrl) {
		this.formUrl = formUrl;
	}

	public Integer getLocationLevelTemplate() {
		return locationLevelTemplate;
	}

	public void setLocationLevelTemplate(Integer locationLevelTemplate) {
		this.locationLevelTemplate = locationLevelTemplate;
	}

	public List<Integer> getSelectedLocations() {
		return selectedLocations;
	}

	public void setSelectedLocations(List<Integer> selectedLocations) {
		this.selectedLocations = selectedLocations;
	}

	public Boolean getIsGlobalTemplate() {
		return isGlobalTemplate;
	}

	public void setIsGlobalTemplate(Boolean isGlobalTemplate) {
		this.isGlobalTemplate = Boolean.TRUE.equals(isGlobalTemplate);
	}

	/**
	 * @return the feedbackNamePlaceholder
	 */
	public String getFeedbackNamePlaceholder() {
		return feedbackNamePlaceholder;
	}

	/**
	 * @param feedbackNamePlaceholder the feedbackNamePlaceholder to set
	 */
	public void setFeedbackNamePlaceholder(String feedbackNamePlaceholder) {
		this.feedbackNamePlaceholder = feedbackNamePlaceholder;
	}

	/**
	 * @return the feedbackEmailPlaceholder
	 */
	public String getFeedbackEmailPlaceholder() {
		return feedbackEmailPlaceholder;
	}

	/**
	 * @param feedbackEmailPlaceholder the feedbackEmailPlaceholder to set
	 */
	public void setFeedbackEmailPlaceholder(String feedbackEmailPlaceholder) {
		this.feedbackEmailPlaceholder = feedbackEmailPlaceholder;
	}

	/**
	 * @return the directFeedbackHeading
	 */
	public String getDirectFeedbackHeading() {
		return directFeedbackHeading;
	}

	/**
	 * @param directFeedbackHeading the directFeedbackHeading to set
	 */
	public void setDirectFeedbackHeading(String directFeedbackHeading) {
		this.directFeedbackHeading = directFeedbackHeading;
	}

	/**
	 * @return the feedbackMessagePositiveSentiment
	 */
	public String getFeedbackMessagePositiveSentiment() {
		return feedbackMessagePositiveSentiment;
	}

	/**
	 * @param feedbackMessagePositiveSentiment the feedbackMessagePositiveSentiment to set
	 */
	public void setFeedbackMessagePositiveSentiment(String feedbackMessagePositiveSentiment) {
		this.feedbackMessagePositiveSentiment = feedbackMessagePositiveSentiment;
	}

	/**
	 * @return the starsSubText
	 */
	public List<String> getStarsSubText() {
		return starsSubText;
	}

	/**
	 * @param starsSubText the starsSubText to set
	 */
	public void setStarsSubText(List<String> starsSubText) {
		this.starsSubText = starsSubText;
	}

	/**
	 * @return the directFeedbackNextButtonText
	 */
	public String getDirectFeedbackNextButtonText() {
		return directFeedbackNextButtonText;
	}

	/**
	 * @param directFeedbackNextButtonText the directFeedbackNextButtonText to set
	 */
	public void setDirectFeedbackNextButtonText(String directFeedbackNextButtonText) {
		this.directFeedbackNextButtonText = directFeedbackNextButtonText;
	}

	/**
	 * @return the assistedByPlaceholder
	 */
	public String getAssistedByPlaceholder() {
		return assistedByPlaceholder;
	}

	/**
	 * @param assistedByPlaceholder the assistedByPlaceholder to set
	 */
	public void setAssistedByPlaceholder(String assistedByPlaceholder) {
		this.assistedByPlaceholder = assistedByPlaceholder;
	}

	/**
	 * @return the referralLinkLabel
	 */
	public String getReferralLinkLabel() {
		return referralLinkLabel;
	}

	/**
	 * @param referralLinkLabel the referralLinkLabel to set
	 */
	public void setReferralLinkLabel(String referralLinkLabel) {
		this.referralLinkLabel = referralLinkLabel;
	}

	/**
	 * @return the referralFirstNamePlaceholder
	 */
	public String getReferralFirstNamePlaceholder() {
		return referralFirstNamePlaceholder;
	}

	/**
	 * @param referralFirstNamePlaceholder the referralFirstNamePlaceholder to set
	 */
	public void setReferralFirstNamePlaceholder(String referralFirstNamePlaceholder) {
		this.referralFirstNamePlaceholder = referralFirstNamePlaceholder;
	}

	/**
	 * @return the referralLastNamePlaceholder
	 */
	public String getReferralLastNamePlaceholder() {
		return referralLastNamePlaceholder;
	}

	/**
	 * @param referralLastNamePlaceholder the referralLastNamePlaceholder to set
	 */
	public void setReferralLastNamePlaceholder(String referralLastNamePlaceholder) {
		this.referralLastNamePlaceholder = referralLastNamePlaceholder;
	}

	/**
	 * @return the referralEmailPlaceholder
	 */
	public String getReferralEmailPlaceholder() {
		return referralEmailPlaceholder;
	}

	/**
	 * @param referralEmailPlaceholder the referralEmailPlaceholder to set
	 */
	public void setReferralEmailPlaceholder(String referralEmailPlaceholder) {
		this.referralEmailPlaceholder = referralEmailPlaceholder;
	}

	/**
	 * @return the referralPhonePlaceholder
	 */
	public String getReferralPhonePlaceholder() {
		return referralPhonePlaceholder;
	}

	/**
	 * @param referralPhonePlaceholder the referralPhonePlaceholder to set
	 */
	public void setReferralPhonePlaceholder(String referralPhonePlaceholder) {
		this.referralPhonePlaceholder = referralPhonePlaceholder;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
