package com.birdeye.template.dto;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown=true)
public class ReviewSourceURL implements Serializable{
	
	private Integer			totalLocations;
	private Integer			emptyUrlLocations;
	private List<SourceURL>	data;
	
	public Integer getTotalLocations() {
		return totalLocations;
	}
	
	public void setTotalLocations(Integer totalLocations) {
		this.totalLocations = totalLocations;
	}
	
	public Integer getEmptyUrlLocations() {
		return emptyUrlLocations;
	}
	
	public void setEmptyUrlLocations(Integer emptyUrlLocations) {
		this.emptyUrlLocations = emptyUrlLocations;
	}
	
	private static final long serialVersionUID = -3290121068276376758L;
	
	public List<SourceURL> getData() {
		return data;
	}
	
	public void setData(List<SourceURL> data) {
		this.data = data;
	}

	public ReviewSourceURL() {
		
	}
	
	/**
	 * @param totalLocations
	 * @param emptyUrlLocations
	 * @param data
	 */
	public ReviewSourceURL(Integer totalLocations, Integer emptyUrlLocations, List<SourceURL> data) {
		super();
		this.totalLocations = totalLocations;
		this.emptyUrlLocations = emptyUrlLocations;
		this.data = data;
	}
	
	
}
