package com.birdeye.template.exception;

import org.springframework.http.HttpStatus;

public class TemplateConfigException extends RuntimeException {

	private static final long serialVersionUID = 6601783671864114022L;
	
	private final HttpStatus code;
	
	public TemplateConfigException(HttpStatus code, String message) {
		super(message);
		this.code = code;
	}
	
	@Override
	public String getMessage() {
		String errorMessage = null;
		if (super.getMessage() != null) {
			errorMessage = super.getMessage();
		} else {
			if (code != null) {
				errorMessage = code.toString();
			}
		}
		return errorMessage;
	}

	public int getCode() {
		if (code != null)
			return code.value();

		return 500;
	}

}
