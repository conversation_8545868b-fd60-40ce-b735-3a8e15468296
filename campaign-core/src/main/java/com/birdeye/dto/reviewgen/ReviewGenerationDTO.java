package com.birdeye.dto.reviewgen;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class ReviewGenerationDTO implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -2411646599556869637L;
	
	private Integer				id;
	private Integer				sourceId;
	private Integer				reviewGenerationSourceId;
	
	public ReviewGenerationDTO() {
		super();
	}
	
	public ReviewGenerationDTO(Integer id, Integer sourceId, Integer reviewGenerationSourceId) {
		this.id = id;
		this.sourceId = sourceId;
		this.reviewGenerationSourceId = reviewGenerationSourceId;
	}
	
	public Integer getId() {
		return id;
	}
	
	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getSourceId() {
		return sourceId;
	}
	
	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}
	
	public Integer getReviewGenerationSourceId() {
		return reviewGenerationSourceId;
	}
	
	public void setReviewGenerationSourceId(Integer reviewGenerationSourceId) {
		this.reviewGenerationSourceId = reviewGenerationSourceId;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
	
}
