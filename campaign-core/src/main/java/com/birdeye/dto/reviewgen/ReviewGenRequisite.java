package com.birdeye.dto.reviewgen;

import java.io.Serializable;
import java.util.Comparator;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * 
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReviewGenRequisite implements Serializable {
	
	private static final long	serialVersionUID	= 1995425403384801043L;
	
	private Integer				id;
	private String				reviewGenURL;
	private String				businessName;
	private Integer				businessId;
	private Integer				businessAggregationId;
	

	public ReviewGenRequisite() {
		super();
	}
	
	public ReviewGenRequisite(Integer id, String reviewGenURL, String businessName, Integer businessId) {
		super();
		this.id = id;
		this.reviewGenURL = reviewGenURL;
		this.businessName = businessName;
		this.businessId = businessId;
	}
	public ReviewGenRequisite(Integer id, String reviewGenURL, Integer businessId, Integer businessAggregationId) {
		super();
		this.id = id;
		this.reviewGenURL = reviewGenURL;
		this.businessId = businessId;
	}
	//(rg.id ,rg.reviewGenerationUrl, rg.businessAggregationId)
	
	public Integer getId() {
		return id;
	}
	
	public void setId(Integer id) {
		this.id = id;
	}
	
	public String getReviewGenURL() {
		return reviewGenURL;
	}
	
	public void setReviewGenURL(String reviewGenURL) {
		this.reviewGenURL = reviewGenURL;
	}
	
	public String getBusinessName() {
		return businessName;
	}
	
	public void setBusinessName(String businessName) {
		this.businessName = businessName;
	}
	
	public Integer getBusinessId() {
		return businessId;
	}
	
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}

	public Integer getBusinessAggregationId() {
		return businessAggregationId;
	}

	public void setBusinessAggregationId(Integer businessAggregationId) {
		this.businessAggregationId = businessAggregationId;
	}
	
public static class ReviewGenRequisiteBusinessNameComparator implements Comparator<ReviewGenRequisite> {
		
		@Override
		public int compare(ReviewGenRequisite o1, ReviewGenRequisite o2) {
			if (o1 == null && o2 == null) {
				return 0;
			}
			
			if (o1 == null) {
				return -1;
			}
			
			if (o2 == null) {
				return 1;
			}
			if(o1.getBusinessName()==null) {
				return -1;
			}
			if(o2.getBusinessName()==null) {
				return 1;
			}
			Integer result = StringUtils.compareIgnoreCase(o1.getBusinessName(), o2.getBusinessName(), true);
			return result;
		}
	}
	
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}	
}
