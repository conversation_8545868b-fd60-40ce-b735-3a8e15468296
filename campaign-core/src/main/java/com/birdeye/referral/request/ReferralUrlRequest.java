package com.birdeye.referral.request;

import java.util.Map;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ReferralUrlRequest {
	
	private String				emailId;
	
	private String				phone;
	
	private String				name;
	
	private String				externalLocationId;	// TitleMax's store id.
	
	private Long					accountId;			// Birdeye's account id
	
	private Integer				campaignId;			// birdeye's campaign id to be used to generate deeplink data.
	
	private Map<String, String>	customFields;
	
	public String getEmailId() {
		return emailId;
	}
	
	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}
	
	public String getPhone() {
		return phone;
	}
	
	public void setPhone(String phone) {
		this.phone = phone;
	}
	
	public String getName() {
		return name;
	}
	
	public void setName(String name) {
		this.name = name;
	}
	
	public String getExternalLocationId() {
		return externalLocationId;
	}
	
	public void setExternalLocationId(String externalLocationId) {
		this.externalLocationId = externalLocationId;
	}
	
	public Long getAccountId() {
		return accountId;
	}
	
	public void setAccountId(Long accountId) {
		this.accountId = accountId;
	}
	
	public Map<String, String> getCustomFields() {
		return customFields;
	}
	
	public void setCustomFields(Map<String, String> customFields) {
		this.customFields = customFields;
	}

	public Integer getCampaignId() {
		return campaignId;
	}

	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return b.toString();
	}
	
}
