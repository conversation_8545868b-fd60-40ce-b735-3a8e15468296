package com.birdeye.referral.request;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class InvalidAppointmentUpdateEvent implements Serializable {
	
	private static final long	serialVersionUID	= 1405695568201938342L;
	
	private Integer				cid;
	
	private String				referralCode;
	
	private Integer				referrerId;
	
	private String				referrerEmail;
	
	private String				referrerPhoneNumber;
	
	private Integer				referrerEcid;
	
	private String				referrerCode;
	
	private String				referrerLocationName;
	
	public Integer getCid() {
		return cid;
	}
	
	public void setCid(Integer cid) {
		this.cid = cid;
	}
	
	public String getReferralCode() {
		return referralCode;
	}
	
	public void setReferralCode(String referralCode) {
		this.referralCode = referralCode;
	}
	
	public Integer getReferrerId() {
		return referrerId;
	}
	
	public void setReferrerId(Integer referrerId) {
		this.referrerId = referrerId;
	}
	
	public String getReferrerEmail() {
		return referrerEmail;
	}
	
	public void setReferrerEmail(String referrerEmail) {
		this.referrerEmail = referrerEmail;
	}
	
	public String getReferrerPhoneNumber() {
		return referrerPhoneNumber;
	}
	
	public void setReferrerPhoneNumber(String referrerPhoneNumber) {
		this.referrerPhoneNumber = referrerPhoneNumber;
	}
	
	public Integer getReferrerEcid() {
		return referrerEcid;
	}
	
	public void setReferrerEcid(Integer referrerEcid) {
		this.referrerEcid = referrerEcid;
	}
	
	public String getReferrerCode() {
		return referrerCode;
	}
	
	public void setReferrerCode(String referrerCode) {
		this.referrerCode = referrerCode;
	}
	
	public String getReferrerLocationName() {
		return referrerLocationName;
	}
	
	public void setReferrerLocationName(String referrerLocationName) {
		this.referrerLocationName = referrerLocationName;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}