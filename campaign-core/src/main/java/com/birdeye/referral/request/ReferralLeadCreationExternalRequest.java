package com.birdeye.referral.request;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ReferralLeadCreationExternalRequest implements Serializable {
	
	//https://docs.google.com/document/d/1EARVTALUZsg1EzMdv04ISIZjYeR7GLhAYvMRx6zXjJA/edit
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -4892614857410134634L;
	private Long	accountId; //mandatory
	private Integer	sharedSourceId; //mandatory
	private String	leadName; //mandatory
	private String	leadEmail; //mandatory
	private String	leadPhone; //mandatory
	private Long	leadCreatedTime; //mandatory
	
	
	//Either request id 
	
	//New Lead creation parameters
	private Long	requestId;
	
	//Historical Lead creation parameters(tmx only)
	private Integer campaignId;
	private Integer templateId;
	private String templateSource;
	private String referrerFirstName;
	private String extRefCode;
	
	public Long getAccountId() {
		return accountId;
	}
	
	public void setAccountId(Long accountId) {
		this.accountId = accountId;
	}
	
	public Long getRequestId() {
		return requestId;
	}
	
	public void setRequestId(Long requestId) {
		this.requestId = requestId;
	}
	
	public Integer getSharedSourceId() {
		return sharedSourceId;
	}
	
	public void setSharedSourceId(Integer sharedSourceId) {
		this.sharedSourceId = sharedSourceId;
	}
	
	public String getLeadName() {
		return leadName;
	}
	
	public void setLeadName(String leadName) {
		this.leadName = leadName;
	}
	
	public String getLeadEmail() {
		return leadEmail;
	}
	
	public void setLeadEmail(String leadEmail) {
		this.leadEmail = leadEmail;
	}
	
	public String getLeadPhone() {
		return leadPhone;
	}
	
	public void setLeadPhone(String leadPhone) {
		this.leadPhone = leadPhone;
	}
	
	public Long getLeadCreatedTime() {
		return leadCreatedTime;
	}
	
	public void setLeadCreatedTime(Long leadCreatedTime) {
		this.leadCreatedTime = leadCreatedTime;
	}
	
	public Integer getCampaignId() {
		return campaignId;
	}

	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}

	public Integer getTemplateId() {
		return templateId;
	}

	public void setTemplateId(Integer templateId) {
		this.templateId = templateId;
	}


	public String getTemplateSource() {
		return templateSource;
	}

	public void setTemplateSource(String templateSource) {
		this.templateSource = templateSource;
	}

	public String getReferrerFirstName() {
		return referrerFirstName;
	}

	public void setReferrerFirstName(String referrerFirstName) {
		this.referrerFirstName = referrerFirstName;
	}

	public String getExtRefCode() {
		return extRefCode;
	}

	public void setExtRefCode(String extRefCode) {
		this.extRefCode = extRefCode;
	}

	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return b.toString();
	}
}
