/**
 * @file_name ReferralCountIncrementRequest.java
 * @created_date 5 Aug 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.referral.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * @file_name ReferralCountIncrementRequest.java
 * @created_date 5 Aug 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public class ReferralCountIncrementRequest implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 9037402157446088243L;
	
	private Integer				sourceId;
	
	private Long				reviewRequestId;
	
	private Date				referralDate;
	
	private Integer				appointmentId;
	
	/**
	 * @return the sourceId
	 */
	public Integer getSourceId() {
		return sourceId;
	}
	
	/**
	 * @param sourceId
	 *            the sourceId to set
	 */
	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}
	
	/**
	 * @return the reviewRequestId
	 */
	public Long getReviewRequestId() {
		return reviewRequestId;
	}
	
	/**
	 * @param reviewRequestId
	 *            the reviewRequestId to set
	 */
	public void setReviewRequestId(Long reviewRequestId) {
		this.reviewRequestId = reviewRequestId;
	}
	
	/**
	 * @return the referralDate
	 */
	public Date getReferralDate() {
		return referralDate;
	}
	
	/**
	 * @param referralDate
	 *            the referralDate to set
	 */
	public void setReferralDate(Date referralDate) {
		this.referralDate = referralDate;
	}
	
	/**
	 * @return the appointmentId
	 */
	public Integer getAppointmentId() {
		return appointmentId;
	}
	
	/**
	 * @param appointmentId
	 *            the appointmentId to set
	 */
	public void setAppointmentId(Integer appointmentId) {
		this.appointmentId = appointmentId;
	}
	
	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("ReferralCountIncrementRequest [sourceId=");
		builder.append(sourceId);
		builder.append(", reviewRequestId=");
		builder.append(reviewRequestId);
		builder.append(", referralDate=");
		builder.append(referralDate);
		builder.append(", appointmentId=");
		builder.append(appointmentId);
		builder.append("]");
		return builder.toString();
	}
	
}
