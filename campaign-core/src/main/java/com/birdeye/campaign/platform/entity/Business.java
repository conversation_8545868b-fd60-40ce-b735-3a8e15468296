package com.birdeye.campaign.platform.entity;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import org.hibernate.annotations.Formula;

import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.utils.PhoneNumberCustomFormat;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "business")
public class Business implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Basic(optional = false)
    @Column(name = "updated")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updated;

    @Basic(optional = false)
    @Column(name = "created")
    @Temporal(TemporalType.TIMESTAMP)
    private Date created;

    @Basic(optional = false)
    @Column(name = "mail_resend_frequency")
    private Integer mailResendFrequency = 0;

    @Column(name = "last_scan_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastScanDate;

    @Column(name = "last_dup_check_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastDupCheckDate;

    @Column(name = "reviews_scrapped")
    private Integer reviewsScrapped;

    @Basic(optional = false)
    @Column(name = "business_id", nullable = false, insertable = false)
    private Long businessId = 0L;

    @Size(min = 1, max = 16)
    @Column(name = "activation_status", length = 16)
    private String activationStatus = "demo";

    @Basic(optional = false)
    @Size(min = 1, max = 1000)
    @Column(name = "name", nullable = false, length = 1000)
    private String name;

    @Size(max = 100)
    @Column(name = "phone", length = 100)
    private String phone;

    @Size(max = 100)
    @Column(name = "fax", length = 100)
    private String fax;

    @Basic(optional = false)
    @Size(min = 1, max = 200)
    @Column(name = "email_id", nullable = false, length = 200)
    private String emailId;

    @Column(name = "bazaarify_email_id", length = 115)
    private String bazaarifyEmailId;

    @Size(max = 1000)
    @Column(name = "website_url", length = 1000)
    private String websiteUrl;

    @Lob
    @Size(max = 65535)
    @Column(name = "description", length = 65535)
    private String description;

    @Size(max = 1000)
    @Column(name = "keywords", length = 1000)
    private String keywords;

    @Size(max = 1000)
    @Column(name = "services", length = 1000)
    private String services;

    @Size(max = 1000)
    @Column(name = "logo_url", length = 1000)
    private String logoUrl;

    @Column(name = "review_agg_repeat_hours")
    private Integer reviewAggRepeatHours = 24;

    public String getZohoId() {
        return zohoId;
    }

    public void setZohoId(String zohoId) {
        this.zohoId = zohoId;
    }

    @Size(max = 150)
    @Column(name = "zoho_id", length = 1000)
    private String zohoId;

    @Size(max = 300)
    @Column(name = "team_image_url", length = 300)
    private String teamImageUrl;

    @Size(max = 300)
    @Column(name = "cover_image_url", length = 300)
    private String coverImageUrl;

    @Size(max = 1000)
    @Column(name = "image1_url", length = 1000)
    private String image1Url;

    @Size(max = 1000)
    @Column(name = "image2_url", length = 1000)
    private String image2Url;

    @Size(max = 1000)
    @Column(name = "image3_url", length = 1000)
    private String image3Url;

    @Size(max = 100)
    @Column(name = "timezone", length = 100)
    private String timezone;
    
    @Size(max = 100)
    @Column(name = "timezone_id", length = 100)
    private String timezoneId;

    @Size(max = 100)
    @Column(name = "languages", length = 100)
    private String languages;

    @Size(max = 100)
    @Column(name = "payment", length = 100)
    private String payment;

    @Column(name = "whole_week_operating")
    private Integer wholeWeekOperating = 0;

    @Size(max = 100)
    @Column(name = "membership_type", length = 100)
    private String membershipType;

    @Basic(optional = false)
    @Size(min = 1, max = 8)
    @Column(name = "status", nullable = false, length = 8)
    private String status;

    @Column(name = "closed")
    private Integer closed;

    @Column(name = "businessFound")
    private Integer businessFound;

    @Column(name = "merge_review")
    private Integer mergeReview = 0;

    @Column(name = "website_integration")
    private Integer websiteIntegration = 0;

    @Column(name = "tablet_sent")
    private Integer tabletSent = 0;

    @Column(name = "group_id")
    private Integer groupId = 0;

    @Column(name = "template_id", insertable = false, updatable = false)
    private Integer templateId;

    @JoinColumn(name = "location_id", referencedColumnName = "id")
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.MERGE)
    private Location location;

    @Column(name = "location_id", insertable = false, updatable = false)
    private Integer locationId;

    @JoinColumn(name = "parent_id", referencedColumnName = "id")
    @ManyToOne(optional = false,fetch = FetchType.LAZY)
    private Business parentId;

    @Column(name = "parent_id",  insertable = false, updatable = false)
    private Integer businessParentId;
    
    @Column(name = "show_dollar_value")
    private Integer showDollarValue = 1;

    @Column(name = "type")
    private String type = "Business";

    @JoinColumn(name = "reseller_id", referencedColumnName = "id")
    @ManyToOne(optional = true,fetch = FetchType.LAZY)
    private Business reseller;

    @Column(name = "reseller_id", insertable = false, updatable = false)
    private Integer resellerId;

    @JoinColumn(name = "enterprise_id", referencedColumnName = "id")
    @ManyToOne(optional = true,fetch = FetchType.LAZY)
    private Business enterprise;

    @Column(name = "enterprise_id", insertable = false, updatable = false)
    private Integer enterpriseId;

    @Column(name = "alias1")
    @Size(max = 100)
    private String alias1;

    @Column(name = "seller")
    @Size(max = 2)
    private String seller = "BS";

    @Column(name = "widget_bgcolor")
    private String widgetBGColor = "#51a8d3";

    @Column(name = "unsubscribed")
    private Integer unsubscribed = 0;

    @Column(name = "unsubscribed_reason")
    private String unsubscribedReason;

    @Column(name = "rep_id")
    private Integer repId;

    @Column(name = "lead_created")
    private Integer leadCreated = 0;

    @Column(name = "cust_unsubscribe_notification")
    private Integer custUnsubscribeNotification = 0;

    @Column(name = "hwtg_mr_emlsnd_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date hwtgMrEmlsndDate;

    @Column(name = "sms_enabled")
    @Size(max = 1)
    private Integer smsEnabled = 0;

    @Column(name = "presence_enabled")
    private Integer presenceEnabled = 0;

    @Column(name = "presence_order_id")
    private Integer presenceOrderId = 0;

    @Column(name = "avg_review_rating")
    private double avgReviewRating = 0.0;

    @Column(name = "review_count")
    private Integer reviewCount = 0;

    @Column(name = "lead_mail_sent")
    private Integer leadMailSent = 0;

    @Column(name = "categories")
    private String categories;

    @Column(name = "reseller_as_mail_sender")
    private Integer resellerAsMailSender = 1;

    @Column(name = "find_in_progress")
    private Integer findInProgress = 0;

    @Column(name = "update_in_progress")
    private Integer updateInProgress = 0;

    @Column(name = "dup_check_in_progress")
    private Integer dupCheckInProgress = 0;

    @Column(name = "analytics_enabled")
    private Integer analyticsEnabled;

    @Column(name = "next_find_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date nextFindDate;

    @Column(name = "next_update_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date nextUpdateDate;

    @Column(name = "next_scrape_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date nextScrapeDate;

    @Column(name = "yext_categories")
    private String yextCategories;

    @Column(name = "account_type")
    private String accountType = "Direct";

    @Column(name = "seo_enabled")
    private Integer seoEnabled = 1;

    @Column(name = "is_added_by_competitor")
    private Integer isAddedByCompetitor = 0;

    @Column(name = "competitive_analysis_enabled")
    private Integer competitiveAnalysisEnabled;

    @Column(name = "deep_link_enabled")
    private Integer deepLinkEnabled = 1;

    @Column(name = "last_feed_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastFeedDate;

    @Column(name = "review_request_email_enabled")
    private Integer reviewRequestEmailEnabled = 1;

    @Column(name = "review_request_sms_enabled")
    private Integer reviewRequestSmsEnabled = 1;

    @Column(name = "featured_reviews_enabled")
    private Integer featuredReviewsEnabled = 0;

    @Column(name = "enable_park_reviews")
    private Integer enableParkReviews;

    @Column(name = "enable_new_emails")
    private Integer enableNewEmails = 1;

    @Column(name = "ext_reference_id")
    private String externalReferenceId;

    @Column(name = "social_enabled")
    private Integer socialEnabled = 0;

    @Column(name = "social_crm_enabled")
    private Integer socialCrmEnabled = 0;

    @Column(name = "next_reply_scrape_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date nextReplyScrapeDate;

    @Column(name = "ignore_google_tutorial")
    private Integer ignoreGoogleTutorial;

    @Column(name = "re_aggregation_required")
    private Integer reAggregationRequired = 0;

    @Column(name = "pagespeed_score")
    private Integer pageSpeedScore;

    @Column(name = "survey_enabled")
    private Integer surveyEnabled=0;
    
    @Column(name = "mms_enabled")
    private Integer mmsEnabled = 0;
    
    @Formula("case "
    		+ "when alias1 is null then name "
    		+ "when alias1 = '' then name "
    		+ "else alias1 "
    		+ "end")
    private String nameOrAlias;

    public String getNameOrAlias() {
		return nameOrAlias;
	}

	public void setNameOrAlias(String nameOrAlias) {
		this.nameOrAlias = nameOrAlias;
	}

	public Business() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, 60);
        this.nextFindDate = cal.getTime();
        cal.add(Calendar.DAY_OF_YEAR, 30);
        this.nextUpdateDate = cal.getTime();
        cal.set(1971, 1, 3);
        this.nextReplyScrapeDate = cal.getTime();
        cal.set(1971, 1, 1);
        this.lastFeedDate = cal.getTime();
    }

    public String getAlias1() {
        return alias1;
    }

    public void setAlias1(String alias1) {
        this.alias1 = alias1;
    }

    public Business(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return getPhone(null);
    }
    
    public String getPhone(PhoneNumberCustomFormat customFormat) {
        return CoreUtils.formatPhoneFaxNumber(phone, location, customFormat);
    }

    public void setPhone(String phone) {
        this.phone = CoreUtils.formatPhoneFaxNumber(phone, location);
    }

     public String getFax(){
         return getFax(null);
     }
    public String getFax(PhoneNumberCustomFormat customFormat) {
        return CoreUtils.formatPhoneFaxNumber(fax, location, customFormat);
    }

    public void setFax(String fax) {
        this.fax = CoreUtils.formatPhoneFaxNumber(fax, location);
    }

    public String getEmailId() {
        return emailId;
    }

    public void setEmailId(String emailId) {
        this.emailId = emailId.toLowerCase();
    }

    public String getWebsiteUrl() {
        return websiteUrl;
    }

    public void setWebsiteUrl(String websiteUrl) {
        this.websiteUrl = websiteUrl;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getLogoUrl() {
        return logoUrl;
    }

    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl;
    }

    public String getImage1Url() {
        return image1Url;
    }

    public void setImage1Url(String image1Url) {
        this.image1Url = image1Url;
    }

    public String getImage2Url() {
        return image2Url;
    }

    public void setImage2Url(String image2Url) {
        this.image2Url = image2Url;
    }

    public String getImage3Url() {
        return image3Url;
    }

    public void setImage3Url(String image3Url) {
        this.image3Url = image3Url;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }
    
    public String getTimezoneId() {
        return timezoneId;
    }

    public void setTimezoneId(String timezoneId) {
        this.timezoneId = timezoneId;
    }

    public String getLanguages() {
        return languages;
    }

    public void setLanguages(String languages) {
        this.languages = languages;
    }

    public String getPayment() {
        return payment;
    }

    public void setPayment(String payment) {
        this.payment = payment;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getClosed() {
        return closed;
    }
    /**
	 * @return the businessParentId
	 */
	public Integer getBusinessParentId() {
		return businessParentId;
	}

	/**
	 * @param businessParentId the businessParentId to set
	 */
	public void setBusinessParentId(Integer businessParentId) {
		this.businessParentId = businessParentId;
	}

	public void setClosed(Integer closed) {
        this.closed = closed;
    }

    public String getBazaarifyEmailId() {
        return bazaarifyEmailId;
    }

    public void setBazaarifyEmailId(String bazaarifyEmailId) {
        this.bazaarifyEmailId = bazaarifyEmailId;
    }

    public String getActivationStatus() {
        return activationStatus;
    }

    public void setActivationStatus(String activationStatus) {
        this.activationStatus = activationStatus;
    }

    public Integer getReviewRequestEmailEnabled() {
        return reviewRequestEmailEnabled;
    }

    public void setReviewRequestEmailEnabled(Integer reviewRequestEmailEnabled) {
        this.reviewRequestEmailEnabled = reviewRequestEmailEnabled;
    }

    public Integer getReviewRequestSmsEnabled() {
        return reviewRequestSmsEnabled;
    }

    public void setReviewRequestSmsEnabled(Integer reviewRequestSmsEnabled) {
        this.reviewRequestSmsEnabled = reviewRequestSmsEnabled;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof Business)) {
            return false;
        }
        Business other = (Business) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    public Date getUpdated() {
        return updated;
    }

    public void setUpdated(Date updated) {
        this.updated = updated;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Integer getTemplateId() {
        return templateId;
    }

    public void setTemplateid(Integer templateId) {
        this.templateId = templateId;
    }

    public String getCoverImageUrl() {
        return coverImageUrl;
    }

    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }

    public String getTeamImageUrl() {
        return teamImageUrl;
    }

    public void setTeamImageUrl(String teamImageUrl) {
        this.teamImageUrl = teamImageUrl;
    }

    public Integer getBusinessFound() {
        return businessFound;
    }

    public void setBusinessFound(Integer businessFound) {
        this.businessFound = businessFound;
    }

    public String getServices() {
        return services;
    }

    public void setServices(String services) {
        this.services = services;
    }

    public Integer getReviewsScrapped() {
        return reviewsScrapped;
    }

    public void setReviewsScrapped(Integer reviewsScrapped) {
        this.reviewsScrapped = reviewsScrapped;
    }

    public Integer getMergeReview() {
        return mergeReview;
    }

    public void setMergeReview(Integer mergeReview) {
        this.mergeReview = mergeReview;
    }

    public Business getParentId() {
        return parentId;
    }

    public void setParentId(Business parentId) {
        this.parentId = parentId;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getSeller() {
        return seller;
    }

    public void setSeller(String seller) {
        this.seller = seller;
    }

    public Integer getWebsiteIntegration() {
        return websiteIntegration;
    }

    public void setWebsiteIntegration(Integer websiteIntegration) {
        this.websiteIntegration = websiteIntegration;
    }

    public Integer getTabletSent() {
        return tabletSent;
    }

    public void setTabletSent(Integer tabletSent) {
        this.tabletSent = tabletSent;
    }

    public Integer getWholeWeekOperating() {
        return wholeWeekOperating;
    }

    public void setWholeWeekOperating(Integer wholeWeekOperating) {
        this.wholeWeekOperating = wholeWeekOperating;
    }

    public String getMembershipType() {
        return membershipType;
    }

    public void setMembershipType(String membershipType) {
        this.membershipType = membershipType;
    }

    public Location getLocation() {
        return location;
    }

    public void setLocation(Location location) {
        this.location = location;
        this.fax = CoreUtils.formatPhoneFaxNumber(fax, location);
        this.phone = CoreUtils.formatPhoneFaxNumber(phone, location);
    }

    public Integer getLocationId() {
        return locationId;
    }

    public void setLocationId(Integer locationId) {
        this.locationId = locationId;
    }

    public Integer getMailResendFrequency() {
        return mailResendFrequency;
    }

    public void setMailResendFrequency(Integer mailResendFrequency) {
        this.mailResendFrequency = mailResendFrequency;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getResellerId() {
        Business tempReseller = getReseller();
        if (tempReseller != null) {
            return tempReseller.getId();
        }
        return null;
    }

    public void setResellerId(Integer resellerId) {
        this.resellerId = resellerId;
    }

    public Business getReseller() {
        if (this.reseller == null
                && this.enterprise != null) {
            Business tempBusiness = this.enterprise;
            while (tempBusiness != null
                    && tempBusiness.getReseller() == null
                    && tempBusiness.getEnterprise() != null) {
                tempBusiness = tempBusiness.getEnterprise();
            }
            if (tempBusiness != null) {
                return tempBusiness.getReseller();
            }
        }
        return reseller;
    }

    public void setReseller(Business reseller) {
        this.reseller = reseller;
    }

    public Integer getShowDollarValue() {
        return showDollarValue;
    }

    public void setShowDollarValue(Integer showDollarValue) {
        this.showDollarValue = showDollarValue;
    }

    public String getWidgetBGColor() {
        return widgetBGColor;
    }

    public void setWidgetBGColor(String widgetBGColor) {
        this.widgetBGColor = widgetBGColor;
    }

    public Integer getUnsubscribed() {
        return unsubscribed;
    }

    public void setUnsubscribed(Integer unsubscribed) {
        this.unsubscribed = unsubscribed;
    }

    public String getUnsubscribedReason() {
        return unsubscribedReason;
    }

    public void setUnsubscribedReason(String unsubscribedReason) {
        this.unsubscribedReason = unsubscribedReason;
    }

    public Integer getRepId() {
        return repId;
    }

    public void setRepId(Integer repId) {
        this.repId = repId;
    }

    public Integer getLeadCreated() {
        return leadCreated;
    }

    public void setLeadCreated(Integer leadCreated) {
        this.leadCreated = leadCreated;
    }

    public Integer getCustUnsubscribeNotification() {
        return custUnsubscribeNotification;
    }

    public void setCustUnsubscribeNotification(Integer custUnsubscribeNotification) {
        this.custUnsubscribeNotification = custUnsubscribeNotification;
    }

    public Date getHwtgMrEmlsndDate() {
        return hwtgMrEmlsndDate;
    }

    public void setHwtgMrEmlsndDate(Date hwtgMrEmlsndDate) {
        this.hwtgMrEmlsndDate = hwtgMrEmlsndDate;
    }

    public Integer getSmsEnabled() {
        return smsEnabled;
    }

    public void setSmsEnabled(Integer smsEnabled) {
        this.smsEnabled = smsEnabled;
    }

    public double getAvgReviewRating() {
        return avgReviewRating;
    }

    public void setAvgReviewRating(double avgReviewRating) {
        this.avgReviewRating = avgReviewRating;
    }

    public Integer getReviewCount() {
        return reviewCount;
    }

    public void setReviewCount(Integer reviewCount) {
        this.reviewCount = reviewCount;
    }

    public Integer getLeadMailSent() {
        return leadMailSent;
    }

    public void setLeadMailSent(Integer leadMailSent) {
        this.leadMailSent = leadMailSent;
    }

    public String getCategories() {
        return categories;
    }

    public void setCategories(String categories) {
        this.categories = categories;
    }

    public Integer getResellerAsMailSender() {
        return resellerAsMailSender;
    }

    public void setResellerAsMailSender(Integer resellerAsMailSender) {
        this.resellerAsMailSender = resellerAsMailSender;
    }

    public Date getNextFindDate() {
        return nextFindDate;
    }

    public void setNextFindDate(Date nextFindDate) {
        this.nextFindDate = nextFindDate;
    }

    public Date getNextUpdateDate() {
        return nextUpdateDate;
    }

    public void setNextUpdateDate(Date nextUpdateDate) {
        this.nextUpdateDate = nextUpdateDate;
    }

    public Date getNextScrapeDate() {
        return nextScrapeDate;
    }

    public void setNextScrapeDate(Date nextScrapeDate) {
        this.nextScrapeDate = nextScrapeDate;
    }

    public Integer getFindInProgress() {
        return findInProgress;
    }

    public void setFindInProgress(Integer findInProgress) {
        this.findInProgress = findInProgress;
    }

    public Integer getUpdateInProgress() {
        return updateInProgress;
    }

    public void setUpdateInProgress(Integer updateInProgress) {
        this.updateInProgress = updateInProgress;
    }

    public Integer getPresenceEnabled() {
        return presenceEnabled;
    }

    public void setPresenceEnabled(Integer presenceEnabled) {
        this.presenceEnabled = presenceEnabled;
    }

    public String getYextCategories() {
        return yextCategories;
    }

    public void setYextCategories(String yextCategories) {
        this.yextCategories = yextCategories;
    }

    public Integer getPresenceOrderId() {
        return presenceOrderId;
    }

    public void setPresenceOrderId(Integer presenceOrderId) {
        this.presenceOrderId = presenceOrderId;
    }

    public Business getEnterprise() {
        return enterprise;
    }

    public void setEnterprise(Business enterprise) {
        this.enterprise = enterprise;
    }

    public Integer getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Integer enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public Integer getStoredResellerId() {
        return resellerId;
    }

    public Integer getAnalyticsEnabled() {
        return analyticsEnabled;
    }

    public void setAnalyticsEnabled(Integer analyticsEnabled) {
        this.analyticsEnabled = analyticsEnabled;
    }

    public Integer getSeoEnabled() {
        return seoEnabled;
    }

    public void setSeoEnabled(Integer seoEnabled) {
        this.seoEnabled = seoEnabled;
    }

    public Integer getIsAddedByCompetitor() {
        return isAddedByCompetitor;
    }

    public void setIsAddedByCompetitor(Integer isAddedByCompetitor) {
        this.isAddedByCompetitor = isAddedByCompetitor;
    }

    public Integer getCompetitiveAnalysisEnabled() {
        return competitiveAnalysisEnabled;
    }

    public void setCompetitiveAnalysisEnabled(Integer competitiveAnalysisEnabled) {
        this.competitiveAnalysisEnabled = competitiveAnalysisEnabled;
    }

    public Integer getDeepLinkEnabled() {
        return deepLinkEnabled;
    }

    public void setDeepLinkEnabled(Integer deepLinkEnabled) {
        this.deepLinkEnabled = deepLinkEnabled;
    }

    public Date getLastScanDate() {
        return lastScanDate;
    }

    public void setLastScanDate(Date lastScanDate) {
        this.lastScanDate = lastScanDate;
    }

    public Date getLastDupCheckDate() {
        return lastDupCheckDate;
    }

    public void setLastDupCheckDate(Date lastDupCheckDate) {
        this.lastDupCheckDate = lastDupCheckDate;
    }

    public Integer getDupCheckInProgress() {
        return dupCheckInProgress;
    }

    public void setDupCheckInProgress(Integer dupCheckInProgress) {
        this.dupCheckInProgress = dupCheckInProgress;
    }

    /**
     * @return the lastFeedDate
     */
    public Date getLastFeedDate() {
        return lastFeedDate;
    }

    /**
     * @param lastFeedDate the lastFeedDate to set
     */
    public void setLastFeedDate(Date lastFeedDate) {
        this.lastFeedDate = lastFeedDate;
    }

    public Integer getFeaturedReviewsEnabled() {
        return featuredReviewsEnabled;
    }

    public void setFeaturedReviewsEnabled(Integer featuredReviewsEnabled) {
        this.featuredReviewsEnabled = featuredReviewsEnabled;
    }

    public Integer getReviewAggRepeatHours() {
        return reviewAggRepeatHours;
    }

    public void setReviewAggRepeatHours(Integer reviewAggRepeatHours) {
        this.reviewAggRepeatHours = reviewAggRepeatHours;
    }

    public Integer getEnableParkReviews() {
        return enableParkReviews;
    }

    public void setEnableParkReviews(Integer enableParkReviews) {
        this.enableParkReviews = enableParkReviews;
    }

    public Integer getEnableNewEmails() {
        return enableNewEmails;
    }

    public void setEnableNewEmails(Integer enableNewEmails) {
        this.enableNewEmails = enableNewEmails;
    }

    public String getExternalReferenceId() {
        return externalReferenceId;
    }

    public void setExternalReferenceId(String externalReferenceId) {
        this.externalReferenceId = externalReferenceId;
    }

    public Integer getSocialEnabled() {
        return socialEnabled;
    }

    public void setSocialEnabled(Integer socialEnabled) {
        this.socialEnabled = socialEnabled;
    }

    public Integer getSocialCrmEnabled() {
        return socialCrmEnabled;
    }

    public void setSocialCrmEnabled(Integer socialCrmEnabled) {
        this.socialCrmEnabled = socialCrmEnabled;
    }

    public Date getNextReplyScrapeDate() {
        return nextReplyScrapeDate;
    }

    public void setNextReplyScrapeDate(Date nextReplyScrapeDate) {
        this.nextReplyScrapeDate = nextReplyScrapeDate;
    }

    /**
     * @return the ignoreGoogleTutorial
     */
    public Integer getIgnoreGoogleTutorial() {
        return ignoreGoogleTutorial;
    }

    /**
     * @param ignoreGoogleTutorial the ignoreGoogleTutorial to set
     */
    public void setIgnoreGoogleTutorial(Integer ignoreGoogleTutorial) {
        this.ignoreGoogleTutorial = ignoreGoogleTutorial;
    }

    public Integer getReAggregationRequired() {
        return reAggregationRequired;
    }

    public void setReAggregationRequired(Integer reAggregationRequired) {
        this.reAggregationRequired = reAggregationRequired;
    }

    public Integer getPageSpeedScore() {
        return pageSpeedScore;
    }

    public void setPageSpeedScore(Integer pageSpeedScore) {
        this.pageSpeedScore = pageSpeedScore;
    }

    public Integer getSurveyEnabled() {
        return surveyEnabled;
    }

    public void setSurveyEnabled(Integer surveyEnabled) {
        this.surveyEnabled = surveyEnabled;
    }

    /**
     * @return the mmsEnabled
     */
    public Integer getMmsEnabled() {
        return mmsEnabled;
    }

    /**
     * @param mmsEnabled the mmsEnabled to set
     */
    public void setMmsEnabled(Integer mmsEnabled) {
        this.mmsEnabled = mmsEnabled;
    }

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("Business [id=");
		builder.append(id);
		builder.append(", updated=");
		builder.append(updated);
		builder.append(", created=");
		builder.append(created);
		builder.append(", mailResendFrequency=");
		builder.append(mailResendFrequency);
		builder.append(", lastScanDate=");
		builder.append(lastScanDate);
		builder.append(", lastDupCheckDate=");
		builder.append(lastDupCheckDate);
		builder.append(", reviewsScrapped=");
		builder.append(reviewsScrapped);
		builder.append(", businessId=");
		builder.append(businessId);
		builder.append(", activationStatus=");
		builder.append(activationStatus);
		builder.append(", name=");
		builder.append(name);
		builder.append(", phone=");
		builder.append(phone);
		builder.append(", fax=");
		builder.append(fax);
		builder.append(", emailId=");
		builder.append(emailId);
		builder.append(", bazaarifyEmailId=");
		builder.append(bazaarifyEmailId);
		builder.append(", websiteUrl=");
		builder.append(websiteUrl);
		builder.append(", description=");
		builder.append(description);
		builder.append(", keywords=");
		builder.append(keywords);
		builder.append(", services=");
		builder.append(services);
		builder.append(", logoUrl=");
		builder.append(logoUrl);
		builder.append(", reviewAggRepeatHours=");
		builder.append(reviewAggRepeatHours);
		builder.append(", zohoId=");
		builder.append(zohoId);
		builder.append(", teamImageUrl=");
		builder.append(teamImageUrl);
		builder.append(", coverImageUrl=");
		builder.append(coverImageUrl);
		builder.append(", image1Url=");
		builder.append(image1Url);
		builder.append(", image2Url=");
		builder.append(image2Url);
		builder.append(", image3Url=");
		builder.append(image3Url);
		builder.append(", timezone=");
		builder.append(timezone);
		builder.append(", timezoneId=");
		builder.append(timezoneId);
		builder.append(", languages=");
		builder.append(languages);
		builder.append(", payment=");
		builder.append(payment);
		builder.append(", wholeWeekOperating=");
		builder.append(wholeWeekOperating);
		builder.append(", membershipType=");
		builder.append(membershipType);
		builder.append(", status=");
		builder.append(status);
		builder.append(", closed=");
		builder.append(closed);
		builder.append(", businessFound=");
		builder.append(businessFound);
		builder.append(", mergeReview=");
		builder.append(mergeReview);
		builder.append(", websiteIntegration=");
		builder.append(websiteIntegration);
		builder.append(", tabletSent=");
		builder.append(tabletSent);
		builder.append(", groupId=");
		builder.append(groupId);
		builder.append(", templateId=");
		builder.append(templateId);
		builder.append(", locationId=");
		builder.append(locationId);
		builder.append(", businessParentId=");
		builder.append(businessParentId);
		builder.append(", showDollarValue=");
		builder.append(showDollarValue);
		builder.append(", type=");
		builder.append(type);
		builder.append(", resellerId=");
		builder.append(resellerId);
		builder.append(", enterpriseId=");
		builder.append(enterpriseId);
		builder.append(", alias1=");
		builder.append(alias1);
		builder.append(", seller=");
		builder.append(seller);
		builder.append(", widgetBGColor=");
		builder.append(widgetBGColor);
		builder.append(", unsubscribed=");
		builder.append(unsubscribed);
		builder.append(", unsubscribedReason=");
		builder.append(unsubscribedReason);
		builder.append(", repId=");
		builder.append(repId);
		builder.append(", leadCreated=");
		builder.append(leadCreated);
		builder.append(", custUnsubscribeNotification=");
		builder.append(custUnsubscribeNotification);
		builder.append(", hwtgMrEmlsndDate=");
		builder.append(hwtgMrEmlsndDate);
		builder.append(", smsEnabled=");
		builder.append(smsEnabled);
		builder.append(", presenceEnabled=");
		builder.append(presenceEnabled);
		builder.append(", presenceOrderId=");
		builder.append(presenceOrderId);
		builder.append(", avgReviewRating=");
		builder.append(avgReviewRating);
		builder.append(", reviewCount=");
		builder.append(reviewCount);
		builder.append(", leadMailSent=");
		builder.append(leadMailSent);
		builder.append(", categories=");
		builder.append(categories);
		builder.append(", resellerAsMailSender=");
		builder.append(resellerAsMailSender);
		builder.append(", findInProgress=");
		builder.append(findInProgress);
		builder.append(", updateInProgress=");
		builder.append(updateInProgress);
		builder.append(", dupCheckInProgress=");
		builder.append(dupCheckInProgress);
		builder.append(", analyticsEnabled=");
		builder.append(analyticsEnabled);
		builder.append(", nextFindDate=");
		builder.append(nextFindDate);
		builder.append(", nextUpdateDate=");
		builder.append(nextUpdateDate);
		builder.append(", nextScrapeDate=");
		builder.append(nextScrapeDate);
		builder.append(", yextCategories=");
		builder.append(yextCategories);
		builder.append(", accountType=");
		builder.append(accountType);
		builder.append(", seoEnabled=");
		builder.append(seoEnabled);
		builder.append(", isAddedByCompetitor=");
		builder.append(isAddedByCompetitor);
		builder.append(", competitiveAnalysisEnabled=");
		builder.append(competitiveAnalysisEnabled);
		builder.append(", deepLinkEnabled=");
		builder.append(deepLinkEnabled);
		builder.append(", lastFeedDate=");
		builder.append(lastFeedDate);
		builder.append(", reviewRequestEmailEnabled=");
		builder.append(reviewRequestEmailEnabled);
		builder.append(", reviewRequestSmsEnabled=");
		builder.append(reviewRequestSmsEnabled);
		builder.append(", featuredReviewsEnabled=");
		builder.append(featuredReviewsEnabled);
		builder.append(", enableParkReviews=");
		builder.append(enableParkReviews);
		builder.append(", enableNewEmails=");
		builder.append(enableNewEmails);
		builder.append(", externalReferenceId=");
		builder.append(externalReferenceId);
		builder.append(", socialEnabled=");
		builder.append(socialEnabled);
		builder.append(", socialCrmEnabled=");
		builder.append(socialCrmEnabled);
		builder.append(", nextReplyScrapeDate=");
		builder.append(nextReplyScrapeDate);
		builder.append(", ignoreGoogleTutorial=");
		builder.append(ignoreGoogleTutorial);
		builder.append(", reAggregationRequired=");
		builder.append(reAggregationRequired);
		builder.append(", pageSpeedScore=");
		builder.append(pageSpeedScore);
		builder.append(", surveyEnabled=");
		builder.append(surveyEnabled);
		builder.append(", mmsEnabled=");
		builder.append(mmsEnabled);
		builder.append(", nameOrAlias=");
		builder.append(nameOrAlias);
		builder.append("]");
		return builder.toString();
	}
    
    
}
