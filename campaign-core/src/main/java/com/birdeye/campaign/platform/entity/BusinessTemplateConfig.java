
package com.birdeye.campaign.platform.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "business_template_config")
public class BusinessTemplateConfig implements Serializable {
    private static final long serialVersionUID = 1L;
   
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @JoinColumn(name = "business_id", referencedColumnName = "id")
    @OneToOne(optional = false , fetch = FetchType.LAZY)
    private Business business;

    @Column(name = "business_id", insertable = false, updatable = false)
    private Integer businessId;

    @Column(name = "review_email_template_id")
    private Integer reviewEmailTemplateId;

    @Column(name = "review_sms_template_id")
    private Integer reviewSmsTemplateId;

    @Column(name = "survey_sms_template_id")
    private Integer surveySmsTemplateId;

    @Column(name = "survey_email_template_id")
    private Integer surveyEmailTemplateId;

    @Column(name = "share_review_template_id")
    private Integer shareReviewTemplateId;

    @Column(name = "promotion_email_template_id")
    private Integer promotionEmailTemplateId;
    
    @Column(name = "promotion_sms_template_id")
    private Integer promotionSmsTemplateId;

    @Column(name = "send_review_sms")
    private Integer sendReviewSms;
    
    @Column(name = "send_review_email")
    private Integer sendReviewEmail;
    
    @Column(name = "send_survey_email")
    private Integer sendSurveyEmail;
    
    @Column(name = "send_cx_email")
    private Integer sendCXEmail;
    
    @Column(name = "send_survey_sms")
    private Integer sendSurveySms;
    
    @Column(name = "send_promotion_email")
    private Integer sendPromotionEmail;
    
    @Column(name = "send_promotion_sms")
    private Integer sendPromotionSms;
    
    @Column(name = "send_cx_sms")
    private Integer sendCXSms;
    
    @Column(name = "send_checkin_after")
    private Integer sendCheckinAfter = 0;
    
    @Column(name = "send_share_review_email")
    private Integer sendShareReviewEmail;
    
    @Column(name = "survey_id")
    private Integer surveyId;
    
    @Column(name = "cx_email_template_id")
    private Integer cxEmailTemplateId;
    
    @Column(name = "cx_sms_template_id")
    private Integer cxSmsTemplateId;
    
    public BusinessTemplateConfig(){ 
    }
    
    /**
     * @return the id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id the id to set
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * @return the business
     */
    public Business getBusiness() {
        return business;
    }

    /**
     * @param business the business to set
     */
    public void setBusiness(Business business) {
        this.business = business;
    }

    /**
     * @return the businessId
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * @param businessId the businessId to set
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * @return the reviewEmailTemplateId
     */
    public Integer getReviewEmailTemplateId() {
        return reviewEmailTemplateId;
    }

    /**
     * @param reviewEmailTemplateId the reviewEmailTemplateId to set
     */
    public void setReviewEmailTemplateId(Integer reviewEmailTemplateId) {
        this.reviewEmailTemplateId = reviewEmailTemplateId;
    }

    /**
     * @return the reviewSmsTemplateId
     */
    public Integer getReviewSmsTemplateId() {
        return reviewSmsTemplateId;
    }

    /**
     * @param reviewSmsTemplateId the reviewSmsTemplateId to set
     */
    public void setReviewSmsTemplateId(Integer reviewSmsTemplateId) {
        this.reviewSmsTemplateId = reviewSmsTemplateId;
    }

    /**
     * @return the surveySmsTemplateId
     */
    public Integer getSurveySmsTemplateId() {
        return surveySmsTemplateId;
    }

    /**
     * @param surveySmsTemplateId the surveySmsTemplateId to set
     */
    public void setSurveySmsTemplateId(Integer surveySmsTemplateId) {
        this.surveySmsTemplateId = surveySmsTemplateId;
    }

    /**
     * @return the surveyEmailTemplateId
     */
    public Integer getSurveyEmailTemplateId() {
        return surveyEmailTemplateId;
    }

    /**
     * @param surveyEmailTemplateId the surveyEmailTemplateId to set
     */
    public void setSurveyEmailTemplateId(Integer surveyEmailTemplateId) {
        this.surveyEmailTemplateId = surveyEmailTemplateId;
    }

    /**
     * @return the shareReviewTemplateId
     */
    public Integer getShareReviewTemplateId() {
        return shareReviewTemplateId;
    }

    /**
     * @param shareReviewTemplateId the shareReviewTemplateId to set
     */
    public void setShareReviewTemplateId(Integer shareReviewTemplateId) {
        this.shareReviewTemplateId = shareReviewTemplateId;
    }

    /**
     * @return the promotionEmailTemplateId
     */
    public Integer getPromotionEmailTemplateId() {
        return promotionEmailTemplateId;
    }

    /**
     * @param promotionEmailTemplateId the promotionEmailTemplateId to set
     */
    public void setPromotionEmailTemplateId(Integer promotionEmailTemplateId) {
        this.promotionEmailTemplateId = promotionEmailTemplateId;
    }

    /**
     * @return the promotionSmsTemplateId
     */
    public Integer getPromotionSmsTemplateId() {
        return promotionSmsTemplateId;
    }

    /**
     * @param promotionSmsTemplateId the promotionSmsTemplateId to set
     */
    public void setPromotionSmsTemplateId(Integer promotionSmsTemplateId) {
        this.promotionSmsTemplateId = promotionSmsTemplateId;
    }

    /**
     * @return the sendReviewSms
     */
    public Integer getSendReviewSms() {
        return sendReviewSms;
    }

    /**
     * @param sendReviewSms the sendReviewSms to set
     */
    public void setSendReviewSms(Integer sendReviewSms) {
        this.sendReviewSms = sendReviewSms;
    }

    /**
     * @return the sendReviewEmail
     */
    public Integer getSendReviewEmail() {
        return sendReviewEmail;
    }

    /**
     * @param sendReviewEmail the sendReviewEmail to set
     */
    public void setSendReviewEmail(Integer sendReviewEmail) {
        this.sendReviewEmail = sendReviewEmail;
    }

    /**
     * @return the sendSurveyEmail
     */
    public Integer getSendSurveyEmail() {
        return sendSurveyEmail;
    }

    /**
     * @param sendSurveyEmail the sendSurveyEmail to set
     */
    public void setSendSurveyEmail(Integer sendSurveyEmail) {
        this.sendSurveyEmail = sendSurveyEmail;
    }

    /**
     * @return the sendSurveySms
     */
    public Integer getSendSurveySms() {
        return sendSurveySms;
    }

    /**
     * @param sendSurveySms the sendSurveySms to set
     */
    public void setSendSurveySms(Integer sendSurveySms) {
        this.sendSurveySms = sendSurveySms;
    }

    /**
     * @return the sendPromotionEmail
     */
    public Integer getSendPromotionEmail() {
        return sendPromotionEmail;
    }

    /**
     * @param sendPromotionEmail the sendPromotionEmail to set
     */
    public void setSendPromotionEmail(Integer sendPromotionEmail) {
        this.sendPromotionEmail = sendPromotionEmail;
    }

    /**
     * @return the sendPromotionSms
     */
    public Integer getSendPromotionSms() {
        return sendPromotionSms;
    }

    /**
     * @param sendPromotionSms the sendPromotionSms to set
     */
    public void setSendPromotionSms(Integer sendPromotionSms) {
        this.sendPromotionSms = sendPromotionSms;
    }

    /**
     * @return the sendShareReviewEmail
     */
    public Integer getSendShareReviewEmail() {
        return sendShareReviewEmail;
    }

    /**
     * @param sendShareReviewEmail the sendShareReviewEmail to set
     */
    public void setSendShareReviewEmail(Integer sendShareReviewEmail) {
        this.sendShareReviewEmail = sendShareReviewEmail;
    }

    /**
     * @return the surveyId
     */
    public Integer getSurveyId() {
        return surveyId;
    }

    /**
     * @param surveyId the surveyId to set
     */
    public void setSurveyId(Integer surveyId) {
        this.surveyId = surveyId;
    }

    /**
     * @return the sendCheckinAfter
     */
    public Integer getSendCheckinAfter() {
        return sendCheckinAfter;
    }

    /**
     * @param sendCheckinAfter the sendCheckinAfter to set
     */
    public void setSendCheckinAfter(Integer sendCheckinAfter) {
        this.sendCheckinAfter = sendCheckinAfter;
    }

	public Integer getSendCXEmail() {
		return sendCXEmail;
	}

	public void setSendCXEmail(Integer sendCXEmail) {
		this.sendCXEmail = sendCXEmail;
	}

	public Integer getSendCXSms() {
		return sendCXSms;
	}

	public void setSendCXSms(Integer sendCXSms) {
		this.sendCXSms = sendCXSms;
	}

	public Integer getCxEmailTemplateId() {
		return cxEmailTemplateId;
	}

	public void setCxEmailTemplateId(Integer cxEmailTemplateId) {
		this.cxEmailTemplateId = cxEmailTemplateId;
	}

	public Integer getCxSmsTemplateId() {
		return cxSmsTemplateId;
	}

	public void setCxSmsTemplateId(Integer cxSmsTemplateId) {
		this.cxSmsTemplateId = cxSmsTemplateId;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("BusinessTemplateConfig [id=");
		builder.append(id);
		builder.append(", businessId=");
		builder.append(businessId);
		builder.append(", reviewEmailTemplateId=");
		builder.append(reviewEmailTemplateId);
		builder.append(", reviewSmsTemplateId=");
		builder.append(reviewSmsTemplateId);
		builder.append(", surveySmsTemplateId=");
		builder.append(surveySmsTemplateId);
		builder.append(", surveyEmailTemplateId=");
		builder.append(surveyEmailTemplateId);
		builder.append(", shareReviewTemplateId=");
		builder.append(shareReviewTemplateId);
		builder.append(", promotionEmailTemplateId=");
		builder.append(promotionEmailTemplateId);
		builder.append(", promotionSmsTemplateId=");
		builder.append(promotionSmsTemplateId);
		builder.append(", sendReviewSms=");
		builder.append(sendReviewSms);
		builder.append(", sendReviewEmail=");
		builder.append(sendReviewEmail);
		builder.append(", sendSurveyEmail=");
		builder.append(sendSurveyEmail);
		builder.append(", sendCXEmail=");
		builder.append(sendCXEmail);
		builder.append(", sendSurveySms=");
		builder.append(sendSurveySms);
		builder.append(", sendPromotionEmail=");
		builder.append(sendPromotionEmail);
		builder.append(", sendPromotionSms=");
		builder.append(sendPromotionSms);
		builder.append(", sendCXSms=");
		builder.append(sendCXSms);
		builder.append(", sendCheckinAfter=");
		builder.append(sendCheckinAfter);
		builder.append(", sendShareReviewEmail=");
		builder.append(sendShareReviewEmail);
		builder.append(", surveyId=");
		builder.append(surveyId);
		builder.append(", cxEmailTemplateId=");
		builder.append(cxEmailTemplateId);
		builder.append(", cxSmsTemplateId=");
		builder.append(cxSmsTemplateId);
		builder.append("]");
		return builder.toString();
	}
    
}