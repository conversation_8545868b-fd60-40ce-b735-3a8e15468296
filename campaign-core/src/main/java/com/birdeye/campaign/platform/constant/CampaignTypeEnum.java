package com.birdeye.campaign.platform.constant;

import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 */

public enum CampaignTypeEnum {
	REVIEW_REQUEST("review_request", "review_request"), 
	SURVEY_REQUEST("survey_request", "survey_request"), 
	PROMOTIONAL("promotional", "promotional"), 
	CX_REQUEST("cx_request", "customer_experience"),
	REFERRAL("referral", "referral"),
	APPOINTMENT_REMINDER("appointment_reminder","appointment_reminder"),
	APPOINTMENT_RECALL("appointment_recall","appointment_recall"),
	APPOINTMENT_FORM("appointment_form", "appointment_form"),
	QR_REVIEW("qr_review","qr_review");
	
	private String	type;
	
	private String	templateType;
	
	private CampaignTypeEnum(String type, String templateType) {
		this.type = type;
		this.templateType = templateType;
	}
	
	public String getType() {
		return type;
	}
	
	public String getTemplateType() {
		return templateType;
	}
	
	public static String getAlias(String requestType) {
		String alias = null;
		CampaignTypeEnum campEnum = getSupportedType(requestType);
		if (campEnum != null) {
			switch (campEnum) {
				case REVIEW_REQUEST:
					alias = "Review request";
					break;
				case CX_REQUEST:
					alias = "Customer Experience";
					break;
				case SURVEY_REQUEST:
					alias = "Survey request";
					break;
				case PROMOTIONAL:
					alias = "Run promotion";
					break;
				case REFERRAL:
					alias = "Referral Request";
					break;
				case APPOINTMENT_REMINDER:
					alias = "Appointment Reminder" ;
					break;
				case APPOINTMENT_RECALL:
					alias = "Appointment Recall" ;
					break;
				case APPOINTMENT_FORM:
					alias = "Appointment Form" ;
					break;
			}
		}
		return alias;
	}
	
	public CampaignTypeEnum getEnum() {
		return this;
	}
	
	public static CampaignTypeEnum getEnum(String type) {
		CampaignTypeEnum campEnum = null;
		for (CampaignTypeEnum statusEnum : CampaignTypeEnum.values()) {
			if (StringUtils.equalsIgnoreCase(statusEnum.getType(), type)) {
				campEnum = statusEnum;
				break;
			}
		}
		return campEnum;
	}
	
	private static CampaignTypeEnum fromTemplateType(String status) {
		CampaignTypeEnum campEnum = null;
		for (CampaignTypeEnum statusEnum : CampaignTypeEnum.values()) {
			if (StringUtils.equalsIgnoreCase(statusEnum.getTemplateType(), status)) {
				campEnum = statusEnum;
				break;
			}
		}
		return campEnum;
	}
	
	/** 
	 * 
	 * 
	 * Helper wrapper method to ensure handing of CampaignType management. 
	 * Multiple type values are kept to support old & new paths.
	 * @param requestType - This type would be reviewrequest, template or campaign
	 * @return
	 */
	public static CampaignTypeEnum getSupportedType(String requestType) {
		// Check first with templateType support
		CampaignTypeEnum campaignType = fromTemplateType(requestType);
		// If not found,
		if (campaignType == null) {
			campaignType = getEnum(requestType);
		}
		return campaignType;
	}
	
}
