package com.birdeye.campaign.platform.entity;

import java.io.Serializable;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

@Entity
@Table(name = "business_level_value_mapping")
public class BusinessLevelValueMapping implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Integer id;
    
    @Basic(optional = false)
    @Column(name = "business_id")
    private int businessId;
    
    @Column(name = "level_value_id")
    private Integer levelValueId;
    
    @JoinColumn(name = "level_value_id", referencedColumnName = "id", insertable=false, updatable=false)
    @ManyToOne(fetch = FetchType.LAZY)
    private LevelValue levelValue;

    @Column(name = "hierarchy_json")
    private String hierarchyJson;
    
    @Column(name = "level_id")
    private Integer levelId;

    @Column(name = "parent_id")
    private Integer parentId;
    
    
    public BusinessLevelValueMapping() {
    }

    public BusinessLevelValueMapping(Integer id) {
        this.id = id;
    }

    public BusinessLevelValueMapping(Integer id, int businessId) {
        this.id = id;
        this.businessId = businessId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public int getBusinessId() {
        return businessId;
    }

    public void setBusinessId(int businessId) {
        this.businessId = businessId;
    }

    public Integer getLevelValueId() {
        return levelValueId;
    }

    public void setLevelValueId(Integer levelValueId) {
        this.levelValueId = levelValueId;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof BusinessLevelValueMapping)) {
            return false;
        }
        BusinessLevelValueMapping other = (BusinessLevelValueMapping) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {//NOSONAR
            return false;
        }
        return true;
    }

    /**
     * @return the levelValue
     */
    public LevelValue getLevelValue() {
        return levelValue;
    }

    /**
     * @param levelValue the levelValue to set
     */
    public void setLevelValue(LevelValue levelValue) {
        this.levelValue = levelValue;
    }

    /**
     * @return the hierarchyJson
     */
    public String getHierarchyJson() {
        return hierarchyJson;
    }

    /**
     * @param hierarchyJson the hierarchyJson to set
     */
    public void setHierarchyJson(String hierarchyJson) {
        this.hierarchyJson = hierarchyJson;
    }

    /**
     * @return the levelId
     */
    public Integer getLevelId() {
        return levelId;
    }

    /**
     * @param levelId the levelId to set
     */
    public void setLevelId(Integer levelId) {
        this.levelId = levelId;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("BusinessLevelValueMapping [id=");
		builder.append(id);
		builder.append(", businessId=");
		builder.append(businessId);
		builder.append(", levelValueId=");
		builder.append(levelValueId);
		builder.append(", hierarchyJson=");
		builder.append(hierarchyJson);
		builder.append(", levelId=");
		builder.append(levelId);
		builder.append(", parentId=");
		builder.append(parentId);
		builder.append("]");
		return builder.toString();
	}
    
}
