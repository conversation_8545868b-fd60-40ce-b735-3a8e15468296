package com.birdeye.campaign.platform.constant;

public enum NexusRecipientTypeEnum {

	CUSTOMER("customer"), BUSINESS_USER("business_user"), BIRDEYE_INTERNAL("birdeye_internal"), CUSTOM(
			"custom"), OTHERS("others");

	String recipientType;

	private NexusRecipientTypeEnum(String recipientType) {
		this.recipientType = recipientType;
	}

	public String getRecipientType() {
		return recipientType;
	}
}