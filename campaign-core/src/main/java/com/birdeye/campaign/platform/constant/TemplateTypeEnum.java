package com.birdeye.campaign.platform.constant;

import java.util.HashMap;
import java.util.Map;

public enum TemplateTypeEnum {

	REVIEW_REQUEST_NEW("review_request_new", "Review request email"),
	REVIEW_REQUEST_SMS("review_request", "Review request SMS"),
	PROMOTION("promotion", "Promotion email"),
	PROMOTION_SMS("promotion", "Promotion SMS"),
	CUSTOMER_EXPERIENCE_SMS("customer_experience", "Customer experience SMS"),
	CUSTOMER_EXPERIENCE("customer_experience", "Customer experience email"),
	REVIEW_REFERRAL("review_referral", "Share review email"),
	SURVEY_REQUEST("survey_request", "Survey email"),
	SURVEY_REQUEST_SMS("survey_request", "Survey SMS"),
	REFERRAL("referral", "Referral email"),
	REFERRAL_SMS("referral_sms", "Referral SMS"),
	REFERRAL_LEAD_SUSPECT_TEXT("referral_lead_suspect_text","referral_lead_suspect_text"),
	APPOINTMENT_REMINDER_SMS("appointment_reminder","Appointment reminder SMS"),
	APPOINTMENT_REMINDER("appointment_reminder","Appointment reminder email"),
	APPOINTMENT_RECALL_SMS("appointment_recall","Appointment recall SMS"),
	APPOINTMENT_RECALL("appointment_recall","Appointment recall email"),
	APPOINTMENT_FORM_SMS("appointment_form","Appointment form SMS"),
	APPOINTMENT_FORM("appointment_form","Appointment form email"),
	QR_REVIEW("qr_review","qr_review");
	// FREE_TEXT - This template is used as a marker for free text campaigns sent via messenger - not to be shown on UI or used for any other purpose


	private final String	name;
	
	private final String	label;
	
	private static final Map<String, TemplateTypeEnum> map;
	static {
		map = new HashMap<>();
		for (TemplateTypeEnum v : TemplateTypeEnum.values()) {
			map.put(v.name, v);
		}
	}
	
	public static TemplateTypeEnum findByKey(String s) {
		return map.get(s);
	}
	
	private TemplateTypeEnum(String name, String label) {
		this.name = name;
		this.label = label;
	}
	
	public String getName() {
		return name;
	}
	
	public String getLabel() {
		return label;
	}
	
	public static void main(String[] args) {
		String label = getLabelByName("appointment_reminder");
		System.out.println(label);
		
	}
	
	public static String getLabelByName(String name) {
		for (TemplateTypeEnum type : values()) {
			if (type.getName().equalsIgnoreCase(name)) {
				return type.getLabel();
			}
		}
		return null;
	}
	
	public static TemplateTypeEnum getTemplateTypeEnumByName(String name) {
		for (TemplateTypeEnum type : values()) {
			if (type.getName().equalsIgnoreCase(name)) {
				return type;
			}
		}
		return null;
	}
	
}
