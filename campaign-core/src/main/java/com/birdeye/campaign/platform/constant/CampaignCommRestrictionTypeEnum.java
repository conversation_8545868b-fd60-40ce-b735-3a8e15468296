package com.birdeye.campaign.platform.constant;


public enum CampaignCommRestrictionTypeEnum {
    IRRESPECTIVE_OF_CAMPAIGN_TYPE(0, "IRRESPECTIVE_OF_CAMPAIGN_TYPE"),
    SHARED_CAMPAIGN_TYPE(1,"SHARED_CAMPAIGN_TYPE"),
    PER_CAMPAIGN_TYPE(2,"PER_CAMPAIGN_TYPE");

    private final Integer value;

    private final String restrictionType;

    CampaignCommRestrictionTypeEnum(int value, String restrictionType) {
        this.value = value;
        this.restrictionType = restrictionType;
    }

    public Integer getValue() {
        return value;
    }

    public String getRestrictionType() {
        return restrictionType;
    }

    public static String getValueOf(Integer value) {
        for (CampaignCommRestrictionTypeEnum campaignCommRestrictionTypeEnum : CampaignCommRestrictionTypeEnum.values()) {
            if (campaignCommRestrictionTypeEnum.getValue() == value) {
                return campaignCommRestrictionTypeEnum.getRestrictionType();
            }
        }
        return null;
    }


}
