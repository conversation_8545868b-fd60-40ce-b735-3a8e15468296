package com.birdeye.campaign.platform.constant;

import org.apache.commons.lang3.StringUtils;

public enum CampaignTriggerTypeEnum {
	
	REVIEW_WRITTEN("review_written"), SURVEY_COMPLETED("survey_completed"), REFERRAL_LEAD_GENERATED("referral_lead_generated"), INBOX_CONVERSATION_CLOSED(
			"inbox_conversation_closed"), CONTACT_ADDED("contact_added"), LEAD_CONVERTED_TO_CONTACT("lead_converted_to_contact"), PAYMENT_COMPLETED(
					"payment_completed"), PAYMENT_REFUNDED("payment_refunded"), PAYMENT_FAILED("payment_failed"), BEFORE_APPOINTMENT_DATE(
							"before_appointment_date"), APPOINTMENT_RECALL("appointment_recall"), APPOINTMENT_BOOKED("appointment_booked"), APPOINTMENT_CANCELED(
									"appointment_canceled"), APPOINTMENT_MISSED("appointment_missed"), APPOINTMENT_COMPLETED("appointment_completed"), CONTACT_EVENT("contact_event");
	
	public String getType() {
		return type;
	}
	
	private final String type;
	
	CampaignTriggerTypeEnum(String type) {
		this.type = type;
	}
	
	public static CampaignTriggerTypeEnum getCampaignTriggerTypeEnum(String type) {
		for (CampaignTriggerTypeEnum campaignTriggerTypeEnum : CampaignTriggerTypeEnum.values()) {
			if (StringUtils.equalsIgnoreCase(type, campaignTriggerTypeEnum.getType())) {
				return campaignTriggerTypeEnum;
			}
		}
		return null;
	}
	
}
