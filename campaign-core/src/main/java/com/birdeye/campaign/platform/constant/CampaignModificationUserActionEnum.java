package com.birdeye.campaign.platform.constant;

import org.apache.commons.lang3.StringUtils;

public enum CampaignModificationUserActionEnum {
	CREATE("created"), EDIT("edited"), PAUSE("paused"), RESUME("resumed"), DELETE("deleted"), MARK_DEFAULT("mark-default"), UNMARK_DEFAULT("unmark-default"), CAMPAIGN_COMPLETE(
			"completed"), STATUS_UPDATE("status-update"), INVALID_ACTION_TYPE("invalid_action_type");
	
	private String userActionType;
	
	private CampaignModificationUserActionEnum(String userActionType) {
		this.userActionType = userActionType;
	}
	
	/**
	 * @return the userActionType
	 */
	public String getUserActionType() {
		return userActionType;
	}
	
	/**
	 * Method to determine UserActionActionEnum based on string actionType.
	 * 
	 * @param userActionType
	 * @return
	 */
	public static CampaignModificationUserActionEnum getEnum(String userActionType) {
		for (CampaignModificationUserActionEnum userActionEnum : CampaignModificationUserActionEnum.values()) {
			if (StringUtils.equalsIgnoreCase(userActionType, userActionEnum.getUserActionType())) {
				return userActionEnum;
			}
		}
		
		if (StringUtils.equalsAnyIgnoreCase(userActionType, "Scheduled", "Running", "Paused", "Expired", "Stopped", "Completed")) {
			return STATUS_UPDATE;
		}
		return INVALID_ACTION_TYPE;
	}
}
