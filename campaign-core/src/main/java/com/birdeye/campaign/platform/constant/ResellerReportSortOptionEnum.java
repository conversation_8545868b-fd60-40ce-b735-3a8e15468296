package com.birdeye.campaign.platform.constant;

public enum ResellerReportSortOptionEnum {
	
	EMAIL_SENT_COUNT("email_sent_count"), EMAIL_OPENED_COUNT("email_opened_count"), EMAIL_CLICKED_COUNT("email_clicked_count"), SMS_SENT_COUNT("sms_sent_count"), SMS_CLICKED_COUNT(
			"sms_clicked_count");
	
	private String sortBy;
	
	private ResellerReportSortOptionEnum(String sortBy) {
		this.sortBy = sortBy;
	}
	
	/**
	 * @return the order
	 */
	public String getSortBy() {
		return sortBy;
	}
	
}
