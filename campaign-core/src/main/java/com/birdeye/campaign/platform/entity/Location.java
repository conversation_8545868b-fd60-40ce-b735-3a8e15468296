package com.birdeye.campaign.platform.entity;

import java.io.Serializable;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "location")
public class Location implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    //@NotNull
    @Column(name = "id", nullable = false)
    private Integer id;
    @Size(max = 1000)
    @Column(name = "address1", length = 1000)
    private String address1;
    @Size(max = 1000)
    @Column(name = "address2", length = 1000)
    private String address2;
    @Size(max = 1000)
    @Column(name = "city", length = 1000)
    private String city;
    @Size(max = 100)
    @Column(name = "state", length = 100)
    private String state;
    @Size(max = 1000)
    @Column(name = "zip", length = 1000)
    private String zip;
    @Size(max = 2)
    @Column(name = "country_code", length = 2)
    private String countryCode;
    @Size(max = 100)
    @Column(name = "country_name", length = 100)
    private String countryName;

    @Column(name = "latitude")
    private Long latitude;
    
    @Column(name = "longitude")
    private Long longitude;
    
    public Location() {
    }

    public Location(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAddress1() {
        return address1;
    }

    public void setAddress1(String address1) {
        this.address1 = address1;
    }

    public String getAddress2() {
        return address2;
    }

    public void setAddress2(String address2) {
        this.address2 = address2;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getZip() {
        return zip;
    }

    public void setZip(String zip) {
        this.zip = zip;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public Long getLatitude() {
        return latitude;
    }

    public void setLatitude(Long latitude) {
        this.latitude = latitude;
    }

    public Long getLongitude() {
        return longitude;
    }

    public void setLongitude(Long longitude) {
        this.longitude = longitude;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof Location)) {
            return false;
        }
        Location other = (Location) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    /**
	  * To String implemntation for logging the entities.
	  * @return the toString 
	*/
    public String entityLog() {
        return "Location{" + "id=" + id + ", address1=" + address1 + ", address2=" + address2 + ", city=" + city + ", state=" + state + ", zip=" + zip + ", countryCode=" + countryCode + ", countryName=" + countryName + "}";
    }

    @Override
    public String toString() {
        return entityLog();
    }

}
