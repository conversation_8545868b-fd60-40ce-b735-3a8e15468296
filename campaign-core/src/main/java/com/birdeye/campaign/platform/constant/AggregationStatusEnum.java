package com.birdeye.campaign.platform.constant;

public enum AggregationStatusEnum {
    SEARCH(1,"Search"), PROFILE(2,"Profile"), REVIEWS(3,"Reviews"), COMPLETE(4,"Complete");
    
    int id;
    String name;

    private AggregationStatusEnum(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static AggregationStatusEnum getAggregationStatusByName(String name) {
        for (AggregationStatusEnum status : AggregationStatusEnum.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        return null;
    }
}
