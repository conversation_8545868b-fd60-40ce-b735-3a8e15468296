/**
 * @file_name BusinessAggregationEntityLite.java
 * @created_date 4 Mar 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.platform.entity;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * @file_name BusinessAggregationEntityLite.java
 * @created_date 4 Mar 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public class BusinessAggregationEntityLite implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -8836920367795975120L;
	private Integer	id;
	private Integer	sourceId;
	private String	sourceName;
	
	
	public BusinessAggregationEntityLite() {
		super();
	}
	/**
	 * @param id
	 * @param sourceId
	 * @param sourceName
	 */
	public BusinessAggregationEntityLite(Integer id, Integer sourceId, String sourceName) {
		super();
		this.id = id;
		this.sourceId = sourceId;
		this.sourceName = sourceName;
	}

	public Integer getId() {
		return id;
	}
	
	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getSourceId() {
		return sourceId;
	}
	
	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}
	
	public String getSourceName() {
		return sourceName;
	}
	
	public void setSourceName(String sourceName) {
		this.sourceName = sourceName;
	}
	
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("BusinessAggregationEntityLite [id=");
		builder.append(id);
		builder.append(", sourceId=");
		builder.append(sourceId);
		builder.append(", sourceName=");
		builder.append(sourceName);
		builder.append("]");
		return builder.toString();
	}
	
}
