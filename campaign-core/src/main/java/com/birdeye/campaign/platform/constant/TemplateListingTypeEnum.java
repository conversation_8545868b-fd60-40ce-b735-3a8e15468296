package com.birdeye.campaign.platform.constant;

import org.apache.commons.lang3.StringUtils;

public enum TemplateListingTypeEnum {
	
	CAMPAIGN_TAB("campaign_tab"), MESSENGER_TAB("messenger_tab"), TEMPLATES_TAB("templates_tab"), TEMPLATES_TAB_FILTERS("templates_tab_with_filters"), SPLIT_CAMPAIGN_TAB("split_campaign_tab");
	
	private String type;
	
	private TemplateListingTypeEnum(String type) {
		this.type = type;
	}
	
	public String getType() {
		return type;
	}
	
	public static TemplateListingTypeEnum getListingTypeEnum(String type) {
		TemplateListingTypeEnum tempEnum = null;
		for (TemplateListingTypeEnum listingEnum : TemplateListingTypeEnum.values()) {
			if (StringUtils.equalsIgnoreCase(type, listingEnum.getType())) {
				tempEnum = listingEnum;
				break;
			}
		}
		return tempEnum;
	}
	
}
