package com.birdeye.campaign.platform.constant;

/**
 * 
 * <AUTHOR>
 *
 */
public enum LocaleTextEnum {
	
	RECALL_SMS_BOOKING_CTA_TEXT_SCHEDULING_ENABLED("Please click here to book an appointment for your checkup."),
	CONFIRM_TEXT("confirm, or reply with C to confirm"),
	RESCHEDULE_TEXT("reschedule"),
	CANCEL_TEXT("cancel"),
	CONFIRM_OR_CANCEL_TEXT("confirm or cancel, or reply with C to confirm"),
	RESCHEDULE_OR_CANCEL_TEXT("reschedule or cancel"),
	CONFIRM_OR_RESCHEDULE_TEXT("confirm or reschedule, or reply with C to confirm"),
	CONFIRM_RESCHEDULE_OR_CANCEL_TEXT("confirm, reschedule, or cancel, or reply with C to confirm"),
	CONFIRM_RESCHEDULE_CANCEL_DISABLED_TEXT("Please call our office at [Business Phone] to reschedule the appointment if needed."),
	CL<PERSON><PERSON>_HERE_TO_TEXT("Click here to "),
	RECALL_EMAIL_BOOKING_CTA_TEXT_SCHEDULING_ENABLED("Book your appointment here or call us at [Business Phone]."),
	RECALL_EMAIL_BOOKING_CTA_TEXT_SCHEDULING_DISABLED("Call us at [Business Phone] to book your appointment."),
	RECALL_SMS_BOOKING_CTA_TEXT_SCHEDULING_DISABLED("Please reply to this message or call our office at [Business Phone] to make an appointment. Thank you!"),
	DEFAULT_SMS_UNSUB_TEXT("Txt STOP to unsub."),
	DEFAULT_SPECIALIST_LABEL("Specialist"),
	TODAY("Today");

	private String defaultEnglishText;
	
	private LocaleTextEnum(String defaultEnglishText) {
		this.defaultEnglishText = defaultEnglishText;
	}
	
	/**
	 * @return the defaultEnglishText
	 */
	public String getDefaultEnglishText() {
		return defaultEnglishText;
	}
	
}
