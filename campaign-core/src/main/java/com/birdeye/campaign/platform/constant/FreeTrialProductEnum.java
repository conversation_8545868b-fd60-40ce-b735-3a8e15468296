package com.birdeye.campaign.platform.constant;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

/**
 * 
 * Enum for Free Trial Product
 * <AUTHOR>
 * @version 1.0
 * 
 */
public enum FreeTrialProductEnum {
	
	REVIEWS("REVIEWS", "REVIEWS");
	
	FreeTrialProductEnum(String productName, String coreServiceLabel) {
		this.productName = productName;
		this.coreServiceLabel = coreServiceLabel;
	}
	
	private String	productName;
	
	private String	coreServiceLabel;
	
	public String getProduct() {
		return productName;
	}
	
	public String getCoreServiceLabel() {
		return coreServiceLabel;
	}
	
	public static FreeTrialProductEnum getFreeTrialProductEnum(String coreServiceLabel) {
		for (FreeTrialProductEnum freeTrialProductEnum : FreeTrialProductEnum.values()) {
			if (freeTrialProductEnum.getCoreServiceLabel().equalsIgnoreCase(coreServiceLabel)) {
				return freeTrialProductEnum;
			}
		}
		return null;
	}
	
	public static boolean containsReviewsFreeTrial(List<String> coreServiceLabel) {
		if (CollectionUtils.isEmpty(coreServiceLabel)) {
			return false;
		}
		
		for (String serviceLabel : coreServiceLabel) {
			if (REVIEWS.getCoreServiceLabel().equalsIgnoreCase(serviceLabel)) {
				return true;
			}
		}
		return false;
	}
}
