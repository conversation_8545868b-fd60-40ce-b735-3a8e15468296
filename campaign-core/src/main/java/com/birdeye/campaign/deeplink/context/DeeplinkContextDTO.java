/**
 * @file_name DeeplinkContextDTO.java
 * @created_date 16 Oct 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.deeplink.context;

import java.io.Serializable;
import java.util.Collections;
import java.util.Map;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * stores -
 * 1. review request
 * 2. location id
 * 3. template id
 * 4. template type
 * 5. channel
 * 6. customer id
 * 7. customer parameters
 * 
 * @file_name DeeplinkContextDTO.java
 * @created_date 16 Oct 2020
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public class DeeplinkContextDTO implements Serializable {
	
	/**
	 * 
	 */
	private static final long			serialVersionUID	= 584552326708798140L;
	private Long						reviewRequestId;
	private Integer						campaignId;
	private BusinessEnterpriseEntity	locationOrSmb;
	private Integer						locationOrSmbId;
	private Integer						enterpriseOrSmbId;
	
	private String						requestType;								// rr, cx, referral, survey, custom, appointment AKA campaign.type
	
	private String						deviceType;									//@Constants.DEVICE_TYPE_WEB OR Constants.DEEPLINK_DEVICETYPE_IOS OR Constants.DEEPLINK_DEVICETYPE_ANDROID
	
	private Integer						templateId;
	private String						templateSource;								//email or sms
	
	private Integer						customerId;
	private String						customerFirstName;
	private Map<String, String>			customFields = Collections.emptyMap();
	
	private String 						productName;
	
	private String						customLeadFormTrackingBaseRedirectURL;					//to be used only for tmx to track custom lead form clicks

	private Integer						appointmentId; 

	private Integer						appointmentRecallId; 

	private Boolean						fetchEmployeeToken;
	/**
	 * @return the reviewRequestId
	 */
	public Long getReviewRequestId() {
		return reviewRequestId;
	}
	
	/**
	 * @param reviewRequestId
	 *            the reviewRequestId to set
	 */
	public void setReviewRequestId(Long reviewRequestId) {
		this.reviewRequestId = reviewRequestId;
	}
	
	/**
	 * @return the campaignId
	 */
	public Integer getCampaignId() {
		return campaignId;
	}

	/**
	 * @param campaignId the campaignId to set
	 */
	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}

	/**
	 * @return the locationOrSmb
	 */
	public BusinessEnterpriseEntity getLocationOrSmb() {
		return locationOrSmb;
	}
	
	/**
	 * @param locationOrSmb
	 *            the locationOrSmb to set
	 */
	public void setLocationOrSmb(BusinessEnterpriseEntity locationOrSmb) {
		this.locationOrSmb = locationOrSmb;
	}
	
	/**
	 * @return the templateId
	 */
	public Integer getTemplateId() {
		return templateId;
	}
	
	/**
	 * @param templateId
	 *            the templateId to set
	 */
	public void setTemplateId(Integer templateId) {
		this.templateId = templateId;
	}
	
	/**
	 * @return the requestType
	 */
	public String getRequestType() {
		return requestType;
	}
	
	/**
	 * @param requestType
	 *            the requestType to set
	 */
	public void setRequestType(String requestType) {
		this.requestType = requestType;
	}
	
	/**
	 * @return the templateSource
	 */
	public String getTemplateSource() {
		return templateSource;
	}
	
	/**
	 * @param templateSource
	 *            the templateSource to set - email or sms
	 */
	public void setTemplateSource(String templateSource) {
		this.templateSource = templateSource;
	}
	
	/**
	 * @return the customerId
	 */
	public Integer getCustomerId() {
		return customerId;
	}
	
	/**
	 * @param customerId
	 *            the customerId to set
	 */
	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}
	
	/**
	 * @return the customerFirstName
	 */
	public String getCustomerFirstName() {
		return customerFirstName;
	}

	/**
	 * @param customerFirstName the customerFirstName to set
	 */
	public void setCustomerFirstName(String customerFirstName) {
		this.customerFirstName = customerFirstName;
	}

	/**
	 * @return the customFields
	 */
	public Map<String, String> getCustomFields() {
		return customFields;
	}

	/**
	 * @param customFields the customFields to set
	 */
	public void setCustomFields(Map<String, String> customFields) {
		this.customFields = customFields;
	}

	/**
	 * @return the deviceType
	 */
	public String getDeviceType() {
		return deviceType;
	}
	
	/**
	 * @param deviceType
	 *            the deviceType to set
	 */
	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}
	
	/**
	 * @return the locationOrSmbId
	 */
	public Integer getLocationOrSmbId() {
		return locationOrSmbId;
	}

	/**
	 * @param locationOrSmbId the locationOrSmbId to set
	 */
	public void setLocationOrSmbId(Integer locationOrSmbId) {
		this.locationOrSmbId = locationOrSmbId;
	}

	/**
	 * @return the enterpriseOrSmbId
	 */
	public Integer getEnterpriseOrSmbId() {
		return enterpriseOrSmbId;
	}

	/**
	 * @param enterpriseOrSmbId the enterpriseOrSmbId to set
	 */
	public void setEnterpriseOrSmbId(Integer enterpriseOrSmbId) {
		this.enterpriseOrSmbId = enterpriseOrSmbId;
	}

	/**
	 * @return the customLeadFormTrackingBaseRedirectURL
	 */
	public String getCustomLeadFormTrackingBaseRedirectURL() {
		return customLeadFormTrackingBaseRedirectURL;
	}

	/**
	 * @param customLeadFormTrackingBaseRedirectURL the customLeadFormTrackingBaseRedirectURL to set
	 */
	public void setCustomLeadFormTrackingBaseRedirectURL(String customLeadFormTrackingBaseRedirectURL) {
		this.customLeadFormTrackingBaseRedirectURL = customLeadFormTrackingBaseRedirectURL;
	}

	/**
	 * @return the productName
	 */
	public String getProductName() {
		return productName;
	}

	/**
	 * @param productName the productName to set
	 */
	public void setProductName(String productName) {
		this.productName = productName;
	}

	public Integer getAppointmentId() {
		return appointmentId;
	}

	public void setAppointmentId(Integer appointmentId) {
		this.appointmentId = appointmentId;
	}

	public Integer getAppointmentRecallId() {
		return appointmentRecallId;
	}

	public void setAppointmentRecallId(Integer appointmentRecallId) {
		this.appointmentRecallId = appointmentRecallId;
	}
	
	public Boolean getFetchEmployeeToken() {
		return fetchEmployeeToken;
	}

	public void setFetchEmployeeToken(Boolean fetchEmployeeToken) {
		this.fetchEmployeeToken = fetchEmployeeToken;
	}

	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return b.toString();
	}
	
}
