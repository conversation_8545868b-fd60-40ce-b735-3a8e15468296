package com.birdeye.campaign.constant;

import java.util.Arrays;
import java.util.List;

/**
 ** File: Constants.java Created: 3 Jan 2019 Author: puneetgupta
 **
 ** This code is copyright (c) BirdEye Software India Pvt. Ltd.
 **/
public abstract class Constants {
	
	private Constants() {
	}
	
	public static final String			REQUEST_ID											= "requestId";
	public static final String			BUSINESS_ID											= "bid";
	public static final String			USER_ID												= "uid";
	public static final String			EMAIL_ID											= "emailId";
	public static final String			CAMPAIGN_COMPLETABLE_FUTURE_TASK_EXECUTOR			= "CampaignCompletableTaskExecutor";
	public static final String			REVIEW_REQUEST_OLD_TEMPLATE_TYPE					= "review_request";																	// Not used currently
	public static final int				MAX_RESELLER_HIERARCHY_DEPTH						= 10;
	public static final String			CAMPAIGN_AMAZONS3_TASK_EXECUTOR						= "CampaignAmazons3TaskExecutor";
	public static final int				MAX_ENTERPRISE_BUSINESS_NUMBERS_EXCEL				= 10000;
	public static final String			REVIEW_REQUEST_TEMPLATE_TYPE						= "review_request";
	public static final String			CX_TEMPLATE_TYPE									= "customer_experience";
	public static final String			REFERRAL_TEMPLATE_TYPE								= "referral";
	public static final String			REFERRAL_TEMPLATE_SMS_TYPE							= "referral_sms";
	public static final String			DEEPLINK_DEVICETYPE_IOS								= "ios";
	public static final String			DEEPLINK_DEVICETYPE_ANDROID							= "android";
	public static final String			SMS													= "_sms";
	public static final String			SOURCE_URL_FIELD									= "source_url";
	public static final String			ALIAS1												= "alias1";
	public static final String			TEMPLATE_BASE_TYPE_SMS								= "sms";
	public static final String			TEMPLATE_BASE_TYPE_EMAIL							= "email";
	public static final String			SORT_ORDER_ASC										= "asc";
	public static final String			SORT_ORDER_DESC										= "desc";
	public static final int				DEFAULT_BIRDEYE_SOURCEID							= 100;
	public static final String			DEFAULT_BIRDEYE_SOURCENAME							= "Our Website";
	public static final String			DERIVED_BUSINESS_SORT_FIELD_NAMEORALIAS				= "nameOrAlias";
	public static final String			CX_REPORT_INDEX										= "cxreport";
	public static final String			CX_REPORT_TYPE										= "analytics";
	public static final String			RR_REPORT_INDEX										= "commreport";
	public static final String			RR_REPORT_TYPE										= "analytics";
	public static final String			REFERRAL_REPORT_INDEX								= "referral_report";
	public static final String			REFERRAL_REPORT_TYPE								= "analytics";
	public static final String			SURVEY_REPORT_INDEX									= "survey";
	public static final String			SURVEY_REPORT_TYPE									= "comm";
	public static final String			REVIEW_INDEX										= "review";
	public static final String			REVIEW_INDEX_TYPE									= "analytics";
	public static final String			EMAIL_TEMPLATE_TYPES								= "promotion, review_request_new, survey_request, customer_experience, referral,appointment_reminder,appointment_recall, appointment_form, qr_review";
	
	public static final String			CAMPAIGN_TYPES										= "promotional, review_request, survey_request, cx_request, referral";
	public static final String			ENTERPRISE_BUSINESS_NUMBER_LOCATION_COLUMN			= "Location";
	public static final String			ENTERPRISE_BUSINESS_NUMBER_BUSINESS_NUMBER_COLUMN	= "Business number";
	public static final String			SURVEY_REQUEST_TEMPLATE_TYPE						= "survey_request";
	public static final String			REFERRAL_REQUEST_TEMPLATE_TYPE						= "referral";
	public static final Integer			TEMPLATE_IS_DELETED_DEFAULT							= 0;
	public static final Integer 		TEMPLATE_IS_DELETED									= 1;
	public static final String			DEVICE_TYPE_WEB										= "web";
	public static final String			DEVICE_TYPE_MOB										= "mob";
	public static final String			BUSINESS_ACCOUNT_TYPE_DIRECT						= "DIRECT";
	public static final String			DEFAULT_BIRDEYE_SOURCENAME_OURWEBSITE				= "Our Website";
	public static final String			DEFAULT_BIRDEYE_SOURCENAME_BIRDEYE					= "Birdeye";
	public static final String			API_URI_GET_BRANDING_BY_LOCATION					= "v1/branding/business/{businessId}/location";
	public static final CharSequence	API_URL_BUSINESS_ID_REGEX							= "{businessId}";
	public static final String			DEFAULT_CUSTOMER_NAME								= "there";
	public static final String			SHORTLINK_TOKEN										= "[Shortlink]";
	public static final String			TEMPLATE_TYPE_CX_ACRONYM							= "CX";
	public static final String			TEMPLATE_TYPE_SR_ACRONYM							= "SR";
	public static final String			TEMPLATE_TYPE_RR_ACRONYM							= "RR";
	public static final String			TEMPLATE_TYPE_REFERRAL_ACRONYM						= "Referral";
	public static final String			TEMPLATE_TYPE_APPOINTMENT_REMINDER_ACRONYM			= "appointment_reminder";
	public static final String			TEMPLATE_TYPE_APPOINTMENT_RECALL_ACRONYM			= "appointment_recall";
	public static final String			TEMPLATE_TYPE_APPOINTMENT_FORM_ACRONYM				= "appointment_form";
	public static final String			DEEPLINK_SUBPATH_MOBILE								= "mobile-deeplink";
	public static final String			DEEPLINK_SUBPATH_WEB								= "web-deeplink";
	public static final String			DEEPLINK_SUBPATH_MOBILE_NEW							= "review-us";
	public static final String			DEEPLINK_REFERRAL_URL_SUFFIX						= "referus";
	public static final String			REFERRAL_TRACKING_URL_SUFFIX						= "tracking";
	
	public static final String			DEEPLINK_SUBPATH_CX_SUFFIX							= "-cx";
	public static final String			SURVEYLINK_SUBPATH									= "/survey";
	public static final String			NPS_LABEL_2_TOKEN									= "[nps label 2]";
	public static final String			NPS_LABEL_1_TOKEN									= "[nps label 1]";
	public static final String			STAR_LABEL_1_TOKEN									= "[star label 1]";
	public static final String			STAR_LABEL_2_TOKEN									= "[star label 2]";
	public static final String			STAR_LABEL_3_TOKEN									= "[star label 3]";
	public static final String			STAR_LABEL_4_TOKEN									= "[star label 4]";
	public static final String			STAR_LABEL_5_TOKEN									= "[star label 5]";
	public static final String			NPS_STAR_URL_0_TOKEN								= "[nps star url 0]";
	public static final String			NPS_STAR_URL_1_TOKEN								= "[nps star url 1]";
	public static final String			NPS_STAR_URL_2_TOKEN								= "[nps star url 2]";
	public static final String			NPS_STAR_URL_3_TOKEN								= "[nps star url 3]";
	public static final String			NPS_STAR_URL_4_TOKEN								= "[nps star url 4]";
	public static final String			NPS_STAR_URL_5_TOKEN								= "[nps star url 5]";
	public static final String			NPS_STAR_URL_6_TOKEN								= "[nps star url 6]";
	public static final String			NPS_STAR_URL_7_TOKEN								= "[nps star url 7]";
	public static final String			NPS_STAR_URL_8_TOKEN								= "[nps star url 8]";
	public static final String			NPS_STAR_URL_9_TOKEN								= "[nps star url 9]";
	public static final String			NPS_STAR_URL_10_TOKEN								= "[nps star url 10]";
	public static final String			RATING_STAR_URL_1_TOKEN								= "[rating star url 1]";
	public static final String			RATING_STAR_URL_2_TOKEN								= "[rating star url 2]";
	public static final String			RATING_STAR_URL_3_TOKEN								= "[rating star url 3]";
	public static final String			RATING_STAR_URL_4_TOKEN								= "[rating star url 4]";
	public static final String			RATING_STAR_URL_5_TOKEN								= "[rating star url 5]";
	public static final String			STAR_FIVE_FILLED_TOKEN								= "[star five filled]";
	public static final String			STAR_FOUR_FILLED_TOKEN								= "[star four filled]";
	public static final String			STAR_THREE_FILLED_TOKEN								= "[star three filled]";
	public static final String			STAR_TWO_FILLED_TOKEN								= "[star two filled]";
	public static final String			STAR_ONE_FILLED_TOKEN								= "[star one filled]";
	public static final String			IMAGE_RATING_TOKEN									= "[Image Rating]";
	public static final String			NON_RECOMMENDED_LINK_TOKEN							= "[Non Recommended Link]";
	public static final String			CX_NEGATIVE_REVIEW_LINK_TOKEN						= "[CX Negative Review Link]";
	public static final String			CX_NEUTRAL_REVIEW_LINK_TOKEN						= "[CX Neutral Review Link]";
	public static final String			CX_POSITIVE_REVIEW_LINK_TOKEN						= "[CX Positive Review Link]";
	public static final String			NEUTRAL_BUTTON_LABEL_TOKEN							= "[Neutral Button Label]";
	public static final String			RECOMMENDED_LINK_TOKEN								= "[Recommended Link]";
	public static final String			ENVELOPE_SHOW_TOKEN									= "[Envelope Show]";
	public static final String			REFERRAL_CODE_TOKEN									= "[Referral code]";																// referrer code for all
																																												// (TMX, non-TMX)
	public static final String			REFERRER_NAME_TOKEN									= "[Referrer name]";																// contact full name
	public static final String			REFEREE_FIRST_NAME_TOKEN							= "[Referee first name]";
	public static final String			CAMPAIGN_PAUSED_FAILURE_MESSAGE						= "campaign has been paused";
	public static final String			CAMPAIGN_SUMMARY_MAIL_DELAY							= "campaign_summary_mail_delay";
	public static final String			HTML_TEMPLATE_V1									= "_v1";
	public static final String			CAMPAIGN_RUN_RR_TASK_EXECUTOR						= "CampaignRunRRTaskExecutor";
	public static final String			CS_EMAIL_HEADING_DEFAULT							= "Email";
	public static final String			CS_SMS_HEADING_DEFAULT								= "SMS";
	public static final String			CS_EMAIL_FAIL_FOOTER_DEFAULT						= "could not be delivered";
	public static final String			CS_SMS_FAIL_FOOTER_DEFAULT							= "could not be delivered";
	public static final String			CS_EMAIL_SUCCESS_FOOTER_DEFAULT						= "successfully sent";
	public static final String			CS_SMS_SUCCESS_FOOTER_DEFAULT						= "successfully sent";
	public static final String			DEEPLINK_CUSTOMER_ID_ENCRYPTION						= "deeplink.customer.id.encryption";
	
	public static final String			API_URI_GET_BUSINESS_BY_ID							= "v1/business/details/{businessId}";
	public static final String			DEEPLINK_FB_APP_ID_TOKEN							= "[Fb App Id]";
	public static final String			REFERRAL_MESSAGE_TOKEN								= "[Referral Message]";
	public static final String			REFERRAL_MESSAGE_WITHOUT_LINK						= "[Referral Message Without Link]";
	public static final String			REFERRAL_LINK_TOKEN									= "[Referral Link]";
	public static final String			REFERRAL_LINK_ENCODED_TOKEN							= "[Referral Link Encoded]";
	
	public static final String			REFERRAL_LINK_CUSTOM_TOKEN							= "[Referral Link Custom]";
	public static final String			REFERRAL_LINK_CUSTOM_ENCODED_TOKEN					= "[Referral Link Custom Encoded]";
	
	public static final String			REFERRAL_LINK_SELF_SUBMIT_TOKEN						= "[Referral Link Self Submit]";
	public static final String			REFERRAL_LINK_SELF_SUBMIT_ENCODED_TOKEN				= "[Referral Link Self Submit Encoded]";
	
	public static final String			REFERRAL_EMAIL_SUBJECT								= "[Referral Email Subject]";
	
	public static final String			APPOINTMENT_LINK_TOKEN								= "[Appointment Link]";
	public static final String			APPOINTMENT_PAGE_HEADING							= "We look forward to seeing you!";
	public static final String			APPOINTMENT_PAGE_HEADING2_CONTACT_NULL				= "Your friend loves [Business Name]. You should try us too!";
	public static final String			AGGREGATION_SOURCE_BIRDEYE_NAME						= "Birdeye";
	public static final String			YOUR_NAME_TOKEN										= "[Your Name]";																	// aka logged in user
																																												// name
																																												// name
	public static final String			PROMOTION_SMS_TYPE									= "promotional_sms";
	public static final String			MESSENGER_PROMOTION_SMS_TYPE						= "messenger_p";
	public static final String			PROMOTION_TEMPLATE_TYPE								= "promotion";
	public static final String			NEXUS_SUCCESS_ACK_STATUS							= "success";
	public static final String			NEXUS_FAILURE_ACK_STATUS							= "failure";
	public static final String			NEXUS_QUEUED_ACK_STATUS								= "queued";
	public static final String			PROMOTION_REPORT_INDEX								= "promotion_report";
	public static final String			APPOINTMENT_REMINDER_REPORT_INDEX					= "appointment_reminder";
	public static final String			APPOINTMENT_REMINDER_REPORT_TYPE					= "analytics";
	public static final String			APPOINTMENT_RECALL_REPORT_INDEX						= "appointment_recall";
	public static final String			APPOINTMENT_RECALL_REPORT_TYPE						= "analytics";
	public static final String			APPOINTMENT_FORM_REPORT_TYPE						= "analytics";
	public static final String			APPOINTMENT_FORM_REPORT_INDEX						= "appointment_form";
	public static final String			PROMOTION_REPORT_TYPE								= "analytics";
	public static final String			FREE_TEXT_TEMPLATE_TYPE								= "free_text";
	public static final String			FACEBOOK_REQUEST_TYPE								= "facebook";
	public static final String			GOOGLE_REQUEST_TYPE									= "google";
	public static final String			FACEBOOK_REVIEW_SOURCE_ID							= "110";
	public static final String			GOOGLE_REVIEW_SOURCE_ID								= "2";
	public static final String			FACEBOOK_SOURCE_ALIAS								= "Review us on Facebook";
	public static final String			GOOGLE_SOURCE_ALIAS									= "Review us on Google";
	public static final String			FACEBOOK_SOURCE_PRIORITY							= "2";
	public static final String			GOOGLE_SOURCE_PRIORITY								= "1";
	public static final String			EMAIL_REFERRAL_SOURCE_PRIORITY						= "1";
	public static final String			EMAIL_REFERRAL_SOURCE_ID							= "3";
	public static final String			TEXT_REFERRAL_SOURCE_PRIORITY						= "2";
	public static final String			TEXT_REFERRAL_SOURCE_ID								= "4";
	public static final String			FACEBOOK_REFERRAL_SOURCE_PRIORITY					= "3";
	public static final String			FACEBOOK_REFERRAL_SOURCE_ID							= "1";
	public static final String			REFERRAL_OPTIONS_DELIMITER							= "|^|";
	public static final String			QUICK_SEND_RR_TASK_EXECUTOR							= "QuickSendRunRRTaskExecutor";
	public static final String			MESSENGER_STATIC_BITLY_URL							= "http://bit.ly/10JSQ1C";
	public static final String			RR_FAILURE_REASON_GENERIC							= "failed";
	public static final String			REFERRAL_SORT_BY_SHARED_BY_PARAM					= "sharedBy";
	public static final String			REFERRAL_SORT_BY_SHARED_ON_PARAM					= "sharedOn";
	
	public static final String			LEAD_SOURCE_REFERRAL								= "referral";
	public static final String			LEAD_SORT_BY_LEADS_PARAM							= "leads";
	public static final String			LEAD_SORT_BY_SHARED_BY_PARAM						= "sharedBy";
	public static final String			LEAD_SORT_BY_CREATED_ON_PARAM						= "createdOn";
	public static final String			LEAD_SORT_BY_RESPONSE_PARAM							= "response";
	public static final String			MESSENGER_ACTIVITY_CLICKED_ON_PREFIX				= "Clicked on ";
	public static final String			MESSENGER_ACTIVITY_SHARED_ON_PREFIX					= "Shared on ";
	public static final String			REFERRAL_BUSINESS_ID_FIELD							= "businessId";
	public static final String			REFERRAL_ENTERPRISE_ID_FIELD						= "enterpriseId";
	
	public static final String			TEMPLATES_IN_USE									= "in_use";
	public static final String			TEMPLATES_NOT_IN_USE								= "not_in_use";
	
	public static final String			POLLER_DRIP_CAMPAIGN_BATCH_CREATE_JOB				= "drip-batch-create";
	public static final String			POLLER_DRIP_CAMPAIGN_BATCH_RUN_JOB					= "drip-batch-run";
	public static final String			TIMEZONE_PST										= "Pacific Standard Time";
	public static final String			AMERICA_LOS_ANGELES									= "America/Los_Angeles";
	
	public static final String			QUICK_SEND_VALIDATE_COUNTRY_CODE					= "quick_send_validate_country_code";
	public static final String			RR_SOURCE_EMAIL										= "email";
	public static final String			RR_SOURCE_SMS										= "sms";
	public static final String			REFERRAL_CREATED_DATE_FORMAT						= "MMM dd, yyyy";
	
	public static final String			CAMPAIGN_CUSTOMER_BATCH_SET							= "campaignCustomerBatchSet";
	public static final String			CAMPAIGN_BATCH_BIN									= "batch-bin";
	
	public static final String			CAMPAIGN_AUDIT_MODULE								= "campaign";
	public static final String			CAMPAIGN_AUDIT_MODULE_INVALID_EVENT					= "campaign | Invalid event";
	public static final String			CAMPAIGN_ES_DOC_DELETE								= "campaign | ES Doc delete";
	public static final String			SUCCESS_STATUS										= "success";
	public static final String			FAILURE_STATUS										= "failed";
	public static final String			CAMPAIGN_UI_COMPLETABLE_FUTURE_TASK_EXECUTOR		= "CampaignUICompletableTaskExecutor";
	public static final String			CAMPAIGN_REPORTING_TASK_EXECUTOR					= "CampaignReportingTaskExecutor";
	
	public static final String			REFERRAL_LEAD_SUSPECT_EMAIL_TYPE					= "lead_suspect_email";
	public static final String			REFERRAL_LEAD_SUSPECT_SMS_TYPE						= "lead_suspect_referral_text";
	public static final String			REFERRAL_LEAD_NAME_TOKEN							= "[Lead name]";
	public static final String			REFERRAL_LEAD_SUSPECT_EMAIL_TYPE_V2					= "lead_suspect_email_v2";
	public static final String			REFERRAL_IMAGE_TOKEN								= "[Referral Image]";
	public static final String			EMAIL_PREVIEW_TEXT_TOKEN							= "[EmailPreviewText]";
	
	public static final String			REFERRAL_LEADS_INDEX								= "referral_leads";
	public static final String			DOC_AS_UPSERT										= "doc_as_upsert";
	
	//TODO: create separate class for character constants
	public static final String			HYPHEN												= "-";
	public static final String			INSTAGRAM_REQUEST_TYPE								= "instagram";
	public static final String			CAMPAIGN_SERVICE									= "campaign";
	public static final String			BAZAARIFY_MASTER_HOST_NAME							= "bazzarify_master_host_name";
	public static final String			BAZAARIFY_MASTER_DB									= "bazaarify";			         	// bazaarify db name
	public static final String			CAMPAIGN_MASTER_DB									= "campaign";						// campaign db name
	public static final String			HOST_NAME											= "host";
	public static final String			DB_PORT												= "port";
	public static final String			DB_USERNAME											= "username";
	public static final String			DB_PASSWORD											= "password";
	public static final String			DB_NAME												= "dbname";
	public static final String			BAZAARIFY_MASTER_DB_PORT							= "bazzarify_port";
	public static final String			BAZAARIFY_MASTER_DB_USERNAME						= "bazzarify_db_username";
	public static final String			BAZAARIFY_MASTER_DB_PASSWORD						= "bazzarify_db_password";
	public static final String			BAZAARIFY_MASTER_DB_NAME							= "bazzarify_db_name";
	public static final String			CAMPAIGN_HOST_NAME									= "campaign_host_name";
	public static final String			CAMPAIGN_DB_PORT									= "campaign_db_port";
	public static final String			CAMPAIGN_USERNAME									= "campaign_username";
	public static final String			CAMPAIGN_PASSWORD									= "campaign_password";
	public static final String			CAMPAIGN_DB_NAME									= "campaign_db_name";
	public static final String			CAMPAIGN_JDBC_URL									= "*****************************************************";
	public static final String			BAZAARIFY_MASTER_JDBC_URL							= "*****************************************************&zeroDateTimeBehavior=convertToNull";
	public static final String			APPLE_REQUEST_TYPE									= "apple";
	public static final String			BAZAARIFY_SLAVE_JDBC_URL							= "*****************************************************&zeroDateTimeBehavior=convertToNull";
	public static final String			BAZAARIFY_SLAVE_HOST_NAME							= "bazzarify_slave_host_name";
	public static final String			BAZAARIFY_SLAVE_DB_NAME								= "bazzarify_slave_db_name";
	public static final String			BAZAARIFY_SLAVE_DB_PORT								= "bazzarify_slave_db_port";
	public static final String			BAZAARIFY_SLAVE_DB_USERNAME							= "bazzarify_slave_db_username";
	public static final String			BAZAARIFY_SLAVE_DB_PASSWORD							= "bazzarify_slave_db_password";
	public static final String			BAZAARIFY_SLAVE_DB									= "bazaarify_slave";
	public static final String			CAMPAIGN_SLAVE_JDBC_URL								= "*****************************************************";
	public static final String			CAMPAIGN_SLAVE_HOST_NAME							= "campaign_slave_host_name";
	public static final String			CAMPAIGN_SLAVE_DB_NAME								= "campaign_slave_db_name";
	public static final String			CAMPAIGN_SLAVE_DB_PORT								= "campaign_slave_db_port";
	public static final String			CAMPAIGN_SLAVE_DB_USERNAME							= "campaign_slave_db_username";
	public static final String			CAMPAIGN_SLAVE_DB_PASSWORD							= "campaign_slave_db_password";
	public static final String			CAMPAIGN_SLAVE_DB									= "campaign_slave";
	public static final String			CAMPAIGN_SMS_TYPE									= "campaign";
	public static final String			QUICK_SEND_SMS_SUB_TYPE								= "quick-send";
	public static final String			SOURCENAME_FIRST_LETTER_IMG_PREFIX_URI				= "sourcename_first_letter_img_prefix_uri";
	public static final String			SOURCENAME_FIRST_LETTER_IMG_SUFFIX_URI				= "sourcename_first_letter_img_suffix_uri";
	public static final String 			GOOGLE_REVIEW_GENERATION_URL_PREFIX 				= "https://search.google.com/local/writereview?placeid=";
	public static final String			APPOINTMENT_REMINDER_TYPE							= "appointment_reminder";
	public static final String			APPOINTMENT_RECALL_TYPE							    = "appointment_recall";
	public static final String			APPOINTMENT_FORM_TYPE							    = "appointment_form";
	
	public static final String			GOOGLE_CALENDAR_DATE_FORMAT							= "yyyyMMdd'T'HHmmss'Z'";
	public static final String			OUTLOOK_CALENDAR_DATE_FORMAT						= "yyyy-MM-dd'T'HH:mm:ss";
	public static final String			OUTLOOK_CALENDAR_DATE_FORMAT_UTC					= "yyyy-MM-dd'T'HH:mm:ss'Z'";
	
	public static final String			APPOINTMENT_STATUS_FILTER_NAME						= "Appointment status";
	public static final String			HOURS												= "hours";
	public static final String			DAYS												= "days";
	public static final String			DEFAULT_APPOINTMENT_REMINDER_AUTOMATION_NAME		= "Appointment reminder";
	public static final String			DEFAULT_APPOINTMENT_RECALL_AUTOMATION_NAME		    = "Appointment recall";
	public static final String			DEFAULT_APPOINTMENT_REMINDER_TRIGGER_EXPRESSION		= "1";
	public static final String			DEFAULT_SPECIALIST_LABEL							= "Specialist";
	
	public static final String			APPOINTMENT_POLLER_CAMPAIGN_TYPES_KEY				= "appointment_poller_campaign_types";
	public static final String			DEFAULT_APPOINTMENT_POLLER_CAMPAIGN_TYPES			= APPOINTMENT_REMINDER_TYPE;
	
	public static final int				TOTAL_HRS_IN_DAY									= 24;
	public static final int				TOTAL_DAYS_IN_WEEK									= 7;
	public static final int				TOTAL_DAYS_IN_MONTH									= 30;
	public static final String			DEFAULT_WIDGET_ID									= "0";
	public static final String			SOURCE_SMS											= "sms";

	public static final String			RECALL_EMAIL_BOOKING_CTA_TEXT_SCHEDULING_ENABLED	= "Book your appointment here or call us at [Business Phone].";
	public static final String			RECALL_EMAIL_BOOKING_CTA_TEXT_SCHEDULING_DISABLED	= "Call us at [Business Phone] to book your appointment.";
	public static final String			RECALL_SMS_BOOKING_CTA_TEXT_SCHEDULING_ENABLED		= "Please click here to book an appointment for your checkup.";
	public static final String			RECALL_SMS_BOOKING_CTA_TEXT_SCHEDULING_DISABLED		= "Please reply to this message or call our office at [Business Phone] to make an appointment. Thank you!";
	public static final String          MESSENGER_RR_SMS_TYPE                               = "messenger";
	public static final String			REFERRAL											= "Referral";
	public static final String			WORD_OF_MOUTH										= "Word of mouth";
	public static final String			REFERRAL_LEAD										= "Referral Lead";
	public static final String			WORD_OF_MOUTH_LEADS									= "Word of mouth Leads";
	public static final String 			DOT													= ".";

	public static final String			UTC_TIMEZONE										= "UTC";
	public static final String	        MANUAL_CAMPAIGN_TYPE							    = "scheduled-manual";
		
	public static final String      	CLICKED												= "clicked";
	public static final String			BOOKED												= "booked";
	public static final String			OPENED												= "opened";

	public static final String			LOCALE_CUSTOM_FIELD									= "language_preference_crm";

	public static final String          DOMAIN_SEPARATOR_IN_URL                             = "/";
	public static final String          URL_HTTP_STRING                                     = "http://";
	public static final String          URL_HTTPS_STRING                                    = "https://";
	public static final String			APPOINTMENT_DELETED									= "Assosciated appointment has been deleted";
	public static final String			RECALL_UPDATED_DELETED								= "Assosciated recall has been either updated or deleted";
	public static final String          REMINDER_SUBJECT_TOKEN_ALLOWED_CAMPAIGN_TYPES       = "review_request";
	
	// Appointment back-fill event constants
	public static final String			APPOINTMENT_BACKFILL_EVENT_DELAY_KEY				= "appointment_backfill_event_delay_in_minutes";
	public static final Integer			APPOINTMENT_BACKFILL_EVENT_DELAY_MINUTES			= 60;											// one hour
	public static final String			APPOINTMENT_BACKFILL_EVENT_OFFSET_KEY				= "appointment_backfill_event_offset_in_minutes";
	public static final Integer			APPOINTMENT_BACKFILL_EVENT_OFFSET_MINUTES			= 60;											// one hour
	public static final String			APPOINTMENT_BACKFILL_RETRY_COUNT_KEY				= "appointment_backfill_retry_count_limit";
	public static final Integer			APPOINTMENT_BACKFILL_RETRY_COUNT_LIMIT				= 2;
	public static final String			APPOINTMENT_BACKFILL_RETRY_EVENT_AEROSPIKE_TTL_KEY	= "appointment_backfill_retry_event_aerospike_ttl_sec";
	public static final Integer			APPOINTMENT_BACKFILL_RETRY_EVENT_AEROSPIKE_TTL_SEC	= 86400;										// one day
	
	// appointments pull API constants
	public static final String			APPOINTMENT_PULL_API_BATCH_SIZE_KEY					= "appointment_pull_api_batch_size";
	public static final Integer			APPOINTMENT_PULL_API_BATCH_SIZE						= 1000;											// pull 1000 appointments per API call
	
	// Appointment reminder communication restriction constants
	public static final String		REMINDER_COMM_RESTRICT_ENTERPRISE_IDS_CSV				= "appointment.reminder.comm.restrict.enterprise.ids.csv";
	public static final String		REMINDER_COMM_RESTRICT_ENTERPRISE_IDS_DEFAULT			= "703094";										// Home match enterprise id as default
	public static final String		REMINDER_COMM_RESTRICT_WINDOW_IN_SECOND					= "appointment.reminder.comm.restrict.window.in.second";
	public static final Integer		REMINDER_COMM_RESTRICT_WINDOW_DEFAULT					= 86400;

	public static final List<String>	APPOINTMENT_TYPES									= Arrays.asList("appointment_reminder", "appointment_recall", "appointment_form");
	
	public static final List<String> 	DAYS_OF_WEEK 										= Arrays.asList("Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday");
	
	// Out of the box default templates name and messages
	public static final String			KEY_NAME 											= "keyName";
	public static final String 			SMS_TEMPLATE_MESSAGE 								= "smsTemplateMessage";
	public static final String 			EMAIL_TEMPLATE_SUBJECT 								= "emailTemplateSubject";
	public static final String 			EMAIL_TEMPLATE_HEADING 								= "emailTemplateHeading";
	public static final String 			EMAIL_TEMPLATE_MESSAGE 								= "emailTemplateMessage";
	public static final String 			EMAIL_TEMPLATE_CUSTOM_HTML_ENABLED 					= "emailTemplateCustomHtmlEnabled";
	public static final String 			EMAIL_TEMPLATE_CUSTOM_HTML 							= "emailTemplateCustomHtml";
	public static final Boolean	        IS_GLOBAL_TEMPLATE					                = false;
	public static final String          SMS_TYPE                                            = "sms";
	public static final String          EMAIL_TYPE                                          = "email";
	public static final String	        EMAIL_AND_SMS							            = "email_and_sms";
	public static final String          DEEPLINK_WAR                                        = "war";
	public static final String          DEEPLINK_REVIEW_QR_CODE                             = "review_qr_code";
	public static final Integer         ZERO_VALUE                                          = 0;

	
	public static final String	        CAMPAIGN_REQUEST_EXTERNAL_SOURCES					= "facebook,google,instagram,apple,twitter";
	
	public static final String          RECURRING_EVENT_AUTOMATION_UPDATED                  = "recurring event automation updated";

	public static final Long	        EPOCHS_IN_A_DAY										= 86400000l;
	
	public static final long			EPOCHS_IN_TWO_HOURS									= 7200000l;	
	
	public static final String	        CAMPAIGN_DT_COMPLETABLE_FUTURE_TASK_EXECUTOR	    = "CampaignDTCompletableTaskExecutor";
	public static final String	        DT_MIGRATION_MOST_RECENT_TEMPLATE_FETCH	            = "dt_migration_mru_template_fetch";
	public static final String          DT_MIGRATION_AUDIT_INIT                             = "dt_migration_audit_init";
	public static final String          DT_MIGRATION_AUDIT_UPDATE                           = "dt_migration_audit_update";

	public static final String		    EMAIL_BLOCKLISTING_KEYWORD_SUBSTRING_CATEGORY				= "substring";
	public static final String			EMAIL_BLOCKLISTING_KEYWORD_PATTERN_CATEGORY					= "pattern";
	
	public static final String	        MODIFICATION_HISTORY_SAVE_CALL					    = "modification_history_for_save_api";
	
	public static final String	        MODIFICATION_HISTORY_STATUS_UPDATE_CALL			    = "modification_history_for_status_update_api";
	
	public static final String			MODIFICATION_HISTORY_DELETE_CALL					= "modification_history_for_delete_api";
	
	public static final Integer			SECONDS_IN_A_DAY									= 86400;
	
	public static final String			URL_SHORTENING_ERROR_REQUEST_RETRY_CACHE			= "rrExecutionURLShortenRetryCounter";
	
	public static final String			URL_SHORTEN_FAILURE_REQUEST_RETRY_LIMIT				= "rr.execution.retry.url.shorten.count.limit";
	
	public static final Integer			URL_SHORTEN_FAILURE_DEFAULT_REQUEST_RETRY_LIMIT		= 3;
	
	public static final String			URL_SHORTEN_ERROR_AEROSPIKE_CACHE_TTL				= "rr.execution.url.retry.event.aerospike.ttl.sec";
	
	public static final String			SIMPLE												= "simple";
	
	public static final String			URL_SHORTENING_RETRY_EXECUTION_DELAY				= "rr.execution.url.retry.event.delay";

	public static final String			URL_SHORTENING_RETRY_EXECUTION_DEFAULT_DELAY		= "30";																																					// in
																																																													// minutes
	public static final String			REFERRAL_WEBHOOKS_ENABLED_ACCOUNT_IDS				= "referral.webhooks.enabled.account.ids";
	
	public static final String			REFERRAL_REQUEST									= "referral";
	
	public static final String			DELIVERED_EVENT										= "delivered";

	public static final String          USER_ACCESS_CAMPAIGN                                = "campaign-access";
	
	public static final String			DEFAULT_FEEDBACK_NAME_LABEL							= "Your name";
	
	public static final String			DEFAULT_FEEBACK_EMAIL_LABEL							= "Your email";
	
	public static final String			DEFAULT_DIRECT_FEEDBACK_HEADING						= "Hi [Contact first name]<br />How did we do?";
	
	public static final String			DEFAULT_ONE_STAR_SUBTEXT							= "Hated it";
	
	public static final String			DEFAULT_TWO_STAR_SUBTEXT							= "Didn't like it";
	
	public static final String			DEFAULT_THREE_STAR_SUBTEXT							= "Liked it";
	
	public static final String			DEFAULT_FOUR_STAR_SUBTEXT							= "Really liked it";
	
	public static final String			DEFAULT_FIVE_STAR_SUBTEXT							= "Loved it";
	
	public static final String			DEFAULT_DIRECT_FEEDBACK_NEXT_BUTTON_TEXT			= "Next";
	
	public static final String			DEFAULT_DIRECT_FEEDBACK_POSITIVE_SENTIMENT_MESSAGE	= "Great! Please tell us in a few words how we did.";
	
	public static final String			DEFAULT_REFERRAL_LINK_LABEL							= "Share your referral link";
	
	public static final String			DEFAULT_REFERRAL_FIRST_NAME_LABEL					= "First name";
	
	public static final String			DEFAULT_REFERRAL_LAST_NAME_LABEL					= "Last name";
	
	public static final String			DEFAULT_REFERRAL_PHONE_LABEL						= "Phone number";
	
	public static final String			DEFAULT_REFERRAL_EMAIL_LABEL						= "Email";
			
	public static final String			DEFAULT_ASSISTED_BY_LABEL							= "Assisted By";
	
	public static final String			DEFAULT_REFERRAL_ZIP_CODE_LABEL						= "Zip code";
	
	public static final String          USER_ACCESS_SPLIT_CAMPAIGN                          = "split-campaign-access";
	
	public static final String			SPLIT												= "split";

	public static final String 			RESELLER_CAMPAIGN_WIDGET_REPORT_TYPE				= "analytics";
	
	public static final String 			CAMPAIGN_WIDGET_ES_INDEX_SHARD_COUNT				= "reseller.campaign.widget.es.index.shard.count";
		
	public static final Integer 		CAMPAIGN_WIDGET_ES_INDEX_DEFAULT_SHARD_COUNT		= 1;
	
	public static final String 			CAMPAIGN_WIDGET_ES_INDEX_REPLICA_COUNT				= "reseller.campaign.widget.es.index.replica.count";
	
	public static final Integer 		CAMPAIGN_WIDGET_ES_INDEX_DEFAULT_REPLCIA_COUNT		= 3;
	
	public static final String			CAMPAIGN_WIDGET_ES_INDEX_MAPPINGS					= "{\n" + "  \"properties\": {\n" + "    \"campaignId\": {\"type\": \"integer\"},\n"
			+ "    \"accountId\": {\"type\": \"integer\"},\n" + "    \"sentCount\": {\"type\": \"integer\"},\n" + "    \"deliveredCount\": {\"type\": \"integer\"},\n"
			+ "    \"openedCount\": {\"type\": \"integer\"},\n" + "    \"openedPercentage\": {\"type\": \"double\"},\n" + "    \"clickedCount\": {\"type\": \"integer\"},\n"
			+ "    \"clickedPercentage\": {\"type\": \"double\"}\n" + "  }\n" + "}";
	
	public static final String 			RESELLER_CAMPAIGN_WIDGET_CAMPAIGN_COUNT 			= "reseller.campaign.widget.campaign.count";
	
	public static final Integer 		RESELLER_CAMPAIGN_WIDGET_DEFAULT_CAMPAIGN_COUNT		= 20;
	
	public static final String			RESELLER_CAMPAIGN_WIDGET_INDEX						= "reseller_campaign_widget";
	
	public static final String          WROTE_A_REVIEW                                      = "Wrote a review";

	public static final String			API_URI_GET_PRODUCT_NAME					        = "v1/business/resellerBranding/info?businessId=";

	public static final Integer         DEFAULT_CHARACTER_LIMIT                             = 125;
	
	public static final Integer 		HIGH_CHARACTER_LIMIT		                        = 250;
	
	public static final String			GROUP_UNSUBSCRIBE									= "group_unsubscribe";

	public static final Integer	        CHROME_EXT_ID										= -3;
	
	public static final Integer	        QUICK_SEND_ID										= -4;
	
	public static final String	        QUICK_SEND											= "quick_send";
	
	public static final String	        CHROME_EXTENTION									= "chrome_extention";

	public static final String          FREE_TRIAL_ACCOUNT_LIMIT_REACHED                    = "FreeTrial Account limit reached";
	
	public static final String			INFLUENCE_RATE_ENABLED								= "CAMPAIGNS_IS_INFLUENCE_RATE_ENABLED";	// Core's feature flag of influence rate metrics.
	
	public static final String			INFLUENCE_WINDOW									= "CAMPAIGNS_INFLUENCE_CHECK_IN_WINDOW";	// Core's feature flag of check-in window for influence rate metrics.
	
	public static final String			INFLUENCE_DEFAULT_WINDOW							= "14"; 									// Default check-in window for influence rate metric, configurable at core end.
	
	public static final String			TRUE												= "true";
	
	public static final String			FALSE												= "false";

	public static final String          UNSUB_FB_KEYWORD                                    = "NOFB";
	
	public static final String          UNSUB_SERV_KEYWORD                                  = "NOSERV";
	
	public static final String          UNSUB_MKT_KEYWORD                                   = "NOMKT";
	
	public static final Integer 		NAME_CHARACTER_LIMIT		                        = 50;

	public static final String			STATUS_ACTIVE										= "active";
	
	public static final String			STATUS_INACTIVE										= "inactive";
	
	public static final String          FREE_TRIAL_INBOX_FAILURE_REASON                     = "campaign_limit_exceeded";
	
	public static final String			DATE_CUSTOM_FIELD_START_HOUR						= "date.custom.field.start.hour";

	
}
