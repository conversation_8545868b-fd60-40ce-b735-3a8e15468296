/**
 *
 */
package com.birdeye.campaign.constant;

/**
** File:         ErrorCodes.java
** Created:      3 Jan 2019
** Author:       puneetgupta
**
** This code is copyright (c) BirdEye Software India Pvt. Ltd.
**/
public enum ErrorCodes {

	INVALID_REQUEST(1001 , "Invalid request received"),
	INVALID_CAMPAIGN(1002 , "Invalid campaign"),
	INVALID_BUSINESS(1003 , "Invalid business"),
	INVALID_TEMPLATE(1004 , "Invalid template"),
	NOT_AUTHORIZED(1033 , "Not Authorized"),
	INVALID_ENTERPRISE(1005 , "Invalid enterprise"),
	INVALID_BUSINESSOPTION(1006 , "Invalid enterprise - no business option found"),
	INVALID_SMS_MESSAGE(1007, "Invalid SMS Message in template"),
	INVALID_QUESTION(1008, "Invalid question in template"),
	INVALID_SMS_MEDIA_URL(1009, "Invalid SMS Media URL"),
	INVALID_SMS_TEMPLATE_TYPE(1010, "Ivalid SMS Template"),
	MISSING_SMS_TEMPLATE_DATA(1011, "Missing template data"),
	UNABLE_TO_CONVERT_SMS_TEMPLATE_REQUEST(1012, "Unable to convert to SmsTemplateMessage"),
	INVALID_MMS_IMAGE_URL(1013, "Invalid MMS Image Url"),
	NO_TEMPLATE_FOUND(1014, "No template found of specified Id or defualt of specified type"),
	INVALID_ELASTIC_QUERY(1015 , "Error occured while preparing elastic query."),
	INVALID_SMS_NUMBER(1016, "Invalid SMS Number"),
	INVALID_CAMPAIGN_STATUS(1017, "Invalid Campaign Status"),
	NO_ENTERPRISE_CONFIG_SETTING(1018 , "No campaign account settings present"),
	NO_REQUEST_ID_TO_MIGRATE(1019 , "No requestIDs to migrate"),
	URL_SHORTEN_ERROR(1020, "Error while calling shorten url API"),
	TOO_MANY_REQUEST_TO_MIGRATE(1021 , "Too many request to migrate"),
	MISSING_CAMPAIGN_SUMMARY_DATA(1022, "missing campaign summary data"),
	ENCRYPTION_ERROR(1023, "error while encryption/decryption"),
	INVALID_TIME_ZONE_ERROR(1024, "Invalid time zone"),
	DUPLICATE_REFERRAL(3190,"Duplicate referral found with email/phone"),
	EXISTING_BUSINESS_USER_REFERRAL(2147,"referral found as existing business user"),
	NO_REFERRAL_CODE_TO_MIGRATE(1027, "no referral code present to migrate"),
	INVALID_FORM_URL(1028, "Invalid form url in template"),
	REFERRAL_DEFAULT_SETTINGS_NOT_FOUND(1040,"Referral Settings not found"),
	SERVER_ERROR(5000,"Server Error. Please try again."),
	INVALID_CUSTOMER_DETAILS(1041 , "Invalid customer details"),
	INVALID_EMPLOYEE_DETAILS(1042 , "Invalid employee details"),
	INVALID_LOCATION_DETAILS(1043 , "Invalid location details"),
	INVALID_PRIORITY_DETAILS(1044 , "Invalid priority details"),
	INVALID_REQUEST_TYPE(1045 , "Invalid request type"),
	CUSTOMER_GET_CREATE_ERROR(1046 , "Error while creating the customer and checkin"),
	INVALID_QUICK_SEND_REQUEST(1047 , "Invalid quick send request received"),
	DUPLICATE_CUSTOMER_CREATE_ERROR(1048 , "Another contact with same phone already present."),
	EXTERNAL_SERVICE_URL_NOT_FOUND(1049 , "External service resource url {0} not found."),
	EXTERNAL_SERVICE_UAVAILABLE(1050 , "External service {0} unavailable"),
	EXTERNAL_SERVICE_CONNECTION_REFUSED(1051 , "External service {0} connection refused"),
	NOT_ACTIVE_BUSINESS(1052 , "Given business {0} is in cancelled/suspended status."),
	INVALID_SURVEY_DETAILS(1053, "Invalid survey details"),
	CUSTOMER_ALREADY_MAPPED(5023, "Contact is mapped with a different location!"),
	AUTOMATION_RULE_CREATION_ERROR(5024, "Automation rule creation error"),
	ERROR_IN_FINDING_RESELLER_TEMPLATE(6001, "No reseller template found for this reseller id and type"),
	CAMPAIGN_DELETE_ERROR(5025, "Campaign can't be deleted its status should be in one of [0,2,3,4,5,6]"),
	ERROR_DEFAULT_RESELLER_TEMPLATE_ALREADY_EXISTS(5026,"Default reseller template already exists for this reseller"),
	ERROR_NOT_A_RESELLER_ACCOUNT(5028,"Error provided business id does not correspond to a reseller account"),
	ERROR_FINDING_DEFAULT_RESELLER_TEMPLATE(5027,"Default reseller template is not present for this seller"),
	ERROR_WHILE_FETCHING_RESELLER_TEMPLATE(5030,"Null values found in input while fetching reseller template"),
	ERROR_INVALID_REQUEST(5031,"Null values found in input"),
	ERROR_INVALID_TEMPLATE_ID(5029,"For the given resellerid, given template id does not match with the stored template id"),
	INVALID_TYPE(5032, "Invalid type inputted for the given api"),
	INVALID_TEMPLATE_DUPLICATE_REQUEST(1801,"invalid template duplicate request"),
	NO_VALID_SOURCE_BUSINESS_FOUND(1803 , "no valid source business found"),
	NO_VALID_DESTINATION_BUSINESS_FOUND(1804 , "no valid destination business found"),
	FAILED_TO_RETRIVE_SECRET(1098, "failed to retrieve secret from AWS SM"),
	FAILED_TO_UNMARSHAL_SECRET(1099, "failed to unmarshal secret"),
	NO_VALID_FAILURE_REASON_FOUND(1100,"no valid failure reason found for the given failure id"),
	OPERATION_NOT_ALLOWED(1358, "Operation Not allowed"),
	NO_VALID_APOINTMENT_FOUND(1401,"no valid appointment found for given id"),
	NO_VALID_CAMPAIGN_CONDITION(1402,"no valid campaign condition found"),
	INVALID_APPOINTMENT(1403,"invalid appointment id"),
	CAMPAIGN_SCHEDULING_CONFIG_NOT_MATCHED(1404,"campaign scheduling config not matched"),
	FUTURE_APPOINTMENT_BOOKED(1405,"future appointment booked"),
	NO_VALID_DUE_DATE_FOUND(1406,"no valid due date available"),
	INVALID_APPOINTMENT_STATUS(1407,"invalid appointment status"),
	INVALID_REVIEW_GENERATOIN_URL(2098, "Either invalid url or url without protocol sent"),
	REVIEW_GENERATION_DOMAIN_ALREADY_EXISTS(2090, "A review site with this domain has already been added."),
	REVIEW_GENERATION_SOURCE_NAME_ALREADY_EXISTS(2091, "A review site with this name has already been added for review generation."),
	REVIEW_GENERATION_SOURCE_NAME_BE_SUPPORTED(2097, "Site with this name is already included, please choose a different name."),
	REVIEW_GENERATION_SOURCE_BE_SUPPORTED(2092, "Good news! This is a supported source, please add using Review Monitoring."),
	INVALID_REVIEW_GENERATOIN_ID(2093, "No request found to edit!"),
	REVIEW_GENERATION_EDIT_DOMAIN_CHANGED(2094, "Changing domains while editing urls is not allowed"),
	REVIEW_GENERATION_ALREADY_EXISTS(2095, "Url already exists for business"),
	NO_REVIEW_GENERATION_DELETED(2096, "Could not delete review generation url"),
	REVIEW_GENERATION_NOT_PRESENT(1707,"Review Generation not found"),
	APPOINTMENT_RECALL_DELETED(1408,"appointment recall deleted"),
	INVALID_ES_INDEX(1501,"invalid ES index"),
	INVALID_TEMPLATE_TYPE(1600, "Invalid Template Type Given"),
	QR_MAPPING_DELETE_ERROR(1601, "Failed to delete QR source mapping"),
	INVALID_REQUEST_UPDATE_DEFAULT_TEMPLATE(1602, "Either Templates not found for given enterprise id ,template ids and type or default template flag not match"),
	ERROR_OCCURRED_WHILE_UPDATING_REQUEST(1603, "Error occurred while updating default and non default template"),
	ERROR_OCCURRED_WHILE_UPDATING_OFFSET(1604, "Error occurred while updating offset"),
	ERROR_OCCURRED_NO_BUSINESS_FOUND(1605, "Business List found is empty for preparing default template migration request"),
	INVALID_VIEW_RECIPIENT_REQUEST(1502, "invalid view recipient request received"),
	FORM_ALREADY_FILLED(1408,"form already filled"),
	FORM_URL_BLANK(1409, "form url is not present for the review request"),
	APPOINTMENT_TIME_PASSED(1410, "The appointment time is already passed for the review request"),
	QR_SLUG_MAPPING_ABSENT(1897, "No QR Config id present for slug"),
	APPOINTMENT_PULL_NULL_RESPONSE(1411, "Error occured in appointment pull API"),
	NO_VALID_APPOINTMENT_SCHEDULE_INFO(1412,"no valid appointment schedule info found"),
	INPUT_LIMIT_EXCEEDED(1413,"max input limit exceeeded"),
	ERROR_OCCURED_WHILE_SPLIT_CAMPAIGN_DELETION(1415, "Error occurred while split campaign deletion"),
	INVALID_SPLIT_CAMPAIGN_ID(1416, "Received invalid split campaign id"),
	INVALID_SPLIT_MAPPING(1417, "Invalid split campaign mappings found for given sub campaign id"),
	INVALID_REPORT_TYPE(1418, "Received invalid report type for split campaign"),
	PMS_SUPPORT_IS_ENABLED(1414, "Non-PMS request dropped: PMS support is enabled"),
	ERROR_OCCURRED_WHILE_UPDATING_STATUS(1419, "Error occurred while updating status for campaigns"),
	ERROR_OCCURRED_WHILE_CREATING_TEMPLATE(1420,"Error occurred while creating template"),
	UNKNOWN_ERROR(1421,"unknown error occurred"),
	
	SHORTEN_URL_RETRIABLE_ERROR(1422,"Error occurred while fetching shortened url"),
	INVALID_RETRY_TYPE(1423,"invalid retry type"),
	INVALID_REVIEWGEN_SOURCE(2097, "{0} is a restricted site and cannot be used for review generation."),
	ERROR_OCCURRED_EVALUATING_DIFF_FOR_CHANGE_LOG(1424,"Error occurred while evaluating diff for audit"),
	REVIEW_WRITTEN_CONDITION_REVIEWED_ERROR(2098, "Request dropped: Contact already reviewed"),
	REVIEW_WRITTEN_CONDITION_NO_REVIEW_ERROR(2099, "Request dropped: No review from contact"), 
	ERROR_OCCURED_CREATING_SMS_TEMPLATE(3001, "Error occurred while creating sms template"),
	ERROR_OCCURRED_FREE_TRIAL_LIMIT_EXHAUSTED(3002, "Error occurred as free trial limit reached"),
	INVALID_LEAD_SOURCE(3002, "Invalid lead source"),
	INVALID_LEAD_TYPE(3003, "Invalid lead type"),
	BE_SUPPORTED_REVIEW_SOURCE( 3004, "This source is supported by Birdeye. Please select it from the dropdown."),
	REVIEW_SOURCES_DOMAIN_MISMATCH(3005, "The site name and domain do not match. Please enter a valid combination"),
	REVIEW_GENERATION_SOURCE_NAME_TAKEN(3006, "This review site name is already linked to a different domain in your account."),
	ERROR_OCCURRED_INBOX_TEMPLATE_CONTENT_PREPARE(3008, "Exception occurred while preparing template data"),
	ERROR_CREATING_AI_TEMPLATE(3009, "Exception occurred while creating AI template");
	
	private final int value;
	
	private final String message;

	ErrorCodes(int value , String message) {
		this.value = value;
		this.message = message;
	}

	public int getValue() {
		return value;
	}
	
	public String getMessage() {
		return message;
	}

	public static ErrorCodes valueOf(int value) {
		for (ErrorCodes errorCodes : ErrorCodes.values()) {
			if (errorCodes.getValue() == value) {
				return errorCodes;
			}
		}
		return null;
	}
}
