/**
 * @file_name ExcelHeaders.java
 * @created_date 5 Apr 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.constant;

/**
 * @file_name ExcelHeaders.java
 * @created_date 5 Apr 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public abstract class ExcelHeaders {
	/*
	Location ID	Template ID	Email Subject Line	Heading	Message	Review Site 1	Review Site 2	Review Site 3	Feedback Message	Contact Us Button			Feedback Message	Send Reminder				Last Used	Can be deleted
	Show Contact us option	Message	Custom URL		Send Reminder Email	Email Subject	Send Reminder after	Number of reminder		
	*/
	public static final String	LOCATION_ID				= "Location ID";
	public static final String	TEMPLATE_ID				= "Template ID";
	public static final String	EMAIL_SUBJECT_LINE		= "Email Subject Line";
	public static final String	HEADER					= "Header";
	public static final String	MESSAGE					= "Message";
	public static final String	REVIEW_SITE_1			= "Review Site 1";
	public static final String	REVIEW_SITE_2			= "Review Site 2";
	public static final String	REVIEW_SITE_3			= "Review Site 3";
	public static final String	FEEDBACK_MESSAGE		= "Feedback Message";
	public static final String	SHOW_CONTACT_US_OPTION	= "Show Contact Us Option";
	public static final String	CONTACT_US_MESSAGE		= "Contact Us Message";
	public static final String	CONTACT_US_CUSTOM_URL	= "Contact Us Custom URL";
	
	public static final String	SEND_REMINDER_EMAIL		= "Send Reminder Email";
	public static final String	SEND_REMINDER_SUBJECT	= "Reminder Subject";
	public static final String	SEND_REMINDER_AFTER		= "Send Reminder After";
	public static final String	NO_OF_REMINDER			= "Number of Reminder";
	
	public static final String	LAST_USED				= "Last Used";
	public static final String	CAN_BE_DELETED			= "Can Be Deleted";
	
	public static final String	FEEDBACK_TYOE			= "Feedback Type";
	
	public static final String	CX_HEADER				= "CX Header";
	public static final String	CX_MESSAGE				= "CX Message";
	
	// WE SHOULD TRY STREAMED EXCEL WRITE TO SAVE MEMORY
	public static final String	RR_SHOW_PAGE	= "RR Show Page";
	public static final String	RR_HEADER		= "RR Header";
	public static final String	RR_MESSAGE		= "RR Message";
	
	public static final String	SMS_TEXT_MESSAGE	= "SMS Text Message";
	public static final String	INCLUDE_UNSUBSCRIBE	= "Include Unsubscribe Text In The Sms";
	public static final String	SEND_IMAGE			= "Send text with image";
	public static final String	IMAGE_URL			= "SMS image URL";
	
	
	public static final String	ANDROID_REVIEW_SITE_1			= "Android Review Site 1";
	public static final String	ANDROID_REVIEW_SITE_2			= "Android Review Site 2";
	public static final String	ANDROID_REVIEW_SITE_3			= "Android Review Site 3";
	
	public static final String	IOS_REVIEW_SITE_1			= "IOS Review Site 1";
	public static final String	IOS_REVIEW_SITE_2			= "IOS Review Site 2";
	public static final String	IOS_REVIEW_SITE_3			= "IOS Review Site 3";

}
