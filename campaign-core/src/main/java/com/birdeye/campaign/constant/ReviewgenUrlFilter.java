package com.birdeye.campaign.constant;



public enum ReviewgenUrlFilter {

	P("Present"), NP("NotPresent"), ALL("All");

	private String value;

	private ReviewgenUrlFilter(String value) {
		this.value = value;
	}
	
	public static ReviewgenUrlFilter getEnumVal(String key) {
		
		for(ReviewgenUrlFilter reviewgenFilter: ReviewgenUrlFilter.values()) {
			if(reviewgenFilter.toString().equalsIgnoreCase(key)) {
				return reviewgenFilter;
			}
		}
		return ALL; // default to all
	}
}
