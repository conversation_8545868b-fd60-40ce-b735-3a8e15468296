package com.birdeye.campaign.constant;

import java.text.MessageFormat;
import java.util.Arrays;

import com.birdeye.campaign.exception.RuleCreationException;

public enum OperandDataType {

	TEXT("text"), NUMBER("number"), DATE("date"), CURRENCY("currency"), YESNO("yesno"), TEXT_MULTI("text_multi"), NUMBER_SINGLE("number_single"), LIST_TEXT("list_text"), TIME("time");

	private String dataType;

	// this for performance as every time whenever we will call values() method it will call clone method which will hit the performance
	private static final OperandDataType[] dataTypes = values();

	private OperandDataType(String dataType) {
		this.dataType = dataType;
	}

	public static OperandDataType getOperatorDataType(String operandType) {
		return Arrays.stream(dataTypes).filter(opType -> opType.dataType.equalsIgnoreCase(operandType)).findFirst()
				.orElseThrow(() -> new RuleCreationException(MessageFormat.format("{0} operandType Not Found", operandType)));
	}
}
