package com.birdeye.campaign.constant;

/**
 * All the static constants used in any type of email/sms templates are kept in this class.
 * 
 * <AUTHOR>
 *
 */
public abstract class TemplateStaticTokens {
	
	private TemplateStaticTokens() {
	}
	
	public static final String	BUSINESS_NAME_TOKEN						= "[Business Name]";
	public static final String	BUSINESS_PHONE_TOKEN					= "[Business Phone]";
	public static final String	BUSINESS_EMAIL_TOKEN					= "[Business Email]";
	public static final String	BUSINESS_LOCATION_ALIAS_TOKEN			= "[Location alias]";
	public static final String	AVG_RATING_TOKEN						= "[AvgRating]";
	public static final String	REVIEW_COUNT_TOKEN						= "[Review Count]";
	public static final String	QUESTION_TOKEN							= "[Question]";
	public static final String	MESSAGE_TOKEN							= "[Message]";
	public static final String	NEGATIVE_BUTTON_LABEL_TOKEN				= "[Negative Button Label]";
	public static final String	POSITIVE_BUTTON_LABEL_TOKEN				= "[Positive Button Label]";
	public static final String	PRODUCT_NAME_TOKEN						= "[Product Name]";
	public static final String	BUSINESS_ADDRESS_INLINE_TOKEN			= "[Business Address Inline]";
	public static final String	BUSINESS_ADDRESS_INLINE_TOKEN_ENCODED	= "[Business Address Inline Encoded]";
	public static final String	LOCATION_ADDRESS_TOKEN					= "[Location address]";
	public static final String	CONTACT_FIRST_NAME_TOKEN				= "[Contact first name]";
	public static final String	CONTACT_LAST_NAME_TOKEN					= "[Contact last name]";
	public static final String	EMPLOYEE_NAME_TOKEN						= "[EmployeeName]";
	public static final String	REFERRAL_CODE_TOKEN						= "[Referral code]";				// referrer code for all (TMX, non-TMX)
	public static final String	REFERRER_NAME_TOKEN						= "[Referrer name]";				// contact full name
	public static final String	LOGO_IMAGE_TOKEN						= "[Logo Image]";
	public static final String	TEXT_COLOR_TOKEN						= "[TextColor]";
	public static final String	BG_COLOR_TOKEN							= "[BGColor]";
	public static final String	REFEREE_FIRST_NAME_TOKEN				= "[Referee first name]";
	public static final String	LOCATION_PHONE_TOKEN					= "[Location phone]";
	public static final String	LOCATION_NAME_TOKEN						= "[Location name]";
	public static final String	PRODUCT_NAME_INC_TOKEN					= "[Product Name Inc]";
	public static final String	CUSTOMER_NAME_TOKEN						= "[Customer Name]";
	public static final String	CUSTOMER_NUMBER_TOKEN					= "[Customer Number]";
	public static final String	BRANDING_IMAGE_TOKEN					= "[Branding Image]";
	public static final String	BRANDING_HEADER_COLOR_TOKEN				= "[Header Color]";
	public static final String	CONTACT_PHONE_NUMER						= "[Contact Phone Number]";
	public static final String	EMAIL_SIGNATURE							= "[Signature]";
	public static final String	EMAIL_SUBJECT							= "emailSubject";
	public static final String	EMAIL_HEADING							= "[Heading]";
	public static final String	PRIVACY_URL								= "[privacyPolicyURL]";
	public static final String	TERMS_AND_CONDITIONS_URL				= "[TermsOfServiceURL]";
	public static final String	COMMENDED_LOGO_START_TOKEN				= "<!-- [Logo Image]";
	public static final String	COMMENDED_LOGO_END_TOKEN				= "[Logo Image] -->";
	
	// appointment reminder template tokens
	public static final String	APPOINTMENT_TIME						= "[Appointment Time]";
	public static final String	APPOINTMENT_DATE						= "[Appointment Date]";
	public static final String	SPECIALIST_NAME							= "[Specialist Name]";
	public static final String	PATIENT_FIRST_NAME						= "[Patient First Name]";
	public static final String	PATIENT_LAST_NAME						= "[Patient Last Name]";
	public static final String	APPOINTMENT_TYPE						= "[Appointment Type]";
	public static final String	CONTACT_EMAIL							= "[Contact Email]";
	public static final String	SPECIALIST_LABEL						= "[Specialist Label]";
	public static final String	CONFIRM_BUTTON_FLAG						= "[showConfirmButton]";
	public static final String	APPOINTMENT_START_DATE_TIME				= "[startDate]";
	public static final String	LATITUDE								= "[latitude]";
	public static final String	LONGITUDE								= "[longitude]";
	public static final String	SERVICE_NAME							= "[serviceName]";
	public static final String	MAP										= "[map]";
	public static final String	GOOGLE_CALENDAR_START_TIME				= "[googleCalendarStartTime]";
	public static final String	GOOGLE_CALENDAR_START_TIME_ENCODED		= "[googleCalendarStartTimeEncoded]";
	public static final String	GOOGLE_CALENDAR_END_TIME				= "[googleCalendarEndTime]";
	public static final String	GOOGLE_CALENDAR_END_TIME_ENCODED		= "[googleCalendarEndTimeEncoded]";
	public static final String	CALENDAR_SUBJECT						= "[calendarSubject]";
	public static final String	CALENDAR_SUBJECT_ENCODED				= "[calendarSubjectEncoded]";
	public static final String	CALENDAR_DESCRIPTION					= "[calendarDescription]";
	public static final String	CALENDAR_DESCRIPTION_ENCODED			= "[calendarDescriptionEncoded]";
	public static final String	OUTLOOK_CALENDAR_START_TIME				= "[outlookCalendarStartTime]";
	public static final String	OUTLOOK_CALENDAR_START_TIME_ENCODED		= "[outlookCalendarStartTimeEncoded]";
	public static final String	CANCEL_OR_RESCHEDULE_BUTTON_LINK		= "[cancelOrRescheduleUrl]";
	public static final String	CONFIRMATION_URL						= "[confirmationUrl]";
	public static final String	COMMENTED_MAPS_URL_START_TOKEN			= "<!-- [latitude] && [longitude] && [showLocationInfo]";
	public static final String	COMMENTED_MAPS_URL_END_TOKEN			= "[latitude] && [longitude] && [showLocationInfo] -->";
	public static final String	COMMENTED_CONFIRM_BUTTON_START_TOKEN	= "<!-- [showConfirmButton]";
	public static final String	COMMENTED_CONFIRM_BUTTON_END_TOKEN		= "[showConfirmButton] -->";
	public static final String	APPLICABLE_CTAS							= "[Applicable CTAs]";				// for appointment reminder texts

	public static final String	LAST_VISIT_DATE							= "[Last Visit Date]";
	public static final String	DAYS_OVERDUE							= "[Days Overdue]";
	public static final String	SPECIALIST_FIRST_NAME					= "[Specialist First Name]";
	public static final String	SPECIALIST_LAST_NAME					= "[Specialist Last Name]";
	public static final String	RECALL_TYPE								= "[Recall type]";
	public static final String	BOOKING_URL								= "[bookingUrl]";

	public static final String	HIDE_BEHALF								= "[Hide Behalf]";
	public static final String	SERVICE_AREA_PROVIDER_START_TOKEN		= "<!-- serviceAreaProvider";
	public static final String	SERVICE_AREA_PROVIDER_END_TOKEN			= "serviceAreaProvider -->";
	public static final String	NOT_SERVICE_AREA_PROVIDER_START_TOKEN	= "<!-- noServiceAreaProvider";
	public static final String	NOT_SERVICE_AREA_PROVIDER_END_TOKEN		= "noServiceAreaProvider -->";

	public static final String	APPOINTMENT_TYPE_START_TOKEN			= "<!-- [appointmentTypeAvailable]";
	public static final String	APPOINTMENT_TYPE_END_TOKEN				= "[appointmentTypeAvailable] -->";
	public static final String	ACTION_CTAS								= "[Action CTAs]";
	
	public static final String	BOOK_APPOINTMENT_BUTTON_START_TOKEN		= "<!-- bookAppointmentBtn";
	public static final String	BOOK_APPOINTMENT_BUTTON_END_TOKEN		= "bookAppointmentBtn -->";
	public static final String	BOOK_APPOINTMENT_CTA_TOKEN				= "[Book Appointment CTA]";	
	public static final String	COMMENTED_ACTION_CTA_START_TOKEN		= "<!-- ActionCTA";
	public static final String	COMMENTED_ACTION_CTA_END_TOKEN			= "ActionCTA -->";
	public static final String	APPOINTMENT_FORM_URL_TOKEN				= "[Form Url]";
	public static final String	FORM_BUTTON_TEXT_TOKEN					= "[Form Button Text]";
	public static final String	FORM_BUTTON_COLOR_TOKEN					= "[Form Button Color]";
	public static final String	FORM_BUTTON_TEXT_COLOR_TOKEN			= "[Form Button Text Color]";
	public static final String	RATING_AND_COUNT_TOKEN					= "[RatingAndCount]";
	public static final String	DAY_OF_THE_WEEK							= "[Day of the week]";
	public static final String	APPOINTMENT_LINK						= "[Appointment link]";
	public static final String	APPOINTMENT_FORM_LINK					= "[Appointment form link]";
	
	//https://birdeye.atlassian.net/browse/BIRD-60846
	public static final String	BIRDEYE_MICROSITE_URL				    = "[Birdeye microsite URL]";
	public static final String	GOOGLE_PROFILE_URL						= "[Google profile URL]";
	public static final String	FACEBOOK_PROFILE_URL					= "[Facebook profile URL]";
	public static final String	TWITTER_PROFILE_URL						= "[Twitter profile URL]";
	public static final String	YOUTUBE_PROFILE_URL						= "[Youtube profile URL]";
	public static final String	LINKEDIN_PROFILE_URL					= "[LinkedIn profile URL]";
	public static final String	INSTAGARAM_PROFILE_URL					= "[Instagram profile URL]";
	public static final String	PINTEREST_PROFILE_URL					= "[Pinterest profile URL]";
	public static final String	RESERVATION_LINK						= "[Reservation link]";
	public static final String	MENU_LINK								= "[Menu link]";
	public static final String	ORDER_AHEAD_LINK						= "[Order ahead link]";
	public static final String	ANDROID_APP_URL							= "[Android App URL]";
	public static final String	IOS_APP_URL								= "[IOS App URL]";
	
	public static final String	YOUR_FIRST_NAME							= "[Your first name]";
	public static final String	YOUR_LAST_NAME							= "[Your last name]";
	
	public static final String	REFERRAL_LINK_LABEL						= "[Referral Link Label]";
	
	public static final String	GOOGLE_MAP_URL							= "[Google map URL]";
	
	public static final String	OUTBOUND_TEXT_URL							= "[URL]";
	
	public static final String  WEBSITE_URL 							= "[Website URL]";
}
