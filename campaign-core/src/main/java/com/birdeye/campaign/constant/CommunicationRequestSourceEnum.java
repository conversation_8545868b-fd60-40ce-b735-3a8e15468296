package com.birdeye.campaign.constant;

public enum CommunicationRequestSourceEnum {
	CAMPAIGN("campaign"), INBOX("inbox"), QUICK_SEND("quick-send"), BROWSER_EXTENSION_QUICK_SEND("browser-extension-quick-send"), EXTERNAL_REQUEST("external-request"), INBOX_GET_CONTENT("inbox-get-template-content");
	
	private String type;
	
	private CommunicationRequestSourceEnum(String type) {
		this.type = type;
	}
	
	public String getType() {
		return type;
	}
}
