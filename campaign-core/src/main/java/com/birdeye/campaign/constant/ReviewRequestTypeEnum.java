package com.birdeye.campaign.constant;

import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 */

public enum ReviewRequestTypeEnum {
	REVIEW_REQUEST("review_request"), 
	PROMOTIONAL("promotional"), 
	QUICK_SEND_REQUEST("quick_send"),
	APPOINTMENT_REMINDER("appointment_reminder");
	
	private String	type;
	
	private ReviewRequestTypeEnum(String type) {
		this.type = type;
	}
	
	public String getType() {
		return type;
	}
	
	
	public static ReviewRequestTypeEnum getReviewRequestTypeEnum(String rrType) {
		ReviewRequestTypeEnum rrTypeEnum = null;
		for (ReviewRequestTypeEnum statusEnum : ReviewRequestTypeEnum.values()) {
			if (StringUtils.equalsIgnoreCase(statusEnum.getType(), rrType)) {
				rrTypeEnum = statusEnum;
				break;
			}
		}
		return rrTypeEnum;
	}
}