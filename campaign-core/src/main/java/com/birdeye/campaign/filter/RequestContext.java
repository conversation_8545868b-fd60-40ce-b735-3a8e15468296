/**
 * @file_name RequestContext.java
 * @created_date 25 Jan 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.filter;

import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.platform.entity.User;

/**
 * 
 * Lightweight threadlocal to contain business data
 * 
 * 
 * @file_name RequestContext.java
 * @created_date 25 Jan 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public class RequestContext {
	
	private static ThreadLocal<RequestContext>	ctx	= new ThreadLocal<RequestContext>();
	
	private BusinessEnterpriseEntity			business;
	private User								user;
	
	public static RequestContext current() {
		RequestContext requestCtx = ctx.get();
		if (requestCtx == null) {
			requestCtx = new RequestContext();
			ctx.set(requestCtx);
		}
		return requestCtx;
	}
	
	public static void destroy() {
		ctx.remove();
	}
	
	public BusinessEnterpriseEntity getBusiness() {
		return business;
	}
	
	public void setBusiness(BusinessEnterpriseEntity business) {
		this.business = business;
	}
	
	public User getUser() { 
		return user;
	}
	
	public void setUser(User user) {
		this.user = user;
	}
	
}
