package com.birdeye.campaign.exception;

import com.birdeye.campaign.constant.ErrorCodes;

public class ReferralPublicFormException extends RuntimeException{

	/**
	 * 
	 */
	private static final long serialVersionUID = 691722494666784722L;

	
	
	private final ErrorCodes	code;
	
	public ReferralPublicFormException(ErrorCodes code, String message) {
		super(message);
		this.code = code;
	}
	
	public int getCode() {
		if (code != null)
			return code.getValue();
		
		return 0;
	}

}
