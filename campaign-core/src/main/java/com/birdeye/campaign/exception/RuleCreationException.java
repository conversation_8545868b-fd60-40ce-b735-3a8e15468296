package com.birdeye.campaign.exception;

import com.birdeye.campaign.constant.ErrorCodes;

public class RuleCreationException extends RuntimeException {

	private static final long serialVersionUID = -8710281222109869789L;
	private final ErrorCodes			code;

	public RuleCreationException(String message) {
		super(message);
		code=ErrorCodes.AUTOMATION_RULE_CREATION_ERROR;
	}

	/**
	 * @return the code
	 */
	public ErrorCodes getCode() {
		return code;
	}
	
	
}
