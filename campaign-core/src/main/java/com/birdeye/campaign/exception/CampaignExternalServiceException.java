package com.birdeye.campaign.exception;

import com.birdeye.campaign.constant.ErrorCodes;

/**
 * 
 * <AUTHOR>
 *
 */
public class CampaignExternalServiceException extends RuntimeException {
	
	/**
	 *
	 */
	private static final long	serialVersionUID	= 554840459459431473L;
	private final ErrorCodes			code;
	

	public ErrorCodes getCode() {
		return code;
	}

	public CampaignExternalServiceException(ErrorCodes code, String message) {
		super(message);
		this.code = code;
	}
	
	public CampaignExternalServiceException(ErrorCodes code) 
	{
		this.code = code;
	}

}