package com.birdeye.campaign.exception;

import com.birdeye.campaign.constant.ErrorCodes;

/**
 * 
 * <AUTHOR>
 *
 */
public class CampaignQuickSendException extends RuntimeException {
	
	/**
	 *
	 */
	private static final long	serialVersionUID	= 554840459459431473L;
	private final ErrorCodes			code;
	

	public ErrorCodes getCode() {
		return code;
	}

	public CampaignQuickSendException(ErrorCodes code, String message) {
		super(message);
		this.code = code;
	}
	
	public CampaignQuickSendException(ErrorCodes code) 
	{
		this.code = code;
	}

}