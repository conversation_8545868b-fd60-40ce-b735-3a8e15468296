package com.birdeye.campaign.exception;

import com.birdeye.campaign.constant.ErrorCodes;

/**
 ** File:         NexusException.java
 ** Created:      15 May 2018
 ** Author:       sahilarora
 **
 ** This code is copyright (c) BirdEye Software India Pvt. Ltd.
 **/
public class CampaignException extends RuntimeException {
	
	/**
	 *
	 */
	private static final long	serialVersionUID	= 554840459459431473L;
	private final ErrorCodes			code;
	

	public ErrorCodes getCode() {
		return code;
	}

	public CampaignException(ErrorCodes code, String message) {
		super(message);
		this.code = code;
	}
	
	public CampaignException(ErrorCodes code) 
	{
		this.code = code;
	}

}
