package com.birdeye.campaign.exception;

import com.birdeye.campaign.constant.ErrorCodes;

/**
 ** File: AppointmentReminderForChainingException.java
 ** Created: 25 May 2023
 ** Author: sahilarora
 **
 ** This code is copyright (c) BirdEye Software India Pvt. Ltd.
 **/
public class AppointmentReminderForChainingException extends RuntimeException {
	
	private static final long	serialVersionUID	= 8779584680440100309L;
	
	private final ErrorCodes	code;
	
	public ErrorCodes getCode() {
		return code;
	}
	
	public AppointmentReminderForChainingException(ErrorCodes code, String message) {
		super(message);
		this.code = code;
	}
	
	public AppointmentReminderForChainingException(ErrorCodes code) {
		this.code = code;
	}
	
}
