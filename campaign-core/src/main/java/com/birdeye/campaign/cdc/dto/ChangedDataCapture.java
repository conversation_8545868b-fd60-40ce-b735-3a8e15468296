package com.birdeye.campaign.cdc.dto;

import java.io.Serializable;
import java.util.Map;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class ChangedDataCapture implements Serializable {
	
	private static final long	serialVersionUID	= 6938056383467497982L;
	
	private Map<String, Object>	dataBefore;
	private Map<String, Object>	dataAfter;
	
	/**
	 * @return the dataBefore
	 */
	public Map<String, Object> getDataBefore() {
		return dataBefore;
	}
	
	/**
	 * @param dataBefore
	 *            the dataBefore to set
	 */
	public void setDataBefore(Map<String, Object> dataBefore) {
		this.dataBefore = dataBefore;
	}
	
	/**
	 * @return the dataAfter
	 */
	public Map<String, Object> getDataAfter() {
		return dataAfter;
	}
	
	/**
	 * @param dataAfter
	 *            the dataAfter to set
	 */
	public void setDataAfter(Map<String, Object> dataAfter) {
		this.dataAfter = dataAfter;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
