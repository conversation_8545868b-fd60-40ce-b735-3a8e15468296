package com.birdeye.campaign.cdc.dto;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;

@JsonDeserialize(builder = AllCampaignDataDTO.AllCampaignDataDTOBuilder.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class AllCampaignDataDTO implements Serializable {
	
	private static final long			serialVersionUID	= 887961129878401358L;
	
	private CampaignDTO					campaignEntityData;
	private CampaignConditionDTO		campaignConditionEntityData;
	private DripCampaignConditionDTO	dripCampaignConditionEntityData;
	
	private AllCampaignDataDTO(AllCampaignDataDTOBuilder builder) {
		this.campaignEntityData = builder.campaignEntityData;
		this.campaignConditionEntityData = builder.campaignConditionEntityData;
		this.dripCampaignConditionEntityData = builder.dripCampaignConditionEntityData;
	}
	
	public static AllCampaignDataDTOBuilder builder() {
		return new AllCampaignDataDTOBuilder();
	}
	
	@JsonPOJOBuilder
	public static final class AllCampaignDataDTOBuilder {
		private CampaignDTO					campaignEntityData;
		private CampaignConditionDTO		campaignConditionEntityData;
		private DripCampaignConditionDTO	dripCampaignConditionEntityData;
		
		public AllCampaignDataDTOBuilder withCampaignEntityData(CampaignDTO campaignEntityData) {
			this.campaignEntityData = campaignEntityData;
			return this;
		}
		
		public AllCampaignDataDTOBuilder withCampaignConditionEntityData(CampaignConditionDTO campaignConditionEntityData) {
			this.campaignConditionEntityData = campaignConditionEntityData;
			return this;
		}
		
		public AllCampaignDataDTOBuilder withDripCampaignConditionEntityData(DripCampaignConditionDTO dripCampaignConditionEntityData) {
			this.dripCampaignConditionEntityData = dripCampaignConditionEntityData;
			return this;
		}
		
		public AllCampaignDataDTO build() {
			return new AllCampaignDataDTO(this);
		}
	}
	
	/**
	 * @return the campaignEntityData
	 */
	public CampaignDTO getCampaignEntityData() {
		return campaignEntityData;
	}
	
	/**
	 * @return the campaignConditionEntityData
	 */
	public CampaignConditionDTO getCampaignConditionEntityData() {
		return campaignConditionEntityData;
	}
	
	/**
	 * @return the dripCampaignConditionEntityData
	 */
	public DripCampaignConditionDTO getDripCampaignConditionEntityData() {
		return dripCampaignConditionEntityData;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
