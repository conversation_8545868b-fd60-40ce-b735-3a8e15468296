package com.birdeye.campaign.cdc.dto;

import java.io.Serializable;
import java.util.Date;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;

/**
 * Data-Transfer-Object For 'Campaign' Entity.
 * Used for the purpose of campaign modification auditing.
 * Entity's attributes with large values not to be included here. E.g: customerIds, appointmentIds
 * 
 * <AUTHOR>
 */
@JsonDeserialize(builder = CampaignDTO.CampaignDTOBuilder.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class CampaignDTO implements Serializable {
	
	private static final long	serialVersionUID	= 1478327445501439798L;
	
	private Integer				id;
	private String				type;
	private Date				startAt;
	private Date				endAt;
	private Integer				businessId;
	private Integer				enterpriseId;
	private Integer				surveyId;
	private Integer				createdBy;
	private String				name;
	private Integer				status;
	private Integer				priorityOrder;
	private Integer				templateId;
	private Integer				resellerId;
	private Integer				isDeleted;
	private Integer				customerCount;
	private Integer				smsTemplateId;
	private String				priority;
	private String				runType;
	private String				reminderSubject;
	private Integer				reminderCount;
	private Integer				reminderFrequency;
	private Integer				sendReminder;
	private Integer				isDefault;
	private Integer				schedule;
	private String				schedulingType;
	private Integer				schedulingInHours;
	private Integer				isMessengerCampaign;
	private String				triggerType;
	private Integer				bypassCommRestriction;
	private Integer				skipFutureAppointment;
	private Integer				isSplitCampaign;
	private Integer				isAppointmentTabCampaign;
	private Integer 			surveyCommFrequency;
	
	private CampaignDTO(CampaignDTOBuilder builder) {
		this.id = builder.id;
		this.type = builder.type;
		this.startAt = builder.startAt;
		this.endAt = builder.endAt;
		this.businessId = builder.businessId;
		this.enterpriseId = builder.enterpriseId;
		this.surveyId = builder.surveyId;
		this.createdBy = builder.createdBy;
		this.name = builder.name;
		this.status = builder.status;
		this.priorityOrder = builder.priorityOrder;
		this.templateId = builder.templateId;
		this.resellerId = builder.resellerId;
		this.isDeleted = builder.isDeleted;
		this.customerCount = builder.customerCount;
		this.smsTemplateId = builder.smsTemplateId;
		this.priority = builder.priority;
		this.runType = builder.runType;
		this.reminderSubject = builder.reminderSubject;
		this.reminderCount = builder.reminderCount;
		this.reminderFrequency = builder.reminderFrequency;
		this.sendReminder = builder.sendReminder;
		this.isDefault = builder.isDefault;
		this.schedule = builder.schedule;
		this.schedulingType = builder.schedulingType;
		this.schedulingInHours = builder.schedulingInHours;
		this.isMessengerCampaign = builder.isMessengerCampaign;
		this.triggerType = builder.triggerType;
		this.bypassCommRestriction = builder.bypassCommRestriction;
		this.skipFutureAppointment = builder.skipFutureAppointment;
		this.isSplitCampaign = builder.isSplitCampaign;
		this.isAppointmentTabCampaign = builder.isAppointmentTabCampaign;
		this.surveyCommFrequency = builder.surveyCommFrequency;
	}
	
	public static CampaignDTOBuilder builder() {
		return new CampaignDTOBuilder();
	}
	
	@JsonPOJOBuilder
	public static final class CampaignDTOBuilder {
		private Integer	id;
		private String	type;
		private Date	startAt;
		private Date	endAt;
		private Integer	businessId;
		private Integer	enterpriseId;
		private Integer	surveyId;
		private Integer	createdBy;
		private String	name;
		private Integer	status;
		private Integer	priorityOrder;
		private Integer	templateId;
		private Integer	resellerId;
		private Integer	isDeleted;
		private Integer	customerCount;
		private Integer	smsTemplateId;
		private String	priority;
		private String	runType;
		private String	reminderSubject;
		private Integer	reminderCount;
		private Integer	reminderFrequency;
		private Integer	sendReminder;
		private Integer	isDefault;
		private Integer	schedule;
		private String	schedulingType;
		private Integer	schedulingInHours;
		private Integer	isMessengerCampaign;
		private String	triggerType;
		private Integer	bypassCommRestriction;
		private Integer	skipFutureAppointment;
		private Integer	isSplitCampaign;
		private Integer	isAppointmentTabCampaign;
		private Integer surveyCommFrequency;
		
		public CampaignDTOBuilder withId(Integer campaignId) {
			this.id = campaignId;
			return this;
		}
		
		public CampaignDTOBuilder withType(String campaignType) {
			this.type = campaignType;
			return this;
		}
		
		public CampaignDTOBuilder withStartAt(Date startAt) {
			this.startAt = startAt;
			return this;
		}
		
		public CampaignDTOBuilder withEndAt(Date endAt) {
			this.endAt = endAt;
			return this;
		}
		
		public CampaignDTOBuilder withBusinessId(Integer businessId) {
			this.businessId = businessId;
			return this;
		}
		
		public CampaignDTOBuilder withEnterpriseId(Integer enterpriseId) {
			this.enterpriseId = enterpriseId;
			return this;
		}
		
		public CampaignDTOBuilder withSurveyId(Integer surveyId) {
			this.surveyId = surveyId;
			return this;
		}
		
		public CampaignDTOBuilder withCreatedBy(Integer createdBy) {
			this.createdBy = createdBy;
			return this;
		}
		
		public CampaignDTOBuilder withName(String campaignName) {
			this.name = campaignName;
			return this;
		}
		
		public CampaignDTOBuilder withStatus(Integer status) {
			this.status = status;
			return this;
		}
		
		public CampaignDTOBuilder withPriorityOrder(Integer priorityOrder) {
			this.priorityOrder = priorityOrder;
			return this;
		}
		
		public CampaignDTOBuilder withTemplateId(Integer templateId) {
			this.templateId = templateId;
			return this;
		}
		
		public CampaignDTOBuilder withResellerId(Integer resellerId) {
			this.resellerId = resellerId;
			return this;
		}
		
		public CampaignDTOBuilder withIsDeleted(Integer isDeleted) {
			this.isDeleted = isDeleted;
			return this;
		}
		
		public CampaignDTOBuilder withCustomerCount(Integer customerCount) {
			this.customerCount = customerCount;
			return this;
		}
		
		public CampaignDTOBuilder withSmsTemplateId(Integer smsTemplateId) {
			this.smsTemplateId = smsTemplateId;
			return this;
		}
		
		public CampaignDTOBuilder withPriority(String priority) {
			this.priority = priority;
			return this;
		}
		
		public CampaignDTOBuilder withRunType(String runType) {
			this.runType = runType;
			return this;
		}
		
		public CampaignDTOBuilder withReminderSubject(String reminderSubject) {
			this.reminderSubject = reminderSubject;
			return this;
		}
		
		public CampaignDTOBuilder withReminderCount(Integer reminderCount) {
			this.reminderCount = reminderCount;
			return this;
		}
		
		public CampaignDTOBuilder withReminderFrequency(Integer reminderFrequency) {
			this.reminderFrequency = reminderFrequency;
			return this;
		}
		
		public CampaignDTOBuilder withSendReminder(Integer sendReminder) {
			this.sendReminder = sendReminder;
			return this;
		}
		
		public CampaignDTOBuilder withIsDefault(Integer isDefault) {
			this.isDefault = isDefault;
			return this;
		}
		
		public CampaignDTOBuilder withSchedule(Integer schedule) {
			this.schedule = schedule;
			return this;
		}
		
		public CampaignDTOBuilder withSchedulingType(String schedulingType) {
			this.schedulingType = schedulingType;
			return this;
		}
		
		public CampaignDTOBuilder withSchedulingInHours(Integer schedulingInHours) {
			this.schedulingInHours = schedulingInHours;
			return this;
		}
		
		public CampaignDTOBuilder withIsMessengerCampaign(Integer isMessengerCampaign) {
			this.isMessengerCampaign = isMessengerCampaign;
			return this;
		}
		
		public CampaignDTOBuilder withTriggerType(String triggerType) {
			this.triggerType = triggerType;
			return this;
		}
		
		public CampaignDTOBuilder withBypassCommRestriction(Integer bypassCommRestriction) {
			this.bypassCommRestriction = bypassCommRestriction;
			return this;
		}
		
		public CampaignDTOBuilder withSkipFutureAppointment(Integer skipFutureAppointment) {
			this.skipFutureAppointment = skipFutureAppointment;
			return this;
		}
		
		public CampaignDTOBuilder withIsSplitCampaign(Integer isSplitCampaign) {
			this.isSplitCampaign = isSplitCampaign;
			return this;
		}
		
		public CampaignDTOBuilder withIsAppointmentTabCampaign(Integer isAppointmentTabCampaign) {
			this.isAppointmentTabCampaign = isAppointmentTabCampaign;
			return this;
		}
		
		public CampaignDTOBuilder withSurveyCommFrequency(Integer surveyCommFrequency) {
			this.surveyCommFrequency = surveyCommFrequency;
			return this;
		}
		
		public CampaignDTO build() {
			return new CampaignDTO(this);
		}
	}
	
	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}
	
	/**
	 * @return the type
	 */
	public String getType() {
		return type;
	}
	
	/**
	 * @return the startAt
	 */
	public Date getStartAt() {
		return startAt;
	}
	
	/**
	 * @return the endAt
	 */
	public Date getEndAt() {
		return endAt;
	}
	
	/**
	 * @return the businessId
	 */
	public Integer getBusinessId() {
		return businessId;
	}
	
	/**
	 * @return the enterpriseId
	 */
	public Integer getEnterpriseId() {
		return enterpriseId;
	}
	
	/**
	 * @return the surveyId
	 */
	public Integer getSurveyId() {
		return surveyId;
	}
	
	/**
	 * @return the createdBy
	 */
	public Integer getCreatedBy() {
		return createdBy;
	}
	
	/**
	 * @return the name
	 */
	public String getName() {
		return name;
	}
	
	/**
	 * @return the status
	 */
	public Integer getStatus() {
		return status;
	}
	
	/**
	 * @return the priorityOrder
	 */
	public Integer getPriorityOrder() {
		return priorityOrder;
	}
	
	/**
	 * @return the templateId
	 */
	public Integer getTemplateId() {
		return templateId;
	}
	
	/**
	 * @return the resellerId
	 */
	public Integer getResellerId() {
		return resellerId;
	}
	
	/**
	 * @return the isDeleted
	 */
	public Integer getIsDeleted() {
		return isDeleted;
	}
	
	/**
	 * @return the customerCount
	 */
	public Integer getCustomerCount() {
		return customerCount;
	}
	
	/**
	 * @return the smsTemplateId
	 */
	public Integer getSmsTemplateId() {
		return smsTemplateId;
	}
	
	/**
	 * @return the priority
	 */
	public String getPriority() {
		return priority;
	}
	
	/**
	 * @return the runType
	 */
	public String getRunType() {
		return runType;
	}
	
	/**
	 * @return the reminderSubject
	 */
	public String getReminderSubject() {
		return reminderSubject;
	}
	
	/**
	 * @return the reminderCount
	 */
	public Integer getReminderCount() {
		return reminderCount;
	}
	
	/**
	 * @return the reminderFrequency
	 */
	public Integer getReminderFrequency() {
		return reminderFrequency;
	}
	
	/**
	 * @return the sendReminder
	 */
	public Integer getSendReminder() {
		return sendReminder;
	}
	
	/**
	 * @return the isDefault
	 */
	public Integer getIsDefault() {
		return isDefault;
	}
	
	/**
	 * @return the schedule
	 */
	public Integer getSchedule() {
		return schedule;
	}
	
	/**
	 * @return the schedulingType
	 */
	public String getSchedulingType() {
		return schedulingType;
	}
	
	/**
	 * @return the schedulingInHours
	 */
	public Integer getSchedulingInHours() {
		return schedulingInHours;
	}
	
	/**
	 * @return the isMessengerCampaign
	 */
	public Integer getIsMessengerCampaign() {
		return isMessengerCampaign;
	}
	
	/**
	 * @return the triggerType
	 */
	public String getTriggerType() {
		return triggerType;
	}
	
	/**
	 * @return the bypassCommRestriction
	 */
	public Integer getBypassCommRestriction() {
		return bypassCommRestriction;
	}
	
	/**
	 * @return the skipFutureAppointment
	 */
	public Integer getSkipFutureAppointment() {
		return skipFutureAppointment;
	}
	
	/**
	 * @return the isSplitCampaign
	 */
	public Integer getIsSplitCampaign() {
		return isSplitCampaign;
	}
	
	/**
	 * @return the isAppointmentTabCampaign
	 */
	public Integer getIsAppointmentTabCampaign() {
		return isAppointmentTabCampaign;
	}
	
	public Integer getSurveyCommFrequency() {
		return surveyCommFrequency;
	}

	public void setSurveyCommFrequency(Integer surveyCommFrequency) {
		this.surveyCommFrequency = surveyCommFrequency;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
