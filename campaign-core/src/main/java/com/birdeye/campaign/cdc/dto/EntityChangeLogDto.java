package com.birdeye.campaign.cdc.dto;

import java.util.List;
import java.util.Map;

public class EntityChangeLogDto {
	
	private Map<String, Object>	dataDiffMap;
	
	private List<String>		updatedFieldNames;
	
	public EntityChangeLogDto() {
		
	}
	
	public EntityChangeLogDto(Map<String, Object> dataDiffMap, List<String> updatedFieldNames) {
		this.dataDiffMap = dataDiffMap;
		this.updatedFieldNames = updatedFieldNames;
	}
	
	/**
	 * @return the dataDiffMap
	 */
	public Map<String, Object> getDataDiffMap() {
		return dataDiffMap;
	}
	
	/**
	 * @param dataDiffMap
	 *            the dataDiffMap to set
	 */
	public void setDataDiffMap(Map<String, Object> dataDiffMap) {
		this.dataDiffMap = dataDiffMap;
	}
	
	/**
	 * @return the updatedFieldNames
	 */
	public List<String> getUpdatedFieldNames() {
		return updatedFieldNames;
	}
	
	/**
	 * @param updatedFieldNames
	 *            the updatedFieldNames to set
	 */
	public void setUpdatedFieldNames(List<String> updatedFieldNames) {
		this.updatedFieldNames = updatedFieldNames;
	}
	
}
