package com.birdeye.campaign.cdc.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;

/**
 * Wrapper For Campaign/CampaignCondition Entity.
 * Created To Capture the Changed Data In Campaign/CampaignCondition.
 * Projection For Entity's attributes with large values not to be included here.
 * 
 * <AUTHOR>
 */
@JsonDeserialize(builder = CampaignCDCDTO.Builder.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class CampaignCDCDTO implements Serializable {
	
	private static final long				serialVersionUID	= -5937560343101889873L;
	
	private Integer							campaignId;
	private String							campaignType;
	private Date							campaignStartAt;
	private Date							campaignEndAt;
	private Date							campaignCreatedAt;
	private Date							campaignUpdatedAt;
	private Integer							businessId;
	private Integer							enterpriseId;
	private Integer							surveyId;
	private Integer							campaignCreatedBy;
	private String							campaignName;
	private Integer							status;
	private Integer							priorityOrder;
	private Integer							emailTemplateId;
	private Integer							resellerId;
	private Integer							isDeleted;
	private Integer							customerCount;
	private Integer							smsTemplateId;
	private String							priority;
	private String							runType;
	private String							reminderSubject;
	private Integer							reminderCount;
	private Integer							reminderFrequency;
	private Integer							sendReminder;
	private Integer							isDefault;
	private Integer							schedule;
	private String							schedulingType;
	private Integer							schedulingInHours;
	private Integer							isMessengerCampaign;
	private String							triggerType;
	private String							campaignEditedBy;
	private Date							campaignEditedOn;
	private Integer							bypassCommRestriction;
	private Integer							skipFutureAppointment;
	private Integer							isSplitCampaign;
	private Integer							isAppointmentTabCampaign;
	private Integer							conditionId;
	private String							event;
	private String							lvlAliasId;
	private String							lvlAlias;
	private List<String>					lvlIds;
	private List<String>					contactSources;
	private String							tags;
	private Date							conditionCreatedAt;
	private Date							conditionUpdatedAt;
	private Integer							conditionUpdatedBy;
	private String							exclusionTag;
	private Integer							noTagFilter;
	private Integer							anyTagFilter;
	private String							ruleExpression;
	private String							mvelExpression;
	private Map<String, String>				mvelParamsAndTypes;
	private String							triggerRuleExpression;
	private String							triggerMvelExpression;
	private Map<String, String>				triggerMvelParamsAndTypes;
	private String							appointmentScheduleInfo;
	private String							scheduleInHours;
	private String							executionDateInfo;
	private String							recurringReminderEventType;
	private List<String>					dripAllowedDays;
	private List<String>					dripExclusionDates;
	private String							dripSendTime;
	private Date							dripCreatedAt;
	private Date							dripUpdatedAt;
	private Integer							dripUpdatedBy;
	
	private CampaignCDCDTO(Builder builder) {
		this.campaignId = builder.campaignId;
		this.campaignType = builder.campaignType;
		this.campaignStartAt = builder.campaignStartAt;
		this.campaignEndAt = builder.campaignEndAt;
		this.campaignCreatedAt = builder.campaignCreatedAt;
		this.campaignUpdatedAt = builder.campaignUpdatedAt;
		this.businessId = builder.businessId;
		this.enterpriseId = builder.enterpriseId;
		this.surveyId = builder.surveyId;
		this.campaignCreatedBy = builder.campaignCreatedBy;
		this.campaignName = builder.campaignName;
		this.status = builder.status;
		this.priorityOrder = builder.priorityOrder;
		this.emailTemplateId = builder.emailTemplateId;
		this.resellerId = builder.resellerId;
		this.isDeleted = builder.isDeleted;
		this.customerCount = builder.customerCount;
		this.smsTemplateId = builder.smsTemplateId;
		this.priority = builder.priority;
		this.runType = builder.runType;
		this.reminderSubject = builder.reminderSubject;
		this.reminderCount = builder.reminderCount;
		this.reminderFrequency = builder.reminderFrequency;
		this.sendReminder = builder.sendReminder;
		this.isDefault = builder.isDefault;
		this.schedule = builder.schedule;
		this.schedulingType = builder.schedulingType;
		this.schedulingInHours = builder.schedulingInHours;
		this.isMessengerCampaign = builder.isMessengerCampaign;
		this.triggerType = builder.triggerType;
		this.campaignEditedBy = builder.campaignEditedBy;
		this.campaignEditedOn = builder.campaignEditedOn;
		this.bypassCommRestriction = builder.bypassCommRestriction;
		this.skipFutureAppointment = builder.skipFutureAppointment;
		this.isSplitCampaign = builder.isSplitCampaign;
		this.isAppointmentTabCampaign = builder.isAppointmentTabCampaign;
		this.conditionId = builder.conditionId;
		this.event = builder.event;
		this.lvlAliasId = builder.lvlAliasId;
		this.lvlAlias = builder.lvlAlias;
		this.lvlIds = builder.lvlIds;
		this.contactSources = builder.contactSources;
		this.tags = builder.tags;
		this.conditionCreatedAt = builder.conditionCreatedAt;
		this.conditionUpdatedAt = builder.conditionUpdatedAt;
		this.conditionUpdatedBy = builder.conditionUpdatedBy;
		this.exclusionTag = builder.exclusionTag;
		this.noTagFilter = builder.noTagFilter;
		this.anyTagFilter = builder.anyTagFilter;
		this.ruleExpression = builder.ruleExpression;
		this.mvelExpression = builder.mvelExpression;
		this.mvelParamsAndTypes = builder.mvelParamsAndTypes;
		this.triggerRuleExpression = builder.triggerRuleExpression;
		this.triggerMvelExpression = builder.triggerMvelExpression;
		this.triggerMvelParamsAndTypes = builder.triggerMvelParamsAndTypes;
		this.appointmentScheduleInfo = builder.appointmentScheduleInfo;
		this.scheduleInHours = builder.scheduleInHours;
		this.executionDateInfo = builder.executionDateInfo;
		this.recurringReminderEventType = builder.recurringReminderEventType;
		this.dripAllowedDays = builder.dripAllowedDays;
		this.dripExclusionDates = builder.dripExclusionDates;
		this.dripSendTime = builder.dripSendTime;
		this.dripCreatedAt = builder.dripCreatedAt;
		this.dripUpdatedAt = builder.dripUpdatedAt;
		this.dripUpdatedBy = builder.dripUpdatedBy;
	}
	
	public static Builder builder() {
		return new Builder();
	}
	
	@JsonPOJOBuilder
	public static final class Builder {
		private Integer							campaignId;
		private String							campaignType;
		private Date							campaignStartAt;
		private Date							campaignEndAt;
		private Date							campaignCreatedAt;
		private Date							campaignUpdatedAt;
		private Integer							businessId;
		private Integer							enterpriseId;
		private Integer							surveyId;
		private Integer							campaignCreatedBy;
		private String							campaignName;
		private Integer							status;
		private Integer							priorityOrder;
		private Integer							emailTemplateId;
		private Integer							resellerId;
		private Integer							isDeleted;
		private Integer							customerCount;
		private Integer							smsTemplateId;
		private String							priority;
		private String							runType;
		private String							reminderSubject;
		private Integer							reminderCount;
		private Integer							reminderFrequency;
		private Integer							sendReminder;
		private Integer							isDefault;
		private Integer							schedule;
		private String							schedulingType;
		private Integer							schedulingInHours;
		private Integer							isMessengerCampaign;
		private String							triggerType;
		private String							campaignEditedBy;
		private Date							campaignEditedOn;
		private Integer							bypassCommRestriction;
		private Integer							skipFutureAppointment;
		private Integer							isSplitCampaign;
		private Integer							isAppointmentTabCampaign;
		private Integer							conditionId;
		private String							event;
		private String							lvlAliasId;
		private String							lvlAlias;
		private List<String>					lvlIds;
		private List<String>					contactSources;
		private String							tags;
		private Date							conditionCreatedAt;
		private Date							conditionUpdatedAt;
		private Integer							conditionUpdatedBy;
		private String							exclusionTag;
		private Integer							noTagFilter;
		private Integer							anyTagFilter;
		private String							ruleExpression;
		private String							mvelExpression;
		private Map<String, String>				mvelParamsAndTypes;
		private String							triggerRuleExpression;
		private String							triggerMvelExpression;
		private Map<String, String>				triggerMvelParamsAndTypes;
		private String							appointmentScheduleInfo;
		private String							scheduleInHours;
		private String							executionDateInfo;
		private String							recurringReminderEventType;
		private List<String>					dripAllowedDays;
		private List<String>					dripExclusionDates;
		private String							dripSendTime;
		private Date							dripCreatedAt;
		private Date							dripUpdatedAt;
		private Integer							dripUpdatedBy;
		
		public Builder withCampaignId(Integer campaignId) {
			this.campaignId = campaignId;
			return this;
		}
		
		public Builder withCampaignType(String campaignType) {
			this.campaignType = campaignType;
			return this;
		}
		
		public Builder withCampaignStartAt(Date startAt) {
			this.campaignStartAt = startAt;
			return this;
		}
		
		public Builder withCampaignEndAt(Date endAt) {
			this.campaignEndAt = endAt;
			return this;
		}
		
		public Builder withCampaignCreatedAt(Date createdAt) {
			this.campaignCreatedAt = createdAt;
			return this;
		}
		
		public Builder withCampaignUpdatedAt(Date updatedAt) {
			this.campaignUpdatedAt = updatedAt;
			return this;
		}
		
		public Builder withBusinessId(Integer businessId) {
			this.businessId = businessId;
			return this;
		}
		
		public Builder withEnterpriseId(Integer enterpriseId) {
			this.enterpriseId = enterpriseId;
			return this;
		}
		
		public Builder withSurveyId(Integer surveyId) {
			this.surveyId = surveyId;
			return this;
		}
		
		public Builder withCampaignCreatedBy(Integer createdBy) {
			this.campaignCreatedBy = createdBy;
			return this;
		}
		
		public Builder withCampaignName(String campaignName) {
			this.campaignName = campaignName;
			return this;
		}
		
		public Builder withStatus(Integer status) {
			this.status = status;
			return this;
		}
		
		public Builder withPriorityOrder(Integer priorityOrder) {
			this.priorityOrder = priorityOrder;
			return this;
		}
		
		public Builder withEmailTemplateId(Integer emailTemplateId) {
			this.emailTemplateId = emailTemplateId;
			return this;
		}
		
		public Builder withResellerId(Integer resellerId) {
			this.resellerId = resellerId;
			return this;
		}
		
		public Builder withIsDeleted(Integer isDeleted) {
			this.isDeleted = isDeleted;
			return this;
		}
		
		public Builder withCustomerCount(Integer customerCount) {
			this.customerCount = customerCount;
			return this;
		}
		
		public Builder withSmsTemplateId(Integer smsTemplateId) {
			this.smsTemplateId = smsTemplateId;
			return this;
		}
		
		public Builder withPriority(String priority) {
			this.priority = priority;
			return this;
		}
		
		public Builder withRunType(String runType) {
			this.runType = runType;
			return this;
		}
		
		public Builder withReminderSubject(String reminderSubject) {
			this.reminderSubject = reminderSubject;
			return this;
		}
		
		public Builder withReminderCount(Integer reminderCount) {
			this.reminderCount = reminderCount;
			return this;
		}
		
		public Builder withReminderFrequency(Integer reminderFrequency) {
			this.reminderFrequency = reminderFrequency;
			return this;
		}
		
		public Builder withSendReminder(Integer sendReminder) {
			this.sendReminder = sendReminder;
			return this;
		}
		
		public Builder withIsDefault(Integer isDefault) {
			this.isDefault = isDefault;
			return this;
		}
		
		public Builder withSchedule(Integer schedule) {
			this.schedule = schedule;
			return this;
		}
		
		public Builder withSchedulingType(String schedulingType) {
			this.schedulingType = schedulingType;
			return this;
		}
		
		public Builder withSchedulingInHours(Integer schedulingInHours) {
			this.schedulingInHours = schedulingInHours;
			return this;
		}
		
		public Builder withIsMessengerCampaign(Integer isMessengerCampaign) {
			this.isMessengerCampaign = isMessengerCampaign;
			return this;
		}
		
		public Builder withTriggerType(String triggerType) {
			this.triggerType = triggerType;
			return this;
		}
		
		public Builder withCampaignEditedBy(String editedBy) {
			this.campaignEditedBy = editedBy;
			return this;
		}
		
		public Builder withCampaignEditedOn(Date editedOn) {
			this.campaignEditedOn = editedOn;
			return this;
		}
		
		public Builder withBypassCommRestriction(Integer bypassCommRestriction) {
			this.bypassCommRestriction = bypassCommRestriction;
			return this;
		}
		
		public Builder withSkipFutureAppointment(Integer skipFutureAppointment) {
			this.skipFutureAppointment = skipFutureAppointment;
			return this;
		}
		
		public Builder withIsSplitCampaign(Integer isSplitCampaign) {
			this.isSplitCampaign = isSplitCampaign;
			return this;
		}
		
		public Builder withIsAppointmentTabCampaign(Integer isAppointmentTabCampaign) {
			this.isAppointmentTabCampaign = isAppointmentTabCampaign;
			return this;
		}
		
		public Builder withConditionId(Integer conditionId) {
			this.conditionId = conditionId;
			return this;
		}
		
		public Builder withEvent(String event) {
			this.event = event;
			return this;
		}
		
		public Builder withLvlAliasId(String lvlAliasId) {
			this.lvlAliasId = lvlAliasId;
			return this;
		}
		
		public Builder withLvlAlias(String lvlAlias) {
			this.lvlAlias = lvlAlias;
			return this;
		}
		
		public Builder withLvlIds(List<String> lvlIds) {
			this.lvlIds = lvlIds;
			return this;
		}
		
		public Builder withContactSources(List<String> contactSources) {
			this.contactSources = contactSources;
			return this;
		}
		
		public Builder withTags(String tags) {
			this.tags = tags;
			return this;
		}
		
		public Builder withConditionCreatedAt(Date conditionCreatedAt) {
			this.conditionCreatedAt = conditionCreatedAt;
			return this;
		}
		
		public Builder withConditionUpdatedAt(Date conditionUpdatedAt) {
			this.conditionUpdatedAt = conditionUpdatedAt;
			return this;
		}
		
		public Builder withConditionUpdatedBy(Integer conditionUpdatedBy) {
			this.conditionUpdatedBy = conditionUpdatedBy;
			return this;
		}
		
		public Builder withExclusionTag(String exclusionTag) {
			this.exclusionTag =  exclusionTag;
			return this;
		}
		
		public Builder withNoTagFilter(Integer noTagFilter) {
			this.noTagFilter = noTagFilter;
			return this;
		}
		
		public Builder withAnyTagFilter(Integer anyTagFilter) {
			this.anyTagFilter = anyTagFilter;
			return this;
		}
		
		public Builder withRuleExpression(String ruleExpression) {
			this.ruleExpression = ruleExpression;
			return this;
		}
		
		public Builder withMvelExpression(String mvelExpression) {
			this.mvelExpression = mvelExpression;
			return this;
		}
		
		public Builder withMvelParamsAndTypes(Map<String, String> mvelParamsAndTypes) {
			this.mvelParamsAndTypes = mvelParamsAndTypes;
			return this;
		}
		
		public Builder withTriggerRuleExpression(String triggerRuleExpression) {
			this.triggerRuleExpression = triggerRuleExpression;
			return this;
		}
		
		public Builder withTriggerMvelExpression(String triggerMvelExpression) {
			this.triggerMvelExpression = triggerMvelExpression;
			return this;
		}
		
		public Builder withTriggerMvelParamsAndTypes(Map<String, String> triggerMvelParamsAndTypes) {
			this.triggerMvelParamsAndTypes = triggerMvelParamsAndTypes;
			return this;
		}
		
		public Builder withAppointmentScheduleInfo(String appointmentScheduleInfo) {
			this.appointmentScheduleInfo = appointmentScheduleInfo;
			return this;
		}
		
		public Builder withScheduleInHours(String scheduleInHours) {
			this.scheduleInHours = scheduleInHours;
			return this;
		}
		
		public Builder withExecutionDateInfo(String executionDateInfo) {
			this.executionDateInfo = executionDateInfo;
			return this;
		}
		
		public Builder withRecurringReminderEventType(String recurringReminderEventType) {
			this.recurringReminderEventType = recurringReminderEventType;
			return this;
		}
		
		public Builder withDripAllowedDays(List<String> dripAllowedDays) {
			this.dripAllowedDays = dripAllowedDays;
			return this;
		}
		
		public Builder withDripExclusionDates(List<String> dripExclusionDates) {
			this.dripExclusionDates = dripExclusionDates;
			return this;
		}
		
		public Builder withDripSendTime(String dripSendTime) {
			this.dripSendTime = dripSendTime;
			return this;
		}
		
		public Builder withDripCreatedAt(Date dripCreatedAt) {
			this.dripCreatedAt = dripCreatedAt;
			return this;
		}
		
		public Builder withDripUpdatedAt(Date dripUpdatedAt) {
			this.dripUpdatedAt = dripUpdatedAt;
			return this;
		}
		
		public Builder withDripUpdatedBy(Integer dripUpdatedBy) {
			this.dripUpdatedBy = dripUpdatedBy;
			return this;
		}
		
		public CampaignCDCDTO build() {
			return new CampaignCDCDTO(this);
		}
	}
	
	/**
	 * @return the campaignId
	 */
	public Integer getCampaignId() {
		return campaignId;
	}
	
	/**
	 * @return the campaignType
	 */
	public String getCampaignType() {
		return campaignType;
	}
	
	/**
	 * @return the startAt
	 */
	public Date getCampaignStartAt() {
		return campaignStartAt;
	}
	
	/**
	 * @return the endAt
	 */
	public Date getCampaignEndAt() {
		return campaignEndAt;
	}
	
	/**
	 * @return the createdAt
	 */
	public Date getCampaignCreatedAt() {
		return campaignCreatedAt;
	}
	
	/**
	 * @return the updatedAt
	 */
	public Date getCampaignUpdatedAt() {
		return campaignUpdatedAt;
	}
	
	/**
	 * @return the businessId
	 */
	public Integer getBusinessId() {
		return businessId;
	}
	
	/**
	 * @return the enterpriseId
	 */
	public Integer getEnterpriseId() {
		return enterpriseId;
	}
	
	/**
	 * @return the surveyId
	 */
	public Integer getSurveyId() {
		return surveyId;
	}
	
	/**
	 * @return the createdBy
	 */
	public Integer getCampaignCreatedBy() {
		return campaignCreatedBy;
	}
	
	/**
	 * @return the campaignName
	 */
	public String getCampaignName() {
		return campaignName;
	}
	
	/**
	 * @return the status
	 */
	public Integer getStatus() {
		return status;
	}
	
	/**
	 * @return the priorityOrder
	 */
	public Integer getPriorityOrder() {
		return priorityOrder;
	}
	
	/**
	 * @return the emailTemplateId
	 */
	public Integer getEmailTemplateId() {
		return emailTemplateId;
	}
	
	/**
	 * @return the resellerId
	 */
	public Integer getResellerId() {
		return resellerId;
	}
	
	/**
	 * @return the isDeleted
	 */
	public Integer getIsDeleted() {
		return isDeleted;
	}
	
	/**
	 * @return the customerCount
	 */
	public Integer getCustomerCount() {
		return customerCount;
	}
	
	/**
	 * @return the smsTemplateId
	 */
	public Integer getSmsTemplateId() {
		return smsTemplateId;
	}
	
	/**
	 * @return the priority
	 */
	public String getPriority() {
		return priority;
	}
	
	/**
	 * @return the runType
	 */
	public String getRunType() {
		return runType;
	}
	
	/**
	 * @return the reminderSubject
	 */
	public String getReminderSubject() {
		return reminderSubject;
	}
	
	/**
	 * @return the reminderCount
	 */
	public Integer getReminderCount() {
		return reminderCount;
	}
	
	/**
	 * @return the reminderFrequency
	 */
	public Integer getReminderFrequency() {
		return reminderFrequency;
	}
	
	/**
	 * @return the sendReminder
	 */
	public Integer getSendReminder() {
		return sendReminder;
	}
	
	/**
	 * @return the isDefault
	 */
	public Integer getIsDefault() {
		return isDefault;
	}
	
	/**
	 * @return the schedule
	 */
	public Integer getSchedule() {
		return schedule;
	}
	
	/**
	 * @return the schedulingType
	 */
	public String getSchedulingType() {
		return schedulingType;
	}
	
	/**
	 * @return the schedulingInHours
	 */
	public Integer getSchedulingInHours() {
		return schedulingInHours;
	}
	
	/**
	 * @return the isMessengerCampaign
	 */
	public Integer getIsMessengerCampaign() {
		return isMessengerCampaign;
	}
	
	/**
	 * @return the triggerType
	 */
	public String getTriggerType() {
		return triggerType;
	}
	
	/**
	 * @return the editedBy
	 */
	public String getCampaignEditedBy() {
		return campaignEditedBy;
	}
	
	/**
	 * @return the editedOn
	 */
	public Date getCampaignEditedOn() {
		return campaignEditedOn;
	}
	
	/**
	 * @return the bypassCommRestriction
	 */
	public Integer getBypassCommRestriction() {
		return bypassCommRestriction;
	}
	
	/**
	 * @return the skipFutureAppointment
	 */
	public Integer getSkipFutureAppointment() {
		return skipFutureAppointment;
	}
	
	/**
	 * @return the isSplitCampaign
	 */
	public Integer getIsSplitCampaign() {
		return isSplitCampaign;
	}
	
	/**
	 * @return the isAppointmentTabCampaign
	 */
	public Integer getIsAppointmentTabCampaign() {
		return isAppointmentTabCampaign;
	}
	
	/**
	 * @return the conditionId
	 */
	public Integer getConditionId() {
		return conditionId;
	}
	
	/**
	 * @return the event
	 */
	public String getEvent() {
		return event;
	}
	
	/**
	 * @return the lvlAliasId
	 */
	public String getLvlAliasId() {
		return lvlAliasId;
	}
	
	/**
	 * @return the lvlAlias
	 */
	public String getLvlAlias() {
		return lvlAlias;
	}
	
	/**
	 * @return the lvlIds
	 */
	public List<String> getLvlIds() {
		return lvlIds;
	}
	
	/**
	 * @return the contactSources
	 */
	public List<String> getContactSources() {
		return contactSources;
	}
	
	/**
	 * @return the tags
	 */
	public String getTags() {
		return tags;
	}
	
	/**
	 * @return the conditionCreatedAt
	 */
	public Date getConditionCreatedAt() {
		return conditionCreatedAt;
	}
	
	/**
	 * @return the conditionUpdatedAt
	 */
	public Date getConditionUpdatedAt() {
		return conditionUpdatedAt;
	}
	
	/**
	 * @return the conditionUpdatedBy
	 */
	public Integer getConditionUpdatedBy() {
		return conditionUpdatedBy;
	}
	
	/**
	 * @return the exclusionTag
	 */
	public String getExclusionTag() {
		return exclusionTag;
	}
	
	/**
	 * @return the noTagFilter
	 */
	public Integer getNoTagFilter() {
		return noTagFilter;
	}
	
	/**
	 * @return the anyTagFilter
	 */
	public Integer getAnyTagFilter() {
		return anyTagFilter;
	}
	
	/**
	 * @return the ruleExpression
	 */
	public String getRuleExpression() {
		return ruleExpression;
	}
	
	/**
	 * @return the mvelExpression
	 */
	public String getMvelExpression() {
		return mvelExpression;
	}
	
	/**
	 * @return the mvelParamsAndTypes
	 */
	public Map<String, String> getMvelParamsAndTypes() {
		return mvelParamsAndTypes;
	}
	
	/**
	 * @return the triggerRuleExpression
	 */
	public String getTriggerRuleExpression() {
		return triggerRuleExpression;
	}
	
	/**
	 * @return the triggerMvelExpression
	 */
	public String getTriggerMvelExpression() {
		return triggerMvelExpression;
	}
	
	/**
	 * @return the triggerMvelParamsAndTypes
	 */
	public Map<String, String> getTriggerMvelParamsAndTypes() {
		return triggerMvelParamsAndTypes;
	}
	
	/**
	 * @return the appointmentScheduleInfo
	 */
	public String getAppointmentScheduleInfo() {
		return appointmentScheduleInfo;
	}
	
	/**
	 * @return the scheduleinHours
	 */
	public String getScheduleInHours() {
		return scheduleInHours;
	}
	
	/**
	 * @return the executionDateInfo
	 */
	public String getExecutionDateInfo() {
		return executionDateInfo;
	}
	
	/**
	 * @return the recurringReminderEventType
	 */
	public String getRecurringReminderEventType() {
		return recurringReminderEventType;
	}
	
	/**
	 * @return the dripAllowedDays
	 */
	public List<String> getDripAllowedDays() {
		return dripAllowedDays;
	}

	/**
	 * @return the dripExclusionDates
	 */
	public List<String> getDripExclusionDates() {
		return dripExclusionDates;
	}

	/**
	 * @return the dripSendTime
	 */
	public String getDripSendTime() {
		return dripSendTime;
	}

	/**
	 * @return the dripCreatedAt
	 */
	public Date getDripCreatedAt() {
		return dripCreatedAt;
	}

	/**
	 * @return the dripUpdatedAt
	 */
	public Date getDripUpdatedAt() {
		return dripUpdatedAt;
	}

	/**
	 * @return the dripUpdatedBy
	 */
	public Integer getDripUpdatedBy() {
		return dripUpdatedBy;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
