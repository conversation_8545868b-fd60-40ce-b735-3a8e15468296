package com.birdeye.campaign.data.migration.request;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class ESDataMigrationRequest implements Serializable {
	
	private static final long	serialVersionUID	= 3789777484598928549L;
	
	private Long				startId;
	
	private Long				endId;
	
	private String				campaignType;
	
	public ESDataMigrationRequest() {
		super();
	}
	
	public ESDataMigrationRequest(Long startId, Long endId, String campaignType) {
		super();
		this.startId = startId;
		this.endId = endId;
		this.campaignType = campaignType;
	}
	
	public Long getStartId() {
		return startId;
	}
	
	public void setStartId(Long startId) {
		this.startId = startId;
	}
	
	public Long getEndId() {
		return endId;
	}
	
	public void setEndId(Long endId) {
		this.endId = endId;
	}
	
	public String getCampaignType() {
		return campaignType;
	}
	
	public void setCampaignType(String campaignType) {
		this.campaignType = campaignType;
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder sb = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return sb.toString();
	}
	
}
