package com.birdeye.campaign.request;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class ResellerCampaignWidgetESBulkInsertRequest implements Serializable {
	
	private static final long	serialVersionUID	= 5253611589079879653L;
	private String				indexName;
	private String				bulkRequestBody;
	
	public ResellerCampaignWidgetESBulkInsertRequest(String indexName, String bulkRequestBody) {
		super();
		this.indexName = indexName;
		this.bulkRequestBody = bulkRequestBody;
	}

	public String getIndexName() {
		return indexName;
	}
	
	public void setIndexName(String indexName) {
		this.indexName = indexName;
	}
	
	public String getBulkRequestBody() {
		return bulkRequestBody;
	}
	
	public void setBulkRequestBody(String bulkRequestBody) {
		this.bulkRequestBody = bulkRequestBody;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
