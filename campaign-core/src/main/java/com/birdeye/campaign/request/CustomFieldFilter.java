package com.birdeye.campaign.request;

import java.io.Serializable;
import java.util.List;

public class Custom<PERSON>ieldFilter implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 384681014903038906L;
	
	private String type;
	private Integer id;
	private String operator;
	private String operand;
	private List<Object> value;
	/**
	 * @return the type
	 */
	public String getType() {
		return type;
	}
	/**
	 * @param type the type to set
	 */
	public void setType(String type) {
		this.type = type;
	}
	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}
	/**
	 * @param id the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	/**
	 * @return the operator
	 */
	public String getOperator() {
		return operator;
	}
	/**
	 * @param operator the operator to set
	 */
	public void setOperator(String operator) {
		this.operator = operator;
	}
	/**
	 * @return the operand
	 */
	public String getOperand() {
		return operand;
	}
	/**
	 * @param operand the operand to set
	 */
	public void setOperand(String operand) {
		this.operand = operand;
	}
	/**
	 * @return the value
	 */
	public List<Object> getValue() {
		return value;
	}
	/**
	 * @param value the value to set
	 */
	public void setValue(List<Object> value) {
		this.value = value;
	}
}