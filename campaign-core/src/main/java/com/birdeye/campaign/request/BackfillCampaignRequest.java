package com.birdeye.campaign.request;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_NULL)
public class BackfillCampaignRequest implements Serializable {
	
	private static final long	serialVersionUID	= 479061381173881299L;
	
	private List<Integer>		campaignIds;
	
	public List<Integer> getCampaignIds() {
		return campaignIds;
	}
	
	public void setCampaignIds(List<Integer> campaignIds) {
		this.campaignIds = campaignIds;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
