package com.birdeye.campaign.request;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ResellerReportingCampaignMetricsRequest implements Serializable {
	private static final long	serialVersionUID	= 4681008402175776860L;
	private Integer				accountId;
	private List<Integer>		businessIds;
	private List<String>		metrics;
	
	public Integer getAccountId() {
		return accountId;
	}
	
	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}
	
	public List<Integer> getBusinessIds() {
		return businessIds;
	}
	
	public void setBusinessIds(List<Integer> businessIds) {
		this.businessIds = businessIds;
	}
	
	public List<String> getMetrics() {
		return metrics;
	}
	
	public void setMetrics(List<String> metrics) {
		this.metrics = metrics;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
