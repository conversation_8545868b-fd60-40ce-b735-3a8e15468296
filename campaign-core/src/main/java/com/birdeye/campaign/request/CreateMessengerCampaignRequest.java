package com.birdeye.campaign.request;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateMessengerCampaignRequest extends CreateManualCampaignRequest {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -6506159848176333183L;
	
	private List<MessengerMediaInfo>	messengerMediaInfo;
	
	private String						freeText;
	
	private Map<String, String>			freeTextCampaignUrlMap;	// to support tracking of free urls in free text campaigns

	public List<MessengerMediaInfo> getMessengerMediaInfo() {
		return messengerMediaInfo;
	}

	public void setMessengerMediaInfo(List<MessengerMediaInfo> messengerMediaInfo) {
		this.messengerMediaInfo = messengerMediaInfo;
	}

	public String getFreeText() {
		return freeText;
	}

	public void setFreeText(String freeText) {
		this.freeText = freeText;
	}

	public Map<String, String> getFreeTextCampaignUrlMap() {
		return freeTextCampaignUrlMap;
	}

	public void setFreeTextCampaignUrlMap(Map<String, String> freeTextCampaignUrlMap) {
		this.freeTextCampaignUrlMap = freeTextCampaignUrlMap;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
