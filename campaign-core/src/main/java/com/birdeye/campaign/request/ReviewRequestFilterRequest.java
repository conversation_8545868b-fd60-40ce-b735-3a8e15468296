package com.birdeye.campaign.request;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ReviewRequestFilterRequest extends PageDetailsRequest implements Serializable {
	
	private static final long	serialVersionUID	= -7818967501760108125L;
	
	private List<Integer>		customerIds;
	
	private String				deliveryStatus;								// default : all rr status values, comma separated list of status
	
	public ReviewRequestFilterRequest() {
		super();
	}
	
	/**
	 * @return the customerIds
	 */
	public List<Integer> getCustomerIds() {
		return customerIds;
	}
	
	/**
	 * @param customerIds
	 *            the customerIds to set
	 */
	public void setCustomerIds(List<Integer> customerIds) {
		this.customerIds = customerIds;
	}
	
	/**
	 * @return the deliveryStatus
	 */
	public String getDeliveryStatus() {
		return deliveryStatus;
	}
	
	/**
	 * @param deliveryStatus
	 *            the deliveryStatus to set
	 */
	public void setDeliveryStatus(String deliveryStatus) {
		this.deliveryStatus = deliveryStatus;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
