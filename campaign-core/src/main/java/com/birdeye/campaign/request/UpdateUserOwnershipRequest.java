package com.birdeye.campaign.request;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_NULL)
public class UpdateUserOwnershipRequest implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -2046854269540007072L;
	private Integer				accountId;
	private Integer				exisitingUserId;
	private Integer				newUserId;
	
	public Integer getAccountId() {
		return accountId;
	}
	
	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}
	
	public Integer getExisitingUserId() {
		return exisitingUserId;
	}
	
	public void setExisitingUserId(Integer exisitingUserId) {
		this.exisitingUserId = exisitingUserId;
	}
	
	public Integer getNewUserId() {
		return newUserId;
	}
	
	public void setNewUserId(Integer newUserId) {
		this.newUserId = newUserId;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
