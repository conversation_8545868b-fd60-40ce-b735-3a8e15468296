/**
 * @file_name ViewRecipientsFilter.java
 * @created_date 12 Aug 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.request.viewrecipients;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ViewRecipientsFilter implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -1117708817062398199L;
	
	private String				status;										//
	private List<String>		sources;									//email/sms
	
	/**
	 * @return the status
	 */
	public String getStatus() {
		return status;
	}
	
	/**
	 * @param status
	 *            the status to set
	 */
	public void setStatus(String status) {
		this.status = status;
	}
	
	/**
	 * @return the sources
	 */
	public List<String> getSources() {
		return sources;
	}
	
	/**
	 * @param sources
	 *            the sources to set
	 */
	public void setSources(List<String> sources) {
		this.sources = sources;
	}
	
	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("ViewRecipientsFilter [status=");
		builder.append(status);
		builder.append(", sources=");
		builder.append(sources);
		builder.append("]");
		return builder.toString();
	}
	
}
