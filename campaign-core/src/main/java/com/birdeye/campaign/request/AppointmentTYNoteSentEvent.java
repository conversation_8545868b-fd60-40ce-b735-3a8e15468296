package com.birdeye.campaign.request;

import java.io.Serializable;
import java.util.Date;

public class AppointmentTYNoteSentEvent implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -4964825269404970458L;
	
	private Integer				cid;
	
	private Integer				thankyouNoteSent = 1;	// will always be 1 - no toggle feature currently
	
	private Date				thankyouNoteSentDate;
	
	private Integer				locationId;
	
	private Integer				enterpriseId;
	
	private Integer				referrerCid;
	
	public Integer getCid() {
		return cid;
	}
	
	public void setCid(Integer cid) {
		this.cid = cid;
	}
	
	public Integer getThankyouNoteSent() {
		return thankyouNoteSent;
	}
	
	public void setThankyouNoteSent(Integer thankyouNoteSent) {
		this.thankyouNoteSent = thankyouNoteSent;
	}
	
	public Date getThankyouNoteSentDate() {
		return thankyouNoteSentDate;
	}
	
	public void setThankyouNoteSentDate(Date thankyouNoteSentDate) {
		this.thankyouNoteSentDate = thankyouNoteSentDate;
	}
	
	public Integer getLocationId() {
		return locationId;
	}
	
	public void setLocationId(Integer locationId) {
		this.locationId = locationId;
	}
	
	public Integer getEnterpriseId() {
		return enterpriseId;
	}
	
	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public Integer getReferrerCid() {
		return referrerCid;
	}

	public void setReferrerCid(Integer referrerCid) {
		this.referrerCid = referrerCid;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("AppointmentTYNoteSentEvent [cid=");
		builder.append(cid);
		builder.append(", thankyouNoteSent=");
		builder.append(thankyouNoteSent);
		builder.append(", thankyouNoteSentDate=");
		builder.append(thankyouNoteSentDate);
		builder.append(", locationId=");
		builder.append(locationId);
		builder.append(", enterpriseId=");
		builder.append(enterpriseId);
		builder.append(", referrerCid=");
		builder.append(referrerCid);
		builder.append("]");
		return builder.toString();
	}
	
}
