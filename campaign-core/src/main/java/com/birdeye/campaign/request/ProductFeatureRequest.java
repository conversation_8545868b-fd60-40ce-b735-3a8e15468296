package com.birdeye.campaign.request;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ProductFeatureRequest implements Serializable {
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1504150089370527322L;
	private Integer				presenceEnabled;
	private Integer				manageServiceEnabled;
	private Integer				messengerEnabled;
	private Integer				websiteIntegration;
	private Integer				businessPresenceSource;
	private Integer				enableReferral;
	private Integer				bulkCustomSms;
	private Integer				liveChat;
	private Integer				chatbot;
	private Integer				isSocialEnabled;
	private Integer				isSocialCrmEnabled;
	private Integer				presenceOpted;
	private Integer				isSurveyEnabled;
	private Integer				chatWidgetEnabled;
	private Integer				googleSellerRating;
	private Integer				professionalService;
	private Integer				competitiveAnalysis;
	private Integer				analyticsEnabled;
	private Integer				basicIntegration;
	private Integer				premiumIntegration;
	private Integer				businessId;
	private Long				businessNumber;
	private Integer				isSuspectSupportOn;
	private Integer             appointmentRemindersEnabled;
	private Integer             appointmentRecallEnabled;
	private Integer				appointmentFormEnabled;
	private Integer				appointmentSchedulingEnabled;
	private Integer 			locationLevelTemplateEnabled;
	private Integer				reviewGenEnabled;
	private Integer             campaignLocationUserAccess;
	
	public ProductFeatureRequest() {
		super();
	}

	public Integer getPresenceEnabled() {
		return presenceEnabled;
	}
	
	public void setPresenceEnabled(Integer presenceEnabled) {
		this.presenceEnabled = presenceEnabled;
	}
	
	public Integer getManageServiceEnabled() {
		return manageServiceEnabled;
	}
	
	public void setManageServiceEnabled(Integer manageServiceEnabled) {
		this.manageServiceEnabled = manageServiceEnabled;
	}
	
	public Integer getMessengerEnabled() {
		return messengerEnabled;
	}
	
	public void setMessengerEnabled(Integer messengerEnabled) {
		this.messengerEnabled = messengerEnabled;
	}
	
	public Integer getWebsiteIntegration() {
		return websiteIntegration;
	}
	
	public void setWebsiteIntegration(Integer websiteIntegration) {
		this.websiteIntegration = websiteIntegration;
	}
	
	public Integer getBusinessPresenceSource() {
		return businessPresenceSource;
	}
	
	public void setBusinessPresenceSource(Integer businessPresenceSource) {
		this.businessPresenceSource = businessPresenceSource;
	}
	
	public Integer getEnableReferral() {
		return enableReferral;
	}
	
	public void setEnableReferral(Integer enableReferral) {
		this.enableReferral = enableReferral;
	}
	
	public Integer getBulkCustomSms() {
		return bulkCustomSms;
	}
	
	public void setBulkCustomSms(Integer bulkCustomSms) {
		this.bulkCustomSms = bulkCustomSms;
	}
	
	public Integer getLiveChat() {
		return liveChat;
	}
	
	public void setLiveChat(Integer liveChat) {
		this.liveChat = liveChat;
	}
	
	public Integer getChatbot() {
		return chatbot;
	}
	
	public void setChatbot(Integer chatbot) {
		this.chatbot = chatbot;
	}
	
	public Integer getIsSocialEnabled() {
		return isSocialEnabled;
	}
	
	public void setIsSocialEnabled(Integer isSocialEnabled) {
		this.isSocialEnabled = isSocialEnabled;
	}
	
	public Integer getIsSocialCrmEnabled() {
		return isSocialCrmEnabled;
	}
	
	public void setIsSocialCrmEnabled(Integer isSocialCrmEnabled) {
		this.isSocialCrmEnabled = isSocialCrmEnabled;
	}
	
	public Integer getPresenceOpted() {
		return presenceOpted;
	}
	
	public void setPresenceOpted(Integer presenceOpted) {
		this.presenceOpted = presenceOpted;
	}
	
	public Integer getIsSurveyEnabled() {
		return isSurveyEnabled;
	}
	
	public void setIsSurveyEnabled(Integer isSurveyEnabled) {
		this.isSurveyEnabled = isSurveyEnabled;
	}
	
	public Integer getChatWidgetEnabled() {
		return chatWidgetEnabled;
	}
	
	public void setChatWidgetEnabled(Integer chatWidgetEnabled) {
		this.chatWidgetEnabled = chatWidgetEnabled;
	}
	
	public Integer getGoogleSellerRating() {
		return googleSellerRating;
	}
	
	public void setGoogleSellerRating(Integer googleSellerRating) {
		this.googleSellerRating = googleSellerRating;
	}
	
	public Integer getProfessionalService() {
		return professionalService;
	}
	
	public void setProfessionalService(Integer professionalService) {
		this.professionalService = professionalService;
	}
	
	public Integer getCompetitiveAnalysis() {
		return competitiveAnalysis;
	}
	
	public void setCompetitiveAnalysis(Integer competitiveAnalysis) {
		this.competitiveAnalysis = competitiveAnalysis;
	}
	
	public Integer getAnalyticsEnabled() {
		return analyticsEnabled;
	}
	
	public void setAnalyticsEnabled(Integer analyticsEnabled) {
		this.analyticsEnabled = analyticsEnabled;
	}
	
	public Integer getBasicIntegration() {
		return basicIntegration;
	}
	
	public void setBasicIntegration(Integer basicIntegration) {
		this.basicIntegration = basicIntegration;
	}
	
	public Integer getPremiumIntegration() {
		return premiumIntegration;
	}
	
	public void setPremiumIntegration(Integer premiumIntegration) {
		this.premiumIntegration = premiumIntegration;
	}
	
	public Integer getBusinessId() {
		return businessId;
	}
	
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	public Long getBusinessNumber() {
		return businessNumber;
	}
	
	public void setBusinessNumber(Long businessNumber) {
		this.businessNumber = businessNumber;
	}
	
	public Integer getIsSuspectSupportOn() {
		return isSuspectSupportOn;
	}

	public void setIsSuspectSupportOn(Integer isSuspectSupportOn) {
		this.isSuspectSupportOn = isSuspectSupportOn;
	}

	public Integer getAppointmentRemindersEnabled() {
		return appointmentRemindersEnabled;
	}

	public void setAppointmentRemindersEnabled(Integer appointmentRemindersEnabled) {
		this.appointmentRemindersEnabled = appointmentRemindersEnabled;
	}
	
	public Integer getAppointmentSchedulingEnabled() {
		return appointmentSchedulingEnabled;
	}

	public void setAppointmentSchedulingEnabled(Integer appointmentSchedulingEnabled) {
		this.appointmentSchedulingEnabled = appointmentSchedulingEnabled;
	}
	
	public Integer getAppointmentRecallEnabled() {
		return appointmentRecallEnabled;
	}

	public void setAppointmentRecallEnabled(Integer appointmentRecallEnabled) {
		this.appointmentRecallEnabled = appointmentRecallEnabled;
	}

	/**
	 * @return the appointmentFormEnabled
	 */
	public Integer getAppointmentFormEnabled() {
		return appointmentFormEnabled;
	}

	/**
	 * @param appointmentFormEnabled the appointmentFormEnabled to set
	 */
	public void setAppointmentFormEnabled(Integer appointmentFormEnabled) {
		this.appointmentFormEnabled = appointmentFormEnabled;
	}
	
	public Integer getLocationLevelTemplateEnabled() {
		return locationLevelTemplateEnabled;
	}
	
	public void setLocationLevelTemplateEnabled(Integer locationLevelTemplateEnabled) {
		this.locationLevelTemplateEnabled = locationLevelTemplateEnabled;
	}
	
	public Integer getReviewGenEnabled() {
		return reviewGenEnabled;
	}

	public void setReviewGenEnabled(Integer reviewGenEnabled) {
		this.reviewGenEnabled = reviewGenEnabled;
	}

	/**
	 * @return the campaignLocationUserAccess
	 */
	public Integer getCampaignLocationUserAccess() {
		return campaignLocationUserAccess;
	}

	/**
	 * @param campaignLocationUserAccess the campaignLocationUserAccess to set
	 */
	public void setCampaignLocationUserAccess(Integer campaignLocationUserAccess) {
		this.campaignLocationUserAccess = campaignLocationUserAccess;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
