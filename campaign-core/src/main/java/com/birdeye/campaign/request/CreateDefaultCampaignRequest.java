package com.birdeye.campaign.request;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_NULL)
public class CreateDefaultCampaignRequest implements Serializable {
	private static final long	serialVersionUID	= 45987984758937l;
	private Integer				businessId;
	private Long				businessNumber;
	private Integer				enterpriseId;
	private String				businessType;
	private String				accountType;
	private Integer				resellerId;
	private String				name;
	private Integer				userId;
	private String				emailId;
	private Boolean				isSMB;
	private Boolean				isEnterprise;
	private Boolean				isEnterpriseLocation;
	private Boolean				isReseller;
	private List<String>		freeTrialProducts;
	
	public CreateDefaultCampaignRequest() {
	}
	
	public Integer getBusinessId() {
		return businessId;
	}
	
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	public Long getBusinessNumber() {
		return businessNumber;
	}
	
	public void setBusinessNumber(Long businessNumber) {
		this.businessNumber = businessNumber;
	}
	
	public Integer getEnterpriseId() {
		return enterpriseId;
	}
	
	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	
	public String getBusinessType() {
		return businessType;
	}
	
	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}
	
	public String getAccountType() {
		return accountType;
	}
	
	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}
	
	public Integer getResellerId() {
		return resellerId;
	}
	
	public void setResellerId(Integer resellerId) {
		this.resellerId = resellerId;
	}
	
	public String getName() {
		return name;
	}
	
	public void setName(String name) {
		this.name = name;
	}
	
	public String getEmailId() {
		return emailId;
	}
	
	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}
	
	public Integer getUserId() {
		return userId;
	}
	
	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public Boolean getIsSMB() {
		return isSMB;
	}

	public void setIsSMB(Boolean isSMB) {
		this.isSMB = isSMB;
	}

	public Boolean getIsEnterprise() {
		return isEnterprise;
	}

	public void setIsEnterprise(Boolean isEnterprise) {
		this.isEnterprise = isEnterprise;
	}

	public Boolean getIsEnterpriseLocation() {
		return isEnterpriseLocation;
	}

	public void setIsEnterpriseLocation(Boolean isEnterpriseLocation) {
		this.isEnterpriseLocation = isEnterpriseLocation;
	}

	public Boolean getIsReseller() {
		return isReseller;
	}

	public void setIsReseller(Boolean isReseller) {
		this.isReseller = isReseller;
	}
	
	/**
	 * @return the freeTrialProducts
	 */
	public List<String> getFreeTrialProducts() {
		return freeTrialProducts;
	}

	/**
	 * @param freeTrialProducts the freeTrialProducts to set
	 */
	public void setFreeTrialProducts(List<String> freeTrialProducts) {
		this.freeTrialProducts = freeTrialProducts;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("CreateDefaultCampaignRequest [businessId=");
		builder.append(businessId);
		builder.append(", businessNumber=");
		builder.append(businessNumber);
		builder.append(", enterpriseId=");
		builder.append(enterpriseId);
		builder.append(", businessType=");
		builder.append(businessType);
		builder.append(", accountType=");
		builder.append(accountType);
		builder.append(", resellerId=");
		builder.append(resellerId);
		builder.append(", name=");
		builder.append(name);
		builder.append(", userId=");
		builder.append(userId);
		builder.append(", emailId=");
		builder.append(emailId);
		builder.append(", isSMB=");
		builder.append(isSMB);
		builder.append(", isEnterprise=");
		builder.append(isEnterprise);
		builder.append(", isEnterpriseLocation=");
		builder.append(isEnterpriseLocation);
		builder.append(", isReseller=");
		builder.append(isReseller);
		builder.append("]");
		return builder.toString();
	}	
	
}