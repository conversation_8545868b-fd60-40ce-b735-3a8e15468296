/**
 * @file_name MessengerSendEventRequest.java
 * @created_date 27 Jan 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * @file_name MessengerSendEventRequest.java
 * @created_date 27 Jan 2020
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public class MessengerSendEventRequest implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -4965682192649378180L;
	
	private Integer				templateId;
	private Integer				businessId;
	private Integer				enterpriseId;
	private Integer				customerId;
	private String				status;
	
	/**
	 * @return the templateId
	 */
	public Integer getTemplateId() {
		return templateId;
	}
	
	/**
	 * @param templateId
	 *            the templateId to set
	 */
	public void setTemplateId(Integer templateId) {
		this.templateId = templateId;
	}
	
	/**
	 * @return the businessId
	 */
	public Integer getBusinessId() {
		return businessId;
	}
	
	/**
	 * @param businessId
	 *            the businessId to set
	 */
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	/**
	 * @return the enterpriseId
	 */
	public Integer getEnterpriseId() {
		return enterpriseId;
	}
	
	/**
	 * @param enterpriseId
	 *            the enterpriseId to set
	 */
	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	
	/**
	 * @return the customerId
	 */
	public Integer getCustomerId() {
		return customerId;
	}
	
	/**
	 * @param customerId
	 *            the customerId to set
	 */
	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}
	
	/**
	 * @return the status
	 */
	public String getStatus() {
		return status;
	}
	
	/**
	 * @param status
	 *            the status to set
	 */
	public void setStatus(String status) {
		this.status = status;
	}
	
	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("MessengerSendEventRequest [templateId=");
		builder.append(templateId);
		builder.append(", businessId=");
		builder.append(businessId);
		builder.append(", enterpriseId=");
		builder.append(enterpriseId);
		builder.append(", customerId=");
		builder.append(customerId);
		builder.append(", status=");
		builder.append(status);
		builder.append("]");
		return builder.toString();
	}
	
}
