package com.birdeye.campaign.request;

import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateManualCampaignRequest extends CampaignBasicRequest {
	
	private static final long			serialVersionUID	= 8881364353997838242L;
	
	private List<Integer>				audience;
	
	private String						cmpStartAt;									// for drip campaign
	
	private String						cmpEndAt;									// for drip campaign
	
	private Integer						scheduled;									// for scheduled manual campaigns : number of hours to schedule
	
	private List<String>		allowedDays;
	
	private List<String>		exclusionDates;
	
	private String				sendTime;
	
	private CampaignAudienceFilter	audienceFilterCampaign;
	
	private List<Integer>			appointmentAudience;
	
	public List<Integer> getAppointmentAudience() {
		return appointmentAudience;
	}

	public void setAppointmentAudience(List<Integer> appointmentAudience) {
		this.appointmentAudience = appointmentAudience;
	}

	public List<Integer> getAudience() {
		return audience;
	}
	
	public void setAudience(List<Integer> audience) {
		this.audience = audience;
	}
	
	public String getCmpStartAt() {
		return cmpStartAt;
	}
	
	public void setCmpStartAt(String cmpStartAt) {
		this.cmpStartAt = cmpStartAt;
	}
	
	public String getCmpEndAt() {
		return cmpEndAt;
	}
	
	public void setCmpEndAt(String cmpEndAt) {
		this.cmpEndAt = cmpEndAt;
	}
	
	/**
	 * @return the scheduled
	 */
	public Integer getScheduled() {
		return scheduled;
	}
	
	/**
	 * @param scheduled
	 *            the scheduled to set
	 */
	public void setScheduled(Integer scheduled) {
		this.scheduled = scheduled;
	}
	
	public List<String> getAllowedDays() {
		return allowedDays;
	}

	public void setAllowedDays(List<String> allowedDays) {
		this.allowedDays = allowedDays;
	}

	public List<String> getExclusionDates() {
		return exclusionDates;
	}

	public void setExclusionDates(List<String> exclusionDates) {
		this.exclusionDates = exclusionDates;
	}

	public String getSendTime() {
		return sendTime;
	}

	public void setSendTime(String sendTime) {
		this.sendTime = sendTime;
	}

	public CampaignAudienceFilter getAudienceFilterCampaign() {
		return audienceFilterCampaign;
	}

	public void setAudienceFilterCampaign(CampaignAudienceFilter audienceFilterCampaign) {
		this.audienceFilterCampaign = audienceFilterCampaign;
	}

	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return b.setExcludeFieldNames("audience", "appointmentAudience").toString();
	}

}
