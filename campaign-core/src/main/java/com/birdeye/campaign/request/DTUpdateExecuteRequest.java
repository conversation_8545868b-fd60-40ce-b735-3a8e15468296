package com.birdeye.campaign.request;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DTUpdateExecuteRequest implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 4843129663574754715L;
	
	private Integer				accountId;
	
	private List<String>		templateTypes;
	
	private Integer				isOneTimeMigration	= 0;
	
	public DTUpdateExecuteRequest() {
		
	}
	
	public DTUpdateExecuteRequest(Integer accountId, List<String> templateTypes, Integer isOneTimeMigration) {
		this.accountId = accountId;
		this.templateTypes = templateTypes;
		this.isOneTimeMigration = isOneTimeMigration;
	}
	
	public Integer getAccountId() {
		return accountId;
	}
	
	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}
	
	public List<String> getTemplateTypes() {
		return templateTypes;
	}
	
	public void setTemplateTypes(List<String> templateTypes) {
		this.templateTypes = templateTypes;
	}
	
	public Integer getIsOneTimeMigration() {
		return isOneTimeMigration;
	}

	public void setIsOneTimeMigration(Integer isOneTimeMigration) {
		this.isOneTimeMigration = isOneTimeMigration;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
