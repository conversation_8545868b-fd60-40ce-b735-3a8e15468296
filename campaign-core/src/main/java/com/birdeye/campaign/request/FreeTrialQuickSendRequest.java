package com.birdeye.campaign.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FreeTrialQuickSendRequest extends QuickSendRequest {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 3082960569735513197L;
	
	private Object				emailTemplateData;
	
	public Object getEmailTemplateData() {
		return emailTemplateData;
	}
	
	public void setEmailTemplateData(Object emailTemplateData) {
		this.emailTemplateData = emailTemplateData;
	}
	
}
