package com.birdeye.campaign.request;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

public class CampaignUsageInfo implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -9100367399589267700L;
	
	private Long				sent				= 0l;
	
	private Long				delivered;
	
	private Long				opened;
	
	private Long				clicked;
	
	private Long				reviewClicks;
	
	private Double				leads;
	
	private AppointmentUsage	appointmentUsage;
	
	private Long				booked;
	
	private Long				formsFilled;
	
	private Long				uniqueRecipients;
	
	private Double				unsubscribeRate;
	
	private Double				bounceRate;
	
	private Double				spamRate;
	
	private Double 				influenceRate;
	
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class AppointmentUsage implements Serializable {
		
		/**
		 * 
		 */
		private static final long	serialVersionUID	= -8412307955886384395L;
		private Long				confirmedClicks;
		private Long				cancelledClicks;
		private Long				rescheduleClicks;
		
		public Long getConfirmedClicks() {
			return confirmedClicks;
		}
		
		public void setConfirmedClicks(Long confirmedClicks) {
			this.confirmedClicks = confirmedClicks;
		}
		
		public Long getCancelledClicks() {
			return cancelledClicks;
		}
		
		public void setCancelledClicks(Long cancelledClicks) {
			this.cancelledClicks = cancelledClicks;
		}
		
		public Long getRescheduleClicks() {
			return rescheduleClicks;
		}
		
		public void setRescheduleClicks(Long rescheduleClicks) {
			this.rescheduleClicks = rescheduleClicks;
		}
		
		@Override
		public String toString() {
			return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
		}
		
	}
	
	public CampaignUsageInfo() {
		super();
	}
	
	public Long getSent() {
		return sent;
	}
	
	public void setSent(Long sent) {
		this.sent = sent;
	}
	
	public Long getDelivered() {
		return delivered;
	}
	
	public void setDelivered(Long delivered) {
		this.delivered = delivered;
	}
	
	public Long getOpened() {
		return opened;
	}
	
	public void setOpened(Long opened) {
		this.opened = opened;
	}
	
	public Long getClicked() {
		return clicked;
	}
	
	public void setClicked(Long clicked) {
		this.clicked = clicked;
	}
	
	public Long getReviewClicks() {
		return reviewClicks;
	}
	
	public void setReviewClicks(Long reviewClicks) {
		this.reviewClicks = reviewClicks;
	}
	
	public Double getLeads() {
		return leads;
	}
	
	public void setLeads(Double leads) {
		this.leads = leads;
	}
	
	public AppointmentUsage getAppointmentUsage() {
		return appointmentUsage;
	}

	public void setAppointmentUsage(AppointmentUsage appointmentUsage) {
		this.appointmentUsage = appointmentUsage;
	}

	public Long getBooked() {
		return booked;
	}

	public void setBooked(Long booked) {
		this.booked = booked;
	}
	
	public Long getFormsFilled() {
		return formsFilled;
	}

	public void setFormsFilled(Long formsFilled) {
		this.formsFilled = formsFilled;
	}

	public Long getUniqueRecipients() {
		return uniqueRecipients;
	}

	public void setUniqueRecipients(Long uniqueRecipients) {
		this.uniqueRecipients = uniqueRecipients;
	}

	public Double getUnsubscribeRate() {
		return unsubscribeRate;
	}

	public void setUnsubscribeRate(Double unsubscribeRate) {
		this.unsubscribeRate = unsubscribeRate;
	}

	public Double getBounceRate() {
		return bounceRate;
	}

	public void setBounceRate(Double bounceRate) {
		this.bounceRate = bounceRate;
	}

	public Double getSpamRate() {
		return spamRate;
	}

	public void setSpamRate(Double spamRate) {
		this.spamRate = spamRate;
	}

	public Double getInfluenceRate() {
		return influenceRate;
	}

	public void setInfluenceRate(Double influenceRate) {
		this.influenceRate = influenceRate;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}