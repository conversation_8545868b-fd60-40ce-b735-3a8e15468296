package com.birdeye.campaign.request;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReferralSummaryReportRequest implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -8133136292494169887L;
	
	private List<Integer>		businessIds;
	
	private Date				startDate;
	
	private Date				endDate;
	
	public List<Integer> getBusinessIds() {
		return businessIds;
	}
	
	public void setBusinessIds(List<Integer> businessIds) {
		this.businessIds = businessIds;
	}
	
	public Date getStartDate() {
		return startDate;
	}
	
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	
	public Date getEndDate() {
		return endDate;
	}
	
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return b.toString();
	}
	
}