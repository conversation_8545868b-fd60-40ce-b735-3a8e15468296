package com.birdeye.campaign.request;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TemplateStagingActionRequest implements Serializable {
	
	private static final long	serialVersionUID	= 4083141399178172958L;
	
	private Integer				templateStagingId;
	
	private String				userEmailId;
	
	private Integer				status;
	
	public TemplateStagingActionRequest() {
		super();
	}
	
	public TemplateStagingActionRequest(Integer templateStagingId, String userEmailId, Integer status) {
		super();
		this.templateStagingId = templateStagingId;
		this.userEmailId = userEmailId;
		this.status = status;
	}
	
	/**
	 * @return the templateStagingId
	 */
	public Integer getTemplateStagingId() {
		return templateStagingId;
	}
	
	/**
	 * @param templateStagingId
	 *            the templateStagingId to set
	 */
	public void setTemplateStagingId(Integer templateStagingId) {
		this.templateStagingId = templateStagingId;
	}
	
	/**
	 * @return the userEmailId
	 */
	public String getUserEmailId() {
		return userEmailId;
	}
	
	/**
	 * @param userEmailId
	 *            the userEmailId to set
	 */
	public void setUserEmailId(String userEmailId) {
		this.userEmailId = userEmailId;
	}
	
	/**
	 * @return the status
	 */
	public Integer getStatus() {
		return status;
	}
	
	/**
	 * @param status
	 *            the status to set
	 */
	public void setStatus(Integer status) {
		this.status = status;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
