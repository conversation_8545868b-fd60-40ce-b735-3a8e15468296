/**
 * @file_name CustomerUnsubscribeEventRequest.java
 * @created_date 7 Jan 2020
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.request;

import java.io.Serializable;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.constant.Constants;

/**
 * @file_name CustomerUnsubscribeEventRequest.java
 * @created_date 7 Jan 2020
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public class CustomerUnsubscribeEventRequest implements Serializable {
	
	private static final long	serialVersionUID	= 1L;

	private Integer				customerId;
	private String				unsubscribeType;			// unsubscribeType - unsubscribe, bounce etc
	private String				externalStatus;				// externalStatus - success/failure (constants)
	private boolean				emailUnsubscribe;			// true : email, false: sms
	private String				emailCategory;
	
	/**
	 * @return the customerId
	 */
	public Integer getCustomerId() {
		return customerId;
	}
	
	/**
	 * @param customerId
	 *            the customerId to set
	 */
	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}
	
	/**
	 * @return the unsubscribeType
	 */
	public String getUnsubscribeType() {
		return unsubscribeType;
	}
	
	/**
	 * @param unsubscribeType
	 *            the unsubscribeType to set
	 */
	public void setUnsubscribeType(String unsubscribeType) {
		this.unsubscribeType = unsubscribeType;
	}
	
	/**
	 * @return the externalStatus
	 */
	public String getExternalStatus() {
		return externalStatus;
	}
	
	/**
	 * @param externalStatus
	 *            the externalStatus to set
	 */
	public void setExternalStatus(String externalStatus) {
		this.externalStatus = externalStatus;
	}
	
	/**
	 * @return the emailUnsubscribe
	 */
	public boolean isEmailUnsubscribe() {
		return emailUnsubscribe;
	}
	
	/**
	 * @param emailUnsubscribe
	 *            the emailUnsubscribe to set
	 */
	public void setEmailUnsubscribe(boolean emailUnsubscribe) {
		this.emailUnsubscribe = emailUnsubscribe;
	}
	
	/**
	 * @return the emailCategory
	 */
	public String getEmailCategory() {
		return emailCategory;
	}

	/**
	 * @param emailCategory the emailCategory to set
	 */
	public void setEmailCategory(String emailCategory) {
		this.emailCategory = emailCategory;
	}

	/**
	 * @param customerId
	 * @param unsubscribeType
	 * @param externalStatus
	 * @param emailUnsubscribe
	 */
	public CustomerUnsubscribeEventRequest(Integer customerId, String unsubscribeType, String externalStatus, boolean emailUnsubscribe, String emailCategory) {
		super();
		this.customerId = customerId;
		this.unsubscribeType = unsubscribeType;
		this.externalStatus = externalStatus;
		this.emailUnsubscribe = emailUnsubscribe;
		this.emailCategory = StringUtils.equalsIgnoreCase(unsubscribeType, Constants.GROUP_UNSUBSCRIBE) ? emailCategory : StringUtils.EMPTY;
	}
	
	/**
	 * 
	 */
	public CustomerUnsubscribeEventRequest() {
		super();
	}
	
	/*
	 * (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
