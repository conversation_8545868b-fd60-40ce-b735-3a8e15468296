package com.birdeye.campaign.request;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class GenerateReviewsCampaignUsageSummaryRequest implements Serializable {
	
	private static final long	serialVersionUID	= -2026950668833232933L;
	private List<Integer>		accountIds;
	private Integer				accountId;
	private String				resellerCampaignWidgetIndexName;
	
	public GenerateReviewsCampaignUsageSummaryRequest(List<Integer> accountIds, String resellerCampaignWidgetIndexName) {
		super();
		this.accountIds = accountIds;
		this.resellerCampaignWidgetIndexName = resellerCampaignWidgetIndexName;
	}
	
	public GenerateReviewsCampaignUsageSummaryRequest() {
		
	}
	
	public List<Integer> getAccountIds() {
		return accountIds;
	}
	
	public void setAccountIds(List<Integer> accountIds) {
		this.accountIds = accountIds;
	}
	
	public Integer getAccountId() {
		return accountId;
	}
	
	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}
	
	public String getResellerCampaignWidgetIndexName() {
		return resellerCampaignWidgetIndexName;
	}
	
	public void setResellerCampaignWidgetIndexName(String resellerCampaignWidgetIndexName) {
		this.resellerCampaignWidgetIndexName = resellerCampaignWidgetIndexName;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
