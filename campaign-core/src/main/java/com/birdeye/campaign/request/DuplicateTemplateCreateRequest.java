package com.birdeye.campaign.request;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * 
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class DuplicateTemplateCreateRequest implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -3661800292885034668L;
	
	private Integer				templateId;
	
	private Integer				sourceEnterpriseId;
	
	private Integer				destinationEnterpriseId;
	
	private String				source;

	/**
	 * @return the templateId
	 */
	public Integer getTemplateId() {
		return templateId;
	}



	/**
	 * @param templateId the templateId to set
	 */
	public void setTemplateId(Integer templateId) {
		this.templateId = templateId;
	}



	/**
	 * @return the sourceEnterpriseId
	 */
	public Integer getSourceEnterpriseId() {
		return sourceEnterpriseId;
	}



	/**
	 * @param sourceEnterpriseId the sourceEnterpriseId to set
	 */
	public void setSourceEnterpriseId(Integer sourceEnterpriseId) {
		this.sourceEnterpriseId = sourceEnterpriseId;
	}



	/**
	 * @return the destinationEnterpriseId
	 */
	public Integer getDestinationEnterpriseId() {
		return destinationEnterpriseId;
	}



	/**
	 * @param destinationEnterpriseId the destinationEnterpriseId to set
	 */
	public void setDestinationEnterpriseId(Integer destinationEnterpriseId) {
		this.destinationEnterpriseId = destinationEnterpriseId;
	}



	/**
	 * @return the source
	 */
	public String getSource() {
		return source;
	}



	/**
	 * @param source the source to set
	 */
	public void setSource(String source) {
		this.source = source;
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return b.toString();
	}
	
}
