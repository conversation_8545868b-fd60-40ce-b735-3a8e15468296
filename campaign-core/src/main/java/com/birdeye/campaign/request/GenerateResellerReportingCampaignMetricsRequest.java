package com.birdeye.campaign.request;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class GenerateResellerReportingCampaignMetricsRequest implements Serializable {
	
	private static final long	serialVersionUID	= -8786507108581206017L;
	
	private Integer				accountId;
	private List<Integer>		businessIds;
	
	GenerateResellerReportingCampaignMetricsRequest() {
		
	}
	
	public GenerateResellerReportingCampaignMetricsRequest(Integer accountId, List<Integer> businessIds) {
		this.accountId = accountId;
		this.businessIds = businessIds;
	}
	
	public Integer getAccountId() {
		return accountId;
	}
	
	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}
	
	public List<Integer> getBusinessIds() {
		return businessIds;
	}
	
	public void setBusinessIds(List<Integer> businessIds) {
		this.businessIds = businessIds;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
