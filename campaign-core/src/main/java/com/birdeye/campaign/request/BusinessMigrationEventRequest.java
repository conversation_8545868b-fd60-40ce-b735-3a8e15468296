package com.birdeye.campaign.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * 
 * <AUTHOR>
 *
 */

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_NULL)
public class BusinessMigrationEventRequest implements Serializable {
	
	/**
	 * 
	 */
	
	private static final long	serialVersionUID	= 7060561729038186937L;
	
	private Integer				eventId;
	private String				eventType;
	private Integer				sourceBusinessId;
	private Long				sourceBusinessNumber;
	private Integer				sourceEnterpriseId;
	private Long				sourceEnterpriseNumber;
	private Integer				sourceResellerId;
	private String				sourceBusinessType;
	private Integer				targetBusinessId;
	private Long				targetBusinessNumber;
	private Integer				targetEnterpriseId;
	private Long				targetEnterpriseNumber;
	private Integer				targetResellerId;
	private String				targetBusinessType;
	private Integer 			userId;
	
	public BusinessMigrationEventRequest() {
		super();
	}
	
	public BusinessMigrationEventRequest(Integer eventId, String eventType, Integer sourceBusinessId, Long sourceBusinessNumber, Integer sourceEnterpriseId, Long sourceEnterpriseNumber,
			Integer sourceResellerId, String sourceBusinessType, Integer targetBusinessId, Long targetBusinessNumber, Integer targetEnterpriseId, Long targetEnterpriseNumber, Integer targetResellerId,
			String targetBusinessType,Integer userId) {
		this.eventId = eventId;
		this.eventType = eventType;
		this.sourceBusinessId = sourceBusinessId;
		this.sourceBusinessNumber = sourceBusinessNumber;
		this.sourceEnterpriseId = sourceEnterpriseId;
		this.sourceEnterpriseNumber = sourceEnterpriseNumber;
		this.sourceResellerId = sourceResellerId;
		this.sourceBusinessType = sourceBusinessType;
		this.targetBusinessId = targetBusinessId;
		this.targetBusinessNumber = targetBusinessNumber;
		this.targetEnterpriseId = targetEnterpriseId;
		this.targetEnterpriseNumber = targetEnterpriseNumber;
		this.targetResellerId = targetResellerId;
		this.targetBusinessType = targetBusinessType;
		this.userId = userId;
	}
	
	public Integer getEventId() {
		return eventId;
	}
	
	public void setEventId(Integer eventId) {
		this.eventId = eventId;
	}
	
	public String getEventType() {
		return eventType;
	}
	
	public void setEventType(String eventType) {
		this.eventType = eventType;
	}
	
	public Integer getSourceBusinessId() {
		return sourceBusinessId;
	}
	
	public void setSourceBusinessId(Integer sourceBusinessId) {
		this.sourceBusinessId = sourceBusinessId;
	}
	
	public Long getSourceBusinessNumber() {
		return sourceBusinessNumber;
	}
	
	public void setSourceBusinessNumber(Long sourceBusinessNumber) {
		this.sourceBusinessNumber = sourceBusinessNumber;
	}
	
	public Integer getSourceEnterpriseId() {
		return sourceEnterpriseId;
	}
	
	public void setSourceEnterpriseId(Integer sourceEnterpriseId) {
		this.sourceEnterpriseId = sourceEnterpriseId;
	}
	
	public Long getSourceEnterpriseNumber() {
		return sourceEnterpriseNumber;
	}
	
	public void setSourceEnterpriseNumber(Long sourceEnterpriseNumber) {
		this.sourceEnterpriseNumber = sourceEnterpriseNumber;
	}
	
	public Integer getSourceResellerId() {
		return sourceResellerId;
	}
	
	public void setSourceResellerId(Integer sourceResellerId) {
		this.sourceResellerId = sourceResellerId;
	}
	
	public String getSourceBusinessType() {
		return sourceBusinessType;
	}
	
	public void setSourceBusinessType(String sourceBusinessType) {
		this.sourceBusinessType = sourceBusinessType;
	}
	
	public Integer getTargetBusinessId() {
		return targetBusinessId;
	}
	
	public void setTargetBusinessId(Integer targetBusinessId) {
		this.targetBusinessId = targetBusinessId;
	}
	
	public Long getTargetBusinessNumber() {
		return targetBusinessNumber;
	}
	
	public void setTargetBusinessNumber(Long targetBusinessNumber) {
		this.targetBusinessNumber = targetBusinessNumber;
	}
	
	public Integer getTargetEnterpriseId() {
		return targetEnterpriseId;
	}
	
	public void setTargetEnterpriseId(Integer targetEnterpriseId) {
		this.targetEnterpriseId = targetEnterpriseId;
	}
	
	public Long getTargetEnterpriseNumber() {
		return targetEnterpriseNumber;
	}
	
	public void setTargetEnterpriseNumber(Long targetEnterpriseNumber) {
		this.targetEnterpriseNumber = targetEnterpriseNumber;
	}
	
	public Integer getTargetResellerId() {
		return targetResellerId;
	}
	
	public void setTargetResellerId(Integer targetResellerId) {
		this.targetResellerId = targetResellerId;
	}
	
	public String getTargetBusinessType() {
		return targetBusinessType;
	}
	
	public void setTargetBusinessType(String targetBusinessType) {
		this.targetBusinessType = targetBusinessType;
	}
	
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("BusinessMigrationEventRequest [eventId=");
		builder.append(eventId);
		builder.append(", eventType=");
		builder.append(eventType);
		builder.append(", sourceBusinessId=");
		builder.append(sourceBusinessId);
		builder.append(", sourceBusinessNumber=");
		builder.append(sourceBusinessNumber);
		builder.append(", sourceEnterpriseId=");
		builder.append(sourceEnterpriseId);
		builder.append(", sourceEnterpriseNumber=");
		builder.append(sourceEnterpriseNumber);
		builder.append(", sourceResellerId=");
		builder.append(sourceResellerId);
		builder.append(", sourceBusinessType=");
		builder.append(sourceBusinessType);
		builder.append(", targetBusinessId=");
		builder.append(targetBusinessId);
		builder.append(", targetBusinessNumber=");
		builder.append(targetBusinessNumber);
		builder.append(", targetEnterpriseId=");
		builder.append(targetEnterpriseId);
		builder.append(", targetEnterpriseNumber=");
		builder.append(targetEnterpriseNumber);
		builder.append(", targetResellerId=");
		builder.append(targetResellerId);
		builder.append(", targetBusinessType=");
		builder.append(targetBusinessType);
		builder.append("]");
		return builder.toString();
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}
}
