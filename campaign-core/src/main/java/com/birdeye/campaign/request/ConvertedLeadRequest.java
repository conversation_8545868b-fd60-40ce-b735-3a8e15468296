package com.birdeye.campaign.request;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.utils.MaskingUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ConvertedLeadRequest implements Serializable {
	
	private static final long	serialVersionUID	= 734889517769088201L;
	
	@NotNull
	private Integer				customerId;									// cid
	
	@NotNull
	private Integer				customerEcid;
	
	@NotBlank
	private String				customerCode;								// referral code
	
	private String				customerName;
	
	private String				customerPhone;
	
	private String				customerEmail;
	
	@NotNull
	private Integer				businessId;
	
	@NotNull
	private Integer				enterpriseId;
	
	@NotNull
	private ReferrerDTO			referredBy;
	
	public static class ReferrerDTO implements Serializable {
		
		private static final long	serialVersionUID	= -629418324816356309L;
		
		@NotNull
		private Integer				cid;
		
		@NotNull
		private Integer				ecid;
		
		public Integer getCid() {
			return cid;
		}
		
		public void setCid(Integer cid) {
			this.cid = cid;
		}
		
		public Integer getEcid() {
			return ecid;
		}
		
		public void setEcid(Integer ecid) {
			this.ecid = ecid;
		}
		
		@Override
		public String toString() {
			return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
		}
		
	}
	
	public Integer getCustomerId() {
		return customerId;
	}
	
	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}
	
	public Integer getCustomerEcid() {
		return customerEcid;
	}
	
	public void setCustomerEcid(Integer customerEcid) {
		this.customerEcid = customerEcid;
	}
	
	public String getCustomerCode() {
		return customerCode;
	}
	
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	
	public String getCustomerName() {
		return customerName;
	}
	
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
	
	public String getCustomerPhone() {
		return customerPhone;
	}
	
	public void setCustomerPhone(String customerPhone) {
		this.customerPhone = customerPhone;
	}
	
	public String getCustomerEmail() {
		return customerEmail;
	}
	
	public void setCustomerEmail(String customerEmail) {
		this.customerEmail = customerEmail;
	}
	
	public Integer getBusinessId() {
		return businessId;
	}
	
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	public Integer getEnterpriseId() {
		return enterpriseId;
	}
	
	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	
	public ReferrerDTO getReferredBy() {
		return referredBy;
	}
	
	public void setReferredBy(ReferrerDTO referredBy) {
		this.referredBy = referredBy;
	}
	
	@JsonIgnore
	public String getMaskedAndEncodedEmail() {
		return MaskingUtils.maskAndEncodeEmail(this.customerEmail);
	}
	
	@JsonIgnore
	public String getMaskedAndEncodedPhone() {
		return MaskingUtils.maskAndEncodePhone(this.customerPhone);
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE).setExcludeFieldNames("customerName", "customerPhone", "customerEmail");
		b.append("customerEmail", getMaskedAndEncodedEmail()).append("customerPhone", getMaskedAndEncodedPhone());
		return b.toString();
	}
	
}
