package com.birdeye.campaign.request.core;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class BusinessDataBatchGetRequest implements Serializable {
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 8763413168135197176L;
	
	private List<Integer>		businessIds;
	
	private List<Long>			businessNumbers;
	
	public List<Integer> getBusinessIds() {
		return businessIds;
	}
	
	public void setBusinessIds(List<Integer> businessIds) {
		this.businessIds = businessIds;
	}
	
	public List<Long> getBusinessNumbers() {
		return businessNumbers;
	}
	
	public void setBusinessNumbers(List<Long> businessNumbers) {
		this.businessNumbers = businessNumbers;
	}
}
