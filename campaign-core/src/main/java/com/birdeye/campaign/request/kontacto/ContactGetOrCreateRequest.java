package com.birdeye.campaign.request.kontacto;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.response.kontacto.KontactoCustomFieldDTO;
import com.birdeye.campaign.utils.MaskingUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ContactGetOrCreateRequest  implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 265406496021841189L;

	private Integer							businessId;
	
	private String							emailId;
	
	private String							phone;
	
	private Location						location;
	
	private String							name;
	
	private String							source;
	
	private List<KontactoCustomFieldDTO>	customFieldValues;
	
	private Map<String, String>				additionalParams;
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class Location {
		private String countryCode;
		
		public String getCountryCode() {
			return countryCode;
		}
		
		public void setCountryCode(String countryCode) {
			this.countryCode = countryCode;
		}
		
		@Override
		public String toString() {
			ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
			return b.toString();
		}
	}
	
	public Integer getBusinessId() {
		return businessId;
	}
	
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	public String getEmailId() {
		return emailId;
	}
	
	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}
	
	public String getPhone() {
		return phone;
	}
	
	public void setPhone(String phone) {
		this.phone = phone;
	}
	
	public Location getLocation() {
		return location;
	}
	
	public void setLocation(Location location) {
		this.location = location;
	}
	
	public String getName() {
		return name;
	}
	
	public void setName(String name) {
		this.name = name;
	}
	
	public String getSource() {
		return source;
	}
	
	public void setSource(String source) {
		this.source = source;
	}
	
	public List<KontactoCustomFieldDTO> getCustomFieldValues() {
		return customFieldValues;
	}
	
	public void setCustomFieldValues(List<KontactoCustomFieldDTO> customFieldValues) {
		this.customFieldValues = customFieldValues;
	}
	
	/**
	 * @return the additionalParams
	 */
	public Map<String, String> getAdditionalParams() {
		return additionalParams;
	}

	/**
	 * @param additionalParams the additionalParams to set
	 */
	public void setAdditionalParams(Map<String, String> additionalParams) {
		this.additionalParams = additionalParams;
	}
	
	@JsonIgnore
	public String getMaskedAndEncodedEmail() {
		return MaskingUtils.maskAndEncodeEmail(this.emailId);
	}
	
	@JsonIgnore
	public String getMaskedAndEncodedPhone() {
		return MaskingUtils.maskAndEncodePhone(this.phone);
	}

	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE).setExcludeFieldNames("emailId", "phone", "name", "customFieldValues");
		b.append("emailId", getMaskedAndEncodedEmail()).append("phone", getMaskedAndEncodedPhone());
		return b.toString();
	}
	
}
