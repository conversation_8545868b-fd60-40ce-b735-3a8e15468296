package com.birdeye.campaign.enums;

public enum BusinessAccountTypeEnum {
	DIRECT(1, "Direct"), COBRANDED(2, "Cobranded"), WHITELABELED(3, "Whitelabel");

	private int value;
	private String type;

	private BusinessAccountTypeEnum(int value, String type) {
		this.value = value;
		this.type = type;
	}

	public int getValue() {
		return value;
	}

	public String getType() {
		return type;
	}

	public static Integer getValue(String type) {
		Integer val = null;
		for (BusinessAccountTypeEnum accountType : values()) {
			if (accountType.getType().equalsIgnoreCase(type)) {
				val = accountType.getValue();
				break;
			}
		}
		return val;
	}

	public static String getType(Integer value) {
		String type = null;
		for (BusinessAccountTypeEnum accountType : values()) {
			if (accountType.getValue() == value) {
				type = accountType.getType();
				break;
			}
		}
		return type;
	}
}
