package com.birdeye.campaign.enums;

public enum BusinessTypeEnum {
	BUSINESS("Business"), ENTERPRISE("Enterprise"), ENTERPRISE_LOCATION("Enterprise-Location"),
	ENTERPRISE_PRODUCT("Enterprise-Product"), RESELLER("Reseller"), PRODUCT("Product"),
	ENTERPRISE_LOCATION_COMPETITOR("Enterprise-Location-Competitor"),
	ENTERPRISE_PRODUCT_COMPETITOR("Enterprise-Product-Competitor"), HIERARCHY_NODE("Hierarchy-Node");

	private String businessType;

	private BusinessTypeEnum(String businessType) {
		this.businessType = businessType;
	}

	public String getBusinessType() {
		return businessType;
	}

	public static BusinessTypeEnum getByName(String name) {
		for (BusinessTypeEnum prop : values()) {
			if (prop.getBusinessType().equals(name)) {
				return prop;
			}
		}
		throw new IllegalArgumentException(name + " is not a valid PropName");
	}
}
