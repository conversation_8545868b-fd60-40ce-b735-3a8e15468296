package com.birdeye.campaign.enums;

import org.apache.commons.lang3.StringUtils;

public enum GroupByTimeEnum {
	
	HOURS("hours"), DAYS("days"), WEEKS("weeks"), MONTHS("months");

	private String groupBy;
	
	private GroupByTimeEnum(String groupBy) {
		this.groupBy = groupBy;
	}
	
	/**
	 * @return the groupBy
	 */
	public String getGroupBy() {
		return groupBy;
	}
	
	public static GroupByTimeEnum getGroupByTimeEnum(String groupByTime) {
		for (GroupByTimeEnum groupByTimeEnum : GroupByTimeEnum.values()) {
			if (StringUtils.equalsIgnoreCase(groupByTimeEnum.getGroupBy(), groupByTime)) {
				return groupByTimeEnum;
			}
		}
		return null;
	}
}
