package com.birdeye.campaign.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 
 * @file_name EmailTemplateTypes.java
 * @created_date 28 Jan 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public enum EmailTemplateTypes {
	PROMOTION, REVIEW_REQUEST_NEW, SURVEY_REQUEST, CUSTOMER_EXPERIENCE, REFERRAL, APPOINTMENT_REMINDER, APPOINTMENT_RECALL,  QR_REVIEW, APPOINTMENT_FORM;
	
	public static boolean isEnumValid(String enumValue) {
		EmailTemplateTypes type = EmailTemplateTypes.valueOf(enumValue);
		if (type == null) {
			return false;
		}
		return true;
	}
	
	public static EmailTemplateTypes getEnumByName(String name) {
		for (EmailTemplateTypes type : values()) {
			if (StringUtils.equalsIgnoreCase(type.name(), name)) {
				return type;
			}
		}
		return null;
	}
	
}
