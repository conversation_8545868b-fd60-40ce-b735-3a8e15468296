package com.birdeye.campaign.enums;

import java.util.HashMap;
import java.util.Map;

public enum SentimentCheckTypeEnum {
	
	STAR("star"), NPS("nps"), YES_NO("yes_no"), SENTIMENT("sentiment");
	
	private final String type;
	
	private SentimentCheckTypeEnum(String type) {
		this.type = type;
	}
	
	public String getType() {
		return type;
	}
	
	private static final Map<String, SentimentCheckTypeEnum> map;
	static {
		map = new HashMap<String, SentimentCheckTypeEnum>();
		for (SentimentCheckTypeEnum v : SentimentCheckTypeEnum.values()) {
			map.put(v.type, v);
		}
	}
	
	public static SentimentCheckTypeEnum findByKey(String s) {
		return map.get(s);
	}
	
}
