package com.birdeye.campaign.enums;

import org.apache.commons.lang3.StringUtils;

public enum CustomFieldSourceEnum {
	CONTACT("contact"), APPOINTMENT("appointment"), LOCATION("location");
	
	private String type;
	
	private CustomFieldSourceEnum(String type) {
		this.type = type;
	}
	
	public String getType() {
		return type;
	}
	
	public static String getType(String customFieldSource) {
		String type = null;
		for (CustomFieldSourceEnum source : values()) {
			if (StringUtils.equals(customFieldSource, source.getType())) {
				type = source.getType();
				break;
			}
		}
		return type;
	}
}
