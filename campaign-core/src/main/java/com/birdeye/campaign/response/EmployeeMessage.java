package com.birdeye.campaign.response;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.platform.entity.User;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EmployeeMessage implements Serializable {
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -3777617986283938735L;
	
	private Integer				id;
	private String				firstName;
	private String				lastName;
	private String				emailId;
	private String				phone;
	private String				imageUrl;
	private String				name;
	private List<Integer>		businessIds;
	
	
	
	public EmployeeMessage() {
		super();
	}

	public EmployeeMessage(User employee) {
		if (employee != null) {
			this.id = employee.getId();
			this.firstName = employee.getFirstName();
			this.lastName = employee.getLastName();
			this.imageUrl = employee.getImageUrl();
			this.phone = employee.getPhone();
			this.emailId = employee.getEmailId();
			this.name=getEmployeeName(employee, true);
		}
	}
	
	public EmployeeMessage(String name) {
		this.name = name;
	}
	
	public Integer getId() {
		return id;
	}
	
	public void setId(Integer id) {
		this.id = id;
	}
	
	public String getFirstName() {
		return firstName;
	}
	
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}
	
	public String getLastName() {
		return lastName;
	}
	
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}
	
	public String getEmailId() {
		return emailId;
	}
	
	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}
	
	public String getPhone() {
		return phone;
	}
	
	public void setPhone(String phone) {
		this.phone = phone;
	}
	
	public String getImageUrl() {
		return imageUrl;
	}
	
	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}
	
	public String getName() {
		return name;
	}
	
	public void setName(String name) {
		this.name = name;
	}
	
	public List<Integer> getBusinessIds() {
		return businessIds;
	}
	
	public void setBusinessIds(List<Integer> businessIds) {
		this.businessIds = businessIds;
	}
	
	private String getEmployeeName(User employee, boolean deriveName) {
        String name = StringUtils.isNotBlank(employee.getFirstName()) ?  employee.getFirstName() :"";
        if(StringUtils.isBlank(name) && StringUtils.isNotBlank(employee.getLastName())){
            name = employee.getLastName();
        }else{
            name = name + (StringUtils.isNotBlank(employee.getLastName()) ? ( " " + employee.getLastName()) : "");
        }
        if(deriveName && StringUtils.isBlank(name) && StringUtils.isNotBlank(emailId)){
        	if(StringUtils.contains(emailId, "@")){
        		name = employee.getEmailId().substring(0, employee.getEmailId().indexOf("@"));
        	}else{
        		name = employee.getEmailId();
        	}
        }
        return name;
    }
	
	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return b.toString();
	}
	
}