package com.birdeye.campaign.response;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class ExternalReferralUsageSentReportResponse implements Serializable {
	
	private static final long	serialVersionUID	= 8787819575045753981L;
	
	private String				sentTo;
	
	private String				sentViaSource;
	
	private String				sentOn;
	
	private Boolean				leadGenerated;
	
	private String				referralCode;
	
	private String				businessName;
	
	public ExternalReferralUsageSentReportResponse() {
		
	}
	
	public ExternalReferralUsageSentReportResponse(String sentTo, String sentViaSource, String sentOn, Boolean leadGenerated, String referralCode, String businessName) {
		this.sentTo = sentTo;
		this.sentViaSource = sentViaSource;
		this.sentOn = sentOn;
		this.leadGenerated = leadGenerated;
		this.referralCode = referralCode;
		this.businessName = businessName;
	}

	public String getSentTo() {
		return sentTo;
	}
	
	public void setSentTo(String sentTo) {
		this.sentTo = sentTo;
	}
	
	public String getSentViaSource() {
		return sentViaSource;
	}
	
	public void setSentViaSource(String sentViaSource) {
		this.sentViaSource = sentViaSource;
	}
	
	public String getSentOn() {
		return sentOn;
	}
	
	public void setSentOn(String sentOn) {
		this.sentOn = sentOn;
	}
	
	public Boolean getLeadGenerated() {
		return leadGenerated;
	}
	
	public void setLeadGenerated(Boolean leadGenerated) {
		this.leadGenerated = leadGenerated;
	}
	
	public String getReferralCode() {
		return referralCode;
	}
	
	public void setReferralCode(String referralCode) {
		this.referralCode = referralCode;
	}
	
	public String getBusinessName() {
		return businessName;
	}
	
	public void setBusinessName(String businessName) {
		this.businessName = businessName;
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return b.toString();
	}
}
