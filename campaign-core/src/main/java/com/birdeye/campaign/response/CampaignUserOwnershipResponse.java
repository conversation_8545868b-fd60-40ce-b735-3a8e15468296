package com.birdeye.campaign.response;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class CampaignUserOwnershipResponse implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 299752723385786487L;
	
	private Integer				automationCount;
	
	private Integer				splitAutomationCount;
	
	private Boolean				hasCampaignCount	= false;
	
	public CampaignUserOwnershipResponse() {
		
	}
	
	public CampaignUserOwnershipResponse(Integer automationCount, Integer splitAutomationCount, Boolean hasCampaignCount) {
		this.automationCount = automationCount;
		this.splitAutomationCount = splitAutomationCount;
		this.hasCampaignCount = hasCampaignCount;
	}
	
	/**
	 * @return the automationCount
	 */
	public Integer getAutomationCount() {
		return automationCount;
	}
	
	/**
	 * @param automationCount
	 *            the automationCount to set
	 */
	public void setAutomationCount(Integer automationCount) {
		this.automationCount = automationCount;
	}
	
	/**
	 * @return the splitAutomationCount
	 */
	public Integer getSplitAutomationCount() {
		return splitAutomationCount;
	}
	
	/**
	 * @param splitAutomationCount
	 *            the splitAutomationCount to set
	 */
	public void setSplitAutomationCount(Integer splitAutomationCount) {
		this.splitAutomationCount = splitAutomationCount;
	}

	/**
	 * @return the hasCampaignCount
	 */
	public Boolean getHasCampaignCount() {
		return hasCampaignCount;
	}

	/**
	 * @param hasCampaignCount the hasCampaignCount to set
	 */
	public void setHasCampaignCount(Boolean hasCampaignCount) {
		this.hasCampaignCount = hasCampaignCount;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
