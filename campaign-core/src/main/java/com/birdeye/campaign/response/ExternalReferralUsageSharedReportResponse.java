package com.birdeye.campaign.response;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class ExternalReferralUsageSharedReportResponse implements Serializable {
	
	private static final long	serialVersionUID	= -7721793524784019425L;
	
	private String				sharedBy;
	
	private String				sharedViaSource;
	
	private String				lastSharedOn;
	
	private Boolean				leadGenerated;
	
	private String				referralCode;
	
	private String				businessName;
	
	public ExternalReferralUsageSharedReportResponse() {
		
	}
	
	public ExternalReferralUsageSharedReportResponse(String sharedBy, String sharedViaSource, String lastSharedOn, Boolean leadGenerated, String referralCode, String businessName) {
		this.sharedBy = sharedBy;
		this.sharedViaSource = sharedViaSource;
		this.lastSharedOn = lastSharedOn;
		this.leadGenerated = leadGenerated;
		this.referralCode = referralCode;
		this.businessName = businessName;
	}

	public String getSharedBy() {
		return sharedBy;
	}
	
	public void setSharedBy(String sharedBy) {
		this.sharedBy = sharedBy;
	}
	
	public String getSharedViaSource() {
		return sharedViaSource;
	}
	
	public void setSharedViaSource(String sharedViaSource) {
		this.sharedViaSource = sharedViaSource;
	}
	
	public String getLastSharedOn() {
		return lastSharedOn;
	}
	
	public void setLastSharedOn(String lastSharedOn) {
		this.lastSharedOn = lastSharedOn;
	}
	
	public Boolean getLeadGenerated() {
		return leadGenerated;
	}
	
	public void setLeadGenerated(Boolean leadGenerated) {
		this.leadGenerated = leadGenerated;
	}
	
	public String getReferralCode() {
		return referralCode;
	}
	
	public void setReferralCode(String referralCode) {
		this.referralCode = referralCode;
	}
	
	public String getBusinessName() {
		return businessName;
	}
	
	public void setBusinessName(String businessName) {
		this.businessName = businessName;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
