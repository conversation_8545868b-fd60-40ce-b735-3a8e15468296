package com.birdeye.campaign.response;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.dto.AppointmentScheduleInfo;
import com.birdeye.campaign.dto.RuleExpression;
import com.birdeye.campaign.dto.SplitCampaignVariantData;
import com.birdeye.campaign.request.CampaignEditInfo;
import com.birdeye.campaign.request.CampaignUsageInfo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SplitAutomationViewDetailsResponse extends SplitCampaignBasicResponse implements Serializable {
	
	private static final long				serialVersionUID	= 8777362782455205195L;
	
	private String							triggerType;
	
	private String							lvlAlias;
	
	private String							lvlAliasId;
	
	private List<String>					lvlIds;
	
	private List<String>					sources;
	
	private RuleExpression					expression;
	
	private CampaignUsageInfo				usageInfo;
	
	private CampaignEditInfo				editInfo;
	
	private RuleExpression					triggerExpression;
	
	private List<AppointmentScheduleInfo>	scheduleInfo;
	
	private String							contactEventType;
	
	private List<SplitCampaignVariantData>	variants;
	
	private EditInfoResponse				auditLogInfo;
	
	/**
	 * @return the triggerType
	 */
	public String getTriggerType() {
		return triggerType;
	}
	
	/**
	 * @param triggerType
	 *            the triggerType to set
	 */
	public void setTriggerType(String triggerType) {
		this.triggerType = triggerType;
	}
	
	/**
	 * @return the lvlAlias
	 */
	public String getLvlAlias() {
		return lvlAlias;
	}
	
	/**
	 * @param lvlAlias
	 *            the lvlAlias to set
	 */
	public void setLvlAlias(String lvlAlias) {
		this.lvlAlias = lvlAlias;
	}
	
	/**
	 * @return the lvlAliasId
	 */
	public String getLvlAliasId() {
		return lvlAliasId;
	}
	
	/**
	 * @param lvlAliasId
	 *            the lvlAliasId to set
	 */
	public void setLvlAliasId(String lvlAliasId) {
		this.lvlAliasId = lvlAliasId;
	}
	
	/**
	 * @return the lvlIds
	 */
	public List<String> getLvlIds() {
		return lvlIds;
	}
	
	/**
	 * @param lvlIds
	 *            the lvlIds to set
	 */
	public void setLvlIds(List<String> lvlIds) {
		this.lvlIds = lvlIds;
	}
	
	/**
	 * @return the sources
	 */
	public List<String> getSources() {
		return sources;
	}
	
	/**
	 * @param sources
	 *            the sources to set
	 */
	public void setSources(List<String> sources) {
		this.sources = sources;
	}
	
	/**
	 * @return the expression
	 */
	public RuleExpression getExpression() {
		return expression;
	}
	
	/**
	 * @param expression
	 *            the expression to set
	 */
	public void setExpression(RuleExpression expression) {
		this.expression = expression;
	}
	
	/**
	 * @return the usageInfo
	 */
	public CampaignUsageInfo getUsageInfo() {
		return usageInfo;
	}
	
	/**
	 * @param usageInfo
	 *            the usageInfo to set
	 */
	public void setUsageInfo(CampaignUsageInfo usageInfo) {
		this.usageInfo = usageInfo;
	}
	
	/**
	 * @return the editInfo
	 */
	public CampaignEditInfo getEditInfo() {
		return editInfo;
	}
	
	/**
	 * @param editInfo
	 *            the editInfo to set
	 */
	public void setEditInfo(CampaignEditInfo editInfo) {
		this.editInfo = editInfo;
	}
	
	/**
	 * @return the triggerExpression
	 */
	public RuleExpression getTriggerExpression() {
		return triggerExpression;
	}
	
	/**
	 * @param triggerExpression
	 *            the triggerExpression to set
	 */
	public void setTriggerExpression(RuleExpression triggerExpression) {
		this.triggerExpression = triggerExpression;
	}
	
	/**
	 * @return the scheduleInfo
	 */
	public List<AppointmentScheduleInfo> getScheduleInfo() {
		return scheduleInfo;
	}
	
	/**
	 * @param scheduleInfo
	 *            the scheduleInfo to set
	 */
	public void setScheduleInfo(List<AppointmentScheduleInfo> scheduleInfo) {
		this.scheduleInfo = scheduleInfo;
	}
	
	/**
	 * @return the contactEventType
	 */
	public String getContactEventType() {
		return contactEventType;
	}
	
	/**
	 * @param contactEventType
	 *            the contactEventType to set
	 */
	public void setContactEventType(String contactEventType) {
		this.contactEventType = contactEventType;
	}
	
	/**
	 * @return the variants
	 */
	public List<SplitCampaignVariantData> getVariants() {
		return variants;
	}
	
	/**
	 * @param variants
	 *            the variants to set
	 */
	public void setVariants(List<SplitCampaignVariantData> variants) {
		this.variants = variants;
	}
	
	/**
	 * @return the auditLogInfo
	 */
	public EditInfoResponse getAuditLogInfo() {
		return auditLogInfo;
	}

	/**
	 * @param auditLogInfo the auditLogInfo to set
	 */
	public void setAuditLogInfo(EditInfoResponse auditLogInfo) {
		this.auditLogInfo = auditLogInfo;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
