package com.birdeye.campaign.response;

import java.io.Serializable;
import java.util.Comparator;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EmployeeAnalyticMessage implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -4023605211264399914L;
	private Integer			count;
	private EmployeeMessage	employee; 
	private Integer			openCount;
	private Integer			clickCount;
	
	public EmployeeAnalyticMessage() {
		super();
	}
	
	public EmployeeAnalyticMessage(Integer count, EmployeeMessage employee) {
		super();
		this.count = count;
		this.employee = employee;
	}
	
	public EmployeeAnalyticMessage(Integer count, Integer openCount, Integer clickCount)
	{
		super();
		this.count=count;
		this.openCount=openCount;
		this.clickCount=clickCount;
	}
	
	public EmployeeAnalyticMessage(Integer count, EmployeeMessage employee, Integer openCount, Integer clickCount) {
		super();
		this.count = count;
		this.employee = employee;
		this.openCount = openCount;
		this.clickCount = clickCount;
		
	}

	public EmployeeMessage getEmployee() {
		return employee;
	}
	
	public void setEmployee(EmployeeMessage employee) {
		this.employee = employee;
	}
	
	public Integer getCount() {
		return count;
	}
	
	public void setCount(Integer count) {
		this.count = count;
	}
	
	public Integer getOpenCount() {
		return openCount;
	}
	
	public void setOpenCount(Integer openCount) {
		this.openCount = openCount;
	}
	
	public Integer getClickCount() {
		return clickCount;
	}
	
	public void setClickCount(Integer clickCount) {
		this.clickCount = clickCount;
	}
	
	
	public static class EmployeeAnalyticCountReportComparator implements Comparator<EmployeeAnalyticMessage> {
		
		@Override
		public int compare(EmployeeAnalyticMessage o1, EmployeeAnalyticMessage o2) {
			if (o1 == null && o2 == null) {
				return 0;
			}
			
			if (o1 == null) {
				return -1;
			}
			
			if (o2 == null) {
				return 1;
			}
			

			if (o1.getEmployee() == null && o2.getEmployee() == null) {
				return 0;
			}
			
			if (o1.getEmployee() == null && o2.getEmployee() != null) {
				return -1;
			}
			
			if (o2.getEmployee() == null && o1.getEmployee() != null) {
				return 1;
			}
			
			int result = Integer.compare(o1.getCount(), o2.getCount());
			if (result == 0) {
				result = StringUtils.compareIgnoreCase(o1.getEmployee().getName(), o2.getEmployee().getName(), true);
			}
			return result;
		}
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return b.toString();
	}
}
