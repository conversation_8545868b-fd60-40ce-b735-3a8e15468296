package com.birdeye.campaign.response;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReferralDashboardResponse implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -2806450687093790429L;
	
	private DashboardResponse	sent;
	private DashboardResponse	shared;
	private DashboardResponse	leads;
	private Integer				activeOngoingCampaigns;
	
	public DashboardResponse getSent() {
		return sent;
	}
	
	public void setSent(DashboardResponse sent) {
		this.sent = sent;
	}
	
	public DashboardResponse getShared() {
		return shared;
	}
	
	public void setShared(DashboardResponse shared) {
		this.shared = shared;
	}
	
	public DashboardResponse getLeads() {
		return leads;
	}
	
	public void setLeads(DashboardResponse leads) {
		this.leads = leads;
	}
	
	public Integer getActiveOngoingCampaigns() {
		return activeOngoingCampaigns;
	}
	
	public void setActiveOngoingCampaigns(Integer activeOngoingCampaigns) {
		this.activeOngoingCampaigns = activeOngoingCampaigns;
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return b.toString();
	}
}
