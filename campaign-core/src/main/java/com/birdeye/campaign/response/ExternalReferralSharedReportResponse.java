package com.birdeye.campaign.response;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class ExternalReferralSharedReportResponse implements Serializable {
	
	private static final long								serialVersionUID	= -7721793524784019425L;
	
	private List<ExternalReferralUsageSharedReportResponse>	data				= new ArrayList<>();
	
	public List<ExternalReferralUsageSharedReportResponse> getData() {
		return data;
	}
	
	public void setData(List<ExternalReferralUsageSharedReportResponse> data) {
		this.data = data;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
