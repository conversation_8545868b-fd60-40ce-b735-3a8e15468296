package com.birdeye.campaign.response;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateCampaignResponse implements Serializable {

	private static final long serialVersionUID = 4148015655863209545L;
	
	private Integer id;
	
	private String campaignName;
	
	private Integer statusId;
	
	public CreateCampaignResponse()
	{
		
	}
	
	public CreateCampaignResponse(Integer id, String campaignName) {
		super();
		this.id = id;
		this.campaignName = campaignName;
	}

	public CreateCampaignResponse(Integer id, String name,Integer statusId)
	{
		this.id=id;
		this.campaignName=name;
		this.statusId=statusId;
	}
	/**
	 * @return the campaignName
	 */
	public String getCampaignName() {
		return campaignName;
	}

	/**
	 * @param campaignName the campaignName to set
	 */
	public void setCampaignName(String campaignName) {
		this.campaignName = campaignName;
	}
	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	
	/**
	 * 
	 * @return statusId
	 */
	public Integer getStatusId() {
		return statusId;
	}
	
	/**
	 * 
	 * @param statusId
	 */
	public void setStatusId(Integer statusId) {
		this.statusId = statusId;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("CreateCampaignResponse [id=");
		builder.append(id);
		builder.append(", campaignName=");
		builder.append(campaignName);
		builder.append(", statusId=");
		builder.append(statusId);
		builder.append("]");
		return builder.toString();
	}
	
}
