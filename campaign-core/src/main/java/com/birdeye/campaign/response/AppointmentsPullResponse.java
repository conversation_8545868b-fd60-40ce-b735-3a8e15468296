package com.birdeye.campaign.response;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * 
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_NULL)
public class AppointmentsPullResponse<T> implements Serializable {
	
	private static final long	serialVersionUID	= 6613233246065518028L;
	
	private Integer				totalCount;
	
	private String				scrollId;
	
	@JsonAlias({ "appointmentList", "recallList" })
	private List<T>				list;
	
	@JsonIgnore
	private String				failureReason;
	
	private Boolean				recallDataPresent;
	
	/**
	 * Default constructor
	 */
	public AppointmentsPullResponse() {
	}
	
	/**
	 * @param failureReason
	 */
	public AppointmentsPullResponse(String failureReason) {
		this.failureReason = failureReason;
	}
	
	/**
	 * @return the totalCount
	 */
	public Integer getTotalCount() {
		return totalCount;
	}
	
	/**
	 * @param totalCount
	 *            the totalCount to set
	 */
	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}
	
	/**
	 * @return the scrollId
	 */
	public String getScrollId() {
		return scrollId;
	}
	
	/**
	 * @param scrollId
	 *            the scrollId to set
	 */
	public void setScrollId(String scrollId) {
		this.scrollId = scrollId;
	}
	
	/**
	 * @return the list
	 */
	public List<T> getList() {
		return list;
	}
	
	/**
	 * @param list
	 *            the list to set
	 */
	public void setList(List<T> list) {
		this.list = list;
	}
	
	/**
	 * @return the failureReason
	 */
	public String getFailureReason() {
		return failureReason;
	}
	
	/**
	 * @param failureReason
	 *            the failureReason to set
	 */
	public void setFailureReason(String failureReason) {
		this.failureReason = failureReason;
	}
	
	public Boolean getRecallDataPresent() {
		return recallDataPresent;
	}

	public void setRecallDataPresent(Boolean recallDataPresent) {
		this.recallDataPresent = recallDataPresent;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
