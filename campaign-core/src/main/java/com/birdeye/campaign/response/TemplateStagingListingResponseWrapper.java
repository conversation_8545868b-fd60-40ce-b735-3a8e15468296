package com.birdeye.campaign.response;

import java.io.Serializable;
import java.util.List;

public class TemplateStagingListingResponseWrapper implements Serializable {
	
	private static final long					serialVersionUID	= -5939668694997421044L;
	
	private Integer								currentPage;
	
	private Integer								totalPages;
	
	private List<TemplateStagingListingMessage>	list;
	
	/**
	 * @return the currentPage
	 */
	public Integer getCurrentPage() {
		return currentPage;
	}

	/**
	 * @param currentPage the currentPage to set
	 */
	public void setCurrentPage(Integer currentPage) {
		this.currentPage = currentPage;
	}

	/**
	 * @return the totalPages
	 */
	public Integer getTotalPages() {
		return totalPages;
	}

	/**
	 * @param totalPages the totalPages to set
	 */
	public void setTotalPages(Integer totalPages) {
		this.totalPages = totalPages;
	}

	/**
	 * @return the list
	 */
	public List<TemplateStagingListingMessage> getList() {
		return list;
	}
	
	/**
	 * @param list
	 *            the list to set
	 */
	public void setList(List<TemplateStagingListingMessage> list) {
		this.list = list;
	}
	
}
