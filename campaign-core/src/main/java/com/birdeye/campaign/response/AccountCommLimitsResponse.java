package com.birdeye.campaign.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AccountCommLimitsResponse implements Serializable {
	
	private static final long	serialVersionUID	= -7405082403717157161L;
	
	private RRCommunication		rrCommunication;
	
	public AccountCommLimitsResponse() {
		this.rrCommunication = new RRCommunication(new LimitInfo(false, 0, 0), new LimitInfo(false, 0, 0));
	}
	
	public RRCommunication getRrCommunication() {
		return rrCommunication;
	}
	
	public void setRrCommunication(RRCommunication rrCommunication) {
		this.rrCommunication = rrCommunication;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class RRCommunication implements Serializable {
		/**
		 * 
		 */
		private static final long	serialVersionUID	= -1309714271221264278L;
		
		private LimitInfo			daily;
		
		private LimitInfo			overall;
		
		public RRCommunication() {
			
		}
		
		public RRCommunication(LimitInfo daily, LimitInfo overall) {
			this.daily = daily;
			this.overall = overall;
		}
		
		public LimitInfo getDaily() {
			return daily;
		}
		
		public void setDaily(LimitInfo daily) {
			this.daily = daily;
		}
		
		public LimitInfo getOverall() {
			return overall;
		}
		
		public void setOverall(LimitInfo overall) {
			this.overall = overall;
		}
		
		@Override
		public String toString() {
			return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
		}
	}
	
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class LimitInfo implements Serializable {
		/**
		 * 
		 */
		private static final long	serialVersionUID	= 4862184919292042618L;
		
		private boolean				isLimitReached;
		
		private Integer				limit;
		
		private Integer				used;
		
		public LimitInfo() {
			
		}
		
		public LimitInfo(boolean isLimitReached, Integer limit, Integer used) {
			this.isLimitReached = isLimitReached;
			this.limit = limit;
			this.used = used;
		}
		
		public boolean getIsLimitReached() {
			return isLimitReached;
		}
		
		public void setIsLimitReached(boolean limitReached) {
			this.isLimitReached = limitReached;
		}
		
		public Integer getLimit() {
			return limit;
		}
		
		public void setLimit(Integer limit) {
			this.limit = limit;
		}
		
		public Integer getUsed() {
			return used;
		}
		
		public void setUsed(Integer used) {
			this.used = used;
		}
		
		@Override
		public String toString() {
			return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
		}
	}
}
