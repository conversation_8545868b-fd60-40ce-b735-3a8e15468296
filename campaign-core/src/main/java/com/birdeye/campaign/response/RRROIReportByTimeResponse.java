package com.birdeye.campaign.response;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class RRROIReportByTimeResponse implements Serializable {
	
	private static final long					serialVersionUID	= 5334264750559954793L;
	
	private ReviewsSummaryResponse				summary;
	private List<RRROIReportByTimeDataPoint>	dataPoints;
	
	public RRROIReportByTimeResponse() {
		
	}
	
	public RRROIReportByTimeResponse(ReviewsSummaryResponse summary, List<RRROIReportByTimeDataPoint> dataPoints) {
		this.summary = summary;
		this.dataPoints = dataPoints;
	}

	public ReviewsSummaryResponse getSummary() {
		return summary;
	}
	
	public void setSummary(ReviewsSummaryResponse summary) {
		this.summary = summary;
	}
	
	public List<RRROIReportByTimeDataPoint> getDataPoints() {
		return dataPoints;
	}
	
	public void setDataPoints(List<RRROIReportByTimeDataPoint> dataPoints) {
		this.dataPoints = dataPoints;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
