package com.birdeye.campaign.response.kontacto;

import java.io.Serializable;
import java.util.Date;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CommPreferences implements Serializable {
	
	private static final long	serialVersionUID	= -872457502204395085L;
	
	private EmailPreferences	emailPreferences;
	private SmsPreferences	    smsPreferences;
	
	/**
	 * @return the emailPreferences
	 */
	public EmailPreferences getEmailPreferences() {
		return emailPreferences;
	}
	
	/**
	 * @param emailPreferences
	 *            the emailPreferences to set
	 */
	public void setEmailPreferences(EmailPreferences emailPreferences) {
		this.emailPreferences = emailPreferences;
	}
	
	/**
	 * @return the smsPreferences
	 */
	public SmsPreferences getSmsPreferences() {
		return smsPreferences;
	}
	
	public void setSmsPreferences(SmsPreferences smsPreferences) {
		this.smsPreferences = smsPreferences;
	}
	
	public static class SmsPreferences implements Serializable {
		
		private static final long	serialVersionUID	= 8647236359829236533L;
		
		private boolean				marketingOptin;
		
		private String				marketingOptoutType;
		
		private Date				marketingOptoutTimestamp;
		
		private boolean				feedbackOptin;
		
		private String				feedbackOptoutType;
		
		private Date				feedbackOptoutTimestamp;
		
		private boolean				serviceOptin;
		
		private String				serviceOptoutType;
		
		private Date				serviceOptoutTimestamp;
		
		/**
		 * @return the marketingOptin
		 */
		public boolean isMarketingOptin() {
			return marketingOptin;
		}
		
		/**
		 * @param marketingOptin
		 *            the marketingOptin to set
		 */
		public void setMarketingOptin(boolean marketingOptin) {
			this.marketingOptin = marketingOptin;
		}
		
		/**
		 * @return the marketingOptoutType
		 */
		public String getMarketingOptoutType() {
			return marketingOptoutType;
		}
		
		/**
		 * @param marketingOptoutType
		 *            the marketingOptoutType to set
		 */
		public void setMarketingOptoutType(String marketingOptoutType) {
			this.marketingOptoutType = marketingOptoutType;
		}
		
		/**
		 * @return the marketingOptoutTimestamp
		 */
		public Date getMarketingOptoutTimestamp() {
			return marketingOptoutTimestamp;
		}
		
		/**
		 * @param marketingOptoutTimestamp
		 *            the marketingOptoutTimestamp to set
		 */
		public void setMarketingOptoutTimestamp(Date marketingOptoutTimestamp) {
			this.marketingOptoutTimestamp = marketingOptoutTimestamp;
		}
		
		/**
		 * @return the feedbackOptin
		 */
		public boolean isFeedbackOptin() {
			return feedbackOptin;
		}
		
		/**
		 * @param feedbackOptin
		 *            the feedbackOptin to set
		 */
		public void setFeedbackOptin(boolean feedbackOptin) {
			this.feedbackOptin = feedbackOptin;
		}
		
		/**
		 * @return the feedbackOptoutType
		 */
		public String getFeedbackOptoutType() {
			return feedbackOptoutType;
		}
		
		/**
		 * @param feedbackOptoutType
		 *            the feedbackOptoutType to set
		 */
		public void setFeedbackOptoutType(String feedbackOptoutType) {
			this.feedbackOptoutType = feedbackOptoutType;
		}
		
		/**
		 * @return the feedbackOptoutTimestamp
		 */
		public Date getFeedbackOptoutTimestamp() {
			return feedbackOptoutTimestamp;
		}
		
		/**
		 * @param feedbackOptoutTimestamp
		 *            the feedbackOptoutTimestamp to set
		 */
		public void setFeedbackOptoutTimestamp(Date feedbackOptoutTimestamp) {
			this.feedbackOptoutTimestamp = feedbackOptoutTimestamp;
		}
		
		/**
		 * @return the serviceOptin
		 */
		public boolean isServiceOptin() {
			return serviceOptin;
		}
		
		/**
		 * @param serviceOptin
		 *            the serviceOptin to set
		 */
		public void setServiceOptin(boolean serviceOptin) {
			this.serviceOptin = serviceOptin;
		}
		
		/**
		 * @return the serviceOptoutType
		 */
		public String getServiceOptoutType() {
			return serviceOptoutType;
		}
		
		/**
		 * @param serviceOptoutType
		 *            the serviceOptoutType to set
		 */
		public void setServiceOptoutType(String serviceOptoutType) {
			this.serviceOptoutType = serviceOptoutType;
		}
		
		/**
		 * @return the serviceOptoutTimestamp
		 */
		public Date getServiceOptoutTimestamp() {
			return serviceOptoutTimestamp;
		}
		
		/**
		 * @param serviceOptoutTimestamp
		 *            the serviceOptoutTimestamp to set
		 */
		public void setServiceOptoutTimestamp(Date serviceOptoutTimestamp) {
			this.serviceOptoutTimestamp = serviceOptoutTimestamp;
		}
		
		@Override
		public String toString() {
			return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
		}
		
	}
	
	public static class EmailPreferences implements Serializable {
		
		private static final long	serialVersionUID	= 7647236359829236532L;
		
		private boolean				marketingOptin;
		
		private String				marketingOptoutType;
		
		private Date				marketingOptoutTimestamp;
		
		private boolean				feedbackOptin;
		
		private String				feedbackOptoutType;
		
		private Date				feedbackOptoutTimestamp;
		
		private boolean				serviceOptin;
		
		private String				serviceOptoutType;
		
		private Date				serviceOptoutTimestamp;
		
		/**
		 * @return the marketingOptin
		 */
		public boolean isMarketingOptin() {
			return marketingOptin;
		}
		
		/**
		 * @param marketingOptin
		 *            the marketingOptin to set
		 */
		public void setMarketingOptin(boolean marketingOptin) {
			this.marketingOptin = marketingOptin;
		}
		
		/**
		 * @return the marketingOptoutType
		 */
		public String getMarketingOptoutType() {
			return marketingOptoutType;
		}
		
		/**
		 * @param marketingOptoutType
		 *            the marketingOptoutType to set
		 */
		public void setMarketingOptoutType(String marketingOptoutType) {
			this.marketingOptoutType = marketingOptoutType;
		}
		
		/**
		 * @return the marketingOptoutTimestamp
		 */
		public Date getMarketingOptoutTimestamp() {
			return marketingOptoutTimestamp;
		}
		
		/**
		 * @param marketingOptoutTimestamp
		 *            the marketingOptoutTimestamp to set
		 */
		public void setMarketingOptoutTimestamp(Date marketingOptoutTimestamp) {
			this.marketingOptoutTimestamp = marketingOptoutTimestamp;
		}
		
		/**
		 * @return the feedbackOptin
		 */
		public boolean isFeedbackOptin() {
			return feedbackOptin;
		}
		
		/**
		 * @param feedbackOptin
		 *            the feedbackOptin to set
		 */
		public void setFeedbackOptin(boolean feedbackOptin) {
			this.feedbackOptin = feedbackOptin;
		}
		
		/**
		 * @return the feedbackOptoutType
		 */
		public String getFeedbackOptoutType() {
			return feedbackOptoutType;
		}
		
		/**
		 * @param feedbackOptoutType
		 *            the feedbackOptoutType to set
		 */
		public void setFeedbackOptoutType(String feedbackOptoutType) {
			this.feedbackOptoutType = feedbackOptoutType;
		}
		
		/**
		 * @return the feedbackOptoutTimestamp
		 */
		public Date getFeedbackOptoutTimestamp() {
			return feedbackOptoutTimestamp;
		}
		
		/**
		 * @param feedbackOptoutTimestamp
		 *            the feedbackOptoutTimestamp to set
		 */
		public void setFeedbackOptoutTimestamp(Date feedbackOptoutTimestamp) {
			this.feedbackOptoutTimestamp = feedbackOptoutTimestamp;
		}
		
		/**
		 * @return the serviceOptin
		 */
		public boolean isServiceOptin() {
			return serviceOptin;
		}
		
		/**
		 * @param serviceOptin
		 *            the serviceOptin to set
		 */
		public void setServiceOptin(boolean serviceOptin) {
			this.serviceOptin = serviceOptin;
		}
		
		/**
		 * @return the serviceOptoutType
		 */
		public String getServiceOptoutType() {
			return serviceOptoutType;
		}
		
		/**
		 * @param serviceOptoutType
		 *            the serviceOptoutType to set
		 */
		public void setServiceOptoutType(String serviceOptoutType) {
			this.serviceOptoutType = serviceOptoutType;
		}
		
		/**
		 * @return the serviceOptoutTimestamp
		 */
		public Date getServiceOptoutTimestamp() {
			return serviceOptoutTimestamp;
		}
		
		/**
		 * @param serviceOptoutTimestamp
		 *            the serviceOptoutTimestamp to set
		 */
		public void setServiceOptoutTimestamp(Date serviceOptoutTimestamp) {
			this.serviceOptoutTimestamp = serviceOptoutTimestamp;
		}
		
		@Override
		public String toString() {
			return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
		}
		
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
