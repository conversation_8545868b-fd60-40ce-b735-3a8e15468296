package com.birdeye.campaign.response;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
public class ResellerReportingManualReviewsCampaignMetricsResponse implements Serializable {
	
	private static final long	serialVersionUID	= 4157588272877309103L;
	private Integer				accountId;
	private String				manualReviewsCampaignCount;					// count of manual reviews campaign that have sent more than 1 review request in past 30 days
	
	public Integer getAccountId() {
		return accountId;
	}
	
	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}
	
	public String getManualReviewsCampaignCount() {
		return manualReviewsCampaignCount;
	}
	
	public void setManualReviewsCampaignCount(String manualReviewsCampaignCount) {
		this.manualReviewsCampaignCount = manualReviewsCampaignCount;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
