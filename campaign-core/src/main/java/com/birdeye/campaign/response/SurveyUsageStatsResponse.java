package com.birdeye.campaign.response;

import java.io.Serializable;
import java.util.Map;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SurveyUsageStatsResponse implements Serializable {
	
	private static final long			serialVersionUID	= 7670318855310874675L;
	
	private Map<String, SurveyStatsDTO>	surveyStats;
	
	public static class SurveyStatsDTO implements Serializable {
		
		private static final long	serialVersionUID	= -2135738031702273829L;
		
		private long				sent;
		
		private long				opened;
		
		private long				clicked;
		
		/**
		 * @return the sent
		 */
		public long getSent() {
			return sent;
		}
		
		/**
		 * @param sent
		 *            the sent to set
		 */
		public void setSent(long sent) {
			this.sent = sent;
		}
		
		/**
		 * @return the opened
		 */
		public long getOpened() {
			return opened;
		}

		/**
		 * @param opened the opened to set
		 */
		public void setOpened(long opened) {
			this.opened = opened;
		}

		/**
		 * @return the clicked
		 */
		public long getClicked() {
			return clicked;
		}
		
		/**
		 * @param clicked
		 *            the clicked to set
		 */
		public void setClicked(long clicked) {
			this.clicked = clicked;
		}
	}

	/**
	 * @return the sourceViaSurveyStats
	 */
	public Map<String, SurveyStatsDTO> getSurveyStats() {
		return surveyStats;
	}

	/**
	 * @param sourceViaSurveyStats the sourceViaSurveyStats to set
	 */
	public void setSurveyStats(Map<String, SurveyStatsDTO> sourceViaSurveyStats) {
		this.surveyStats = sourceViaSurveyStats;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this,ToStringStyle.JSON_STYLE);
	}
	
}
