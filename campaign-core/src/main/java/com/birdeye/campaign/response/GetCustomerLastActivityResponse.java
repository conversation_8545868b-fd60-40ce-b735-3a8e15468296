/**
 * @file_name GetCustomerLastActivityResponse.java
 * @created_date 5 Aug 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.response;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * @file_name GetCustomerLastActivityResponse.java
 * @created_date 5 Aug 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetCustomerLastActivityResponse implements Serializable {
	
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -512148541881379828L;
	private String lastMessageOn; //Apr 15, 2020

	/**
	 * @return the lastMessageOn
	 */
	public String getLastMessageOn() {
		return lastMessageOn;
	}

	/**
	 * @param lastMessageOn the lastMessageOn to set
	 */
	public void setLastMessageOn(String lastMessageOn) {
		this.lastMessageOn = lastMessageOn;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("GetCustomerLastActivityResponse [lastMessageOn=");
		builder.append(lastMessageOn);
		builder.append("]");
		return builder.toString();
	}

	/**
	 * @param lastMessageOn
	 */
	public GetCustomerLastActivityResponse(String lastMessageOn) {
		super();
		this.lastMessageOn = lastMessageOn;
	}

	/**
	 * 
	 */
	public GetCustomerLastActivityResponse() {
		super();
	}
	
	
}
