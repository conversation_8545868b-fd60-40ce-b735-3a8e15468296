package com.birdeye.campaign.response;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
public class ResellerReportingReferralCampaignMetricsResponse implements Serializable {
	private static final long	serialVersionUID	= -4824731780158030557L;
	private Integer				accountId;
	private String				referralSentCount;							// count of referrals sent in past 30 days
	private String				referralSharedCount;						// count of referrals shared in past 30 days
	
	public Integer getAccountId() {
		return accountId;
	}
	
	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}
	
	public String getReferralSentCount() {
		return referralSentCount;
	}
	
	public void setReferralSentCount(String referralSentCount) {
		this.referralSentCount = referralSentCount;
	}
	
	public String getReferralSharedCount() {
		return referralSharedCount;
	}
	
	public void setReferralSharedCount(String referralSharedCount) {
		this.referralSharedCount = referralSharedCount;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
