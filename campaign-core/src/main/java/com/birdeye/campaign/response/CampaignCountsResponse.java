package com.birdeye.campaign.response;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CampaignCountsResponse implements Serializable {

	private static final long serialVersionUID = 8881364353997838242L;

	private Long ongoingCount=0L;

	private Long manualCount=0L;

	/**
	 * @return the ongoingCount
	 */
	public Long getOngoingCount() {
		return ongoingCount;
	}

	/**
	 * @param ongoingCount
	 *            the ongoingCount to set
	 */
	public void setOngoingCount(Long ongoingCount) {
		this.ongoingCount = ongoingCount;
	}

	/**
	 * @return the manualCount
	 */
	public Long getManualCount() {
		return manualCount;
	}

	/**
	 * @param manualCount
	 *            the manualCount to set
	 */
	public void setManualCount(Long manualCount) {
		this.manualCount = manualCount;
	}
}
