package com.birdeye.campaign.response;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.dto.AppointmentCustomFieldDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_NULL)
public class PullAppointmentRecords implements Serializable {
	
	private static final long				serialVersionUID	= 8969685091142010001L;
	
	private Integer							appointmentId;
	
	private String							extAppointmentId;
	
	private Integer							businessId;
	
	private Long							startTime;
	
	private Long							endTime;
	
	private String							appointmentStatus;
	
	private String							action;
	
	private String							status;
	
	private Boolean							noShow;
	
	private String							source;
	
	private Integer							widgetId;
	
	private Boolean							bookingForSelf;
	
	private String							timezone;
	
	private String							offset;
	
	private String							locale;
	
	private Integer							specialistId;
	
	private String							specialistName;
	
	private Integer							serviceId;
	
	private String							service;
	
	private Integer							cid;
	
	private PatientDetails					patientDetails;
	
	private List<AppointmentCustomFieldDTO>	customFields;
	
	private Integer 						accountId;
	
	
	public Integer getAccountId() {
		return accountId;
	}

	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}

	/**
	 * Default constructor
	 */
	public PullAppointmentRecords() {
	}
	
	/**
	 * @return the appointmentId
	 */
	public Integer getAppointmentId() {
		return appointmentId;
	}
	
	/**
	 * @param appointmentId
	 *            the appointmentId to set
	 */
	public void setAppointmentId(Integer appointmentId) {
		this.appointmentId = appointmentId;
	}
	
	/**
	 * @return the extAppointmentId
	 */
	public String getExtAppointmentId() {
		return extAppointmentId;
	}
	
	/**
	 * @param extAppointmentId
	 *            the extAppointmentId to set
	 */
	public void setExtAppointmentId(String extAppointmentId) {
		this.extAppointmentId = extAppointmentId;
	}
	
	/**
	 * @return the businessId
	 */
	public Integer getBusinessId() {
		return businessId;
	}
	
	/**
	 * @param businessId
	 *            the businessId to set
	 */
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	/**
	 * @return the startTime
	 */
	public Long getStartTime() {
		return startTime;
	}
	
	/**
	 * @param startTime
	 *            the startTime to set
	 */
	public void setStartTime(Long startTime) {
		this.startTime = startTime;
	}
	
	/**
	 * @return the endTime
	 */
	public Long getEndTime() {
		return endTime;
	}
	
	/**
	 * @param endTime
	 *            the endTime to set
	 */
	public void setEndTime(Long endTime) {
		this.endTime = endTime;
	}
	
	/**
	 * @return the appointmentStatus
	 */
	public String getAppointmentStatus() {
		return appointmentStatus;
	}
	
	/**
	 * @param appointmentStatus
	 *            the appointmentStatus to set
	 */
	public void setAppointmentStatus(String appointmentStatus) {
		this.appointmentStatus = appointmentStatus;
	}
	
	/**
	 * @return the action
	 */
	public String getAction() {
		return action;
	}
	
	/**
	 * @param action
	 *            the action to set
	 */
	public void setAction(String action) {
		this.action = action;
	}
	
	/**
	 * @return the status
	 */
	public String getStatus() {
		return status;
	}
	
	/**
	 * @param status
	 *            the status to set
	 */
	public void setStatus(String status) {
		this.status = status;
	}
	
	/**
	 * @return the noShow
	 */
	public Boolean getNoShow() {
		return noShow;
	}
	
	/**
	 * @param noShow
	 *            the noShow to set
	 */
	public void setNoShow(Boolean noShow) {
		this.noShow = noShow;
	}
	
	/**
	 * @return the source
	 */
	public String getSource() {
		return source;
	}
	
	/**
	 * @param source
	 *            the source to set
	 */
	public void setSource(String source) {
		this.source = source;
	}
	
	/**
	 * @return the widgetId
	 */
	public Integer getWidgetId() {
		return widgetId;
	}
	
	/**
	 * @param widgetId
	 *            the widgetId to set
	 */
	public void setWidgetId(Integer widgetId) {
		this.widgetId = widgetId;
	}
	
	/**
	 * @return the bookingForSelf
	 */
	public Boolean getBookingForSelf() {
		return bookingForSelf;
	}
	
	/**
	 * @param bookingForSelf
	 *            the bookingForSelf to set
	 */
	public void setBookingForSelf(Boolean bookingForSelf) {
		this.bookingForSelf = bookingForSelf;
	}
	
	/**
	 * @return the timezone
	 */
	public String getTimezone() {
		return timezone;
	}
	
	/**
	 * @param timezone
	 *            the timezone to set
	 */
	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}
	
	/**
	 * @return the offset
	 */
	public String getOffset() {
		return offset;
	}
	
	/**
	 * @param offset
	 *            the offset to set
	 */
	public void setOffset(String offset) {
		this.offset = offset;
	}
	
	/**
	 * @return the locale
	 */
	public String getLocale() {
		return locale;
	}
	
	/**
	 * @param locale
	 *            the locale to set
	 */
	public void setLocale(String locale) {
		this.locale = locale;
	}
	
	/**
	 * @return the specialistId
	 */
	public Integer getSpecialistId() {
		return specialistId;
	}
	
	/**
	 * @param specialistId
	 *            the specialistId to set
	 */
	public void setSpecialistId(Integer specialistId) {
		this.specialistId = specialistId;
	}
	
	/**
	 * @return the specialistName
	 */
	public String getSpecialistName() {
		return specialistName;
	}
	
	/**
	 * @param specialistName
	 *            the specialistName to set
	 */
	public void setSpecialistName(String specialistName) {
		this.specialistName = specialistName;
	}
	
	/**
	 * @return the serviceId
	 */
	public Integer getServiceId() {
		return serviceId;
	}
	
	/**
	 * @param serviceId
	 *            the serviceId to set
	 */
	public void setServiceId(Integer serviceId) {
		this.serviceId = serviceId;
	}
	
	/**
	 * @return the service
	 */
	public String getService() {
		return service;
	}
	
	/**
	 * @param service
	 *            the service to set
	 */
	public void setService(String service) {
		this.service = service;
	}
	
	/**
	 * @return the cid
	 */
	public Integer getCid() {
		return cid;
	}
	
	/**
	 * @param cid
	 *            the cid to set
	 */
	public void setCid(Integer cid) {
		this.cid = cid;
	}
	
	/**
	 * @return the patientDetails
	 */
	public PatientDetails getPatientDetails() {
		return patientDetails;
	}
	
	/**
	 * @param patientDetails
	 *            the patientDetails to set
	 */
	public void setPatientDetails(PatientDetails patientDetails) {
		this.patientDetails = patientDetails;
	}
	
	/**
	 * @return the customFields
	 */
	public List<AppointmentCustomFieldDTO> getCustomFields() {
		return customFields;
	}
	
	/**
	 * @param customFields
	 *            the customFields to set
	 */
	public void setCustomFields(List<AppointmentCustomFieldDTO> customFields) {
		this.customFields = customFields;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
	@JsonIgnoreProperties(ignoreUnknown = true)
	@JsonInclude(value = Include.NON_NULL)
	public static class PatientDetails implements Serializable {
		
		private static final long	serialVersionUID	= -8799851463269223733L;
		
		private String				firstName;
		
		private String				lastName;
		
		private String				dob;
		
		private String				gender;
		
		private Integer				patientAge;
		
		/**
		 * @return the firstName
		 */
		public String getFirstName() {
			return firstName;
		}
		
		/**
		 * @param firstName
		 *            the firstName to set
		 */
		public void setFirstName(String firstName) {
			this.firstName = firstName;
		}
		
		/**
		 * @return the lastName
		 */
		public String getLastName() {
			return lastName;
		}
		
		/**
		 * @param lastName
		 *            the lastName to set
		 */
		public void setLastName(String lastName) {
			this.lastName = lastName;
		}
		
		/**
		 * @return the dob
		 */
		public String getDob() {
			return dob;
		}
		
		/**
		 * @param dob
		 *            the dob to set
		 */
		public void setDob(String dob) {
			this.dob = dob;
		}
		
		/**
		 * @return the gender
		 */
		public String getGender() {
			return gender;
		}
		
		/**
		 * @param gender
		 *            the gender to set
		 */
		public void setGender(String gender) {
			this.gender = gender;
		}
		
		/**
		 * @return the patientAge
		 */
		public Integer getPatientAge() {
			return patientAge;
		}
		
		/**
		 * @param patientAge
		 *            the patientAge to set
		 */
		public void setPatientAge(Integer patientAge) {
			this.patientAge = patientAge;
		}
		
		
		
		@Override
		public String toString() {
			return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
		}
		
	}
	
}
