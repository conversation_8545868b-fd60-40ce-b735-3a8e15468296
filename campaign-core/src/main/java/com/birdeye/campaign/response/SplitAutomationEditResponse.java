package com.birdeye.campaign.response;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.dto.AppointmentScheduleInfo;
import com.birdeye.campaign.dto.RuleExpression;
import com.birdeye.campaign.dto.SplitCampaignVariantData;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SplitAutomationEditResponse extends SplitCampaignBasicResponse implements Serializable{

	private static final long serialVersionUID = -5676404138268162476L;
	
	private String reminderSubject;
	
	private String lvlAlias;
	
	private String lvlAliasId;
	
	private List<String> lvlIds;
	
	private List<String> sources;
	
	private RuleExpression expression;
	
	private Integer isDraft;
	
	private String triggerType;
	
	private RuleExpression triggerExpression;
	
	private List<AppointmentScheduleInfo> scheduleInfo;
	
	private String contactEventType;
	
	private List<SplitCampaignVariantData> variants;
	
	private Boolean							isActiveSelectedLocationCountZero	= Boolean.FALSE; //BIRD-58032 - To handle the scenario when the only location selected for automation trigger becomes inactive.

	private List<Integer>					ownerPermissibleLocations			= new ArrayList<>();
	
	/**
	 * @return the reminderSubject
	 */
	public String getReminderSubject() {
		return reminderSubject;
	}

	/**
	 * @param reminderSubject the reminderSubject to set
	 */
	public void setReminderSubject(String reminderSubject) {
		this.reminderSubject = reminderSubject;
	}

	/**
	 * @return the lvlAlias
	 */
	public String getLvlAlias() {
		return lvlAlias;
	}

	/**
	 * @param lvlAlias the lvlAlias to set
	 */
	public void setLvlAlias(String lvlAlias) {
		this.lvlAlias = lvlAlias;
	}

	/**
	 * @return the lvlAliasId
	 */
	public String getLvlAliasId() {
		return lvlAliasId;
	}

	/**
	 * @param lvlAliasId the lvlAliasId to set
	 */
	public void setLvlAliasId(String lvlAliasId) {
		this.lvlAliasId = lvlAliasId;
	}

	/**
	 * @return the lvlIds
	 */
	public List<String> getLvlIds() {
		return lvlIds;
	}

	/**
	 * @param lvlIds the lvlIds to set
	 */
	public void setLvlIds(List<String> lvlIds) {
		this.lvlIds = lvlIds;
	}

	/**
	 * @return the sources
	 */
	public List<String> getSources() {
		return sources;
	}

	/**
	 * @param sources the sources to set
	 */
	public void setSources(List<String> sources) {
		this.sources = sources;
	}

	/**
	 * @return the expression
	 */
	public RuleExpression getExpression() {
		return expression;
	}

	/**
	 * @param expression the expression to set
	 */
	public void setExpression(RuleExpression expression) {
		this.expression = expression;
	}

	/**
	 * @return the isDraft
	 */
	public Integer getIsDraft() {
		return isDraft;
	}

	/**
	 * @param isDraft the isDraft to set
	 */
	public void setIsDraft(Integer isDraft) {
		this.isDraft = isDraft;
	}

	/**
	 * @return the triggerType
	 */
	public String getTriggerType() {
		return triggerType;
	}

	/**
	 * @param triggerType the triggerType to set
	 */
	public void setTriggerType(String triggerType) {
		this.triggerType = triggerType;
	}

	/**
	 * @return the triggerExpression
	 */
	public RuleExpression getTriggerExpression() {
		return triggerExpression;
	}

	/**
	 * @param triggerExpression the triggerExpression to set
	 */
	public void setTriggerExpression(RuleExpression triggerExpression) {
		this.triggerExpression = triggerExpression;
	}

	/**
	 * @return the scheduleInfo
	 */
	public List<AppointmentScheduleInfo> getScheduleInfo() {
		return scheduleInfo;
	}

	/**
	 * @param scheduleInfo the scheduleInfo to set
	 */
	public void setScheduleInfo(List<AppointmentScheduleInfo> scheduleInfo) {
		this.scheduleInfo = scheduleInfo;
	}

	/**
	 * @return the contactEventType
	 */
	public String getContactEventType() {
		return contactEventType;
	}

	/**
	 * @param contactEventType the contactEventType to set
	 */
	public void setContactEventType(String contactEventType) {
		this.contactEventType = contactEventType;
	}

	/**
	 * @return the variants
	 */
	public List<SplitCampaignVariantData> getVariants() {
		return variants;
	}

	/**
	 * @param variants the variants to set
	 */
	public void setVariants(List<SplitCampaignVariantData> variants) {
		this.variants = variants;
	}
	
	/**
	 * @return the isActiveSelectedLocationCountZero
	 */
	public Boolean getIsActiveSelectedLocationCountZero() {
		return isActiveSelectedLocationCountZero;
	}
	
	/**
	 * @param isActiveSelectedLocationCountZero
	 *            the isActiveSelectedLocationCountZero to set
	 */
	public void setIsActiveSelectedLocationCountZero(Boolean isActiveSelectedLocationCountZero) {
		this.isActiveSelectedLocationCountZero = isActiveSelectedLocationCountZero;
	}
	
	/**
	 * @return the ownerPermissibleLocations
	 */
	public List<Integer> getOwnerPermissibleLocations() {
		return ownerPermissibleLocations;
	}

	/**
	 * @param ownerPermissibleLocations the ownerPermissibleLocations to set
	 */
	public void setOwnerPermissibleLocations(List<Integer> ownerPermissibleLocations) {
		this.ownerPermissibleLocations = ownerPermissibleLocations;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
