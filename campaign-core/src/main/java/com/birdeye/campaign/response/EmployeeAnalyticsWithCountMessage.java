package com.birdeye.campaign.response;

import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class EmployeeAnalyticsWithCountMessage {
	
	private Integer							totalCount;			// total no. off employees
	private Integer							totalCheckinCount;
	private List<EmployeeAnalyticMessage>	employeeMessage;
	private Integer							totalPages;
	private Integer							page;
	private Integer							size;
	
	public EmployeeAnalyticsWithCountMessage() {
		super();
	}
	
	public EmployeeAnalyticsWithCountMessage(Integer totalCheckinCount, Integer totalCount, List<EmployeeAnalyticMessage> employeeMessage, Integer totalPages, Integer page, Integer size) {
		super();
		this.totalCheckinCount = totalCheckinCount;
		this.totalCount = totalCount;
		this.employeeMessage = employeeMessage;
		this.totalPages = totalPages;
		this.page = page;
		this.size = size;
	}
	
	public EmployeeAnalyticsWithCountMessage(Integer totalCount, List<EmployeeAnalyticMessage> employeeMessage, Integer totalPages, Integer page, Integer size) {
		super();
		this.totalCount = totalCount;
		this.employeeMessage = employeeMessage;
		this.totalPages = totalPages;
		this.page = page;
		this.size = size;
	}
	
	public EmployeeAnalyticsWithCountMessage(Integer totalCount, List<EmployeeAnalyticMessage> employeeMessage) {
		super();
		this.totalCount = totalCount;
		this.employeeMessage = employeeMessage;
	}
	
	public EmployeeAnalyticsWithCountMessage(Integer totalCheckinCount, Integer totalCount, List<EmployeeAnalyticMessage> employeeMessage) {
		super();
		this.totalCheckinCount = totalCheckinCount;
		this.totalCount = totalCount;
		this.employeeMessage = employeeMessage;
	}
	
	public Integer getTotalCount() {
		return totalCount;
	}
	
	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}
	
	public Integer getTotalCheckinCount() {
		return totalCheckinCount;
	}
	
	public void setTotalCheckinCount(Integer totalCheckinCount) {
		this.totalCheckinCount = totalCheckinCount;
	}
	
	public List<EmployeeAnalyticMessage> getEmployeeMessage() {
		return employeeMessage;
	}
	
	public void setEmployeeMessage(List<EmployeeAnalyticMessage> employeeMessage) {
		this.employeeMessage = employeeMessage;
	}
	
	public Integer getTotalPages() {
		return totalPages;
	}
	
	public void setTotalPages(Integer totalPages) {
		this.totalPages = totalPages;
	}
	
	public Integer getPage() {
		return page;
	}
	
	public void setPage(Integer page) {
		this.page = page;
	}
	
	public Integer getSize() {
		return size;
	}
	
	public void setSize(Integer size) {
		this.size = size;
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return b.toString();
	}
	
}