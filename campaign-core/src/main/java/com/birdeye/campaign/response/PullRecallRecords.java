package com.birdeye.campaign.response;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.dto.AppointmentCustomFieldDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_NULL)
public class PullRecallRecords implements Serializable {
	
	private static final long		serialVersionUID	= -3876121982475750685L;
	
	private Integer					appointmentRecallId;
	
	private Integer					businessId;
	
	private Integer					appointmentId;
	
	private String					altAppointmentId;
	
	private String					appointmentDate;
	
	private String					eventDate;
	
	private Boolean					updated;
	
	private <PERSON>olean					deleted;
	
	private Boolean					apptRecallDeleted;
	
	private RecallAppointmentRecord	appointment;
	
	private Integer 				accountId;
	
	
	public Integer getAccountId() {
		return accountId;
	}

	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}

	/**
	 * Default constructor
	 */
	public PullRecallRecords() {
	}
	
	/**
	 * @return the appointmentRecallId
	 */
	public Integer getAppointmentRecallId() {
		return appointmentRecallId;
	}
	
	/**
	 * @param appointmentRecallId
	 *            the appointmentRecallId to set
	 */
	public void setAppointmentRecallId(Integer appointmentRecallId) {
		this.appointmentRecallId = appointmentRecallId;
	}
	
	/**
	 * @return the businessId
	 */
	public Integer getBusinessId() {
		return businessId;
	}
	
	/**
	 * @param businessId
	 *            the businessId to set
	 */
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	/**
	 * @return the appointmentId
	 */
	public Integer getAppointmentId() {
		return appointmentId;
	}
	
	/**
	 * @param appointmentId
	 *            the appointmentId to set
	 */
	public void setAppointmentId(Integer appointmentId) {
		this.appointmentId = appointmentId;
	}
	
	/**
	 * @return the altAppointmentId
	 */
	public String getAltAppointmentId() {
		return altAppointmentId;
	}
	
	/**
	 * @param altAppointmentId
	 *            the altAppointmentId to set
	 */
	public void setAltAppointmentId(String altAppointmentId) {
		this.altAppointmentId = altAppointmentId;
	}
	
	/**
	 * @return the appointmentDate
	 */
	public String getAppointmentDate() {
		return appointmentDate;
	}
	
	/**
	 * @param appointmentDate
	 *            the appointmentDate to set
	 */
	public void setAppointmentDate(String appointmentDate) {
		this.appointmentDate = appointmentDate;
	}
	
	/**
	 * @return the eventDate
	 */
	public String getEventDate() {
		return eventDate;
	}
	
	/**
	 * @param eventDate
	 *            the eventDate to set
	 */
	public void setEventDate(String eventDate) {
		this.eventDate = eventDate;
	}
	
	/**
	 * @return the updated
	 */
	public Boolean getUpdated() {
		return updated;
	}
	
	/**
	 * @param updated
	 *            the updated to set
	 */
	public void setUpdated(Boolean updated) {
		this.updated = updated;
	}
	
	/**
	 * @return the deleted
	 */
	public Boolean getDeleted() {
		return deleted;
	}
	
	/**
	 * @param deleted
	 *            the deleted to set
	 */
	public void setDeleted(Boolean deleted) {
		this.deleted = deleted;
	}
	
	/**
	 * @return the apptRecallDeleted
	 */
	public Boolean getApptRecallDeleted() {
		return apptRecallDeleted;
	}
	
	/**
	 * @param apptRecallDeleted
	 *            the apptRecallDeleted to set
	 */
	public void setApptRecallDeleted(Boolean apptRecallDeleted) {
		this.apptRecallDeleted = apptRecallDeleted;
	}
	
	/**
	 * @return the appointment
	 */
	public RecallAppointmentRecord getAppointment() {
		return appointment;
	}
	
	/**
	 * @param appointment
	 *            the appointment to set
	 */
	public void setAppointment(RecallAppointmentRecord appointment) {
		this.appointment = appointment;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
	public static class RecallAppointmentRecord implements Serializable {
		
		private static final long				serialVersionUID	= -8896621920350192523L;
		
		private Integer							appointmentId;
		
		private Integer							businessId;
		
		private Integer							accountId;
		
		private Integer							customerId;
		
		private Integer							specialistId;
		
		private Integer							serviceId;
		
		private String							startTime;
		
		private String							endTime;
		
		private String							eventTime;
		
		private String							firstName;
		
		private String							lastName;
		
		private Integer							age;
		
		private Boolean							selfBooking;
		
		private String							action;
		
		private String							status;
		
		private String							source;
		
		private String							altAppointmentId;
		
		private String							appointmentStatus;
		
		private String							formFillStatus;
		
		private Boolean							futureAppointmentPresent;
		
		private List<AppointmentCustomFieldDTO>	customFields;
		
		/**
		 * Default constructor
		 */
		public RecallAppointmentRecord() {
		}
		
		/**
		 * @return the appointmentId
		 */
		public Integer getAppointmentId() {
			return appointmentId;
		}
		
		/**
		 * @param appointmentId
		 *            the appointmentId to set
		 */
		public void setAppointmentId(Integer appointmentId) {
			this.appointmentId = appointmentId;
		}
		
		/**
		 * @return the businessId
		 */
		public Integer getBusinessId() {
			return businessId;
		}
		
		/**
		 * @param businessId
		 *            the businessId to set
		 */
		public void setBusinessId(Integer businessId) {
			this.businessId = businessId;
		}
		
		/**
		 * @return the accountId
		 */
		public Integer getAccountId() {
			return accountId;
		}
		
		/**
		 * @param accountId
		 *            the accountId to set
		 */
		public void setAccountId(Integer accountId) {
			this.accountId = accountId;
		}
		
		/**
		 * @return the customerId
		 */
		public Integer getCustomerId() {
			return customerId;
		}
		
		/**
		 * @param customerId
		 *            the customerId to set
		 */
		public void setCustomerId(Integer customerId) {
			this.customerId = customerId;
		}
		
		/**
		 * @return the specialistId
		 */
		public Integer getSpecialistId() {
			return specialistId;
		}
		
		/**
		 * @param specialistId
		 *            the specialistId to set
		 */
		public void setSpecialistId(Integer specialistId) {
			this.specialistId = specialistId;
		}
		
		/**
		 * @return the serviceId
		 */
		public Integer getServiceId() {
			return serviceId;
		}
		
		/**
		 * @param serviceId
		 *            the serviceId to set
		 */
		public void setServiceId(Integer serviceId) {
			this.serviceId = serviceId;
		}
		
		/**
		 * @return the startTime
		 */
		public String getStartTime() {
			return startTime;
		}
		
		/**
		 * @param startTime
		 *            the startTime to set
		 */
		public void setStartTime(String startTime) {
			this.startTime = startTime;
		}
		
		/**
		 * @return the endTime
		 */
		public String getEndTime() {
			return endTime;
		}
		
		/**
		 * @param endTime
		 *            the endTime to set
		 */
		public void setEndTime(String endTime) {
			this.endTime = endTime;
		}
		
		/**
		 * @return the eventTime
		 */
		public String getEventTime() {
			return eventTime;
		}
		
		/**
		 * @param eventTime
		 *            the eventTime to set
		 */
		public void setEventTime(String eventTime) {
			this.eventTime = eventTime;
		}
		
		/**
		 * @return the firstName
		 */
		public String getFirstName() {
			return firstName;
		}
		
		/**
		 * @param firstName
		 *            the firstName to set
		 */
		public void setFirstName(String firstName) {
			this.firstName = firstName;
		}
		
		/**
		 * @return the lastName
		 */
		public String getLastName() {
			return lastName;
		}
		
		/**
		 * @param lastName
		 *            the lastName to set
		 */
		public void setLastName(String lastName) {
			this.lastName = lastName;
		}
		
		/**
		 * @return the age
		 */
		public Integer getAge() {
			return age;
		}
		
		/**
		 * @param age
		 *            the age to set
		 */
		public void setAge(Integer age) {
			this.age = age;
		}
		
		/**
		 * @return the selfBooking
		 */
		public Boolean getSelfBooking() {
			return selfBooking;
		}
		
		/**
		 * @param selfBooking
		 *            the selfBooking to set
		 */
		public void setSelfBooking(Boolean selfBooking) {
			this.selfBooking = selfBooking;
		}
		
		/**
		 * @return the action
		 */
		public String getAction() {
			return action;
		}
		
		/**
		 * @param action
		 *            the action to set
		 */
		public void setAction(String action) {
			this.action = action;
		}
		
		/**
		 * @return the status
		 */
		public String getStatus() {
			return status;
		}
		
		/**
		 * @param status
		 *            the status to set
		 */
		public void setStatus(String status) {
			this.status = status;
		}
		
		/**
		 * @return the source
		 */
		public String getSource() {
			return source;
		}
		
		/**
		 * @param source
		 *            the source to set
		 */
		public void setSource(String source) {
			this.source = source;
		}
		
		/**
		 * @return the altAppointmentId
		 */
		public String getAltAppointmentId() {
			return altAppointmentId;
		}
		
		/**
		 * @param altAppointmentId
		 *            the altAppointmentId to set
		 */
		public void setAltAppointmentId(String altAppointmentId) {
			this.altAppointmentId = altAppointmentId;
		}
		
		/**
		 * @return the appointmentStatus
		 */
		public String getAppointmentStatus() {
			return appointmentStatus;
		}
		
		/**
		 * @param appointmentStatus
		 *            the appointmentStatus to set
		 */
		public void setAppointmentStatus(String appointmentStatus) {
			this.appointmentStatus = appointmentStatus;
		}
		
		/**
		 * @return the formFillStatus
		 */
		public String getFormFillStatus() {
			return formFillStatus;
		}
		
		/**
		 * @param formFillStatus
		 *            the formFillStatus to set
		 */
		public void setFormFillStatus(String formFillStatus) {
			this.formFillStatus = formFillStatus;
		}
		
		/**
		 * @return the futureAppointmentPresent
		 */
		public Boolean getFutureAppointmentPresent() {
			return futureAppointmentPresent;
		}
		
		/**
		 * @param futureAppointmentPresent
		 *            the futureAppointmentPresent to set
		 */
		public void setFutureAppointmentPresent(Boolean futureAppointmentPresent) {
			this.futureAppointmentPresent = futureAppointmentPresent;
		}
		
		/**
		 * @return the customFields
		 */
		public List<AppointmentCustomFieldDTO> getCustomFields() {
			return customFields;
		}
		
		/**
		 * @param customFields
		 *            the customFields to set
		 */
		public void setCustomFields(List<AppointmentCustomFieldDTO> customFields) {
			this.customFields = customFields;
		}
		
		@Override
		public String toString() {
			return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
		}
		
	}
	
}
