package com.birdeye.campaign.dto;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class CXUsageFunnelReportResponse {
	
	private int							totalPages;
	private List<CXUsageReportResponse>	usageReportResponses;
	private String						campaignName;			// Only used in case of Campaign Specific Report
	
	public int getTotalPages() {
		return totalPages;
	}
	
	public void setTotalPages(int totalPages) {
		this.totalPages = totalPages;
	}
	
	public List<CXUsageReportResponse> getUsageReportResponses() {
		return usageReportResponses;
	}
	
	public void setUsageReportResponses(List<CXUsageReportResponse> usageReportResponses) {
		this.usageReportResponses = usageReportResponses;
	}
	
	public String getCampaignName() {
		return campaignName;
	}
	
	public void setCampaignName(String campaignName) {
		this.campaignName = campaignName;
	}
	
}