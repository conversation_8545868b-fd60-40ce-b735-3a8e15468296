package com.birdeye.campaign.dto;

import java.io.Serializable;

/**
 * This class is intended to hold message to be pushed on kafka topic.
 * 
 * <AUTHOR>
 */
public class KafkaMessage {
	private Serializable key;
	private Serializable value;

	public KafkaMessage(Serializable value) {
		this.value = value;
	}

	public KafkaMessage(Serializable key, Serializable value) {
		this.key = key;
		this.value = value;
	}

	public Serializable getKey() {
		return key;
	}

	public void setKey(Serializable key) {
		this.key = key;
	}

	public Serializable getValue() {
		return value;
	}

	public void setValue(Serializable value) {
		this.value = value;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("KafkaMessage [key=");
		builder.append(key);
		builder.append(", value=");
		builder.append(value);
		builder.append("]");
		return builder.toString();
	}

}
