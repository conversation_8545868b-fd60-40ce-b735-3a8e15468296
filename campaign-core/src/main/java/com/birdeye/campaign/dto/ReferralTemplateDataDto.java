package com.birdeye.campaign.dto;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.entity.ReferralTemplateData;

public class ReferralTemplateDataDto implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 924531676512138161L;
	
	private Integer				id;
	
	private Integer				enterpriseId;
	
	private Integer				templateId;
	
	private String				source;
	
	private String				referralMessageEmailSubject;
	
	private Integer				referralMessageCtaUrlEnabled;
	
	private String				referralMessageCtaUrl;
	
	private String				referralFormHeading;
	
	private String				referralFormMessage;
	
	private String				referralFormCtaLabel;
	
	private String				tyPageAlternateUrl;
	
	private Integer				tyPageEnabled;
	
	private String				tyPageHeading;
	
	private String				tyPageMessage;
	
	private String				tyPageCtaLabel;
	
	private String				tyPageCtaUrl;
	
	public ReferralTemplateDataDto() {
		
	}
	
	public ReferralTemplateDataDto(ReferralTemplateData referralTemplateData) {
		this.id = referralTemplateData.getId();
		this.enterpriseId = referralTemplateData.getEnterpriseId();
		this.templateId = referralTemplateData.getTemplateId();
		this.source = referralTemplateData.getSource();
		this.referralMessageEmailSubject = referralTemplateData.getReferralMessageEmailSubject();
		this.referralMessageCtaUrlEnabled = referralTemplateData.getReferralMessageCtaUrlEnabled();
		this.referralMessageCtaUrl = referralTemplateData.getReferralMessageCtaUrl();
		this.referralFormHeading = referralTemplateData.getReferralFormHeading();
		this.referralFormMessage = referralTemplateData.getReferralFormMessage();
		this.referralFormCtaLabel = referralTemplateData.getReferralFormCtaLabel();
		this.tyPageAlternateUrl = referralTemplateData.getTyPageAlternateUrl();
		this.tyPageEnabled = referralTemplateData.getTyPageEnabled();
		this.tyPageHeading = referralTemplateData.getTyPageHeading();
		this.tyPageMessage = referralTemplateData.getTyPageMessage();
		this.tyPageCtaLabel = referralTemplateData.getTyPageCtaLabel();
		this.tyPageCtaUrl = referralTemplateData.getTyPageCtaUrl();
	}
	
	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}
	
	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	
	/**
	 * @return the enterpriseId
	 */
	public Integer getEnterpriseId() {
		return enterpriseId;
	}
	
	/**
	 * @param enterpriseId
	 *            the enterpriseId to set
	 */
	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	
	/**
	 * @return the templateId
	 */
	public Integer getTemplateId() {
		return templateId;
	}
	
	/**
	 * @param templateId
	 *            the templateId to set
	 */
	public void setTemplateId(Integer templateId) {
		this.templateId = templateId;
	}
	
	/**
	 * @return the source
	 */
	public String getSource() {
		return source;
	}
	
	/**
	 * @param source
	 *            the source to set
	 */
	public void setSource(String source) {
		this.source = source;
	}
	
	/**
	 * @return the referralMessageEmailSubject
	 */
	public String getReferralMessageEmailSubject() {
		return referralMessageEmailSubject;
	}
	
	/**
	 * @param referralMessageEmailSubject
	 *            the referralMessageEmailSubject to set
	 */
	public void setReferralMessageEmailSubject(String referralMessageEmailSubject) {
		this.referralMessageEmailSubject = referralMessageEmailSubject;
	}
	
	/**
	 * @return the referralMessageCtaUrlEnabled
	 */
	public Integer getReferralMessageCtaUrlEnabled() {
		return referralMessageCtaUrlEnabled;
	}
	
	/**
	 * @param referralMessageCtaUrlEnabled
	 *            the referralMessageCtaUrlEnabled to set
	 */
	public void setReferralMessageCtaUrlEnabled(Integer referralMessageCtaUrlEnabled) {
		this.referralMessageCtaUrlEnabled = referralMessageCtaUrlEnabled;
	}
	
	/**
	 * @return the referralMessageCtaUrl
	 */
	public String getReferralMessageCtaUrl() {
		return referralMessageCtaUrl;
	}
	
	/**
	 * @param referralMessageCtaUrl
	 *            the referralMessageCtaUrl to set
	 */
	public void setReferralMessageCtaUrl(String referralMessageCtaUrl) {
		this.referralMessageCtaUrl = referralMessageCtaUrl;
	}
	
	/**
	 * @return the referralFormHeading
	 */
	public String getReferralFormHeading() {
		return referralFormHeading;
	}
	
	/**
	 * @param referralFormHeading
	 *            the referralFormHeading to set
	 */
	public void setReferralFormHeading(String referralFormHeading) {
		this.referralFormHeading = referralFormHeading;
	}
	
	/**
	 * @return the referralFormMessage
	 */
	public String getReferralFormMessage() {
		return referralFormMessage;
	}
	
	/**
	 * @param referralFormMessage
	 *            the referralFormMessage to set
	 */
	public void setReferralFormMessage(String referralFormMessage) {
		this.referralFormMessage = referralFormMessage;
	}
	
	/**
	 * @return the referralFormCtaLabel
	 */
	public String getReferralFormCtaLabel() {
		return referralFormCtaLabel;
	}
	
	/**
	 * @param referralFormCtaLabel
	 *            the referralFormCtaLabel to set
	 */
	public void setReferralFormCtaLabel(String referralFormCtaLabel) {
		this.referralFormCtaLabel = referralFormCtaLabel;
	}
	
	/**
	 * @return the tyPageAlternateUrl
	 */
	public String getTyPageAlternateUrl() {
		return tyPageAlternateUrl;
	}
	
	/**
	 * @param tyPageAlternateUrl
	 *            the tyPageAlternateUrl to set
	 */
	public void setTyPageAlternateUrl(String tyPageAlternateUrl) {
		this.tyPageAlternateUrl = tyPageAlternateUrl;
	}
	
	/**
	 * @return the tyPageEnabled
	 */
	public Integer getTyPageEnabled() {
		return tyPageEnabled;
	}
	
	/**
	 * @param tyPageEnabled
	 *            the tyPageEnabled to set
	 */
	public void setTyPageEnabled(Integer tyPageEnabled) {
		this.tyPageEnabled = tyPageEnabled;
	}
	
	/**
	 * @return the tyPageHeading
	 */
	public String getTyPageHeading() {
		return tyPageHeading;
	}
	
	/**
	 * @param tyPageHeading
	 *            the tyPageHeading to set
	 */
	public void setTyPageHeading(String tyPageHeading) {
		this.tyPageHeading = tyPageHeading;
	}
	
	/**
	 * @return the tyPageMessage
	 */
	public String getTyPageMessage() {
		return tyPageMessage;
	}
	
	/**
	 * @param tyPageMessage
	 *            the tyPageMessage to set
	 */
	public void setTyPageMessage(String tyPageMessage) {
		this.tyPageMessage = tyPageMessage;
	}
	
	/**
	 * @return the tyPageCtaLabel
	 */
	public String getTyPageCtaLabel() {
		return tyPageCtaLabel;
	}
	
	/**
	 * @param tyPageCtaLabel
	 *            the tyPageCtaLabel to set
	 */
	public void setTyPageCtaLabel(String tyPageCtaLabel) {
		this.tyPageCtaLabel = tyPageCtaLabel;
	}
	
	/**
	 * @return the tyPageCtaUrl
	 */
	public String getTyPageCtaUrl() {
		return tyPageCtaUrl;
	}
	
	/**
	 * @param tyPageCtaUrl
	 *            the tyPageCtaUrl to set
	 */
	public void setTyPageCtaUrl(String tyPageCtaUrl) {
		this.tyPageCtaUrl = tyPageCtaUrl;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
