package com.birdeye.campaign.dto;

public class CampaignCountMessage {

	private Long count;
	private String runType;

	public CampaignCountMessage() {

	}

	public CampaignCountMessage(Long count, String runType) {
		super();
		this.count = count;
		this.runType = runType;
	}

	/**
	 * @return the count
	 */
	public Long getCount() {
		return count;
	}

	/**
	 * @param count
	 *            the count to set
	 */
	public void setCount(Long count) {
		this.count = count;
	}

	/**
	 * @return the runType
	 */
	public String getRunType() {
		return runType;
	}

	/**
	 * @param runType
	 *            the runType to set
	 */
	public void setRunType(String runType) {
		this.runType = runType;
	}
}
