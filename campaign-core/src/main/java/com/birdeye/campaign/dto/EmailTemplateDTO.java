package com.birdeye.campaign.dto;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.entity.EmailTemplate;

public class EmailTemplateDTO implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -87817754638753294L;
	
	private Integer				id;
	
	private String				name;
	
	private String				message;
	
	private String				sentimentMessage;
	
	private String				starMessage;
	
	private String				subject;
	
	private String				reminderSubject;
	
	private String				type;
	
	private String				emailQuestion;
	
	private String				sentimentHeading;
	
	private String				starHeading;
	
	private String				positiveButtonLabel;
	
	private String				negativeButtonLabel;
	
	private String				neutralButtonLabel;
	
	private String				recommendPageHeading;
	
	private String				recommendPageMessage;
	
	private String				recommendThankPageMessage;
	
	private String				recommendThankPageFooter;
	
	private String				nonRecommendPageMessage;
	
	private String				nonRecommendThankPageMessage;
	
	private String				nonRecommendThankPageFooter;
	
	private String				prioritySources;
	
	private String				writeReviewQuestion;
	
	private String				writeReviewNegativeText;
	
	private String				writeReviewPositiveText;
	
	private Integer				positiveRatingThreshold;
	
	private String				contactUsMessage;
	
	private String				contactUsButtonText;
	
	private String				contactUsButtonTextColor;
	
	private String				contactUsButtonColor;
	
	private String				reviewHeading;
	
	private String				reviewMessage;
	
	private String				reviewSiteButtonColor;
	
	private String				reviewSiteButtonTextColor;
	
	private String				feedbackMessage;
	
	private Integer				feedbackShowCallbackOption;
	
	private Integer				feedbackDefaultCheckboxChecked;
	
	private String				thankyouHeading;
	
	private String				thankyouMessage;
	
	private String				signature;
	
	private String				mediaUrls;
	
	private String				referralQuestion;
	
	private String				referralOptions;
	
	private Integer				referralContactOptionEnabled;
	
	private String				emailCategory;
	
	public EmailTemplateDTO() {
		
	}
	
	public EmailTemplateDTO(EmailTemplate emailTemplate) {
		this.message = emailTemplate.getMessage();
		this.sentimentMessage = emailTemplate.getSentimentMessage();
		this.starMessage = emailTemplate.getStarMessage();
		this.subject = emailTemplate.getSubject();
		this.reminderSubject = emailTemplate.getReminderSubject();
		this.type = emailTemplate.getType();
		this.name = emailTemplate.getName();
		this.emailQuestion = emailTemplate.getEmailQuestion();
		this.sentimentHeading = emailTemplate.getSentimentHeading();
		this.starHeading = emailTemplate.getStarHeading();
		this.positiveButtonLabel = emailTemplate.getPositiveButtonLabel();
		this.negativeButtonLabel = emailTemplate.getNegativeButtonLabel();
		this.neutralButtonLabel = emailTemplate.getNeutralButtonLabel();
		this.recommendPageHeading = emailTemplate.getRecommendPageHeading();
		this.recommendPageMessage = emailTemplate.getRecommendPageMessage();
		this.nonRecommendPageMessage = emailTemplate.getNonRecommendPageMessage();
		this.recommendThankPageMessage = emailTemplate.getRecommendThankPageMessage();
		this.recommendThankPageFooter = emailTemplate.getRecommendThankPageFooter();
		this.nonRecommendThankPageMessage = emailTemplate.getNonRecommendThankPageMessage();
		this.nonRecommendThankPageFooter = emailTemplate.getNonRecommendThankPageFooter();
		this.reviewHeading = emailTemplate.getReviewHeading();
		this.reviewMessage = emailTemplate.getReviewMessage();
		this.writeReviewQuestion = emailTemplate.getWriteReviewQuestion();
		this.writeReviewPositiveText = emailTemplate.getWriteReviewPositiveText();
		this.writeReviewNegativeText = emailTemplate.getWriteReviewNegativeText();
		this.positiveRatingThreshold = emailTemplate.getPositiveRatingThreshold();
		this.contactUsMessage = emailTemplate.getContactUsMessage();
		this.contactUsButtonText = emailTemplate.getContactUsButtonText();
		this.contactUsButtonTextColor = emailTemplate.getContactUsButtonTextColor();
		this.contactUsButtonColor = emailTemplate.getContactUsButtonColor();
		this.reviewSiteButtonColor = emailTemplate.getReviewSiteButtonColor();
		this.reviewSiteButtonTextColor = emailTemplate.getReviewSiteButtonTextColor();
		this.feedbackMessage = emailTemplate.getFeedbackMessage();
		this.feedbackShowCallbackOption = emailTemplate.getFeedbackShowCallbackOption();
		this.feedbackDefaultCheckboxChecked = emailTemplate.getFeedbackDefaultCheckboxChecked();
		this.thankyouHeading = emailTemplate.getThankyouHeading();
		this.thankyouMessage = emailTemplate.getThankyouMessage();
		this.signature = emailTemplate.getSignature();
		this.id = emailTemplate.getId();
		this.prioritySources = emailTemplate.getPrioritySources();
		this.mediaUrls = emailTemplate.getMediaUrls();
		this.referralOptions = emailTemplate.getReferralOptions();
		this.referralContactOptionEnabled = emailTemplate.getReferralContactOptionEnabled();
		this.emailCategory = emailTemplate.getEmailCategory();
	}
	
	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}
	
	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	
	/**
	 * @return the name
	 */
	public String getName() {
		return name;
	}
	
	/**
	 * @param name
	 *            the name to set
	 */
	public void setName(String name) {
		this.name = name;
	}
	
	/**
	 * @return the message
	 */
	public String getMessage() {
		return message;
	}
	
	/**
	 * @param message
	 *            the message to set
	 */
	public void setMessage(String message) {
		this.message = message;
	}
	
	/**
	 * @return the sentimentMessage
	 */
	public String getSentimentMessage() {
		return sentimentMessage;
	}
	
	/**
	 * @param sentimentMessage
	 *            the sentimentMessage to set
	 */
	public void setSentimentMessage(String sentimentMessage) {
		this.sentimentMessage = sentimentMessage;
	}
	
	/**
	 * @return the starMessage
	 */
	public String getStarMessage() {
		return starMessage;
	}
	
	/**
	 * @param starMessage
	 *            the starMessage to set
	 */
	public void setStarMessage(String starMessage) {
		this.starMessage = starMessage;
	}
	
	/**
	 * @return the subject
	 */
	public String getSubject() {
		return subject;
	}
	
	/**
	 * @param subject
	 *            the subject to set
	 */
	public void setSubject(String subject) {
		this.subject = subject;
	}
	
	/**
	 * @return the reminderSubject
	 */
	public String getReminderSubject() {
		return reminderSubject;
	}
	
	/**
	 * @param reminderSubject
	 *            the reminderSubject to set
	 */
	public void setReminderSubject(String reminderSubject) {
		this.reminderSubject = reminderSubject;
	}
	
	/**
	 * @return the type
	 */
	public String getType() {
		return type;
	}
	
	/**
	 * @param type
	 *            the type to set
	 */
	public void setType(String type) {
		this.type = type;
	}
	
	/**
	 * @return the emailQuestion
	 */
	public String getEmailQuestion() {
		return emailQuestion;
	}
	
	/**
	 * @param emailQuestion
	 *            the emailQuestion to set
	 */
	public void setEmailQuestion(String emailQuestion) {
		this.emailQuestion = emailQuestion;
	}
	
	/**
	 * @return the sentimentHeading
	 */
	public String getSentimentHeading() {
		return sentimentHeading;
	}
	
	/**
	 * @param sentimentHeading
	 *            the sentimentHeading to set
	 */
	public void setSentimentHeading(String sentimentHeading) {
		this.sentimentHeading = sentimentHeading;
	}
	
	/**
	 * @return the starHeading
	 */
	public String getStarHeading() {
		return starHeading;
	}
	
	/**
	 * @param starHeading
	 *            the starHeading to set
	 */
	public void setStarHeading(String starHeading) {
		this.starHeading = starHeading;
	}
	
	/**
	 * @return the positiveButtonLabel
	 */
	public String getPositiveButtonLabel() {
		return positiveButtonLabel;
	}
	
	/**
	 * @param positiveButtonLabel
	 *            the positiveButtonLabel to set
	 */
	public void setPositiveButtonLabel(String positiveButtonLabel) {
		this.positiveButtonLabel = positiveButtonLabel;
	}
	
	/**
	 * @return the negativeButtonLabel
	 */
	public String getNegativeButtonLabel() {
		return negativeButtonLabel;
	}
	
	/**
	 * @param negativeButtonLabel
	 *            the negativeButtonLabel to set
	 */
	public void setNegativeButtonLabel(String negativeButtonLabel) {
		this.negativeButtonLabel = negativeButtonLabel;
	}
	
	/**
	 * @return the neutralButtonLabel
	 */
	public String getNeutralButtonLabel() {
		return neutralButtonLabel;
	}
	
	/**
	 * @param neutralButtonLabel
	 *            the neutralButtonLabel to set
	 */
	public void setNeutralButtonLabel(String neutralButtonLabel) {
		this.neutralButtonLabel = neutralButtonLabel;
	}
	
	/**
	 * @return the recommendPageHeading
	 */
	public String getRecommendPageHeading() {
		return recommendPageHeading;
	}
	
	/**
	 * @param recommendPageHeading
	 *            the recommendPageHeading to set
	 */
	public void setRecommendPageHeading(String recommendPageHeading) {
		this.recommendPageHeading = recommendPageHeading;
	}
	
	/**
	 * @return the recommendPageMessage
	 */
	public String getRecommendPageMessage() {
		return recommendPageMessage;
	}
	
	/**
	 * @param recommendPageMessage
	 *            the recommendPageMessage to set
	 */
	public void setRecommendPageMessage(String recommendPageMessage) {
		this.recommendPageMessage = recommendPageMessage;
	}
	
	/**
	 * @return the recommendThankPageMessage
	 */
	public String getRecommendThankPageMessage() {
		return recommendThankPageMessage;
	}
	
	/**
	 * @param recommendThankPageMessage
	 *            the recommendThankPageMessage to set
	 */
	public void setRecommendThankPageMessage(String recommendThankPageMessage) {
		this.recommendThankPageMessage = recommendThankPageMessage;
	}
	
	/**
	 * @return the recommendThankPageFooter
	 */
	public String getRecommendThankPageFooter() {
		return recommendThankPageFooter;
	}
	
	/**
	 * @param recommendThankPageFooter
	 *            the recommendThankPageFooter to set
	 */
	public void setRecommendThankPageFooter(String recommendThankPageFooter) {
		this.recommendThankPageFooter = recommendThankPageFooter;
	}
	
	/**
	 * @return the nonRecommendPageMessage
	 */
	public String getNonRecommendPageMessage() {
		return nonRecommendPageMessage;
	}
	
	/**
	 * @param nonRecommendPageMessage
	 *            the nonRecommendPageMessage to set
	 */
	public void setNonRecommendPageMessage(String nonRecommendPageMessage) {
		this.nonRecommendPageMessage = nonRecommendPageMessage;
	}
	
	/**
	 * @return the nonRecommendThankPageMessage
	 */
	public String getNonRecommendThankPageMessage() {
		return nonRecommendThankPageMessage;
	}
	
	/**
	 * @param nonRecommendThankPageMessage
	 *            the nonRecommendThankPageMessage to set
	 */
	public void setNonRecommendThankPageMessage(String nonRecommendThankPageMessage) {
		this.nonRecommendThankPageMessage = nonRecommendThankPageMessage;
	}
	
	/**
	 * @return the nonRecommendThankPageFooter
	 */
	public String getNonRecommendThankPageFooter() {
		return nonRecommendThankPageFooter;
	}
	
	/**
	 * @param nonRecommendThankPageFooter
	 *            the nonRecommendThankPageFooter to set
	 */
	public void setNonRecommendThankPageFooter(String nonRecommendThankPageFooter) {
		this.nonRecommendThankPageFooter = nonRecommendThankPageFooter;
	}
	
	/**
	 * @return the prioritySources
	 */
	public String getPrioritySources() {
		return prioritySources;
	}
	
	/**
	 * @param prioritySources
	 *            the prioritySources to set
	 */
	public void setPrioritySources(String prioritySources) {
		this.prioritySources = prioritySources;
	}
	
	/**
	 * @return the writeReviewQuestion
	 */
	public String getWriteReviewQuestion() {
		return writeReviewQuestion;
	}
	
	/**
	 * @param writeReviewQuestion
	 *            the writeReviewQuestion to set
	 */
	public void setWriteReviewQuestion(String writeReviewQuestion) {
		this.writeReviewQuestion = writeReviewQuestion;
	}
	
	/**
	 * @return the writeReviewNegativeText
	 */
	public String getWriteReviewNegativeText() {
		return writeReviewNegativeText;
	}
	
	/**
	 * @param writeReviewNegativeText
	 *            the writeReviewNegativeText to set
	 */
	public void setWriteReviewNegativeText(String writeReviewNegativeText) {
		this.writeReviewNegativeText = writeReviewNegativeText;
	}
	
	/**
	 * @return the writeReviewPositiveText
	 */
	public String getWriteReviewPositiveText() {
		return writeReviewPositiveText;
	}
	
	/**
	 * @param writeReviewPositiveText
	 *            the writeReviewPositiveText to set
	 */
	public void setWriteReviewPositiveText(String writeReviewPositiveText) {
		this.writeReviewPositiveText = writeReviewPositiveText;
	}
	
	/**
	 * @return the positiveRatingThreshold
	 */
	public Integer getPositiveRatingThreshold() {
		return positiveRatingThreshold;
	}
	
	/**
	 * @param positiveRatingThreshold
	 *            the positiveRatingThreshold to set
	 */
	public void setPositiveRatingThreshold(Integer positiveRatingThreshold) {
		this.positiveRatingThreshold = positiveRatingThreshold;
	}
	
	/**
	 * @return the contactUsMessage
	 */
	public String getContactUsMessage() {
		return contactUsMessage;
	}
	
	/**
	 * @param contactUsMessage
	 *            the contactUsMessage to set
	 */
	public void setContactUsMessage(String contactUsMessage) {
		this.contactUsMessage = contactUsMessage;
	}
	
	/**
	 * @return the contactUsButtonText
	 */
	public String getContactUsButtonText() {
		return contactUsButtonText;
	}
	
	/**
	 * @param contactUsButtonText
	 *            the contactUsButtonText to set
	 */
	public void setContactUsButtonText(String contactUsButtonText) {
		this.contactUsButtonText = contactUsButtonText;
	}
	
	/**
	 * @return the contactUsButtonTextColor
	 */
	public String getContactUsButtonTextColor() {
		return contactUsButtonTextColor;
	}
	
	/**
	 * @param contactUsButtonTextColor
	 *            the contactUsButtonTextColor to set
	 */
	public void setContactUsButtonTextColor(String contactUsButtonTextColor) {
		this.contactUsButtonTextColor = contactUsButtonTextColor;
	}
	
	/**
	 * @return the contactUsButtonColor
	 */
	public String getContactUsButtonColor() {
		return contactUsButtonColor;
	}
	
	/**
	 * @param contactUsButtonColor
	 *            the contactUsButtonColor to set
	 */
	public void setContactUsButtonColor(String contactUsButtonColor) {
		this.contactUsButtonColor = contactUsButtonColor;
	}
	
	/**
	 * @return the reviewHeading
	 */
	public String getReviewHeading() {
		return reviewHeading;
	}
	
	/**
	 * @param reviewHeading
	 *            the reviewHeading to set
	 */
	public void setReviewHeading(String reviewHeading) {
		this.reviewHeading = reviewHeading;
	}
	
	/**
	 * @return the reviewMessage
	 */
	public String getReviewMessage() {
		return reviewMessage;
	}
	
	/**
	 * @param reviewMessage
	 *            the reviewMessage to set
	 */
	public void setReviewMessage(String reviewMessage) {
		this.reviewMessage = reviewMessage;
	}
	
	/**
	 * @return the reviewSiteButtonColor
	 */
	public String getReviewSiteButtonColor() {
		return reviewSiteButtonColor;
	}
	
	/**
	 * @param reviewSiteButtonColor
	 *            the reviewSiteButtonColor to set
	 */
	public void setReviewSiteButtonColor(String reviewSiteButtonColor) {
		this.reviewSiteButtonColor = reviewSiteButtonColor;
	}
	
	/**
	 * @return the reviewSiteButtonTextColor
	 */
	public String getReviewSiteButtonTextColor() {
		return reviewSiteButtonTextColor;
	}
	
	/**
	 * @param reviewSiteButtonTextColor
	 *            the reviewSiteButtonTextColor to set
	 */
	public void setReviewSiteButtonTextColor(String reviewSiteButtonTextColor) {
		this.reviewSiteButtonTextColor = reviewSiteButtonTextColor;
	}
	
	/**
	 * @return the feedbackMessage
	 */
	public String getFeedbackMessage() {
		return feedbackMessage;
	}
	
	/**
	 * @param feedbackMessage
	 *            the feedbackMessage to set
	 */
	public void setFeedbackMessage(String feedbackMessage) {
		this.feedbackMessage = feedbackMessage;
	}
	
	/**
	 * @return the feedbackShowCallbackOption
	 */
	public Integer getFeedbackShowCallbackOption() {
		return feedbackShowCallbackOption;
	}
	
	/**
	 * @param feedbackShowCallbackOption
	 *            the feedbackShowCallbackOption to set
	 */
	public void setFeedbackShowCallbackOption(Integer feedbackShowCallbackOption) {
		this.feedbackShowCallbackOption = feedbackShowCallbackOption;
	}
	
	/**
	 * @return the feedbackDefaultCheckboxChecked
	 */
	public Integer getFeedbackDefaultCheckboxChecked() {
		return feedbackDefaultCheckboxChecked;
	}
	
	/**
	 * @param feedbackDefaultCheckboxChecked
	 *            the feedbackDefaultCheckboxChecked to set
	 */
	public void setFeedbackDefaultCheckboxChecked(Integer feedbackDefaultCheckboxChecked) {
		this.feedbackDefaultCheckboxChecked = feedbackDefaultCheckboxChecked;
	}
	
	/**
	 * @return the thankyouHeading
	 */
	public String getThankyouHeading() {
		return thankyouHeading;
	}
	
	/**
	 * @param thankyouHeading
	 *            the thankyouHeading to set
	 */
	public void setThankyouHeading(String thankyouHeading) {
		this.thankyouHeading = thankyouHeading;
	}
	
	/**
	 * @return the thankyouMessage
	 */
	public String getThankyouMessage() {
		return thankyouMessage;
	}
	
	/**
	 * @param thankyouMessage
	 *            the thankyouMessage to set
	 */
	public void setThankyouMessage(String thankyouMessage) {
		this.thankyouMessage = thankyouMessage;
	}
	
	/**
	 * @return the signature
	 */
	public String getSignature() {
		return signature;
	}
	
	/**
	 * @param signature
	 *            the signature to set
	 */
	public void setSignature(String signature) {
		this.signature = signature;
	}
	
	/**
	 * @return the mediaUrls
	 */
	public String getMediaUrls() {
		return mediaUrls;
	}
	
	/**
	 * @param mediaUrls
	 *            the mediaUrls to set
	 */
	public void setMediaUrls(String mediaUrls) {
		this.mediaUrls = mediaUrls;
	}
	
	/**
	 * @return the referralQuestion
	 */
	public String getReferralQuestion() {
		return referralQuestion;
	}
	
	/**
	 * @param referralQuestion
	 *            the referralQuestion to set
	 */
	public void setReferralQuestion(String referralQuestion) {
		this.referralQuestion = referralQuestion;
	}
	
	/**
	 * @return the referralOptions
	 */
	public String getReferralOptions() {
		return referralOptions;
	}
	
	/**
	 * @param referralOptions
	 *            the referralOptions to set
	 */
	public void setReferralOptions(String referralOptions) {
		this.referralOptions = referralOptions;
	}
	
	/**
	 * @return the referralContactOptionEnabled
	 */
	public Integer getReferralContactOptionEnabled() {
		return referralContactOptionEnabled;
	}
	
	/**
	 * @param referralContactOptionEnabled
	 *            the referralContactOptionEnabled to set
	 */
	public void setReferralContactOptionEnabled(Integer referralContactOptionEnabled) {
		this.referralContactOptionEnabled = referralContactOptionEnabled;
	}
	
	/**
	 * @return the emailCategory
	 */
	public String getEmailCategory() {
		return emailCategory;
	}

	/**
	 * @param emailCategory the emailCategory to set
	 */
	public void setEmailCategory(String emailCategory) {
		this.emailCategory = emailCategory;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
