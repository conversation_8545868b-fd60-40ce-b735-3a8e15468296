package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.Date;

import com.birdeye.campaign.constant.Constants;

public class BusinessAITemplateEntity implements Serializable {
	
	private static final long	serialVersionUID	= -2763434316642206886L;
	
	private Integer				aiTemplateId;
	private Integer				baseTemplateId;
	private String				templateType;
	private String				templateName;
	private String				channel;
	private Integer				accountId;
	private Date				createdAt;
	private Integer				createdBy;
	private Date				updatedAt;
	private Integer				updatedBy;
	private String				thumbnailUrl;
	private String				previewText;
	private Integer				locationLevelTemplate;
	private Integer				isDefaultTemplate	= 0;
	private Boolean				isGlobalTemplate	= false;
	private String				subject;
	private Integer				isDeleted;
	private String				emailCategory;
	private String				smsCategory;
	
	public BusinessAITemplateEntity() {
		
	}
	
	/**
	 * Usage: EMAIL_TEMPLATE_AI_BASE_QUERY
	 * 
	 * @param aiTemplateId
	 * @param templateName
	 * @param templateType
	 * @param accountId
	 * @param thumbnailUrl
	 * @param previewText
	 * @param emailCategory
	 * @param isDefaultTemplate
	 * @param locationLevelTemplate
	 * @param createdAt
	 * @param createdById
	 * @param updatedAt
	 * @param updatedById
	 */
	public BusinessAITemplateEntity(Integer aiTemplateId, String templateName, String templateType, Integer accountId, String thumbnailUrl,
			String previewText, String emailCategory, Integer isDefaultTemplate, Integer locationLevelTemplate, Date createdAt, Integer createdById, Date updatedAt,
			Integer updatedById) {
		this.aiTemplateId = aiTemplateId;
		this.templateName = templateName;
		this.templateType = templateType;
		this.accountId = accountId;
		this.thumbnailUrl = thumbnailUrl;
		this.previewText = previewText;
		this.emailCategory = emailCategory;
		this.channel = Constants.EMAIL_TYPE;
		this.isDefaultTemplate = isDefaultTemplate;
		this.locationLevelTemplate = locationLevelTemplate;
		this.createdAt = createdAt;
		this.createdBy = createdById;
		this.updatedAt = updatedAt;
		this.updatedBy = updatedById;
	}
	
	/**
	 * Usage: EMAIL_TEMPLATE_AI_LISTING_BY_TYPE_QUERY
	 * 
	 * @param aiTemplateId
	 * @param baseTemplatId
	 * @param templateName
	 * @param templateType
	 * @param accountId
	 * @param thumbnailUrl
	 * @param previewText
	 * @param emailCategory
	 * @param isDefaultTemplate
	 * @param locationLevelTemplate
	 * @param createdAt
	 * @param createdById
	 * @param updatedAt
	 * @param updatedById
	 */
	public BusinessAITemplateEntity(Integer aiTemplateId, Integer baseTemplateId, String templateName, String templateType, Integer accountId, String thumbnailUrl,
			String previewText, String emailCategory, Integer isDefaultTemplate, Integer locationLevelTemplate, Date createdAt, Integer createdById, Date updatedAt,
			Integer updatedById) {
		this.aiTemplateId = aiTemplateId;
		this.baseTemplateId = baseTemplateId;
		this.templateName = templateName;
		this.templateType = templateType;
		this.accountId = accountId;
		this.thumbnailUrl = thumbnailUrl;
		this.previewText = previewText;
		this.emailCategory = emailCategory;
		this.channel = Constants.EMAIL_TYPE;
		this.isDefaultTemplate = isDefaultTemplate;
		this.locationLevelTemplate = locationLevelTemplate;
		this.createdAt = createdAt;
		this.createdBy = createdById;
		this.updatedAt = updatedAt;
		this.updatedBy = updatedById;
	}
	
	/**
	 * @return the templateId
	 */
	public Integer getAiTemplateId() {
		return aiTemplateId;
	}
	
	/**
	 * @param templateId
	 *            the templateId to set
	 */
	public void setAiTemplateId(Integer templateId) {
		this.aiTemplateId = templateId;
	}
	
	/**
	 * @return the baseTemplateId
	 */
	public Integer getBaseTemplateId() {
		return baseTemplateId;
	}
	
	/**
	 * @param baseTemplateId
	 *            the baseTemplateId to set
	 */
	public void setBaseTemplateId(Integer baseTemplateId) {
		this.baseTemplateId = baseTemplateId;
	}
	
	/**
	 * @return the templateType
	 */
	public String getTemplateType() {
		return templateType;
	}
	
	/**
	 * @param templateType
	 *            the templateType to set
	 */
	public void setTemplateType(String templateType) {
		this.templateType = templateType;
	}
	
	/**
	 * @return the templateName
	 */
	public String getTemplateName() {
		return templateName;
	}
	
	/**
	 * @param templateName
	 *            the templateName to set
	 */
	public void setTemplateName(String templateName) {
		this.templateName = templateName;
	}
	
	/**
	 * @return the channel
	 */
	public String getChannel() {
		return channel;
	}
	
	/**
	 * @param channel
	 *            the channel to set
	 */
	public void setChannel(String channel) {
		this.channel = channel;
	}
	
	/**
	 * @return the accountId
	 */
	public Integer getAccountId() {
		return accountId;
	}
	
	/**
	 * @param accountId
	 *            the accountId to set
	 */
	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}
	
	/**
	 * @return the createdAt
	 */
	public Date getCreatedAt() {
		return createdAt;
	}
	
	/**
	 * @param createdAt
	 *            the createdAt to set
	 */
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}
	
	/**
	 * @return the createdBy
	 */
	public Integer getCreatedBy() {
		return createdBy;
	}
	
	/**
	 * @param createdBy
	 *            the createdBy to set
	 */
	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}
	
	/**
	 * @return the updatedAt
	 */
	public Date getUpdatedAt() {
		return updatedAt;
	}
	
	/**
	 * @param updatedAt
	 *            the updatedAt to set
	 */
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}
	
	/**
	 * @return the updatedBy
	 */
	public Integer getUpdatedBy() {
		return updatedBy;
	}
	
	/**
	 * @param updatedBy
	 *            the updatedBy to set
	 */
	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}
	
	/**
	 * @return the thumbnailUrl
	 */
	public String getThumbnailUrl() {
		return thumbnailUrl;
	}
	
	/**
	 * @param thumbnailUrl
	 *            the thumbnailUrl to set
	 */
	public void setThumbnailUrl(String thumbnailUrl) {
		this.thumbnailUrl = thumbnailUrl;
	}
	
	/**
	 * @return the previewText
	 */
	public String getPreviewText() {
		return previewText;
	}
	
	/**
	 * @param previewText
	 *            the previewText to set
	 */
	public void setPreviewText(String previewText) {
		this.previewText = previewText;
	}
	
	/**
	 * @return the locationLevelTemplate
	 */
	public Integer getLocationLevelTemplate() {
		return locationLevelTemplate;
	}
	
	/**
	 * @param locationLevelTemplate
	 *            the locationLevelTemplate to set
	 */
	public void setLocationLevelTemplate(Integer locationLevelTemplate) {
		this.locationLevelTemplate = locationLevelTemplate;
	}
	
	/**
	 * @return the isDefaultTemplate
	 */
	public Integer getIsDefaultTemplate() {
		return isDefaultTemplate;
	}
	
	/**
	 * @param isDefaultTemplate
	 *            the isDefaultTemplate to set
	 */
	public void setIsDefaultTemplate(Integer isDefaultTemplate) {
		this.isDefaultTemplate = isDefaultTemplate;
	}
	
	/**
	 * @return the isGlobalTemplate
	 */
	public Boolean getIsGlobalTemplate() {
		return isGlobalTemplate;
	}
	
	/**
	 * @param isGlobalTemplate
	 *            the isGlobalTemplate to set
	 */
	public void setIsGlobalTemplate(Boolean isGlobalTemplate) {
		this.isGlobalTemplate = isGlobalTemplate;
	}
	
	/**
	 * @return the subject
	 */
	public String getSubject() {
		return subject;
	}
	
	/**
	 * @param subject
	 *            the subject to set
	 */
	public void setSubject(String subject) {
		this.subject = subject;
	}
	
	/**
	 * @return the isDeleted
	 */
	public Integer getIsDeleted() {
		return isDeleted;
	}
	
	/**
	 * @param isDeleted
	 *            the isDeleted to set
	 */
	public void setIsDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted;
	}
	
	/**
	 * @return the emailCategory
	 */
	public String getEmailCategory() {
		return emailCategory;
	}
	
	/**
	 * @param emailCategory
	 *            the emailCategory to set
	 */
	public void setEmailCategory(String emailCategory) {
		this.emailCategory = emailCategory;
	}
	
	/**
	 * @return the smsCategory
	 */
	public String getSmsCategory() {
		return smsCategory;
	}
	
	/**
	 * @param smsCategory
	 *            the smsCategory to set
	 */
	public void setSmsCategory(String smsCategory) {
		this.smsCategory = smsCategory;
	}
	
}
