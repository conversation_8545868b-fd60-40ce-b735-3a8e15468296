package com.birdeye.campaign.dto;

import java.util.Collection;
import java.util.Collections;

/**
 * 
 * <AUTHOR>
 *
 */
public class RRUsageFunnelReportResponse {
	
	private int									totalPages;
	private String								campaignName;
	private Collection<RRUsageReportResponse>	usageReportResponses	= Collections.emptyList();
	
	public int getTotalPages() {
		return totalPages;
	}
	
	public void setTotalPages(int totalPages) {
		this.totalPages = totalPages;
	}
	
	public Collection<RRUsageReportResponse> getUsageReportResponses() {
		return usageReportResponses;
	}
	
	public void setUsageReportResponses(Collection<RRUsageReportResponse> usageReportResponses) {
		this.usageReportResponses = usageReportResponses;
	}
	
	public String getCampaignName() {
		return campaignName;
	}
	
	public void setCampaignName(String campaignName) {
		this.campaignName = campaignName;
	}
	
}
