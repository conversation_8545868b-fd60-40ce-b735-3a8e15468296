package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class ReviewRequestListDTO implements Serializable {
	
	private static final long	serialVersionUID	= -5895873866629357685L;
	
	private Integer				campaignId;
	
	private Integer				enterpriseId;
	
	private List<Long>			reviewRequestIds;
	
	private Integer				batchNumber;
	
	private Integer				batchSize;
	
	/**
	 * Default constructor
	 */
	public ReviewRequestListDTO() {
	}
	
	/**
	 * @param campaignId
	 * @param enterpriseId
	 * @param reviewRequestIds
	 * @param batchNumber
	 * @param batchSize
	 */
	public ReviewRequestListDTO(Integer campaignId, Integer enterpriseId, List<Long> reviewRequestIds, Integer batchNumber, Integer batchSize) {
		this.campaignId = campaignId;
		this.enterpriseId = enterpriseId;
		this.reviewRequestIds = reviewRequestIds;
		this.batchNumber = batchNumber;
		this.batchSize = batchSize;
	}
	
	/**
	 * @return the campaignId
	 */
	public Integer getCampaignId() {
		return campaignId;
	}
	
	/**
	 * @param campaignId
	 *            the campaignId to set
	 */
	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}
	
	/**
	 * @return the enterpriseId
	 */
	public Integer getEnterpriseId() {
		return enterpriseId;
	}
	
	/**
	 * @param enterpriseId
	 *            the enterpriseId to set
	 */
	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	
	/**
	 * @return the reviewRequestIds
	 */
	public List<Long> getReviewRequestIds() {
		return reviewRequestIds;
	}
	
	/**
	 * @param reviewRequestIds
	 *            the reviewRequestIds to set
	 */
	public void setReviewRequestIds(List<Long> reviewRequestIds) {
		this.reviewRequestIds = reviewRequestIds;
	}
	
	/**
	 * @return the batchNumber
	 */
	public Integer getBatchNumber() {
		return batchNumber;
	}
	
	/**
	 * @param batchNumber
	 *            the batchNumber to set
	 */
	public void setBatchNumber(Integer batchNumber) {
		this.batchNumber = batchNumber;
	}
	
	/**
	 * @return the batchSize
	 */
	public Integer getBatchSize() {
		return batchSize;
	}
	
	/**
	 * @param batchSize
	 *            the batchSize to set
	 */
	public void setBatchSize(Integer batchSize) {
		this.batchSize = batchSize;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
