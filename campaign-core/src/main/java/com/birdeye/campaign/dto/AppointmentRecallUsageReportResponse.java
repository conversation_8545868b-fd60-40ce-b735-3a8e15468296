package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppointmentRecallUsageReportResponse extends BaseUsageReportResponse {
	
	private AppointmentRecallSentInfo						sentInfo		= new AppointmentRecallSentInfo();
	private AppointmentRecallDeliveredInfo					deliveredInfo	= new AppointmentRecallDeliveredInfo();
	private AppointmentRecallOpenedInfo						openInfo		= new AppointmentRecallOpenedInfo();
	private AppointmentRecallClickedInfo					clickInfo		= new AppointmentRecallClickedInfo();
	private AppointmentRecallBookedInfo						bookedInfo		= new AppointmentRecallBookedInfo();
	private AppointmentRecallUsageResponseFailureMessage	failureMessage	= new AppointmentRecallUsageResponseFailureMessage();
	
	public AppointmentRecallSentInfo getSentInfo() {
		return sentInfo;
	}
	
	public void setSentInfo(AppointmentRecallSentInfo sentInfo) {
		this.sentInfo = sentInfo;
	}
	
	public AppointmentRecallDeliveredInfo getDeliveredInfo() {
		return deliveredInfo;
	}
	
	public void setDeliveredInfo(AppointmentRecallDeliveredInfo deliveredInfo) {
		this.deliveredInfo = deliveredInfo;
	}

	
	/**
	 * @return the openInfo
	 */
	public AppointmentRecallOpenedInfo getOpenInfo() {
		return openInfo;
	}

	/**
	 * @param openInfo the openInfo to set
	 */
	public void setOpenInfo(AppointmentRecallOpenedInfo openInfo) {
		this.openInfo = openInfo;
	}

	/**
	 * @return the clickInfo
	 */
	public AppointmentRecallClickedInfo getClickInfo() {
		return clickInfo;
	}

	/**
	 * @param clickInfo the clickInfo to set
	 */
	public void setClickInfo(AppointmentRecallClickedInfo clickInfo) {
		this.clickInfo = clickInfo;
	}

	public AppointmentRecallBookedInfo getBookedInfo() {
		return bookedInfo;
	}
	
	public void setBookedInfo(AppointmentRecallBookedInfo bookedInfo) {
		this.bookedInfo = bookedInfo;
	}
	
	public AppointmentRecallUsageResponseFailureMessage getFailureMessage() {
		return failureMessage;
	}
	
	public void setFailureMessage(AppointmentRecallUsageResponseFailureMessage failureMessage) {
		this.failureMessage = failureMessage;
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder sb = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return sb.toString();
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class AppointmentRecallSentInfo {
		
		private long	total;
		private double	percent;
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public void addTotal(Long total) {
			this.total += total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class AppointmentRecallDeliveredInfo {
		
		private long	total;
		private double	percent;
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public void addTotal(Long total) {
			this.total += total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class AppointmentRecallOpenedInfo {
		
		private long	total;
		private double	percent;
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public void addTotal(Long total) {
			this.total += total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class AppointmentRecallClickedInfo {
		
		private long	total;
		private double	percent;
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public void addTotal(Long total) {
			this.total += total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class AppointmentRecallBookedInfo {
		
		private long	total;
		private double	percent;
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public void addTotal(Long total) {
			this.total += total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class AppointmentRecallUsageResponseFailureMessage implements Serializable {
		
		private static final long							serialVersionUID	= -3538255383054840743L;
		private long										total;
		private double										percent;
		private List<AppointmentRecallFailureReasonMessage>	failureReasons		= new ArrayList<>();
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public void addTotal(Long total) {
			this.total += total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
		public List<AppointmentRecallFailureReasonMessage> getFailureReasons() {
			return failureReasons;
		}
		
		public void setFailureReasons(List<AppointmentRecallFailureReasonMessage> failureReasons) {
			this.failureReasons = failureReasons;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class AppointmentRecallFailureReasonMessage {
		
		private Long	count	= 0l;
		private Double	percent;
		private Integer	failureId;
		private String	failureReason;
		
		public AppointmentRecallFailureReasonMessage() {
			super();
		}
		
		public AppointmentRecallFailureReasonMessage(Integer failureId) {
			super();
			this.failureId = failureId;
		}
		
		public Long getCount() {
			return count;
		}
		
		public void setCount(Long count) {
			this.count = count;
		}
		
		public Double getPercent() {
			return percent;
		}
		
		public void setPercent(Double percent) {
			this.percent = percent;
		}
		
		public Integer getFailureId() {
			return failureId;
		}
		
		public void setFailureId(Integer failureId) {
			this.failureId = failureId;
		}
		
		public String getFailureReason() {
			return failureReason;
		}
		
		public void setFailureReason(String failureReason) {
			this.failureReason = failureReason;
		}
		
	}
	
}
