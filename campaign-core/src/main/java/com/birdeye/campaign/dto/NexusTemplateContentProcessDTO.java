package com.birdeye.campaign.dto;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class NexusTemplateContentProcessDTO {
	
	private String	content;
	
	private String	failureReason;
	
	private Integer	externalUId;
	
	private Integer	businessId;
	
	private Long	requestId;
	
	private String	emailType;
	
	public NexusTemplateContentProcessDTO() {
		
	}
	
	public NexusTemplateContentProcessDTO(String failureReason, Integer externalUId) {
		this.failureReason = failureReason;
		this.externalUId = externalUId;
	}
	
	public NexusTemplateContentProcessDTO(String content, Integer externalUId, Long requestId, String emailType, Integer businessId) {
		this.content = content;
		this.externalUId = externalUId;
		this.requestId = requestId;
		this.emailType = emailType;
		this.businessId = businessId;
	}
	
	/**
	 * @return the content
	 */
	public String getContent() {
		return content;
	}
	
	/**
	 * @param content
	 *            the content to set
	 */
	public void setContent(String content) {
		this.content = content;
	}
	
	/**
	 * @return the failureReason
	 */
	public String getFailureReason() {
		return failureReason;
	}
	
	/**
	 * @param failureReason
	 *            the failureReason to set
	 */
	public void setFailureReason(String failureReason) {
		this.failureReason = failureReason;
	}
	
	/**
	 * @return the externalUId
	 */
	public Integer getExternalUId() {
		return externalUId;
	}

	/**
	 * @param externalUId the externalUId to set
	 */
	public void setExternalUId(Integer externalUId) {
		this.externalUId = externalUId;
	}

	/**
	 * @return the businessId
	 */
	public Integer getBusinessId() {
		return businessId;
	}
	
	/**
	 * @param businessId
	 *            the businessId to set
	 */
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	/**
	 * @return the requestId
	 */
	public Long getRequestId() {
		return requestId;
	}
	
	/**
	 * @param requestId
	 *            the requestId to set
	 */
	public void setRequestId(Long requestId) {
		this.requestId = requestId;
	}
	
	/**
	 * @return the emailType
	 */
	public String getEmailType() {
		return emailType;
	}
	
	/**
	 * @param emailType
	 *            the emailType to set
	 */
	public void setEmailType(String emailType) {
		this.emailType = emailType;
	}
	
	@Override
	public String toString() {
		return new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE).setExcludeFieldNames("content").toString();
	}
	
}
