/**
 * @file_name MessengerTemplatesListingResponse.java
 * @created_date 10 Jun 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * @file_name MessengerTemplatesListingResponse.java
 * @created_date 10 Jun 2020
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public class MessengerTemplatesListingResponse implements Serializable {
	
	/**
	 * 
	 */
	private static final long				serialVersionUID	= -8862682776661371333L;
	private List<BusinessTemplateMessage>	templates;									//sms templates
	private List<BusinessTemplateMessage>	emailTemplates;
	private Map<Integer, List<Integer>>		locationTemplatesMap;
	
	/**
	 * @return the templates
	 */
	public List<BusinessTemplateMessage> getTemplates() {
		return templates;
	}
	
	/**
	 * @param templates
	 *            the templates to set
	 */
	public void setTemplates(List<BusinessTemplateMessage> templates) {
		this.templates = templates;
	}
	
	/**
	 * @return the emailTemplates
	 */
	public List<BusinessTemplateMessage> getEmailTemplates() {
		return emailTemplates;
	}
	
	/**
	 * @param emailTemplates
	 *            the emailTemplates to set
	 */
	public void setEmailTemplates(List<BusinessTemplateMessage> emailTemplates) {
		this.emailTemplates = emailTemplates;
	}
	/**
	 * 
	 * @return
	 */
	public Map<Integer, List<Integer>> getLocationTemplatesMap() {
		return locationTemplatesMap;
	}
	
	/**
	 * 
	 * @param locationTemplatesMap
	 */
	public void setLocationTemplatesMap(Map<Integer, List<Integer>> locationTemplatesMap) {
		this.locationTemplatesMap = locationTemplatesMap;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
