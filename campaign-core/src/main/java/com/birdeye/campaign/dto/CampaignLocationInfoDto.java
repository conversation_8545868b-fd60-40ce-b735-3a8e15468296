package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CampaignLocationInfoDto implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 8842887485200551845L;
	
	private Integer				campaignId;
	
	private String				lvlAliasId;
	
	private String				lvlAlias;
	
	private List<String>		lvlIds;
	
	private Integer             splitCampaignId;
	
	public CampaignLocationInfoDto() {
		
	}
	
	public CampaignLocationInfoDto(Integer campaignId, String lvlAliasId, String lvlAlias, Object lvlIds) {
		this.campaignId = campaignId;
		this.lvlAliasId = lvlAliasId;
		this.lvlAlias = lvlAlias;
		
		List<String> lvlIdList = new ArrayList<>();
		ObjectMapper objectMapper = new ObjectMapper();
		try {
			String json = objectMapper.writeValueAsString(lvlIds);
			lvlIdList = objectMapper.readValue(json, new TypeReference<List<String>>() {
			});
		} catch (Exception e) {
			
		}
		this.lvlIds = lvlIdList;
	}
	
	/**
	 * @return the campaignId
	 */
	public Integer getCampaignId() {
		return campaignId;
	}
	
	/**
	 * @param campaignId
	 *            the campaignId to set
	 */
	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}
	
	/**
	 * @return the lvlAliasId
	 */
	public String getLvlAliasId() {
		return lvlAliasId;
	}
	
	/**
	 * @param lvlAliasId
	 *            the lvlAliasId to set
	 */
	public void setLvlAliasId(String lvlAliasId) {
		this.lvlAliasId = lvlAliasId;
	}
	
	/**
	 * @return the lvlAlias
	 */
	public String getLvlAlias() {
		return lvlAlias;
	}
	
	/**
	 * @param lvlAlias
	 *            the lvlAlias to set
	 */
	public void setLvlAlias(String lvlAlias) {
		this.lvlAlias = lvlAlias;
	}
	
	/**
	 * @return the lvlIds
	 */
	public List<String> getLvlIds() {
		return lvlIds;
	}
	
	/**
	 * @param lvlIds
	 *            the lvlIds to set
	 */
	public void setLvlIds(List<String> lvlIds) {
		this.lvlIds = lvlIds;
	}

	/**
	 * @return the splitCampaignId
	 */
	public Integer getSplitCampaignId() {
		return splitCampaignId;
	}

	/**
	 * @param splitCampaignId the splitCampaignId to set
	 */
	public void setSplitCampaignId(Integer splitCampaignId) {
		this.splitCampaignId = splitCampaignId;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
