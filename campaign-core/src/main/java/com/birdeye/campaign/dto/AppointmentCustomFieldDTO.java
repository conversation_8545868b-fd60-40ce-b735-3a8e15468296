package com.birdeye.campaign.dto;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_NULL)
public class AppointmentCustomFieldDTO implements Serializable {
	
	private static final long	serialVersionUID	= -281673098397154465L;
	
	private Integer				id;
	private String				fieldName;
	private String				type;
	private Object				value;
	private Boolean				deleted;
	
	/**
	 * Default constructor
	 */
	public AppointmentCustomFieldDTO() {
		super();
	}
	
	/**
	 * Constructor with name & value
	 * 
	 * @param fieldName
	 * @param fieldValue
	 */
	public AppointmentCustomFieldDTO(String fieldName, String fieldValue) {
		super();
		this.fieldName = fieldName;
		this.value = fieldValue;
	}
	
	/**
	 * Constructor with id, name & value
	 * 
	 * @param id
	 * @param fieldName
	 * @param fieldValue
	 */
	public AppointmentCustomFieldDTO(Integer id, String fieldName, String fieldValue) {
		super();
		this.id = id;
		this.fieldName = fieldName;
		this.value = fieldValue;
	}
	
	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}
	
	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	
	/**
	 * @return the fieldName
	 */
	public String getFieldName() {
		return fieldName;
	}
	
	/**
	 * @param fieldName
	 *            the fieldName to set
	 */
	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}
	
	/**
	 * @return the type
	 */
	public String getType() {
		return type;
	}
	
	/**
	 * @param type
	 *            the type to set
	 */
	public void setType(String type) {
		this.type = type;
	}
	
	/**
	 * @return the value
	 */
	public Object getValue() {
		return value;
	}
	
	/**
	 * @param value
	 *            the value to set
	 */
	public void setValue(Object value) {
		this.value = value;
	}
	
	/**
	 * @return the deleted
	 */
	public Boolean getDeleted() {
		return deleted;
	}
	
	/**
	 * @param deleted
	 *            the deleted to set
	 */
	public void setDeleted(Boolean deleted) {
		this.deleted = deleted;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
