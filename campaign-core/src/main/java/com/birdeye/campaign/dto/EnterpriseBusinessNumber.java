package com.birdeye.campaign.dto;

import org.apache.commons.lang.StringUtils;
import org.codehaus.jackson.map.annotate.JsonSerialize;

import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.utils.CoreUtils;
import com.birdeye.campaign.workbook.service.CellType;
import com.birdeye.campaign.workbook.service.ExcelCell;
import com.birdeye.campaign.workbook.service.ExcelData;

@JsonSerialize(include=JsonSerialize.Inclusion.NON_NULL)
@ExcelData(name = "EnterpriseBusinessNumber")
public class EnterpriseBusinessNumber {

	private Integer id;
    
    @ExcelCell(order=1, header=Constants.ENTERPRISE_BUSINESS_NUMBER_LOCATION_COLUMN, cellType=CellType.TEXT)
	private String name;
    
    @ExcelCell(order=2, header=Constants.ENTERPRISE_BUSINESS_NUMBER_BUSINESS_NUMBER_COLUMN, cellType=CellType.TEXT)
	private String smsPhone;
	
	public EnterpriseBusinessNumber(Integer id, String alias1, String name, String smsPhone) {
		super();
		this.id = id;
		this.name = (name == null || StringUtils.isEmpty(name)) ? alias1 : name;
		this.smsPhone = StringUtils.isEmpty(smsPhone) ? "" : CoreUtils.formatPhoneNumber(smsPhone, null);
	}
	
	public EnterpriseBusinessNumber(Integer id, String nameOrAlias, String smsPhone) {
		super();
		this.id = id;
		this.name = nameOrAlias;
		this.smsPhone = StringUtils.isEmpty(smsPhone) ? "" : CoreUtils.formatPhoneNumber(smsPhone, null);
	}
	
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSmsPhone() {
		return smsPhone;
	}

	public void setSmsPhone(String smsPhone) {
		this.smsPhone = smsPhone;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("EnterpriseBusinessNumber [id=");
		builder.append(id);
		builder.append(", name=");
		builder.append(name);
		builder.append(", smsPhone=");
		builder.append(smsPhone);
		builder.append("]");
		return builder.toString();
	}
	
	
}
