package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.codehaus.jackson.map.annotate.JsonSerialize;
import org.codehaus.jackson.map.annotate.JsonSerialize.Inclusion;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
@JsonSerialize(include = Inclusion.NON_NULL)
public class CXReportWrapperResponse implements Serializable{

	private static final long serialVersionUID = -6894377408895423975L;

	private String type;
	private Map<String,Object> response = new HashMap<>();
	
	/**
	 * Add response for type
	 * @param type
	 * @param clickReportResponse
	 */
	public void addResponseForType(String type , Object clickReportResponse) {
		response.put(type, clickReportResponse);
	}

	/**
	 * @return the response
	 */
	public Map<String, Object> getResponse() {
		return response;
	}

	/**
	 * @param response the response to set
	 */
	public void setResponse(Map<String, Object> response) {
		this.response = response;
	}


	/**
	 * @return the type
	 */
	public String getType() {
		return type;
	}

	/**
	 * @param type the type to set
	 */
	public void setType(String type) {
		this.type = type;
	}

	@Override
	public String toString() {
		ReflectionToStringBuilder sb = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return sb.toString();
	}
	
	
}
