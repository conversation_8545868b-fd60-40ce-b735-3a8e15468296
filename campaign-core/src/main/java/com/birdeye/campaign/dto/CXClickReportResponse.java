package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CXClickReportResponse implements Serializable {
	
	private static final long				serialVersionUID	= 1271875350780557419L;
	
	private Long							totalCount			= 0L;
	
	private float							detPer;
	
	private float							promPer;
	
	private float							passPer;
	
	private long							detCount;
	
	private long							promCount;
	
	private long							passCount;
	
	private List<CXClickReportDataPoint>	dataPoints			= Collections.emptyList();
	
	public Long getTotalCount() {
		return totalCount;
	}
	
	public void setTotalCount(Long totalCount) {
		this.totalCount = totalCount;
	}
	
	public float getDetPer() {
		return detPer;
	}
	
	public void setDetPer(float detPer) {
		this.detPer = detPer;
	}
	
	public float getPromPer() {
		return promPer;
	}
	
	public void setPromPer(float promPer) {
		this.promPer = promPer;
	}
	
	public float getPassPer() {
		return passPer;
	}
	
	public void setPassPer(float passPer) {
		this.passPer = passPer;
	}
	
	public List<CXClickReportDataPoint> getDataPoints() {
		return dataPoints;
	}
	
	public void setDataPoints(List<CXClickReportDataPoint> dataPoints) {
		this.dataPoints = dataPoints;
	}
	
	/**
	 * @return the detCount
	 */
	public long getDetCount() {
		return detCount;
	}
	
	/**
	 * @param detCount
	 *            the detCount to set
	 */
	public void setDetCount(long detCount) {
		this.detCount = detCount;
	}
	
	/**
	 * @return the promCount
	 */
	public long getPromCount() {
		return promCount;
	}
	
	/**
	 * @param promCount
	 *            the promCount to set
	 */
	public void setPromCount(long promCount) {
		this.promCount = promCount;
	}
	
	/**
	 * @return the passCount
	 */
	public long getPassCount() {
		return passCount;
	}
	
	/**
	 * @param passCount
	 *            the passCount to set
	 */
	public void setPassCount(long passCount) {
		this.passCount = passCount;
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder sb = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return sb.toString();
	}
}