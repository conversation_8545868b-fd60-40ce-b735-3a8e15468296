package com.birdeye.campaign.dto;

import java.util.List;

public class BusinessAllAITemplatesMessage {
	
	private Integer							totalCount		= 0;
	private Integer							filteredCount	= 0;
	private List<BusinessAITemplateMessage>	templates;
	
	/**
	 * @return the totalCount
	 */
	public Integer getTotalCount() {
		return totalCount;
	}
	
	/**
	 * @param totalCount
	 *            the totalCount to set
	 */
	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}
	
	/**
	 * @return the filteredCount
	 */
	public Integer getFilteredCount() {
		return filteredCount;
	}
	
	/**
	 * @param filteredCount
	 *            the filteredCount to set
	 */
	public void setFilteredCount(Integer filteredCount) {
		this.filteredCount = filteredCount;
	}
	
	/**
	 * @return the templates
	 */
	public List<BusinessAITemplateMessage> getTemplates() {
		return templates;
	}
	
	/**
	 * @param templates
	 *            the templates to set
	 */
	public void setTemplates(List<BusinessAITemplateMessage> templates) {
		this.templates = templates;
	}
	
}
