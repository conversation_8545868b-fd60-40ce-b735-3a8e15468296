package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReferralUsageSharedReportResponse implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private Integer totalCount;
	
	private List<ReferralUsageSharedReportDTO> data;

	public ReferralUsageSharedReportResponse() {
		super();
	}

	public ReferralUsageSharedReportResponse(Integer totalCount, List<ReferralUsageSharedReportDTO> data) {
		super();
		this.totalCount = totalCount;
		this.data = data;
	}

	public Integer getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}

	public List<ReferralUsageSharedReportDTO> getData() {
		return data;
	}

	public void setData(List<ReferralUsageSharedReportDTO> data) {
		this.data = data;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
