package com.birdeye.campaign.dto;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.entity.Campaign;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
public class CampaignInfoDTO implements Serializable {
	
	private static final long	serialVersionUID	= -8450437370047849921L;
	
	private Integer				campaignId;
	
	private String				campaignName;
	
	public CampaignInfoDTO() {
	}
	
	public CampaignInfoDTO(Integer campaignId, String name) {
		this.campaignId = campaignId;
		this.campaignName = name;
	}
	
	public CampaignInfoDTO(Campaign campaign) {
		this.campaignId = campaign.getId();
		this.campaignName = campaign.getName();
	}
	
	/**
	 * @return the campaignId
	 */
	public Integer getCampaignId() {
		return campaignId;
	}
	
	/**
	 * @param campaignId
	 *            the campaignId to set
	 */
	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}
	
	/**
	 * @return the campaignName
	 */
	public String getCampaignName() {
		return campaignName;
	}
	
	/**
	 * @param campaignName
	 *            the campaignName to set
	 */
	public void setCampaignName(String campaignName) {
		this.campaignName = campaignName;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
