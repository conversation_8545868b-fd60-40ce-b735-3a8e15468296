package com.birdeye.campaign.dto;

import java.io.Serializable;

public class RunCampaignKafkaMessage implements Serializable {

	private static final long serialVersionUID = -2842839527147882501L;

	private Long requestId;
	
	private String requestType;

	public Long getRequestId() {
		return requestId;
	}

	public void setRequestId(Long requestId) {
		this.requestId = requestId;
	}

	public RunCampaignKafkaMessage(Long requestId, String requestType) {
		this.requestId = requestId;
		this.requestType = requestType;
	}

	public String getRequestType() {
		return requestType;
	}

	public void setRequestType(String requestType) {
		this.requestType = requestType;
	}

}
