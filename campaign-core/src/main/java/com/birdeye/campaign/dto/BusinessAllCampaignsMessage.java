package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Wrapper object for all campaigns for Account
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class BusinessAllCampaignsMessage implements Serializable{

	private static final long serialVersionUID = 4344421874158420894L;

	private Integer totalCount = 0;
	
	private Integer filteredCount = 0;
	
	private List<BusinessCampaignMessage> data;

	public Integer getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}

	public List<BusinessCampaignMessage> getData() {
		return data;
	}

	public void setData(List<BusinessCampaignMessage> data) {
		this.data = data;
	}

	public Integer getFilteredCount() {
		return filteredCount;
	}

	public void setFilteredCount(Integer filteredCount) {
		this.filteredCount = filteredCount;
	}
}
