package com.birdeye.campaign.dto;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * Message to hold campaign audience data for single business customer
 * <AUTHOR>
 *
 */
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BusinessCustomerCampaignMessage implements Serializable{

	private static final long serialVersionUID = -2997767542418906069L;
	
	private String customerName;
	
	private String source; //email/sms
	
	@JsonIgnore
	private Integer customerId;
	
	@JsonIgnore
	private Integer businessId;
	private String location;
	private String status;
	private String lastActivityDate;
	private String lastActivityDateWithTime;
	
	public BusinessCustomerCampaignMessage() {
		
	}
	
	public BusinessCustomerCampaignMessage(Integer customerId ,Integer businessId, String source) {
		this.customerId = customerId;
		this.source = source;
		this.businessId = businessId;
	}
	
	/**
	 * @return the customerName
	 */
	public String getCustomerName() {
		return customerName;
	}

	/**
	 * @param customerName the customerName to set
	 */
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	/**
	 * @return the location
	 */
	public String getLocation() {
		return location;
	}

	/**
	 * @param location the location to set
	 */
	public void setLocation(String location) {
		this.location = location;
	}

	/**
	 * @return the status
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * @param status the status to set
	 */
	public void setStatus(String status) {
		this.status = status;
	}

	/**
	 * @return the lastActivityDate
	 */
	public String getLastActivityDate() {
		return lastActivityDate;
	}

	/**
	 * @param lastActivityDate the lastActivityDate to set
	 */
	public void setLastActivityDate(String lastActivityDate) {
		this.lastActivityDate = lastActivityDate;
	}
	
	/**
	 * @return the lastActivityDateWithTime
	 */
	public String getLastActivityDateWithTime() {
		return lastActivityDateWithTime;
	}

	/**
	 * @param lastActivityDateWithTime the lastActivityDateWithTime to set
	 */
	public void setLastActivityDateWithTime(String lastActivityDateWithTime) {
		this.lastActivityDateWithTime = lastActivityDateWithTime;
	}

	/**
	 * @return the source
	 */
	public String getSource() {
		return source;
	}

	/**
	 * @param source the source to set
	 */
	public void setSource(String source) {
		this.source = source;
	}

	/**
	 * @return the customerId
	 */
	public Integer getCustomerId() {
		return customerId;
	}

	/**
	 * @param customerId the customerId to set
	 */
	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	/**
	 * @return the businessId
	 */
	public Integer getBusinessId() {
		return businessId;
	}

	/**
	 * @param businessId the businessId to set
	 */
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	

}