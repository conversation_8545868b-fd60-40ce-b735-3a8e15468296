package com.birdeye.campaign.dto;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class ReferralSharedReportDTO extends BaseUsageReportResponse implements Serializable {
	
	private static final long	serialVersionUID	= -6581860591754326569L;
	private Integer				totalShared;
	private Long				sharedViaFacebook;
	private Long				sharedViaFacebookMessenger;
	private Long				sharedViaEmail;
	private Long				sharedViaSms;
	private Long				sharedViaReferralLink;
	private Long				sharedViaWordOfMouth;
	
	public ReferralSharedReportDTO() {
		
	}
	
	public ReferralSharedReportDTO(ReferralSharedReportResponse referralSharedReportResponse) {
		super();
		
		this.setBusinessId(referralSharedReportResponse.getBusinessId());
		this.setBusinessNumber(referralSharedReportResponse.getBusinessNumber());
		this.setName(referralSharedReportResponse.getName());
		
		this.totalShared = referralSharedReportResponse.getTotalShared();
		this.sharedViaFacebook = referralSharedReportResponse.getSharedViaFacebook().getTotal();
		this.sharedViaFacebookMessenger = referralSharedReportResponse.getSharedViaFacebookMessenger().getTotal();
		this.sharedViaEmail = referralSharedReportResponse.getSharedViaEmail().getTotal();
		this.sharedViaSms = referralSharedReportResponse.getSharedViaSms().getTotal();
		this.sharedViaReferralLink = referralSharedReportResponse.getSharedViaReferralLink().getTotal();
		this.sharedViaWordOfMouth = referralSharedReportResponse.getSharedViaWordOfMouth().getTotal();
	}
	
	public Integer getTotalShared() {
		return totalShared;
	}
	
	public void setTotalShared(Integer totalShared) {
		this.totalShared = totalShared;
	}


	public Long getSharedViaFacebook() {
		return sharedViaFacebook;
	}
	
	public void setSharedViaFacebook(Long sharedViaFacebook) {
		this.sharedViaFacebook = sharedViaFacebook;
	}
	
	public Long getSharedViaFacebookMessenger() {
		return sharedViaFacebookMessenger;
	}
	
	public void setSharedViaFacebookMessenger(Long sharedViaFacebookMessenger) {
		this.sharedViaFacebookMessenger = sharedViaFacebookMessenger;
	}
	
	public Long getSharedViaEmail() {
		return sharedViaEmail;
	}
	
	public void setSharedViaEmail(Long sharedViaEmail) {
		this.sharedViaEmail = sharedViaEmail;
	}
	
	public Long getSharedViaSms() {
		return sharedViaSms;
	}
	
	public void setSharedViaSms(Long sharedViaSms) {
		this.sharedViaSms = sharedViaSms;
	}
	
	public Long getSharedViaReferralLink() {
		return sharedViaReferralLink;
	}
	
	public void setSharedViaReferralLink(Long sharedViaReferralLink) {
		this.sharedViaReferralLink = sharedViaReferralLink;
	}
	
	public Long getSharedViaWordOfMouth() {
		return sharedViaWordOfMouth;
	}

	public void setSharedViaWordOfMouth(Long sharedViaWordOfMouth) {
		this.sharedViaWordOfMouth = sharedViaWordOfMouth;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
