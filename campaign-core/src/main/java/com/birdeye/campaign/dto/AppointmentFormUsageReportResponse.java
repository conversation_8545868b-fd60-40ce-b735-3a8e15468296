package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppointmentFormUsageReportResponse extends BaseUsageReportResponse {
	
	private AppointmentFormSentInfo						sentInfo		= new AppointmentFormSentInfo();
	private AppointmentFormDeliveredInfo				deliveredInfo	= new AppointmentFormDeliveredInfo();
	private AppointmentFormOpenedInfo					openInfo		= new AppointmentFormOpenedInfo();
	private AppointmentFormClickedInfo					clickInfo		= new AppointmentFormClickedInfo();
	private AppointmentFormFilledInfo					formFilledInfo		= new AppointmentFormFilledInfo();
	private AppointmentFormUsageResponseFailureMessage	failureMessage	= new AppointmentFormUsageResponseFailureMessage();
	
	public AppointmentFormSentInfo getSentInfo() {
		return sentInfo;
	}
	
	public void setSentInfo(AppointmentFormSentInfo sentInfo) {
		this.sentInfo = sentInfo;
	}
	
	public AppointmentFormDeliveredInfo getDeliveredInfo() {
		return deliveredInfo;
	}
	
	public void setDeliveredInfo(AppointmentFormDeliveredInfo deliveredInfo) {
		this.deliveredInfo = deliveredInfo;
	}
	
	/**
	 * @return the openInfo
	 */
	public AppointmentFormOpenedInfo getOpenInfo() {
		return openInfo;
	}
	
	/**
	 * @param openInfo
	 *            the openInfo to set
	 */
	public void setOpenInfo(AppointmentFormOpenedInfo openInfo) {
		this.openInfo = openInfo;
	}
	
	/**
	 * @return the clickInfo
	 */
	public AppointmentFormClickedInfo getClickInfo() {
		return clickInfo;
	}
	
	/**
	 * @param clickInfo
	 *            the clickInfo to set
	 */
	public void setClickInfo(AppointmentFormClickedInfo clickInfo) {
		this.clickInfo = clickInfo;
	}
	
	public AppointmentFormFilledInfo getFormFilledInfo() {
		return formFilledInfo;
	}
	
	public void setFormFilledInfo(AppointmentFormFilledInfo filledInfo) {
		this.formFilledInfo = filledInfo;
	}
	
	public AppointmentFormUsageResponseFailureMessage getFailureMessage() {
		return failureMessage;
	}
	
	public void setFailureMessage(AppointmentFormUsageResponseFailureMessage failureMessage) {
		this.failureMessage = failureMessage;
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder sb = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return sb.toString();
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class AppointmentFormSentInfo {
		private long	total;
		private double	percent;
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public void addTotal(Long total) {
			this.total += total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class AppointmentFormDeliveredInfo {
		
		private long	total;
		private double	percent;
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public void addTotal(Long total) {
			this.total += total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class AppointmentFormOpenedInfo {
		
		private long	total;
		private double	percent;
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public void addTotal(Long total) {
			this.total += total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class AppointmentFormClickedInfo {
		
		private long	total;
		private double	percent;
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public void addTotal(Long total) {
			this.total += total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class AppointmentFormFilledInfo {
		
		private long	total;
		private double	percent;
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public void addTotal(Long total) {
			this.total += total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class AppointmentFormFailureReasonMessage {
		
		private Long	count	= 0l;
		private Double	percent;
		private Integer	failureId;
		private String	failureReason;
		
		public AppointmentFormFailureReasonMessage() {
			super();
		}
		
		public AppointmentFormFailureReasonMessage(Integer failureId) {
			super();
			this.failureId = failureId;
		}
		
		public Long getCount() {
			return count;
		}
		
		public void setCount(Long count) {
			this.count = count;
		}
		
		public Double getPercent() {
			return percent;
		}
		
		public void setPercent(Double percent) {
			this.percent = percent;
		}
		
		public Integer getFailureId() {
			return failureId;
		}
		
		public void setFailureId(Integer failureId) {
			this.failureId = failureId;
		}
		
		public String getFailureReason() {
			return failureReason;
		}
		
		public void setFailureReason(String failureReason) {
			this.failureReason = failureReason;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class AppointmentFormUsageResponseFailureMessage implements Serializable {
		
		private static final long							serialVersionUID	= -3538255383054840743L;
		private long										total;
		private double										percent;
		private List<AppointmentFormFailureReasonMessage>	failureReasons		= new ArrayList<>();
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public void addTotal(Long total) {
			this.total += total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
		public List<AppointmentFormFailureReasonMessage> getFailureReasons() {
			return failureReasons;
		}
		
		public void setFailureReasons(List<AppointmentFormFailureReasonMessage> failureReasons) {
			this.failureReasons = failureReasons;
		}
		
	}
}
