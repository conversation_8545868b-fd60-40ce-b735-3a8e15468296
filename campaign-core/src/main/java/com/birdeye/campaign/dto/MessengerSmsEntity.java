package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.Date;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.request.MessengerMediaInfo;

/**
 * 
 * <AUTHOR>
 *
 */
public class MessengerSmsEntity implements Serializable {
	
	private static final long	serialVersionUID	= -9040350765197316736L;
	
	private Integer				smsId;
	private Integer				campaignId;
	private Long				reviewRequestId;
	private String				type;
	private String				userId;
	private Integer				customerId;
	private Integer				errorCode;
	private String				failureReason;
	private String				commType;
	private MessengerMediaInfo	mediaInfo;
	private String				surveyType;
	private Integer				surveyId;
	private Integer				questionId;
	private Integer				locationId;
	private Integer				enterpriseId;
	private String				requestType;
	private Integer				appointmentId;
	private SmsData             smsData;
	private String              rType;
	private boolean				appointmentConfirmOptionEnabled;
	private boolean				appointmentRescheduleOptionEnabled;
	private boolean				appointmentCancelOptionEnabled;
	private Date				sentOn;
	
	public MessengerSmsEntity() {
		
	}
	
	public MessengerSmsEntity(Integer customerId, Integer smsId, Integer campaignId, Long reviewRequestId, String type, String userId, MessengerMediaInfo mediaInfo,
			String surveyType, Integer surveyId, Integer questionId, Integer locationId, Integer enterpriseId, String requestType, String rType) {
		this.smsId = smsId;
		this.campaignId = campaignId;
		this.reviewRequestId = reviewRequestId;
		this.type = type;
		this.userId = userId;
		this.mediaInfo = mediaInfo;
		this.customerId = customerId;
		this.surveyType = surveyType;
		this.surveyId = surveyId;
		this.questionId = questionId;
		this.locationId = locationId;
		this.enterpriseId = enterpriseId;
		this.requestType = requestType;	
		this.rType = rType;
	}
	
	public MessengerSmsEntity(Integer smsId, Integer campaignId, Long reviewRequestId, String userId, String surveyType, Integer surveyId, Integer questionId, Integer locationId,
			Integer enterpriseId, String requestType, String rType) {
		this.smsId = smsId;
		this.campaignId = campaignId;
		this.reviewRequestId = reviewRequestId;
		this.userId = userId;
		this.surveyType = surveyType;
		this.surveyId = surveyId;
		this.questionId = questionId;
		this.locationId = locationId;
		this.enterpriseId = enterpriseId;
		this.requestType = requestType;
		this.rType = rType;
	}
	
	public MessengerSmsEntity(Integer smsId, Integer campaignId, Long reviewRequestId, String userId, String surveyType, Integer surveyId, Integer questionId, Integer locationId,
			Integer enterpriseId, String requestType, String rType, Integer customerId, Date sentOn) {
		this.smsId = smsId;
		this.campaignId = campaignId;
		this.reviewRequestId = reviewRequestId;
		this.userId = userId;
		this.surveyType = surveyType;
		this.surveyId = surveyId;
		this.questionId = questionId;
		this.locationId = locationId;
		this.enterpriseId = enterpriseId;
		this.requestType = requestType;
		this.rType = rType;
		this.customerId = customerId;
		this.sentOn = sentOn;
	}
	
	public MessengerSmsEntity(Integer smsId, Long reviewRequestId, Integer customerId, Integer errorCode, String failureReason, String commType) {
		this.smsId = smsId;
		this.customerId = customerId;
		this.reviewRequestId = reviewRequestId;
		this.failureReason = failureReason;
		this.errorCode = errorCode;
		this.commType = commType;
	}
	
	public static class SmsData implements Serializable {
		private static final long	serialVersionUID	= 8168419145646148533L;
		
		private int					businessId;
		
		private int					customerId			= 0;
		
		private int					toUserId			= 0;
		
		private String				fromNumber;
		
		private String				toNumber;
		
		private String				messageBody;
		
		private Date				createDate;
		
		private String				mediaURL;
		
		private Date				sentOn;
		
		private Integer				encrypted			= 0;
		
		private String				messageBodyUnencrypted;
		
		public int getBusinessId() {
			return businessId;
		}
		
		public void setBusinessId(int businessId) {
			this.businessId = businessId;
		}
		
		public int getCustomerId() {
			return customerId;
		}

		public void setCustomerId(int customerId) {
			this.customerId = customerId;
		}

		public int getToUserId() {
			return toUserId;
		}
		
		public void setToUserId(int toUserId) {
			this.toUserId = toUserId;
		}
		
		public String getFromNumber() {
			return fromNumber;
		}
		
		public void setFromNumber(String fromNumber) {
			this.fromNumber = fromNumber;
		}
		
		public String getToNumber() {
			return toNumber;
		}
		
		public void setToNumber(String toNumber) {
			this.toNumber = toNumber;
		}
		
		public String getMessageBody() {
			return messageBody;
		}
		
		public void setMessageBody(String messageBody) {
			this.messageBody = messageBody;
		}
		
		public Date getCreateDate() {
			return createDate;
		}
		
		public void setCreateDate(Date createDate) {
			this.createDate = createDate;
		}
		
		public String getMediaURL() {
			return mediaURL;
		}
		
		public void setMediaURL(String mediaURL) {
			this.mediaURL = mediaURL;
		}
		
		public Date getSentOn() {
			return sentOn;
		}
		
		public void setSentOn(Date sentOn) {
			this.sentOn = sentOn;
		}
		
		public Integer getEncrypted() {
			return encrypted;
		}
		
		public void setEncrypted(Integer encrypted) {
			this.encrypted = encrypted;
		}
		
		public String getMessageBodyUnencrypted() {
			return messageBodyUnencrypted;
		}
		
		public void setMessageBodyUnencrypted(String messageBodyUnencrypted) {
			this.messageBodyUnencrypted = messageBodyUnencrypted;
		}

		@Override
		public String toString() {
			return new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE).toString();
		}
		
	}
	
	public MessengerSmsEntity(Integer smsId) {
		this.smsId = smsId;
	}
	
	public Integer getSmsId() {
		return smsId;
	}
	
	public void setSmsId(Integer smsId) {
		this.smsId = smsId;
	}
	
	public Integer getCampaignId() {
		return campaignId;
	}
	
	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}
	
	public Long getReviewRequestId() {
		return reviewRequestId;
	}
	
	public void setReviewRequestId(Long reviewRequestId) {
		this.reviewRequestId = reviewRequestId;
	}
	
	public String getType() {
		return type;
	}
	
	public void setType(String type) {
		this.type = type;
	}
	
	public String getUserId() {
		return userId;
	}
	
	public void setUserId(String userId) {
		this.userId = userId;
	}
	
	public Integer getCustomerId() {
		return customerId;
	}
	
	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}
	
	public Integer getErrorCode() {
		return errorCode;
	}
	
	public void setErrorCode(Integer errorCode) {
		this.errorCode = errorCode;
	}
	
	public String getFailureReason() {
		return failureReason;
	}
	
	public void setFailureReason(String failureReason) {
		this.failureReason = failureReason;
	}
	
	public String getCommType() {
		return commType;
	}
	
	public void setCommType(String commType) {
		this.commType = commType;
	}
	
	public MessengerMediaInfo getMediaInfo() {
		return mediaInfo;
	}
	
	public void setMediaInfo(MessengerMediaInfo mediaInfo) {
		this.mediaInfo = mediaInfo;
	}
	
	public String getSurveyType() {
		return surveyType;
	}
	
	public void setSurveyType(String surveyType) {
		this.surveyType = surveyType;
	}
	
	public Integer getSurveyId() {
		return surveyId;
	}
	
	public void setSurveyId(Integer surveyId) {
		this.surveyId = surveyId;
	}
	
	public Integer getQuestionId() {
		return questionId;
	}
	
	public void setQuestionId(Integer questionId) {
		this.questionId = questionId;
	}
	
	public Integer getLocationId() {
		return locationId;
	}

	public void setLocationId(Integer locationId) {
		this.locationId = locationId;
	}

	public Integer getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public String getRequestType() {
		return requestType;
	}

	public void setRequestType(String requestType) {
		this.requestType = requestType;
	}

	/**
	 * @return the appointmentId
	 */
	public Integer getAppointmentId() {
		return appointmentId;
	}

	/**
	 * @param appointmentId the appointmentId to set
	 */
	public void setAppointmentId(Integer appointmentId) {
		this.appointmentId = appointmentId;
	}

	public SmsData getSmsData() {
		return smsData;
	}

	public void setSmsData(SmsData smsData) {
		this.smsData = smsData;
	}
	
	public void createSmsData(SmsData sms, SmsDto inputSmsData) {
		sms.setBusinessId(inputSmsData.getBusinessId());
		sms.setCustomerId(inputSmsData.getCustomerId());
		sms.setFromNumber(inputSmsData.getFromNumber());
		sms.setToNumber(inputSmsData.getToNumber());
		sms.setMessageBody(inputSmsData.getMessageBody());
		sms.setCreateDate(inputSmsData.getCreateDate());
		sms.setSentOn(inputSmsData.getSentOn());
		sms.setMediaURL(inputSmsData.getMediaURL());
		sms.setEncrypted(inputSmsData.getEncrypted());
		sms.setMessageBodyUnencrypted(inputSmsData.getMessageBodyUnencrypted());
	}
	
	public String getrType() {
		return rType;
	}

	public void setrType(String rType) {
		this.rType = rType;
	}

	public boolean isAppointmentConfirmOptionEnabled() {
		return appointmentConfirmOptionEnabled;
	}

	public void setAppointmentConfirmOptionEnabled(boolean appointmentConfirmOptionEnabled) {
		this.appointmentConfirmOptionEnabled = appointmentConfirmOptionEnabled;
	}

	public boolean isAppointmentRescheduleOptionEnabled() {
		return appointmentRescheduleOptionEnabled;
	}

	public void setAppointmentRescheduleOptionEnabled(boolean appointmentRescheduleOptionEnabled) {
		this.appointmentRescheduleOptionEnabled = appointmentRescheduleOptionEnabled;
	}

	public boolean isAppointmentCancelOptionEnabled() {
		return appointmentCancelOptionEnabled;
	}

	public void setAppointmentCancelOptionEnabled(boolean appointmentCancelOptionEnabled) {
		this.appointmentCancelOptionEnabled = appointmentCancelOptionEnabled;
	}

	public Date getSentOn() {
		return sentOn;
	}

	public void setSentOn(Date sentOn) {
		this.sentOn = sentOn;
	}

	@Override
	public String toString() {
		return new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE).toString();
	}
	
}
