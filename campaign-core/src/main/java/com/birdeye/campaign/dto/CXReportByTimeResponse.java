package com.birdeye.campaign.dto;


import java.io.Serializable;
import java.util.Collections;
import java.util.List;

import org.apache.commons.lang.WordUtils;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.codehaus.jackson.map.annotate.JsonSerialize;
import org.codehaus.jackson.map.annotate.JsonSerialize.Inclusion;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
@JsonSerialize(include = Inclusion.NON_NULL)
public class CXReportByTimeResponse implements Serializable {
	
	private static final long				serialVersionUID	= 6343986634429039198L;
	
	private long							totalCount;
	
	private List<CXReportByTimeDataPoint>	dataPoints			= Collections.emptyList();
	
	private String							groupByType;									// grouping applied on the data
	
	// difference in selected range in days
	private long							dateDiff			= Long.MAX_VALUE;
	
	private float							detPer				= 0;
	
	private float							passPer				= 0;
	
	private float							promPer				= 0;
	
	private long							detCount			= 0;
	
	private long							passCount			= 0;
	
	private long							promCount			= 0;
	
	
	
	public long getTotalCount() {
		return totalCount;
	}
	
	public void setTotalCount(long totalCount) {
		this.totalCount = totalCount;
	}
	
	public List<CXReportByTimeDataPoint> getDataPoints() {
		return dataPoints;
	}
	
	public void setDataPoints(List<CXReportByTimeDataPoint> dataPoints) {
		this.dataPoints = dataPoints;
	}
	
	public String getGroupByType() {
		return groupByType;
	}
	
	public String getGroupByTypeLabel() {
		return WordUtils.capitalizeFully(groupByType);
	}
	
	public void setGroupByType(String groupByType) {
		this.groupByType = groupByType;
	}
	
	public long getDateDiff() {
		return dateDiff;
	}
	
	public void setDateDiff(long dateDiff) {
		this.dateDiff = dateDiff;
	}
	
	public float getDetPer() {
		return detPer;
	}
	
	public void setDetPer(float detPer) {
		this.detPer = detPer;
	}
	
	public float getPassPer() {
		return passPer;
	}
	
	public void setPassPer(float passPer) {
		this.passPer = passPer;
	}
	
	public float getPromPer() {
		return promPer;
	}
	
	public void setPromPer(float promPer) {
		this.promPer = promPer;
	}

	/**
	 * @return the detCount
	 */
	public long getDetCount() {
		return detCount;
	}

	/**
	 * @param detCount the detCount to set
	 */
	public void setDetCount(long detCount) {
		this.detCount = detCount;
	}

	/**
	 * @return the passCount
	 */
	public long getPassCount() {
		return passCount;
	}

	/**
	 * @param passCount the passCount to set
	 */
	public void setPassCount(long passCount) {
		this.passCount = passCount;
	}

	/**
	 * @return the promCount
	 */
	public long getPromCount() {
		return promCount;
	}

	/**
	 * @param promCount the promCount to set
	 */
	public void setPromCount(long promCount) {
		this.promCount = promCount;
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder sb = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return sb.toString();
	}}
