package com.birdeye.campaign.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

public class EnterpriseBusinessNumberList {
	@JsonProperty("data")
	List<EnterpriseBusinessNumber> listEnterpriseBusinessNumber;
	Long totalLocations;

	public Long getTotalLocations() {
		return totalLocations;
	}

	public void setTotalLocations(Long l) {
		this.totalLocations = l;
	}

	public List<EnterpriseBusinessNumber> getListEnterpriseBusinessNumber() {
		return listEnterpriseBusinessNumber;
	}

	public void setListEnterpriseBusinessNumber(List<EnterpriseBusinessNumber> listEnterpriseBusinessNumber) {
		this.listEnterpriseBusinessNumber = listEnterpriseBusinessNumber;
	}

}
