/**
 * @file_name LeadsFormBrandingDTO.java
 * @created_date 12 Oct 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.dto;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * @file_name LeadsFormBrandingDTO.java
 * @created_date 12 Oct 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LeadFormBrandingDTO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5866918863937077781L;
	
	private String logo;
	private String headerColor;
	private String textColor;
	/**
	 * @return the logo
	 */
	public String getLogo() {
		return logo;
	}
	/**
	 * @param logo the logo to set
	 */
	public void setLogo(String logo) {
		this.logo = logo;
	}
	/**
	 * @return the headerColor
	 */
	public String getHeaderColor() {
		return headerColor;
	}
	/**
	 * @param headerColor the headerColor to set
	 */
	public void setHeaderColor(String headerColor) {
		this.headerColor = headerColor;
	}
	/**
	 * @return the textColor
	 */
	public String getTextColor() {
		return textColor;
	}
	/**
	 * @param textColor the textColor to set
	 */
	public void setTextColor(String textColor) {
		this.textColor = textColor;
	}
	
	@Override
	public String toString() {
        ReflectionToStringBuilder b = new ReflectionToStringBuilder(this,ToStringStyle.JSON_STYLE);
        return b.toString();        
	}
	
}
