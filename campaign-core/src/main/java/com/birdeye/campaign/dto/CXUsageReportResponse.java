package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CXUsageReportResponse extends BaseUsageReportResponse {
	
	private CommunicationSentCount		sentInfo			= new CommunicationSentCount();
	private CommunicationDeliveredCount	deliveredInfo		= new CommunicationDeliveredCount();
	private CommunicationOpenCount		openInfo			= new CommunicationOpenCount();
	private ReviewSourceClickInfo		clickInfo			= new ReviewSourceClickInfo();
	private SentimentClickInfo			sentimentClickInfo	= new SentimentClickInfo();
	private CommunicationFailureMessage	failureMessage		= new CommunicationFailureMessage();
	private CommunicationScheduledCount	scheduledInfo		= new CommunicationScheduledCount();
	private Integer						reviewCount			= 0;
	private Double						avgRating			= 0.0;
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class ReviewSourceClickInfo {
		private long						total;
		private double						percent;
		private Collection<ReviewAnalytics>	rcmdCount;
		private Collection<ReviewAnalytics>	nonRcmdCount;
		
		/**
		 * @return the total
		 */
		public long getTotal() {
			return total;
		}
		
		public void addTotal(Long total) {
			this.total += total;
		}
		
		/**
		 * @param total
		 *            the total to set
		 */
		public void setTotal(long total) {
			this.total = total;
		}
		
		/**
		 * @return the percent
		 */
		public double getPercent() {
			return percent;
		}
		
		/**
		 * @param percent
		 *            the percent to set
		 */
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
		public Collection<ReviewAnalytics> getRcmdCount() {
			return rcmdCount;
		}
		
		public void setRcmdCount(Collection<ReviewAnalytics> rcmdCount) {
			this.rcmdCount = rcmdCount;
		}
		
		public Collection<ReviewAnalytics> getNonRcmdCount() {
			return nonRcmdCount;
		}
		
		public void setNonRcmdCount(Collection<ReviewAnalytics> nonRcmdCount) {
			this.nonRcmdCount = nonRcmdCount;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class ReviewAnalytics implements Comparable<ReviewAnalytics> {
		private String	name;
		private String	alias;
		private long		count;
		private double	percent;
		private int		sourceId;
		
		public String getName() {
			return name;
		}
		
		public void setName(String name) {
			this.name = name;
		}
		
		public String getAlias() {
			return alias;
		}
		
		public void setAlias(String alias) {
			this.alias = alias;
		}
		
		public long getCount() {
			return count;
		}
		
		public void setCount(long count) {
			this.count = count;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
		public int getSourceId() {
			return sourceId;
		}
		
		public void setSourceId(int sourceId) {
			this.sourceId = sourceId;
		}
		
		@Override
		public int compareTo(ReviewAnalytics obj) {
			return (-1 * ((int) (this.count - obj.count)));
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class CommunicationSentCount {
		
		private long		total;
		private double	percent;
		private long		reqCount;
		private double		reqReminderCount;
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public double getPercent() {
			return percent;
		}

		public void setPercent(double percent) {
			this.percent = percent;
		}

		public void addTotal(long count) {
			this.total += count;
		}
		
		public long getReqCount() {
			return reqCount;
		}
		
		public void setReqCount(long reqCount) {
			this.reqCount = reqCount;
		}
		
		
		
		public double getReqReminderCount() {
			return reqReminderCount;
		}

		public void setReqReminderCount(double reqReminderCount) {
			this.reqReminderCount = reqReminderCount;
		}

		public void addReqReminderCount(double reqReminderCount) {
			this.reqReminderCount += reqReminderCount;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class CommunicationDeliveredCount {
		
		private long		total;
		private double	percent;
		
		/**
		 * @return the total
		 */
		public long getTotal() {
			return total;
		}
		
		/**
		 * @param total
		 *            the total to set
		 */
		public void setTotal(long total) {
			this.total = total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
		public void addTotal(long count) {
			this.total += count;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class CommunicationScheduledCount {
		
		private long		total;
		private double	percent;
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
		public void addTotal(long count) {
			this.total += count;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class CommunicationOpenCount {
		
		private long		total;
		private double	percent;
		
		/**
		 * @return the total
		 */
		public long getTotal() {
			return total;
		}
		
		/**
		 * @param total
		 *            the total to set
		 */
		public void setTotal(long total) {
			this.total = total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
		public void addTotal(long count) {
			this.total += count;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class SentimentClickInfo {
		private long							total;
		private double							percent;
		private Collection<SentimentAnalytics>	sentimentCount;
		
		/**
		 * @return the total
		 */
		public long getTotal() {
			return total;
		}
		
		public void addTotal(Long total) {
			this.total += total;
		}
		
		/**
		 * @param total
		 *            the total to set
		 */
		public void setTotal(long total) {
			this.total = total;
		}
		
		/**
		 * @return the percent
		 */
		public double getPercent() {
			return percent;
		}
		
		/**
		 * @param percent
		 *            the percent to set
		 */
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
		public Collection<SentimentAnalytics> getSentimentCount() {
			return sentimentCount;
		}
		
		public void setSentimentCount(Collection<SentimentAnalytics> sentimentCount) {
			this.sentimentCount = sentimentCount;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class SentimentAnalytics implements Comparable<SentimentAnalytics> {
		private String	sentimentName;
		private long		sentimentCount;
		private double	percent;
		
		public String getSentimentName() {
			return sentimentName;
		}
		
		public void setSentimentName(String sentimentName) {
			this.sentimentName = sentimentName;
		}
		
		public long getSentimentCount() {
			return sentimentCount;
		}
		
		public void setSentimentCount(long sentimentCount) {
			this.sentimentCount = sentimentCount;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
		public SentimentAnalytics() {
			
		}
		
		public SentimentAnalytics(String sentimentName) {
			this.sentimentName = sentimentName;
		}
		
		@Override
		public int compareTo(SentimentAnalytics obj) {
			return (-1 * (this.sentimentName.compareTo(obj.sentimentName)));
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class CommunicationFailureMessage implements Serializable {
		
		private static final long				serialVersionUID	= 3899077908616673917L;
		
		private long								total;
		private double							percent;
		private List<CxFailureReasonMessage>	failureReasons		= new ArrayList<>();
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
		public void addTotal(Long total) {
			this.total += total;
		}
		
		public List<CxFailureReasonMessage> getFailureReasons() {
			return failureReasons;
		}
		
		public void setFailureReasons(List<CxFailureReasonMessage> failureReasons) {
			this.failureReasons = failureReasons;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class CxFailureReasonMessage {
		
		private Long count = 0l;

		private Double percent;

		private Integer failureId;

		private String failureReason;

		public String getFailureReason() {
			return failureReason;
		}

		public void setFailureReason(String failureReason) {
			this.failureReason = failureReason;
		}

		public Long getCount() {
			return count;
		}

		public void setCount(Long count) {
			this.count = count;
		}

		public Double getPercent() {
			return percent;
		}

		public void setPercent(Double percent) {
			this.percent = percent;
		}
		
		public Integer getFailureId() {
			return failureId;
		}
		
		public void setFailureId(Integer failureId) {
			this.failureId = failureId;
		}
		
		public CxFailureReasonMessage(Integer failureId) {
			super();
			this.failureId = failureId;
		}
		
		public CxFailureReasonMessage() {
			super();
		}
	}
	
	public CommunicationSentCount getSentInfo() {
		return sentInfo;
	}
	
	public void setSentInfo(CommunicationSentCount sentInfo) {
		this.sentInfo = sentInfo;
	}
	
	public CommunicationDeliveredCount getDeliveredInfo() {
		return deliveredInfo;
	}
	
	public void setDeliveredInfo(CommunicationDeliveredCount deliveredInfo) {
		this.deliveredInfo = deliveredInfo;
	}
	
	public CommunicationOpenCount getOpenInfo() {
		return openInfo;
	}
	
	public void setOpenInfo(CommunicationOpenCount openInfo) {
		this.openInfo = openInfo;
	}
	
	public ReviewSourceClickInfo getClickInfo() {
		return clickInfo;
	}
	
	public void setClickInfo(ReviewSourceClickInfo clickInfo) {
		this.clickInfo = clickInfo;
	}
	
	public SentimentClickInfo getSentimentClickInfo() {
		return sentimentClickInfo;
	}
	
	public void setSentimentClickInfo(SentimentClickInfo sentimentClickInfo) {
		this.sentimentClickInfo = sentimentClickInfo;
	}
	
	public CommunicationFailureMessage getFailureMessage() {
		return failureMessage;
	}
	
	public void setFailureMessage(CommunicationFailureMessage failureMessage) {
		this.failureMessage = failureMessage;
	}

	public CommunicationScheduledCount getScheduledInfo() {
		return scheduledInfo;
	}

	public void setScheduledInfo(CommunicationScheduledCount scheduledInfo) {
		this.scheduledInfo = scheduledInfo;
	}

	/**
	 * @return the reviewCount
	 */
	public Integer getReviewCount() {
		return reviewCount;
	}

	/**
	 * @param reviewCount the reviewCount to set
	 */
	public void setReviewCount(Integer reviewCount) {
		this.reviewCount = reviewCount;
	}

	/**
	 * @return the avgRating
	 */
	public Double getAvgRating() {
		return avgRating;
	}

	/**
	 * @param avgRating the avgRating to set
	 */
	public void setAvgRating(Double avgRating) {
		this.avgRating = avgRating;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("CXUsageReportResponse [sentInfo=");
		builder.append(sentInfo);
		builder.append(", deliveredInfo=");
		builder.append(deliveredInfo);
		builder.append(", openInfo=");
		builder.append(openInfo);
		builder.append(", clickInfo=");
		builder.append(clickInfo);
		builder.append(", sentimentClickInfo=");
		builder.append(sentimentClickInfo);
		builder.append(", failureMessage=");
		builder.append(failureMessage);
		builder.append(", scheduledInfo=");
		builder.append(scheduledInfo);
		builder.append("]");
		return builder.toString();
	}
	
}