package com.birdeye.campaign.dto;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class ReviewRequestRetryDTO implements Serializable {
	
	private static final long		serialVersionUID	= 4954895387521166669L;
	
	private Long					reviewRequestId;
	
	private String					requestType;
	
	private RetryRequestMetaInfo	retryRequestMetaInfo;
	
	public ReviewRequestRetryDTO() {
		super();
	}
	
	public ReviewRequestRetryDTO(Long reviewRequestId, String requestType) {
		super();
		this.reviewRequestId = reviewRequestId;
		this.requestType = requestType;
	}
	
	public ReviewRequestRetryDTO(Long reviewRequestId, String requestType, RetryRequestMetaInfo retryRequestMetaInfo) {
		super();
		this.reviewRequestId = reviewRequestId;
		this.requestType = requestType;
		this.retryRequestMetaInfo = retryRequestMetaInfo;
	}
	
	/**
	 * @return the reviewRequestId
	 */
	public Long getReviewRequestId() {
		return reviewRequestId;
	}
	
	/**
	 * @param reviewRequestId
	 *            the reviewRequestId to set
	 */
	public void setReviewRequestId(Long reviewRequestId) {
		this.reviewRequestId = reviewRequestId;
	}
	
	/**
	 * @return the requestType
	 */
	public String getRequestType() {
		return requestType;
	}
	
	/**
	 * @param requestType
	 *            the requestType to set
	 */
	public void setRequestType(String requestType) {
		this.requestType = requestType;
	}

	/**
	 * @return the retryRequestMetaInfo
	 */
	public RetryRequestMetaInfo getRetryRequestMetaInfo() {
		return retryRequestMetaInfo;
	}

	/**
	 * @param retryRequestMetaInfo the retryRequestMetaInfo to set
	 */
	public void setRetryRequestMetaInfo(RetryRequestMetaInfo retryRequestMetaInfo) {
		this.retryRequestMetaInfo = retryRequestMetaInfo;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
