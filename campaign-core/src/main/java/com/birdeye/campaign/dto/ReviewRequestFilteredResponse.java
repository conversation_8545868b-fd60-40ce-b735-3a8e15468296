package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class ReviewRequestFilteredResponse implements Serializable {
	
	private static final long		serialVersionUID	= -7400288279104430558L;
	
	private List<ReviewRequestDto>	data;
	
	private Integer					pageSize;
	
	private Integer					page;
	
	private Integer					recordsCount		= 0;
	
	public ReviewRequestFilteredResponse() {
	}
	
	public ReviewRequestFilteredResponse(Integer pageSize, Integer page, Integer recordsCount) {
		super();
		this.pageSize = pageSize;
		this.page = page;
		this.recordsCount = recordsCount;
	}

	/**
	 * @return the data
	 */
	public List<ReviewRequestDto> getData() {
		return data;
	}

	/**
	 * @param data the data to set
	 */
	public void setData(List<ReviewRequestDto> data) {
		this.data = data;
	}

	/**
	 * @return the pageSize
	 */
	public Integer getPageSize() {
		return pageSize;
	}
	
	/**
	 * @param pageSize
	 *            the pageSize to set
	 */
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
	
	/**
	 * @return the page
	 */
	public Integer getPage() {
		return page;
	}
	
	/**
	 * @param page
	 *            the page to set
	 */
	public void setPage(Integer page) {
		this.page = page;
	}
	
	/**
	 * @return the recordsCount
	 */
	public Integer getRecordsCount() {
		return recordsCount;
	}
	
	/**
	 * @param recordsCount
	 *            the recordsCount to set
	 */
	public void setRecordsCount(Integer recordsCount) {
		this.recordsCount = recordsCount;
	}
	
}
