package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.enums.CampaignRunTypeEnum;
import com.birdeye.campaign.platform.constant.CampaignStatusEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * Communication message for Campaign object
 * <AUTHOR>
 *
 */
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BusinessCampaignMessage implements Serializable{

	private static final long serialVersionUID = -7622100915084303308L;
	
	private Integer id;
	private String name;
	private String type;
	private String scheduledDt;
	private String status;
	private Integer statusId;
	private String runType;
	private Integer surveyId;
	private Integer createdById;
	private String priority;
	private String scheduleType;
	private Integer isFreeTextCampaign;
	private Integer isMessengerCampaign;
	private List<String> userPermissions		= Collections.emptyList();
	private Integer isAppointmentTabCampaign;
	
	private CommunicationUsageStatsMessage usageStats;
	
	public BusinessCampaignMessage() {
		
	}

	public BusinessCampaignMessage(Campaign campaign, boolean isFreeTextCampaign) {
		this.id = campaign.getId();
		this.name = campaign.getName();
		this.type = campaign.getCampaignType();
		this.surveyId = campaign.getSurveyId();
		this.status = CampaignStatusEnum.getStatusAlias(campaign.getStatus());
		this.statusId = campaign.getStatus();
		if (CampaignRunTypeEnum.getEnum(campaign.getRunType()) != null) {
			this.runType = CampaignRunTypeEnum.getEnum(campaign.getRunType()).getLabel();
		} else {
			this.runType = CampaignRunTypeEnum.MANUAL.getLabel(); // Default for old campaigns
		}
		this.createdById = campaign.getCreatedBy();
		this.priority = campaign.getPriority();
		this.scheduleType = campaign.getSchedulingType();
		usageStats = new CommunicationUsageStatsMessage();
		this.isFreeTextCampaign = isFreeTextCampaign ? 1 : 0;
		this.isMessengerCampaign = campaign.getIsMessengerCampaign() != null ? campaign.getIsMessengerCampaign() : 0;
		this.isAppointmentTabCampaign = campaign.getIsAppointmentTabCampaign() != null ? campaign.getIsAppointmentTabCampaign() : 0;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getScheduledDt() {
		return scheduledDt;
	}

	public void setScheduledDt(String scheduledDt) {
		this.scheduledDt = scheduledDt;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Integer getStatusId() {
		return statusId;
	}

	public void setStatusId(Integer statusId) {
		this.statusId = statusId;
	}

	public String getRunType() {
		return runType;
	}

	public void setRunType(String runType) {
		this.runType = runType;
	}

	public CommunicationUsageStatsMessage getUsageStats() {
		return usageStats;
	}

	public void setUsageStats(CommunicationUsageStatsMessage usageStats) {
		if(usageStats != null) {
			this.usageStats = usageStats;
		}
	}

	public Integer getSurveyId() {
		return surveyId;
	}

	public void setSurveyId(Integer surveyId) {
		this.surveyId = surveyId;
	}

	public Integer getCreatedById() {
		return createdById;
	}

	public void setCreatedById(Integer createdById) {
		this.createdById = createdById;
	}

	public String getPriority() {
		return priority;
	}

	public void setPriority(String priority) {
		this.priority = priority;
	}

	public String getScheduleType() {
		return scheduleType;
	}

	public void setScheduleType(String scheduleType) {
		this.scheduleType = scheduleType;
	}

	public Integer getIsFreeTextCampaign() {
		return isFreeTextCampaign;
	}

	public void setIsFreeTextCampaign(Integer isFreeText) {
		this.isFreeTextCampaign = isFreeText;
	}

	public Integer getIsMessengerCampaign() {
		return isMessengerCampaign;
	}

	public void setIsMessengerCampaign(Integer isMessengerCampaign) {
		this.isMessengerCampaign = isMessengerCampaign;
	}

	/**
	 * @return the userPermissions
	 */
	public List<String> getUserPermissions() {
		return userPermissions;
	}
	
	/**
	 * @param userPermissions
	 *            the userPermissions to set
	 */
	public void setUserPermissions(List<String> userPermissions) {
		this.userPermissions = userPermissions;
	}
	
	/**
	 * @return the isAppointmentTabCampaign
	 */
	public Integer getIsAppointmentTabCampaign() {
		return isAppointmentTabCampaign;
	}
	
	/**
	 * @param isAppointmentTabCampaign
	 *            the isAppointmentTabCampaign to set
	 */
	public void setIsAppointmentTabCampaign(Integer isAppointmentTabCampaign) {
		this.isAppointmentTabCampaign = isAppointmentTabCampaign;
	}

}
