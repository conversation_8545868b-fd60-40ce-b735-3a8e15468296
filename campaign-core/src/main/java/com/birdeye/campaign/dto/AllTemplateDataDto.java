package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class AllTemplateDataDto implements Serializable {
	
	/**
	 * 
	 */
	private static final long			serialVersionUID	= 6424558931595876725L;
	
	private BusinessEmailTemplateDTO	businessEmailTemplate;
	
	private EmailTemplateDTO			emailTemplate;
	
	private ReferralTemplateDataDto		referralTemplateData;
	
	private BusinessSmsTemplateDto		businessSmsTemplate;
	
	private AdditionalDataParams		additionalDataParams;
	
	public AllTemplateDataDto() {
		
	}
	
	public static class AdditionalDataParams implements Serializable {
		
		/**
		 * 
		 */
		private static final long		serialVersionUID	= -4006113141907173736L;
		
		private List<TemplateSourceDto>	selectedSources;
		
		private List<TemplateSourceDto>	selectedAndroidSources;
		
		private List<TemplateSourceDto>	selectedReferralSources;
		
		private List<Integer>			selectedLocations;
		
		private Map<String, String>		customCampaignUrlMap;
		
		/**
		 * @return the selectedSources
		 */
		public List<TemplateSourceDto> getSelectedSources() {
			return selectedSources;
		}
		
		/**
		 * @param selectedSources
		 *            the selectedSources to set
		 */
		public void setSelectedSources(List<TemplateSourceDto> selectedSources) {
			this.selectedSources = selectedSources;
		}
		
		/**
		 * @return the selectedAndroidSources
		 */
		public List<TemplateSourceDto> getSelectedAndroidSources() {
			return selectedAndroidSources;
		}
		
		/**
		 * @param selectedAndroidSources
		 *            the selectedAndroidSources to set
		 */
		public void setSelectedAndroidSources(List<TemplateSourceDto> selectedAndroidSources) {
			this.selectedAndroidSources = selectedAndroidSources;
		}
		
		/**
		 * @return the selectedReferralSources
		 */
		public List<TemplateSourceDto> getSelectedReferralSources() {
			return selectedReferralSources;
		}
		
		/**
		 * @param selectedReferralSources
		 *            the selectedReferralSources to set
		 */
		public void setSelectedReferralSources(List<TemplateSourceDto> selectedReferralSources) {
			this.selectedReferralSources = selectedReferralSources;
		}
		
		/**
		 * @return the selectedLocations
		 */
		public List<Integer> getSelectedLocations() {
			return selectedLocations;
		}
		
		/**
		 * @param selectedLocations
		 *            the selectedLocations to set
		 */
		public void setSelectedLocations(List<Integer> selectedLocations) {
			this.selectedLocations = selectedLocations;
		}
		
		/**
		 * @return the customCampaignUrlMap
		 */
		public Map<String, String> getCustomCampaignUrlMap() {
			return customCampaignUrlMap;
		}
		
		/**
		 * @param customCampaignUrlMap
		 *            the customCampaignUrlMap to set
		 */
		public void setCustomCampaignUrlMap(Map<String, String> customCampaignUrlMap) {
			this.customCampaignUrlMap = customCampaignUrlMap;
		}
		
	}
	
	/**
	 * @return the businessEmailTemplate
	 */
	public BusinessEmailTemplateDTO getBusinessEmailTemplate() {
		return businessEmailTemplate;
	}
	
	/**
	 * @param businessEmailTemplate
	 *            the businessEmailTemplate to set
	 */
	public void setBusinessEmailTemplate(BusinessEmailTemplateDTO businessEmailTemplate) {
		this.businessEmailTemplate = businessEmailTemplate;
	}
	
	/**
	 * @return the emailTemplate
	 */
	public EmailTemplateDTO getEmailTemplate() {
		return emailTemplate;
	}
	
	/**
	 * @param emailTemplate
	 *            the emailTemplate to set
	 */
	public void setEmailTemplate(EmailTemplateDTO emailTemplate) {
		this.emailTemplate = emailTemplate;
	}
	
	/**
	 * @return the referralTemplateData
	 */
	public ReferralTemplateDataDto getReferralTemplateData() {
		return referralTemplateData;
	}
	
	/**
	 * @param referralTemplateData
	 *            the referralTemplateData to set
	 */
	public void setReferralTemplateData(ReferralTemplateDataDto referralTemplateData) {
		this.referralTemplateData = referralTemplateData;
	}
	
	/**
	 * @return the businessSmsTemplate
	 */
	public BusinessSmsTemplateDto getBusinessSmsTemplate() {
		return businessSmsTemplate;
	}
	
	/**
	 * @param businessSmsTemplate
	 *            the businessSmsTemplate to set
	 */
	public void setBusinessSmsTemplate(BusinessSmsTemplateDto businessSmsTemplate) {
		this.businessSmsTemplate = businessSmsTemplate;
	}

	/**
	 * @return the additionalDataParams
	 */
	public AdditionalDataParams getAdditionalDataParams() {
		return additionalDataParams;
	}

	/**
	 * @param additionalDataParams the additionalDataParams to set
	 */
	public void setAdditionalDataParams(AdditionalDataParams additionalDataParams) {
		this.additionalDataParams = additionalDataParams;
	}
	
}
