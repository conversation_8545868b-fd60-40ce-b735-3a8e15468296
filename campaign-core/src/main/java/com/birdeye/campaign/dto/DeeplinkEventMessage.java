package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DeeplinkEventMessage implements Serializable {
	
	private static final long	serialVersionUID	= 2382096927694803821L;
	
	private String				eventId;
	private String				sentimentType;
	private Integer				recommended;
	private Integer				rating;
	private String				time				= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
	private String				os;
	private String				deviceType;
	private String				sourceName;
	private Integer				sourceId;
	private Integer				reviewId;
	private Float				revRating;
	
	public DeeplinkEventMessage() {
		// TODO Auto-generated constructor stub
	}
	
	// for open event
	public DeeplinkEventMessage(String eventId, String os, String deviceType) {
		this.eventId = eventId;
		this.os = os;
		this.deviceType = deviceType;
	}
	
	// for sentiment click event
	public DeeplinkEventMessage(String eventId, String os, String deviceType, String sentimentType, Integer recommended, Integer rating) {
		this.eventId = eventId;
		this.sentimentType = sentimentType;
		this.recommended = recommended;
		this.rating = rating;
		this.os = os;
		this.deviceType = deviceType;
	}
	
	// for review source event
	public DeeplinkEventMessage(String eventId, String os, String deviceType, Integer sourceId, String sourceName) {
		this.eventId = eventId;
		this.os = os;
		this.deviceType = deviceType;
		this.sourceName = sourceName;
		this.sourceId = sourceId;
	}
	
	public String getEventId() {
		return eventId;
	}
	
	public void setEventId(String eventId) {
		this.eventId = eventId;
	}
	
	public String getSentimentType() {
		return sentimentType;
	}
	
	public void setSentimentType(String sentimentType) {
		this.sentimentType = sentimentType;
	}
	
	public Integer getRecommended() {
		return recommended;
	}
	
	public void setRecommended(Integer recommended) {
		this.recommended = recommended;
	}
	
	public Integer getRating() {
		return rating;
	}
	
	public void setRating(Integer rating) {
		this.rating = rating;
	}
	
	public String getTime() {
		return time;
	}
	
	public void setTime(String time) {
		this.time = time;
	}
	
	public String getOs() {
		return os;
	}
	
	public void setOs(String os) {
		this.os = os;
	}
	
	public String getDeviceType() {
		return deviceType;
	}
	
	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}
	
	public String getSourceName() {
		return sourceName;
	}
	
	public void setSourceName(String sourceName) {
		this.sourceName = sourceName;
	}
	
	public Integer getSourceId() {
		return sourceId;
	}
	
	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}
	
	public Integer getReviewId() {
		return reviewId;
	}
	
	public void setReviewId(Integer reviewId) {
		this.reviewId = reviewId;
	}
	
	public Float getRevRating() {
		return revRating;
	}
	
	public void setRevRating(Float revRating) {
		this.revRating = revRating;
	}
	
}
