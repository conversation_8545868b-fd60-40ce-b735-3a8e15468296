package com.birdeye.campaign.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ClickDetail {

	private Integer sourceId;
	private String sourceName;
	private String time;
	public Integer getSourceId() {
		return sourceId;
	}
	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}
	public String getSourceName() {
		return sourceName;
	}
	public void setSourceName(String sourceName) {
		this.sourceName = sourceName;
	}
	public String getTime() {
		return time;
	}
	public void setTime(String time) {
		this.time = time;
	}
	
	public ClickDetail(Integer sourceId, String sourceName, String time) {
		super();
		this.sourceId = sourceId;
		this.sourceName = sourceName;
		this.time = time;
	}
	
	

}
