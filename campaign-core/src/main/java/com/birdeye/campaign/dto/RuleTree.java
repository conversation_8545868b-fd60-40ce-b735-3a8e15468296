package com.birdeye.campaign.dto;

public class RuleTree {

	private RuleTree left;
	private String logicalOptr;
	private RuleTree right;
	private RuleCondition condition;

	public RuleTree() {
	}

	public RuleTree(RuleCondition condition) {
		this.condition = condition;
	}

	/**
	 * @return the left
	 */
	public RuleTree getLeft() {
		return left;
	}

	/**
	 * @param left
	 *            the left to set
	 */
	public void setLeft(RuleTree left) {
		this.left = left;
	}

	/**
	 * @return the logicalOptr
	 */
	public String getLogicalOptr() {
		return logicalOptr;
	}

	/**
	 * @param logicalOptr
	 *            the logicalOptr to set
	 */
	public void setLogicalOptr(String logicalOptr) {
		this.logicalOptr = logicalOptr;
	}

	/**
	 * @return the right
	 */
	public RuleTree getRight() {
		return right;
	}

	/**
	 * @param right
	 *            the right to set
	 */
	public void setRight(RuleTree right) {
		this.right = right;
	}

	/**
	 * @return the condition
	 */
	public RuleCondition getCondition() {
		return condition;
	}

	/**
	 * @param condition
	 *            the condition to set
	 */
	public void setCondition(RuleCondition condition) {
		this.condition = condition;
	}
}
