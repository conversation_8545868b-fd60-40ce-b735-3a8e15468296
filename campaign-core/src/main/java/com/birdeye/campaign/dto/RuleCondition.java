package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.Objects;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RuleCondition implements Serializable {
	
	private static final long	serialVersionUID	= 2830832653259393081L;
	private Integer				id;
	private String				operand;
	private String				operator;
	private String				type;
	private Object				value;
	private String				customFieldSource;
	
	/*
	 * "type": "currency", "id": 123, "operator" : "greater than", "operand": "Annual revenue", "value" : ["USD", "200000"]
	 */
	
	public RuleCondition(Integer id, String operand, String operator, String type, Object value) {
		super();
		this.id = id;
		this.operand = operand;
		this.operator = operator;
		this.type = type;
		this.value = value;
	}
	
	public RuleCondition() {
		
	}
	
	/**
	 * @return the operand
	 */
	public String getOperand() {
		return operand;
	}
	
	/**
	 * @param operand
	 *            the operand to set
	 */
	public void setOperand(String operand) {
		this.operand = operand;
	}
	
	/**
	 * @return the operator
	 */
	public String getOperator() {
		return operator;
	}
	
	/**
	 * @param operator
	 *            the operator to set
	 */
	public void setOperator(String operator) {
		this.operator = operator;
	}
	
	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}
	
	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	
	/**
	 * @return the type
	 */
	public String getType() {
		return type;
	}
	
	/**
	 * @param type
	 *            the type to set
	 */
	public void setType(String type) {
		this.type = type;
	}
	
	/**
	 * @return the value
	 */
	public Object getValue() {
		return value;
	}
	
	/**
	 * @param value
	 *            the value to set
	 */
	public void setValue(Object value) {
		this.value = value;
	}
	
	/**
	 * @return customFieldSource
	 */
	public String getCustomFieldSource() {
		return customFieldSource;
	}
	
	/**
	 * @param customFieldSource
	 *            the customFieldSource to set
	 */
	public void setCustomFieldSource(String customFieldSource) {
		this.customFieldSource = customFieldSource;
	}
	
	@Override
	public int hashCode() {
		return Objects.hash(id, operand, operator, type, value, customFieldSource);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj) {
			return true;
		}

		if (obj == null || getClass() != obj.getClass()) {
			return false;
		}

		RuleCondition other = (RuleCondition) obj;
		return Objects.equals(id, other.id) && Objects.equals(operand, other.operand) && Objects.equals(operator, other.operator) && Objects.equals(type, other.type)
				&& Objects.equals(value, other.value)     && Objects.equals(customFieldSource, other.customFieldSource);
    }

	
	/*
	 * (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE).toString();
	}
}
