/**
 * @file_name DGRWrapper.java
 * @created_date 12 Jun 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.dto;

import java.io.Serializable;

/**
 * @file_name DGRWrapper.java
 * @created_date 12 Jun 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public class DGRWrapper implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -1215933912948776839L;
	
	private Integer				isDGR;
	
	/**
	 * 
	 */
	public DGRWrapper() {
		super();
	}

	/**
	 * @param isDGR
	 */
	public DGRWrapper(Integer isDGR) {
		super();
		this.isDGR = isDGR;
	}

	/**
	 * @return the isDGR
	 */
	public Integer getIsDGR() {
		return isDGR;
	}
	
	/**
	 * @param isDGR
	 *            the isDGR to set
	 */
	public void setIsDGR(Integer isDGR) {
		this.isDGR = isDGR;
	}
	
	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("DGRWrapper [isDGR=");
		builder.append(isDGR);
		builder.append("]");
		return builder.toString();
	}
	
}
