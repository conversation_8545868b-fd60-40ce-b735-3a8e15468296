package com.birdeye.campaign.dto;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
/***
 * 
 * <AUTHOR>
 * BIRD-100854
 * In phase I
 * used to fetch business product name and reseller website and logo
 * In phase II we will fetch business branding info too
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_NULL)
public class BusinessBrandingWithResellerInfo implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -1715817192989928403L;
	private String	resellerWebsiteUrl;
	private String	resellerLogoUrl;
	private String	productName;
	private String	businessName;
	private Integer	businessId;
	private String	activationStatus;
	
	public String getResellerWebsiteUrl() {
		return resellerWebsiteUrl;
	}
	
	public void setResellerWebsiteUrl(String resellerWebsiteUrl) {
		this.resellerWebsiteUrl = resellerWebsiteUrl;
	}
	
	public String getResellerLogoUrl() {
		return resellerLogoUrl;
	}
	
	public void setResellerLogoUrl(String resellerLogoUrl) {
		this.resellerLogoUrl = resellerLogoUrl;
	}
	
	public String getProductName() {
		return productName;
	}
	
	public void setProductName(String productName) {
		this.productName = productName;
	}
	
	public String getBusinessName() {
		return businessName;
	}
	
	public void setBusinessName(String businessName) {
		this.businessName = businessName;
	}
	
	public Integer getBusinessId() {
		return businessId;
	}
	
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	public String getActivationStatus() {
		return activationStatus;
	}
	
	public void setActivationStatus(String activationStatus) {
		this.activationStatus = activationStatus;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
