package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CampaignFailureMessage implements Serializable {
	
	private static final long					serialVersionUID	= 6861656077269127257L;
	
	private long								total;
	private List<CampaignFailureReasonMessage>	failureReasons		= new ArrayList<>();
	
	public long getTotal() {
		return total;
	}
	
	public void setTotal(long total) {
		this.total = total;
	}
	
	public List<CampaignFailureReasonMessage> getFailureReasons() {
		return failureReasons;
	}
	
	public void setFailureReasons(List<CampaignFailureReasonMessage> failureReasons) {
		this.failureReasons = failureReasons;
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class CampaignFailureReasonMessage {
		private Long	count	= 0l;
		
		private Integer	failureId;
		
		private String	failureReason;
		
		public Long getCount() {
			return count;
		}
		
		public void setCount(Long count) {
			this.count = count;
		}
		
		public Integer getFailureId() {
			return failureId;
		}
		
		public void setFailureId(Integer failureId) {
			this.failureId = failureId;
		}
		
		public String getFailureReason() {
			return failureReason;
		}
		
		public void setFailureReason(String failureReason) {
			this.failureReason = failureReason;
		}

		/**
		 * @param failureId
		 */
		public CampaignFailureReasonMessage(Integer failureId) {
			this.failureId = failureId;
		}

		/**
		 * No arg constructor
		 */
		public CampaignFailureReasonMessage() {
			
		}
		
		
	}
	
}
