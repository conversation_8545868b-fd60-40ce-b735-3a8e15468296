package com.birdeye.campaign.dto;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

import java.io.Serializable;
import java.util.Date;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * @file_name SmsDto.java
 * @created_date 24 Dec 2022
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

public class SmsDto implements Serializable {
	
	private static final long	serialVersionUID	= 6669402699800100749L;
	
	private Integer				smsId;
	
	private int					businessId;
	
	private int					customerId			= 0;
	
	private int					toUserId			= 0;
	
	private String				fromNumber;
	
	private String				toNumber;
	
	private String				messageBody;
	
	private Date				createDate;
	
	private String				mediaURL;
	
	private Date				sentOn;
	
	private Long				reviewRequestId;
	
	private Integer				encrypted			= 0;
	
	private String				messageBodyUnencrypted;
	
	public Integer getSmsId() {
		return smsId;
	}

	public void setSmsId(Integer smsId) {
		this.smsId = smsId;
	}

	public int getBusinessId() {
		return businessId;
	}
	
	public void setBusinessId(int businessId) {
		this.businessId = businessId;
	}
	
	public int getCustomerId() {
		return customerId;
	}
	
	public void setCustomerId(int customerId) {
		this.customerId = customerId;
	}
	
	public int getToUserId() {
		return toUserId;
	}
	
	public void setToUserId(int toUserId) {
		this.toUserId = toUserId;
	}
	
	public String getFromNumber() {
		return fromNumber;
	}
	
	public void setFromNumber(String fromNumber) {
		this.fromNumber = fromNumber;
	}
	
	public String getToNumber() {
		return toNumber;
	}
	
	public void setToNumber(String toNumber) {
		this.toNumber = toNumber;
	}
	
	public String getMessageBody() {
		return messageBody;
	}
	
	public void setMessageBody(String messageBody) {
		this.messageBody = messageBody;
	}
	
	public Date getCreateDate() {
		return createDate;
	}
	
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	
	public String getMediaURL() {
		return mediaURL;
	}
	
	public void setMediaURL(String mediaURL) {
		this.mediaURL = mediaURL;
	}
	
	public Date getSentOn() {
		return sentOn;
	}
	
	public void setSentOn(Date sentOn) {
		this.sentOn = sentOn;
	}
	
	public Long getReviewRequestId() {
		return reviewRequestId;
	}
	
	public void setReviewRequestId(Long reviewRequestId) {
		this.reviewRequestId = reviewRequestId;
	}
	
	public Integer getEncrypted() {
		return encrypted;
	}
	
	public void setEncrypted(Integer encrypted) {
		this.encrypted = encrypted;
	}
	
	public String getMessageBodyUnencrypted() {
		return messageBodyUnencrypted;
	}
	
	public void setMessageBodyUnencrypted(String messageBodyUnencrypted) {
		this.messageBodyUnencrypted = messageBodyUnencrypted;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}