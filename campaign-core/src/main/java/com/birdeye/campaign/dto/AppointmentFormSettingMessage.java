package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.entity.ReferralFormSetting;
import com.birdeye.campaign.entity.ReferralSetting;
import com.birdeye.campaign.response.ReferralAppointmentTemplateDetails;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * 
 * <AUTHOR>
 *
 */
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppointmentFormSettingMessage implements Serializable {
	
	/**
	 * 
	 */
	private static final long					serialVersionUID	= 1L;
	private String								referralMessage;
	private AppointmentPage						appointmentPage;
	private AppointmentConfirmation				appointmentConfirmation;
	private List<ReferralLocation>				locations;
	private Integer								defaultBusinessId;
	private Long								businessNumber;
	private String								businessAddress;			//location Address
	private String								refererName;
	private Set<String>							supportedCountries;
	private Integer								isSMB				= 0;
	private String								businessTypeTitle;
	private String								businessName;				//accountName aka Enterprise name
	private String								locationName;				//locationName
	private String								locationCountryCode;		// country code for business location
	private String								ogImage;
	private Integer								accountType;
	private boolean								showZipCode;				// only if the account is titlemax account - temporary solution given the time crunch - bad design - look for alternative options for its configuration in templates in future
	private String								requestSource;
	
	private ReferralAppointmentTemplateDetails	templateData;
	private LeadFormSEOMetadata					meta;
	private Map<String, String>					tokens;
	private LeadFormBrandingDTO					branding;
	private LeadFormReferrerDTO					referrer;
	//0 for false 1 for true
	private Integer serviceAreaProvider;
	//TODO: group business fields later - sync with UI
	
	/**
	 * @return the locationName
	 */
	public String getLocationName() {
		return locationName;
	}
	
	/**
	 * @param locationName
	 *            the locationName to set
	 */
	public void setLocationName(String locationName) {
		this.locationName = locationName;
	}
	
	/**
	 * @return the locationCountryCode
	 */
	public String getLocationCountryCode() {
		return locationCountryCode;
	}
	
	/**
	 * @param locationCountryCode
	 *            the locationCountryCode to set
	 */
	public void setLocationCountryCode(String locationCountryCode) {
		this.locationCountryCode = locationCountryCode;
	}

	/**
	 * @return the templateData
	 */
	public ReferralAppointmentTemplateDetails getTemplateData() {
		return templateData;
	}
	
	/**
	 * @param templateData
	 *            the templateData to set
	 */
	public void setTemplateData(ReferralAppointmentTemplateDetails templateData) {
		this.templateData = templateData;
	}
	
	public AppointmentFormSettingMessage() {
	}
	
	/**
	 * @return the meta
	 */
	public LeadFormSEOMetadata getMeta() {
		return meta;
	}
	
	/**
	 * @param meta
	 *            the meta to set
	 */
	public void setMeta(LeadFormSEOMetadata meta) {
		this.meta = meta;
	}
	
	/**
	 * @return the appointmentPage
	 */
	public AppointmentPage getAppointmentPage() {
		return appointmentPage;
	}
	
	/**
	 * @param appointmentPage
	 *            the appointmentPage to set
	 */
	public void setAppointmentPage(AppointmentPage appointmentPage) {
		this.appointmentPage = appointmentPage;
	}
	
	public String getBusinessName() {
		return businessName;
	}
	
	public void setBusinessName(String businessName) {
		this.businessName = businessName;
	}
	
	public String getOgImage() {
		return ogImage;
	}
	
	public void setOgImage(String ogImage) {
		this.ogImage = ogImage;
	}
	
	/**
	 * @return the businessNumber
	 */
	public Long getBusinessNumber() {
		return businessNumber;
	}
	
	/**
	 * @param businessNumber
	 *            the businessNumber to set
	 */
	public void setBusinessNumber(Long businessNumber) {
		this.businessNumber = businessNumber;
	}
	
	/**
	 * @return the refererName
	 */
	public String getRefererName() {
		return refererName;
	}
	
	/**
	 * @param refererName
	 *            the refererName to set
	 */
	public void setRefererName(String refererName) {
		this.refererName = refererName;
	}
	
	/**
	 * @return the appointmentConfirmation
	 */
	public AppointmentConfirmation getAppointmentConfirmation() {
		return appointmentConfirmation;
	}
	
	/**
	 * @param appointmentConfirmation
	 *            the appointmentConfirmation to set
	 */
	public void setAppointmentConfirmation(AppointmentConfirmation appointmentConfirmation) {
		this.appointmentConfirmation = appointmentConfirmation;
	}
	
	/**
	 * @return the locations
	 */
	public List<ReferralLocation> getLocations() {
		return locations;
	}
	
	/**
	 * @param locations
	 *            the locations to set
	 */
	public void setLocations(List<ReferralLocation> locations) {
		this.locations = locations;
	}
	
	/**
	 * @return the defaultBusinessId
	 */
	public Integer getDefaultBusinessId() {
		return defaultBusinessId;
	}
	
	/**
	 * @param defaultBusinessId
	 *            the defaultBusinessId to set
	 */
	public void setDefaultBusinessId(Integer defaultBusinessId) {
		this.defaultBusinessId = defaultBusinessId;
	}
	
	/**
	 * @return the isSMB
	 */
	public Integer getIsSMB() {
		return isSMB;
	}
	
	/**
	 * @param isSMB
	 *            the isSMB to set
	 */
	public void setIsSMB(Integer isSMB) {
		this.isSMB = isSMB;
	}
	
	/**
	 * @return the referralMessage
	 */
	public String getReferralMessage() {
		return referralMessage;
	}
	
	/**
	 * @param referralMessage
	 *            the referralMessage to set
	 */
	public void setReferralMessage(String referralMessage) {
		this.referralMessage = referralMessage;
	}
	
	/**
	 * @return the businessTypeTitle
	 */
	public String getBusinessTypeTitle() {
		return businessTypeTitle;
	}
	
	/**
	 * @param businessTypeTitle
	 *            the businessTypeTitle to set
	 */
	public void setBusinessTypeTitle(String businessTypeTitle) {
		this.businessTypeTitle = businessTypeTitle;
	}
	
	public Integer getAccountType() {
		return accountType;
	}
	
	public void setAccountType(Integer accountType) {
		this.accountType = accountType;
	}
	
	/**
	 * @return the businessAddress
	 */
	public String getBusinessAddress() {
		return businessAddress;
	}
	
	/**
	 * @param businessAddress
	 *            the businessAddress to set
	 */
	public void setBusinessAddress(String businessAddress) {
		this.businessAddress = businessAddress;
	}
	
	/**
	 * @return the supportedCountries
	 */
	public Set<String> getSupportedCountries() {
		return supportedCountries;
	}
	
	/**
	 * @param supportedCountries
	 *            the supportedCountries to set
	 */
	public void setSupportedCountries(Set<String> supportedCountries) {
		this.supportedCountries = supportedCountries;
	}
	
	/**
	 * @return the tokens
	 */
	public Map<String, String> getTokens() {
		return tokens;
	}
	
	/**
	 * @param tokens
	 *            the tokens to set
	 */
	public void setTokens(Map<String, String> tokens) {
		this.tokens = tokens;
	}
	
	/**
	 * @return the branding
	 */
	public LeadFormBrandingDTO getBranding() {
		return branding;
	}
	
	/**
	 * @param branding
	 *            the branding to set
	 */
	public void setBranding(LeadFormBrandingDTO branding) {
		this.branding = branding;
	}
	
	/**
	 * @return the showZipCode
	 */
	public boolean isShowZipCode() {
		return showZipCode;
	}
	
	
	
	/**
	 * @return the requestSource
	 */
	public String getRequestSource() {
		return requestSource;
	}

	/**
	 * @param requestSource the requestSource to set
	 */
	public void setRequestSource(String requestSource) {
		this.requestSource = requestSource;
	}

	/**
	 * @param showZipCode
	 *            the showZipCode to set
	 */
	public void setShowZipCode(boolean showZipCode) {
		this.showZipCode = showZipCode;
	}
	
	/**
	 * @return the referrer
	 */
	public LeadFormReferrerDTO getReferrer() {
		return referrer;
	}

	/**
	 * @param referrer the referrer to set
	 */
	public void setReferrer(LeadFormReferrerDTO referrer) {
		this.referrer = referrer;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("AppointmentFormSettingMessage [referralMessage=");
		builder.append(referralMessage);
		builder.append(", appointmentPage=");
		builder.append(appointmentPage);
		builder.append(", appointmentConfirmation=");
		builder.append(appointmentConfirmation);
		builder.append(", locations=");
		builder.append(locations);
		builder.append(", defaultBusinessId=");
		builder.append(defaultBusinessId);
		builder.append(", businessNumber=");
		builder.append(businessNumber);
		builder.append(", businessAddress=");
		builder.append(businessAddress);
		builder.append(", refererName=");
		builder.append(refererName);
		builder.append(", supportedCountries=");
		builder.append(supportedCountries);
		builder.append(", isSMB=");
		builder.append(isSMB);
		builder.append(", businessTypeTitle=");
		builder.append(businessTypeTitle);
		builder.append(", businessName=");
		builder.append(businessName);
		builder.append(", locationName=");
		builder.append(locationName);
		builder.append(", ogImage=");
		builder.append(ogImage);
		builder.append(", accountType=");
		builder.append(accountType);
		builder.append(", showZipCode=");
		builder.append(showZipCode);
		builder.append(", requestSource=");
		builder.append(requestSource);
		builder.append(", templateData=");
		builder.append(templateData);
		builder.append(", meta=");
		builder.append(meta);
		builder.append(", tokens=");
		builder.append(tokens);
		builder.append(", branding=");
		builder.append(branding);
		builder.append(", referrer=");
		builder.append(referrer);
		builder.append("]");
		return builder.toString();
	}



	public Integer getServiceAreaProvider() {
		return serviceAreaProvider;
	}

	public void setServiceAreaProvider(Integer serviceAreaProvider) {
		this.serviceAreaProvider = serviceAreaProvider;
	}



	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class AppointmentPage {
		private String	heading;
		private String	message;
		private String	image;
		
		public AppointmentPage() {
		}
		
		public AppointmentPage(ReferralSetting referralSetting, String customerName, String businessName) {
			if (StringUtils.isBlank(customerName)) {
				this.heading = Constants.APPOINTMENT_PAGE_HEADING;
				this.message = new String(Constants.APPOINTMENT_PAGE_HEADING2_CONTACT_NULL).replace("[Business Name]", businessName);
			} else {
				this.heading = referralSetting.getHeading1() != null ? referralSetting.getHeading1().replace("[Contact Name]", customerName) : null;
				this.message = referralSetting.getHeading2() != null ? referralSetting.getHeading2().replace("[Contact Name]", customerName).replace("[Business Name]", businessName) : null;
			}
			this.image = referralSetting.getImage();
		}
		
		/**
		 * @return the heading
		 */
		public String getHeading() {
			return heading;
		}
		
		/**
		 * @param heading
		 *            the heading to set
		 */
		public void setHeading(String heading) {
			this.heading = heading;
		}
		
		/**
		 * @return the message
		 */
		public String getMessage() {
			return message;
		}
		
		/**
		 * @param message
		 *            the message to set
		 */
		public void setMessage(String message) {
			this.message = message;
		}
		
		/**
		 * @return the image
		 */
		public String getImage() {
			return image;
		}
		
		/**
		 * @param image
		 *            the image to set
		 */
		public void setImage(String image) {
			this.image = image;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class AppointmentForm {
		private String						heading;
		private String						buttonText;
		private String						buttonColor;
		private String						buttonTextColor;
		private List<AppointmentFormField>	fields;
		
		public AppointmentForm() {
		}
		
		public AppointmentForm(ReferralSetting referralSetting, Integer isSMB, List<ReferralFormSetting> referralFormSettings) {
			this.heading = referralSetting.getFormHeading();
			this.buttonText = referralSetting.getFormBottonText();
			this.buttonColor = referralSetting.getFormButtonColor();
			this.buttonTextColor = referralSetting.getFormButtonTextColor();
			if (CollectionUtils.isNotEmpty(referralFormSettings)) {
				this.fields = new ArrayList<>();
				referralFormSettings.stream().filter(form -> !(isSMB == 1 && "location".equalsIgnoreCase(form.getName()))).forEach(form -> fields.add(new AppointmentFormField(form)));
			}
		}
		
		/**
		 * @return the heading
		 */
		public String getHeading() {
			return heading;
		}
		
		/**
		 * @param heading
		 *            the heading to set
		 */
		public void setHeading(String heading) {
			this.heading = heading;
		}
		
		/**
		 * @return the buttonText
		 */
		public String getButtonText() {
			return buttonText;
		}
		
		/**
		 * @param buttonText
		 *            the buttonText to set
		 */
		public void setButtonText(String buttonText) {
			this.buttonText = buttonText;
		}
		
		/**
		 * @return the buttonColor
		 */
		public String getButtonColor() {
			return buttonColor;
		}
		
		/**
		 * @param buttonColor
		 *            the buttonColor to set
		 */
		public void setButtonColor(String buttonColor) {
			this.buttonColor = buttonColor;
		}
		
		/**
		 * @return the buttonTextColor
		 */
		public String getButtonTextColor() {
			return buttonTextColor;
		}
		
		/**
		 * @param buttonTextColor
		 *            the buttonTextColor to set
		 */
		public void setButtonTextColor(String buttonTextColor) {
			this.buttonTextColor = buttonTextColor;
		}
		
		/**
		 * @return the fields
		 */
		public List<AppointmentFormField> getFields() {
			return fields;
		}
		
		/**
		 * @param fields
		 *            the fields to set
		 */
		public void setFields(List<AppointmentFormField> fields) {
			this.fields = fields;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class AppointmentFormField {
		private String	name;
		private String	label;
		private Integer	show;
		private Integer	required;
		private Integer	rank;
		
		public AppointmentFormField() {
		}
		
		public AppointmentFormField(ReferralFormSetting referralFormSetting) {
			this.name = referralFormSetting.getName();
			this.label = referralFormSetting.getLabel();
			this.show = referralFormSetting.getShow();
			this.required = referralFormSetting.getRequired();
			this.rank = referralFormSetting.getRank();
		}
		
		/**
		 * @return the name
		 */
		public String getName() {
			return name;
		}
		
		/**
		 * @param name
		 *            the name to set
		 */
		public void setName(String name) {
			this.name = name;
		}
		
		/**
		 * @return the label
		 */
		public String getLabel() {
			return label;
		}
		
		/**
		 * @param label
		 *            the label to set
		 */
		public void setLabel(String label) {
			this.label = label;
		}
		
		/**
		 * @return the show
		 */
		public Integer getShow() {
			return show;
		}
		
		/**
		 * @param show
		 *            the show to set
		 */
		public void setShow(Integer show) {
			this.show = show;
		}
		
		/**
		 * @return the required
		 */
		public Integer getRequired() {
			return required;
		}
		
		/**
		 * @param required
		 *            the required to set
		 */
		public void setRequired(Integer required) {
			this.required = required;
		}
		
		/**
		 * @return the rank
		 */
		public Integer getRank() {
			return rank;
		}
		
		/**
		 * @param rank
		 *            the rank to set
		 */
		public void setRank(Integer rank) {
			this.rank = rank;
		}
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class AppointmentConfirmation {
		private String	heading;
		private String	message;
		
		public AppointmentConfirmation() {
		}
		
		public AppointmentConfirmation(ReferralSetting referralSetting) {
			this.heading = referralSetting.getConfirmationHeading();
			this.message = referralSetting.getConfirmationMessage();
		}
		
		/**
		 * @return the heading
		 */
		public String getHeading() {
			return heading;
		}
		
		/**
		 * @param heading
		 *            the heading to set
		 */
		public void setHeading(String heading) {
			this.heading = heading;
		}
		
		/**
		 * @return the message
		 */
		public String getMessage() {
			return message;
		}
		
		/**
		 * @param message
		 *            the message to set
		 */
		public void setMessage(String message) {
			this.message = message;
		}
		
	}
	
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class LeadFormReferrerDTO {
		private String	customerId;
		private Integer encrypted;
		/**
		 * @return the customerId
		 */
		public String getCustomerId() {
			return customerId;
		}
		/**
		 * @param customerId the customerId to set
		 */
		public void setCustomerId(String customerId) {
			this.customerId = customerId;
		}
		/**
		 * @return the encrypted
		 */
		public Integer getEncrypted() {
			return encrypted;
		}
		/**
		 * @param encrypted the encrypted to set
		 */
		public void setEncrypted(Integer encrypted) {
			this.encrypted = encrypted;
		}
		/**
		 * @param customerId
		 * @param encrypted
		 */
		public LeadFormReferrerDTO(String customerId, Integer encrypted) {
			super();
			this.customerId = customerId;
			this.encrypted = encrypted;
		}
		/**
		 * 
		 */
		public LeadFormReferrerDTO() {
			super();
		}
		

		
	}
	
}
