package com.birdeye.campaign.dto;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class ResellerReviewGenUpdateDTO implements Serializable {
	
	private static final long	serialVersionUID	= 9121018521059299104L;
	
	private Integer				resellerId;
	private Integer				accountCreationAttempt;
	private String				domainName;
	private Long				businessNumber;
	
	public ResellerReviewGenUpdateDTO() {
		
	}
	
	/**
	 * @param resellerId
	 * @param accountCreationAttempt
	 * @param domainName
	 * @param businessNumber
	 */
	public ResellerReviewGenUpdateDTO(Integer resellerId, Integer accountCreationAttempt, String domainName, Long businessNumber) {
		this.resellerId = resellerId;
		this.accountCreationAttempt = accountCreationAttempt;
		this.domainName = domainName;
		this.businessNumber = businessNumber;
	}
	
	/**
	 * @return the resellerId
	 */
	public Integer getResellerId() {
		return resellerId;
	}
	
	/**
	 * @param resellerId
	 *            the resellerId to set
	 */
	public void setResellerId(Integer resellerId) {
		this.resellerId = resellerId;
	}
	
	/**
	 * @return the accountCreationAttempt
	 */
	public Integer getAccountCreationAttempt() {
		return accountCreationAttempt;
	}
	
	/**
	 * @param accountCreationAttempt
	 *            the accountCreationAttempt to set
	 */
	public void setAccountCreationAttempt(Integer accountCreationAttempt) {
		this.accountCreationAttempt = accountCreationAttempt;
	}
	
	/**
	 * @return the domainName
	 */
	public String getDomainName() {
		return domainName;
	}
	
	/**
	 * @param domainName
	 *            the domainName to set
	 */
	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}
	
	/**
	 * @return the businessNumber
	 */
	public Long getBusinessNumber() {
		return businessNumber;
	}
	
	/**
	 * @param businessNumber
	 *            the businessNumber to set
	 */
	public void setBusinessNumber(Long businessNumber) {
		this.businessNumber = businessNumber;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
