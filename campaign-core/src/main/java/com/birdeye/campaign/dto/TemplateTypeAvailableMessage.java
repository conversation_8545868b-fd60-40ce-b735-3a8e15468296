package com.birdeye.campaign.dto;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Message to communicate campaign enabled flags for campaign types
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class TemplateTypeAvailableMessage implements Serializable{

	private static final long serialVersionUID = -5568442987966630188L;

	private boolean cxAvailable;
	private boolean rrAvailable ;
	private boolean surveyAvailable ;
	private boolean promotionAvailable ;
	private Integer surveyCount;
	private boolean referralAvailable;
	private Integer campaignCustomerLimit;
	private boolean appointmentReminderAvailable;
	private boolean appointmentRecallAvailable;
	private boolean appointmentFormAvailable;
	private boolean reminderCampaignsRunningStatus;
	
	private Long cxEmailCount = 0l;
	private Long rrEmailCount = 0l;
	private Long surveyEmailCount = 0l;
	private Long promotionEmailCount = 0l;
	private Long referralEmailCount = 0l;
	private Long appointmentReminderEmailCount = 0l;
	private Long appointmentRecallEmailCount = 0l;
	private Long appointmentFormEmailCount = 0l;
	
	/**
	 * @return the cxAvailable
	 */
	public boolean isCxAvailable() {
		return cxAvailable;
	}
	/**
	 * @param cxAvailable the cxAvailable to set
	 */
	public void setCxAvailable(boolean cxAvailable) {
		this.cxAvailable = cxAvailable;
	}
	/**
	 * @return the rrAvailable
	 */
	public boolean isRrAvailable() {
		return rrAvailable;
	}
	/**
	 * @param rrAvailable the rrAvailable to set
	 */
	public void setRrAvailable(boolean rrAvailable) {
		this.rrAvailable = rrAvailable;
	}
	/**
	 * @return the surveyAvailable
	 */
	public boolean isSurveyAvailable() {
		return surveyAvailable;
	}
	/**
	 * @param surveyAvailable the surveyAvailable to set
	 */
	public void setSurveyAvailable(boolean surveyAvailable) {
		this.surveyAvailable = surveyAvailable;
	}
	/**
	 * @return the promotionAvailable
	 */
	public boolean isPromotionAvailable() {
		return promotionAvailable;
	}
	/**
	 * @param promotionAvailable the promotionAvailable to set
	 */
	public void setPromotionAvailable(boolean promotionAvailable) {
		this.promotionAvailable = promotionAvailable;
	}
	/**
	 * @return the surveyCount
	 */
	public Integer getSurveyCount() {
		return surveyCount;
	}
	/**
	 * @param surveyCount the surveyCount to set
	 */
	public void setSurveyCount(Integer surveyCount) {
		this.surveyCount = surveyCount;
	}
	/**
	 * @return the referralAvailable
	 */
	public boolean isReferralAvailable() {
		return referralAvailable;
	}
	/**
	 * @param referralAvailable the referralAvailable to set
	 */
	public void setReferralAvailable(boolean referralAvailable) {
		this.referralAvailable = referralAvailable;
	}
	/**
	 * @return the campaignCustomerLimit
	 */
	public Integer getCampaignCustomerLimit() {
		return campaignCustomerLimit;
	}
	/**
	 * @param campaignCustomerLimit the campaignCustomerLimit to set
	 */
	public void setCampaignCustomerLimit(Integer campaignCustomerLimit) {
		this.campaignCustomerLimit = campaignCustomerLimit;
	}
	/**
	 * @return the appointmentReminderAvailable
	 */
	public boolean isAppointmentReminderAvailable() {
		return appointmentReminderAvailable;
	}
	/**
	 * @param appointmentReminderAvailable the appointmentReminderAvailable to set
	 */
	public void setAppointmentReminderAvailable(boolean appointmentReminderAvailable) {
		this.appointmentReminderAvailable = appointmentReminderAvailable;
	}
	/**
	 * @return the appointmentRecallAvailable
	 */
	public boolean isAppointmentRecallAvailable() {
		return appointmentRecallAvailable;
	}
	/**
	 * @param appointmentRecallAvailable the appointmentRecallAvailable to set
	 */
	public void setAppointmentRecallAvailable(boolean appointmentRecallAvailable) {
		this.appointmentRecallAvailable = appointmentRecallAvailable;
	}
	/**
	 * @return the appointmentFormAvailable
	 */
	public boolean isAppointmentFormAvailable() {
		return appointmentFormAvailable;
	}
	/**
	 * @param appointmentFormAvailable the appointmentFormAvailable to set
	 */
	public void setAppointmentFormAvailable(boolean appointmentFormAvailable) {
		this.appointmentFormAvailable = appointmentFormAvailable;
	}
	/**
	 * @return the reminderCampaignsRunningStatus
	 */
	public boolean isReminderCampaignsRunningStatus() {
		return reminderCampaignsRunningStatus;
	}
	/**
	 * @param reminderCampaignsRunningStatus the reminderCampaignsRunningStatus to set
	 */
	public void setReminderCampaignsRunningStatus(boolean reminderCampaignsRunningStatus) {
		this.reminderCampaignsRunningStatus = reminderCampaignsRunningStatus;
	}
	
	public Long getCxEmailCount() {
		return cxEmailCount;
	}
	
	public void setCxEmailCount(Long cxEmailCount) {
		this.cxEmailCount = cxEmailCount;
	}
	
	public Long getRrEmailCount() {
		return rrEmailCount;
	}
	
	public void setRrEmailCount(Long rrEmailCount) {
		this.rrEmailCount = rrEmailCount;
	}
	
	public Long getSurveyEmailCount() {
		return surveyEmailCount;
	}
	
	public void setSurveyEmailCount(Long surveyEmailCount) {
		this.surveyEmailCount = surveyEmailCount;
	}
	
	public Long getPromotionEmailCount() {
		return promotionEmailCount;
	}
	
	public void setPromotionEmailCount(Long promotionEmailCount) {
		this.promotionEmailCount = promotionEmailCount;
	}
	
	public Long getReferralEmailCount() {
		return referralEmailCount;
	}
	
	public void setReferralEmailCount(Long referralEmailCount) {
		this.referralEmailCount = referralEmailCount;
	}
	
	public Long getAppointmentReminderEmailCount() {
		return appointmentReminderEmailCount;
	}
	
	public void setAppointmentReminderEmailCount(Long appointmentReminderEmailCount) {
		this.appointmentReminderEmailCount = appointmentReminderEmailCount;
	}
	
	public Long getAppointmentRecallEmailCount() {
		return appointmentRecallEmailCount;
	}
	
	public void setAppointmentRecallEmailCount(Long appointmentRecallEmailCount) {
		this.appointmentRecallEmailCount = appointmentRecallEmailCount;
	}
	
	public Long getAppointmentFormEmailCount() {
		return appointmentFormEmailCount;
	}
	
	public void setAppointmentFormEmailCount(Long appointmentFormEmailCount) {
		this.appointmentFormEmailCount = appointmentFormEmailCount;
	}
	
	
}
