package com.birdeye.campaign.dto;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReviewRequestLiteResponse implements Serializable {
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -5509336611912076849L;
	private Long				reviewRequestId;
	private Integer				checkinId;
	
	public ReviewRequestLiteResponse() {
		
	}
	
	public ReviewRequestLiteResponse(Long reviewRequestId, Integer checkinId) {
		super();
		this.reviewRequestId = reviewRequestId;
		this.checkinId = checkinId;
	}

	public Long getReviewRequestId() {
		return reviewRequestId;
	}
	
	public void setReviewRequestId(Long reviewRequestId) {
		this.reviewRequestId = reviewRequestId;
	}
	
	public Integer getCheckinId() {
		return checkinId;
	}
	
	public void setCheckinId(Integer checkinId) {
		this.checkinId = checkinId;
	}

	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return b.toString();
	}
}
