package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class AppointmentInfoLiteDTO implements Serializable {
	
	private static final long				serialVersionUID	= -7692634166295926545L;
	
	private Integer							appointmentId;
	
	private Integer							businessId;
	
	private Long							startTime;
	
	private Long							endTime;
	
	private String							appointmentStatus;
	
	private Boolean							bookingForSelf;
	
	private Integer							specialistId;
	
	private String							specialistName;
	
	private Integer							serviceId;
	
	private String							serviceName;
	
	private String							patientFirstName;
	
	private String							patientLastName;
	
	private String							paitnetDob;
	
	private String							patientGender;
	
	private Integer							age;
	
	private Integer							customerId;
	
	private String							extAppointmentId;
	
	private String							locale;
	
	private String							source;
	
	private String							customerType;
	
	private String							formFillStatus;
	
	private Integer							campaignId;
	
	private Integer							appointmentStartTime;
	
	private List<AppointmentCustomFieldDTO>	customFields;
	
	private Boolean							futureAppointmentPresent;
	
	private String							dayOfTheWeek;
	
	/**
	 * @return the appointmentId
	 */
	public Integer getAppointmentId() {
		return appointmentId;
	}
	
	/**
	 * @param appointmentId
	 *            the appointmentId to set
	 */
	public void setAppointmentId(Integer appointmentId) {
		this.appointmentId = appointmentId;
	}
	
	/**
	 * @return the businessId
	 */
	public Integer getBusinessId() {
		return businessId;
	}
	
	/**
	 * @param businessId
	 *            the businessId to set
	 */
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	/**
	 * @return the startTime
	 */
	public Long getStartTime() {
		return startTime;
	}
	
	/**
	 * @param startTime
	 *            the startTime to set
	 */
	public void setStartTime(Long startTime) {
		this.startTime = startTime;
	}
	
	/**
	 * @return the endTime
	 */
	public Long getEndTime() {
		return endTime;
	}
	
	/**
	 * @param endTime
	 *            the endTime to set
	 */
	public void setEndTime(Long endTime) {
		this.endTime = endTime;
	}
	
	/**
	 * @return the appointmentStatus
	 */
	public String getAppointmentStatus() {
		return appointmentStatus;
	}
	
	/**
	 * @param appointmentStatus
	 *            the appointmentStatus to set
	 */
	public void setAppointmentStatus(String appointmentStatus) {
		this.appointmentStatus = appointmentStatus;
	}
	
	/**
	 * @return the bookingForSelf
	 */
	public Boolean getBookingForSelf() {
		return bookingForSelf;
	}
	
	/**
	 * @param bookingForSelf
	 *            the bookingForSelf to set
	 */
	public void setBookingForSelf(Boolean bookingForSelf) {
		this.bookingForSelf = bookingForSelf;
	}
	
	/**
	 * @return the specialistId
	 */
	public Integer getSpecialistId() {
		return specialistId;
	}
	
	/**
	 * @param specialistId
	 *            the specialistId to set
	 */
	public void setSpecialistId(Integer specialistId) {
		this.specialistId = specialistId;
	}
	
	/**
	 * @return the specialistName
	 */
	public String getSpecialistName() {
		return specialistName;
	}
	
	/**
	 * @param specialistName
	 *            the specialistName to set
	 */
	public void setSpecialistName(String specialistName) {
		this.specialistName = specialistName;
	}
	
	/**
	 * @return the serviceId
	 */
	public Integer getServiceId() {
		return serviceId;
	}
	
	/**
	 * @param serviceId
	 *            the serviceId to set
	 */
	public void setServiceId(Integer serviceId) {
		this.serviceId = serviceId;
	}
	
	/**
	 * @return the serviceName
	 */
	public String getServiceName() {
		return serviceName;
	}
	
	/**
	 * @param serviceName
	 *            the serviceName to set
	 */
	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}
	
	/**
	 * @return the patientFirstName
	 */
	public String getPatientFirstName() {
		return patientFirstName;
	}
	
	/**
	 * @param patientFirstName
	 *            the patientFirstName to set
	 */
	public void setPatientFirstName(String patientFirstName) {
		this.patientFirstName = patientFirstName;
	}
	
	/**
	 * @return the patientLastName
	 */
	public String getPatientLastName() {
		return patientLastName;
	}
	
	/**
	 * @param patientLastName
	 *            the patientLastName to set
	 */
	public void setPatientLastName(String patientLastName) {
		this.patientLastName = patientLastName;
	}
	
	/**
	 * @return the paitnetDob
	 */
	public String getPaitnetDob() {
		return paitnetDob;
	}
	
	/**
	 * @param paitnetDob
	 *            the paitnetDob to set
	 */
	public void setPaitnetDob(String paitnetDob) {
		this.paitnetDob = paitnetDob;
	}
	
	/**
	 * @return the patientGender
	 */
	public String getPatientGender() {
		return patientGender;
	}
	
	/**
	 * @param patientGender
	 *            the patientGender to set
	 */
	public void setPatientGender(String patientGender) {
		this.patientGender = patientGender;
	}
	
	/**
	 * @return the age
	 */
	public Integer getAge() {
		return age;
	}
	
	/**
	 * @param age
	 *            the age to set
	 */
	public void setAge(Integer age) {
		this.age = age;
	}
	
	/**
	 * @return the customerId
	 */
	public Integer getCustomerId() {
		return customerId;
	}
	
	/**
	 * @param customerId
	 *            the customerId to set
	 */
	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}
	
	public String getExtAppointmentId() {
		return extAppointmentId;
	}
	
	public void setExtAppointmentId(String extAppointmentId) {
		this.extAppointmentId = extAppointmentId;
	}
	
	/**
	 * @return the locale
	 */
	public String getLocale() {
		return locale;
	}
	
	/**
	 * @param locale
	 *            the locale to set
	 */
	public void setLocale(String locale) {
		this.locale = locale;
	}
	
	/**
	 * @return the source
	 */
	public String getSource() {
		return source;
	}
	
	/**
	 * @param source
	 *            the source to set
	 */
	public void setSource(String source) {
		this.source = source;
	}
	
	/**
	 * @return the customerType
	 */
	public String getCustomerType() {
		return customerType;
	}
	
	/**
	 * @param customerType
	 *            the customerType to set
	 */
	public void setCustomerType(String customerType) {
		this.customerType = customerType;
	}
	
	/**
	 * @return the formFillStatus
	 */
	public String getFormFillStatus() {
		return formFillStatus;
	}
	
	/**
	 * @param formFillStatus
	 *            the formFillStatus to set
	 */
	public void setFormFillStatus(String formFillStatus) {
		this.formFillStatus = formFillStatus;
	}
	
	/**
	 * @return the campaignId
	 */
	public Integer getCampaignId() {
		return campaignId;
	}
	
	/**
	 * @param campaignId
	 *            the campaignId to set
	 */
	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}
	
	/**
	 * @return the appointmentStartTime
	 */
	public Integer getAppointmentStartTime() {
		return appointmentStartTime;
	}
	
	/**
	 * @param appointmentStartTime
	 *            the appointmentStartTime to set
	 */
	public void setAppointmentStartTime(Integer appointmentStartTime) {
		this.appointmentStartTime = appointmentStartTime;
	}
	
	public List<AppointmentCustomFieldDTO> getCustomFields() {
		return customFields;
	}
	
	public void setCustomFields(List<AppointmentCustomFieldDTO> customFields) {
		this.customFields = customFields;
	}
	
	/**
	 * @return the futureAppointmentPresent
	 */
	public Boolean getFutureAppointmentPresent() {
		return futureAppointmentPresent;
	}
	
	/**
	 * @param futureAppointmentPresent
	 *            the futureAppointmentPresent to set
	 */
	public void setFutureAppointmentPresent(Boolean futureAppointmentPresent) {
		this.futureAppointmentPresent = futureAppointmentPresent;
	}
	
	/**
	 * @return the dayOfTheWeek
	 */
	public String getDayOfTheWeek() {
		return dayOfTheWeek;
	}
	
	/**
	 * @param dayOfTheWeek
	 *            the dayOfTheWeek to set
	 */
	public void setDayOfTheWeek(String dayOfTheWeek) {
		this.dayOfTheWeek = dayOfTheWeek;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
