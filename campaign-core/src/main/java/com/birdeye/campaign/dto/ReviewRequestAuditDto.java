package com.birdeye.campaign.dto;

import java.util.HashMap;
import java.util.Map;

import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.CampaignEvent;
import com.birdeye.campaign.entity.ReviewRequest;

/**
 * 
 * <AUTHOR>
 *
 */
public class ReviewRequestAuditDto {
	
	private Long				reviewRequestId;
	
	private Integer				customerId;
	
	private String 				customerName;
	
	private String 				customerPhone;
	
	private String 				customerPhoneE164;
	
	private String 				customerEmail;
	
	private Integer				businessId;
	
	private String				source;
	
	private Integer				templateId;
	
	private Integer				campaignId;
	
	private String				requestType;
		
	private String				deliveryStatus;
	
	private String				failureReason;
	
	private Integer				surveyId;
	
	private Integer				reminderCount;
	
	private String				reviewRequestCreationDate;
	
	private String				reviewRequestSentOnDate;
	
	private CampaignEventInfo	campaignEventInfo;
	
	private Map<String, Object>	nexusCommAudit;
	
	private Map<String,Object> campaignDetails;
	
	public static class CampaignEventInfo {
		
		private String	category;		// email/sms
		
		private String	type;			// review_request_new/customer_experience/survey/promotional
		
		private String	campaignType;	// instant/drip/ongoing
		
		private String	rrActualTime;	// Time at which review request was sent/scheduled for, irrespective of DND period
		
		private String	scheduledTime;
		
		private Integer	isDND;
		
		private Integer	isReminder;
		
		private Integer	numAttempts;
		
		private String	createdAt;
		
		private String	updatedAt;
		
		private String	status;			// INIT/INPROGRESS/SUCCESS/FAILED
		
		private String	failureReason;
		
		public CampaignEventInfo() {
			
		}
		
		public CampaignEventInfo(CampaignEvent campaignEvent) {
			this.category = campaignEvent.getCategory();
			this.type = campaignEvent.getType();
			this.campaignType = campaignEvent.getCampaignType();
			this.rrActualTime = String.valueOf(campaignEvent.getRrActualTime());
			this.scheduledTime = String.valueOf(campaignEvent.getScheduledTime());
			this.isDND = campaignEvent.getIsDND();
			this.isReminder = campaignEvent.getIsReminder();
			this.numAttempts = campaignEvent.getNumAttempts();
			this.createdAt = String.valueOf(campaignEvent.getCreatedAt());
			this.updatedAt = String.valueOf(campaignEvent.getUpdatedAt());
			this.status = campaignEvent.getStatus();
			this.failureReason = campaignEvent.getFailureReason();
		}
		
		public String getCategory() {
			return category;
		}
		
		public void setCategory(String category) {
			this.category = category;
		}
		
		public String getType() {
			return type;
		}
		
		public void setType(String type) {
			this.type = type;
		}
		
		public String getCampaignType() {
			return campaignType;
		}
		
		public void setCampaignType(String campaignType) {
			this.campaignType = campaignType;
		}
		
		public String getRrActualTime() {
			return rrActualTime;
		}
		
		public void setRrActualTime(String rrActualTime) {
			this.rrActualTime = rrActualTime;
		}
		
		public String getScheduledTime() {
			return scheduledTime;
		}
		
		public void setScheduledTime(String scheduledTime) {
			this.scheduledTime = scheduledTime;
		}
		
		public Integer getIsDND() {
			return isDND;
		}
		
		public void setIsDND(Integer isDND) {
			this.isDND = isDND;
		}
		
		public Integer getIsReminder() {
			return isReminder;
		}
		
		public void setIsReminder(Integer isReminder) {
			this.isReminder = isReminder;
		}
		
		public Integer getNumAttempts() {
			return numAttempts;
		}
		
		public void setNumAttempts(Integer numAttempts) {
			this.numAttempts = numAttempts;
		}
		
		public String getCreatedAt() {
			return createdAt;
		}
		
		public void setCreatedAt(String createdAt) {
			this.createdAt = createdAt;
		}
		
		public String getUpdatedAt() {
			return updatedAt;
		}
		
		public void setUpdatedAt(String updatedAt) {
			this.updatedAt = updatedAt;
		}
		
		public String getStatus() {
			return status;
		}
		
		public void setStatus(String status) {
			this.status = status;
		}
		
		public String getFailureReason() {
			return failureReason;
		}
		
		public void setFailureReason(String failureReason) {
			this.failureReason = failureReason;
		}
		
	}
	
	public ReviewRequestAuditDto() {
		
	}
	
	private static final String ENT_ID="accountId";
	private static final String PRIORITY="priority";
	private static final String RUN_TYPE="runType";
	private static final String SCHED_TYPE="scheduleType";
	
	public ReviewRequestAuditDto(ReviewRequest reviewRequest, Campaign campaign, CampaignEvent campaignEvent) {
		this.reviewRequestId = reviewRequest.getId();
		this.customerId = reviewRequest.getCustId();
		this.businessId = reviewRequest.getBusinessId();
		this.source = reviewRequest.getSource();
		this.templateId = reviewRequest.getTemplateId();
		this.campaignId = reviewRequest.getCampaignId();
		this.requestType = reviewRequest.getRequestType();
		this.deliveryStatus = reviewRequest.getDeliveryStatus();
		this.failureReason = reviewRequest.getFailureReason();
		this.surveyId = reviewRequest.getSurveyId();
		this.reminderCount = reviewRequest.getReminderCount();
		this.reviewRequestCreationDate = String.valueOf(reviewRequest.getRequestDate());
		this.reviewRequestSentOnDate = String.valueOf(reviewRequest.getSentOn());
		if (campaign != null) {
			campaignDetails = new HashMap<String, Object>();
			campaignDetails.put(ENT_ID, campaign.getEnterpriseId());
			campaignDetails.put(PRIORITY, campaign.getPriority());
			campaignDetails.put(RUN_TYPE, campaign.getRunType());
			campaignDetails.put(SCHED_TYPE, campaign.getSchedulingType());
		}
		if (campaignEvent != null) {
			this.campaignEventInfo = new CampaignEventInfo(campaignEvent);
		}
	}
	
	public Long getReviewRequestId() {
		return reviewRequestId;
	}
	
	public void setReviewRequestId(Long reviewRequestId) {
		this.reviewRequestId = reviewRequestId;
	}
	
	public Integer getCustomerId() {
		return customerId;
	}
	
	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}
	
	public Integer getBusinessId() {
		return businessId;
	}
	
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getCustomerPhone() {
		return customerPhone;
	}

	public void setCustomerPhone(String customerPhone) {
		this.customerPhone = customerPhone;
	}
	
	public String getCustomerPhoneE164() {
		return customerPhoneE164;
	}

	public void setCustomerPhoneE164(String customerPhoneE164) {
		this.customerPhoneE164 = customerPhoneE164;
	}

	public String getCustomerEmail() {
		return customerEmail;
	}

	public void setCustomerEmail(String customerEmail) {
		this.customerEmail = customerEmail;
	}
	
	public String getSource() {
		return source;
	}
	
	public void setSource(String source) {
		this.source = source;
	}
	
	public Integer getTemplateId() {
		return templateId;
	}
	
	public void setTemplateId(Integer templateId) {
		this.templateId = templateId;
	}
	
	public Integer getCampaignId() {
		return campaignId;
	}
	
	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}
	
	public String getRequestType() {
		return requestType;
	}
	
	public void setRequestType(String requestType) {
		this.requestType = requestType;
	}
	

	public String getDeliveryStatus() {
		return deliveryStatus;
	}
	
	public void setDeliveryStatus(String deliveryStatus) {
		this.deliveryStatus = deliveryStatus;
	}
	
	public String getFailureReason() {
		return failureReason;
	}
	
	public void setFailureReason(String failureReason) {
		this.failureReason = failureReason;
	}
	
	public Integer getSurveyId() {
		return surveyId;
	}
	
	public void setSurveyId(Integer surveyId) {
		this.surveyId = surveyId;
	}
	
	public Integer getReminderCount() {
		return reminderCount;
	}
	
	public void setReminderCount(Integer reminderCount) {
		this.reminderCount = reminderCount;
	}
	
	public String getReviewRequestCreationDate() {
		return reviewRequestCreationDate;
	}
	
	public void setReviewRequestCreationDate(String reviewRequestCreationDate) {
		this.reviewRequestCreationDate = reviewRequestCreationDate;
	}
	
	public String getReviewRequestSentOnDate() {
		return reviewRequestSentOnDate;
	}
	
	public void setReviewRequestSentOnDate(String reviewRequestSentOnDate) {
		this.reviewRequestSentOnDate = reviewRequestSentOnDate;
	}
	
	public CampaignEventInfo getCampaignEventInfo() {
		return campaignEventInfo;
	}
	
	public Map<String,Object> getCampaignDetails(){
		return campaignDetails;
	}
	
	public Map<String, Object> getNexusCommAudit() {
		return nexusCommAudit;
	}
	
	public void setNexusCommAudit(Map<String, Object> nexusCommAudit) {
		this.nexusCommAudit = nexusCommAudit;
	}
	
}
