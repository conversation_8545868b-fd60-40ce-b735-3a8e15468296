package com.birdeye.campaign.dto;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;

public class CommunicationUsageStatsMessage implements Serializable{

	private static final long serialVersionUID = 4066664110816143689L;
	
	@JsonProperty("openStartedCt")
	private Long openStartedCount; 
	
	@JsonProperty("openStartedPer")
	private Double openStartedPercent = 0d;
	
	@JsonProperty("clickedCmpCt")
	private Long clickedCompletedCount; 
	
	@JsonProperty("clickedCmpPer")
	private Double clickedCompletedPercent;
	
	@JsonProperty("sentSuccess")
	private Long sentSuccess = 0l;

	@JsonProperty("delivered")
	private Long delivered = 0l;
	

	/**
	 * @return the openStartedCount
	 */
	public Long getOpenStartedCount() {
		return openStartedCount;
	}

	/**
	 * @param openStartedCount the openStartedCount to set
	 */
	public void setOpenStartedCount(Long openStartedCount) {
		this.openStartedCount = openStartedCount;
	}

	/**
	 * @return the openStartedPercent
	 */
	public Double getOpenStartedPercent() {
		return openStartedPercent;
	}

	/**
	 * @param openStartedPercent the openStartedPercent to set
	 */
	public void setOpenStartedPercent(Double openStartedPercent) {
		this.openStartedPercent = openStartedPercent;
	}

	/**
	 * @return the clickedCompletedCount
	 */
	public Long getClickedCompletedCount() {
		return clickedCompletedCount;
	}

	/**
	 * @param clickedCompletedCount the clickedCompletedCount to set
	 */
	public void setClickedCompletedCount(Long clickedCompletedCount) {
		this.clickedCompletedCount = clickedCompletedCount;
	}

	/**
	 * @return the clickedCompletedPercent
	 */
	public Double getClickedCompletedPercent() {
		return clickedCompletedPercent;
	}

	/**
	 * @param clickedCompletedPercent the clickedCompletedPercent to set
	 */
	public void setClickedCompletedPercent(Double clickedCompletedPercent) {
		this.clickedCompletedPercent = clickedCompletedPercent;
	}

	/**
	 * @return the sentSuccess
	 */
	public Long getSentSuccess() {
		return sentSuccess;
	}

	/**
	 * @param sentSuccess the sentSuccess to set
	 */
	public void setSentSuccess(Long sentSuccess) {
		this.sentSuccess = sentSuccess;
	}

	public Long getDelivered() {
		return delivered;
	}

	public void setDelivered(Long delivered) {
		this.delivered = delivered;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("CommunicationUsageStatsMessage [openStartedCount=");
		builder.append(openStartedCount);
		builder.append(", openStartedPercent=");
		builder.append(openStartedPercent);
		builder.append(", clickedCompletedCount=");
		builder.append(clickedCompletedCount);
		builder.append(", clickedCompletedPercent=");
		builder.append(clickedCompletedPercent);
		builder.append(", sentSuccess=");
		builder.append(sentSuccess);
		builder.append(", delivered=");
		builder.append(delivered);
		builder.append("]");
		return builder.toString();
	}

}
