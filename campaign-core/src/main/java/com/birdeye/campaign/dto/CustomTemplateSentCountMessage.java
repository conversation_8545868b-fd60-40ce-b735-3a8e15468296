/**
 * @file_name CustomTemplateSentCountMessage.java
 * @created_date 28 Jan 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.dto;

import java.io.Serializable;

/**
 * @file_name CustomTemplateSentCountMessage.java
 * @created_date 28 Jan 2020
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public class CustomTemplateSentCountMessage implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 4833705457982515586L;
	private Integer				templateId;
	private Long				sentCount;
	
	/**
	 * @return the templateId
	 */
	public Integer getTemplateId() {
		return templateId;
	}
	
	/**
	 * @param templateId
	 *            the templateId to set
	 */
	public void setTemplateId(Integer templateId) {
		this.templateId = templateId;
	}
	
	/**
	 * @return the sentCount
	 */
	public Long getSentCount() {
		return sentCount;
	}
	
	/**
	 * @param sentCount
	 *            the sentCount to set
	 */
	public void setSentCount(Long sentCount) {
		this.sentCount = sentCount;
	}
	
	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("CustomTemplateSentCountMessage [templateId=");
		builder.append(templateId);
		builder.append(", sentCount=");
		builder.append(sentCount);
		builder.append("]");
		return builder.toString();
	}
	
	/**
	 * @param templateId
	 * @param sentCount
	 */
	public CustomTemplateSentCountMessage(Integer templateId, Long sentCount) {
		super();
		this.templateId = templateId;
		this.sentCount = sentCount;
	}
	
	/**
	 * 
	 */
	public CustomTemplateSentCountMessage() {
		super();
	}
	
}
