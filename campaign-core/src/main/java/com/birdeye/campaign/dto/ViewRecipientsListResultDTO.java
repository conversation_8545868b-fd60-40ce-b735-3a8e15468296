/**
 * @file_name ViewRecipientsListResultDTO.java
 * @created_date 17 Aug 2020
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * @file_name ViewRecipientsListResultDTO.java
 * @created_date 17 Aug 2020
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ViewRecipientsListResultDTO implements Serializable {
	
	private static final long						serialVersionUID	= 4562634065558444015L;
	private Long									totalCount;
	private Long									pageCount;
	private List<ViewRecipientsCommunicationDTO>	data;

	
	public ViewRecipientsListResultDTO() {
		super();
	}

	public ViewRecipientsListResultDTO(Long totalCount, List<ViewRecipientsCommunicationDTO> data) {
		super();
		this.totalCount = totalCount;
		this.pageCount = Long.valueOf(CollectionUtils.size(data));
		this.data = data;
	}


	public static ViewRecipientsListResultDTO emptyResponse() {
		return new ViewRecipientsListResultDTO(0l, Collections.emptyList());
	}
	
	/**
	 * @return the totalCount
	 */
	public Long getTotalCount() {
		return totalCount;
	}
	
	/**
	 * @param totalCount
	 *            the totalCount to set
	 */
	public void setTotalCount(Long totalCount) {
		this.totalCount = totalCount;
	}
	
	/**
	 * @return the data
	 */
	public List<ViewRecipientsCommunicationDTO> getData() {
		return data;
	}
	
	/**
	 * @param data
	 *            the data to set
	 */
	public void setData(List<ViewRecipientsCommunicationDTO> data) {
		this.data = data;
	}
	
	
	/**
	 * @return the pageCount
	 */
	public Long getPageCount() {
		return pageCount;
	}



	/**
	 * @param pageCount the pageCount to set
	 */
	public void setPageCount(Long pageCount) {
		this.pageCount = pageCount;
	}



	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return b.toString();
	}
	
}
