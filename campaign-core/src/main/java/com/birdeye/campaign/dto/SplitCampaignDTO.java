package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class SplitCampaignDTO implements Serializable {
	
	/**
	 * 
	 */
	private static final long		serialVersionUID	= -3547732662328925083L;
	
	private Integer					id;											// Split Campaign Id
	
	private String					splitCampaignName;
	
	private Date					startAt;
	
	private String					runType;
	
	private Integer					status;
	
	private Integer					priorityOrder;
	
	private String					priority;
	
	private String					triggerType;
	
	private Integer					createdBy;
	
	private Date					updatedAt;
	
	private List<SplitCampaignMappingDTO>	splitMappingData	= new ArrayList<>();
	
	public SplitCampaignDTO() {
		
	}
	
	public SplitCampaignDTO(Integer id, Integer splitCampaignPercent, String type, Integer campaignId, Date updatedAt) {
		this.id = id;
		this.updatedAt = updatedAt;
	}
	
	public SplitCampaignDTO(Integer id, String name, Date startAt, Integer status, String priority, Integer priorityOrder, String runType, String triggerType, Integer createdBy, Date updatedAt) {
		this.id = id;
		this.splitCampaignName = name;
		this.startAt = startAt;
		this.status = status;
		this.priorityOrder = priorityOrder;
		this.priority = priority;
		this.runType = runType;
		this.triggerType = triggerType;
		this.createdBy = createdBy;
		this.updatedAt = updatedAt;
	}
	
	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}
	
	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	
	/**
	 * @return the splitCampaignName
	 */
	public String getSplitCampaignName() {
		return splitCampaignName;
	}
	
	/**
	 * @param splitCampaignName
	 *            the splitCampaignName to set
	 */
	public void setSplitCampaignName(String splitCampaignName) {
		this.splitCampaignName = splitCampaignName;
	}
	
	/**
	 * @return the startAt
	 */
	public Date getStartAt() {
		return startAt;
	}
	
	/**
	 * @param startAt
	 *            the startAt to set
	 */
	public void setStartAt(Date startAt) {
		this.startAt = startAt;
	}
	
	/**
	 * @return the runType
	 */
	public String getRunType() {
		return runType;
	}
	
	/**
	 * @param runType
	 *            the runType to set
	 */
	public void setRunType(String runType) {
		this.runType = runType;
	}
	
	/**
	 * @return the status
	 */
	public Integer getStatus() {
		return status;
	}
	
	/**
	 * @param status
	 *            the status to set
	 */
	public void setStatus(Integer status) {
		this.status = status;
	}
	
	/**
	 * @return the priorityOrder
	 */
	public Integer getPriorityOrder() {
		return priorityOrder;
	}
	
	/**
	 * @param priorityOrder
	 *            the priorityOrder to set
	 */
	public void setPriorityOrder(Integer priorityOrder) {
		this.priorityOrder = priorityOrder;
	}
	
	/**
	 * @return the priority
	 */
	public String getPriority() {
		return priority;
	}
	
	/**
	 * @param priority
	 *            the priority to set
	 */
	public void setPriority(String priority) {
		this.priority = priority;
	}
	
	/**
	 * @return the triggerType
	 */
	public String getTriggerType() {
		return triggerType;
	}
	
	/**
	 * @param triggerType
	 *            the triggerType to set
	 */
	public void setTriggerType(String triggerType) {
		this.triggerType = triggerType;
	}
	
	/**
	 * @return the createdBy
	 */
	public Integer getCreatedBy() {
		return createdBy;
	}
	
	/**
	 * @param createdBy
	 *            the createdBy to set
	 */
	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}
	
	/**
	 * @return the updatedAt
	 */
	public Date getUpdatedAt() {
		return updatedAt;
	}
	
	/**
	 * @param updatedAt
	 *            the updatedAt to set
	 */
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}
	
	/**
	 * @return the splitMappingData
	 */
	public List<SplitCampaignMappingDTO> getSplitMappingData() {
		return splitMappingData;
	}
	
	/**
	 * @param splitMappingData
	 *            the splitMappingData to set
	 */
	public void setSplitMappingData(List<SplitCampaignMappingDTO> splitMappingData) {
		this.splitMappingData = splitMappingData;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
