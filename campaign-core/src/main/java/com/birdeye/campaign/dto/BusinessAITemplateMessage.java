package com.birdeye.campaign.dto;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

public class BusinessAITemplateMessage {
	private Integer							id;
	private Integer							nonAITemplateId;
	private String							name;
	private String							templateType;
	private String							channel;
	private String							label;
	private Integer							canDelete;
	private Date							updatedAt;
	private CommunicationUsageStatsMessage	usageStats			= new CommunicationUsageStatsMessage();
	private Integer							status;
	private String							previewText;
	private String							thumbnailUrl;
	private boolean							attachmentExists;
	private String							editable;
	private String							nonEditable;
	private String							mediaUrl;
	private Long							lastUsedTime;
	private String							unsubscribeText;
	private List<TemplateCustomFieldSRO>	selectedCustomFields;
	private Long							smsSegments;
	private Integer							includeUnsubscribeText;
	private Integer							locationTemplate	= 0;
	private List<Integer>					selectedLocations;
	private Boolean							formUrlPresent;
	private List<TemplateCustomFieldSRO>	appointmentCustomFields;
	private Integer							isDefaultTemplate	= 0;
	private Boolean							isGlobalTemplate	= false;
	private List<TemplateCustomFieldSRO>	locationCustomFields;
	private String							templateCategory	= StringUtils.EMPTY;					// < applicable values: ['feedback', 'marketing', 'service'] >
	private Integer							includeImageWithText;
	
	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}
	
	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	
	/**
	 * @return the nonAITemplateId
	 */
	public Integer getNonAITemplateId() {
		return nonAITemplateId;
	}

	/**
	 * @param nonAITemplateId the nonAITemplateId to set
	 */
	public void setNonAITemplateId(Integer nonAITemplateId) {
		this.nonAITemplateId = nonAITemplateId;
	}

	/**
	 * @return the name
	 */
	public String getName() {
		return name;
	}
	
	/**
	 * @param name
	 *            the name to set
	 */
	public void setName(String name) {
		this.name = name;
	}
	
	/**
	 * @return the templateType
	 */
	public String getTemplateType() {
		return templateType;
	}
	
	/**
	 * @param templateType
	 *            the templateType to set
	 */
	public void setTemplateType(String templateType) {
		this.templateType = templateType;
	}
	
	/**
	 * @return the channel
	 */
	public String getChannel() {
		return channel;
	}
	
	/**
	 * @param channel
	 *            the channel to set
	 */
	public void setChannel(String channel) {
		this.channel = channel;
	}
	
	/**
	 * @return the label
	 */
	public String getLabel() {
		return label;
	}
	
	/**
	 * @param label
	 *            the label to set
	 */
	public void setLabel(String label) {
		this.label = label;
	}
	
	/**
	 * @return the canDelete
	 */
	public Integer getCanDelete() {
		return canDelete;
	}
	
	/**
	 * @param canDelete
	 *            the canDelete to set
	 */
	public void setCanDelete(Integer canDelete) {
		this.canDelete = canDelete;
	}
	
	/**
	 * @return the updatedAt
	 */
	public Date getUpdatedAt() {
		return updatedAt;
	}
	
	/**
	 * @param updatedAt
	 *            the updatedAt to set
	 */
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}
	
	/**
	 * @return the usageStats
	 */
	public CommunicationUsageStatsMessage getUsageStats() {
		return usageStats;
	}
	
	/**
	 * @param usageStats
	 *            the usageStats to set
	 */
	public void setUsageStats(CommunicationUsageStatsMessage usageStats) {
		this.usageStats = usageStats;
	}
	
	/**
	 * @return the status
	 */
	public Integer getStatus() {
		return status;
	}
	
	/**
	 * @param status
	 *            the status to set
	 */
	public void setStatus(Integer status) {
		this.status = status;
	}
	
	/**
	 * @return the previewText
	 */
	public String getPreviewText() {
		return previewText;
	}
	
	/**
	 * @param previewText
	 *            the previewText to set
	 */
	public void setPreviewText(String previewText) {
		this.previewText = previewText;
	}
	
	/**
	 * @return the thumbnailUrl
	 */
	public String getThumbnailUrl() {
		return thumbnailUrl;
	}
	
	/**
	 * @param thumbnailUrl
	 *            the thumbnailUrl to set
	 */
	public void setThumbnailUrl(String thumbnailUrl) {
		this.thumbnailUrl = thumbnailUrl;
	}
	
	/**
	 * @return the attachmentExists
	 */
	public boolean isAttachmentExists() {
		return attachmentExists;
	}
	
	/**
	 * @param attachmentExists
	 *            the attachmentExists to set
	 */
	public void setAttachmentExists(boolean attachmentExists) {
		this.attachmentExists = attachmentExists;
	}
	
	/**
	 * @return the editable
	 */
	public String getEditable() {
		return editable;
	}
	
	/**
	 * @param editable
	 *            the editable to set
	 */
	public void setEditable(String editable) {
		this.editable = editable;
	}
	
	/**
	 * @return the nonEditable
	 */
	public String getNonEditable() {
		return nonEditable;
	}
	
	/**
	 * @param nonEditable
	 *            the nonEditable to set
	 */
	public void setNonEditable(String nonEditable) {
		this.nonEditable = nonEditable;
	}
	
	/**
	 * @return the mediaUrl
	 */
	public String getMediaUrl() {
		return mediaUrl;
	}
	
	/**
	 * @param mediaUrl
	 *            the mediaUrl to set
	 */
	public void setMediaUrl(String mediaUrl) {
		this.mediaUrl = mediaUrl;
	}
	
	/**
	 * @return the lastUsedTime
	 */
	public Long getLastUsedTime() {
		return lastUsedTime;
	}
	
	/**
	 * @param lastUsedTime
	 *            the lastUsedTime to set
	 */
	public void setLastUsedTime(Long lastUsedTime) {
		this.lastUsedTime = lastUsedTime;
	}
	
	/**
	 * @return the unsubscribeText
	 */
	public String getUnsubscribeText() {
		return unsubscribeText;
	}
	
	/**
	 * @param unsubscribeText
	 *            the unsubscribeText to set
	 */
	public void setUnsubscribeText(String unsubscribeText) {
		this.unsubscribeText = unsubscribeText;
	}
	
	/**
	 * @return the selectedCustomFields
	 */
	public List<TemplateCustomFieldSRO> getSelectedCustomFields() {
		return selectedCustomFields;
	}
	
	/**
	 * @param selectedCustomFields
	 *            the selectedCustomFields to set
	 */
	public void setSelectedCustomFields(List<TemplateCustomFieldSRO> selectedCustomFields) {
		this.selectedCustomFields = selectedCustomFields;
	}
	
	/**
	 * @return the smsSegments
	 */
	public Long getSmsSegments() {
		return smsSegments;
	}
	
	/**
	 * @param smsSegments
	 *            the smsSegments to set
	 */
	public void setSmsSegments(Long smsSegments) {
		this.smsSegments = smsSegments;
	}
	
	/**
	 * @return the includeUnsubscribeText
	 */
	public Integer getIncludeUnsubscribeText() {
		return includeUnsubscribeText;
	}
	
	/**
	 * @param includeUnsubscribeText
	 *            the includeUnsubscribeText to set
	 */
	public void setIncludeUnsubscribeText(Integer includeUnsubscribeText) {
		this.includeUnsubscribeText = includeUnsubscribeText;
	}
	
	/**
	 * @return the locationTemplate
	 */
	public Integer getLocationTemplate() {
		return locationTemplate;
	}
	
	/**
	 * @param locationTemplate
	 *            the locationTemplate to set
	 */
	public void setLocationTemplate(Integer locationTemplate) {
		this.locationTemplate = locationTemplate;
	}
	
	/**
	 * @return the selectedLocations
	 */
	public List<Integer> getSelectedLocations() {
		return selectedLocations;
	}
	
	/**
	 * @param selectedLocations
	 *            the selectedLocations to set
	 */
	public void setSelectedLocations(List<Integer> selectedLocations) {
		this.selectedLocations = selectedLocations;
	}
	
	/**
	 * @return the formUrlPresent
	 */
	public Boolean getFormUrlPresent() {
		return formUrlPresent;
	}
	
	/**
	 * @param formUrlPresent
	 *            the formUrlPresent to set
	 */
	public void setFormUrlPresent(Boolean formUrlPresent) {
		this.formUrlPresent = formUrlPresent;
	}
	
	/**
	 * @return the appointmentCustomFields
	 */
	public List<TemplateCustomFieldSRO> getAppointmentCustomFields() {
		return appointmentCustomFields;
	}
	
	/**
	 * @param appointmentCustomFields
	 *            the appointmentCustomFields to set
	 */
	public void setAppointmentCustomFields(List<TemplateCustomFieldSRO> appointmentCustomFields) {
		this.appointmentCustomFields = appointmentCustomFields;
	}
	
	/**
	 * @return the isDefaultTemplate
	 */
	public Integer getIsDefaultTemplate() {
		return isDefaultTemplate;
	}
	
	/**
	 * @param isDefaultTemplate
	 *            the isDefaultTemplate to set
	 */
	public void setIsDefaultTemplate(Integer isDefaultTemplate) {
		this.isDefaultTemplate = isDefaultTemplate;
	}
	
	/**
	 * @return the isGlobalTemplate
	 */
	public Boolean getIsGlobalTemplate() {
		return isGlobalTemplate;
	}
	
	/**
	 * @param isGlobalTemplate
	 *            the isGlobalTemplate to set
	 */
	public void setIsGlobalTemplate(Boolean isGlobalTemplate) {
		this.isGlobalTemplate = isGlobalTemplate;
	}
	
	/**
	 * @return the locationCustomFields
	 */
	public List<TemplateCustomFieldSRO> getLocationCustomFields() {
		return locationCustomFields;
	}
	
	/**
	 * @param locationCustomFields
	 *            the locationCustomFields to set
	 */
	public void setLocationCustomFields(List<TemplateCustomFieldSRO> locationCustomFields) {
		this.locationCustomFields = locationCustomFields;
	}
	
	/**
	 * @return the templateCategory
	 */
	public String getTemplateCategory() {
		return templateCategory;
	}
	
	/**
	 * @param templateCategory
	 *            the templateCategory to set
	 */
	public void setTemplateCategory(String templateCategory) {
		this.templateCategory = templateCategory;
	}
	
	/**
	 * @return the includeImageWithText
	 */
	public Integer getIncludeImageWithText() {
		return includeImageWithText;
	}
	
	/**
	 * @param includeImageWithText
	 *            the includeImageWithText to set
	 */
	public void setIncludeImageWithText(Integer includeImageWithText) {
		this.includeImageWithText = includeImageWithText;
	}
	
}
