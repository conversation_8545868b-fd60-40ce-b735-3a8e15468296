package com.birdeye.campaign.dto;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RecallBookDto {
	
	private Integer	businessId;
	
	private Long	reviewRequestId;
	
	private Long	appointmentRecallId;
	
	public Integer getBusinessId() {
		return businessId;
	}
	
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	public Long getReviewRequestId() {
		return reviewRequestId;
	}
	
	public void setReviewRequestId(Long reviewRequestId) {
		this.reviewRequestId = reviewRequestId;
	}
	
	public Long getAppointmentRecallId() {
		return appointmentRecallId;
	}
	
	public void setAppointmentRecallId(Long appointmentRecallId) {
		this.appointmentRecallId = appointmentRecallId;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}

// private Integer reviewId;

// private Long reviewDate;
