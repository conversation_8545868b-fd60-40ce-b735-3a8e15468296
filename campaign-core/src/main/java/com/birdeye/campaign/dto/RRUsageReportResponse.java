package com.birdeye.campaign.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RRUsageReportResponse extends BaseUsageReportResponse implements Serializable{
	
	private static final long serialVersionUID = 6833260735736968161L;
	private RRCommSentInfo				sentInfo		= new RRCommSentInfo();
	private RRCommOpenInfo				openInfo		= new RRCommOpenInfo();
	private RRCommDeliveredInfo			deliveredInfo	= new RRCommDeliveredInfo();
	private DeviceSource				clickInfo		= new DeviceSource();
	private UsageResponseFailureMessage	failureMessage	= new UsageResponseFailureMessage();
	private RRCommScheduledInfo			scheduledInfo	= new RRCommScheduledInfo();
	private Integer						reviewCount		= 0;
	private Double						avgRating		= 0.0;
	private Long 						uniqueRecipients = 0L;
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class ReviewAnaytics implements Comparable<ReviewAnaytics> {
		private String	name;
		private long		count;
		private double	percent;
		private int		sourceId;
		
		public String getName() {
			return name;
		}
		
		public void setName(String name) {
			this.name = name;
		}
		
		public long getCount() {
			return count;
		}
		
		public void setCount(long count) {
			this.count = count;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
		public int getSourceId() {
			return sourceId;
		}
		
		public void setSourceId(int sourceId) {
			this.sourceId = sourceId;
		}
		
		@Override
		public int compareTo(ReviewAnaytics obj) {
			return this.compareTo(obj);
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class DeviceSource {
		private long						total;
		private double						percent;
		private Collection<ReviewAnaytics>	rcmdCount;
		private Collection<ReviewAnaytics>	nonRcmdCount;
		
		public Collection<ReviewAnaytics> getRcmdCount() {
			return rcmdCount;
		}
		
		public void setRcmdCount(Collection<ReviewAnaytics> rcmdCount) {
			this.rcmdCount = rcmdCount;
		}
		
		public Collection<ReviewAnaytics> getNonRcmdCount() {
			return nonRcmdCount;
		}
		
		public void setNonRcmdCount(Collection<ReviewAnaytics> nonRcmdCount) {
			this.nonRcmdCount = nonRcmdCount;
		}
		
		public long getTotal() {
			return total;
		}
		
		public void addTotal(Long total) {
			this.total += total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
		@Override
		public String toString() {
			StringBuilder builder = new StringBuilder();
			builder.append("DeviceSource [total=");
			builder.append(total);
			builder.append(", percent=");
			builder.append(percent);
			builder.append(", rcmdCount=");
			builder.append(rcmdCount);
			builder.append(", nonRcmdCount=");
			builder.append(nonRcmdCount);
			builder.append("]");
			return builder.toString();
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class RRCommSentInfo {
		
		private long	total;

		private double	percent;
		private long	reqCount;
		private long	reqReminderCount;
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public double getPercent() {
			return percent;
		}

		public void setPercent(double percent) {
			this.percent = percent;
		}

		public void addTotal(long count) {
			this.total += count;
		}
		
		public long getReqCount() {
			return reqCount;
		}
		
		public void setReqCount(long reqCount) {
			this.reqCount = reqCount;
		}
		
		public long getReqReminderCount() {
			return reqReminderCount;
		}
		
		public void setReqReminderCount(long reqReminderCount) {
			this.reqReminderCount = reqReminderCount;
		}
		
		public void addReqReminderCount(long reqReminderCount) {
			this.reqReminderCount += reqReminderCount;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class RRCommOpenInfo {
		
		private long		total;
		private double	percent;
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
		public void addTotal(long count) {
			this.total += count;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class RRCommDeliveredInfo {
		
		private long		total;
		private double	percent;
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
		public void addTotal(long count) {
			this.total += count;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class RRCommScheduledInfo {
		
		private long		total;
		private double	percent;
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
		public void addTotal(long count) {
			this.total += count;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class ReviewsSummaryForReseller {
		private Integer	sourceId;
		private String	sourceName;
		private String	sourceAlias;
		private Double	avgRating;
		private Integer	reviewCount;
		
		public String getSourceName() {
			return sourceName;
		}
		
		public void setSourceName(String sourceName) {
			this.sourceName = sourceName;
		}
		
		public String getSourceAlias() {
			return sourceAlias;
		}
		
		public void setSourceAlias(String sourceAlias) {
			this.sourceAlias = sourceAlias;
		}
		
		public Double getAvgRating() {
			return avgRating;
		}
		
		public void setAvgRating(Double avgRating) {
			this.avgRating = avgRating;
		}
		
		public Integer getReviewCount() {
			return reviewCount;
		}
		
		public void setReviewCount(Integer reviewCount) {
			this.reviewCount = reviewCount;
		}
		
		public Integer getSourceId() {
			return sourceId;
		}
		
		public void setSourceId(Integer sourceId) {
			this.sourceId = sourceId;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class UsageResponseFailureMessage implements Serializable {
		
		private static final long			serialVersionUID	= -1376538977646901324L;
		
		private long						total;
		private double						percent;
		private List<FailureReasonMessage>	failureReasons		= new ArrayList<>();
		
		public long getTotal() {
			return total;
		}
		
		public void setTotal(long total) {
			this.total = total;
		}
		
		public double getPercent() {
			return percent;
		}
		
		public void setPercent(double percent) {
			this.percent = percent;
		}
		
		public List<FailureReasonMessage> getFailureReasons() {
			return failureReasons;
		}
		
		public void setFailureReasons(List<FailureReasonMessage> failureReasons) {
			this.failureReasons = failureReasons;
		}
		
		public void addTotal(Long total) {
			this.total += total;
		}
		
	}
	
	@JsonInclude(Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class FailureReasonMessage {
		
		private Long count = 0l;

		private Double percent;

		private Integer failureId;

		private String failureReason;

		public String getFailureReason() {
			return failureReason;
		}

		public void setFailureReason(String failureReason) {
			this.failureReason = failureReason;
		}

		public Long getCount() {
			return count;
		}

		public void setCount(Long count) {
			this.count = count;
		}

		public Double getPercent() {
			return percent;
		}

		public void setPercent(Double percent) {
			this.percent = percent;
		}

		public Integer getFailureId() {
			return failureId;
		}

		public void setFailureId(Integer failureId) {
			this.failureId = failureId;
		}

		public FailureReasonMessage(Integer failureId) {
			super();
			this.failureId = failureId;
		}

		public FailureReasonMessage() {
			super();
		}

	}

	public RRCommSentInfo getSentInfo() {
		return sentInfo;
	}

	public void setSentInfo(RRCommSentInfo sentInfo) {
		this.sentInfo = sentInfo;
	}

	public RRCommOpenInfo getOpenInfo() {
		return openInfo;
	}

	public void setOpenInfo(RRCommOpenInfo openInfo) {
		this.openInfo = openInfo;
	}

	public RRCommDeliveredInfo getDeliveredInfo() {
		return deliveredInfo;
	}

	public void setDeliveredInfo(RRCommDeliveredInfo deliveredInfo) {
		this.deliveredInfo = deliveredInfo;
	}

	public DeviceSource getClickInfo() {
		return clickInfo;
	}

	public void setClickInfo(DeviceSource clickInfo) {
		this.clickInfo = clickInfo;
	}

	public UsageResponseFailureMessage getFailureMessage() {
		return failureMessage;
	}

	public void setFailureMessage(UsageResponseFailureMessage failureMessage) {
		this.failureMessage = failureMessage;
	}

	public RRCommScheduledInfo getScheduledInfo() {
		return scheduledInfo;
	}

	public void setScheduledInfo(RRCommScheduledInfo scheduledInfo) {
		this.scheduledInfo = scheduledInfo;
	}

	/**
	 * @return the avgRating
	 */
	public Double getAvgRating() {
		return avgRating;
	}

	/**
	 * @param avgRating the avgRating to set
	 */
	public void setAvgRating(Double avgRating) {
		this.avgRating = avgRating;
	}

	/**
	 * @return the reviewCount
	 */
	public Integer getReviewCount() {
		return reviewCount;
	}

	/**
	 * @param reviewCount the reviewCount to set
	 */
	public void setReviewCount(Integer reviewCount) {
		this.reviewCount = reviewCount;
	}

	public Long getUniqueRecipients() {
		return uniqueRecipients;
	}

	public void setUniqueRecipients(Long uniqueRecipients) {
		this.uniqueRecipients = uniqueRecipients;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("RRUsageReportResponse [sentInfo=");
		builder.append(sentInfo);
		builder.append(", openInfo=");
		builder.append(openInfo);
		builder.append(", deliveredInfo=");
		builder.append(deliveredInfo);
		builder.append(", clickInfo=");
		builder.append(clickInfo);
		builder.append(", failureMessage=");
		builder.append(failureMessage);
		builder.append(", scheduledInfo=");
		builder.append(scheduledInfo);
		builder.append("]");
		return builder.toString();
	}
	
}
