package com.birdeye.campaign.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

@Entity
@Table(name = "campaign_trigger_filters")
public class CampaignTriggerFilters implements Serializable {
	
	private static final long	serialVersionUID	= 5037141690038180454L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id")
	private Integer				id;
	
	@Column(name = "filter_name", nullable = false)
	private String				filterName;
	
	@Column(name = "campaign_trigger_id")
	private Integer				campaignTriggerId;
	
	@Column(name = "filter_jsonpath_exp", nullable = false)
	private String				filterJsonpathExp;
	
	@Column(name = "filter_label", nullable = false)
	private String				filterLabel;
	
	@Column(name = "data_type", nullable = false)
	private String				dataType;
	
	@CreationTimestamp
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_at")
	private Date				createdAt;
	
	@UpdateTimestamp
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_at")
	private Date				updatedAt;
	
	public Integer getId() {
		return id;
	}
	
	public void setId(Integer id) {
		this.id = id;
	}
	
	public String getFilterName() {
		return filterName;
	}
	
	public void setFilterName(String filterName) {
		this.filterName = filterName;
	}
	
	public Integer getCampaignTriggerId() {
		return campaignTriggerId;
	}
	
	public void setCampaignTriggerId(Integer campaignTriggerId) {
		this.campaignTriggerId = campaignTriggerId;
	}
	
	public String getFilterJsonpathExp() {
		return filterJsonpathExp;
	}
	
	public void setFilterJsonpathExp(String filterJsonpathExp) {
		this.filterJsonpathExp = filterJsonpathExp;
	}
	
	public String getFilterLabel() {
		return filterLabel;
	}
	
	public void setFilterLabel(String filterLabel) {
		this.filterLabel = filterLabel;
	}
	
	public String getDataType() {
		return dataType;
	}
	
	public void setDataType(String dataType) {
		this.dataType = dataType;
	}
	
	public Date getCreatedAt() {
		return createdAt;
	}
	
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}
	
	public Date getUpdatedAt() {
		return updatedAt;
	}
	
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder sb = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return sb.toString();
	}
}
