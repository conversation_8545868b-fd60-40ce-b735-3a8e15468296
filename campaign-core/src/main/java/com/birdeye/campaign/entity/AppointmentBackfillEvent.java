package com.birdeye.campaign.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import com.birdeye.campaign.enums.AppointmentBackfillEventStatusEnum;

/**
 * 
 * <AUTHOR>
 * @since May 25, 2023
 * @implNote This entity class represents SQL DB table appointment_backfill_event which is used for scheduling appointment back-fill events due to
 *           campaign update.
 * 
 *           This code is copyright (c) BirdEye Software India Pvt. Ltd.
 * 			
 */

/**
 * 
 * @implSpec
 *           CREATE TABLE `appointment_backfill_event` (
 *           `id` SERIAL,
 *           `enterprise_id` INT NULL,
 *           `campaign_id` INT NOT NULL,
 *           `campaign_type` ENUM('appointment_reminder') NOT NULL,
 *           `schedule_time` BIGINT NOT NULL,
 *           `schedule_time_readable` TIMESTAMP NULL,
 *           `event_status` ENUM('INIT', 'CANCELED', 'INPROGRESS', 'SUCCESS', 'FAILED') NOT NULL,
 *           `failure_reason` VARCHAR(100) NULL,
 *           `records_processed` INT NULL,
 *           `processing_time` BIGINT(20) NULL,
 *           `retry_counter` INT NULL,
 *           `parent_id` BIGINT(20) NULL,
 *           `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
 *           `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
 *           PRIMARY KEY (`id`),
 *           KEY `idx_campaign_id` (`campaign_id`),
 *           KEY `idx_schedule_time` (`schedule_time`),
 *           KEY `idx_created_at` (`created_at`)
 *           ) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
 * 			
 */
@Entity
@Table(name = "appointment_backfill_event")
public class AppointmentBackfillEvent implements Serializable {
	
	private static final long					serialVersionUID	= -2754451363369803088L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id")
	private Long								id;
	
	@Column(name = "enterprise_id")
	private Integer								enterpriseId;
	
	@Column(name = "campaign_id")
	private Integer								campaignId;
	
	@Column(name = "campaign_type")
	private String								campaignType;
	
	@Column(name = "schedule_time")
	private Long								scheduleTime;
	
	@Column(name = "schedule_time_readable")
	@Temporal(TemporalType.TIMESTAMP)
	private Date								scheduleTimeReadable;
	
	@Column(name = "event_status")
	@Enumerated(EnumType.STRING)
	private AppointmentBackfillEventStatusEnum	eventStatus;
	
	@Column(name = "failure_reason")
	private String								failureReason;
	
	@Column(name = "records_processed")
	private Integer								recordsProcessed;
	
	@Column(name = "processing_time")
	private Long								processingTime;								// in millisecond
	
	@Column(name = "retry_counter")
	private Integer								retryCounter;
	
	@Column(name = "parent_id")
	private Long								parentId;
	
	@CreationTimestamp
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_at")
	private Date								createdAt;
	
	@UpdateTimestamp
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_at")
	private Date								updatedAt;
	
	/**
	 * Default constructor
	 */
	public AppointmentBackfillEvent() {
	}
	
	/**
	 * 
	 * @param enterpriseId
	 * @param campaignId
	 * @param campaignType
	 * @param eventStatus
	 * @param retryCounter
	 * @param parentId
	 */
	public AppointmentBackfillEvent(Integer enterpriseId, Integer campaignId, String campaignType, AppointmentBackfillEventStatusEnum eventStatus, Integer retryCounter,
			Long parentId) {
		this.enterpriseId = enterpriseId;
		this.campaignId = campaignId;
		this.campaignType = campaignType;
		this.eventStatus = eventStatus;
		this.retryCounter = retryCounter;
		this.parentId = parentId;
	}
	
	/**
	 * @return the id
	 */
	public Long getId() {
		return id;
	}
	
	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}
	
	/**
	 * @return the enterpriseId
	 */
	public Integer getEnterpriseId() {
		return enterpriseId;
	}
	
	/**
	 * @param enterpriseId
	 *            the enterpriseId to set
	 */
	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	
	/**
	 * @return the campaignId
	 */
	public Integer getCampaignId() {
		return campaignId;
	}
	
	/**
	 * @param campaignId
	 *            the campaignId to set
	 */
	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}
	
	/**
	 * @return the campaignType
	 */
	public String getCampaignType() {
		return campaignType;
	}
	
	/**
	 * @param campaignType
	 *            the campaignType to set
	 */
	public void setCampaignType(String campaignType) {
		this.campaignType = campaignType;
	}
	
	/**
	 * @return the scheduleTime
	 */
	public Long getScheduleTime() {
		return scheduleTime;
	}
	
	/**
	 * @param scheduleTime
	 *            the scheduleTime to set
	 */
	public void setScheduleTime(Long scheduleTime) {
		this.scheduleTime = scheduleTime;
	}
	
	/**
	 * @return the scheduleTimeReadable
	 */
	public Date getScheduleTimeReadable() {
		return scheduleTimeReadable;
	}
	
	/**
	 * @param scheduleTimeReadable
	 *            the scheduleTimeReadable to set
	 */
	public void setScheduleTimeReadable(Date scheduleTimeReadable) {
		this.scheduleTimeReadable = scheduleTimeReadable;
	}
	
	/**
	 * @return the eventStatus
	 */
	public AppointmentBackfillEventStatusEnum getEventStatus() {
		return eventStatus;
	}
	
	/**
	 * @param eventStatus
	 *            the eventStatus to set
	 */
	public void setEventStatus(AppointmentBackfillEventStatusEnum eventStatus) {
		this.eventStatus = eventStatus;
	}
	
	/**
	 * @return the failureReason
	 */
	public String getFailureReason() {
		return failureReason;
	}
	
	/**
	 * @param failureReason
	 *            the failureReason to set
	 */
	public void setFailureReason(String failureReason) {
		this.failureReason = failureReason;
	}
	
	/**
	 * @return the recordsProcessed
	 */
	public Integer getRecordsProcessed() {
		return recordsProcessed;
	}
	
	/**
	 * @param recordsProcessed
	 *            the recordsProcessed to set
	 */
	public void setRecordsProcessed(Integer recordsProcessed) {
		this.recordsProcessed = recordsProcessed;
	}
	
	/**
	 * @return the processingTime
	 */
	public Long getProcessingTime() {
		return processingTime;
	}
	
	/**
	 * @param processingTime
	 *            the processingTime to set
	 */
	public void setProcessingTime(Long processingTime) {
		this.processingTime = processingTime;
	}
	
	/**
	 * @return the retryCounter
	 */
	public Integer getRetryCounter() {
		return retryCounter;
	}
	
	/**
	 * @param retryCounter
	 *            the retryCounter to set
	 */
	public void setRetryCounter(Integer retryCounter) {
		this.retryCounter = retryCounter;
	}
	
	/**
	 * @return the parentId
	 */
	public Long getParentId() {
		return parentId;
	}
	
	/**
	 * @param parentId
	 *            the parentId to set
	 */
	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}
	
	/**
	 * @return the createdAt
	 */
	public Date getCreatedAt() {
		return createdAt;
	}
	
	/**
	 * @param createdAt
	 *            the createdAt to set
	 */
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}
	
	/**
	 * @return the updatedAt
	 */
	public Date getUpdatedAt() {
		return updatedAt;
	}
	
	/**
	 * @param updatedAt
	 *            the updatedAt to set
	 */
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
