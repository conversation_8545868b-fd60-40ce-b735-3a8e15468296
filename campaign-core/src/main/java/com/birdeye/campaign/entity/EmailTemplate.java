package com.birdeye.campaign.entity;

import java.io.Serializable;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import org.apache.commons.lang.builder.ReflectionToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "email_template")
public class EmailTemplate implements Serializable{
    private static final long serialVersionUID = 1L;
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id", nullable = false)
    private Integer id;
    
    @Basic(fetch = FetchType.LAZY)
    @Size(min = 1, max = 100)
    @Column(name = "name")
    private String name;

    @Basic(fetch = FetchType.LAZY)
    @Size(min = 1, max = 20000)
    @Column(name = "message")
    private String message;
    
    @Basic(fetch = FetchType.LAZY)
    @Size(min = 1, max = 20000)
    @Column(name = "sentiment_message")
    private String sentimentMessage;
    
    @Basic(fetch = FetchType.LAZY)
    @Size(min = 1, max = 20000)
    @Column(name = "star_message")
    private String starMessage;
    
    @Size(min = 1, max = 130)
    @Column(name = "subject")
    private String subject;

    @Size(min = 1, max = 100)
    @Column(name = "reminder_subject")
    private String reminderSubject;

    @Basic(optional = false, fetch = FetchType.EAGER)
    @Column(name = "is_default", nullable = false, length = 1)
    private String isDefault = "n";
    
    @Basic(optional = false)
    @Size(min = 1, max = 30)
    @Column(name = "type", nullable = false)
    private String type;
    
    @Column(name = "email_content")
    private String emailContent;
    
    @Column(name = "email_question")
    private String emailQuestion;
    
    @Column(name = "sentiment_heading")
    private String sentimentHeading;
    
    @Column(name = "star_heading")
    private String starHeading;
    
    @Column(name = "positive_button_label")
    private String positiveButtonLabel;
    
    @Column(name = "negative_button_label")
    private String negativeButtonLabel;
    
    @Column(name = "neutral_button_label")
    private String neutralButtonLabel;
    
    @Column(name = "recommend_page_heading")
    private String recommendPageHeading;
    
    @Column(name = "recommend_page_message")
    private String recommendPageMessage;
    
    @Column(name = "recommend_thank_page_message")
    private String recommendThankPageMessage;
    
    @Column(name = "recommend_thank_page_footer")
    private String recommendThankPageFooter;
    
    @Column(name = "non_recommend_page_message")
    private String nonRecommendPageMessage;
    
    @Column(name = "non_recommend_thank_page_message")
    private String nonRecommendThankPageMessage;
    
    @Column(name = "non_recommend_thank_page_footer")
    private String nonRecommendThankPageFooter;
    
    @Column(name = "priority_sources")
    private String prioritySources;
    
    @Column(name = "write_review_question")
    private String writeReviewQuestion;
    
    @Column(name = "write_review_neg_text")
    private String writeReviewNegativeText;
    
    @Column(name = "write_review_pos_text")
    private String writeReviewPositiveText;
    
    // equal or above rating is positive
    @Column(name = "positive_rating_threshold")
    private Integer positiveRatingThreshold;
    
    @Column(name = "contact_us_message")
    private String contactUsMessage;
    
    @Column(name = "contact_us_button_text")
    private String contactUsButtonText;
    
    @Column(name = "contact_us_button_text_color")
    private String contactUsButtonTextColor;
    
    @Column(name = "contact_us_button_color")
    private String contactUsButtonColor;
    
    @Column(name = "review_heading")
    private String reviewHeading;
    
    @Basic(fetch = FetchType.LAZY)
    @Size(min = 1, max = 20000)
    @Column(name = "review_message")
    private String reviewMessage;
    
    @Column(name = "review_site_button_color")
    private String reviewSiteButtonColor;
    
    @Column(name = "review_site_button_text_color")
    private String reviewSiteButtonTextColor;
    
    
    @Column(name = "feedback_message")
    private String feedbackMessage;
    
    @Column(name = "feedback_show_callback_option")
    private Integer feedbackShowCallbackOption;
    
    @Column(name = "feedback_default_checkbox_checked")
    private Integer feedbackDefaultCheckboxChecked;
    
    
    @Column(name = "thankyou_heading")
    private String thankyouHeading;
    
    @Column(name = "thankyou_Message")
    private String thankyouMessage;
    
    
    @Column(name = "signature")
    private String signature;
    
    @Column(name="media_urls")
	private String mediaUrls;         // comma separated attachment(image) path
	
	@Column(name="referral_question")
	private String referralQuestion;
	
	@Column(name="referral_options")
	private String referralOptions;
	
	@Column(name="referral_contact_option_enabled")
	private Integer referralContactOptionEnabled = 0;
	
	@Column(name = "label_configs")
	private String	labelConfigs;
	
	@Column(name = "email_category")
	private String	emailCategory;
    
    @Column(name = "ai_template_id")
    private Integer aiTemplateId;
    
    public EmailTemplate() {
    	
    }
    
    public EmailTemplate(EmailTemplate emailTemplate) {
        this.message = emailTemplate.getMessage();
        this.sentimentMessage = emailTemplate.getSentimentMessage();
        this.starMessage = emailTemplate.getStarMessage();
        this.subject = emailTemplate.getSubject();
        this.reminderSubject = emailTemplate.getReminderSubject();
        this.type = emailTemplate.getType();
        this.name = emailTemplate.getName();
        this.emailQuestion = emailTemplate.getEmailQuestion();
        this.sentimentHeading = emailTemplate.getSentimentHeading();
        this.starHeading = emailTemplate.getStarHeading();
        this.positiveButtonLabel = emailTemplate.getPositiveButtonLabel();
        this.negativeButtonLabel = emailTemplate.getNegativeButtonLabel();
        this.neutralButtonLabel = emailTemplate.getNeutralButtonLabel();
        this.recommendPageHeading = emailTemplate.getRecommendPageHeading();
        this.recommendPageMessage = emailTemplate.getRecommendPageMessage();
        this.nonRecommendPageMessage = emailTemplate.getNonRecommendPageMessage();
        this.recommendThankPageMessage = emailTemplate.getRecommendThankPageMessage();
        this.recommendThankPageFooter = emailTemplate.getRecommendThankPageFooter();
        this.nonRecommendThankPageMessage = emailTemplate.getNonRecommendThankPageMessage();
        this.nonRecommendThankPageFooter = emailTemplate.getNonRecommendThankPageFooter();
        this.reviewHeading = emailTemplate.getReviewHeading();
        this.reviewMessage = emailTemplate.getReviewMessage();
        this.emailContent = emailTemplate.getEmailContent();
        this.writeReviewQuestion = emailTemplate.getWriteReviewQuestion();
        this.writeReviewPositiveText = emailTemplate.getWriteReviewPositiveText();
        this.writeReviewNegativeText = emailTemplate.getWriteReviewNegativeText();
        this.positiveRatingThreshold = emailTemplate.getPositiveRatingThreshold();
        this.contactUsMessage = emailTemplate.getContactUsMessage();
        this.contactUsButtonText = emailTemplate.getContactUsButtonText();
        this.contactUsButtonTextColor = emailTemplate.getContactUsButtonTextColor();
        this.contactUsButtonColor = emailTemplate.getContactUsButtonColor();
        
        this.reviewSiteButtonColor = emailTemplate.getReviewSiteButtonColor();
        this.reviewSiteButtonTextColor = emailTemplate.getReviewSiteButtonTextColor();
        this.feedbackMessage = emailTemplate.getFeedbackMessage();
        this.feedbackShowCallbackOption = emailTemplate.getFeedbackShowCallbackOption();
        this.feedbackDefaultCheckboxChecked = emailTemplate.getFeedbackDefaultCheckboxChecked();
        this.thankyouHeading = emailTemplate.getThankyouHeading();
        this.thankyouMessage = emailTemplate.getThankyouMessage();
        this.signature = emailTemplate.getSignature();
        
        if(emailTemplate.getReminderSubject() != null) this.reminderSubject = emailTemplate.getReminderSubject();
        
		this.labelConfigs = emailTemplate.getLabelConfigs();
		this.emailCategory = emailTemplate.getEmailCategory();
    }
    
   
	public String getSignature() {
		return signature;
	}

	public void setSignature(String signature) {
		this.signature = signature;
	}

	public String getWriteReviewQuestion() {
		return writeReviewQuestion;
	}

	public void setWriteReviewQuestion(String writeReviewQuestion) {
		this.writeReviewQuestion = writeReviewQuestion;
	}

	public String getWriteReviewNegativeText() {
		return writeReviewNegativeText;
	}

	public void setWriteReviewNegativeText(String writeReviewNegativeText) {
		this.writeReviewNegativeText = writeReviewNegativeText;
	}

	public String getWriteReviewPositiveText() {
		return writeReviewPositiveText;
	}

	public void setWriteReviewPositiveText(String writeReviewPositiveText) {
		this.writeReviewPositiveText = writeReviewPositiveText;
	}

	public Integer getPositiveRatingThreshold() {
		return positiveRatingThreshold;
	}

	public void setPositiveRatingThreshold(Integer positiveRatingThreshold) {
		this.positiveRatingThreshold = positiveRatingThreshold;
	}

	public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    /**
      * To String implementation for logging the entities.
      * @return the toString 
      */
    public String entityLog() {
        return "EmailTemplate{" + "id=" + id + ", message=" + message + ", subject=" + subject + ", isDefault=" + isDefault + '}';
    }
	

    /**
     * @return the isDefault
     */
    public String getIsDefault() {
        return isDefault;
    }

    /**
     * @param isDefault the isDefault to set
     */
    public void setIsDefault(String isDefault) {
        this.isDefault = isDefault;
    }

    /**
     * @return the type
     */
    public String getType() {
        return type;
    }

    /**
     * @param type the type to set
     */
    public void setType(String type) {
        this.type = type;
    }

    public String getReminderSubject() {
        return reminderSubject;
    }

    public void setReminderSubject(String reminderSubject) {
        this.reminderSubject = reminderSubject;
    }

    /**
     * @return the emailContent
     */
    public String getEmailContent() {
        return emailContent;
    }

    /**
     * @param emailContent the emailContent to set
     */
    public void setEmailContent(String emailContent) {
        this.emailContent = emailContent;
    }

    public String getEmailQuestion() {
        return emailQuestion;
    }

    public void setEmailQuestion(String emailQuestion) {
        this.emailQuestion = emailQuestion;
    }
    
    /**
	 * @return the sentimentHeading
	 */
	public String getSentimentHeading() {
		return sentimentHeading;
	}

	/**
	 * @param sentimentHeading the sentimentHeading to set
	 */
	public void setSentimentHeading(String sentimentHeading) {
		this.sentimentHeading = sentimentHeading;
	}

	/**
	 * @return the starHeading
	 */
	public String getStarHeading() {
		return starHeading;
	}

	/**
	 * @param starHeading the starHeading to set
	 */
	public void setStarHeading(String starHeading) {
		this.starHeading = starHeading;
	}
	
	/**
	 * @return the sentimentMessage
	 */
	public String getSentimentMessage() {
		return sentimentMessage;
	}

	/**
	 * @param sentimentMessage the sentimentMessage to set
	 */
	public void setSentimentMessage(String sentimentMessage) {
		this.sentimentMessage = sentimentMessage;
	}

	/**
	 * @return the starMessage
	 */
	public String getStarMessage() {
		return starMessage;
	}

	/**
	 * @param starMessage the starMessage to set
	 */
	public void setStarMessage(String starMessage) {
		this.starMessage = starMessage;
	}

	public String getPositiveButtonLabel() {
        return positiveButtonLabel;
    }

    public void setPositiveButtonLabel(String positiveButtonLabel) {
        this.positiveButtonLabel = positiveButtonLabel;
    }

    public String getNegativeButtonLabel() {
        return negativeButtonLabel;
    }

    public void setNegativeButtonLabel(String negativeButtonLabel) {
        this.negativeButtonLabel = negativeButtonLabel;
    }

    public String getRecommendPageHeading() {
        return recommendPageHeading;
    }

    public void setRecommendPageHeading(String recommendPageHeading) {
        this.recommendPageHeading = recommendPageHeading;
    }

    public String getRecommendPageMessage() {
        return recommendPageMessage;
    }

    public void setRecommendPageMessage(String recommendPageMessage) {
        this.recommendPageMessage = recommendPageMessage;
    }

    public String getNonRecommendPageMessage() {
        return nonRecommendPageMessage;
    }

    public void setNonRecommendPageMessage(String nonRecommendPageMessage) {
        this.nonRecommendPageMessage = nonRecommendPageMessage;
    }

    public String getPrioritySources() {
        return prioritySources;
    }

    public void setPrioritySources(String prioritySources) {
        this.prioritySources = prioritySources;
    }

    /**
     * @return the name
     */
    public String getName() {
        return name;
    }

    /**
     * @param name the name to set
     */
    public void setName(String name) {
        this.name = name;
    }

    public String getRecommendThankPageMessage() {
        return recommendThankPageMessage;
    }

    public void setRecommendThankPageMessage(String recommendThankPageMessage) {
        this.recommendThankPageMessage = recommendThankPageMessage;
    }

    public String getRecommendThankPageFooter() {
        return recommendThankPageFooter;
    }

    public void setRecommendThankPageFooter(String recommendThankPageFooter) {
        this.recommendThankPageFooter = recommendThankPageFooter;
    }

    public String getNonRecommendThankPageMessage() {
        return nonRecommendThankPageMessage;
    }

    public void setNonRecommendThankPageMessage(String nonRecommendThankPageMessage) {
        this.nonRecommendThankPageMessage = nonRecommendThankPageMessage;
    }

    public String getNonRecommendThankPageFooter() {
        return nonRecommendThankPageFooter;
    }

    public void setNonRecommendThankPageFooter(String nonRecommendThankPageFooter) {
        this.nonRecommendThankPageFooter = nonRecommendThankPageFooter;
    }

	@Override
	public String toString(){
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.DEFAULT_STYLE);
		return b.toString();
	}

	/**
	 * @return the contactUsMessage
	 */
	public String getContactUsMessage() {
		return contactUsMessage;
	}

	/**
	 * @param contactUsMessage the contactUsMessage to set
	 */
	public void setContactUsMessage(String contactUsMessage) {
		this.contactUsMessage = contactUsMessage;
	}

	/**
	 * @return the contactUsButtonText
	 */
	public String getContactUsButtonText() {
		return contactUsButtonText;
	}

	/**
	 * @param contactUsButtonText the contactUsButtonText to set
	 */
	public void setContactUsButtonText(String contactUsButtonText) {
		this.contactUsButtonText = contactUsButtonText;
	}

	/**
	 * @return the contactUsButtonTextColor
	 */
	public String getContactUsButtonTextColor() {
		return contactUsButtonTextColor;
	}

	/**
	 * @param contactUsButtonTextColor the contactUsButtonTextColor to set
	 */
	public void setContactUsButtonTextColor(String contactUsButtonTextColor) {
		this.contactUsButtonTextColor = contactUsButtonTextColor;
	}

	/**
	 * @return the contactUsButtonColor
	 */
	public String getContactUsButtonColor() {
		return contactUsButtonColor;
	}

	/**
	 * @param contactUsButtonColor the contactUsButtonColor to set
	 */
	public void setContactUsButtonColor(String contactUsButtonColor) {
		this.contactUsButtonColor = contactUsButtonColor;
	}

	/**
	 * @return the neutralButtonLabel
	 */
	public String getNeutralButtonLabel() {
		return neutralButtonLabel;
	}

	/**
	 * @param neutralButtonLabel the neutralButtonLabel to set
	 */
	public void setNeutralButtonLabel(String neutralButtonLabel) {
		this.neutralButtonLabel = neutralButtonLabel;
	}

	/**
	 * @return the reviewHeading
	 */
	public String getReviewHeading() {
		return reviewHeading;
	}

	/**
	 * @param reviewHeading the reviewHeading to set
	 */
	public void setReviewHeading(String reviewHeading) {
		this.reviewHeading = reviewHeading;
	}

	/**
	 * @return the reviewMessage
	 */
	public String getReviewMessage() {
		return reviewMessage;
	}

	/**
	 * @param reviewMessage the reviewMessage to set
	 */
	public void setReviewMessage(String reviewMessage) {
		this.reviewMessage = reviewMessage;
	}

	public String getReviewSiteButtonColor() {
		return reviewSiteButtonColor;
	}

	public void setReviewSiteButtonColor(String reviewSiteButtonColor) {
		this.reviewSiteButtonColor = reviewSiteButtonColor;
	}

	public String getReviewSiteButtonTextColor() {
		return reviewSiteButtonTextColor;
	}

	public void setReviewSiteButtonTextColor(String reviewSiteButtonTextColor) {
		this.reviewSiteButtonTextColor = reviewSiteButtonTextColor;
	}

	public String getFeedbackMessage() {
		return feedbackMessage;
	}

	public void setFeedbackMessage(String feedbackMessage) {
		this.feedbackMessage = feedbackMessage;
	}

	public Integer getFeedbackShowCallbackOption() {
		return feedbackShowCallbackOption;
	}

	public void setFeedbackShowCallbackOption(Integer feedbackShowCallbackOption) {
		this.feedbackShowCallbackOption = feedbackShowCallbackOption;
	}

	public Integer getFeedbackDefaultCheckboxChecked() {
		return feedbackDefaultCheckboxChecked;
	}

	public void setFeedbackDefaultCheckboxChecked(Integer feedbackDefaultCheckboxChecked) {
		this.feedbackDefaultCheckboxChecked = feedbackDefaultCheckboxChecked;
	}

	public String getThankyouHeading() {
		return thankyouHeading;
	}

	public void setThankyouHeading(String thankyouHeading) {
		this.thankyouHeading = thankyouHeading;
	}

	public String getThankyouMessage() {
		return thankyouMessage;
	}

	public void setThankyouMessage(String thankyouMessage) {
		this.thankyouMessage = thankyouMessage;
	}
	
	public String getMediaUrls() {
		return mediaUrls;
	}

	public void setMediaUrls(String mediaUrls) {
		this.mediaUrls = mediaUrls;
	}

	public String getReferralQuestion() {
		return referralQuestion;
	}

	public void setReferralQuestion(String referralQuestion) {
		this.referralQuestion = referralQuestion;
	}

	public String getReferralOptions() {
		return referralOptions;
	}

	public void setReferralOptions(String referralOptions) {
		this.referralOptions = referralOptions;
	}

	public Integer getReferralContactOptionEnabled() {
		return referralContactOptionEnabled;
	}

	public void setReferralContactOptionEnabled(Integer referralContactOptionEnabled) {
		this.referralContactOptionEnabled = referralContactOptionEnabled;
	}

	/**
	 * @return the labelConfigs
	 */
	public String getLabelConfigs() {
		return labelConfigs;
	}

	/**
	 * @param labelConfigs the labelConfigs to set
	 */
	public void setLabelConfigs(String labelConfigs) {
		this.labelConfigs = labelConfigs;
	}

	/**
	 * @return the emailCategory
	 */
	public String getEmailCategory() {
		return emailCategory;
	}

	/**
	 * @param emailCategory the emailCategory to set
	 */
	public void setEmailCategory(String emailCategory) {
		this.emailCategory = emailCategory;
	}

    public Integer getAiTemplateId() {
        return aiTemplateId;
    }

    public void setAiTemplateId(Integer aiTemplateId) {
        this.aiTemplateId = aiTemplateId;
    }
}
