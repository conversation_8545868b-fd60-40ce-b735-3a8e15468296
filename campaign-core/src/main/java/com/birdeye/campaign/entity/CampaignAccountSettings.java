package com.birdeye.campaign.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

@Entity
@Table(name = "campaign_account_settings")
public class CampaignAccountSettings implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1565615528780119860L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	@Column(name = "account_id")
	private Integer accountId;

	@Column(name = "mail_resend_frequency")
	private Integer mailResendFrequency;

	@Column(name = "is_direct_google_reviews")
	private Integer isDirectGoogleReviews;
	
	@Column(name = "campaign_customer_limit")
	private Integer campaignCustomerLimit;

	@Column(name = "survey_comm_frequency")
	private Integer surveyCommFrequency;
	@Column(name = "review_comm_frequency")
	private Integer reviewCommFrequency;
	@Column(name = "referral_comm_frequency")
	private Integer referralCommFrequency;
	@Column(name = "promotion_comm_frequency")
	private Integer promotionCommFrequency;
	@Column(name = "cx_comm_frequency")
	private Integer cxCommFrequency;
	@Column(name = "restriction_at_account_level")
	private Integer restrictionAtAccountLevel;
	@Column(name = "restriction_on_campaign_type")
	private String restrictionOnCampaignType;
	@Column(name = "is_comm_restriction_enabled")
	private Integer isCommRestrictionEnabled;
	
	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created")
	@CreationTimestamp
	private Date created;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated")
	@UpdateTimestamp
	private Date updated;

	@Column(name = "survey_restriction_scope")
	private Integer surveyRestrictionScope;

	public CampaignAccountSettings() {

	}


	public Integer getSurveyCommFrequency() {
		return surveyCommFrequency;
	}

	public void setSurveyCommFrequency(Integer surveyCommFrequency) {
		this.surveyCommFrequency = surveyCommFrequency;
	}

	public Integer getReviewCommFrequency() {
		return reviewCommFrequency;
	}

	public void setReviewCommFrequency(Integer reviewCommFrequency) {
		this.reviewCommFrequency = reviewCommFrequency;
	}

	public Integer getReferralCommFrequency() {
		return referralCommFrequency;
	}

	public void setReferralCommFrequency(Integer referralCommFrequency) {
		this.referralCommFrequency = referralCommFrequency;
	}

	public Integer getPromotionCommFrequency() {
		return promotionCommFrequency;
	}

	public void setPromotionCommFrequency(Integer promotionCommFrequency) {
		this.promotionCommFrequency = promotionCommFrequency;
	}

	public Integer getCxCommFrequency() {
		return cxCommFrequency;
	}

	public void setCxCommFrequency(Integer cxCommFrequency) {
		this.cxCommFrequency = cxCommFrequency;
	}

	public Integer getRestrictionAtAccountLevel() {
		return restrictionAtAccountLevel;
	}

	public void setRestrictionAtAccountLevel(Integer restrictionAtAccountLevel) {
		this.restrictionAtAccountLevel = restrictionAtAccountLevel;
	}

	public String getRestrictionOnCampaignType() {
		return restrictionOnCampaignType;
	}

	public void setRestrictionOnCampaignType(String restrictionOnCampaignType) {
		this.restrictionOnCampaignType = restrictionOnCampaignType;
	}

	public Integer getIsCommRestrictionEnabled() {
		return isCommRestrictionEnabled;
	}

	public void setIsCommRestrictionEnabled(Integer isCommRestrictionEnabled) {
		this.isCommRestrictionEnabled = isCommRestrictionEnabled;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getAccountId() {
		return accountId;
	}

	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}

	public Integer getMailResendFrequency() {
		return mailResendFrequency;
	}

	public void setMailResendFrequency(Integer mailResendFrequency) {
		this.mailResendFrequency = mailResendFrequency;
	}

	public Integer getIsDirectGoogleReviews() {
		return isDirectGoogleReviews;
	}

	public void setIsDirectGoogleReviews(Integer isDirectGoogleReviews) {
		this.isDirectGoogleReviews = isDirectGoogleReviews;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getCreated() {
		return created;
	}

	public void setCreated(Date created) {
		this.created = created;
	}

	public Date getUpdated() {
		return updated;
	}

	public void setUpdated(Date updated) {
		this.updated = updated;
	}

	public Integer getCampaignCustomerLimit() {
		return campaignCustomerLimit;
	}

	public Integer getSurveyRestrictionScope() {
		return surveyRestrictionScope;
	}

	public void setSurveyRestrictionScope(Integer surveyRestrictionScope) {
		this.surveyRestrictionScope = surveyRestrictionScope;
	}

	public void setCampaignCustomerLimit(Integer campaignCustomerLimit) {
		this.campaignCustomerLimit = campaignCustomerLimit;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}

}
