package com.birdeye.campaign.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
@Entity
@Table(name = "appointment")
public class Appointment implements Serializable {
	
	private static final long	serialVersionUID	= 1305385188896511401L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id", nullable = false)
	private Integer				id;
	
	@Column(name = "name")
	private String				name;
	
	@Column(name = "phone_number")
	private String				phoneNumber;
	
	@Column(name = "email")
	private String				email;
	
	@Column(name = "campaign_id")
	private Integer				campaignId;
	
	@Column(name = "review_request_id")
	private Long				reviewRequestId;
	
	@Column(name = "referrer_id")
	private Integer				referrerId;
	
	@Column(name = "business_id")
	private Integer				businessId;
	
	@Column(name = "enterprise_id")
	private Integer				enterpriseId;
	
	@Column(name = "description")
	private String				description;
	
	@Column(name = "channel_id")
	private Integer				channelId;
	
	@Column(name = "device_type")
	private String				deviceType;
	
	// snapshot of the referrer name as sorting to be provided on this
	@Column(name = "referrer_name")
	private String	referrerName;
	
	@Column(name = "ecid")
	private Integer	ecid;
	
	@Column(name = "cid")
	private Integer	cid;
	
	@CreationTimestamp
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_at")
	private Date	createdAt;
	
	@UpdateTimestamp
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_at")
	private Date	updatedAt;
	
	// date on which thank-you note was sent to the referrer - null if note not sent yet
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ty_note_sent_time")
	private Date	tyNoteSentTime;
	
	@Column(name = "custom_fields") // map/json of custom field attributes like pincode for tmx. Map<String,Object> similar to Nexus CommunicationSMSAudit->params
	private String	customFields;
	
	@Column(name = "referral_message")
	private String	referralMessage;
	
	@Column(name = "is_suspect")
	private Integer	isSuspect;
	
	@Column(name = "is_prospect")
	private Integer	isProspect;
	
	@Column(name = "referral_code")
	private String	referralCode;
	
	@Column(name = "referrer_code")
	private String	referrerCode;
	
	@Column(name = "referrer_email")
	private String	referrerEmail;
	
	@Column(name = "referrer_phone_number")
	private String	referrerPhoneNumber;
	
	@Column(name = "referrer_ecid")
	private Integer	referrerEcid;
	
	// Ideally same value for location name of lead as referrals are at location level at present
	@Column(name = "referrer_location_name")
	private String referrerLocationName;
	
	public Integer getId() {
		return id;
	}
	
	public Integer getChannelId() {
		return channelId;
	}
	
	public void setChannelId(Integer channelId) {
		this.channelId = channelId;
	}
	
	public void setId(Integer id) {
		this.id = id;
	}
	
	public String getName() {
		return name;
	}
	
	public void setName(String name) {
		this.name = name;
	}
	
	public String getPhoneNumber() {
		return phoneNumber;
	}
	
	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}
	
	public String getEmail() {
		return email;
	}
	
	public void setEmail(String email) {
		this.email = email;
	}
	
	public Integer getCampaignId() {
		return campaignId;
	}
	
	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}
	
	public Long getReviewRequestId() {
		return reviewRequestId;
	}
	
	public void setReviewRequestId(Long reviewRequestId) {
		this.reviewRequestId = reviewRequestId;
	}
	
	public Integer getBusinessId() {
		return businessId;
	}
	
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	public Integer getEnterpriseId() {
		return enterpriseId;
	}
	
	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	
	public Date getCreatedAt() {
		return createdAt;
	}
	
	public String getReferrerName() {
		return referrerName;
	}
	
	public void setReferrerName(String referrerName) {
		this.referrerName = referrerName;
	}
	
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}
	
	public Integer getReferrerId() {
		return referrerId;
	}
	
	public void setReferrerId(Integer referrerId) {
		this.referrerId = referrerId;
	}
	
	public String getDescription() {
		return description;
	}
	
	public void setDescription(String description) {
		this.description = description;
	}
	
	public Date getUpdatedAt() {
		return updatedAt;
	}
	
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}
	
	public String getDeviceType() {
		return deviceType;
	}
	
	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}
	
	/**
	 * @return the ecid
	 */
	public Integer getEcid() {
		return ecid;
	}
	
	/**
	 * @param ecid
	 *            the ecid to set
	 */
	public void setEcid(Integer ecid) {
		this.ecid = ecid;
	}
	
	public Integer getCid() {
		return cid;
	}
	
	public void setCid(Integer cid) {
		this.cid = cid;
	}
	
	public Date getTyNoteSentTime() {
		return tyNoteSentTime;
	}
	
	public void setTyNoteSentTime(Date tyNoteSentTime) {
		this.tyNoteSentTime = tyNoteSentTime;
	}
	
	/**
	 * @return the customFields
	 */
	public String getCustomFields() {
		return customFields;
	}
	
	/**
	 * @param customFields
	 *            the customFields to set
	 */
	public void setCustomFields(String customFields) {
		this.customFields = customFields;
	}
	
	/**
	 * 
	 * @return referralMessage
	 */
	public String getReferralMessage() {
		return referralMessage;
	}
	
	/**
	 * 
	 * @param referralMessage
	 */
	public void setReferralMessage(String referralMessage) {
		this.referralMessage = referralMessage;
	}
	
	/**
	 * 
	 * @return isSuspect
	 */
	public Integer getIsSuspect() {
		return isSuspect;
	}
	
	/**
	 * 
	 * @param isSuspect
	 */
	public void setIsSuspect(Integer isSuspect) {
		this.isSuspect = isSuspect;
	}
	
	/**
	 * 
	 * @return isProspect
	 */
	public Integer getIsProspect() {
		return isProspect;
	}
	
	/**
	 * 
	 * @param isProspect
	 */
	public void setIsProspect(Integer isProspect) {
		this.isProspect = isProspect;
	}
	
	/**
	 * @return referralCode
	 */
	public String getReferralCode() {
		return referralCode;
	}
	
	/**
	 * @param referralCode
	 */
	public void setReferralCode(String referralCode) {
		this.referralCode = referralCode;
	}
	
	/**
	 * @return referrerCode
	 */
	public String getReferrerCode() {
		return referrerCode;
	}
	
	/**
	 * @param referrerCode
	 *            referrerCode to be set
	 */
	public void setReferrerCode(String referrerCode) {
		this.referrerCode = referrerCode;
	}
	
	/**
	 * @return referrerEmail
	 */
	public String getReferrerEmail() {
		return referrerEmail;
	}
	
	/**
	 * @param referrerEmail
	 *            referrerEmail to be set
	 */
	public void setReferrerEmail(String referrerEmail) {
		this.referrerEmail = referrerEmail;
	}
	
	/**
	 * @return referrerPhoneNumber
	 */
	public String getReferrerPhoneNumber() {
		return referrerPhoneNumber;
	}
	
	/**
	 * @param referrerPhoneNumber
	 *            referrerPhoneNumber to be set
	 */
	public void setReferrerPhoneNumber(String referrerPhoneNumber) {
		this.referrerPhoneNumber = referrerPhoneNumber;
	}
	
	/**
	 * @return referrerEcid
	 */
	public Integer getReferrerEcid() {
		return referrerEcid;
	}
	
	/**
	 * @param referrerEcid
	 *            referrerEcid to be set
	 */
	public void setReferrerEcid(Integer referrerEcid) {
		this.referrerEcid = referrerEcid;
	}
	
	/**
	 * @return referrerLocationName
	 */
	public String getReferrerLocationName() {
		return referrerLocationName;
	}
	
	/**
	 * @param referrerLocationName
	 *            referrerLocationName to be set
	 */
	public void setReferrerLocationName(String referrerLocationName) {
		this.referrerLocationName = referrerLocationName;
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return b.toString();
	}
	
}