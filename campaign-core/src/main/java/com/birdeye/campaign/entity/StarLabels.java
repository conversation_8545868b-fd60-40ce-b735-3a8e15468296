package com.birdeye.campaign.entity;

import javax.persistence.*;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "star_labels")
public class StarLabels {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "business_template_id")
    private Integer				businessTemplateId;

    @Column(name = "starlabels")
    private String starLabels;

    @Column(name = "label_order")
    private Integer labelOrder;

    public StarLabels() {
    }

    public StarLabels(Integer id, Integer businessTemplateId, String starLabels, Integer labelOrder) {
        this.id = id;
        this.businessTemplateId = businessTemplateId;
        this.starLabels = starLabels;
        this.labelOrder = labelOrder;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getBusinessTemplateId() {
        return businessTemplateId;
    }

    public void setBusinessTemplateId(Integer businessTemplateId) {
        this.businessTemplateId = businessTemplateId;
    }

    public String getStarLabels() {
        return starLabels;
    }

    public void setStarLabels(String starLabels) {
        this.starLabels = starLabels;
    }

    public Integer getLabelOrder() {
        return labelOrder;
    }

    public void setLabelOrder(Integer labelOrder) {
        this.labelOrder = labelOrder;
    }
}
