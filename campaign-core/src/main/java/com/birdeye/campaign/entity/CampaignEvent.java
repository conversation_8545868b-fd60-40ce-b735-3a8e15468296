package com.birdeye.campaign.entity;

import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * @file_name CampaignEvent.java
 * @created_date 12 Mar 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

/**
 * CREATE TABLE `campaign_event` (
 * `id` int(11) NOT NULL AUTO_INCREMENT,
 * `business_id` int(11) NOT NULL,
 * `enterprise_id` int(11) NOT NULL,
 * `review_request_id` int(11) NOT NULL,
 * `campaign_id` int(45) NOT NULL,
 * `template_id` int(11) NOT NULL,
 * `category` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'email/sms',
 * `type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'review_request_new/customer_experience/survey/promotional',
 * `campaign_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'ongoing/instant/drip',
 * `schedule_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
 * `rr_actual_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
 * `processed_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
 * `is_dnd` tinyint(1) NOT NULL DEFAULT '0',
 * `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
 * `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
 * PRIMARY KEY (`id`),
 * UNIQUE KEY `review_request_id_UNIQUE` (`review_request_id`)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
 * 
 * 
 * @file_name CampaignEvent.java
 * @created_date 12 Mar 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@Entity
@Table(name = "campaign_event")
public class CampaignEvent {
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id")
	private Integer	id;
	
	@Column(name = "business_id")
	private Integer	businessId;
	
	@Column(name = "enterprise_id")
	private Integer	enterpriseId;
	
	@Column(name = "review_request_id")
	private Long	reviewRequestId;
	
	@Column(name = "campaign_id")
	private Integer	campaignId;
	
	@Column(name = "template_id")
	private Integer	templateId;
	
	@Column(name = "category")
	private String	category;				//email/sms
	
	@Column(name = "type")
	private String	type;					//review_request_new/customer_experience/survey/promotional
	
	@Column(name = "campaign_type")
	private String	campaignType;			//instant/drip/ongoing
	
	@Column(name = "rr_actual_time")
	@Temporal(TemporalType.TIMESTAMP)
	private Date	rrActualTime;			//Time at which review request was sent/scheduled for, irrespective of DND period
	
	@Column(name = "scheduled_time")
	@Temporal(TemporalType.TIMESTAMP) //Tweaked time wer @rrActualTime, takes care of dnd period
	private Date	scheduledTime;			// scheduledTime = rrActualTime + dndHours left (takes care of different time zones)
	
	@Column(name = "processed_time")
	@Temporal(TemporalType.TIMESTAMP)
	private Date	processedTime;			//Time at which review request was sent/scheduled for, irrespective of DND period
	
	@Column(name = "is_dnd")
	private Integer	isDND		= 0;
	
	@Column(name = "is_reminder")
	private Integer	isReminder		= 0;
	
	@Column(name = "num_attempts")
	private Integer	numAttempts	= 0;
	
	@CreationTimestamp
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_at")
	private Date	createdAt;
	
	@UpdateTimestamp
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_at")
	private Date	updatedAt;
	
	@Column(name = "status")
	private String	status		= "INIT";	//INIT/INPROGRESS/SUCCESS/FAILED
	
	@Column(name = "failure_reason")
	private String	failureReason;
	
	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}
	
	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	
	/**
	 * @return the businessId
	 */
	public Integer getBusinessId() {
		return businessId;
	}
	
	/**
	 * @param businessId
	 *            the businessId to set
	 */
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	/**
	 * @return the enterprise_id
	 */
	public Integer getEnterpriseId() {
		return enterpriseId;
	}
	
	/**
	 * @param enterprise_id
	 *            the enterprise_id to set
	 */
	public void setEnterpriseId(Integer enterprise_id) {
		this.enterpriseId = enterprise_id;
	}
	
	/**
	 * @return the reviewRequestId
	 */
	public Long getReviewRequestId() {
		return reviewRequestId;
	}
	
	/**
	 * @param reviewRequestId
	 *            the reviewRequestId to set
	 */
	public void setReviewRequestId(Long reviewRequestId) {
		this.reviewRequestId = reviewRequestId;
	}
	
	/**
	 * @return the campaignId
	 */
	public Integer getCampaignId() {
		return campaignId;
	}
	
	/**
	 * @param campaignId
	 *            the campaignId to set
	 */
	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}
	
	/**
	 * @return the templateId
	 */
	public Integer getTemplateId() {
		return templateId;
	}
	
	/**
	 * @param templateId
	 *            the templateId to set
	 */
	public void setTemplateId(Integer templateId) {
		this.templateId = templateId;
	}
	
	/**
	 * @return the category
	 */
	public String getCategory() {
		return category;
	}
	
	/**
	 * @param category
	 *            the category to set
	 */
	public void setCategory(String category) {
		this.category = category;
	}
	
	/**
	 * @return the type
	 */
	public String getType() {
		return type;
	}
	
	/**
	 * @param type
	 *            the type to set
	 */
	public void setType(String type) {
		this.type = type;
	}
	
	/**
	 * @return the campaignType
	 */
	public String getCampaignType() {
		return campaignType;
	}
	
	/**
	 * @param campaignType
	 *            the campaignType to set
	 */
	public void setCampaignType(String campaignType) {
		this.campaignType = campaignType;
	}
	
	/**
	 * @return the rrActualTime
	 */
	public Date getRrActualTime() {
		return rrActualTime;
	}
	
	/**
	 * @param rrActualTime
	 *            the rrActualTime to set
	 */
	public void setRrActualTime(Date rrActualTime) {
		this.rrActualTime = rrActualTime;
	}
	
	/**
	 * @return the scheduledTime
	 */
	public Date getScheduledTime() {
		return scheduledTime;
	}
	
	/**
	 * @param scheduledTime
	 *            the scheduledTime to set
	 */
	public void setScheduledTime(Date scheduledTime) {
		this.scheduledTime = scheduledTime;
	}
	
	/**
	 * @return the processed_time
	 */
	public Date getProcessedTime() {
		return processedTime;
	}
	
	/**
	 * @param processed_time
	 *            the processed_time to set
	 */
	public void setProcessedTime(Date processedTime) {
		this.processedTime = processedTime;
	}
	
	/**
	 * @return the isDND
	 */
	public Integer getIsDND() {
		return isDND;
	}
	
	/**
	 * @param isDND
	 *            the isDND to set
	 */
	public void setIsDND(Integer isDND) {
		this.isDND = isDND;
	}
	
	/**
	 * @return the retryCount
	 */
	public Integer getNumAttempts() {
		return numAttempts;
	}
	
	/**
	 * @param retryCount
	 *            the retryCount to set
	 */
	public void setNumAttempts(Integer retryCount) {
		this.numAttempts = retryCount;
	}
	
	/**
	 * @return the createdAt
	 */
	public Date getCreatedAt() {
		return createdAt;
	}
	
	/**
	 * @param createdAt
	 *            the createdAt to set
	 */
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}
	
	/**
	 * @return the updatedAt
	 */
	public Date getUpdatedAt() {
		return updatedAt;
	}
	
	/**
	 * @param updatedAt
	 *            the updatedAt to set
	 */
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}
	
	/**
	 * @return the status
	 */
	public String getStatus() {
		return status;
	}
	
	/**
	 * @param status
	 *            the status to set
	 */
	public void setStatus(String status) {
		this.status = status;
	}
	
	/**
	 * @return the failureReason
	 */
	public String getFailureReason() {
		return failureReason;
	}
	
	/**
	 * @param failureReason
	 *            the failureReason to set
	 */
	public void setFailureReason(String failureReason) {
		this.failureReason = failureReason;
	}

	/**
	 * @return the isReminder
	 */
	public Integer getIsReminder() {
		return isReminder;
	}

	/**
	 * @param isReminder the isReminder to set
	 */
	public void setIsReminder(Integer isReminder) {
		this.isReminder = isReminder;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this,ToStringStyle.JSON_STYLE);
	}
	
	
	
}
