package com.birdeye.campaign.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

@Entity
@Table(name = "account_global_template_mapping")
public class AccountGlobalTemplateMapping implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 4679118615403695710L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer				id;
	
	@Column(name = "account_id")
	private Integer				accountId;
	
	@Column(name = "global_temp_id")
	private Integer				globalTemplateId;
	
	@Column(name = "sms_template_id")
	private Integer				smsTemplateId;
	
	@Column(name = "is_deleted")
	private Integer				isDeleted = 0;
	
	@Column(name = "email_template_id")
	private Integer				emailTemplateId;
	
	@Column(name = "created_at")
	@Temporal(TemporalType.TIMESTAMP)
	@CreationTimestamp
	private Date				createdAt;
	
	@Column(name = "updated_at")
	@Temporal(TemporalType.TIMESTAMP)
	@UpdateTimestamp
	private Date				updatedAt;
	
	//Default Constructor
	public AccountGlobalTemplateMapping() {
		
	}
	
	//Parameterized Constructor
	public AccountGlobalTemplateMapping(Integer accountId, Integer globalTemplateId, Integer smsTemplateId, Integer emailTemplateId) {
		this.accountId = accountId;
		this.globalTemplateId = globalTemplateId;
		this.smsTemplateId = smsTemplateId;
		this.emailTemplateId = emailTemplateId;
	}
	
	
	//Getters & Setters
	public Integer getId() {
		return id;
	}
	
	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getAccountId() {
		return accountId;
	}
	
	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}
	
	public Integer getGlobalTemplateId() {
		return globalTemplateId;
	}
	
	public void setGlobalTemplateId(Integer globalTemplateId) {
		this.globalTemplateId = globalTemplateId;
	}
	
	public Integer getSmsTemplateId() {
		return smsTemplateId;
	}
	
	public void setSmsTemplateId(Integer smsTemplateId) {
		this.smsTemplateId = smsTemplateId;
	}
	
	public Integer getIsDeleted() {
		return isDeleted;
	}
	
	public void setIsDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted==null? 0: isDeleted;
	}
	
	public Integer getEmailTemplateId() {
		return emailTemplateId;
	}
	
	public void setEmailTemplateId(Integer emailTemplateId) {
		this.emailTemplateId = emailTemplateId;
	}
	
	public Date getCreatedAt() {
		return createdAt;
	}
	
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}
	
	public Date getUpdatedAt() {
		return updatedAt;
	}
	
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
