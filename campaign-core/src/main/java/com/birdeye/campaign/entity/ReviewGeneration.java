package com.birdeye.campaign.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

@Entity
@Table(name = "review_generation")
public class ReviewGeneration implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id")
	private Integer id;

	@Column(name = "source_id")
	private Integer sourceId;

	@Column(name = "review_gen_source_id")
	private Integer reviewGenerationSourceId;

	@Size(min = 1, max = 1000)
	@Column(name = "review_generation_url")
	private String reviewGenerationUrl;

	@Column(name = "business_aggregation_id")
	private Integer businessAggregationId;

	@Column(name = "business_id")
	private Integer businessId;

	@Column(name = "enterprise_id")
	private Integer enterpriseId;

	@Column(name = "reseller_id")
	private Integer resellerId;

	@Column(name = "defaultForTemplate")
	private Integer defaultForTemplate;

	@Column(name = "updated", updatable = false)
	@Temporal(TemporalType.TIMESTAMP)
	private Date updated;

	@Column(name = "created")
	@Temporal(TemporalType.TIMESTAMP)
	private Date created;

	@Column(name = "updated_by")
	private Integer updatedBy;

	@Column(name = "created_by")
	private Integer createdBy;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getSourceId() {
		return sourceId;
	}

	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}

	public String getReviewGenerationUrl() {
		return reviewGenerationUrl;
	}

	public void setReviewGenerationUrl(String reviewGenerationUrl) {
		this.reviewGenerationUrl = reviewGenerationUrl;
	}

	public Integer getBusinessAggregationId() {
		return businessAggregationId;
	}

	public void setBusinessAggregationId(Integer businessAggregationId) {
		this.businessAggregationId = businessAggregationId;
	}

	public Integer getBusinessId() {
		return businessId;
	}

	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}

	public Integer getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public Integer getResellerId() {
		return resellerId;
	}

	public void setResellerId(Integer resellerId) {
		this.resellerId = resellerId;
	}

	public Integer getDefaultForTemplate() {
		return defaultForTemplate;
	}

	public void setDefaultForTemplate(Integer defaultForTemplate) {
		this.defaultForTemplate = defaultForTemplate;
	}

	public Date getUpdated() {
		return updated;
	}

	public void setUpdated(Date updated) {
		this.updated = updated;
	}

	public Date getCreated() {
		return created;
	}

	public void setCreated(Date created) {
		this.created = created;
	}

	public Integer getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Integer getReviewGenerationSourceId() {
		return reviewGenerationSourceId;
	}

	public void setReviewGenerationSourceId(Integer reviewGenerationSourceId) {
		this.reviewGenerationSourceId = reviewGenerationSourceId;
	}

}
