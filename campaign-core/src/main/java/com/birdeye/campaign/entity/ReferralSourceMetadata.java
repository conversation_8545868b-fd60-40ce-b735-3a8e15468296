/**
 * @file_name ReferralSource.java
 * @created_date 8 Jul 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.entity;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * @file_name ReferralSource.java
 * @created_date 8 Jul 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
@Entity
@Table(name = "referral_source_metadata")
public class ReferralSourceMetadata {
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id")
	private Integer			id;
	
	@JoinColumn(name = "referral_source_id", referencedColumnName = "id", insertable = false, updatable = false)
	@ManyToOne(optional = false, fetch = FetchType.LAZY)
	private ReferralSource	referralSource;
	
	@Column(name = "referral_source_id") // Refers to ReferralSource -  
	private Integer			referralSourceId;
	
	@Column(name = "source", nullable = false)
	private String			source;				//web/ios/android
	
	@Column(name = "enabled", nullable = false)
	private Integer			enabled;
	
	@Column(name = "is_default", nullable = false)
	private Integer			isDefault;
	
	@Column(name = "default_priority", nullable = false)
	private Integer			defaultPriority;
	
	@Column(name = "referral_source_url")
	private String			referralSourceUrl;
	
	@Column(name = "source_alias", nullable = false)
	private String			sourceAlias;
	
	@Column(name = "logo_url")
	private String	logoUrl;
	
	@Column(name = "button_color")
	private String	buttonColor;
	
	@Column(name = "button_text_color")
	private String	buttonTextColor;
	
	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}
	
	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	
	/**
	 * @return the referralSource
	 */
	public ReferralSource getReferralSource() {
		return referralSource;
	}
	
	/**
	 * @param referralSource
	 *            the referralSource to set
	 */
	public void setReferralSource(ReferralSource referralSource) {
		this.referralSource = referralSource;
	}
	
	/**
	 * @return the referralSourceId
	 */
	public Integer getReferralSourceId() {
		return referralSourceId;
	}
	
	/**
	 * @param referralSourceId
	 *            the referralSourceId to set
	 */
	public void setReferralSourceId(Integer referralSourceId) {
		this.referralSourceId = referralSourceId;
	}
	
	/**
	 * @return the enabled
	 */
	public Integer getEnabled() {
		return enabled;
	}
	
	/**
	 * @param enabled
	 *            the enabled to set
	 */
	public void setEnabled(Integer enabled) {
		this.enabled = enabled;
	}
	
	/**
	 * @return the source
	 */
	public String getSource() {
		return source;
	}
	
	/**
	 * @param source
	 *            the source to set
	 */
	public void setSource(String source) {
		this.source = source;
	}
	
	/**
	 * @return the defaultPriority
	 */
	public Integer getDefaultPriority() {
		return defaultPriority;
	}
	
	/**
	 * @param defaultPriority
	 *            the defaultPriority to set
	 */
	public void setDefaultPriority(Integer defaultPriority) {
		this.defaultPriority = defaultPriority;
	}
	
	/**
	 * @return the isDefault
	 */
	public Integer getIsDefault() {
		return isDefault;
	}
	
	/**
	 * @param isDefault
	 *            the isDefault to set
	 */
	public void setIsDefault(Integer isDefault) {
		this.isDefault = isDefault;
	}
	
	public String getReferralSourceUrl() {
		return referralSourceUrl;
	}

	public void setReferralSourceUrl(String referralSourceUrl) {
		this.referralSourceUrl = referralSourceUrl;
	}
	
	/**
	 * @return the logoUrl
	 */
	public String getLogoUrl() {
		return logoUrl;
	}

	/**
	 * @param logoUrl the logoUrl to set
	 */
	public void setLogoUrl(String logoUrl) {
		this.logoUrl = logoUrl;
	}

	/**
	 * @return the buttonColor
	 */
	public String getButtonColor() {
		return buttonColor;
	}

	/**
	 * @param buttonColor the buttonColor to set
	 */
	public void setButtonColor(String buttonColor) {
		this.buttonColor = buttonColor;
	}

	/**
	 * @return the buttonTextColor
	 */
	public String getButtonTextColor() {
		return buttonTextColor;
	}

	/**
	 * @param buttonTextColor the buttonTextColor to set
	 */
	public void setButtonTextColor(String buttonTextColor) {
		this.buttonTextColor = buttonTextColor;
	}

	public String getSourceAlias() {
		return sourceAlias;
	}

	public void setSourceAlias(String sourceAlias) {
		this.sourceAlias = sourceAlias;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("ReferralSourceMetadata [id=");
		builder.append(id);
		builder.append(", referralSourceId=");
		builder.append(referralSourceId);
		builder.append(", source=");
		builder.append(source);
		builder.append(", enabled=");
		builder.append(enabled);
		builder.append(", isDefault=");
		builder.append(isDefault);
		builder.append(", defaultPriority=");
		builder.append(defaultPriority);
		builder.append(", referralSourceUrl=");
		builder.append(referralSourceUrl);
		builder.append(", logoUrl=");
		builder.append(logoUrl);
		builder.append(", buttonColor=");
		builder.append(buttonColor);
		builder.append(", buttonTextColor=");
		builder.append(buttonTextColor);
		builder.append(", sourceAlias=");
		builder.append(sourceAlias);
		builder.append("]");
		return builder.toString();
	}
	
}
