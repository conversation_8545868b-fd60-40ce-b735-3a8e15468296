
package com.birdeye.campaign.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CollectionTable;
import javax.persistence.Column;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OrderColumn;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "business_sms_template")
public class BusinessSmsTemplate implements Serializable {

	private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    
    @Column(name = "name")
    private String name;

    @Column(name = "business_id")
    private Integer businessId;
    
    @Column(name = "enterprise_id")
    private Integer enterpriseId;
    
    @Column(name = "reseller_id")
    private Integer resellerId;
    
    @Column(name = "type")
    private String type = "review_request";

    @Column(name = "message_from")
    private String messageFrom;
    
    @Column(name = "message_body")
    private String messageBody;
    
    @Column(name = "question")
    private String question;

    @Column(name = "positive_link_label")
    private String positiveLinkLabel;
    
    @Column(name = "negative_link_label")
    private String negativeLinkLabel;
    
    @Column(name = "recommend_page_heading")
    private String recommendPageHeading;
    
    @Column(name = "recommend_page_message")
    private String recommendPageMessage;
    
    @Column(name = "recommend_thank_page_message")
    private String recommendThankPageMessage;
    
    @Column(name = "recommend_thank_page_footer")
    private String recommendThankPageFooter;

    @Column(name = "non_recommend_review_page_message")
    private String nonRecommendReviewPageMessage;
    
    @Column(name = "non_recommend_thank_page_message")
    private String nonRecommendThankPageMessage;
    
    @Column(name = "non_recommend_thank_page_footer")
    private String nonRecommendThankPageFooter;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name="created_at")
    @CreationTimestamp
    private Date createdAt;
    
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name="updated_at")
    @UpdateTimestamp
    private Date updatedAt;
    
    @Column(name = "created_by")
    private Integer createdBy;

    @Column(name = "updated_by")
    private Integer updatedBy;

    @Column(name = "unsubscribe_text")
    private String unsubscribeText;
    
    @Column(name = "media_url")
    private String mediaUrl;
    
    @Column(name = "media_type")
    private String mediaType;
    
    @Column(name = "non_recommended_url")
    private String nonRecommendedUrl;
    
    @Column(name = "include_message_from")
    private Integer includeMessageFrom = 1;
    
    @Column(name = "enable_sentiment_check")
    private Integer enableSentimentCheck;
    
    @Column(name = "mms_enabled")
    private Integer mmsEnabled;
    
    @Column(name = "sentiment_check_type")
    private String sentimentCheckType;

    @Column(name = "star_rating_min")
    private Integer starRatingMin;

    @Column(name = "nps_rating_min")
    private Integer npsRatingMin;

    @Column(name = "show_emoticon")
    private Integer showEmoticon;

    @ElementCollection
    @CollectionTable(name="nps_labels_sms",joinColumns=@JoinColumn(name="business_template_id"))
    @OrderColumn(name="label_order")
    private List<String> npsLabels = new ArrayList<>();

    @ElementCollection
    @CollectionTable(name="star_labels_sms", joinColumns=@JoinColumn(name="business_template_id"))
    @OrderColumn(name="label_order")
    private List<String> starLabels = new ArrayList<>();
    
    @Column(name = "unsubscribe_text_enabled")
    private Integer unsubscribeTextEnabled = 0;
    
    @Column(name = "write_review_question")
    private String writeReviewQuestion;
    
    @Column(name = "write_review_neg_text")
    private String writeReviewNegativeText;
    
    @Column(name = "write_review_pos_text")
    private String writeReviewPositiveText;
    
    // equal or above rating is positive
    @Column(name = "positive_rating_threshold")
    private Integer positiveRatingThreshold;
    
    @Column (name = "send_mail_after")
    private Integer sendMailAfter = 0;
    
    @Column (name = "salutation")
    private String salutation;
    
    @Column(name = "enable_contact_us")
    private Integer enableContactUs;
    
    @Column(name = "contact_us_message")
    private String contactUsMessage;
    
    @Column(name = "contact_us_button_text")
    private String contactUsButtonText;
    
    @Column(name = "contact_us_button_text_color")
    private String contactUsButtonTextColor;
    
    @Column(name = "contact_us_button_color")
    private String contactUsButtonColor;
    
    @Column(name = "custom_image_url")
    private String customImageUrl;
    
    // CX Template Variables
    @Column(name = "neutral_link_label")
    private String neutralLinkLabel;
    
    @Column(name = "exclude_neutral")
    private Integer excludeNeutral;
    
	@Column(name = "star_heading")
	private String	starHeading;
	
	@Column(name = "sentiment_heading")
	private String	sentimentHeading;
	
	@Column(name = "sentiment_message")
    private String sentimentMessage;
	
	@Column(name = "star_message")
    private String starMessage;
	
	@Column(name = "is_deleted")
	private Integer	isDeleted;
    //new fields
	@Column(name = "enable_feedback_message")
	private Integer enableFeedbackMessage;
	
	@Column(name = "feedback_callback_message")
	private String feedbackCallbackMessage;
	
	@Column(name = "enable_feedback_checkbox")
	private Integer enableFeedbackCheckbox;
	
	@Column(name = "thankyou_message")
	private String thankyouMessage;
	
	@Column(name = "thankyou_heading")
	private String thankyouHeading;
	
	@Column(name = "review_site_button_color")
	private String reviewSiteButtonColor;
	
	@Column(name = "review_site_button_text_color")
	private String reviewSiteButtonTextColor;
	
	@Column(name = "review_enable")
	private Integer reviewEnable;
	
	@Column(name = "location_branding_enabled")
	private Integer locationBrandingEnabled;
	
	
	@Column(name = "referral_message")
	private String referralMessage;
	
	@Column(name="media_urls")
	private String mediaUrls;         // comma separated attachment(image) path
	
	@Column(name="referral_question")
	private String referralQuestion;
	
	@Column(name="referral_options")
	private String referralOptions;
	
	@Column(name="referral_contact_option_enabled")
	private Integer referralContactOptionEnabled;
	
	@Column(name="confirm_button_enabled")
	private Integer confirmButtonEnabled;
	
	@Column(name="reschedule_button_enabled")
	private Integer rescheduleButtonEnabled;
	
	@Column(name="cancel_button_enabled")
	private Integer cancelButtonEnabled;
	
	@Column(name="book_appointment_button_enabled")
	private Integer bookAppointmentButtonEnabled;
	
	@Column(name = "feedback_text_label")
	private String	feedbackTextLabel;
	
	@Column(name = "submit_button_text")
	private String	submitButtonText;
	
	@Column(name = "form_url")
	private String	formUrl;
	
	@Column(name = "location_level_template")
	private Integer locationLevelTemplate;
	
	@Column(name = "is_default_template")
	private Integer	isDefaultTemplate	= 0;
	
	@Column(name = "label_configs")
	private String	labelConfigs;
	
	@Column(name = "sms_category")
	private String	smsCategory;

	public String getFeedbackTextLabel() {
		return feedbackTextLabel;
	}

	public void setFeedbackTextLabel(String feedbackTextLabel) {
		this.feedbackTextLabel = feedbackTextLabel;
	}

	public String getSubmitButtonText() {
		return submitButtonText;
	}

	public void setSubmitButtonText(String submitButtonText) {
		this.submitButtonText = submitButtonText;
	}
	
	public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getWriteReviewQuestion() {
            return writeReviewQuestion;
    }

    public void setWriteReviewQuestion(String writeReviewQuestion) {
            this.writeReviewQuestion = writeReviewQuestion;
    }

    public String getWriteReviewNegativeText() {
            return writeReviewNegativeText;
    }

    public void setWriteReviewNegativeText(String writeReviewNegativeText) {
            this.writeReviewNegativeText = writeReviewNegativeText;
    }

    public String getWriteReviewPositiveText() {
            return writeReviewPositiveText;
    }

    public void setWriteReviewPositiveText(String writeReviewPositiveText) {
            this.writeReviewPositiveText = writeReviewPositiveText;
    }

    public Integer getPositiveRatingThreshold() {
            return positiveRatingThreshold;
    }

    public void setPositiveRatingThreshold(Integer positiveRatingThreshold) {
            this.positiveRatingThreshold = positiveRatingThreshold;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public String getMessageFrom() {
        return messageFrom;
    }

    public void setMessageFrom(String messageFrom) {
        this.messageFrom = messageFrom;
    }

    public String getMessageBody() {
        return messageBody;
    }

    public void setMessageBody(String messageBody) {
        this.messageBody = messageBody;
    }

    public String getPositiveLinkLabel() {
        return positiveLinkLabel;
    }

    public void setPositiveLinkLabel(String positiveLinkLabel) {
        this.positiveLinkLabel = positiveLinkLabel;
    }

    public String getNegativeLinkLabel() {
        return negativeLinkLabel;
    }

    public void setNegativeLinkLabel(String negativeLinkLabel) {
        this.negativeLinkLabel = negativeLinkLabel;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public String getNonRecommendReviewPageMessage() {
        return nonRecommendReviewPageMessage;
    }

    public void setNonRecommendReviewPageMessage(String nonRecommendReviewPageMessage) {
        this.nonRecommendReviewPageMessage = nonRecommendReviewPageMessage;
    }

    public String getUnsubscribeText() {
        return unsubscribeText;
    }

    public void setUnsubscribeText(String unsubscribeText) {
        this.unsubscribeText = unsubscribeText;
    }
    
    public String getCustomImageUrl() {
		return customImageUrl;
	}

	public void setCustomImageUrl(String customImageUrl) {
		this.customImageUrl = customImageUrl;
	}

    /**
	 * @return the referralMessage
	 */
	public String getReferralMessage() {
		return referralMessage;
	}

	/**
	 * @param referralMessage the referralMessage to set
	 */
	public void setReferralMessage(String referralMessage) {
		this.referralMessage = referralMessage;
	}

	@Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof BusinessSmsTemplate)) {
            return false;
        }
        BusinessSmsTemplate other = (BusinessSmsTemplate) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    /**
     * @return the mediaUrl
     */
    public String getMediaUrl() {
        return mediaUrl;
    }

    /**
     * @param mediaUrl the mediaUrl to set
     */
    public void setMediaUrl(String mediaUrl) {
        this.mediaUrl = mediaUrl;
    }

    /**
     * @return the mediaType
     */
    public String getMediaType() {
        return mediaType;
    }

    /**
     * @param mediaType the mediaType to set
     */
    public void setMediaType(String mediaType) {
        this.mediaType = mediaType;
    }

    public String getNonRecommendedUrl() {
        return nonRecommendedUrl;
    }

    public void setNonRecommendedUrl(String nonRecommendedUrl) {
        this.nonRecommendedUrl = nonRecommendedUrl;
    }

    public Integer getIncludeMessageFrom() {
        return includeMessageFrom;
    }

    public void setIncludeMessageFrom(Integer includeMessageFrom) {
        this.includeMessageFrom = includeMessageFrom;
    }

    /**
     * @return the name
     */
    public String getName() {
        return name;
    }

    /**
     * @param name the name to set
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * @return the enableSentimentCheck
     */
    public Integer getEnableSentimentCheck() {
        return enableSentimentCheck;
    }

    /**
     * @param enableSentimentCheck the enableSentimentCheck to set
     */
    public void setEnableSentimentCheck(Integer enableSentimentCheck) {
    	if(type != null){
    		switch(type){
	    		case "review_request":
				case "survey_request":
					enableSentimentCheck = 0;
					break;
    		}
    	}    	
        this.enableSentimentCheck = enableSentimentCheck;
    }

    /**
     * @return the mmsEnabled
     */
    public Integer getMmsEnabled() {
        return mmsEnabled;
    }

    /**
     * @param mmsEnabled the mmsEnabled to set
     */
    public void setMmsEnabled(Integer mmsEnabled) {
        this.mmsEnabled = mmsEnabled;
    }

    public String getSentimentCheckType() {
            return sentimentCheckType;
    }

    public void setSentimentCheckType(String sentimentCheckType) {
            this.sentimentCheckType = sentimentCheckType;
    }

    public Integer getShowEmoticon() {
            return showEmoticon;
    }

    public void setShowEmoticon(Integer showEmoticon) {
            this.showEmoticon = showEmoticon;
    }

    public List<String> getNpsLabels() {
            return npsLabels;
    }

    public void setNpsLabels(List<String> npsLabels) {
            this.npsLabels = npsLabels;
    }

    public List<String> getStarLabels() {
            return starLabels;
    }

    public void setStarLabels(List<String> starLabels) {
            this.starLabels = starLabels;
    }

    public Integer getStarRatingMin() {
            return starRatingMin;
    }

    public void setStarRatingMin(Integer starRatingMin) {
            this.starRatingMin = starRatingMin;
    }

    public Integer getNpsRatingMin() {
            return npsRatingMin;
    }

    public void setNpsRatingMin(Integer npsRatingMin) {
            this.npsRatingMin = npsRatingMin;
    }

    public String getNonRecommendThankPageMessage() {
        return nonRecommendThankPageMessage;
    }

    public void setNonRecommendThankPageMessage(String nonRecommendThankPageMessage) {
        this.nonRecommendThankPageMessage = nonRecommendThankPageMessage;
    }

    public String getNonRecommendThankPageFooter() {
        return nonRecommendThankPageFooter;
    }

    public void setNonRecommendThankPageFooter(String nonRecommendThankPageFooter) {
        this.nonRecommendThankPageFooter = nonRecommendThankPageFooter;
    }

    public String getRecommendPageHeading() {
        return recommendPageHeading;
    }

    public void setRecommendPageHeading(String recommendPageHeading) {
        this.recommendPageHeading = recommendPageHeading;
    }

    public String getRecommendPageMessage() {
        return recommendPageMessage;
    }

    public void setRecommendPageMessage(String recommendPageMessage) {
        this.recommendPageMessage = recommendPageMessage;
    }

    public String getRecommendThankPageMessage() {
        return recommendThankPageMessage;
    }

    public void setRecommendThankPageMessage(String recommendThankPageMessage) {
        this.recommendThankPageMessage = recommendThankPageMessage;
    }

    public String getRecommendThankPageFooter() {
        return recommendThankPageFooter;
    }

    public void setRecommendThankPageFooter(String recommendThankPageFooter) {
        this.recommendThankPageFooter = recommendThankPageFooter;
    }

    public Integer getUnsubscribeTextEnabled() {
        return unsubscribeTextEnabled;
    }

    public void setUnsubscribeTextEnabled(Integer unsubscribeTextEnabled) {
        this.unsubscribeTextEnabled = unsubscribeTextEnabled;
    }

    public Integer getSendMailAfter() {
        return sendMailAfter;
    }

    public void setSendMailAfter(Integer sendMailAfter) {
        this.sendMailAfter = sendMailAfter;
    }

	/**
	 * @return the salutation
	 */
	public String getSalutation() {
		return salutation;
	}

	/**
	 * @param salutation the salutation to set
	 */
	public void setSalutation(String salutation) {
		this.salutation = salutation;
	}

	/**
	 * @return the enableContactUs
	 */
	public Integer getEnableContactUs() {
		return enableContactUs;
	}

	/**
	 * @param enableContactUs the enableContactUs to set
	 */
	public void setEnableContactUs(Integer enableContactUs) {
		this.enableContactUs = enableContactUs;
	}

	/**
	 * @return the contactUsMessage
	 */
	public String getContactUsMessage() {
		return contactUsMessage;
	}

	/**
	 * @param contactUsMessage the contactUsMessage to set
	 */
	public void setContactUsMessage(String contactUsMessage) {
		this.contactUsMessage = contactUsMessage;
	}

	/**
	 * @return the contactUsButtonText
	 */
	public String getContactUsButtonText() {
		return contactUsButtonText;
	}

	/**
	 * @param contactUsButtonText the contactUsButtonText to set
	 */
	public void setContactUsButtonText(String contactUsButtonText) {
		this.contactUsButtonText = contactUsButtonText;
	}

	/**
	 * @return the contactUsButtonTextColor
	 */
	public String getContactUsButtonTextColor() {
		return contactUsButtonTextColor;
	}

	/**
	 * @param contactUsButtonTextColor the contactUsButtonTextColor to set
	 */
	public void setContactUsButtonTextColor(String contactUsButtonTextColor) {
		this.contactUsButtonTextColor = contactUsButtonTextColor;
	}

	/**
	 * @return the contactUsButtonColor
	 */
	public String getContactUsButtonColor() {
		return contactUsButtonColor;
	}

	/**
	 * @param contactUsButtonColor the contactUsButtonColor to set
	 */
	public void setContactUsButtonColor(String contactUsButtonColor) {
		this.contactUsButtonColor = contactUsButtonColor;
	}

	public String getNeutralLinkLabel() {
		return neutralLinkLabel;
	}

	public void setNeutralLinkLabel(String neutralLinkLabel) {
		this.neutralLinkLabel = neutralLinkLabel;
	}

	public Integer getExcludeNeutral() {
		return excludeNeutral;
	}

	public void setExcludeNeutral(Integer excludeNeutral) {
		this.excludeNeutral = excludeNeutral;
	}

	public String getStarHeading() {
		return starHeading;
	}

	public void setStarHeading(String starHeading) {
		this.starHeading = starHeading;
	}

	public String getSentimentHeading() {
		return sentimentHeading;
	}

	public void setSentimentHeading(String sentimentHeading) {
		this.sentimentHeading = sentimentHeading;
	}

	public String getSentimentMessage() {
		return sentimentMessage;
	}

	public void setSentimentMessage(String sentimentMessage) {
		this.sentimentMessage = sentimentMessage;
	}

	public String getStarMessage() {
		return starMessage;
	}

	public void setStarMessage(String starMessage) {
		this.starMessage = starMessage;
	}

	public Integer getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public Integer isDeleted() {
		return isDeleted;
	}

	public void setDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Integer getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Integer getEnableFeedbackMessage() {
		return enableFeedbackMessage;
	}

	public void setEnableFeedbackMessage(Integer enableFeedbackMessage) {
		this.enableFeedbackMessage = enableFeedbackMessage;
	}

	public String getFeedbackCallbackMessage() {
		return feedbackCallbackMessage;
	}

	public void setFeedbackCallbackMessage(String feedbackCallbackMessage) {
		this.feedbackCallbackMessage = feedbackCallbackMessage;
	}

	public Integer getEnableFeedbackCheckbox() {
		return enableFeedbackCheckbox;
	}

	public void setEnableFeedbackCheckbox(Integer enableFeedbackCheckbox) {
		this.enableFeedbackCheckbox = enableFeedbackCheckbox;
	}

	public String getThankyouMessage() {
		return thankyouMessage;
	}

	public void setThankyouMessage(String thankyouMessage) {
		this.thankyouMessage = thankyouMessage;
	}

	public String getThankyouHeading() {
		return thankyouHeading;
	}

	public void setThankyouHeading(String thankyouHeading) {
		this.thankyouHeading = thankyouHeading;
	}

	public String getReviewSiteButtonColor() {
		return reviewSiteButtonColor;
	}

	public void setReviewSiteButtonColor(String reviewSiteButtonColor) {
		this.reviewSiteButtonColor = reviewSiteButtonColor;
	}

	public String getReviewSiteButtonTextColor() {
		return reviewSiteButtonTextColor;
	}

	public void setReviewSiteButtonTextColor(String reviewSiteButtonTextColor) {
		this.reviewSiteButtonTextColor = reviewSiteButtonTextColor;
	}

	public Integer getReviewEnable() {
		return reviewEnable;
	}

	public void setReviewEnable(Integer reviewEnable) {
		this.reviewEnable = reviewEnable;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */

	public String getMediaUrls() {
		return mediaUrls;
	}

	public void setMediaUrls(String mediaUrls) {
		this.mediaUrls = mediaUrls;
	}

	/**
	 * @return the locationBrandingEnabled
	 */
	public Integer getLocationBrandingEnabled() {
		return locationBrandingEnabled;
	}

	/**
	 * @param locationBrandingEnabled the locationBrandingEnabled to set
	 */
	public void setLocationBrandingEnabled(Integer locationBrandingEnabled) {
		this.locationBrandingEnabled = locationBrandingEnabled;
	}

	public String getReferralQuestion() {
		return referralQuestion;
	}

	public void setReferralQuestion(String referralQuestion) {
		this.referralQuestion = referralQuestion;
	}

	public String getReferralOptions() {
		return referralOptions;
	}

	public void setReferralOptions(String referralOptions) {
		this.referralOptions = referralOptions;
	}

	public Integer getReferralContactOptionEnabled() {
		return referralContactOptionEnabled;
	}

	public void setReferralContactOptionEnabled(Integer referralContactOptionEnabled) {
		this.referralContactOptionEnabled = referralContactOptionEnabled;
	}

	public Integer getResellerId() {
		return resellerId;
	}

	public void setResellerId(Integer resellerId) {
		this.resellerId = resellerId;
	}

	public Integer getConfirmButtonEnabled() {
		return confirmButtonEnabled;
	}

	public void setConfirmButtonEnabled(Integer confirmButtonEnabled) {
		this.confirmButtonEnabled = confirmButtonEnabled;
	}

	public Integer getRescheduleButtonEnabled() {
		return rescheduleButtonEnabled;
	}

	public void setRescheduleButtonEnabled(Integer rescheduleButtonEnabled) {
		this.rescheduleButtonEnabled = rescheduleButtonEnabled;
	}

	public Integer getCancelButtonEnabled() {
		return cancelButtonEnabled;
	}

	public void setCancelButtonEnabled(Integer cancelButtonEnabled) {
		this.cancelButtonEnabled = cancelButtonEnabled;
	}

	public Integer getBookAppointmentButtonEnabled() {
		return bookAppointmentButtonEnabled;
	}

	public void setBookAppointmentButtonEnabled(Integer bookAppointmentButtonEnabled) {
		this.bookAppointmentButtonEnabled = bookAppointmentButtonEnabled;
	}
	
	public String getFormUrl() {
		return formUrl;
	}

	public void setFormUrl(String formUrl) {
		this.formUrl = formUrl;
	}
	

	public Integer getLocationLevelTemplate() {
		return locationLevelTemplate;
	}

	public void setLocationLevelTemplate(Integer locationLevelTemplate) {
		this.locationLevelTemplate = locationLevelTemplate;
	}

	public Integer getIsDefaultTemplate() {
		return isDefaultTemplate;
	}

	public void setIsDefaultTemplate(Integer isDefaultTemplate) {
		this.isDefaultTemplate = isDefaultTemplate;
	}

	/**
	 * @return the labelConfigs
	 */
	public String getLabelConfigs() {
		return labelConfigs;
	}
	
	/**
	 * @param labelConfigs
	 *            the labelConfigs to set
	 */
	public void setLabelConfigs(String labelConfigs) {
		this.labelConfigs = labelConfigs;
	}

	/**
	 * @return the smsCategory
	 */
	public String getSmsCategory() {
		return smsCategory;
	}

	/**
	 * @param smsCategory the smsCategory to set
	 */
	public void setSmsCategory(String smsCategory) {
		this.smsCategory = smsCategory;
	}

	@Override
	public String toString() {
		ReflectionToStringBuilder sb = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return sb.toString();
	}

}
