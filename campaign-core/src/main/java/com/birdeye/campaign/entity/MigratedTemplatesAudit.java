package com.birdeye.campaign.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * Audit table for templates migrated to new campaign flow.
 * 
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "migrated_templates_audit")
public class MigratedTemplatesAudit {
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer	id;
	
	@Column(name = "enterprise_id")
	private Integer	enterpriseId;
	
	@Column(name = "business_id")
	private Integer	businessId;
	
	@Column(name = "old_template_id")
	private Integer	oldTemplateId;
	
	@Column(name = "new_template_id")
	private Integer	newTemplateId;
	
	@Column(name = "user_id")
	private Integer	userId;
	
	@Column(name = "type")
	private String	type;
	
	public MigratedTemplatesAudit() {
	}
	
	public MigratedTemplatesAudit(Integer enterpriseId, Integer businessId, Integer oldTemplateId, Integer newTemplateId, Integer userId, String type) {
		super();
		this.enterpriseId = enterpriseId;
		this.businessId = businessId;
		this.oldTemplateId = oldTemplateId;
		this.newTemplateId = newTemplateId;
		this.userId = userId;
		this.type = type;
	}

	public Integer getId() {
		return id;
	}
	
	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getEnterpriseId() {
		return enterpriseId;
	}
	
	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	
	public Integer getBusinessId() {
		return businessId;
	}
	
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	public Integer getOldTemplateId() {
		return oldTemplateId;
	}
	
	public void setOldTemplateId(Integer oldTemplateId) {
		this.oldTemplateId = oldTemplateId;
	}
	
	public Integer getNewTemplateId() {
		return newTemplateId;
	}
	
	public void setNewTemplateId(Integer newTemplateId) {
		this.newTemplateId = newTemplateId;
	}
	
	public Integer getUserId() {
		return userId;
	}
	
	public void setUserId(Integer userId) {
		this.userId = userId;
	}
	
	public String getType() {
		return type;
	}
	
	public void setType(String type) {
		this.type = type;
	}
	
}
