/**
 * 
 */
package com.birdeye.campaign.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.UpdateTimestamp;

import com.birdeye.campaign.dto.AppointmentScheduleInfo;
import com.birdeye.campaign.dto.RecallDueDateInfo;
import com.birdeye.campaign.dto.RuleExpression;
import com.birdeye.campaign.request.Tag;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vladmihalcea.hibernate.type.json.JsonStringType;

/**
 * <AUTHOR> Kumar
 *
 */
@Entity
@Table(name = "campaign_condition")
@TypeDef(name = "json", typeClass = JsonStringType.class)
public class CampaignCondition implements Serializable {
	
	private static final long	serialVersionUID	= -9163123418990291767L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id", nullable = false)
	private Integer				id;
	
	@Column(name = "campaign_id", nullable = false)
	private Integer				campaignId;
	
	@Column(name = "enterprise_id", nullable = false)
	private Integer				enterpriseId;
	
	@Column(name = "event_name", nullable = false)
	private String				event;
	
	@Column(name = "lvl_alias_id")
	private String				lvlAliasId;
	
	@Column(name = "lvl_alias")
	private String				lvlAlias;
	
	@Type(type = "json")
	@Column(name = "lvl_ids", columnDefinition = "json")
	private List<String>		lvlIds;
	
	@Type(type = "json")
	@Column(name = "contact_sources", columnDefinition = "json")
	private List<String>		contactSources;
	
	@Type(type = "json")
	@Column(columnDefinition = "json")
	private List<Tag>			tags;
	
	@Column(name = "created_at")
	@Temporal(TemporalType.TIMESTAMP)
	@CreationTimestamp
	private Date				createdAt;
	
	@Column(name = "updated_at")
	@Temporal(TemporalType.TIMESTAMP)
	@UpdateTimestamp
	private Date				updatedAt;
	
	@Column(name = "updated_by")
	private Integer				updatedBy;
	
	// stores list of tags to be excluded in the rule evaluation - BIRDEYE-51130
	@Type(type = "json")
	@Column(name = "exclusion_tags", columnDefinition = "json")
	private List<Tag> exclusionTags;
	
	// identifier to select contacts with no tags - BIRDEYE-51130
	@Column(name = "no_tag_filter")
	private Integer noTagFilter;
	
	// identifier to select contacts with any tags - BIRDEYE-51130
	@Column(name = "any_tag_filter")
	private Integer							anyTagFilter;
	
	@Type(type = "json")
	@Column(name = "rule_expression", columnDefinition = "json")
	private RuleExpression					ruleExpression;
	
	@Column(name = "mvel_expression")
	private String							mvelExpression;
	
	@Type(type = "json")
	@Column(name = "mvel_params", columnDefinition = "json")
	private Map<String, String>				mvelParamsAndTypes;
	
	@Type(type = "json")
	@Column(name = "trigger_rule_expression", columnDefinition = "json")
	private RuleExpression					triggerRuleExpression;
	
	@Column(name = "trigger_mvel_expression")
	private String							triggerMvelExpression;
	
	@Type(type = "json")
	@Column(name = "trigger_mvel_params", columnDefinition = "json")
	private Map<String, String>				triggerMvelParamsAndTypes;
	
	@Type(type = "json")
	@Column(name = "appointment_schedule_info", columnDefinition = "json")
	private List<AppointmentScheduleInfo>	appointmentScheduleInfo;
	
	@Column(name = "schedule_in_hours")
	private String							scheduleinHours;			// comma separated values of schedules in hours
	
	@Type(type = "json")
	@Column(name = "custom_condition_info", columnDefinition = "json")
	private RecallDueDateInfo				executionDateInfo;
	
	@Column(name = "reminder_event_type")
	private String							recurringReminderEventType;
	
	
	/**
	 * Default constructor
	 */
	public CampaignCondition() {
	}
	
	/**
	 * Copy constructor
	 * @param campaignCondition
	 */
	public CampaignCondition(CampaignCondition other) {
		
		ObjectMapper objectMapper = new ObjectMapper();
        try {
            String json = objectMapper.writeValueAsString(other);
            CampaignCondition campaignCondition = objectMapper.readValue(json, CampaignCondition.class);
            this.id = campaignCondition.id;
    		this.campaignId = campaignCondition.campaignId;
    		this.enterpriseId = campaignCondition.enterpriseId;
    		this.event = campaignCondition.event;
    		this.lvlAliasId = campaignCondition.lvlAliasId;
    		this.lvlAlias = campaignCondition.lvlAlias;
    		this.lvlIds = campaignCondition.lvlIds;
    		this.contactSources = campaignCondition.contactSources;
    		this.tags = campaignCondition.tags;
    		this.createdAt = campaignCondition.createdAt;
    		this.updatedAt = campaignCondition.updatedAt;
    		this.updatedBy = campaignCondition.updatedBy;
    		this.exclusionTags = campaignCondition.exclusionTags;
    		this.noTagFilter = campaignCondition.noTagFilter;
    		this.anyTagFilter = campaignCondition.anyTagFilter;
    		this.ruleExpression = campaignCondition.ruleExpression;
    		this.mvelExpression = campaignCondition.mvelExpression;
    		this.mvelParamsAndTypes = campaignCondition.mvelParamsAndTypes;
    		this.triggerRuleExpression = campaignCondition.triggerRuleExpression;
    		this.triggerMvelExpression = campaignCondition.triggerMvelExpression;
    		this.triggerMvelParamsAndTypes = campaignCondition.triggerMvelParamsAndTypes;
    		this.appointmentScheduleInfo = campaignCondition.appointmentScheduleInfo;
    		this.scheduleinHours = campaignCondition.scheduleinHours;
    		this.executionDateInfo = campaignCondition.executionDateInfo;
    		this.recurringReminderEventType = campaignCondition.recurringReminderEventType;
        } catch (Exception e) {
            // Ignore the exception
        }
	}

	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}
	
	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	
	/**
	 * @return the campaignId
	 */
	public Integer getCampaignId() {
		return campaignId;
	}
	
	/**
	 * @param campaignId
	 *            the campaignId to set
	 */
	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}
	
	/**
	 * @return the enterpriseId
	 */
	public Integer getEnterpriseId() {
		return enterpriseId;
	}
	
	/**
	 * @param enterpriseId
	 *            the enterpriseId to set
	 */
	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	
	/**
	 * @return the tags
	 */
	public List<Tag> getTags() {
		return tags;
	}
	
	/**
	 * @param tags
	 *            the tags to set
	 */
	public void setTags(List<Tag> tags) {
		this.tags = tags;
	}
	
	/**
	 * @return the createdAt
	 */
	public Date getCreatedAt() {
		return createdAt;
	}
	
	/**
	 * @param createdAt
	 *            the createdAt to set
	 */
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}
	
	/**
	 * @return the updatedAt
	 */
	public Date getUpdatedAt() {
		return updatedAt;
	}
	
	/**
	 * @param updatedAt
	 *            the updatedAt to set
	 */
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}
	
	/**
	 * @return the updatedBy
	 */
	public Integer getUpdatedBy() {
		return updatedBy;
	}
	
	/**
	 * @param updatedBy
	 *            the updatedBy to set
	 */
	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}
	
	/**
	 * @return the event
	 */
	public String getEvent() {
		return event;
	}
	
	/**
	 * @param event
	 *            the event to set
	 */
	public void setEvent(String event) {
		this.event = event;
	}
	
	/**
	 * @return the lvlAliasId
	 */
	public String getLvlAliasId() {
		return lvlAliasId;
	}
	
	/**
	 * @param lvlAliasId
	 *            the lvlAliasId to set
	 */
	public void setLvlAliasId(String lvlAliasId) {
		this.lvlAliasId = lvlAliasId;
	}
	
	/**
	 * @return the lvlAlias
	 */
	public String getLvlAlias() {
		return lvlAlias;
	}
	
	/**
	 * @param lvlAlias
	 *            the lvlAlias to set
	 */
	public void setLvlAlias(String lvlAlias) {
		this.lvlAlias = lvlAlias;
	}
	
	/**
	 * @return the lvlIds
	 */
	public List<String> getLvlIds() {
		return lvlIds;
	}
	
	/**
	 * @param lvlIds
	 *            the lvlIds to set
	 */
	public void setLvlIds(List<String> lvlIds) {
		this.lvlIds = lvlIds;
	}
	
	public List<String> getContactSources() {
		return contactSources;
	}
	
	public void setContactSources(List<String> contactSources) {
		this.contactSources = contactSources;
	}
	
	public List<Tag> getExclusionTags() {
		return exclusionTags;
	}
	
	public void setExclusionTags(List<Tag> exclusionTags) {
		this.exclusionTags = exclusionTags;
	}
	
	public Integer getNoTagFilter() {
		return noTagFilter;
	}
	
	public void setNoTagFilter(Integer noTagFilter) {
		this.noTagFilter = noTagFilter;
	}
	
	public Integer getAnyTagFilter() {
		return anyTagFilter;
	}
	
	public void setAnyTagFilter(Integer anyTagFilter) {
		this.anyTagFilter = anyTagFilter;
	}
	
	/**
	 * @return the ruleExpression
	 */
	public RuleExpression getRuleExpression() {
		return ruleExpression;
	}
	
	/**
	 * @param ruleExpression
	 *            the ruleExpression to set
	 */
	public void setRuleExpression(RuleExpression ruleExpression) {
		this.ruleExpression = ruleExpression;
	}
	
	/**
	 * @return the mvelExpression
	 */
	public String getMvelExpression() {
		return mvelExpression;
	}
	
	/**
	 * @param mvelExpression
	 *            the mvelExpression to set
	 */
	public void setMvelExpression(String mvelExpression) {
		this.mvelExpression = mvelExpression;
	}
	
	/**
	 * @return the mvelParamsAndTypes
	 */
	public Map<String, String> getMvelParamsAndTypes() {
		return mvelParamsAndTypes;
	}
	
	/**
	 * @param mvelParamsAndTypes
	 *            the mvelParamsAndTypes to set
	 */
	public void setMvelParamsAndTypes(Map<String, String> mvelParamsAndTypes) {
		this.mvelParamsAndTypes = mvelParamsAndTypes;
	}
	
	/**
	 * @return the triggerRuleExpression
	 */
	public RuleExpression getTriggerRuleExpression() {
		return triggerRuleExpression;
	}
	
	/**
	 * @param triggerRuleExpression
	 *            the triggerRuleExpression to set
	 */
	public void setTriggerRuleExpression(RuleExpression triggerRuleExpression) {
		this.triggerRuleExpression = triggerRuleExpression;
	}
	
	/**
	 * @return the triggerMvelExpression
	 */
	public String getTriggerMvelExpression() {
		return triggerMvelExpression;
	}
	
	/**
	 * @param triggerMvelExpression
	 *            the triggerMvelExpression to set
	 */
	public void setTriggerMvelExpression(String triggerMvelExpression) {
		this.triggerMvelExpression = triggerMvelExpression;
	}
	
	/**
	 * @return the triggerMvelParamsAndTypes
	 */
	public Map<String, String> getTriggerMvelParamsAndTypes() {
		return triggerMvelParamsAndTypes;
	}
	
	/**
	 * @param triggerMvelParamsAndTypes
	 *            the triggerMvelParamsAndTypes to set
	 */
	public void setTriggerMvelParamsAndTypes(Map<String, String> triggerMvelParamsAndTypes) {
		this.triggerMvelParamsAndTypes = triggerMvelParamsAndTypes;
	}
	
	public List<AppointmentScheduleInfo> getAppointmentScheduleInfo() {
		return appointmentScheduleInfo;
	}
	
	public void setAppointmentScheduleInfo(List<AppointmentScheduleInfo> appointmentScheduleInfo) {
		this.appointmentScheduleInfo = appointmentScheduleInfo;
	}
	
	public String getScheduleinHours() {
		return scheduleinHours;
	}
	
	public void setScheduleinHours(String scheduleinHours) {
		this.scheduleinHours = scheduleinHours;
	}
	
	/**
	 * @return the executionDateInfo
	 */
	public RecallDueDateInfo getExecutionDateInfo() {
		return executionDateInfo;
	}

	/**
	 * @param executionDateInfo the executionDateInfo to set
	 */
	public void setExecutionDateInfo(RecallDueDateInfo executionDateInfo) {
		this.executionDateInfo = executionDateInfo;
	}
	
	public String getRecurringReminderEventType() {
		return recurringReminderEventType;
	}
	
	public void setRecurringReminderEventType(String recurringReminderEventType) {
		this.recurringReminderEventType = recurringReminderEventType;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
