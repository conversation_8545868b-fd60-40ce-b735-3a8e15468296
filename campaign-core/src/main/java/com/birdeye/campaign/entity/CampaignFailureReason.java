package com.birdeye.campaign.entity;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "campaign_failure_reason")
public class CampaignFailureReason {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id")
	private Integer id;

	@Column(name = "failure_id")
	private Integer failureId;

	@Column(name = "message_regex")
	private String messageRegex;

	@Column(name = "failure_reason")
	private String failureReason;

	public String getFailureReason() {
		return failureReason;
	}

	public void setFailureReason(String failureReason) {
		this.failureReason = failureReason;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getFailureId() {
		return failureId;
	}

	public void setFailureId(Integer failureId) {
		this.failureId = failureId;
	}

	public String getMessageRegex() {
		return messageRegex;
	}

	public void setMessageRegex(String messageRegex) {
		this.messageRegex = messageRegex;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("CampaignFailureReason [id=");
		builder.append(id);
		builder.append(", failureId=");
		builder.append(failureId);
		builder.append(", messageRegex=");
		builder.append(messageRegex);
		builder.append(", failureReason=");
		builder.append(failureReason);
		builder.append("]");
		return builder.toString();
	}

}