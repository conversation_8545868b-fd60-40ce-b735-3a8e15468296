package com.birdeye.campaign.entity;

import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * 
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "referral_template_data")
public class ReferralTemplateData {
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id", nullable = false)
	private Integer	id;
	
	@Column(name = "enterprise_id")
	private Integer	enterpriseId;
	
	@Column(name = "template_id")
	private Integer	templateId;
	
	@Column(name = "source")
	private String	source;
	
	@Column(name = "referral_message_email_subject")
	private String	referralMessageEmailSubject;
	
	@Column(name = "referral_message_cta_url_enabled")
	private Integer	referralMessageCtaUrlEnabled	= 0;
	
	@Column(name = "referral_message_cta_url")
	private String	referralMessageCtaUrl;
	
	@Column(name = "referral_form_heading")
	private String	referralFormHeading;
	
	@Column(name = "referral_form_message")
	private String	referralFormMessage;
	
	@Column(name = "referral_form_cta_label")
	private String	referralFormCtaLabel;
	
	@Column(name = "ty_page_alternate_url")
	private String	tyPageAlternateUrl;
	
	@Column(name = "ty_page_enabled")
	private Integer	tyPageEnabled = 0;
	
	@Column(name = "ty_page_heading")
	private String	tyPageHeading;
	
	@Column(name = "ty_page_message")
	private String	tyPageMessage;
	
	@Column(name = "ty_page_cta_label")
	private String	tyPageCtaLabel;
	
	@Column(name = "ty_page_cta_url")
	private String	tyPageCtaUrl;
	
	@Column(name = "created_at")
	@Temporal(TemporalType.TIMESTAMP)
	@CreationTimestamp
	private Date	createdAt;
	
	@Column(name = "updated_at")
	@Temporal(TemporalType.TIMESTAMP)
	@UpdateTimestamp
	private Date	updatedAt;
	
	public Integer getId() {
		return id;
	}
	
	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getEnterpriseId() {
		return enterpriseId;
	}
	
	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	
	public Integer getTemplateId() {
		return templateId;
	}
	
	public void setTemplateId(Integer templateId) {
		this.templateId = templateId;
	}
	
	public String getSource() {
		return source;
	}
	
	public void setSource(String source) {
		this.source = source;
	}
	
	public String getReferralMessageEmailSubject() {
		return referralMessageEmailSubject;
	}
	
	public void setReferralMessageEmailSubject(String referralMessageEmailSubject) {
		this.referralMessageEmailSubject = referralMessageEmailSubject;
	}
	
	public String getReferralFormHeading() {
		return referralFormHeading;
	}
	
	public void setReferralFormHeading(String referralFormHeading) {
		this.referralFormHeading = referralFormHeading;
	}
	
	public String getReferralFormMessage() {
		return referralFormMessage;
	}
	
	public void setReferralFormMessage(String referralFormMessage) {
		this.referralFormMessage = referralFormMessage;
	}
	
	public String getReferralFormCtaLabel() {
		return referralFormCtaLabel;
	}
	
	public void setReferralFormCtaLabel(String referralFormCtaLabel) {
		this.referralFormCtaLabel = referralFormCtaLabel;
	}
	
	public String getTyPageHeading() {
		return tyPageHeading;
	}
	
	public void setTyPageHeading(String tyPageHeading) {
		this.tyPageHeading = tyPageHeading;
	}
	
	public String getTyPageMessage() {
		return tyPageMessage;
	}
	
	public void setTyPageMessage(String tyPageMessage) {
		this.tyPageMessage = tyPageMessage;
	}
	
	public String getTyPageCtaLabel() {
		return tyPageCtaLabel;
	}
	
	public void setTyPageCtaLabel(String tyPageCtaLabel) {
		this.tyPageCtaLabel = tyPageCtaLabel;
	}
	
	public String getTyPageCtaUrl() {
		return tyPageCtaUrl;
	}
	
	public void setTyPageCtaUrl(String tyPageCtaUrl) {
		this.tyPageCtaUrl = tyPageCtaUrl;
	}
	
	public Date getCreatedAt() {
		return createdAt;
	}
	
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}
	
	public Date getUpdatedAt() {
		return updatedAt;
	}
	
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}
	
	public String getReferralMessageCtaUrl() {
		return referralMessageCtaUrl;
	}
	
	public void setReferralMessageCtaUrl(String referralMessageCtaUrl) {
		this.referralMessageCtaUrl = referralMessageCtaUrl;
	}

	public Integer getReferralMessageCtaUrlEnabled() {
		return referralMessageCtaUrlEnabled;
	}

	public void setReferralMessageCtaUrlEnabled(Integer referralMessageCtaUrlEnabled) {
		this.referralMessageCtaUrlEnabled = referralMessageCtaUrlEnabled;
	}

	public String getTyPageAlternateUrl() {
		return tyPageAlternateUrl;
	}

	public void setTyPageAlternateUrl(String tyPageAlternateUrl) {
		this.tyPageAlternateUrl = tyPageAlternateUrl;
	}

	public Integer getTyPageEnabled() {
		return tyPageEnabled;
	}

	public void setTyPageEnabled(Integer tyPageEnabled) {
		this.tyPageEnabled = tyPageEnabled;
	}
	
}
