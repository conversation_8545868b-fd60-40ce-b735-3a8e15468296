package com.birdeye.campaign.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

@Entity(name = "recurring_reminder_events_audit")
public class RecurringReminderEventsAudit implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 398685363357025748L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id", nullable = false)
	private Integer				id;
	
	@Column(name = "reminder_events_id", nullable = false)
	private Integer				reminderEventsId;
	
	@Column(name = "campaign_id", nullable = false)
	private Integer				campaignId;
	
	@Column(name = "contact_audit_map", nullable = false)
	private String				contactAuditMap;
	
	@Column(name = "status", nullable = false)
	private String				status;
	
	@Column(name = "failure_reason", nullable = false)
	private String				failureReason;
	
	@CreationTimestamp
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_at")
	private Date				createdAt;
	
	@UpdateTimestamp
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_at")
	private Date				updatedAt;
	
	public Integer getId() {
		return id;
	}
	
	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getReminderEventsId() {
		return reminderEventsId;
	}
	
	public void setReminderEventsId(Integer reminderEventsId) {
		this.reminderEventsId = reminderEventsId;
	}
	
	public Integer getCampaignId() {
		return campaignId;
	}
	
	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}
	
	public String getContactAuditMap() {
		return contactAuditMap;
	}
	
	public void setContactAuditMap(String contactAuditMap) {
		this.contactAuditMap = contactAuditMap;
	}
	
	public String getStatus() {
		return status;
	}
	
	public void setStatus(String status) {
		this.status = status;
	}
	
	public String getFailureReason() {
		return failureReason;
	}
	
	public void setFailureReason(String failureReason) {
		this.failureReason = failureReason;
	}
	
	public Date getCreatedAt() {
		return createdAt;
	}
	
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}
	
	public Date getUpdatedAt() {
		return updatedAt;
	}
	
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
