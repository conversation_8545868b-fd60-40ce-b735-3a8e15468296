package com.birdeye.campaign.entity;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.persistence.*;
import java.io.Serializable;

@Entity(name = "outbound_text_audit")
public class OutboundTextAudit implements Serializable {
	
	private static final long serialVersionUID = 4875765904352734538L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id")
	private Long id;
	
	@Column(name = "promotion_id")
	private Long promotionId;
	
	@Column(name = "free_text")
	private String freeText;
	
	@Column(name = "url_value")
	private String urlValue;
	
	@Column(name = "customer_id")
	private Integer customerId;
	
	@Column(name = "campaign_id")
	private Integer campaignId;
	
	@Column(name = "template_id")
	private Integer templateId;
	
	@Column(name = "business_id")
	private Integer businessId;
	
	@Column(name = "external_uid")
	private String externalUid;
	
	public OutboundTextAudit() {
	}
	
	public OutboundTextAudit(Integer customerId, Integer campaignId, Integer templateId, Integer businessId) {
		this.customerId = customerId;
		this.campaignId = campaignId;
		this.templateId = templateId;
		this.businessId = businessId;
	}
	
	public Long getId() {
		return id;
	}
	
	public Long getPromotionId() {
		return promotionId;
	}
	
	public void setPromotionId(Long promotionId) {
		this.promotionId = promotionId;
	}
	
	public String getFreeText() {
		return freeText;
	}
	
	public void setFreeText(String freeText) {
		this.freeText = freeText;
	}
	
	public String getUrlValue() {
		return urlValue;
	}
	
	public void setUrlValue(String urlValue) {
		this.urlValue = urlValue;
	}
	
	public Integer getCustomerId() {
		return customerId;
	}
	
	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}
	
	public Integer getCampaignId() {
		return campaignId;
	}
	
	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}
	
	public Integer getTemplateId() {
		return templateId;
	}
	
	public void setTemplateId(Integer templateId) {
		this.templateId = templateId;
	}
	
	public Integer getBusinessId() {
		return businessId;
	}
	
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	public String getExternalUid() {
		return externalUid;
	}
	
	public void setExternalUid(String externalUid) {
		this.externalUid = externalUid;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
