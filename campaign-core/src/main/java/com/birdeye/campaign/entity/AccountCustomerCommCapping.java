package com.birdeye.campaign.entity;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "account_customer_comm_capping")
public class AccountCustomerCommCapping implements Serializable {

    private static final long serialVersionUID = 2064082305576047934L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Integer id;

    @Column(name = "account_customer_id")
    private Integer accountCustomerId;

    @Column(name = "review_request_email_on")
    @Temporal(TemporalType.TIMESTAMP)
    private Date rrEmailOn;

    @Column(name = "review_request_sms_on")
    @Temporal(TemporalType.TIMESTAMP)
    private Date rrSmsOn;

    @Column(name = "cx_email_on")
    @Temporal(TemporalType.TIMESTAMP)
    private Date cxEmailOn;

    @Column(name = "cx_sms_on")
    @Temporal(TemporalType.TIMESTAMP)
    private Date cxSmsOn;

    @Column(name = "referral_email_on")
    @Temporal(TemporalType.TIMESTAMP)
    private Date referralEmailOn;

    @Column(name = "referral_sms_on")
    @Temporal(TemporalType.TIMESTAMP)
    private Date referralSmsOn;

    @Column(name = "promotion_email_on")
    @Temporal(TemporalType.TIMESTAMP)
    private Date promotionalEmailOn;

    @Column(name = "promotion_sms_on")
    @Temporal(TemporalType.TIMESTAMP)
    private Date promotionalSmsOn;

    @Column(name = "survey_email_on")
    @Temporal(TemporalType.TIMESTAMP)
    private Date surveyEmailOn;

    @Column(name = "survey_sms_on")
    @Temporal(TemporalType.TIMESTAMP)
    private Date surveySmsOn;


    @Column(name = "updated_at")
    @Temporal(TemporalType.TIMESTAMP)
    @UpdateTimestamp
    private Date updatedAt;

    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @Version
    private int version;

    public AccountCustomerCommCapping() {
        this.rrEmailOn = new Date(0L);
        this.rrSmsOn = new Date(0L);
        this.cxEmailOn = new Date(0L);
        this.cxSmsOn = new Date(0L);
        this.referralEmailOn = new Date(0L);
        this.referralSmsOn = new Date(0L);
        this.promotionalEmailOn = new Date(0L);
        this.promotionalSmsOn = new Date(0L);
        this.surveyEmailOn = new Date(0L);
        this.surveySmsOn = new Date(0L);
    }

    public AccountCustomerCommCapping(Integer id, Integer accountCustomerId, Date rrEmailOn, Date rrSmsOn, Date cxEmailOn, Date cxSmsOn, Date referralEmailOn, Date referralSmsOn, Date promotionalEmailOn,
                               Date promotionalSmsOn, Date surveyEmailOn, Date surveySmsOn, Date updatedAt, Date createdAt) {
        this.id = id;
        this.accountCustomerId = accountCustomerId;
        this.rrEmailOn = rrEmailOn;
        this.rrSmsOn = rrSmsOn;
        this.cxEmailOn = cxEmailOn;
        this.cxSmsOn = cxSmsOn;
        this.referralEmailOn = referralEmailOn;
        this.referralSmsOn = referralSmsOn;
        this.promotionalEmailOn = promotionalEmailOn;
        this.promotionalSmsOn = promotionalSmsOn;
        this.surveyEmailOn = surveyEmailOn;
        this.surveySmsOn = surveySmsOn;
        this.updatedAt = updatedAt;
        this.createdAt = createdAt;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getAccountCustomerId() {
        return accountCustomerId;
    }

    public void setAccountCustomerId(Integer accountCustomerId) {
        this.accountCustomerId = accountCustomerId;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public Date getRrEmailOn() {
        return rrEmailOn;
    }

    public void setRrEmailOn(Date rrEmailOn) {
        this.rrEmailOn = rrEmailOn;
    }

    public Date getRrSmsOn() {
        return rrSmsOn;
    }

    public void setRrSmsOn(Date rrSmsOn) {
        this.rrSmsOn = rrSmsOn;
    }

    public Date getCxEmailOn() {
        return cxEmailOn;
    }

    public void setCxEmailOn(Date cxEmailOn) {
        this.cxEmailOn = cxEmailOn;
    }

    public Date getCxSmsOn() {
        return cxSmsOn;
    }

    public void setCxSmsOn(Date cxSmsOn) {
        this.cxSmsOn = cxSmsOn;
    }

    public Date getReferralEmailOn() {
        return referralEmailOn;
    }

    public void setReferralEmailOn(Date referralEmailOn) {
        this.referralEmailOn = referralEmailOn;
    }

    public Date getReferralSmsOn() {
        return referralSmsOn;
    }

    public void setReferralSmsOn(Date referralSmsOn) {
        this.referralSmsOn = referralSmsOn;
    }

    public Date getPromotionalEmailOn() {
        return promotionalEmailOn;
    }

    public void setPromotionalEmailOn(Date promotionalEmailOn) {
        this.promotionalEmailOn = promotionalEmailOn;
    }

    public Date getPromotionalSmsOn() {
        return promotionalSmsOn;
    }

    public void setPromotionalSmsOn(Date promotionalSmsOn) {
        this.promotionalSmsOn = promotionalSmsOn;
    }

    public Date getSurveyEmailOn() {
        return surveyEmailOn;
    }

    public void setSurveyEmailOn(Date surveyEmailOn) {
        this.surveyEmailOn = surveyEmailOn;
    }

    public Date getSurveySmsOn() {
        return surveySmsOn;
    }

    public void setSurveySmsOn(Date surveySmsOn) {
        this.surveySmsOn = surveySmsOn;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
    }

}
