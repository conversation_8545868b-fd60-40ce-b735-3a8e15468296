package com.birdeye.campaign.entity;

import javax.persistence.*;

import org.hibernate.annotations.UpdateTimestamp;

import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "customer_comm_capping")
public class CustomerCommCapping implements Serializable {

	private static final long serialVersionUID = -2094401946560359117L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id")
	private Integer id;

	@Column(name = "customer_id")
	private Integer customerId;

	@Column(name = "review_request_email_on")
	@Temporal(TemporalType.TIMESTAMP)
	private Date rrEmailOn;

	@Column(name = "review_request_sms_on")
	@Temporal(TemporalType.TIMESTAMP)
	private Date rrSmsOn;

	@Column(name = "cx_email_on")
	@Temporal(TemporalType.TIMESTAMP)
	private Date cxEmailOn;

	@Column(name = "cx_sms_on")
	@Temporal(TemporalType.TIMESTAMP)
	private Date cxSmsOn;

	@Column(name = "referral_email_on")
	@Temporal(TemporalType.TIMESTAMP)
	private Date referralEmailOn;

	@Column(name = "referral_sms_on")
	@Temporal(TemporalType.TIMESTAMP)
	private Date referralSmsOn;

	@Column(name = "promotion_email_on")
	@Temporal(TemporalType.TIMESTAMP)
	private Date promotionalEmailOn;

	@Column(name = "promotion_sms_on")
	@Temporal(TemporalType.TIMESTAMP)
	private Date promotionalSmsOn;

	@Column(name = "survey_email_on")
	@Temporal(TemporalType.TIMESTAMP)
	private Date surveyEmailOn;

	@Column(name = "survey_sms_on")
	@Temporal(TemporalType.TIMESTAMP)
	private Date surveySmsOn;

	@Column(name = "updated_at")
	@Temporal(TemporalType.TIMESTAMP)
	@UpdateTimestamp
	private Date updatedAt;

	@Column(name = "created_at")
	@Temporal(TemporalType.TIMESTAMP)
	private Date createdAt;
	
	@Version
	private int version;

	public CustomerCommCapping() {
		this.rrEmailOn = new Date(0L);
		this.rrSmsOn = new Date(0L);
		this.cxEmailOn = new Date(0L);
		this.cxSmsOn = new Date(0L);
		this.referralEmailOn = new Date(0L);
		this.referralSmsOn = new Date(0L);
		this.promotionalEmailOn = new Date(0L);
		this.promotionalSmsOn = new Date(0L);
		this.surveyEmailOn = new Date(0L);
		this.surveySmsOn = new Date(0L);
	}

	public CustomerCommCapping(Integer id, Integer customerId, Date rrEmailOn, Date rrSmsOn, Date cxEmailOn, Date cxSmsOn, Date referralEmailOn, Date referralSmsOn, Date promotionalEmailOn,
			Date promotionalSmsOn, Date surveyEmailOn, Date surveySmsOn, Date updatedAt, Date createdAt) {
		this.id = id;
		this.customerId = customerId;
		this.rrEmailOn = rrEmailOn;
		this.rrSmsOn = rrSmsOn;
		this.cxEmailOn = cxEmailOn;
		this.cxSmsOn = cxSmsOn;
		this.referralEmailOn = referralEmailOn;
		this.referralSmsOn = referralSmsOn;
		this.promotionalEmailOn = promotionalEmailOn;
		this.promotionalSmsOn = promotionalSmsOn;
		this.surveyEmailOn = surveyEmailOn;
		this.surveySmsOn = surveySmsOn;
		this.updatedAt = updatedAt;
		this.createdAt = createdAt;
	}


	public int getVersion() {
		return version;
	}

	public void setVersion(int version) {
		this.version = version;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	public Date getRrEmailOn() {
		return rrEmailOn;
	}

	public void setRrEmailOn(Date rrEmailOn) {
		this.rrEmailOn = rrEmailOn;
	}

	public Date getRrSmsOn() {
		return rrSmsOn;
	}

	public void setRrSmsOn(Date rrSmsOn) {
		this.rrSmsOn = rrSmsOn;
	}

	public Date getCxEmailOn() {
		return cxEmailOn;
	}

	public void setCxEmailOn(Date cxEmailOn) {
		this.cxEmailOn = cxEmailOn;
	}

	public Date getCxSmsOn() {
		return cxSmsOn;
	}

	public void setCxSmsOn(Date cxSmsOn) {
		this.cxSmsOn = cxSmsOn;
	}

	public Date getReferralEmailOn() {
		return referralEmailOn;
	}

	public void setReferralEmailOn(Date referralEmailOn) {
		this.referralEmailOn = referralEmailOn;
	}

	public Date getReferralSmsOn() {
		return referralSmsOn;
	}

	public void setReferralSmsOn(Date referralSmsOn) {
		this.referralSmsOn = referralSmsOn;
	}

	public Date getPromotionalEmailOn() {
		return promotionalEmailOn;
	}

	public void setPromotionalEmailOn(Date promotionalEmailOn) {
		this.promotionalEmailOn = promotionalEmailOn;
	}

	public Date getPromotionalSmsOn() {
		return promotionalSmsOn;
	}

	public void setPromotionalSmsOn(Date promotionalSmsOn) {
		this.promotionalSmsOn = promotionalSmsOn;
	}

	public Date getSurveyEmailOn() {
		return surveyEmailOn;
	}

	public void setSurveyEmailOn(Date surveyEmailOn) {
		this.surveyEmailOn = surveyEmailOn;
	}

	public Date getSurveySmsOn() {
		return surveySmsOn;
	}

	public void setSurveySmsOn(Date surveySmsOn) {
		this.surveySmsOn = surveySmsOn;
	}

	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Override
	public String toString() {
		final StringBuilder sb = new StringBuilder("CustomerCommCapping{");
		sb.append("id=").append(id);
		sb.append(", customerId=").append(customerId);
		sb.append(", rrEmailOn=").append(rrEmailOn);
		sb.append(", rrSmsOn=").append(rrSmsOn);
		sb.append(", cxEmailOn=").append(cxEmailOn);
		sb.append(", cxSmsOn=").append(cxSmsOn);
		sb.append(", referralEmailOn=").append(referralEmailOn);
		sb.append(", referralSmsOn=").append(referralSmsOn);
		sb.append(", promotionalEmailOn=").append(promotionalEmailOn);
		sb.append(", promotionalSmsOn=").append(promotionalSmsOn);
		sb.append(", surveyEmailOn=").append(surveyEmailOn);
		sb.append(", surveySmsOn=").append(surveySmsOn);
		sb.append(", updatedAt=").append(updatedAt);
		sb.append(", createdAt=").append(createdAt);
		sb.append('}');
		return sb.toString();
	}
}
