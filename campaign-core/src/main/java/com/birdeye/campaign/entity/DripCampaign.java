package com.birdeye.campaign.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.CreationTimestamp;

/**
 * <AUTHOR> Store Day Wise Summary for Drip Campaigns
 *
 */
@Entity
@Table(name = "drip_campaign")
public class DripCampaign implements Serializable {

	private static final long serialVersionUID = -8768220957932711513L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	@Column(name = "campaign_id")
	private Integer campaignId;

	@Column(name = "customer_list")
	private String customerList;

	@Column(name = "day_of_execution")
	private Integer dayOfExecution;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "date_of_execution")
	private Date dateOfExecution;

	@Temporal(TemporalType.TIMESTAMP)
	@CreationTimestamp
	@Column(name = "created_at")
	private Date createdAt = new Date();

	// time as configured by user to send campaign - this will help compute further batches based on what was the last batch's location schedule time
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "location_schedule_time")
	private Date locationScheduleTime;

	// time as configured by user converted to server timezone - audit field only - this time would be the time fed to poller to run the bathc at
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "actual_schedule_time")
	private Date actualScheduleTime;

	@Column(name = "status")
	private Integer status = 0;

	@Column(name = "batch_size")
	private Integer batchSize;

	// timezone of the batch customers
	@Column(name = "timezone")
	private String timezone;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getCampaignId() {
		return campaignId;
	}

	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}

	public String getCustomerList() {
		return customerList;
	}

	public void setCustomerList(String customerList) {
		this.customerList = customerList;
	}

	public Integer getDayOfExecution() {
		return dayOfExecution;
	}

	public void setDayOfExecution(Integer dayOfExecution) {
		this.dayOfExecution = dayOfExecution;
	}

	public Date getDateOfExecution() {
		return dateOfExecution;
	}

	public void setDateOfExecution(Date dateOfExecution) {
		this.dateOfExecution = dateOfExecution;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getBatchSize() {
		return batchSize;
	}

	public void setBatchSize(Integer batchSize) {
		this.batchSize = batchSize;
	}

	public Date getLocationScheduleTime() {
		return locationScheduleTime;
	}

	public void setLocationScheduleTime(Date locationScheduleTime) {
		this.locationScheduleTime = locationScheduleTime;
	}

	public Date getActualScheduleTime() {
		return actualScheduleTime;
	}

	public void setActualScheduleTime(Date actualScheduleTime) {
		this.actualScheduleTime = actualScheduleTime;
	}

	public String getTimezone() {
		return timezone;
	}

	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("DripCampaign [id=");
		builder.append(id);
		builder.append(", campaignId=");
		builder.append(campaignId);
		builder.append(", customerList=");
		builder.append(customerList);
		builder.append(", dayOfExecution=");
		builder.append(dayOfExecution);
		builder.append(", dateOfExecution=");
		builder.append(dateOfExecution);
		builder.append(", createdAt=");
		builder.append(createdAt);
		builder.append(", locationScheduleTime=");
		builder.append(locationScheduleTime);
		builder.append(", actualScheduleTime=");
		builder.append(actualScheduleTime);
		builder.append(", status=");
		builder.append(status);
		builder.append(", batchSize=");
		builder.append(batchSize);
		builder.append(", timezone=");
		builder.append(timezone);
		builder.append("]");
		return builder.toString();
	}

}
