package com.birdeye.campaign.entity;

import java.util.Date;

import javax.annotation.Generated;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

@Entity
@Table(name = "campaign_modification_audit")
public class CampaignModificationAudit {
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id")
	private Integer	id;
	
	@Column(name = "business_id")
	private Integer	businessId;
	
	@Column(name = "campaign_id")
	private Integer	campaignId;
	
	@Column(name = "email_template_id")
	private Integer	emailTemplateId;
	
	@Column(name = "sms_template_id")
	private Integer	smsTemplateId;
	
	@Column(name = "request_id")
	private Integer	requestId;
	
	@Column(name = "request_type")
	private String	requestType;
	
	@Column(name = "event_message")
	private String	eventMessage;
	
	@Column(name = "created_at")
	@Temporal(TemporalType.TIMESTAMP)
	@CreationTimestamp
	private Date	createdAt;
	
	@Column(name = "updated_at")
	@Temporal(TemporalType.TIMESTAMP)
	@UpdateTimestamp
	private Date	updatedAt;
	
	public Integer getId() {
		return id;
	}
	
	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getBusinessId() {
		return businessId;
	}
	
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	public Integer getCampaignId() {
		return campaignId;
	}
	
	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}
	
	public Integer getEmailTemplateId() {
		return emailTemplateId;
	}
	
	public void setEmailTemplateId(Integer emailTemplateId) {
		this.emailTemplateId = emailTemplateId;
	}
	
	public Integer getSmsTemplateId() {
		return smsTemplateId;
	}
	
	public void setSmsTemplateId(Integer smsTemplateId) {
		this.smsTemplateId = smsTemplateId;
	}
	
	public Integer getRequestId() {
		return requestId;
	}
	
	public void setRequestId(Integer requestId) {
		this.requestId = requestId;
	}
	
	public String getRequestType() {
		return requestType;
	}
	
	public void setRequestType(String requestType) {
		this.requestType = requestType;
	}
	
	public String getEventMessage() {
		return eventMessage;
	}
	
	public void setEventMessage(String eventMessage) {
		this.eventMessage = eventMessage;
	}
	
	public Date getCreatedAt() {
		return createdAt;
	}
	
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}
	
	public Date getUpdatedAt() {
		return updatedAt;
	}
	
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}
	
	@Generated("SparkTools")
	private CampaignModificationAudit(Builder builder) {
		this.id = builder.id;
		this.businessId = builder.businessId;
		this.campaignId = builder.campaignId;
		this.emailTemplateId = builder.emailTemplateId;
		this.smsTemplateId = builder.smsTemplateId;
		this.requestId = builder.requestId;
		this.requestType = builder.requestType;
		this.eventMessage = builder.eventMessage;
		this.createdAt = builder.createdAt;
		this.updatedAt = builder.updatedAt;
	}
	
	@Generated("SparkTools")
	public static Builder builder() {
		return new Builder();
	}
	
	@Generated("SparkTools")
	public static final class Builder {
		
		private Integer	id;
		private Integer	businessId;
		private Integer	campaignId;
		private Integer	emailTemplateId;
		private Integer	smsTemplateId;
		private Integer	requestId;
		private String	requestType;
		private String	eventMessage;
		private Date	createdAt;
		private Date	updatedAt;
		
		private Builder() {
		}
		
		public Builder withId(Integer id) {
			this.id = id;
			return this;
		}
		
		public Builder withCampaignId(Integer campaignId) {
			this.campaignId = campaignId;
			return this;
		}
		
		public Builder withBusinessId(Integer businessId) {
			this.businessId = businessId;
			return this;
		}
		
		public Builder withEmailTemplateId(Integer emailTemplateId) {
			this.emailTemplateId = emailTemplateId;
			return this;
		}
		
		public Builder withSmsTemplateId(Integer smsTemplateId) {
			this.smsTemplateId = smsTemplateId;
			return this;
		}
		
		public Builder withRequestId(Integer requestId) {
			this.requestId = requestId;
			return this;
		}
		
		public Builder withRequestType(String requestType) {
			this.requestType = requestType;
			return this;
		}
		
		public Builder withEventMessage(String eventMessage) {
			this.eventMessage = eventMessage;
			return this;
		}
		
		public Builder withCreatedAt(Date createdAt) {
			this.createdAt = createdAt;
			return this;
		}
		
		public Builder withUpdatedAt(Date updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}
		
		public CampaignModificationAudit build() {
			return new CampaignModificationAudit(this);
		}
		
	}
	
}
