package com.birdeye.campaign.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * <AUTHOR>
 */
@Entity
@Table(name="review_requests_log")
public class ReviewRequestsLog implements Serializable{
    private static final long serialVersionUID = 1L;
        
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Integer id;

    @Column(name="review_request_id")
    private Long reviewRequestId;

    @Column(name="source_id")
    private Integer sourceId;

    @Basic(optional = false)
    @Column(name = "clicked_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date clickedAt;

    @Column(name="clicked_on")
    private String clickedOn = "web";
    
    @Column(name = "os")
    private String os;
    
    @Column(name = "event_id")
    private String eventId;
    
    @Column(name = "click_type")
    private Integer clickType = 1;
    
    @Column(name = "event")
    private String event = "click";
    
    @Column(name = "user_agent")
    private String userAgent ;
    
    @Column(name = "browser")
    private String browser ;
    
    @Column(name = "recommended")
    private Integer recommended ;

    @Column(name = "business_id")
    private Integer businessId;
    
    @Column(name = "rating")
    private Integer rating;
    
    @Column(name = "sentiment_check_type")
    private String sentimentCheckType;

    public ReviewRequestsLog() 
    {
    	// just for entity
    }

    public Date getClickedAt() {
        return clickedAt;
    }

    public void setClickedAt(Date clickedAt) {
        this.clickedAt = clickedAt;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    
    public Long getReviewRequestId() {
        return reviewRequestId;
    }

    public void setReviewRequestId(Long reviewRequestId) {
        this.reviewRequestId = reviewRequestId;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public String getClickedOn() {
        return clickedOn;
    }

    public void setClickedOn(String clickedOn) {
        this.clickedOn = clickedOn;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public Integer getClickType() {
        return clickType;
    }

    public void setClickType(Integer clickType) {
        this.clickType = clickType;
    }

    /**
     * @return the event
     */
    public String getEvent() {
        return event;
    }

    /**
     * @param event the event to set
     */
    public void setEvent(String event) {
        this.event = event;
    }
    
    /**
     * @return the userAgent
     */
    public String getUserAgent() {
        return userAgent;
    }

    /**
     * @param userAgent the userAgent to set
     */
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }
    
    /**
     * @return the browser
     */
    public String getBrowser() {
        return browser;
    }

    /**
     * @param browser the browser to set
     */
    public void setBrowser(String browser) {
        this.browser = browser;
    }

    public Integer getRecommended() {
        return recommended;
    }

    public void setRecommended(Integer recommended) {
        this.recommended = recommended;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }
    
    public Integer getRating() {
		return rating;
	}

	public void setRating(Integer rating) {
		this.rating = rating;
	}

	public String getSentimentCheckType() {
		return sentimentCheckType;
	}

	public void setSentimentCheckType(String sentimentCheckType) {
		this.sentimentCheckType = sentimentCheckType;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("ReviewRequestsLog [id=");
		builder.append(id);
		builder.append(", reviewRequestId=");
		builder.append(reviewRequestId);
		builder.append(", sourceId=");
		builder.append(sourceId);
		builder.append(", clickedAt=");
		builder.append(clickedAt);
		builder.append(", clickedOn=");
		builder.append(clickedOn);
		builder.append(", os=");
		builder.append(os);
		builder.append(", eventId=");
		builder.append(eventId);
		builder.append(", clickType=");
		builder.append(clickType);
		builder.append(", event=");
		builder.append(event);
		builder.append(", userAgent=");
		builder.append(userAgent);
		builder.append(", browser=");
		builder.append(browser);
		builder.append(", recommended=");
		builder.append(recommended);
		builder.append(", businessId=");
		builder.append(businessId);
		builder.append(", rating=");
		builder.append(rating);
		builder.append(", sentimentCheckType=");
		builder.append(sentimentCheckType);
		builder.append("]");
		return builder.toString();
	}
	
	
}
