package com.birdeye.campaign.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.CollectionTable;
import javax.persistence.Column;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OrderColumn;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "business_email_template")
public class BusinessEmailTemplate implements Serializable {
	
	private static final long	serialVersionUID		= 1L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer				id;
	
	@Column(name = "business_id")
	private Integer				businessId;
	
	@Column(name = "enterprise_id")
	private Integer				enterpriseId;
	
	@Column(name = "reseller_id")
	private Integer             resellerId;
	
	@JoinColumn(name = "template_id", referencedColumnName = "id")
	@ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.MERGE)
	private EmailTemplate		templateId;
	
	@Column(name = "template_id", insertable = false, updatable = false)
	private Integer				emailTemplateId;
	
	@Column(name = "incentive_id")
	private Integer				businessIncentiveId;
	
	@NotNull
	@Column(name = "send_mail_after")
	private Integer				sendMailAfter			= 0;
	
	@Column(name = "updated_at")
	@Temporal(TemporalType.TIMESTAMP)
	@UpdateTimestamp
	private Date				updatedAt;
	
	@Column(name = "created_at")
	@Temporal(TemporalType.TIMESTAMP)
	@CreationTimestamp
	private Date				createdAt;
	
	@Column(name = "updated_by")
	private Integer				updatedById;

	@Column(name = "created_by")
	private Integer				createdById;
	
	@Size(max = 255)
	@Column(name = "link_url", length = 255)
	private String				linkURL;
	
	@NotNull
	@Column(name = "send_mail")
	private Integer				sendMail				= 1;
	
	@NotNull
	@Column(name = "max_reminder_count")
	private Integer				maxReminderCount		= 2;
	
	@NotNull
	@Column(name = "reminder_frequency")
	private Integer				reminderFrequency		= 2;
	
	@NotNull
	@Column(name = "send_reminder")
	private Integer				sendReminder			= 1;
	
	@NotNull
	@Column(name = "enable_sentiment_check")
	private Integer				enableSentimentCheck	= 0;
	
	@Column(name = "non_recommended_url")
	private String				nonRecommendedUrl;
	
	@Column(name = "sentiment_check_type")
	private String				sentimentCheckType		= "nps";
	
	@Column(name = "star_rating_min")
	private Integer				starRatingMin;
	
	@Column(name = "nps_rating_min")
	private Integer				npsRatingMin;
	
	@Column(name = "show_emoticon")
	private Integer				showEmoticon			= 0;
	
	@Column(name = "custom_html_template")
	private Integer				customHtmlTemplate		= 0;
	
	@ElementCollection
	@CollectionTable(name = "nps_labels", joinColumns = @JoinColumn(name = "business_template_id"))
	@OrderColumn(name = "label_order")
	private List<String>		npsLabels				= new ArrayList<>();
	
	@ElementCollection
	@CollectionTable(name = "star_labels", joinColumns = @JoinColumn(name = "business_template_id"))
	@OrderColumn(name = "label_order")
	private List<String>		starLabels				= new ArrayList<>();
	
	@Column(name = "enable_contact_us")
	private Integer				enableContactUs			= 1;
	
	@Column(name = "exclude_neutral")
	private Integer				excludeNeutral			= 0;
	
	@Column(name = "review_enabled")
	private Integer				reviewEnabled			= 1;
	
	@Column(name = "is_deleted")
	private Integer				isDeleted				= 0;
	
	@Column(name = "default_reply_to_enabled")
	private Integer				defaultReplyToEnabled	= 1;
	
	@Column(name = "survey_button_color")
	private String				surveyButtonColor		= "#4caf50";
	
	@Column(name = "survey_button_text_color")
	private String				surveyButtonTextColor	= "#ffffff";
	
	@Column(name = "location_branding_enabled")
	private Integer				locationBrandingEnabled	= 0;
	
	@Column(name="confirm_button_enabled")
	private Integer confirmButtonEnabled;
	
	@Column(name="reschedule_button_enabled")
	private Integer rescheduleButtonEnabled;
	
	@Column(name="cancel_button_enabled")
	private Integer cancelButtonEnabled;

	@Column(name="book_appointment_button_enabled")
	private Integer bookAppointmentButtonEnabled;

	@Column(name = "enable_reply_to_inbox")
	private Integer				enableReplyToInbox;
	
	@Column(name = "feedback_text_label")
	private String				feedbackTextLabel		= "Tell us about your experience";
	
	@Column(name = "submit_button_text")
	private String				submitButtonText		= "Submit";
	
	@Column(name = "form_url")
	private String				formUrl;
	
	@Column(name = "form_button_text_color")
	private String				formButtonTextColor		= "#FFFFFF";
	
	@Column(name = "form_button_color")
	private String				formButtonColor			= "#1976D2";
	
	@Column(name = "form_button_text")
	private String				formButtonText			= "Fill out form";
	
	@Column(name = "is_default_template")
	private Integer				isDefaultTemplate		= 0;
	
	public String getFormUrl() {
		return formUrl;
	}

	public void setFormUrl(String formUrl) {
		this.formUrl = formUrl;
	}

	public String getFormButtonTextColor() {
		return formButtonTextColor;
	}

	public void setFormButtonTextColor(String formButtonTextColor) {
		this.formButtonTextColor = formButtonTextColor;
	}

	public String getFormButtonColor() {
		return formButtonColor;
	}

	public void setFormButtonColor(String formButtonColor) {
		this.formButtonColor = formButtonColor;
	}

	public String getFormButtonText() {
		return formButtonText;
	}

	public void setFormButtonText(String formButtonText) {
		this.formButtonText = formButtonText;
	}

	public String getFeedbackTextLabel() {
		return feedbackTextLabel;
	}

	public void setFeedbackTextLabel(String feedbackTextLabel) {
		this.feedbackTextLabel = feedbackTextLabel;
	}

	public String getSubmitButtonText() {
		return submitButtonText;
	}

	public void setSubmitButtonText(String submitButtonText) {
		this.submitButtonText = submitButtonText;
	}

	public Integer getId() {
		return id;
	}
	
	public Integer getCreatedById() {
		return createdById;
	}
	
	public void setCreatedById(Integer createdById) {
		this.createdById = createdById;
	}
	
	public void setId(int id) {
		this.id = id;
	}
	
	/**
	 * @return the templateId
	 */
	public EmailTemplate getTemplateId() {
		return templateId;
	}
	
	/**
	 * @param templateId
	 *            the templateId to set
	 */
	public void setTemplateId(EmailTemplate templateId) {
		this.templateId = templateId;
	}
	
	/**
	 * @return the updatedAt
	 */
	public Date getUpdatedAt() {
		return updatedAt;
	}
	
	/**
	 * @param updatedAt
	 *            the updatedAt to set
	 */
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}
	
	/**
	 * @return the createdAt
	 */
	public Date getCreatedAt() {
		return createdAt;
	}
	
	/**
	 * @param createdAt
	 *            the createdAt to set
	 */
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}
	
	public String getLinkURL() {
		return linkURL;
	}
	
	public void setLinkURL(String linkURL) {
		this.linkURL = linkURL;
	}
	
	public Integer getSendMail() {
		return sendMail;
	}
	
	public void setSendMail(Integer sendMail) {
		this.sendMail = sendMail;
	}
	
	public Integer getMaxReminderCount() {
		return maxReminderCount;
	}
	
	public void setMaxReminderCount(Integer maxReminderCount) {
		this.maxReminderCount = maxReminderCount;
	}
	
	public Integer getReminderFrequency() {
		return reminderFrequency;
	}
	
	public void setReminderFrequency(Integer reminderFrequency) {
		this.reminderFrequency = reminderFrequency;
	}
	
	public Integer getSendMailAfter() {
		return sendMailAfter;
	}
	
	public void setSendMailAfter(Integer sendMailAfter) {
		this.sendMailAfter = sendMailAfter;
	}
	
	public Integer getSendReminder() {
		return sendReminder;
	}
	
	public void setSendReminder(Integer sendReminder) {
		this.sendReminder = sendReminder;
	}
	
	public Integer getBusinessId() {
		return businessId;
	}
	
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	@Override
	public int hashCode() {
		int hash = 0;
		hash += (id != null ? id.hashCode() : 0);
		return hash;
	}
	
	@Override
	public boolean equals(Object object) {
		// TODO: Warning - this method won't work in the case the id fields are not set
		if (!(object instanceof BusinessEmailTemplate)) {
			return false;
		}
		BusinessEmailTemplate other = (BusinessEmailTemplate) object;
		if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
			return false;
		}
		return true;
	}
	
	public String getNonRecommendedUrl() {
		return nonRecommendedUrl;
	}
	
	public void setNonRecommendedUrl(String nonRecommendedUrl) {
		this.nonRecommendedUrl = nonRecommendedUrl;
	}
	
	/**
	 * @return the enableSentimentCheck
	 */
	public Integer getEnableSentimentCheck() {
		return enableSentimentCheck;
	}
	
	/**
	 * @param enableSentimentCheck
	 *            the enableSentimentCheck to set
	 */
	public void setEnableSentimentCheck(Integer enableSentimentCheck) {
		if (templateId != null) {
			switch (templateId.getType()) {
				case "review_request_new":
				case "review_request":
				case "survey_request":
					enableSentimentCheck = 0;
					break;
			}
		}
		this.enableSentimentCheck = enableSentimentCheck;
	}
	
	public String getSentimentCheckType() {
		return sentimentCheckType;
	}
	
	public void setSentimentCheckType(String sentimentCheckType) {
		this.sentimentCheckType = sentimentCheckType;
	}
	
	public Integer getShowEmoticon() {
		return showEmoticon;
	}
	
	public void setShowEmoticon(Integer showEmoticon) {
		this.showEmoticon = showEmoticon;
	}
	
	public void setId(Integer id) {
		this.id = id;
	}
	
	public List<String> getNpsLabels() {
		return npsLabels;
	}
	
	public void setNpsLabels(List<String> npsLabels) {
		this.npsLabels = npsLabels;
	}
	
	public List<String> getStarLabels() {
		return starLabels;
	}
	
	public void setStarLabels(List<String> starLabels) {
		this.starLabels = starLabels;
	}
	
	public Integer getStarRatingMin() {
		return starRatingMin;
	}
	
	public void setStarRatingMin(Integer starRatingMin) {
		this.starRatingMin = starRatingMin;
	}
	
	public Integer getNpsRatingMin() {
		return npsRatingMin;
	}
	
	public void setNpsRatingMin(Integer npsRatingMin) {
		this.npsRatingMin = npsRatingMin;
	}
	
	public Integer getCustomHtmlTemplate() {
		return customHtmlTemplate;
	}
	
	public void setCustomeHtmlTemplate(Integer customHtmlTemplate) {
		this.customHtmlTemplate = customHtmlTemplate;
	}
	
	/**
	 * @return the enableContactUs
	 */
	public Integer getEnableContactUs() {
		return enableContactUs;
	}
	
	/**
	 * @param enableContactUs
	 *            the enableContactUs to set
	 */
	public void setEnableContactUs(Integer enableContactUs) {
		this.enableContactUs = enableContactUs;
	}
	
	/**
	 * @return the excludeNeutral
	 */
	public Integer getExcludeNeutral() {
		return excludeNeutral;
	}
	
	/**
	 * @param excludeNeutral
	 *            the excludeNeutral to set
	 */
	public void setExcludeNeutral(Integer excludeNeutral) {
		this.excludeNeutral = excludeNeutral;
	}
	
	/**
	 * @return the reviewEnabled
	 */
	public Integer getReviewEnabled() {
		return reviewEnabled;
	}
	
	/**
	 * @param reviewEnabled
	 *            the reviewEnabled to set
	 */
	public void setReviewEnabled(Integer reviewEnabled) {
		this.reviewEnabled = reviewEnabled;
	}
	
	public Integer isDeleted() {
		return isDeleted;
	}
	
	public void setDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted;
	}
	
	public Integer getUpdatedById() {
		return updatedById;
	}
	
	public void setUpdatedById(Integer updatedById) {
		this.updatedById = updatedById;
	}
	
	public Integer getBusinessIncentiveId() {
		return businessIncentiveId;
	}
	
	public void setBusinessIncentiveId(Integer businessIncentiveId) {
		this.businessIncentiveId = businessIncentiveId;
	}
	
	public Integer getEnterpriseId() {
		return enterpriseId;
	}
	
	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	
	public Integer getEmailTemplateId() {
		return emailTemplateId;
	}
	
	public void setEmailTemplateId(Integer emailTemplateId) {
		this.emailTemplateId = emailTemplateId;
	}
	
	public Integer getIsDeleted() {
		return isDeleted;
	}
	
	public void setIsDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted;
	}
	
	public Integer getDefaultReplyToEnabled() {
		return (defaultReplyToEnabled != null) ? defaultReplyToEnabled : 0;
	}
	
	public void setDefaultReplyToEnabled(Integer defaultReplyToEnabled) {
		this.defaultReplyToEnabled = defaultReplyToEnabled;
	}
	
	public String getSurveyButtonColor() {
		return surveyButtonColor;
	}
	
	public void setSurveyButtonColor(String surveyButtonColor) {
		this.surveyButtonColor = surveyButtonColor;
	}
	
	public String getSurveyButtonTextColor() {
		return surveyButtonTextColor;
	}
	
	public void setSurveyButtonTextColor(String surveyButtonTextColor) {
		this.surveyButtonTextColor = surveyButtonTextColor;
	}
	
	public Integer getLocationBrandingEnabled() {
		return locationBrandingEnabled;
	}
	
	public void setLocationBrandingEnabled(Integer locationBrandingEnabled) {
		this.locationBrandingEnabled = locationBrandingEnabled;
	}

	public Integer getResellerId() {
		return resellerId;
	}

	public void setResellerId(Integer resellerId) {
		this.resellerId = resellerId;
	}
	
	public Integer getEnableReplyToInbox() {
		return enableReplyToInbox;
	}
	
	public void setEnableReplyToInbox(Integer enableReplyToInbox) {
		this.enableReplyToInbox = enableReplyToInbox;
	}

	public Integer getConfirmButtonEnabled() {
		return confirmButtonEnabled;
	}

	public void setConfirmButtonEnabled(Integer confirmButtonEnabled) {
		this.confirmButtonEnabled = confirmButtonEnabled;
	}

	public Integer getRescheduleButtonEnabled() {
		return rescheduleButtonEnabled;
	}

	public void setRescheduleButtonEnabled(Integer rescheduleButtonEnabled) {
		this.rescheduleButtonEnabled = rescheduleButtonEnabled;
	}

	public Integer getCancelButtonEnabled() {
		return cancelButtonEnabled;
	}

	public void setCancelButtonEnabled(Integer cancelButtonEnabled) {
		this.cancelButtonEnabled = cancelButtonEnabled;
	}

	public Integer getBookAppointmentButtonEnabled() {
		return bookAppointmentButtonEnabled;
	}

	public void setBookAppointmentButtonEnabled(Integer bookAppointmentButtonEnabled) {
		this.bookAppointmentButtonEnabled = bookAppointmentButtonEnabled;
	}

	public Integer getIsDefaultTemplate() {
		return isDefaultTemplate;
	}

	public void setIsDefaultTemplate(Integer isDefaultTemplate) {
		this.isDefaultTemplate = isDefaultTemplate;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
     
	
	
}