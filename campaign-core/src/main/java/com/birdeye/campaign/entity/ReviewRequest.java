package com.birdeye.campaign.entity;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.NotNull;

import org.apache.commons.lang3.StringUtils;

import com.birdeye.campaign.platform.constant.TemplateTypeEnum;
import com.birdeye.campaign.platform.entity.BaseCommunicationEntity;

/**
 * 
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "review_request")
public class ReviewRequest extends BaseCommunicationEntity  {
    
	private static final long serialVersionUID = 1L;
	
	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Long id;
    
    @Column(name = "customer_id")
    private Integer custId;

    @Basic(optional = false)
    @Column(name = "request_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date requestDate = new Date();

    @Basic(optional = false)
    @Column(name = "source")
    private String source = "website";

    @NotNull
    @Column(name = "reminder_count")
    private Integer reminderCount = 0;
    
    @Basic(optional = false)
    @Column(name = "sent_on")
    @Temporal(TemporalType.TIMESTAMP)
    private Date sentOn = new Date();

    @Column(name = "delivery_status")
    private String deliveryStatus = "inprogress";
    
    @Column(name = "failure_reason")
    private String failureReason;
    
    @Column(name = "parent_request_id")
    private Long parentRequestId;
    
    @JoinColumn(name = "parent_request_id", referencedColumnName = "id", insertable = false, updatable = false)
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private ReviewRequest parentRequest;

    @Column(name = "email_Template_id")
    private Integer templateId;
    
    @Column(name = "campaign_id")
    private Integer campaignId;
    
    @Column(name = "business_id")
    private Integer businessId;

    @Column(name = "survey_id")
    private Integer surveyId;
    
    @Column(name = "survey_completed_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date surveyCompletedDate;

    @Column(name = "checkin_id")
    private Integer checkinId;
    
    @Column(name = "request_type")
    private String requestType = "review_request_new"; //EmailTemplateTypes

    public ReviewRequest() {
        try{
            Calendar defaultDate = Calendar.getInstance();
            defaultDate.setTime(new SimpleDateFormat("MM/dd/yyyy HH:mm:ss").parse("01/02/1970 00:00:00"));
            this.surveyCompletedDate = defaultDate.getTime();
            this.requestType = TemplateTypeEnum.REVIEW_REQUEST_NEW.getName(); //Default Review Request Type
        }catch(ParseException e){
            //do nothing
        }
    }
    
    public ReviewRequest(TemplateTypeEnum requestType) {
        try{
            Calendar defaultDate = Calendar.getInstance();
            defaultDate.setTime(new SimpleDateFormat("MM/dd/yyyy HH:mm:ss").parse("01/02/1970 00:00:00"));
            this.surveyCompletedDate = defaultDate.getTime();
            if(requestType == null) {
            	this.requestType = TemplateTypeEnum.REVIEW_REQUEST_NEW.getName(); //Default Review Request Type
            }else {
            	this.requestType = requestType.getName();
            }
        }catch(ParseException e){
            //do nothing
        }
    }
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getRequestDate() {
        return requestDate;
    }

    public void setRequestDate(Date requestDate) {
        this.requestDate = requestDate;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getReminderCount() {
        return reminderCount;
    }

    public void setReminderCount(Integer reminderCount) {
        this.reminderCount = reminderCount;
    }

    public Date getSentOn() {
        return sentOn;
    }

    public void setSentOn(Date sentOn) {
        this.sentOn = sentOn;
    }

    public String getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(String deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    public String getFailureReason() {
        return failureReason;
    }

    public void setFailureReason(String failureReason) {
        this.failureReason = failureReason;
    }

    /**
     * @return the parentRequestId
     */
    public Long getParentRequestId() {
        return parentRequestId;
    }

    /**
     * @param parentRequestId the parentRequestId to set
     */
    public void setParentRequestId(Long parentRequestId) {
        this.parentRequestId = parentRequestId;
    }

    /**
     * @return the templateId
     */
    public Integer getTemplateId() {
        return templateId;
    }

    /**
     * @param templateId the templateId to set
     */
    public void setTemplateId(Integer templateId) {
        this.templateId = templateId;
    }

    public Integer getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    public Integer getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Integer surveyId) {
        this.surveyId = surveyId;
        if(surveyId != null) {
        	this.requestType = TemplateTypeEnum.SURVEY_REQUEST.getName();
        }
    }
    
    public void setSurveyId(Integer surveyId , String source) {
        this.surveyId = surveyId;
        if(surveyId != null) {
        	if(StringUtils.isNotBlank(source) && source.equalsIgnoreCase("sms")) {
        		this.requestType = StringUtils.join(TemplateTypeEnum.SURVEY_REQUEST_SMS.getName(), "_sms");
        	}else {
        		this.requestType = TemplateTypeEnum.SURVEY_REQUEST.getName();
        	}
        	
        }
    }

    public Date getSurveyCompletedDate() {
        return surveyCompletedDate;
    }

    public void setSurveyCompletedDate(Date surveyCompletedDate) {
        this.surveyCompletedDate = surveyCompletedDate;
    }

    public Integer getCheckinId() {
        return checkinId;
    }

    public void setCheckinId(Integer checkinId) {
        this.checkinId = checkinId;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    
    public ReviewRequest getParentRequest() {
        return parentRequest;
    }

    public void setParentRequest(ReviewRequest parentRequest) {
        this.parentRequest = parentRequest;
    }
    
    public String getRequestType() {
		return requestType;
	}

	public void setRequestType(String requestType) {
		this.requestType = requestType;
	}

	/**
	 * @return the custId
	 */
	public Integer getCustId() {
		return custId;
	}

	/**
	 * @param custId the custId to set
	 */
	public void setCustId(Integer custId) {
		this.custId = custId;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("ReviewRequest [id=");
		builder.append(id);
		builder.append(", custId=");
		builder.append(custId);
		builder.append(", requestDate=");
		builder.append(requestDate);
		builder.append(", source=");
		builder.append(source);
		builder.append(", reminderCount=");
		builder.append(reminderCount);
		builder.append(", sentOn=");
		builder.append(sentOn);
		builder.append(", deliveryStatus=");
		builder.append(deliveryStatus);
		builder.append(", failureReason=");
		builder.append(failureReason);
		builder.append(", parentRequestId=");
		builder.append(parentRequestId);
		builder.append(", templateId=");
		builder.append(templateId);
		builder.append(", campaignId=");
		builder.append(campaignId);
		builder.append(", businessId=");
		builder.append(businessId);
		builder.append(", surveyId=");
		builder.append(surveyId);
		builder.append(", surveyCompletedDate=");
		builder.append(surveyCompletedDate);
		builder.append(", checkinId=");
		builder.append(checkinId);
		builder.append(", requestType=");
		builder.append(requestType);
		builder.append("]");
		return builder.toString();
	}
	
	
}
