package com.birdeye.campaign.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.hibernate.annotations.CreationTimestamp;

/**
 * 
 * This class is used to store the account outbound RRs (rr,cx,promotion,...) communication limits along with free trial status.
 * 
 * <AUTHOR>
 *
 */
@Table(name = "account_comm_limits")
@Entity
public class AccountCommLimits implements Serializable {
	
	private static final long	serialVersionUID	= -7405082403717157161L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Integer				id;
	
	@Column(name = "account_id")
	private Integer				accountId;
	
	@Column(name = "rr_limit_daily")
	private Integer				rrLimitDaily;
	
	@Column(name = "rr_limit_all_time")
	private Integer				rrLimitAllTime;
	
	@Column(name = "is_rr_free_trial")
	private Integer				isRRFreeTrial=0;
	
	@Column(name = "created_at")
	@Temporal(TemporalType.TIMESTAMP)
	@CreationTimestamp
	private Date				createdAt;
	
	@Column(name = "updated_at")
	@Temporal(TemporalType.TIMESTAMP)
	@CreationTimestamp
	private Date				updatedAt;
	
	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}


	/**
	 * @param id the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}


	/**
	 * @return the accountId
	 */
	public Integer getAccountId() {
		return accountId;
	}


	/**
	 * @param accountId the accountId to set
	 */
	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}


	/**
	 * @return the rrLimitDaily
	 */
	public Integer getRrLimitDaily() {
		return rrLimitDaily;
	}


	/**
	 * @param rrLimitDaily the rrLimitDaily to set
	 */
	public void setRrLimitDaily(Integer rrLimitDaily) {
		this.rrLimitDaily = rrLimitDaily;
	}


	/**
	 * @return the rrLimitAllTime
	 */
	public Integer getRrLimitAllTime() {
		return rrLimitAllTime;
	}


	/**
	 * @param rrLimitAllTime the rrLimitAllTime to set
	 */
	public void setRrLimitAllTime(Integer rrLimitAllTime) {
		this.rrLimitAllTime = rrLimitAllTime;
	}

	/**
	 * @return the isRRFreeTrial
	 */
	public Integer getIsRRFreeTrial() {
		return isRRFreeTrial;
	}


	/**
	 * @param isRRFreeTrial the isRRFreeTrial to set
	 */
	public void setIsRRFreeTrial(Integer isRRFreeTrial) {
		this.isRRFreeTrial = isRRFreeTrial;
	}


	/**
	 * @return the createdAt
	 */
	public Date getCreatedAt() {
		return createdAt;
	}


	/**
	 * @param createdAt the createdAt to set
	 */
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}


	/**
	 * @return the updatedAt
	 */
	public Date getUpdatedAt() {
		return updatedAt;
	}


	/**
	 * @param updatedAt the updatedAt to set
	 */
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
