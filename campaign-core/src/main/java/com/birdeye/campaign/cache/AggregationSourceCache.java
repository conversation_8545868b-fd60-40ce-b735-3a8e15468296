package com.birdeye.campaign.cache;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.birdeye.campaign.cache.annotation.Cache;
import com.birdeye.campaign.platform.entity.AggregationSource;

@Cache(name = "aggregationSource")
public class AggregationSourceCache {

	private final Map<Integer, AggregationSource> aggregationSourceMap =new ConcurrentHashMap<>();

	public void addAggregationSource(AggregationSource aggregationSource)
	{
		aggregationSourceMap.put(aggregationSource.getId(), aggregationSource);
	}

	public AggregationSource getAggregationSource(Integer id)
	{
		return aggregationSourceMap.get(id);
	}

}