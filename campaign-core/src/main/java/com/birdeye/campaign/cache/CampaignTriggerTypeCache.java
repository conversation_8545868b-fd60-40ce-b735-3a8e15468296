package com.birdeye.campaign.cache;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import com.birdeye.campaign.cache.annotation.Cache;
import com.birdeye.campaign.entity.CampaignTriggerType;

@Cache(name = "campaignTriggerTypeCache")
public class CampaignTriggerTypeCache {
	
	private final Map<Integer, CampaignTriggerType> campaignTriggerTypeMap = new ConcurrentHashMap<>();
	
	public void addCampaignTriggerType(CampaignTriggerType campaignTriggerType) {
		campaignTriggerTypeMap.put(campaignTriggerType.getId(), campaignTriggerType);
	}
	
	public CampaignTriggerType getCampaignTriggerType(Integer id) {
		return campaignTriggerTypeMap.get(id);
	}
	
	public List<CampaignTriggerType> getAllCampaignTriggerType() {
		return campaignTriggerTypeMap.values().stream().collect(Collectors.toList());
	}
	
	public CampaignTriggerType getCampaignTriggerTypeByName(String triggerName) {
		
		return campaignTriggerTypeMap.values().stream().filter(triggerType -> triggerType.getTriggerName().equalsIgnoreCase(triggerName)).findAny().orElse(null);
		
	}
	
	public CampaignTriggerType getCampaignTriggerTypeByLabel(String triggerLabel) {
		
		return campaignTriggerTypeMap.values().stream().filter(triggerType -> triggerType.getTriggerLabel().equalsIgnoreCase(triggerLabel)).findAny().orElse(null);
		
	}
	
}