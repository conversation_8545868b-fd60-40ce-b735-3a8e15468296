/**
 * @file_name DefaultEmailTemplatesCache.java
 * @created_date 25 Feb 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.cache;

import com.birdeye.campaign.cache.annotation.Cache;
import com.birdeye.campaign.entity.EmailTemplate;
import com.birdeye.campaign.entity.EmailTemplateAI;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


@Cache(name = "defaultEmailTemplatesAICache")
public class DefaultEmailTemplatesAICache {
	
	private final Map<String, EmailTemplateAI> properties = new ConcurrentHashMap<>();
	
	public void addEmailTemplate(EmailTemplateAI template) {
		properties.put(template.getType(), template);
	}
	
	public EmailTemplateAI getEmailTemplate(String type) {
		return properties.get(type);
	}
	
}
