/**
 * @file_name DefaultEmailTemplatesCache.java
 * @created_date 25 Feb 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.cache;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.birdeye.campaign.cache.annotation.Cache;
import com.birdeye.campaign.entity.ReferralTemplateData;

/**
 * 
 * <AUTHOR>
 *
 */
@Cache(name = "defaultReferralTemplateDataCache")
public class DefaultReferralTemplateDataCache {
	
	private final Map<String, ReferralTemplateData> properties = new ConcurrentHashMap<>();
	
	public void addTemplateData(ReferralTemplateData templateData) {
		properties.put(templateData.getSource(), templateData);
	}
	
	public ReferralTemplateData getTemplateDataBySource(String source) {
		return properties.get(source);
	}
	
}
