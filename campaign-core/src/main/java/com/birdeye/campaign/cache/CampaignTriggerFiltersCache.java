package com.birdeye.campaign.cache;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import com.birdeye.campaign.cache.annotation.Cache;
import com.birdeye.campaign.entity.CampaignTriggerFilters;

@Cache(name = "campaignTriggerFiltersCache")
public class CampaignTriggerFiltersCache {
	
	private final Map<Integer, CampaignTriggerFilters> campaignTriggerFiltersMap = new ConcurrentHashMap<>();
	
	public void addCampaignTriggerFilters(CampaignTriggerFilters campaignTriggerFilters) {
		campaignTriggerFiltersMap.put(campaignTriggerFilters.getId(), campaignTriggerFilters);
	}
	
	public CampaignTriggerFilters getCampaignTriggerFilters(Integer id) {
		return campaignTriggerFiltersMap.get(id);
	}
	
	public List<CampaignTriggerFilters> getAllCampaignTriggerFilters() {
		return campaignTriggerFiltersMap.values().stream().collect(Collectors.toList());
	}
	
	public List<CampaignTriggerFilters> getCampaignTriggerFiltersByTriggerId(Integer triggerId) {
		
		return campaignTriggerFiltersMap.values().stream().filter(triggerFilter -> triggerFilter.getCampaignTriggerId().equals(triggerId)).collect(Collectors.toList());
		
	}
	
}