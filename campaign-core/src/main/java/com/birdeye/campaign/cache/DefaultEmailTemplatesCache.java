/**
 * @file_name DefaultEmailTemplatesCache.java
 * @created_date 25 Feb 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.cache;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.birdeye.campaign.cache.annotation.Cache;
import com.birdeye.campaign.entity.EmailTemplate;

/**
 * @file_name DefaultEmailTemplatesCache.java
 * @created_date 25 Feb 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@Cache(name = "defaultEmailTemplatesCache")
public class DefaultEmailTemplatesCache {
	
	private final Map<String, EmailTemplate> properties = new ConcurrentHashMap<>();
	
	public void addEmailTemplate(EmailTemplate template) {
		properties.put(template.getType(), template);
	}
	
	public EmailTemplate getEmailTemplate(String type) {
		return properties.get(type);
	}
	
}
