package com.birdeye.campaign.cache;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.collections4.CollectionUtils;

import com.birdeye.campaign.cache.annotation.Cache;
import com.birdeye.campaign.entity.RestrictAppointmentCommunication;

@Cache(name = "restrictAppointmentCommunicationCache")
public class RestrictAppointmentCommunicationCache {
	
	private final Map<Integer, Integer> commRestrictMap = new ConcurrentHashMap<>();
	
	public void addRestriction(RestrictAppointmentCommunication rAppointmentCommunication) {
		commRestrictMap.put(rAppointmentCommunication.getEnterpriseId(), rAppointmentCommunication.getRestrictWindow());
	}
	
	public Integer getRestrictionWindowTimeByEnterpriseId(Integer enterpriseId, Integer defaultWindowTime) {
		return Optional.ofNullable(commRestrictMap.get(enterpriseId)).orElse(defaultWindowTime);
	}
	
	public boolean isEnterpriseIdRestrictedForTimeWindow(Integer enterpriseId) {
		return Optional.ofNullable(commRestrictMap).map(map -> map.containsKey(enterpriseId)).orElse(false);
	}
	
	public Set<Integer> getAppointmentCommRestrictedAccounts(){
		Set<Integer> accounts = commRestrictMap.keySet();
		return CollectionUtils.isNotEmpty(accounts) ? accounts : Collections.emptySet();
	}
}
