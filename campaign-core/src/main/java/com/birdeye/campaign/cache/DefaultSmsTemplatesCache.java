package com.birdeye.campaign.cache;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.birdeye.campaign.cache.annotation.Cache;
import com.birdeye.campaign.entity.BusinessSmsTemplate;

@Cache(name = "defaultSmsTemplatesCache")
public class DefaultSmsTemplatesCache {

	private final Map<String, BusinessSmsTemplate> properties = new ConcurrentHashMap<>();

	public void addSmsTemplate(BusinessSmsTemplate template) {
		properties.put(template.getType(), template);
	}

	public BusinessSmsTemplate getSmsTemplate(String type) {
		return properties.get(type);
	}

}
