/**
 * @file_name RREmailTemplateDTO.java
 * @created_date 5 Apr 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.excel.dto;

import org.codehaus.jackson.map.annotate.JsonSerialize;

import com.birdeye.campaign.constant.ExcelHeaders;
import com.birdeye.campaign.workbook.service.CellType;
import com.birdeye.campaign.workbook.service.ExcelCell;
import com.birdeye.campaign.workbook.service.ExcelData;

/**
 * @file_name RREmailTemplateDTO.java
 * @created_date 5 Apr 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ExcelData(name = "promotionEmailTemplateDTO")
public class PromotionEmailTemplateDTO {
	
	@ExcelCell(order = 1, header = ExcelHeaders.LOCATION_ID, cellType = CellType.TEXT)
	private String	locationId;
	
	@ExcelCell(order = 2, header = ExcelHeaders.TEMPLATE_ID, cellType = CellType.TEXT)
	private String	templateId;
	
	@ExcelCell(order = 3, header = ExcelHeaders.EMAIL_SUBJECT_LINE, cellType = CellType.TEXT)
	private String	emailSubjectLine;
	
	@ExcelCell(order = 4, header = ExcelHeaders.HEADER, cellType = CellType.TEXT)
	private String	header;
	
	@ExcelCell(order = 5, header = ExcelHeaders.MESSAGE, cellType = CellType.TEXT)
	private String	message;
	
	@ExcelCell(order = 6, header = ExcelHeaders.LAST_USED, cellType = CellType.TEXT)
	private String	lastUsed;
	
	@ExcelCell(order = 7, header = ExcelHeaders.CAN_BE_DELETED, cellType = CellType.TEXT)
	private String	canBeDeleted;
	
	/**
	 * @return the locationId
	 */
	public String getLocationId() {
		return locationId;
	}
	
	/**
	 * @param locationId
	 *            the locationId to set
	 */
	public void setLocationId(String locationId) {
		this.locationId = locationId;
	}
	
	/**
	 * @return the templateId
	 */
	public String getTemplateId() {
		return templateId;
	}
	
	/**
	 * @param templateId
	 *            the templateId to set
	 */
	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}
	
	/**
	 * @return the emailSubjectLine
	 */
	public String getEmailSubjectLine() {
		return emailSubjectLine;
	}
	
	/**
	 * @param emailSubjectLine
	 *            the emailSubjectLine to set
	 */
	public void setEmailSubjectLine(String emailSubjectLine) {
		this.emailSubjectLine = emailSubjectLine;
	}
	
	/**
	 * @return the header
	 */
	public String getHeader() {
		return header;
	}
	
	/**
	 * @param header
	 *            the header to set
	 */
	public void setHeader(String header) {
		this.header = header;
	}
	
	/**
	 * @return the message
	 */
	public String getMessage() {
		return message;
	}
	
	/**
	 * @param message
	 *            the message to set
	 */
	public void setMessage(String message) {
		this.message = message;
	}
	
	/**
	 * @return the lastUsed
	 */
	public String getLastUsed() {
		return lastUsed;
	}
	
	/**
	 * @param lastUsed
	 *            the lastUsed to set
	 */
	public void setLastUsed(String lastUsed) {
		this.lastUsed = lastUsed;
	}
	
	/**
	 * @return the canBeDeleted
	 */
	public String getCanBeDeleted() {
		return canBeDeleted;
	}
	
	/**
	 * @param canBeDeleted
	 *            the canBeDeleted to set
	 */
	public void setCanBeDeleted(String canBeDeleted) {
		this.canBeDeleted = canBeDeleted;
	}
	
	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("PromotionEmailTemplateDTO [locationId=");
		builder.append(locationId);
		builder.append(", templateId=");
		builder.append(templateId);
		builder.append(", emailSubjectLine=");
		builder.append(emailSubjectLine);
		builder.append(", header=");
		builder.append(header);
		builder.append(", message=");
		builder.append(message);
		builder.append(", lastUsed=");
		builder.append(lastUsed);
		builder.append(", canBeDeleted=");
		builder.append(canBeDeleted);
		builder.append("]");
		return builder.toString();
	}
	
}
