/**
 * @file_name RREmailTemplateDTO.java
 * @created_date 5 Apr 2019
 * <AUTHOR>
 *
 * This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.excel.dto;

import org.codehaus.jackson.map.annotate.JsonSerialize;

import com.birdeye.campaign.constant.ExcelHeaders;
import com.birdeye.campaign.workbook.service.CellType;
import com.birdeye.campaign.workbook.service.ExcelCell;
import com.birdeye.campaign.workbook.service.ExcelData;

/**
 * @file_name RREmailTemplateDTO.java
 * @created_date 5 Apr 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */

@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ExcelData(name = "rREmailTemplateDTO")
public class RREmailTemplateDTO {
	
	@ExcelCell(order = 1, header = ExcelHeaders.LOCATION_ID, cellType = CellType.TEXT)
	private String	locationId;
	
	@ExcelCell(order = 2, header = ExcelHeaders.TEMPLATE_ID, cellType = CellType.TEXT)
	private String	templateId;
	
	@ExcelCell(order = 3, header = ExcelHeaders.EMAIL_SUBJECT_LINE, cellType = CellType.TEXT)
	private String	emailSubjectLine;
	
	@ExcelCell(order = 4, header = ExcelHeaders.HEADER, cellType = CellType.TEXT)
	private String	header;
	
	@ExcelCell(order = 5, header = ExcelHeaders.MESSAGE, cellType = CellType.TEXT)
	private String	message;
	
	@ExcelCell(order = 6, header = ExcelHeaders.REVIEW_SITE_1, cellType = CellType.TEXT)
	private String	reviewSite1;
	
	@ExcelCell(order = 7, header = ExcelHeaders.REVIEW_SITE_2, cellType = CellType.TEXT)
	private String	reviewSite2;
	
	@ExcelCell(order = 8, header = ExcelHeaders.REVIEW_SITE_3, cellType = CellType.TEXT)
	private String	reviewSite3;
	
	@ExcelCell(order = 9, header = ExcelHeaders.FEEDBACK_MESSAGE, cellType = CellType.TEXT)
	private String	feedbackMessage;
	
	@ExcelCell(order = 10, header = ExcelHeaders.SHOW_CONTACT_US_OPTION, cellType = CellType.TEXT)
	private String	showContactUsOption;
	
	@ExcelCell(order = 11, header = ExcelHeaders.CONTACT_US_MESSAGE, cellType = CellType.TEXT)
	private String	contactUsMessage;
	
	@ExcelCell(order = 12, header = ExcelHeaders.CONTACT_US_CUSTOM_URL, cellType = CellType.TEXT)
	private String	contactUsCustomUrl;
	
	@ExcelCell(order = 13, header = ExcelHeaders.SEND_REMINDER_EMAIL, cellType = CellType.TEXT)
	private String	sendReminderEmail;
	
	@ExcelCell(order = 14, header = ExcelHeaders.SEND_REMINDER_SUBJECT, cellType = CellType.TEXT)
	private String	sendReminderSubject;
	
	@ExcelCell(order = 15, header = ExcelHeaders.SEND_REMINDER_AFTER, cellType = CellType.TEXT)
	private String	sendReminderAfter;
	
	@ExcelCell(order = 16, header = ExcelHeaders.NO_OF_REMINDER, cellType = CellType.TEXT)
	private String	noOfReminder;
	
	@ExcelCell(order = 17, header = ExcelHeaders.LAST_USED, cellType = CellType.TEXT)
	private String	lastUsed;
	
	@ExcelCell(order = 18, header = ExcelHeaders.CAN_BE_DELETED, cellType = CellType.TEXT)
	private String	canBeDeleted;
	
	/**
	 * @return the locationId
	 */
	public String getLocationId() {
		return locationId;
	}
	
	/**
	 * @param locationId
	 *            the locationId to set
	 */
	public void setLocationId(String locationId) {
		this.locationId = locationId;
	}
	
	/**
	 * @return the templateId
	 */
	public String getTemplateId() {
		return templateId;
	}
	
	/**
	 * @param templateId
	 *            the templateId to set
	 */
	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}
	
	/**
	 * @return the emailSubjectLine
	 */
	public String getEmailSubjectLine() {
		return emailSubjectLine;
	}
	
	/**
	 * @param emailSubjectLine
	 *            the emailSubjectLine to set
	 */
	public void setEmailSubjectLine(String emailSubjectLine) {
		this.emailSubjectLine = emailSubjectLine;
	}
	
	/**
	 * @return the header
	 */
	public String getHeader() {
		return header;
	}
	
	/**
	 * @param header
	 *            the header to set
	 */
	public void setHeader(String header) {
		this.header = header;
	}
	
	/**
	 * @return the message
	 */
	public String getMessage() {
		return message;
	}
	
	/**
	 * @param message
	 *            the message to set
	 */
	public void setMessage(String message) {
		this.message = message;
	}
	
	/**
	 * @return the reviewSite1
	 */
	public String getReviewSite1() {
		return reviewSite1;
	}
	
	/**
	 * @param reviewSite1
	 *            the reviewSite1 to set
	 */
	public void setReviewSite1(String reviewSite1) {
		this.reviewSite1 = reviewSite1;
	}
	
	/**
	 * @return the reviewSite2
	 */
	public String getReviewSite2() {
		return reviewSite2;
	}
	
	/**
	 * @param reviewSite2
	 *            the reviewSite2 to set
	 */
	public void setReviewSite2(String reviewSite2) {
		this.reviewSite2 = reviewSite2;
	}
	
	/**
	 * @return the reviewSite3
	 */
	public String getReviewSite3() {
		return reviewSite3;
	}
	
	/**
	 * @param reviewSite3
	 *            the reviewSite3 to set
	 */
	public void setReviewSite3(String reviewSite3) {
		this.reviewSite3 = reviewSite3;
	}
	
	/**
	 * @return the feedbackMessage
	 */
	public String getFeedbackMessage() {
		return feedbackMessage;
	}
	
	/**
	 * @param feedbackMessage
	 *            the feedbackMessage to set
	 */
	public void setFeedbackMessage(String feedbackMessage) {
		this.feedbackMessage = feedbackMessage;
	}
	
	/**
	 * @return the showContactUsOption
	 */
	public String getShowContactUsOption() {
		return showContactUsOption;
	}
	
	/**
	 * @param showContactUsOption
	 *            the showContactUsOption to set
	 */
	public void setShowContactUsOption(String showContactUsOption) {
		this.showContactUsOption = showContactUsOption;
	}
	
	/**
	 * @return the contactUsMessage
	 */
	public String getContactUsMessage() {
		return contactUsMessage;
	}
	
	/**
	 * @param contactUsMessage
	 *            the contactUsMessage to set
	 */
	public void setContactUsMessage(String contactUsMessage) {
		this.contactUsMessage = contactUsMessage;
	}
	
	/**
	 * @return the contactUsCustomUrl
	 */
	public String getContactUsCustomUrl() {
		return contactUsCustomUrl;
	}
	
	/**
	 * @param contactUsCustomUrl
	 *            the contactUsCustomUrl to set
	 */
	public void setContactUsCustomUrl(String contactUsCustomUrl) {
		this.contactUsCustomUrl = contactUsCustomUrl;
	}
	
	/**
	 * @return the sendReminderEmail
	 */
	public String getSendReminderEmail() {
		return sendReminderEmail;
	}
	
	/**
	 * @param sendReminderEmail
	 *            the sendReminderEmail to set
	 */
	public void setSendReminderEmail(String sendReminderEmail) {
		this.sendReminderEmail = sendReminderEmail;
	}
	
	/**
	 * @return the sendReminderSubject
	 */
	public String getSendReminderSubject() {
		return sendReminderSubject;
	}
	
	/**
	 * @param sendReminderSubject
	 *            the sendReminderSubject to set
	 */
	public void setSendReminderSubject(String sendReminderSubject) {
		this.sendReminderSubject = sendReminderSubject;
	}
	
	/**
	 * @return the sendReminderAfter
	 */
	public String getSendReminderAfter() {
		return sendReminderAfter;
	}
	
	/**
	 * @param sendReminderAfter
	 *            the sendReminderAfter to set
	 */
	public void setSendReminderAfter(String sendReminderAfter) {
		this.sendReminderAfter = sendReminderAfter;
	}
	
	/**
	 * @return the noOfReminder
	 */
	public String getNoOfReminder() {
		return noOfReminder;
	}
	
	/**
	 * @param noOfReminder
	 *            the noOfReminder to set
	 */
	public void setNoOfReminder(String noOfReminder) {
		this.noOfReminder = noOfReminder;
	}
	
	/**
	 * @return the lastUsed
	 */
	public String getLastUsed() {
		return lastUsed;
	}
	
	/**
	 * @param lastUsed
	 *            the lastUsed to set
	 */
	public void setLastUsed(String lastUsed) {
		this.lastUsed = lastUsed;
	}
	
	/**
	 * @return the canBeDeleted
	 */
	public String getCanBeDeleted() {
		return canBeDeleted;
	}
	
	/**
	 * @param canBeDeleted
	 *            the canBeDeleted to set
	 */
	public void setCanBeDeleted(String canBeDeleted) {
		this.canBeDeleted = canBeDeleted;
	}
	
}
