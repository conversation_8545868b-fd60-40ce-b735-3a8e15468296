package com.birdeye.campaign.ai.constant;

import org.apache.commons.lang3.StringUtils;

public enum LandingPageTypeEnum {
    DIRECT_FEEDBACK("direct_feedback", "Direct Feedback"),
    DIRECT_FEEDBACK_TY("direct_feedback_ty", "Direct Feedback Thank You"),
    REVIEW_REQUEST("review_request", "Review Request");

    private final String name;
    private final String label;

    private LandingPageTypeEnum(String name, String label) {
        this.name = name;
        this.label = label;
    }

    public String getName() {
        return name;
    }

    public String getLabel() {
        return label;
    }

    public static LandingPageTypeEnum getLandingPageTypeEnumByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (LandingPageTypeEnum typeEnum : values()) {
            if (StringUtils.equalsIgnoreCase(typeEnum.getName(), name)) {
                return typeEnum;
            }
        }
        return null;
    }
}