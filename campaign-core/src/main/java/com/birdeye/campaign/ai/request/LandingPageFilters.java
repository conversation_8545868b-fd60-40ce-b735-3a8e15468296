package com.birdeye.campaign.ai.request;

import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class LandingPageFilters {
	
	private String searchStr;
	private List<String> landingPageStatus; // ["in_use", "not_in_use"]
	private List<String> landingPageType; // ["review_request", "direct_feedback"]
	private Integer page;
	private Integer size;
	private Integer sortOrder; // '1' for descending & '0' for ascending order
	private String sortBy;
	
	/**
	 * @return the searchStr
	 */
	public String getSearchStr() {
		return searchStr;
	}
	
	/**
	 * @param searchStr the searchStr to set
	 */
	public void setSearchStr(String searchStr) {
		this.searchStr = searchStr;
	}
	
	/**
	 * @return the landingPageStatus
	 */
	public List<String> getLandingPageStatus() {
		return landingPageStatus;
	}
	
	/**
	 * @param landingPageStatus the landingPageStatus to set
	 */
	public void setLandingPageStatus(List<String> landingPageStatus) {
		this.landingPageStatus = landingPageStatus;
	}
	
	/**
	 * @return the landingPageType
	 */
	public List<String> getLandingPageType() {
		return landingPageType;
	}
	
	/**
	 * @param landingPageType the landingPageType to set
	 */
	public void setLandingPageType(List<String> landingPageType) {
		this.landingPageType = landingPageType;
	}
	
	/**
	 * @return the page
	 */
	public Integer getPage() {
		return page;
	}
	
	/**
	 * @param page the page to set
	 */
	public void setPage(Integer page) {
		this.page = page;
	}
	
	/**
	 * @return the size
	 */
	public Integer getSize() {
		return size;
	}
	
	/**
	 * @param size the size to set
	 */
	public void setSize(Integer size) {
		this.size = size;
	}
	
	/**
	 * @return the sortOrder
	 */
	public Integer getSortOrder() {
		return sortOrder;
	}
	
	/**
	 * @param sortOrder the sortOrder to set
	 */
	public void setSortOrder(Integer sortOrder) {
		this.sortOrder = sortOrder;
	}
	
	/**
	 * @return the sortBy
	 */
	public String getSortBy() {
		return sortBy;
	}
	
	/**
	 * @param sortBy the sortBy to set
	 */
	public void setSortBy(String sortBy) {
		this.sortBy = sortBy;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
}
