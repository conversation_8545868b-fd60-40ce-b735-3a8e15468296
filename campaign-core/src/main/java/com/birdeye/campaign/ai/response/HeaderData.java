package com.birdeye.campaign.ai.response;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
public class HeaderData implements Serializable {
	private static final long	serialVersionUID	= 1433351272020313626L;
	
	private int					order;
	private String				value;
	private String				label;
	private boolean				enabled;
	private boolean				sortable;
	private boolean				fixed;
	
	public HeaderData() {
		
	}
	
	public HeaderData(int order, String value, String label, boolean enabled, boolean sortable, boolean fixed) {
		this.order = order;
		this.value = value;
		this.label = label;
		this.enabled = enabled;
		this.sortable = sortable;
		this.fixed = fixed;
	}
	
	/**
	 * @return the order
	 */
	public int getOrder() {
		return order;
	}
	
	/**
	 * @param order
	 *            the order to set
	 */
	public void setOrder(int order) {
		this.order = order;
	}
	
	/**
	 * @return the value
	 */
	public String getValue() {
		return value;
	}
	
	/**
	 * @param value
	 *            the value to set
	 */
	public void setValue(String value) {
		this.value = value;
	}
	
	/**
	 * @return the label
	 */
	public String getLabel() {
		return label;
	}
	
	/**
	 * @param label
	 *            the label to set
	 */
	public void setLabel(String label) {
		this.label = label;
	}
	
	/**
	 * @return the enabled
	 */
	public boolean isEnabled() {
		return enabled;
	}
	
	/**
	 * @param enabled
	 *            the enabled to set
	 */
	public void setEnabled(boolean enabled) {
		this.enabled = enabled;
	}
	
	/**
	 * @return the sortable
	 */
	public boolean isSortable() {
		return sortable;
	}
	
	/**
	 * @param sortable
	 *            the sortable to set
	 */
	public void setSortable(boolean sortable) {
		this.sortable = sortable;
	}
	
	/**
	 * @return the fixed
	 */
	public boolean isFixed() {
		return fixed;
	}
	
	/**
	 * @param fixed
	 *            the fixed to set
	 */
	public void setFixed(boolean fixed) {
		this.fixed = fixed;
	}
}
