package com.birdeye.campaign.ai.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EmailTemplateAIMetadata implements Serializable {
	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1287759023824973168L;
	
	private Integer				locationBrandingEnabled;
	
	private Integer				showRating;
	
	private Integer				showReviewCount;
	
	public EmailTemplateAIMetadata() {
		
	}
	
	public EmailTemplateAIMetadata(Integer locationBrandingEnabled, Integer showRating, Integer showReviewCount) {
		this.locationBrandingEnabled = locationBrandingEnabled;
		this.showRating = showRating;
		this.showReviewCount = showReviewCount;
	}
	
	public Integer getLocationBrandingEnabled() {
		return locationBrandingEnabled;
	}
	
	public void setLocationBrandingEnabled(Integer locationBrandingEnabled) {
		this.locationBrandingEnabled = locationBrandingEnabled;
	}
	
	public Integer getShowRating() {
		return showRating;
	}
	
	public void setShowRating(Integer showRating) {
		this.showRating = showRating;
	}
	
	public Integer getShowReviewCount() {
		return showReviewCount;
	}
	
	public void setShowReviewCount(Integer showReviewCount) {
		this.showReviewCount = showReviewCount;
	}
	
	@Override
	public String toString() {
		ReflectionToStringBuilder sb = new ReflectionToStringBuilder(this, ToStringStyle.JSON_STYLE);
		return sb.toString();
	}
}
