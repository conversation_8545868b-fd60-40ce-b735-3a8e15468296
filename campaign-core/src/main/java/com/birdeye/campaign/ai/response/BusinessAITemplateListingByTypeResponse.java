package com.birdeye.campaign.ai.response;

import java.io.Serializable;
import java.util.List;

import com.birdeye.campaign.dto.TemplateCustomFieldSRO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
public class BusinessAITemplateListingByTypeResponse extends BusinessAITemplateBaseResponse implements Serializable {
	
	private static final long				serialVersionUID	= -7974220005528470412L;
	
	private Integer							nonAITemplateId;
	private boolean							attachmentExists;
	private String							editable;
	private String							nonEditable;
	private String							mediaUrl;
	private Long							lastUsedTime;
	private String							unsubscribeText;
	private List<TemplateCustomFieldSRO>	selectedCustomFields;
	private Long							smsSegments;
	private Integer							includeUnsubscribeText;
	private List<Integer>					selectedLocations;
	private Boolean							formUrlPresent;
	private List<TemplateCustomFieldSRO>	appointmentCustomFields;
	private Integer							isDefaultTemplate	= 0;
	private Boolean							isGlobalTemplate	= false;
	private List<TemplateCustomFieldSRO>	locationCustomFields;
	private Integer							includeImageWithText;
	
	/**
	 * @return the nonAITemplateId
	 */
	public Integer getNonAITemplateId() {
		return nonAITemplateId;
	}

	/**
	 * @param nonAITemplateId the nonAITemplateId to set
	 */
	public void setNonAITemplateId(Integer nonAITemplateId) {
		this.nonAITemplateId = nonAITemplateId;
	}

	/**
	 * @return the attachmentExists
	 */
	public boolean isAttachmentExists() {
		return attachmentExists;
	}
	
	/**
	 * @param attachmentExists
	 *            the attachmentExists to set
	 */
	public void setAttachmentExists(boolean attachmentExists) {
		this.attachmentExists = attachmentExists;
	}
	
	/**
	 * @return the editable
	 */
	public String getEditable() {
		return editable;
	}
	
	/**
	 * @param editable
	 *            the editable to set
	 */
	public void setEditable(String editable) {
		this.editable = editable;
	}
	
	/**
	 * @return the nonEditable
	 */
	public String getNonEditable() {
		return nonEditable;
	}
	
	/**
	 * @param nonEditable
	 *            the nonEditable to set
	 */
	public void setNonEditable(String nonEditable) {
		this.nonEditable = nonEditable;
	}
	
	/**
	 * @return the mediaUrl
	 */
	public String getMediaUrl() {
		return mediaUrl;
	}
	
	/**
	 * @param mediaUrl
	 *            the mediaUrl to set
	 */
	public void setMediaUrl(String mediaUrl) {
		this.mediaUrl = mediaUrl;
	}
	
	/**
	 * @return the lastUsedTime
	 */
	public Long getLastUsedTime() {
		return lastUsedTime;
	}
	
	/**
	 * @param lastUsedTime
	 *            the lastUsedTime to set
	 */
	public void setLastUsedTime(Long lastUsedTime) {
		this.lastUsedTime = lastUsedTime;
	}
	
	/**
	 * @return the unsubscribeText
	 */
	public String getUnsubscribeText() {
		return unsubscribeText;
	}
	
	/**
	 * @param unsubscribeText
	 *            the unsubscribeText to set
	 */
	public void setUnsubscribeText(String unsubscribeText) {
		this.unsubscribeText = unsubscribeText;
	}
	
	/**
	 * @return the selectedCustomFields
	 */
	public List<TemplateCustomFieldSRO> getSelectedCustomFields() {
		return selectedCustomFields;
	}
	
	/**
	 * @param selectedCustomFields
	 *            the selectedCustomFields to set
	 */
	public void setSelectedCustomFields(List<TemplateCustomFieldSRO> selectedCustomFields) {
		this.selectedCustomFields = selectedCustomFields;
	}
	
	/**
	 * @return the smsSegments
	 */
	public Long getSmsSegments() {
		return smsSegments;
	}
	
	/**
	 * @param smsSegments
	 *            the smsSegments to set
	 */
	public void setSmsSegments(Long smsSegments) {
		this.smsSegments = smsSegments;
	}
	
	/**
	 * @return the includeUnsubscribeText
	 */
	public Integer getIncludeUnsubscribeText() {
		return includeUnsubscribeText;
	}
	
	/**
	 * @param includeUnsubscribeText
	 *            the includeUnsubscribeText to set
	 */
	public void setIncludeUnsubscribeText(Integer includeUnsubscribeText) {
		this.includeUnsubscribeText = includeUnsubscribeText;
	}
	
	/**
	 * @return the selectedLocations
	 */
	public List<Integer> getSelectedLocations() {
		return selectedLocations;
	}
	
	/**
	 * @param selectedLocations
	 *            the selectedLocations to set
	 */
	public void setSelectedLocations(List<Integer> selectedLocations) {
		this.selectedLocations = selectedLocations;
	}
	
	/**
	 * @return the formUrlPresent
	 */
	public Boolean getFormUrlPresent() {
		return formUrlPresent;
	}
	
	/**
	 * @param formUrlPresent
	 *            the formUrlPresent to set
	 */
	public void setFormUrlPresent(Boolean formUrlPresent) {
		this.formUrlPresent = formUrlPresent;
	}
	
	/**
	 * @return the appointmentCustomFields
	 */
	public List<TemplateCustomFieldSRO> getAppointmentCustomFields() {
		return appointmentCustomFields;
	}
	
	/**
	 * @param appointmentCustomFields
	 *            the appointmentCustomFields to set
	 */
	public void setAppointmentCustomFields(List<TemplateCustomFieldSRO> appointmentCustomFields) {
		this.appointmentCustomFields = appointmentCustomFields;
	}
	
	/**
	 * @return the isDefaultTemplate
	 */
	public Integer getIsDefaultTemplate() {
		return isDefaultTemplate;
	}
	
	/**
	 * @param isDefaultTemplate
	 *            the isDefaultTemplate to set
	 */
	public void setIsDefaultTemplate(Integer isDefaultTemplate) {
		this.isDefaultTemplate = isDefaultTemplate;
	}
	
	/**
	 * @return the isGlobalTemplate
	 */
	public Boolean getIsGlobalTemplate() {
		return isGlobalTemplate;
	}
	
	/**
	 * @param isGlobalTemplate
	 *            the isGlobalTemplate to set
	 */
	public void setIsGlobalTemplate(Boolean isGlobalTemplate) {
		this.isGlobalTemplate = isGlobalTemplate;
	}
	
	/**
	 * @return the locationCustomFields
	 */
	public List<TemplateCustomFieldSRO> getLocationCustomFields() {
		return locationCustomFields;
	}
	
	/**
	 * @param locationCustomFields
	 *            the locationCustomFields to set
	 */
	public void setLocationCustomFields(List<TemplateCustomFieldSRO> locationCustomFields) {
		this.locationCustomFields = locationCustomFields;
	}
	
	/**
	 * @return the includeImageWithText
	 */
	public Integer getIncludeImageWithText() {
		return includeImageWithText;
	}
	
	/**
	 * @param includeImageWithText
	 *            the includeImageWithText to set
	 */
	public void setIncludeImageWithText(Integer includeImageWithText) {
		this.includeImageWithText = includeImageWithText;
	}
	
}
