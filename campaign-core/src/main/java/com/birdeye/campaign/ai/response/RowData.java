package com.birdeye.campaign.ai.response;

import java.io.Serializable;

public class RowData implements Serializable {
	private static final long	serialVersionUID	= 4594501507922078946L;
	
	private Integer				order;
	private String				value;
	private String				label;
	
	public RowData() {
		
	}
	
	public RowData(Integer order, String value, String label) {
		this.order = order;
		this.value = value;
		this.label = label;
	}
	
	/**
	 * @return the order
	 */
	public Integer getOrder() {
		return order;
	}
	
	/**
	 * @param order
	 *            the order to set
	 */
	public void setOrder(Integer order) {
		this.order = order;
	}
	
	/**
	 * @return the value
	 */
	public String getValue() {
		return value;
	}
	
	/**
	 * @param value
	 *            the value to set
	 */
	public void setValue(String value) {
		this.value = value;
	}
	
	/**
	 * @return the label
	 */
	public String getLabel() {
		return label;
	}
	
	/**
	 * @param label
	 *            the label to set
	 */
	public void setLabel(String label) {
		this.label = label;
	}
	
}
