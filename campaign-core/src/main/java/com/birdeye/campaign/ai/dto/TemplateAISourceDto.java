package com.birdeye.campaign.ai.dto;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.birdeye.campaign.entity.TemplateReviewSourceMapping;

public class TemplateAISourceDto {
	
	private Integer	id;			// Id of entry in TemplateReviewSourceMapping
	
	private Integer	sourceId;
	
	private Integer	assoicatedEntityId;
	
	private Integer	accountId;
	
	private String	deviceType;
	
	private String	sourceAlias;
	
	private String  assEntityType;
	
	private Integer supported;
	
	public TemplateAISourceDto() {
		
	}
	
	public TemplateAISourceDto(Integer sourceId, Integer assoicatedEntityId, Integer accountId, String deviceType, String sourceAlias, String assEntityType, Integer supported) {
		this.sourceId = sourceId;
		this.assoicatedEntityId = assoicatedEntityId;
		this.accountId = accountId;
		this.deviceType = deviceType;
		this.sourceAlias = sourceAlias;
		this.assEntityType = assEntityType;
		this.supported = supported;
	}
	
	public TemplateAISourceDto(TemplateReviewSourceMapping mapping) {
		this.id = mapping.getId();
		this.sourceId = mapping.getSourceId();
		this.assoicatedEntityId = mapping.getAssociatedEntityId();
		this.accountId = mapping.getAccountId();
		this.deviceType = mapping.getDeviceType();
		this.sourceAlias = mapping.getSourceAlias();
		this.assEntityType = mapping.getAssociatedEntity();
		this.supported = mapping.getSupported();
	}
	
	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}

	/**
	 * @return the sourceId
	 */
	public Integer getSourceId() {
		return sourceId;
	}
	
	/**
	 * @param sourceId
	 *            the sourceId to set
	 */
	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}
	
	/**
	 * @return the assoicatedEntityId
	 */
	public Integer getAssoicatedEntityId() {
		return assoicatedEntityId;
	}

	/**
	 * @param assoicatedEntityId the assoicatedEntityId to set
	 */
	public void setAssoicatedEntityId(Integer assoicatedEntityId) {
		this.assoicatedEntityId = assoicatedEntityId;
	}

	/**
	 * @return the accountId
	 */
	public Integer getAccountId() {
		return accountId;
	}
	
	/**
	 * @param accountId
	 *            the accountId to set
	 */
	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}
	
	/**
	 * @return the deviceType
	 */
	public String getDeviceType() {
		return deviceType;
	}
	
	/**
	 * @param deviceType
	 *            the deviceType to set
	 */
	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}
	
	/**
	 * @return the sourceAlias
	 */
	public String getSourceAlias() {
		return sourceAlias;
	}
	
	/**
	 * @param sourceAlias
	 *            the sourceAlias to set
	 */
	public void setSourceAlias(String sourceAlias) {
		this.sourceAlias = sourceAlias;
	}
	
	/**
	 * @return the assEntityType
	 */
	public String getAssEntityType() {
		return assEntityType;
	}

	/**
	 * @param assEntityType the assEntityType to set
	 */
	public void setAssEntityType(String assEntityType) {
		this.assEntityType = assEntityType;
	}

	/**
	 * @return the supported
	 */
	public Integer getSupported() {
		return supported;
	}

	/**
	 * @param supported the supported to set
	 */
	public void setSupported(Integer supported) {
		this.supported = supported;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
