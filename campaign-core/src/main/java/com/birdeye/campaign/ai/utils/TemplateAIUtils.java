package com.birdeye.campaign.ai.utils;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import com.birdeye.campaign.ai.response.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import com.birdeye.campaign.ai.constant.AIAssociatedEntityEnum;
import com.birdeye.campaign.ai.constant.AITemplateTypeEnum;
import com.birdeye.campaign.ai.dto.EmailTemplateAIMetadata;
import com.birdeye.campaign.ai.dto.TemplateAISourceDto;
import com.birdeye.campaign.ai.request.AITemplateFilters;
import com.birdeye.campaign.ai.sro.CustomFieldSRO;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.dto.BusinessAITemplateEntity;
import com.birdeye.campaign.dto.BusinessAITemplateMessage;
import com.birdeye.campaign.dto.BusinessAllAITemplatesMessage;
import com.birdeye.campaign.entity.EmailTemplateAI;
import com.birdeye.campaign.entity.TemplateAICustomFieldAssociation;
import com.birdeye.campaign.entity.TemplateReviewSourceMapping;
import com.birdeye.campaign.enums.CustomFieldSourceEnum;
import com.birdeye.campaign.platform.constant.CommunicationCategoryEnum;
import com.birdeye.campaign.platform.constant.CustomFieldAssociatedObjectTypeEnum;
import com.birdeye.campaign.utils.TemplateUtils;
import com.birdeye.email.templates.ai.request.EmailTemplateAISaveRequest;
import com.birdeye.templates.ai.sro.TemplateAIReviewSourceSRO;

/**
 * Utility class for handling operations related to AI templates.
 */
public class TemplateAIUtils {
    
    private TemplateAIUtils(){
        
    }
	
	public static final Integer HUNDERD = 100;

    /**
     * Checks if the provided templateId indicates a new template.
     *
     * @param templateId the ID of the template to check
     * @return true if the template is new (i.e., templateId is null or 0), false otherwise
     */
    public static boolean isNewTemplate(Integer templateId) {
        // A new template is identified by an empty or null templateId
        return templateId == null || templateId ==0;
    }
    
	public static void setFieldsInTemplateSaveRequest(Integer userId, Integer accountId, String type, Integer templateId, EmailTemplateAISaveRequest request) {
		request.setAccountId(accountId);
		request.setUserId(userId);
		request.setTemplateId(templateId);
		request.setTemplateType(type);
	}
    
    /**
	 * Prepares the generic email template body by populating the provided template object
	 * with details from the request.
	 *
	 * @param template
	 *            The email template object to populate.
	 * @param request
	 *            The request containing template details.
	 * @param templateId
	 *            The ID of the template.
	 * @param userId
	 *            The ID of the user making the request.
	 * @param type
	 *            The type of the email template.
	 */
	public static void prepareGenericEmailTemplateBody(EmailTemplateAI template, EmailTemplateAISaveRequest request, String type) {
		if (isNewTemplate(request.getTemplateId())) {
			template.setCreatedBy(request.getUserId());
			template.setCreatedAt(new Date());
		}
		template.setUpdatedBy(request.getUserId());
		template.setHtmlContent(request.getHtml());
		template.setSubject(request.getSubject());
		template.setBrandkitId(request.getBrandkitId());
		template.setThumbnailUrl(request.getThumbnailUrl());
		template.setIsDeleted(0);
		template.setLocationLevelTemplate(request.getLocationTemplate());
		template.setEnableReplyToInbox(request.getEnableReplyToInbox());
		template.setEmailCategory(CommunicationCategoryEnum.getCommunicationCategoryByTemplateType(type, request.getTemplateCategory()).getCommunicationCategory());
		template.setMetadata(new EmailTemplateAIMetadata(request.getMetadata().getLocationBrandingEnabled(), request.getMetadata().getShowRating(), request.getMetadata().getShowReviewCount()));
	}
	
	/**
	 * Updates the review sources for a template by determining which sources to add and remove.
	 *
	 * @param accountId
	 *            The ID of the account associated with the template.
	 * @param templateId
	 *            The ID of the template.
	 * @param deviceType
	 *            The type of device (e.g., web, mobile).
	 * @param selectedSources
	 *            The list of selected review sources.
	 * @param oldDeeplinkSources
	 *            The list of existing review source mappings.
	 * @param associatedObjectEnum
	 *            The associated entity type.
	 * @return A map containing lists of sources to add and remove.
	 */
	private static Map<String, List<TemplateAISourceDto>> updateReviewSourcesForATemplate(Integer accountId, Integer templateId, String deviceType, List<TemplateAIReviewSourceSRO> selectedSources,
			List<TemplateAISourceDto> oldDeeplinkSources, AIAssociatedEntityEnum associatedObjectEnum) {
		Map<String, List<TemplateAISourceDto>> resultMap = new HashMap<>();
		Map<Integer, TemplateAISourceDto> existingSourceIdtoSourceMap = oldDeeplinkSources.stream()
				.collect(Collectors.toMap(source -> source.getSourceId(), source -> source, (first, second) -> second));
		
		List<TemplateAISourceDto> newSourcesList = new ArrayList<>();
		
		for (TemplateAIReviewSourceSRO reviewSource : selectedSources) {
			TemplateAISourceDto source = existingSourceIdtoSourceMap.remove(reviewSource.getSourceId());
			if (source == null) {
				source = new TemplateAISourceDto(reviewSource.getSourceId(), templateId, accountId, deviceType,
						(Objects.equals(reviewSource.getSourceId(), HUNDERD) && StringUtils.isNotBlank(reviewSource.getSourceAlias())) ? reviewSource.getSourceAlias() : null,
						associatedObjectEnum.getAssociatedEntityType(), reviewSource.getSupported());
				newSourcesList.add(source);
			} else if (Objects.equals(reviewSource.getSourceId(), HUNDERD) && StringUtils.isNotBlank(reviewSource.getSourceAlias())
					&& BooleanUtils.isFalse(StringUtils.equals(reviewSource.getSourceAlias(), source.getSourceAlias()))) {
				source.setSourceAlias(reviewSource.getSourceAlias());
				newSourcesList.add(source);
			}
		}
		
		if (CollectionUtils.isNotEmpty(newSourcesList)) {
			resultMap.put("addSourceList", newSourcesList);
		}
		if (MapUtils.isNotEmpty(existingSourceIdtoSourceMap)) {
			resultMap.put("removeSourceList", existingSourceIdtoSourceMap.values().stream().collect(Collectors.toList()));
		}
		return resultMap;	
	}
	
	/**
	 * Prepares and retrieves the updated review sources for a template.
	 *
	 * @param accountId
	 *            The ID of the account associated with the template.
	 * @param templateId
	 *            The ID of the template.
	 * @param deviceType
	 *            The type of device (e.g., web, mobile).
	 * @param selectedSources
	 *            The list of selected review sources.
	 * @param oldDeeplinkSources
	 *            The list of existing review source mappings.
	 * @param associatedObjectEnum
	 *            The associated entity type.
	 * @return A map containing lists of sources to add and remove.
	 */
	public static Map<String, List<TemplateAISourceDto>> prepareAndGetUpdatedReviewSourceForTemplate(Integer accountId, Integer templateId, String deviceType,
			List<TemplateAIReviewSourceSRO> selectedSources, List<TemplateReviewSourceMapping> oldDeeplinkSources, AIAssociatedEntityEnum associatedObjectEnum) {
		List<TemplateAISourceDto> existingDeeplinkSources = (CollectionUtils.isNotEmpty(oldDeeplinkSources))
				? oldDeeplinkSources.stream().map(source -> new TemplateAISourceDto(source)).collect(Collectors.toList())
				: new ArrayList<>();
		return updateReviewSourcesForATemplate(accountId, templateId, deviceType, selectedSources, existingDeeplinkSources, associatedObjectEnum);
	}
	
	/**
	 * Converts a list of DTOs to a list of review source mapping entities.
	 *
	 * @param mappingDtoList
	 *            The list of DTOs to convert.
	 * @return A list of TemplateReviewSourceMapping entities.
	 */
	public static List<TemplateReviewSourceMapping> getEntityFromDto(List<TemplateAISourceDto> mappingDtoList) {
		if (CollectionUtils.isEmpty(mappingDtoList)) {
			return new ArrayList<>();
		}
		return mappingDtoList.stream().map(source -> new TemplateReviewSourceMapping(source)).collect(Collectors.toList());
	}
	
	/**
	 * Prepares a list of custom field association entities from the provided SROs.
	 *
	 * @param customFieldsList
	 *            The list of custom field SROs.
	 * @param templateId
	 *            The ID of the template.
	 * @param sourceType
	 *            The type of the source (e.g., email, SMS).
	 * @param userId
	 *            The ID of the user making the request.
	 * @param enumObj
	 *            The source enum for the custom fields.
	 * @return A list of TemplateAICustomFieldAssociation entities.
	 */
	public static List<TemplateAICustomFieldAssociation> prepareCustomFieldAssociationEntityFromSro(List<CustomFieldSRO> customFieldsList, Integer templateId, String sourceType, Integer userId,
			CustomFieldSourceEnum enumObj) {
		List<TemplateAICustomFieldAssociation> customFieldAssocnList = new ArrayList<>();
		for (CustomFieldSRO customField : customFieldsList) {
			TemplateAICustomFieldAssociation fieldAssociation = new TemplateAICustomFieldAssociation();
			fieldAssociation.setAssociatedObjectId(templateId);
			fieldAssociation.setAssociatedType(CustomFieldAssociatedObjectTypeEnum.getObjectTypeByCommType(sourceType));
			fieldAssociation.setCustomFieldName(customField.getName());
			fieldAssociation.setCustomFieldId(customField.getId());
			fieldAssociation.setCustomFieldLabel(replaceSpaceWithUnderScore(customField.getName()));
			fieldAssociation.setUpdatedBy(userId);
			fieldAssociation.setCustomFieldSource(enumObj);
			customFieldAssocnList.add(fieldAssociation);
		}
		return customFieldAssocnList;
	}
	
	/**
	 * Replaces spaces in a string with underscores and prefixes it with a dollar sign.
	 *
	 * @param operand
	 *            The string to process.
	 * @return The processed string with spaces replaced by underscores.
	 */
	private static String replaceSpaceWithUnderScore(String operand) {
		if (operand.indexOf(' ') != -1) {
			return "$" + StringUtils.replace(operand, " ", "_");
		}
		return "$" + operand;
	}
    
	/**
	 * Filters a list of AI templates based on the specified template type.
	 * 
	 * @param allAITemplatesEntities
	 *            the list of all AI template entities to filter
	 * @param aiTemplateFilters
	 *            the filters to apply, specifically the template type
	 * @return
	 */
	public static List<BusinessAITemplateEntity> filterAITemplatesListByType(List<BusinessAITemplateEntity> allAITemplatesEntities, List<String> templateTypes) {
		Predicate<BusinessAITemplateEntity> filterPredicate = aiTemplateEntity -> templateTypes.contains(aiTemplateEntity.getTemplateType());
		return allAITemplatesEntities.stream().filter(filterPredicate).collect(Collectors.toList());
	}
	
	/**
	 * Filter a list of AI templates messages based on the specified status and search string.
	 * 
	 * @param allAITemplatesMessages
	 * @param aiTemplateFilters
	 * @return
	 */
	public static List<BusinessAITemplateMessage> filterAITemplatesMessageListByStatusAndSearchStr(List<BusinessAITemplateMessage> allAITemplatesMessages,
			AITemplateFilters aiTemplateFilters) {
		List<BusinessAITemplateMessage> filteredTemplatesBySearchStr = filterAITemplatesMessageListBySearchStr(allAITemplatesMessages, aiTemplateFilters.getSearchStr());
		
		// of both statuses are selected.
		if (aiTemplateFilters.getTemplateStatus().contains(Constants.TEMPLATES_IN_USE) && aiTemplateFilters.getTemplateStatus().contains(Constants.TEMPLATES_NOT_IN_USE)) {
			return filteredTemplatesBySearchStr;
		}
		
		// filter templates by status (in_use/not_in_use).
		Map<Boolean, List<BusinessAITemplateMessage>> templatesMap = filteredTemplatesBySearchStr.stream()
				.collect(Collectors.partitioningBy(t -> Objects.equals(t.getStatus(), 0)));
		if (aiTemplateFilters.getTemplateStatus().contains(Constants.TEMPLATES_IN_USE)) {
			return templatesMap.get(Boolean.TRUE);
		}
		if (aiTemplateFilters.getTemplateStatus().contains(Constants.TEMPLATES_NOT_IN_USE)) {
			return templatesMap.get(Boolean.FALSE);
		}
		
		return filteredTemplatesBySearchStr;
	}
	
	/**
	 * Filter a list of AI templates messages based on the search string.
	 * 
	 * @param allAITemplatesMessages
	 * @param searchString
	 * @return
	 */
	public static List<BusinessAITemplateMessage> filterAITemplatesMessageListBySearchStr(List<BusinessAITemplateMessage> allAITemplatesMessages, String searchString) {
		if (StringUtils.isNotBlank(searchString)) {
			
			Predicate<BusinessAITemplateMessage> searchStrPredicate = entity -> (StringUtils.containsIgnoreCase(entity.getName(), searchString));
			
			return allAITemplatesMessages.stream().filter(searchStrPredicate).collect(Collectors.toList());
		}
		return allAITemplatesMessages;
	}
	
	/**
	 * Prepares a BusinessAITemplateMessage from a BusinessAITemplateEntity.
	 * 
	 * @param aiTemplateEntity
	 *            the AI template entity to convert
	 * @return
	 */
	public static BusinessAITemplateMessage prepareTemplateMessageFromTemplateEntity(BusinessAITemplateEntity aiTemplateEntity) {
		BusinessAITemplateMessage aiTemplateMessage = new BusinessAITemplateMessage();
		aiTemplateMessage.setId(aiTemplateEntity.getAiTemplateId());
		aiTemplateMessage.setNonAITemplateId(aiTemplateEntity.getBaseTemplateId());
		aiTemplateMessage.setName(aiTemplateEntity.getTemplateName());
		aiTemplateMessage.setTemplateType(aiTemplateEntity.getTemplateType());
		aiTemplateMessage.setChannel(aiTemplateEntity.getChannel());
		aiTemplateMessage.setCanDelete(0); // To be modified later on
		aiTemplateMessage.setStatus(-1); // To be modified later on
		aiTemplateMessage.setLabel(AITemplateTypeEnum.valueOf(StringUtils.upperCase(aiTemplateMessage.getTemplateType())).getLabel());
		aiTemplateMessage.setUpdatedAt(aiTemplateEntity.getUpdatedAt());
		aiTemplateMessage.setAttachmentExists(false); // To be modified later on
		aiTemplateMessage.setIsDefaultTemplate(aiTemplateEntity.getIsDefaultTemplate());
		aiTemplateMessage.setTemplateCategory(TemplateUtils.getUnsubscriptionCategoryWithDefault(aiTemplateEntity.getEmailCategory(), aiTemplateEntity.getTemplateType()));
		aiTemplateMessage.setThumbnailUrl(aiTemplateEntity.getThumbnailUrl());
		
		return aiTemplateMessage;
	}
	
	public static List<HeaderData> buildTemplateHeader() {
        List<HeaderData> headers = new ArrayList<>();

        headers.add(new HeaderData(0, "name", "Name", true, true, true));
        headers.add(new HeaderData(1, "templateType", "Type", true, true, false));
        headers.add(new HeaderData(2, "usage", "Usage", true, true, false));
        headers.add(new HeaderData(3, "conversion", "Open rate", true, true, false));
        headers.add(new HeaderData(4, "created-by", "Created by", true, false, false));
        
        return headers;
    }
	
	
	public static BusinessAllAITemplatesResponse transformToResponse(BusinessAllAITemplatesMessage source, List<HeaderData> headerData) {
        BusinessAllAITemplatesResponse response = new BusinessAllAITemplatesResponse();
        response.setTotalCount(source.getTotalCount());
        
        
        if (Objects.isNull(source) || CollectionUtils.isEmpty(source.getTemplates())) {
        	response.setFilteredCount(0);
        	response.setData(Collections.emptyList());
        	return response;
        }
		
		// Determine response type based on headerData presence
		if (headerData != null && !headerData.isEmpty()) {
			// List API - with section listing response
			response.setData(transformToSectionListingResponse(source.getTemplates()));
			response.setFilteredCount(source.getFilteredCount());
	        response.setHeaderData(headerData);
		} else {
			// List by type API - with simple listing response
			response.setData(transformToListingByTypeResponse(source.getTemplates()));
		}
		
		return response;
    }
	
	private static Collection<? extends BusinessAITemplateBaseResponse> transformToListingByTypeResponse(List<BusinessAITemplateMessage> templates) {
		List<BusinessAITemplateListingByTypeResponse> transformedTemplates = new ArrayList<>();
		
		for (BusinessAITemplateMessage message : templates) {
			BusinessAITemplateListingByTypeResponse typeResponse = new BusinessAITemplateListingByTypeResponse();
			
			// Populate base class fields only
			populateBaseResponseFields(message, typeResponse);
			
			typeResponse.setNonAITemplateId(message.getNonAITemplateId());
			typeResponse.setAttachmentExists(message.isAttachmentExists());
			typeResponse.setEditable(message.getEditable());
			typeResponse.setNonEditable(message.getNonEditable());
			typeResponse.setMediaUrl(message.getMediaUrl());
			typeResponse.setLastUsedTime(message.getLastUsedTime());
			typeResponse.setUnsubscribeText(message.getUnsubscribeText());
			typeResponse.setSelectedCustomFields(message.getSelectedCustomFields());
			typeResponse.setSmsSegments(message.getSmsSegments());
			typeResponse.setIncludeUnsubscribeText(message.getIncludeUnsubscribeText());
			typeResponse.setSelectedLocations(message.getSelectedLocations());
			typeResponse.setFormUrlPresent(message.getFormUrlPresent());
			typeResponse.setAppointmentCustomFields(message.getAppointmentCustomFields());
			typeResponse.setIsDefaultTemplate(message.getIsDefaultTemplate());
			typeResponse.setIsGlobalTemplate(message.getIsGlobalTemplate());
			typeResponse.setLocationCustomFields(message.getLocationCustomFields());
			typeResponse.setIncludeImageWithText(message.getIncludeImageWithText());
			
			transformedTemplates.add(typeResponse);
		}
		
		return transformedTemplates;
	}
	
	private static void populateBaseResponseFields(BusinessAITemplateMessage message, BusinessAITemplateBaseResponse response) {
		response.setId(message.getId());
		response.setTemplateName(message.getName());
		response.setTemplateType(message.getTemplateType());
		response.setTemplateCategory(message.getTemplateCategory());
		response.setChannel(message.getChannel());
		response.setLabel(message.getLabel());
		response.setCanDelete(message.getCanDelete());
		response.setStatus(message.getStatus());
		response.setPreviewText(message.getPreviewText());
		response.setLocationTemplate(message.getLocationTemplate());
		response.setThumbnailUrl(message.getThumbnailUrl());
	}
	
	private static Collection<? extends BusinessAITemplateBaseResponse> transformToSectionListingResponse(List<BusinessAITemplateMessage> templates) {
		List<BusinessAITemplateSectionListingResponse> transformedTemplates = new ArrayList<>();
		
		for (BusinessAITemplateMessage message : templates) {
			BusinessAITemplateSectionListingResponse sectionResponse = new BusinessAITemplateSectionListingResponse();
			
			// Populate base class fields
			populateBaseResponseFields(message, sectionResponse);
			
			// Populate section-specific fields (rowData)
			List<RowData> rowDataList = new ArrayList<>();
			rowDataList.add(new RowData(0, message.getName(), "name"));
			rowDataList.add(new RowData(1, message.getLabel(), "templateType"));
			rowDataList.add(new RowData(2, message.getUsageStats() != null ? String.valueOf(message.getUsageStats().getSentSuccess()) : "0", "usage"));
			rowDataList.add(new RowData(3, message.getUsageStats() != null ? String.valueOf(message.getUsageStats().getOpenStartedPercent()) : "0", "conversion"));
			rowDataList.add(new RowData(4, "Emily", "created-by")); // Placeholder
			
			sectionResponse.setRowData(rowDataList);
			transformedTemplates.add(sectionResponse);
		}
		
		return transformedTemplates;
	}
	
	/**
	 * Sorts a list of business AI template messages based on the provided filters and prepares a paginated list.
	 * 
	 * @param allAiTemplateMessages
	 * @param aiTemplateFilters
	 * @return
	 */
	public static void prepareSortedPaginatedTemplateMessagesList(BusinessAllAITemplatesMessage allAITemplateMessages, AITemplateFilters aiTemplateFilters) {
		if (Objects.isNull(allAITemplateMessages) || CollectionUtils.isEmpty(allAITemplateMessages.getTemplates())) {
			return;
		}
		
		Comparator<BusinessAITemplateMessage> comparator = getComparatorForAITemplates(aiTemplateFilters.getSortBy());
		if (comparator != null && aiTemplateFilters.getSortOrder() == 1) {
			// Reverse comparator for descending order
			comparator = comparator.reversed();
		}
		
		List<BusinessAITemplateMessage> aiTemplateMessages = allAITemplateMessages.getTemplates();
		// Apply the sorting if a comparator is defined
		if (comparator != null) {
			aiTemplateMessages.sort(comparator);
		}
		
		// Calculate pagination parameters
		int page = aiTemplateFilters.getPage() != null ? aiTemplateFilters.getPage() : 0;
		int size = aiTemplateFilters.getSize() != null ? aiTemplateFilters.getSize() : 25;
		int fromIndex = page * size;
		int toIndex = Math.min(fromIndex + size, aiTemplateMessages.size());
		
		// Add the sublist based on pagination or an empty list if out of bounds
		allAITemplateMessages.setTemplates(fromIndex >= aiTemplateMessages.size() ? Collections.emptyList() : aiTemplateMessages.subList(fromIndex, toIndex));
		return;
	}
	
	/**
	 * Returns a comparator based on the provided 'sortBy' field.
	 * 
	 * @param sortBy 
	 * @return
	 */
	public static Comparator<BusinessAITemplateMessage> getComparatorForAITemplates(String sortBy) {
		if (StringUtils.isBlank(sortBy)) {
			return getDefaultComparatorForAITemplates();
		}
		
		// Return the appropriate comparator based on the sorting field
        switch (sortBy) {
            case "name":
                return Comparator.comparing(BusinessAITemplateMessage::getName, Comparator.nullsLast(String::compareTo));
            case "templateType":
                return Comparator.comparing(BusinessAITemplateMessage::getTemplateType, Comparator.nullsLast(String::compareTo));
            case "usage":
                return Comparator.comparing(t -> t.getUsageStats().getSentSuccess(), Comparator.nullsLast(Long::compareTo));
            case "conversion":
                return Comparator.comparing(t -> t.getUsageStats().getOpenStartedPercent(), Comparator.nullsLast(Double::compareTo));
            default:
                return getDefaultComparatorForAITemplates();
        }
		
	}
	
	/**
	 * Returns the default comparator for AI templates, sorting by status and updatedAt in descending order.
	 * 
	 * @return
	 */
	public static Comparator<BusinessAITemplateMessage> getDefaultComparatorForAITemplates() {
		return Comparator.comparing(BusinessAITemplateMessage::getStatus, (s1, s2) -> {
			return s2.compareTo(s1);
		}).thenComparing(BusinessAITemplateMessage::getUpdatedAt, (s1, s2) -> {
			return s2.compareTo(s1);
		});
	}
}
