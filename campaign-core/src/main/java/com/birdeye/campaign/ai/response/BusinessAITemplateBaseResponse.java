package com.birdeye.campaign.ai.response;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
public class BusinessAITemplateBaseResponse implements Serializable {
	
	private static final long	serialVersionUID	= 7933322715229416240L;
	
	private Integer				id;
	private String				templateName;
	private String				templateType;
	private String				templateCategory;
	private String				channel;
	private String				label;
	private Integer				canDelete;
	private Integer				status;
	private String				previewText;
	private Integer				locationTemplate	= 0;
	private String				thumbnailUrl;
	
	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}
	
	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	
	/**
	 * @return the templateName
	 */
	public String getTemplateName() {
		return templateName;
	}
	
	/**
	 * @param templateName
	 *            the templateName to set
	 */
	public void setTemplateName(String templateName) {
		this.templateName = templateName;
	}
	
	/**
	 * @return the templateType
	 */
	public String getTemplateType() {
		return templateType;
	}
	
	/**
	 * @param templateType
	 *            the templateType to set
	 */
	public void setTemplateType(String templateType) {
		this.templateType = templateType;
	}
	
	/**
	 * @return the templateCategory
	 */
	public String getTemplateCategory() {
		return templateCategory;
	}
	
	/**
	 * @param templateCategory
	 *            the templateCategory to set
	 */
	public void setTemplateCategory(String templateCategory) {
		this.templateCategory = templateCategory;
	}
	
	/**
	 * @return the channel
	 */
	public String getChannel() {
		return channel;
	}
	
	/**
	 * @param channel
	 *            the channel to set
	 */
	public void setChannel(String channel) {
		this.channel = channel;
	}
	
	/**
	 * @return the label
	 */
	public String getLabel() {
		return label;
	}
	
	/**
	 * @param label
	 *            the label to set
	 */
	public void setLabel(String label) {
		this.label = label;
	}
	
	/**
	 * @return the canDelete
	 */
	public Integer getCanDelete() {
		return canDelete;
	}
	
	/**
	 * @param canDelete
	 *            the canDelete to set
	 */
	public void setCanDelete(Integer canDelete) {
		this.canDelete = canDelete;
	}
	
	/**
	 * @return the status
	 */
	public Integer getStatus() {
		return status;
	}
	
	/**
	 * @param status
	 *            the status to set
	 */
	public void setStatus(Integer status) {
		this.status = status;
	}
	
	/**
	 * @return the previewText
	 */
	public String getPreviewText() {
		return previewText;
	}
	
	/**
	 * @param previewText
	 *            the previewText to set
	 */
	public void setPreviewText(String previewText) {
		this.previewText = previewText;
	}
	
	/**
	 * @return the locationTemplate
	 */
	public Integer getLocationTemplate() {
		return locationTemplate;
	}
	
	/**
	 * @param locationTemplate
	 *            the locationTemplate to set
	 */
	public void setLocationTemplate(Integer locationTemplate) {
		this.locationTemplate = locationTemplate;
	}
	
	/**
	 * @return the thumbnailUrl
	 */
	public String getThumbnailUrl() {
		return thumbnailUrl;
	}
	
	/**
	 * @param thumbnailUrl
	 *            the thumbnailUrl to set
	 */
	public void setThumbnailUrl(String thumbnailUrl) {
		this.thumbnailUrl = thumbnailUrl;
	}
	
}
