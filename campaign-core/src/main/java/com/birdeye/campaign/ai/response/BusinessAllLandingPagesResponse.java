package com.birdeye.campaign.ai.response;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
public class BusinessAllLandingPagesResponse implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	private Integer totalCount;
	private Integer filteredCount;
	private List<HeaderData> headerData;
	private Collection<? extends BusinessLandingPageBaseResponse> data;
	
	/**
	 * @return the totalCount
	 */
	public Integer getTotalCount() {
		return totalCount;
	}
	
	/**
	 * @param totalCount the totalCount to set
	 */
	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}
	
	/**
	 * @return the filteredCount
	 */
	public Integer getFilteredCount() {
		return filteredCount;
	}
	
	/**
	 * @param filteredCount the filteredCount to set
	 */
	public void setFilteredCount(Integer filteredCount) {
		this.filteredCount = filteredCount;
	}
	
	/**
	 * @return the headerData
	 */
	public List<HeaderData> getHeaderData() {
		return headerData;
	}
	
	/**
	 * @param headerData the headerData to set
	 */
	public void setHeaderData(List<HeaderData> headerData) {
		this.headerData = headerData;
	}
	
	/**
	 * @return the data
	 */
	public Collection<? extends BusinessLandingPageBaseResponse> getData() {
		return data;
	}
	
	/**
	 * @param data the data to set
	 */
	public void setData(Collection<? extends BusinessLandingPageBaseResponse> data) {
		this.data = data;
	}
}
