package com.birdeye.campaign.utils;

import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.birdeye.campaign.cdc.dto.AllCampaignDataDTO;
import com.birdeye.campaign.cdc.dto.CampaignConditionDTO;
import com.birdeye.campaign.cdc.dto.CampaignDTO;
import com.birdeye.campaign.cdc.dto.DripCampaignConditionDTO;
import com.birdeye.campaign.cdc.dto.EntityChangeLogDto;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.dto.AllTemplateDataDto;
import com.birdeye.campaign.dto.BusinessEmailTemplateDTO;
import com.birdeye.campaign.dto.BusinessSmsTemplateDto;
import com.birdeye.campaign.dto.CampaignModificationEvent;
import com.birdeye.campaign.dto.EmailTemplateDTO;
import com.birdeye.campaign.dto.ReferralTemplateDataDto;
import com.birdeye.campaign.dto.TemplateSourceDto;
import com.birdeye.campaign.entity.BusinessDeeplinkPriority;
import com.birdeye.campaign.entity.BusinessEmailTemplate;
import com.birdeye.campaign.entity.BusinessReferralSourcePriority;
import com.birdeye.campaign.entity.BusinessSmsTemplate;
import com.birdeye.campaign.entity.Campaign;
import com.birdeye.campaign.entity.CampaignCondition;
import com.birdeye.campaign.entity.CampaignEntitiesChangeLog;
import com.birdeye.campaign.entity.DripCampaignCondition;
import com.birdeye.campaign.entity.EmailTemplate;
import com.birdeye.campaign.entity.ReferralTemplateData;
import com.birdeye.campaign.exception.CampaignException;
import com.birdeye.campaign.platform.constant.CampaignModificationUserActionEnum;
import com.birdeye.campaign.platform.constant.CampaignStatusEnum;
import com.fasterxml.jackson.databind.ObjectMapper;

public class CampaignModificationAuditUtils {
	
	public static final String	CAMPAIGN_ENTITY				= "campaign";
	
	public static final String	TEMPLATE_ENTITY				= "template";
	
	public static final String	CAMPAIGN_DATA				= "CampaignData";
	
	public static final String	TEMPLATE_DATA				= "TemplateData";
	
	public static final String	OLD_TEMPLATE_DATA			= "oldTemplateData";
	
	public static final String	NEW_TEMPLATE_DATA			= "newTemplateData";
	
	public static final String	ORIGINAL_LOCATIONS			= "originalLocations";
	
	public static final String	UPDATED_LOCATIONS			= "updatedLocations";
	
	public static final String	SELECTED_SOURCES			= "selectedSources";
	
	public static final String	SELECTED_ANDROID_SOURCES	= "selectedAndroidSources";
	
	public static final String	OLD_SELECTED_SOURCES		= "oldSelectedSources";
	
	public static final String	NEW_SELECTED_SOURCES		= "newSelectedSources";
	
	public static final String	REFERRAL_SOURCES			= "referralSources";
	
	public static final String	MODIFICATION_DATE_FORMAT	= "MMM dd, yyyy";
	
	public static final String	MODIFICATION_TIME_FORMAT	= "h:mm a";
	
	private static final Logger	LOG							= LoggerFactory.getLogger(CampaignModificationAuditUtils.class);
	
	private CampaignModificationAuditUtils() {
		
	}
	
	/**
	 * Utility method to prepare campaign modification event.
	 * 
	 * @param oldDataMap
	 * @param newDataMap
	 * @param userId
	 * @param modificationEventType
	 * @param modificationTimeStamp
	 * @param entityName
	 * @param accountId
	 * @param entityId
	 * @param eventSubType
	 * @return
	 */
	public static CampaignModificationEvent prepareModificationEvent(Map<String, Object> oldData, Map<String, Object> newData, Integer userId, String modificationEventType,
			Long modificationTimeStamp, String entityName, Integer accountId, Integer entityId, String eventSubType) {
		CampaignModificationEvent campaignEvent = new CampaignModificationEvent();
		campaignEvent.setAccountId(accountId);
		campaignEvent.setEntityName(entityName);
		campaignEvent.setEntityId(entityId);
		campaignEvent.setUserId(userId);
		campaignEvent.setModificationActionType(modificationEventType);
		campaignEvent.setModificationTimeStamp(modificationTimeStamp);
		campaignEvent.setOldEntities(oldData);
		campaignEvent.setNewEntities(newData);
		campaignEvent.setEntitySubType(eventSubType);
		return campaignEvent;
	}
	
	/**
	 * Utility method to prepare modification event for default template update.
	 * 
	 * 
	 * @param templateId
	 * @param accountId
	 * @param userId
	 * @param modificationTimeStamp
	 * @param templateCategory
	 * @param setAsDefault
	 * @return
	 */
	public static CampaignModificationEvent prepareModificationEventForDefaultTemplateUpdate(Integer templateId, Integer accountId, Integer userId, Long modificationTimeStamp,
			String templateCategory, boolean setAsDefault) {
		AllTemplateDataDto oldTemplateDataDto = new AllTemplateDataDto();
		oldTemplateDataDto.setBusinessSmsTemplate(new BusinessSmsTemplateDto());
		oldTemplateDataDto.getBusinessSmsTemplate().setId(templateId);
		oldTemplateDataDto.getBusinessSmsTemplate().setIsDefaultTemplate(setAsDefault ? 0 : 1); // Initializing old value of isDefault flag
		
		Map<String, Object> oldTemplateDataMap = populateModifiedEntitiesMap(oldTemplateDataDto, CampaignModificationAuditUtils.TEMPLATE_DATA);
		
		AllTemplateDataDto newTemplateDataDto = new AllTemplateDataDto();
		newTemplateDataDto.setBusinessSmsTemplate(new BusinessSmsTemplateDto());
		newTemplateDataDto.getBusinessSmsTemplate().setId(templateId);
		newTemplateDataDto.getBusinessSmsTemplate().setIsDefaultTemplate(setAsDefault ? 1 : 0); // Initializing new value of isDefault flag
		Map<String, Object> newTemplateDataMap = populateModifiedEntitiesMap(newTemplateDataDto, CampaignModificationAuditUtils.TEMPLATE_DATA);
		
		CampaignModificationEvent modificationEvent = new CampaignModificationEvent();
		modificationEvent.setAccountId(accountId);
		modificationEvent.setEntityName(CampaignModificationAuditUtils.TEMPLATE_ENTITY);
		modificationEvent.setEntityId(templateId);
		modificationEvent.setUserId(userId);
		modificationEvent.setModificationActionType(
				setAsDefault ? CampaignModificationUserActionEnum.MARK_DEFAULT.getUserActionType() : CampaignModificationUserActionEnum.UNMARK_DEFAULT.getUserActionType());
		modificationEvent.setModificationTimeStamp(modificationTimeStamp);
		modificationEvent.setOldEntities(oldTemplateDataMap);
		modificationEvent.setNewEntities(newTemplateDataMap);
		modificationEvent.setEntitySubType(templateCategory);
		return modificationEvent;
		
	}
	
	/**
	 * Utility Method to populate the entities map.
	 * 
	 * @param campaign
	 * @return
	 */
	public static Map<String, Object> populateModifiedEntitiesMap(Object entityData, String entityName) {
		Map<String, Object> entitiesMap = new HashMap<>();
		if (!Objects.isNull(entityData)) {
			entitiesMap.put(entityName, entityData);
		}
		
		return entitiesMap;
	}
	
	/**
	 * Utility method to prepare 'AllCampaignDataDTO' from respective entities.
	 * 
	 * @param campaign
	 * @param condition
	 * @param dripCampaignCondition
	 * @return
	 */
	public static AllCampaignDataDTO prepareAllCampaignDataDTO(Campaign campaign, CampaignCondition condition, DripCampaignCondition dripCampaignCondition) {
		return AllCampaignDataDTO.builder().withCampaignEntityData(prepareCampaignDTO(campaign)).withCampaignConditionEntityData(prepareCampaignConditionDTO(condition))
				.withDripCampaignConditionEntityData(prepareDripCampaignConditionDTO(dripCampaignCondition)).build();
	}
	
	/**
	 * Utility method to prepare 'CamapignDTO' from 'Campaign' entity.
	 * 
	 * Return 'null' in case argument to the method is null.
	 * 
	 * @param campaign
	 * @return
	 */
	public static CampaignDTO prepareCampaignDTO(Campaign campaign) {
		if (!Objects.isNull(campaign)) {
			return CampaignDTO.builder().withId(campaign.getId()).withType(campaign.getType()).withStartAt(campaign.getStartAt()).withEndAt(campaign.getEndAt())
					.withBusinessId(campaign.getBusinessId()).withEnterpriseId(campaign.getEnterpriseId()).withSurveyId(campaign.getSurveyId())
					.withCreatedBy(campaign.getCreatedBy()).withName(campaign.getName()).withStatus(campaign.getStatus()).withPriorityOrder(campaign.getPriorityOrder())
					.withTemplateId(campaign.getTemplateId()).withResellerId(campaign.getResellerId()).withIsDeleted(campaign.getIsDeleted())
					.withCustomerCount(campaign.getCustomerCount()).withSmsTemplateId(campaign.getSmsTemplateId()).withPriority(campaign.getPriority())
					.withRunType(campaign.getRunType()).withReminderSubject(campaign.getReminderSubject()).withReminderCount(campaign.getReminderCount())
					.withReminderFrequency(campaign.getReminderFrequency()).withSendReminder(campaign.getSendReminder()).withIsDefault(campaign.getIsDefault())
					.withSchedule(campaign.getSchedule()).withSchedulingType(campaign.getSchedulingType()).withSchedulingInHours(campaign.getSchedulingInHours())
					.withIsMessengerCampaign(campaign.getIsMessengerCampaign()).withTriggerType(campaign.getTriggerType())
					.withBypassCommRestriction(campaign.getBypassCommRestriction()).withSkipFutureAppointment(campaign.getSkipFutureAppointment())
					.withIsSplitCampaign(campaign.getIsSplitCampaign()).withIsAppointmentTabCampaign(campaign.getIsAppointmentTabCampaign()).withSurveyCommFrequency(campaign.getSurveyCommFrequency()).build();
		}
		return null;
	}
	
	/**
	 * Utility method to prepare 'CamapignConditionDTO' from 'CampaignCondition' entity.
	 * 
	 * Return 'null' in case argument to the method is null.
	 * 
	 * @param condition
	 * @return
	 */
	public static CampaignConditionDTO prepareCampaignConditionDTO(CampaignCondition condition) {
		if (!Objects.isNull(condition)) {
			if (CollectionUtils.isNotEmpty(condition.getLvlIds())) {
				Collections.sort(condition.getLvlIds()); // To account for ignoring ordering of elements, when comparing two lists to evaluate data differences.
			}
			
			return CampaignConditionDTO.builder().withId(condition.getId()).withCampaignId(condition.getCampaignId()).withEnterpriseId(condition.getEnterpriseId())
					.withEvent(condition.getEvent()).withLvlAlias(condition.getLvlAlias()).withLvlAliasId(condition.getLvlAliasId()).withLvlIds(condition.getLvlIds())
					.withContactSources(condition.getContactSources()).withTags(condition.getTags()).withExclusionTags(condition.getExclusionTags())
					.withNoTagFilter(condition.getNoTagFilter()).withAnyTagFilter(condition.getAnyTagFilter()).withRuleExpression(condition.getRuleExpression())
					.withMvelExpression(condition.getMvelExpression()).withMvelParamsAndTypes(condition.getMvelParamsAndTypes())
					.withTriggerRuleExpression(condition.getTriggerRuleExpression()).withTriggerMvelExpression(condition.getTriggerMvelExpression())
					.withTriggerMvelParamsAndTypes(condition.getTriggerMvelParamsAndTypes()).withAppointmentScheduleInfo(condition.getAppointmentScheduleInfo())
					.withScheduleInHours(condition.getScheduleinHours()).withExecutionDateInfo(condition.getExecutionDateInfo())
					.withRecurringReminderEventType(condition.getRecurringReminderEventType()).build();
		}
		return null;
	}
	
	/**
	 * Utility method to prepare 'DripCamapignConditionDTO' from 'DripCampaignCondition' entity.
	 * 
	 * Return 'null' in case argument to the method is null.
	 * 
	 * @param dripCondition
	 * @return
	 */
	public static DripCampaignConditionDTO prepareDripCampaignConditionDTO(DripCampaignCondition dripCondition) {
		if (!Objects.isNull(dripCondition)) {
			return DripCampaignConditionDTO.builder().withId(dripCondition.getId()).withCampaignId(dripCondition.getCampaignId()).withEnterpriseId(dripCondition.getEnterpriseId())
					.withAllowedDays(dripCondition.getAllowedDays()).withExclusionDates(dripCondition.getExclusionDates()).withSendTime(dripCondition.getSendTime()).build();
		}
		return null;
	}
	
	/**
	 * Utility method to prepare serialized json string corresponding to an Object.
	 * 
	 * @param entityChangeData
	 * @return
	 */
	public static String prepareSerializedStringForObject(Object entityChangeData) {
		if (Objects.isNull(entityChangeData)) {
			return StringUtils.EMPTY;
		}
		
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			return objectMapper.writeValueAsString(entityChangeData);
		} catch (Exception e) {
			LOG.error("Exception occured while prepared serialised string data for object: {}. Exception: {}", entityChangeData, ExceptionUtils.getStackTrace(e));
			return StringUtils.EMPTY;
		}
	}
	
	/**
	 * Utility method to prepare CampaignModificationUserActivity.
	 * 
	 * @param changedData
	 * @param accountId
	 * @param actionType
	 * @param entityName
	 * @param entityId
	 * @param updationTime
	 * @param userId
	 * @return
	 */
	public static CampaignEntitiesChangeLog prepareCampaignChangeLogAudit(Integer accountId, String actionType, String entityName, Integer entityId, Long updationTime, Integer userId,
			EntityChangeLogDto changeData, String entitySubType) {
		CampaignEntitiesChangeLog changeLog = new CampaignEntitiesChangeLog();
		changeLog.setAccountId(accountId);
		changeLog.setEvent(actionType);
		changeLog.setEntityId(entityId);
		changeLog.setEntityType(StringUtils.isNotBlank(entitySubType) ? StringUtils.join(entitySubType, entityName) : entityName);
		changeLog.setModificationDetails(prepareSerializedStringForObject(changeData.getDataDiffMap()));
		changeLog.setModifiedFields(CollectionUtils.isNotEmpty(changeData.getUpdatedFieldNames()) ? String.join(",", changeData.getUpdatedFieldNames()) : null);
		changeLog.setEventTime((updationTime != null) ? new Date(updationTime) : new Date());
		changeLog.setUserId(userId);
		return changeLog;
	}
	
	/**
	 * Utility method to prepare campaign status modification user action type.
	 * 
	 * @param campaignStatus
	 * @return
	 */
	public static String getCampaignRunningStatus(int campaignStatus) {
		switch (campaignStatus) {
			case 1:
				return CampaignModificationUserActionEnum.RESUME.getUserActionType();
			
			case 2:
				return CampaignModificationUserActionEnum.PAUSE.getUserActionType();
			
			default:
				return CampaignStatusEnum.getStatusAlias(campaignStatus);
		}
	}
	
	/**
	 * 
	 * Utility method to evaluate difference between 2 objects without calculating separately the name of the fields changed
	 * 
	 * @param oldDataObject,
	 *            ongoingDataObject
	 * 
	 */
	public static Map<String, Object> evaluateDataDifferenceWithoutFields(Object oldDataObject, Object ongoingDataObject) {
		Map<String, Object> dataDifference = new HashMap<>();
		try {
			if (oldDataObject.getClass().isAssignableFrom(ongoingDataObject.getClass())) {
				for (PropertyDescriptor propertyDescriptor : Introspector.getBeanInfo(ongoingDataObject.getClass(), Object.class).getPropertyDescriptors()) {
					String propertyName = propertyDescriptor.getName();
					Object oldDataObjectValue = propertyDescriptor.getReadMethod().invoke(oldDataObject);
					Object ongoingDataObjectValue = propertyDescriptor.getReadMethod().invoke(ongoingDataObject);
					
					if (oldDataObjectValue == null && ongoingDataObjectValue == null) {
						continue;
					} else if (oldDataObjectValue == null || ongoingDataObjectValue == null) {
						dataDifference.put(propertyName, ongoingDataObjectValue);
					} else if (isUserDefinedClass(propertyDescriptor.getPropertyType()) && BooleanUtils.isFalse(propertyDescriptor.getPropertyType().isEnum())) {
						Map<String, Object> nestedDifferences = evaluateDataDifferenceWithoutFields(oldDataObjectValue, ongoingDataObjectValue);
						if (!nestedDifferences.isEmpty()) {
							dataDifference.put(propertyName, nestedDifferences);
						}
					} else if (!oldDataObjectValue.equals(ongoingDataObjectValue)) {
						dataDifference.put(propertyName, ongoingDataObjectValue);
					}
				}
			}
		} catch (Exception e) {
			LOG.error("Exception occurred while evaluating data difference without field names. Exception: {}", ExceptionUtils.getStackTrace(e));
			throw new CampaignException(ErrorCodes.ERROR_OCCURRED_EVALUATING_DIFF_FOR_CHANGE_LOG, ErrorCodes.ERROR_OCCURRED_EVALUATING_DIFF_FOR_CHANGE_LOG.getMessage());
		}
		return dataDifference;
	}
	
	/**
	 * 
	 * Utility method to evaluate difference between 2 objects with calculating separately the name of the fields changed
	 * 
	 * @param oldDataObject,
	 *            ongoingDataObject
	 * 
	 */
	public static EntityChangeLogDto evaluateDataDifference(Object oldDataObject, Object ongoingDataObject) throws Exception {
		if (oldDataObject == null || ongoingDataObject == null) {
			return null;
		}
		Map<String, Object> dataDifference = new HashMap<>();
		List<String> allUpdatedFieldList = new ArrayList<>();
		try {
			if (oldDataObject.getClass().isAssignableFrom(ongoingDataObject.getClass())) {
				for (PropertyDescriptor propertyDescriptor : Introspector.getBeanInfo(ongoingDataObject.getClass(), Object.class).getPropertyDescriptors()) {
					String propertyName = propertyDescriptor.getName();
					Object oldDataObjectValue = propertyDescriptor.getReadMethod().invoke(oldDataObject);
					Object ongoingDataObjectValue = propertyDescriptor.getReadMethod().invoke(ongoingDataObject);
					
					if (BooleanUtils.isFalse(isUserDefinedClass(propertyDescriptor.getPropertyType())) || oldDataObjectValue == null || ongoingDataObject == null) {
						continue;
					}
					Map<String, Object> nestedDifferences = evaluateDataDifferenceWithoutFields(oldDataObjectValue, ongoingDataObjectValue);
					if (!nestedDifferences.isEmpty()) {
						allUpdatedFieldList.addAll(fetchKeysFromMap(nestedDifferences));
						dataDifference.put(propertyName, nestedDifferences);
					}
				}
			}
		} catch (Exception e) {
			LOG.error("Exception occurred while evaluating data difference. Exception: {}", ExceptionUtils.getStackTrace(e));
			throw new CampaignException(ErrorCodes.ERROR_OCCURRED_EVALUATING_DIFF_FOR_CHANGE_LOG, ErrorCodes.ERROR_OCCURRED_EVALUATING_DIFF_FOR_CHANGE_LOG.getMessage());
		}
		return new EntityChangeLogDto(dataDifference, allUpdatedFieldList);
	}
	
	/**
	 * 
	 * Utility method to Fetch keys from Map
	 * 
	 * @param dataMap
	 * 
	 */
	public static List<String> fetchKeysFromMap(Map<?, ?> dataMap) {
		if (dataMap == null) {
			return new ArrayList<>();
		}
		List<String> keyList = new ArrayList<>();
		for (Object name : dataMap.keySet()) {
			keyList.add(String.valueOf(name));
		}
		return keyList;
	}
	
	/**
	 * 
	 * Utility method to Fetch all the fields of an object and there corresponding data
	 * 
	 * @param dataObject
	 * 
	 */
	public static Map<String, Object> fetchFieldsAndData(Object dataObject) {
		if (Objects.isNull(dataObject)) {
			return null;
		}
		Map<String, Object> fieldData = new HashMap<>();
		try {
			for (PropertyDescriptor propertyDescriptor : Introspector.getBeanInfo(dataObject.getClass(), Object.class).getPropertyDescriptors()) {
				String propertyName = propertyDescriptor.getName();
				Object dataObjectValue = propertyDescriptor.getReadMethod().invoke(dataObject);
				if (dataObjectValue != null) {
					fieldData.put(propertyName, dataObjectValue);
				}
			}
		} catch (Exception e) {
			LOG.error("fetchFieldsAndData :: Exception occurred while fetching fields and there data. Exception: {}", ExceptionUtils.getStackTrace(e));
		}
		return fieldData;
	}
	
	/**
	 * Utility method to check if a class is user defined or not
	 * 
	 * @param clazz
	 * 
	 */
	public static boolean isUserDefinedClass(Class<?> clazz) {
		String packageName = clazz.getPackage() != null ? clazz.getPackage().getName() : "";
		return packageName.startsWith("com.birdeye");
	}
	
	/**
	 * Utility method to prepare list of TemplateSourceDto from Map of BusinessDeeplinkPriority
	 * 
	 * @param priorityByPriorityOrderMap
	 * 
	 */
	public static List<TemplateSourceDto> prepareDtoFromBDPMap(Map<Integer, BusinessDeeplinkPriority> priorityByPriorityOrderMap) {
		if (MapUtils.isEmpty(priorityByPriorityOrderMap)) {
			return null;
		}
		List<TemplateSourceDto> bdpDtoList = new ArrayList<>();
		for (Map.Entry<Integer, BusinessDeeplinkPriority> entry : priorityByPriorityOrderMap.entrySet()) {
			bdpDtoList.add(new TemplateSourceDto(entry.getValue()));
		}
		return bdpDtoList;
	}
	
	/**
	 * Utility method to prepare list of TemplateSourceDto from List of BusinessDeeplinkPriority
	 * 
	 * @param priorityByPriorityOrderList
	 * 
	 */
	public static List<TemplateSourceDto> prepareDtoFromBDPList(List<BusinessDeeplinkPriority> priorityByPriorityOrderList) {
		return (CollectionUtils.isEmpty(priorityByPriorityOrderList)) ? null : priorityByPriorityOrderList.stream().map(e -> new TemplateSourceDto(e)).collect(Collectors.toList());
	}
	
	/**
	 * Utility method to prepare list of TemplateSourceDto from Map of BusinessReferralSourcePriority
	 * 
	 * @param priorityByPriorityOrderMap
	 * 
	 */
	public static List<TemplateSourceDto> prepareDtoFromReferralSourcePriority(Map<Integer, BusinessReferralSourcePriority> priorityByPriorityOrderMap) {
		if (MapUtils.isEmpty(priorityByPriorityOrderMap)) {
			return null;
		}
		List<TemplateSourceDto> bdpDtoList = new ArrayList<>();
		for (Map.Entry<Integer, BusinessReferralSourcePriority> entry : priorityByPriorityOrderMap.entrySet()) {
			bdpDtoList.add(new TemplateSourceDto(entry.getValue()));
		}
		return bdpDtoList;
	}
	
	/**
	 * Utility method to prepare list of TemplateSourceDto from List of BusinessReferralSourcePriority
	 * 
	 * @param priorityByPriorityOrderList
	 * 
	 */
	public static List<TemplateSourceDto> prepareDtoFromReferralSourcePriority(List<BusinessReferralSourcePriority> priorityByPriorityOrderList) {
		return (CollectionUtils.isEmpty(priorityByPriorityOrderList)) ? null : priorityByPriorityOrderList.stream().map(e -> new TemplateSourceDto(e)).collect(Collectors.toList());
	}
	
	/**
	 * Utility method to prepare Map of TemplateSourceDto from List of TemplateSourceDto
	 * 
	 * @param sourcesListDto,
	 *            key
	 * 
	 */
	public static Map<String, List<TemplateSourceDto>> prepareMapFromSourcePriorityDtoList(List<TemplateSourceDto> sourcesListDto, String key) {
		if (CollectionUtils.isEmpty(sourcesListDto) || StringUtils.isBlank(key)) {
			return null;
		}
		Map<String, List<TemplateSourceDto>> sourcesMap = new HashMap<>();
		sourcesMap.put(key, sourcesListDto);
		return sourcesMap;
	}
	
	/**
	 * 
	 * Utility method to prepare Map with Prefix Key
	 * 
	 * 1. Validate If Map in request is not empty
	 * 2. Fetch all keys from map which start with given prefix.
	 * 3. Add the data to new map by removing the prefix from fetched key.
	 * 
	 * @param oldNewEntityDtoMap,
	 *            prefix
	 * 
	 */
	public static Map<String, Object> fetchAndPrepareMapWithPrefixKeyForTemplates(Map<String, AllTemplateDataDto> oldNewEntityDtoMap, String prefix) {
		if (MapUtils.isEmpty(oldNewEntityDtoMap)) {
			return new HashMap<>();
		}
		Map<String, Object> entitiesMap = new HashMap<>();
		for (Map.Entry<String, AllTemplateDataDto> entry : oldNewEntityDtoMap.entrySet()) {
			if (entry.getKey().startsWith(prefix) && BooleanUtils.isFalse(Objects.isNull(entry.getValue()))) {
				String newKey = entry.getKey().replace(prefix, StringUtils.EMPTY);
				entitiesMap.put(newKey, entry.getValue());
			}
		}
		return entitiesMap;
	}
	
	/**
	 * 
	 * Utility method to update object with Email Template Data
	 * 
	 * 1. Validate Request
	 * 2. Fetch Object with given key
	 * 3. Update Object
	 * 
	 * @param oldNewEntityDtoMap,
	 *            businessEmailTemplate,emailTemplate,key
	 * 
	 */
	public static void addTemplateDataToAllTemplateData(Map<String, AllTemplateDataDto> oldNewEntityDtoMap, BusinessEmailTemplate businessEmailTemplate, EmailTemplate emailTemplate, String key) {
		if (validateInputData(oldNewEntityDtoMap, key) && checkIfAllTemplateDataInitialized(oldNewEntityDtoMap.get(key)) && (businessEmailTemplate != null && emailTemplate != null)) {
			oldNewEntityDtoMap.get(key).setBusinessEmailTemplate(new BusinessEmailTemplateDTO(businessEmailTemplate));
			oldNewEntityDtoMap.get(key).setEmailTemplate(new EmailTemplateDTO(emailTemplate));
		}
	}
	
	/**
	 * 
	 * Utility method to update object with Sms Template Data
	 * 
	 * 1. Validate Request
	 * 2. Fetch Object with given key
	 * 3. Update Object
	 * 
	 * @param oldNewEntityDtoMap,
	 *            businessSmsTemplate,key
	 * 
	 */
	public static void addTemplateDataToAllTemplateData(Map<String, AllTemplateDataDto> oldNewEntityDtoMap, BusinessSmsTemplate businessSmsTemplate, String key) {
		if (validateInputData(oldNewEntityDtoMap, key) && checkIfAllTemplateDataInitialized(oldNewEntityDtoMap.get(key)) && businessSmsTemplate != null)
			oldNewEntityDtoMap.get(key).setBusinessSmsTemplate(new BusinessSmsTemplateDto(businessSmsTemplate));
	}
	
	/**
	 * 
	 * Initialize All Template Data Object
	 * 
	 */
	public static AllTemplateDataDto initializeAllTemplateDataObject() {
		AllTemplateDataDto templateDto = new AllTemplateDataDto();
		AllTemplateDataDto.AdditionalDataParams dataParams = new AllTemplateDataDto.AdditionalDataParams();
		templateDto.setAdditionalDataParams(dataParams);
		return templateDto;
	}
	
	/**
	 * 
	 * Validate If All Template Data Object is initialized or not.
	 * 
	 * @param templateDto
	 * 
	 */
	private static Boolean checkIfAllTemplateDataInitialized(AllTemplateDataDto templateDto) {
		return (templateDto != null && templateDto.getAdditionalDataParams() != null);
	}
	
	/**
	 * 
	 * Validate If Diff Map or Key is null or not.
	 * 
	 * @param oldNewEntityDtoMap,
	 *            key
	 * 
	 */
	private static Boolean validateInputData(Map<String, AllTemplateDataDto> oldNewEntityDtoMap, String key) {
		return BooleanUtils.isFalse(MapUtils.isEmpty(oldNewEntityDtoMap)) && StringUtils.isNotBlank(key);
	}
	
	/**
	 * 
	 * Utility method to initialize data difference map for Templates
	 * 1. If Reseller Template then return empty map
	 * 2. If global template or new template, initialize map with new template data only
	 * 
	 * @param isNewTemplate,isGlobalTemplate,
	 *            isResellerLevelTemplate
	 * 
	 */
	public static Map<String, AllTemplateDataDto> initializeDataDiffMap(Boolean isNewTemplate, Boolean isGlobalTemplate, Boolean isResellerLevelTemplate) {
		Map<String, AllTemplateDataDto> oldNewEntityDtoMap = new HashMap<>();
		if (BooleanUtils.isTrue(isResellerLevelTemplate)) {
			return oldNewEntityDtoMap;
		}
		oldNewEntityDtoMap.put(NEW_TEMPLATE_DATA, initializeAllTemplateDataObject());
		if (BooleanUtils.isFalse(isNewTemplate) && BooleanUtils.isFalse(isGlobalTemplate)) {
			oldNewEntityDtoMap.put(OLD_TEMPLATE_DATA, initializeAllTemplateDataObject());
		}
		return oldNewEntityDtoMap;
	}
	
	/**
	 * 
	 * Utility method to update object with Referral Template Data
	 * 
	 * 1. Validate Request
	 * 2. Fetch Object with given key
	 * 3. Update Object
	 * 
	 * @param oldNewEntityDtoMap,
	 *            referralTemplateData,key
	 * 
	 */
	public static void addReferralDataToAllTemplateData(Map<String, AllTemplateDataDto> oldNewEntityDtoMap, ReferralTemplateData referralTemplateData, String key) {
		if (validateInputData(oldNewEntityDtoMap, key) && checkIfAllTemplateDataInitialized(oldNewEntityDtoMap.get(key)) && referralTemplateData != null)
			oldNewEntityDtoMap.get(key).setReferralTemplateData(new ReferralTemplateDataDto(referralTemplateData));
	}
	
	/**
	 * 
	 * Utility method to update object with Sources Data
	 * 
	 * 1. Validate Request
	 * 2. Fetch and Validate Object with given fieldName
	 * 3. Update Object
	 * 
	 * @param oldNewEntityDtoMap,
	 *            sourcesData,fieldName
	 * 
	 */
	public static void addSourcesToAllTemplateData(Map<String, AllTemplateDataDto> oldNewEntityDtoMap, Map<String, List<TemplateSourceDto>> sourcesData, String fieldName) {
		if (MapUtils.isEmpty(oldNewEntityDtoMap) || (Objects.isNull(oldNewEntityDtoMap.get(OLD_TEMPLATE_DATA)) && Objects.isNull(oldNewEntityDtoMap.get(NEW_TEMPLATE_DATA)))
				|| StringUtils.isBlank(fieldName) || MapUtils.isEmpty(sourcesData)) {
			return;
		}
		if (StringUtils.equalsIgnoreCase(fieldName, SELECTED_SOURCES)) {
			if (checkIfAllTemplateDataInitialized(oldNewEntityDtoMap.get(OLD_TEMPLATE_DATA)))
				oldNewEntityDtoMap.get(OLD_TEMPLATE_DATA).getAdditionalDataParams().setSelectedSources(sourcesData.get(OLD_SELECTED_SOURCES));
			if (checkIfAllTemplateDataInitialized(oldNewEntityDtoMap.get(NEW_TEMPLATE_DATA)))
				oldNewEntityDtoMap.get(NEW_TEMPLATE_DATA).getAdditionalDataParams().setSelectedSources(sourcesData.get(NEW_SELECTED_SOURCES));
			return;
		} else if (StringUtils.equalsIgnoreCase(fieldName, REFERRAL_SOURCES)) {
			if (checkIfAllTemplateDataInitialized(oldNewEntityDtoMap.get(OLD_TEMPLATE_DATA)))
				oldNewEntityDtoMap.get(OLD_TEMPLATE_DATA).getAdditionalDataParams().setSelectedReferralSources(sourcesData.get(OLD_SELECTED_SOURCES));
			if (checkIfAllTemplateDataInitialized(oldNewEntityDtoMap.get(NEW_TEMPLATE_DATA)))
				oldNewEntityDtoMap.get(NEW_TEMPLATE_DATA).getAdditionalDataParams().setSelectedReferralSources(sourcesData.get(NEW_SELECTED_SOURCES));
			return;
		}
		
		if (checkIfAllTemplateDataInitialized(oldNewEntityDtoMap.get(OLD_TEMPLATE_DATA)))
			oldNewEntityDtoMap.get(OLD_TEMPLATE_DATA).getAdditionalDataParams().setSelectedAndroidSources(sourcesData.get(OLD_SELECTED_SOURCES));
		if (checkIfAllTemplateDataInitialized(oldNewEntityDtoMap.get(NEW_TEMPLATE_DATA)))
			oldNewEntityDtoMap.get(NEW_TEMPLATE_DATA).getAdditionalDataParams().setSelectedAndroidSources(sourcesData.get(NEW_SELECTED_SOURCES));
	}
	
	/**
	 * 
	 * Utility method to update object with Location Template Data
	 * 
	 * 1. Validate Request
	 * 2. Fetch and Validate Object
	 * 3. Update Object
	 * 
	 * @param oldNewEntityDtoMap,
	 *            locationDiffMap
	 * 
	 */
	public static void addLocationTemplateDataToAllTemplateData(Map<String, AllTemplateDataDto> oldNewEntityDtoMap, Map<String, List<Integer>> locationDiffMap) {
		if (MapUtils.isEmpty(oldNewEntityDtoMap) || (Objects.isNull(oldNewEntityDtoMap.get(OLD_TEMPLATE_DATA)) && Objects.isNull(oldNewEntityDtoMap.get(NEW_TEMPLATE_DATA)))
				|| MapUtils.isEmpty(locationDiffMap)) {
			return;
		}
		if (checkIfAllTemplateDataInitialized(oldNewEntityDtoMap.get(OLD_TEMPLATE_DATA)) && CollectionUtils.isNotEmpty(locationDiffMap.get(ORIGINAL_LOCATIONS)))
			oldNewEntityDtoMap.get(OLD_TEMPLATE_DATA).getAdditionalDataParams().setSelectedLocations(locationDiffMap.get(ORIGINAL_LOCATIONS));
		if (checkIfAllTemplateDataInitialized(oldNewEntityDtoMap.get(NEW_TEMPLATE_DATA)) && CollectionUtils.isNotEmpty(locationDiffMap.get(UPDATED_LOCATIONS)))
			oldNewEntityDtoMap.get(NEW_TEMPLATE_DATA).getAdditionalDataParams().setSelectedLocations(locationDiffMap.get(UPDATED_LOCATIONS));
	}
	
	/**
	 * 
	 * Utility method to update object with Custom Url Data
	 * 
	 * 1. Validate Request
	 * 2. Fetch Object with given key
	 * 3. Update Object
	 * 
	 * @param oldNewEntityDtoMap,
	 *            customUrlMap,key
	 * 
	 */
	public static void addCustomUrlMapToAllTemplateData(Map<String, AllTemplateDataDto> oldNewEntityDtoMap, Map<String, String> customUrlMap, String key) {
		if (validateInputData(oldNewEntityDtoMap, key) && checkIfAllTemplateDataInitialized(oldNewEntityDtoMap.get(key))) {
			oldNewEntityDtoMap.get(key).getAdditionalDataParams().setCustomCampaignUrlMap(customUrlMap);
		}
	}
	
	/**
	 * 
	 * Utility method to create map of original and updated locations from list
	 * 
	 * @param originalLocationIds,
	 *            updatedLocationIds
	 * 
	 */
	public static Map<String, List<Integer>> createLocationDiffMapForLocationTemplate(List<Integer> originalLocationIds, List<Integer> updatedLocationIds) {
		Map<String, List<Integer>> locDiffMap = new HashMap<>();
		locDiffMap.put(ORIGINAL_LOCATIONS, originalLocationIds);
		locDiffMap.put(UPDATED_LOCATIONS, updatedLocationIds);
		return locDiffMap;
	}
	
	/**
	 * 
	 * This method is used to fetch Dto Object based upon event type
	 * 
	 * @param eventType
	 */
	public static Class<?> findClassBasesUponEventType(String eventType) {
		switch (eventType) {
			case TEMPLATE_ENTITY:
				return AllTemplateDataDto.class;
				
			case CAMPAIGN_ENTITY:
				return AllCampaignDataDTO.class;
				
			default:
				return null;
		}
	}
	
	/**
	 * 
	 * This method is used to fetch Map key based upon event type
	 * 
	 * @param eventType
	 */
	public static String getKeyBasedUponEventType(String eventType) {
		switch (eventType) {
			case TEMPLATE_ENTITY:
				return TEMPLATE_DATA;
				
			case CAMPAIGN_ENTITY:
				return CAMPAIGN_DATA;
				
			default:
				return StringUtils.EMPTY;
		}
	}
}
