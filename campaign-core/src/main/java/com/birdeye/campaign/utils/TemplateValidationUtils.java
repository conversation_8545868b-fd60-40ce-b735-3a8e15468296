package com.birdeye.campaign.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;

import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.dto.BusinessTemplateEntity;
import com.birdeye.campaign.enums.EmailTemplateTypes;
import com.birdeye.campaign.platform.constant.TemplateTypeEnum;
import com.birdeye.template.exception.TemplateConfigException;
import com.birdeye.template.sms.dto.SmsTemplateMessage;

public class TemplateValidationUtils {
	
	private TemplateValidationUtils() {
		
	}
	
	public static void validateSmsTemplateInput(SmsTemplateMessage smsTemplateMessage, String smsTemplateType, boolean highCharacterLimitFlag) {
		
		if (isInvalidSMSTemplate(smsTemplateType)) {
			throw new TemplateConfigException(HttpStatus.BAD_REQUEST, ErrorCodes.INVALID_SMS_TEMPLATE_TYPE.getMessage());
		}
		
		if (smsTemplateMessage == null) {
			throw new TemplateConfigException(HttpStatus.BAD_REQUEST, ErrorCodes.MISSING_SMS_TEMPLATE_DATA.getMessage());
		}
		
		validateTemplateContent(smsTemplateMessage, smsTemplateType,highCharacterLimitFlag);
	}
	
	public static String validateTemplateType(String templateType) {
		if (templateType == null) {
			throw new TemplateConfigException(HttpStatus.BAD_REQUEST, ErrorCodes.INVALID_SMS_TEMPLATE_TYPE.getMessage());
		} else {
			if (templateType.contains(Constants.SMS)) {
				templateType = templateType.replace(Constants.SMS, "");
			}
			if (!(templateType.equalsIgnoreCase(Constants.CX_TEMPLATE_TYPE) || templateType.equalsIgnoreCase(Constants.REVIEW_REQUEST_TEMPLATE_TYPE)
					|| templateType.equalsIgnoreCase(Constants.SURVEY_REQUEST_TEMPLATE_TYPE) || templateType.equalsIgnoreCase(Constants.REFERRAL_TEMPLATE_TYPE)
					|| templateType.equalsIgnoreCase(Constants.PROMOTION_TEMPLATE_TYPE) || StringUtils.equalsIgnoreCase(templateType, EmailTemplateTypes.APPOINTMENT_REMINDER.name())
					|| StringUtils.equalsIgnoreCase(templateType, EmailTemplateTypes.APPOINTMENT_RECALL.name())
					|| StringUtils.equalsIgnoreCase(templateType, EmailTemplateTypes.APPOINTMENT_FORM.name()))) {
				throw new TemplateConfigException(HttpStatus.BAD_REQUEST, ErrorCodes.INVALID_SMS_TEMPLATE_TYPE.getMessage());
			}
		}
		return templateType;
	}
	
	private static void validateTemplateContent(SmsTemplateMessage smsTemplateMessage, String smsTemplateType, boolean highCharacterLimitFlag) {
		if (StringUtils.equalsIgnoreCase(Constants.PROMOTION_TEMPLATE_TYPE, smsTemplateType))
			return;
		
		if (isInvalidSMSLength(smsTemplateMessage,smsTemplateType,highCharacterLimitFlag)) {
			throw new TemplateConfigException(HttpStatus.BAD_REQUEST, ErrorCodes.INVALID_SMS_MESSAGE.getMessage());
		}
		
		if (StringUtils.isNotBlank(smsTemplateMessage.getQuestion()) && (smsTemplateMessage.getQuestion().length() < 3 || smsTemplateMessage.getQuestion().length() > 200)) {
			throw new TemplateConfigException(HttpStatus.BAD_REQUEST, ErrorCodes.INVALID_QUESTION.getMessage());
		}
		
		if (StringUtils.isNotBlank(smsTemplateMessage.getSentimentHeading()) && (smsTemplateMessage.getSentimentHeading().length() < 3 || smsTemplateMessage.getSentimentHeading().length() > 200)) {
			throw new TemplateConfigException(HttpStatus.BAD_REQUEST, ErrorCodes.INVALID_QUESTION.getMessage());
		}
		
		if (StringUtils.isNotBlank(smsTemplateMessage.getStarHeading()) && (smsTemplateMessage.getStarHeading().length() < 3 || smsTemplateMessage.getStarHeading().length() > 200)) {
			throw new TemplateConfigException(HttpStatus.BAD_REQUEST, ErrorCodes.INVALID_QUESTION.getMessage());
		}
		
		if (StringUtils.isNotBlank(smsTemplateMessage.getFormUrl()) && (smsTemplateMessage.getFormUrl().length() > 100)) {
			throw new TemplateConfigException(HttpStatus.BAD_REQUEST, ErrorCodes.INVALID_FORM_URL.getMessage());
		}
		
	}

	private static boolean isInvalidSMSLength(SmsTemplateMessage smsTemplateMessage, String smsTemplateType, boolean highCharacterLimitFlag) {
         int characterLimit = highCharacterLimitFlag ? Constants.HIGH_CHARACTER_LIMIT : Constants.DEFAULT_CHARACTER_LIMIT;
		if (StringUtils.equalsAnyIgnoreCase(smsTemplateType, Constants.APPOINTMENT_REMINDER_TYPE, Constants.APPOINTMENT_RECALL_TYPE, Constants.APPOINTMENT_FORM_TYPE)) {
			return StringUtils.isBlank(smsTemplateMessage.getMessageBody()) || smsTemplateMessage.getMessageBody().length() < 5 || smsTemplateMessage.getMessageBody().length() > 500;
		}
		return StringUtils.isBlank(smsTemplateMessage.getMessageBody()) || smsTemplateMessage.getMessageBody().length() < 5 || smsTemplateMessage.getMessageBody().length() > characterLimit;
	}
	
	public static boolean isInvalidSMSTemplate(String smsTemplateType) {
		return StringUtils.isBlank(smsTemplateType) || !StringUtils.equalsAnyIgnoreCase(smsTemplateType, "review_request", "survey_request", EmailTemplateTypes.PROMOTION.name(),
				EmailTemplateTypes.CUSTOMER_EXPERIENCE.name(), EmailTemplateTypes.REFERRAL.name(), EmailTemplateTypes.APPOINTMENT_REMINDER.name(),
				EmailTemplateTypes.APPOINTMENT_RECALL.name(), EmailTemplateTypes.APPOINTMENT_FORM.name());
	}
	
	public static boolean isInvalidEmailTemplate(String templateType) {
		return StringUtils.isBlank(templateType) || !StringUtils.equalsAnyIgnoreCase(templateType, EmailTemplateTypes.REVIEW_REQUEST_NEW.name(), EmailTemplateTypes.SURVEY_REQUEST.name(),
				EmailTemplateTypes.PROMOTION.name(), EmailTemplateTypes.CUSTOMER_EXPERIENCE.name(), EmailTemplateTypes.REFERRAL.name(), EmailTemplateTypes.APPOINTMENT_REMINDER.name(),
				EmailTemplateTypes.APPOINTMENT_RECALL.name(), EmailTemplateTypes.APPOINTMENT_FORM.name());
	}
	
	public static boolean validateSMSTemplateForHighCharacterLimit(BusinessTemplateEntity smsTemplate, boolean highCharacterLimitFlag) {
		return ((StringUtils.equalsIgnoreCase(smsTemplate.getTemplateType(), TemplateTypeEnum.PROMOTION_SMS.getName()))
				|| ((smsTemplate.getTemplateType().equalsIgnoreCase(TemplateTypeEnum.REVIEW_REQUEST_SMS.getName())
						|| smsTemplate.getTemplateType()
								.equalsIgnoreCase(TemplateTypeEnum.CUSTOMER_EXPERIENCE_SMS.getName())
						|| smsTemplate.getTemplateType().equalsIgnoreCase(TemplateTypeEnum.SURVEY_REQUEST_SMS.getName())
						|| smsTemplate.getTemplateType().equalsIgnoreCase(TemplateTypeEnum.REFERRAL_SMS.getName()))
						&& (highCharacterLimitFlag)));
	}
}
