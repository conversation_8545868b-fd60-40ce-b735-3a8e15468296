package com.birdeye.campaign.utils;

import org.apache.commons.lang3.StringUtils;

import com.birdeye.campaign.platform.constant.CustomerCategoryEnum;

/**
 * 
 * <AUTHOR>
 *
 */
public class CustomerUtils {
	
	private CustomerUtils() {
		
	}
	
	/**
	 * return customer category
	 * 
	 * @param recommended
	 * @param rating
	 * @param sentimentType
	 * @return
	 */
	public static CustomerCategoryEnum getCustomerCategory(Integer recommended, Integer rating, String sentimentType) {
		if (StringUtils.isBlank(sentimentType)) {
			return null;
		}
		CustomerCategoryEnum customerCategoryEnum = null;
		switch (sentimentType) {
			case "yes_no":
				if (recommended != null) {
					if (recommended == 1) {
						customerCategoryEnum = CustomerCategoryEnum.PROMOTER;
					} else {
						customerCategoryEnum = CustomerCategoryEnum.DETRACTOR;
					}
				}
				break;
			case "sentiment":
				if (recommended != null) {
					if (recommended == 1) {
						customerCategoryEnum = CustomerCategoryEnum.PROMOTER;
					} else if (recommended == -1) {
						customerCategoryEnum = CustomerCategoryEnum.DETRACTOR;
					} else {
						customerCategoryEnum = CustomerCategoryEnum.PASSIVE;
					}
				}
				break;
			case "nps":
				if (rating != null) {
					if (rating.compareTo(6) <= 0) {
						customerCategoryEnum = CustomerCategoryEnum.DETRACTOR;
					} else if (rating.compareTo(8) <= 0) {
						customerCategoryEnum = CustomerCategoryEnum.PASSIVE;
					} else {
						customerCategoryEnum = CustomerCategoryEnum.PROMOTER;
					}
				}
				break;
			case "star":
				if (rating != null) {
					if (rating.compareTo(3) <= 0) {
						customerCategoryEnum = CustomerCategoryEnum.DETRACTOR;
					} else if (rating.compareTo(4) == 0) {
						customerCategoryEnum = CustomerCategoryEnum.PASSIVE;
					} else {
						customerCategoryEnum = CustomerCategoryEnum.PROMOTER;
					}
				}
				break;
			default:
				break;
		}
		return customerCategoryEnum;
	}
}
