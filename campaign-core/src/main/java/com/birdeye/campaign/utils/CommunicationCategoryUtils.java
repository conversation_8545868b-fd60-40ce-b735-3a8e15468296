package com.birdeye.campaign.utils;

import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.birdeye.campaign.platform.constant.CommunicationCategoryEnum;
import com.birdeye.campaign.response.kontacto.CommPreferences;

public class CommunicationCategoryUtils {
	
	private static final Logger LOG = LoggerFactory.getLogger(CommunicationCategoryUtils.class);
	
	private CommunicationCategoryUtils() {
		throw new IllegalStateException("Utility class. Not meant to be instantized.");
	}
	
	/**
	 * Utility method to validate the customer's subscription status for a communication category.
	 * 
	 * @param emailCategory
	 * @param customerCommPreferences
	 * @return
	 */
	public static boolean isCustomerOptedInForEmailCategory(String emailCategory, CommPreferences customerCommPreferences) {
		if (Objects.isNull(customerCommPreferences)) {
			LOG.warn("isCustomerOptedInForEmailCategory - Blank value received for customer's communication preferences!");
			return true;
		}
		
		CommunicationCategoryEnum communicationCategory = CommunicationCategoryEnum.getCommunicationCategoryByInputCategory(emailCategory);
		
		switch (communicationCategory) {
			case SERVICE:
				return customerCommPreferences.getEmailPreferences().isServiceOptin();
			
			case FEEDBACK:
				return customerCommPreferences.getEmailPreferences().isFeedbackOptin();
			
			case MARKETING:
				return customerCommPreferences.getEmailPreferences().isMarketingOptin();
			
			default:
				LOG.info("isCustomerOptedInForEmailCategory - RECEIVED INVALID COMMUNICATION CATEGORY: {}!", emailCategory);
		}
		
		return true;
	}
	
	/**
	 * Utility method to prepare communication failure reason based on email category & customer's opt-out type.
	 * 
	 * @param emailCategory
	 * @param unsubscribeReason
	 * @return
	 */
	public static String prepareCommunicationFailureMessageForEmail(String emailCategory, CommPreferences customerCommPreferences) {
		String unsubscribeReason = fetchCustomerUnsubscribeReasonForEmail(emailCategory, customerCommPreferences);
		
		String failureReason = null;
		switch (unsubscribeReason) {
			case "unsubscribe":
				failureReason = StringUtils.join("Contact unsubscribed from ", emailCategory.toLowerCase(), " emails");
				break;
			case "spamreport":
				failureReason = "Contact has marked the email spam";
				break;
			case "dropped":
				failureReason = "Email has been dropped in past";
				break;
			case "bounce":
				failureReason = "Email has bounced in past";
				break;
			case "optout":
				failureReason = StringUtils.join("Contact has been restricted from ", emailCategory.toLowerCase(), " emails");
				break;
			default:
				LOG.info("prepareCommunicationFailureMessage - RECEIVED FAILURE REASON: {}", unsubscribeReason);
				failureReason = StringUtils.EMPTY;
		}
		return failureReason;
	}
	
	private static String fetchCustomerUnsubscribeReasonForEmail(String emailCategory, CommPreferences customerCommPreferences) {
		if (StringUtils.equalsIgnoreCase(emailCategory, CommunicationCategoryEnum.FEEDBACK.getCommunicationCategory())) {
			return customerCommPreferences.getEmailPreferences().getFeedbackOptoutType();
		} else if (StringUtils.equalsIgnoreCase(emailCategory, CommunicationCategoryEnum.SERVICE.getCommunicationCategory())) {
			return customerCommPreferences.getEmailPreferences().getServiceOptoutType();
		} else {
			return customerCommPreferences.getEmailPreferences().getMarketingOptoutType();
		}
	}
	
	/**
	 * Utility method to validate the customer's subscription status for a communication category.
	 *
	 * @param smsCategory
	 * @param customerCommPreferences
	 * @return
	 */
	public static boolean isCustomerOptedInForSmsCategory(String smsCategory, CommPreferences customerCommPreferences) {
		if (Objects.isNull(customerCommPreferences)) {
			LOG.warn("isCustomerOptedInForSmsCategory - Blank value received for customer's communication preferences!");
			return true;
		}
		
		CommunicationCategoryEnum communicationCategory = CommunicationCategoryEnum.getCommunicationCategoryByInputCategory(smsCategory);
		
		switch (communicationCategory) {
			case SERVICE:
				return customerCommPreferences.getSmsPreferences().isServiceOptin();
			
			case FEEDBACK:
				return customerCommPreferences.getSmsPreferences().isFeedbackOptin();
			
			case MARKETING:
				return customerCommPreferences.getSmsPreferences().isMarketingOptin();
			
			default:
				LOG.info("isCustomerOptedInForSmsCategory - RECEIVED INVALID COMMUNICATION SMS CATEGORY: {}!", smsCategory);
		}
		
		return true;
	}
	
	public static String prepareCommunicationFailureMessageForSms(String smsCategory, CommPreferences customerCommPreferences) {
		String unsubscribeReason = fetchCustomerUnsubscribeReasonForSms(smsCategory, customerCommPreferences);
		
		String failureReason = null;
		if (unsubscribeReason == null) {
			failureReason = "Customer opted out of receiving text messages"; // or any sensible default
		}else {
			switch (unsubscribeReason) {
				case "unsubscribe":
					failureReason = StringUtils.join("Contact unsubscribed from ", smsCategory.toLowerCase(), " texts");
					break;
				case "invalid":
					failureReason = "Contact has marked the communication as invalid";
					break;
				case "Opted out":
					failureReason = StringUtils.join("Contact has been restricted from ", smsCategory.toLowerCase(), " texts");
					break;
				default:
					LOG.info("prepareCommunicationFailureMessage - RECEIVED FAILURE REASON: {}", unsubscribeReason);
					failureReason = "Customer opted out of receiving text messages";
			}
		}
		return failureReason;
	}
	
	private static String fetchCustomerUnsubscribeReasonForSms(String smsCategory, CommPreferences customerCommPreferences) {
		if (StringUtils.equalsIgnoreCase(smsCategory, CommunicationCategoryEnum.FEEDBACK.getCommunicationCategory())) {
			return customerCommPreferences.getSmsPreferences().getFeedbackOptoutType();
		} else if (StringUtils.equalsIgnoreCase(smsCategory, CommunicationCategoryEnum.SERVICE.getCommunicationCategory())) {
			return customerCommPreferences.getSmsPreferences().getServiceOptoutType();
		} else {
			return customerCommPreferences.getSmsPreferences().getMarketingOptoutType();
		}
	}
}
