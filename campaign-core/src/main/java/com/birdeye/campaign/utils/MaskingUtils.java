package com.birdeye.campaign.utils;

import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.SystemPropertiesCache;

public class MaskingUtils {
	
	private MaskingUtils() {
		
	}
	
	private static final Logger		logger				= LoggerFactory.getLogger(MaskingUtils.class);
	
	private static final String		AT_THE_RATE_SYMBOL	= "@";
	
	private static final Integer	TEN					= 10;
	
	private static final Integer	ZERO				= 0;
	
	private static final Integer	SIX					= 6;
	
	private static final Integer	THREE				= 3;
	
	private static final String		STAR_SYMBOL			= "*";
	
	private static final Integer	FOUR				= 4;
	
	private static Integer getPropertyWithDefaultValue(String inputName, Integer defaultValue) {
		return CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty(inputName, defaultValue);
	}
	
	/* ----------------------- EMAIL MASKING ----------------------- */
	
	public static String maskAndEncodeEmail(String email) {
		try {
			if (StringUtils.isBlank(email) || !isValidEmailFormat(email)) {
				return email;
			}
			return StringUtils.join(maskEmail(email), "[", EncodeUtils.getBase64EncodedString(email), "]");
		} catch (Exception e) {
			logger.error("maskAndEncodeEmail :: Exception occurred in method for email {} and exception {}", email, ExceptionUtils.getStackTrace(e));
		}
		return email;
	}
	
	/**
	 * 
	 * Email Masking (Non-Regex):
	 * - If username ≥ 6: show first 3, mask the rest.
	 * - If username < 6: show first 3 (or fewer), mask rest and pad to length 6.
	 * 
	 * @param email
	 * 
	 */
	private static String maskEmail(String email) {
		int atIndex = email.indexOf(AT_THE_RATE_SYMBOL);
		String username = email.substring(ZERO, atIndex);
		String domain = email.substring(atIndex);
		Integer threshold = getPropertyWithDefaultValue("email.masking.username.length", SIX);
		Integer visibleLength = getPropertyWithDefaultValue("email.masking.visible.length", THREE);
		
		if (username.length() >= threshold) {
			return StringUtils.join(username.substring(ZERO, visibleLength), StringUtils.repeat(STAR_SYMBOL, username.length() - visibleLength), domain);
		} else {
			String visible = StringUtils.left(username, visibleLength);
			int remaining = Math.max(ZERO, username.length() - visibleLength);
			int padding = threshold - (visible.length() + remaining);
			return StringUtils.join(visible, StringUtils.repeat(STAR_SYMBOL, remaining + padding), domain);
		}
	}
	
	/**
	 * 
	 * Lightweight email format validator.
	 * 
	 * @param email
	 * 
	 */
	private static boolean isValidEmailFormat(String email) {
		if (StringUtils.isBlank(email)) return false;
		int atIndex = email.indexOf(AT_THE_RATE_SYMBOL);
		return atIndex > ZERO && atIndex < email.length() - 1;
	}
	
	/* ----------------------- PHONE MASKING ----------------------- */
	
	/**
	 * 
	 * Phone Masking (Non-Regex):
	 * - Always show first 4 digits, mask the remaining 6 digits.
	 * 
	 * @param phone
	 * 
	 */
	private static String maskPhone(String phone) {
		Integer visibleLength = getPropertyWithDefaultValue("phone.number.visible.length", FOUR);
		return StringUtils.join(StringUtils.repeat(STAR_SYMBOL, phone.length() - visibleLength), phone.substring(phone.length() - visibleLength, phone.length()));
	}
	
	public static String maskAndEncodePhone(String phone) {
		if (StringUtils.isBlank(phone) || phone.length() < TEN) {
			return phone;
		}
		try {
			return StringUtils.join(maskPhone(phone), "[", EncodeUtils.getBase64EncodedString(phone), "]");
		} catch (Exception e) {
			logger.error("maskAndEncodePhone :: Exception occurred in method for phone {} and exception {}", phone, ExceptionUtils.getStackTrace(e));
		}
		return phone;
	}
}
