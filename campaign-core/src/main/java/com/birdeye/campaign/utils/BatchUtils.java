package com.birdeye.campaign.utils;

import java.util.Iterator;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.google.common.collect.Lists;

public class BatchUtils {
	
	private static Logger logger = LoggerFactory.getLogger(BatchUtils.class);

	public static <T> void doInBatch(List<T> requestList, int batchSize,
			BatchCallBack<T> callback) {
		if (!requestList.isEmpty()) {
			List<List<T>> batches = Lists.partition(requestList, batchSize);
			Iterator<List<T>> batchItr = batches.iterator();
			while (batchItr.hasNext()) {
				List<T> dataSet = batchItr.next();
				logger.debug("Calling batch with dataset: {} where batchSize is: {}", dataSet.size(),batchSize);
				callback.doInBatch(dataSet);
			}
		}
	}

	public static interface BatchCallBack<A> {
		public void doInBatch(List<A> data);
	}
}