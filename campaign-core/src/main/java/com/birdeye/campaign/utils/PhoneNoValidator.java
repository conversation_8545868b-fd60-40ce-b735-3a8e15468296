package com.birdeye.campaign.utils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;

/**
 * <AUTHOR>
 */
public class PhoneNoValidator {
	
	private PhoneNoValidator() {
		
	}
	private static final Logger logger = Logger.getLogger(PhoneNoValidator.class.getName());

	public static boolean isAValidUSPhoneNumber(String phoneNo) {
		return isAnyValidPhoneNumber(phoneNo, Arrays.asList("US", "CA"));
	}

	public static boolean isAnyValidPhoneNumber(String phoneNo, List<String> codes) {
		boolean isValid = Boolean.FALSE;
		
		for (String code : codes) {
			// if code is MX and not valid
			if ("MX".equalsIgnoreCase(code) && !isValidPhoneNumber(phoneNo, code)) {
				code = "US";
			}
		    if (isValidPhoneNumber(phoneNo, code)) {
				isValid = Boolean.TRUE;
				break;
			} 
		}
		return isValid;
	}

	public static boolean isValidPhoneNumber(String number, String code) {
		String countryCode = CoreUtils.getIsoCountryCode(code);
		PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance();
		List<String> regionCodes = getCountryCodeAndRegionCodes(countryCode);
		boolean isValid = false;
		for (String regionCode : regionCodes) {
			isValid = validateNumber(phoneUtil, number, regionCode);
			if (isValid) {
				break;
			}
		}
		return isValid;
	}

	private static List<String> getCountryCodeAndRegionCodes(String countryCode) {
		List<String> regionCodes = null;
		if (countryCode.equals("US") || countryCode.equals("CA")) {
			// Get Region codes for +1 Code
			regionCodes = PhoneNumberUtil.getInstance().getRegionCodesForCountryCode(1);
		} else if (countryCode.equals("GB")) {
			// Get Region codes for +44 Code
			regionCodes = PhoneNumberUtil.getInstance().getRegionCodesForCountryCode(44);
		} else if (countryCode.equals("AU")) {
			// Get Region codes for +61 Code
			regionCodes = PhoneNumberUtil.getInstance().getRegionCodesForCountryCode(61);
		}

		if (regionCodes == null || regionCodes.isEmpty()) {
			regionCodes = Collections.singletonList(countryCode);
		}
		if (!regionCodes.contains(countryCode)) {
			regionCodes.add(countryCode);
		}
		return regionCodes;
	}

	private static boolean validateNumber(PhoneNumberUtil phoneUtil, String number, String countryCode) {
		boolean isValid = Boolean.FALSE;
		try {
			Phonenumber.PhoneNumber phoneNumber = phoneUtil.parse(number, countryCode);
			isValid = phoneUtil.isValidNumberForRegion(phoneNumber, countryCode); // returns true
		} catch (NumberParseException e) {
			logger.log(Level.INFO, "NumberParseException was thrown:  {0}", e.getMessage());
		}
		return isValid;
	}
}
