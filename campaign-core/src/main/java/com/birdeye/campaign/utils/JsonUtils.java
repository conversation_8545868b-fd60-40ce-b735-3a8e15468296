/**
 * @file_name JsonUtils.java
 * @created_date 28 Aug 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
package com.birdeye.campaign.utils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * @file_name JsonUtils.java
 * @created_date 28 Aug 2019
 * <AUTHOR>
 *
 *         This code is copyright (c) BirdEye Software India Pvt. Ltd.
 */
public class JsonUtils {
	
	private JsonUtils() {
	}
	
	private static final Logger			logger			= LoggerFactory.getLogger(JsonUtils.class);
	
	private static final ObjectMapper	OBJECT_MAPPER	= new ObjectMapper();
	
	// Common configuration settings.
	static {
		OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
	}
	
	public static Map<String, Object> jsonToMap(String json) throws IOException {
		return OBJECT_MAPPER.readValue(json, new TypeReference<HashMap<String, Object>>() {
		});
	}
	
	public static String toJSON(Object object) {
		try {
			return OBJECT_MAPPER.writeValueAsString(object);
		} catch (Exception e) {
			logger.error("Error writing JSON for {}", object, e);
		}
		return null;
	}
	
}