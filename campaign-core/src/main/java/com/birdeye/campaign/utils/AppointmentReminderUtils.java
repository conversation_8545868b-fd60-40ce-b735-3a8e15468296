package com.birdeye.campaign.utils;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.dto.AppointmentInfoLiteDTO;
import com.birdeye.campaign.dto.AppointmentScheduleInfo;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.entity.CampaignCondition;
import com.birdeye.campaign.entity.ReviewRequest;
import com.birdeye.campaign.enums.AppointmentStatusEnum;
import com.birdeye.campaign.enums.GroupByTimeEnum;
import com.birdeye.campaign.enums.ScheduleAtEnum;
import com.birdeye.campaign.platform.constant.LocaleTextEnum;
import com.birdeye.campaign.platform.entity.BaseCommunicationEntity;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

public class AppointmentReminderUtils {
	
	private static final String	APPOINTMENT_REMINDER_ACCEPTABLE_DELAY				= "appointment.reminder.acceptable.delay";
	
	private static final String	APPOINTMENT_DATE_FORMAT								= "E, MMM dd YYYY";
	
	private static final String	APPOINTMENT_DATE_FORMAT_CURRENT_YEAR				= "E, MMM dd";
	
	private static final String	APPOINTMENT_TIME_FORMAT								= "hh:mm a";
	
	private static final String	APPOINTMENT_DATE_TIME_FORMAT						= "E, MMM dd YYYY, hh:mm a";
	
	private static final String	SPANISH_APPOINTMENT_DATE_FORMAT						= "dd 'de' MMM 'de' yyyy";
	
	private static final String	SPANISH_APPOINTMENT_DATE_FORMAT_CURRENT_YEAR		= "dd 'de' MMM";
	
	private static final String	SPANISH_APPOINTMENT_DATE_TIME_FORMAT				= "dd 'de' MMM 'de' yyyy hh:mm a";
	
	private static final String	SPANISH_APPOINTMENT_DATE_TIME_FORMAT_CURRENT_YEAR	= "dd 'de' MMM hh:mm a";
	
	private static final String	APPOINTMENT_DATE_TIME_FORMAT_CURRENT_YEAR			= "E, MMM dd, hh:mm a";
	
	public static final Logger	logger												= LoggerFactory.getLogger(AppointmentReminderUtils.class);
	
	private AppointmentReminderUtils() {
		
	}
	
	public static Integer getAppointmentReminderValidSchedulingHrs(ZonedDateTime zonedAppointmentStartTime, List<Integer> schedulingHours) {
		ZonedDateTime currentTime = ZonedDateTime.ofInstant(Instant.now(), ZoneId.of("UTC"));
		Duration duration = Duration.between(currentTime, zonedAppointmentStartTime);
		long scheduleMinutes = duration.getSeconds();
		for (Integer hour : schedulingHours) {
			int minutes = hour * 60 * 60;
			if (scheduleMinutes > minutes) {
				return hour;
			}
		}
		return null;
	}
	
	public static boolean isValidRequestExecutionTime(List<Integer> schedulingHours, AppointmentInfoLiteDTO appointmentInfo) {
		long appointmentStartTimeEpochMillis = appointmentInfo.getStartTime(); // appointment external service - 1664517453000
		ZonedDateTime zonedAppointmentStartTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(appointmentStartTimeEpochMillis), ZoneId.of("UTC"));
		return isValidExceutionTime(zonedAppointmentStartTime, schedulingHours);
	}
	
	private static boolean isValidExceutionTime(ZonedDateTime zonedAppointmentStartTime, List<Integer> campaignSchedulingHours) {
		ZonedDateTime currentTime = ZonedDateTime.ofInstant(Instant.now(), ZoneId.of("UTC"));
		Duration durationBeforeAppointment = Duration.between(currentTime, zonedAppointmentStartTime);
		if (durationBeforeAppointment.isNegative())
			return false;
		
		long beforeAppointmentMinutes = durationBeforeAppointment.toMinutes();
		long acceptableDelay = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty(APPOINTMENT_REMINDER_ACCEPTABLE_DELAY, 5); // in minutes
		for (Integer configHour : campaignSchedulingHours) {
			int configMinutes = configHour * 60;
			// hour - scheduledHour < 5 minutes
			if (configMinutes - beforeAppointmentMinutes >= 0 && configMinutes - beforeAppointmentMinutes <= acceptableDelay) {
				return true;
			}
		}
		return false;
	}
	
	/**
	 * 
	 * @param startTime
	 * @param timeZone
	 * @param locale
	 * @return
	 * 
	 *         formatted appointment date time
	 */
	public static String getAppointmentDate(Long startTime, String timeZoneId, String locale) {
		boolean appointmentInCurrentYear = BooleanUtils.isTrue(DateTimeUtils.checkDateInCurrentYear(startTime, timeZoneId));
		if (StringUtils.isBlank(locale)) {
			return DateTimeUtils.getFormattedDateTimeFromEpoch(startTime, timeZoneId, appointmentInCurrentYear ? APPOINTMENT_DATE_FORMAT_CURRENT_YEAR : APPOINTMENT_DATE_FORMAT);
		}
		String[] localeArray = StringUtils.split(locale, "-");
		String language = localeArray[0];
		String region = locale.length() > 1 ? localeArray[1] : StringUtils.EMPTY;
		String appointmentDateFormat = getAppointmentDateFormat(language, appointmentInCurrentYear);
		return DateTimeUtils.getFormattedDateTimeFromEpoch(startTime, timeZoneId, appointmentDateFormat, new Locale(language, region));
	}
	
	private static String getAppointmentDateFormat(String languageLocaleCode, boolean appointmentInCurrentYear) {
		if (StringUtils.equalsIgnoreCase(languageLocaleCode, "ES")) {
			return appointmentInCurrentYear ? SPANISH_APPOINTMENT_DATE_FORMAT_CURRENT_YEAR : SPANISH_APPOINTMENT_DATE_FORMAT;
		}
		return appointmentInCurrentYear ? APPOINTMENT_DATE_FORMAT_CURRENT_YEAR : APPOINTMENT_DATE_FORMAT;
	}
	
	private static String getAppointmentDateTimeFormat(String languageLocaleCode, boolean appointmentInCurrentYear) {
		if (StringUtils.equalsIgnoreCase(languageLocaleCode, "ES")) {
			return appointmentInCurrentYear ? SPANISH_APPOINTMENT_DATE_TIME_FORMAT_CURRENT_YEAR : SPANISH_APPOINTMENT_DATE_TIME_FORMAT;
		}
		return appointmentInCurrentYear ? APPOINTMENT_DATE_TIME_FORMAT_CURRENT_YEAR : APPOINTMENT_DATE_TIME_FORMAT;
	}
	
	/**
	 * return formatted appointment time
	 * Ex. 12:43AM CST
	 * if appointment time is equal to reminder sent time
	 * Ex. 12:43AM CST (Today)
	 * 
	 * @param startTime
	 * @param timeZone
	 * @param locale
	 * @return
	 */
	public static String getAppointmentTime(Long startTime, String timeZoneId, String locale) {
		
		// String timeZoneId = DateTimeUtils.getTimeZneId(timeZone);
		if (StringUtils.isBlank(timeZoneId)) {
			timeZoneId = Constants.AMERICA_LOS_ANGELES;
		}
		String shortTimeZoneId = DateTimeUtils.getShortFormOfBusinessTimezone(timeZoneId, DateTimeUtils.isDaylightSaving(new Date(startTime), timeZoneId));
		String appointmentTime = DateTimeUtils.getFormattedDateTimeFromEpoch(startTime, timeZoneId, APPOINTMENT_TIME_FORMAT);
		String appointmentTimeWithTimezoneId = StringUtils.join(appointmentTime, " ", shortTimeZoneId);
		if (DateTimeUtils.isSameDay(startTime, timeZoneId)) {
			return StringUtils.join(appointmentTimeWithTimezoneId, " (", LocalTextConversionUtils.getTranslatedText(locale, LocaleTextEnum.TODAY), ")");
		}
		return appointmentTimeWithTimezoneId;
	}
	
	/**
	 * 
	 * @param startTime
	 * @param locale
	 * @param timeZoneId
	 * @return Formatted Date for appointment reminder Ex. 25 Sep 2022
	 * 
	 */
	public static String getAppointmentDateTime(Long startTime, String timeZoneId, String locale) {
		// String timeZoneId = DateTimeUtils.getTimeZneId(timeZone);
		String shortTimeZoneId = DateTimeUtils.getShortFormOfBusinessTimezone(timeZoneId, DateTimeUtils.isDaylightSaving(new Date(startTime), timeZoneId));
		boolean appointmentInCurrentYear = BooleanUtils.isTrue(DateTimeUtils.checkDateInCurrentYear(startTime, timeZoneId));
		if (StringUtils.isBlank(locale)) {
			return StringUtils.join(
					DateTimeUtils.getFormattedDateTimeFromEpoch(startTime, timeZoneId, appointmentInCurrentYear ? APPOINTMENT_DATE_TIME_FORMAT_CURRENT_YEAR : APPOINTMENT_DATE_TIME_FORMAT), " ",
					shortTimeZoneId);
		}
		String[] localeArray = StringUtils.split(locale, "-");
		String language = localeArray[0];
		String region = locale.length() > 1 ? localeArray[1] : StringUtils.EMPTY;
		String appointmentDateTimeFormat = getAppointmentDateTimeFormat(language, appointmentInCurrentYear);
		return DateTimeUtils.getFormattedDateTimeFromEpoch(startTime, timeZoneId, appointmentDateTimeFormat, new Locale(language, region));
	}
	
	/**
	 * @param rescheduleButtonEnabled
	 * @param cancelButtonEnabled
	 * @param appointmentSchedulingEnabled
	 * @return text for [Action CTA] token in reminder html template
	 */
	public static String getActionCTAsText(boolean hideRescheduleOption, boolean rescheduleButtonEnabled, boolean hideCancelOption, boolean cancelButtonEnabled, boolean appointmentSchedulingEnabled,
			String locale) {
		if (BooleanUtils.isTrue(appointmentSchedulingEnabled) && BooleanUtils.isTrue(rescheduleButtonEnabled) && BooleanUtils.isFalse(hideRescheduleOption)) {
			return (BooleanUtils.isTrue(cancelButtonEnabled) && BooleanUtils.isFalse(hideCancelOption)) ? LocalTextConversionUtils.getTranslatedText(locale, LocaleTextEnum.RESCHEDULE_OR_CANCEL_TEXT)
					: LocalTextConversionUtils.getTranslatedText(locale, LocaleTextEnum.RESCHEDULE_TEXT);
		} else if (BooleanUtils.isTrue(cancelButtonEnabled) && BooleanUtils.isFalse(hideCancelOption)) {
			return LocalTextConversionUtils.getTranslatedText(locale, LocaleTextEnum.CANCEL_TEXT);
		}
		return StringUtils.EMPTY;
	}
	
	/**
	 * @param confirmButtonEnabled
	 * @param rescheduleButtonEnabled
	 * @param cancelButtonEnabled
	 * @param isSchedulingEnabled
	 * @param appointmentStatus
	 * @param hideRescheduleOption
	 * @param hideCancelOption
	 * @return text for [Applicable CTAs] token in sms template
	 */
	public static String getApplicableCTAsText(Integer confirmButtonEnabled, Integer rescheduleButtonEnabled, Integer cancelButtonEnabled, Integer isSchedulingEnabled, String appointmentStatus,
			Boolean hideRescheduleOption, Boolean hideCancelOption, String locale) {
		boolean confirmTextAllowed = BooleanUtils.isFalse(StringUtils.equalsIgnoreCase(appointmentStatus, AppointmentStatusEnum.CONFIRMED.getType()))
				&& BooleanUtils.isTrue(CoreUtils.getBooleanValueFromInteger(confirmButtonEnabled));
		boolean rescheduleTextAllowed = BooleanUtils.isTrue(CoreUtils.getBooleanValueFromInteger(isSchedulingEnabled))
				&& BooleanUtils.isTrue(CoreUtils.getBooleanValueFromInteger(rescheduleButtonEnabled)) && BooleanUtils.isFalse(hideRescheduleOption);
		boolean cancelTextAllowed = BooleanUtils.isTrue(CoreUtils.getBooleanValueFromInteger(cancelButtonEnabled)) && BooleanUtils.isFalse(hideCancelOption);
		
		// Set flags in MDC (Mapped Diagnostic Context) to control action options in the messenger.
		// These flags indicate which options should be enabled for the user:
		// - "confirmTextEnabled": Enables the "confirm" option if confirmTextAllowed is true
		// - "rescheduleTextEnabled": Enables the "reschedule" option if rescheduleTextAllowed is true
		// - "cancelTextEnabled": Enables the "cancel" option if cancelTextAllowed is true
		// The messenger will read these flags to determine available actions for the user.
		MDC.put("confirmTextEnabled", String.valueOf(confirmTextAllowed));
		MDC.put("rescheduleTextEnabled", String.valueOf(rescheduleTextAllowed));
		MDC.put("cancelTextEnabled", String.valueOf(cancelTextAllowed));
		
		String applicableCTAsText = getAllowedTextForApplicableCTAs(confirmTextAllowed, rescheduleTextAllowed, cancelTextAllowed, locale);
		
		return StringUtils.isNotBlank(applicableCTAsText) ? StringUtils.join(LocalTextConversionUtils.getTranslatedText(locale, LocaleTextEnum.CLICK_HERE_TO_TEXT), applicableCTAsText)
				: LocalTextConversionUtils.getTranslatedText(locale, LocaleTextEnum.CONFIRM_RESCHEDULE_CANCEL_DISABLED_TEXT);
	}
	
	private static String getAllowedTextForApplicableCTAs(boolean confirmTextAllowed, boolean rescheduleTextAllowed, boolean cancelTextAllowed, String locale) {
		if (BooleanUtils.isTrue(rescheduleTextAllowed)) {
			if (BooleanUtils.isTrue(cancelTextAllowed)) {
				return confirmTextAllowed ? LocalTextConversionUtils.getTranslatedText(locale, LocaleTextEnum.CONFIRM_RESCHEDULE_OR_CANCEL_TEXT)
						: LocalTextConversionUtils.getTranslatedText(locale, LocaleTextEnum.RESCHEDULE_OR_CANCEL_TEXT);
			} else {
				return confirmTextAllowed ? LocalTextConversionUtils.getTranslatedText(locale, LocaleTextEnum.CONFIRM_OR_RESCHEDULE_TEXT)
						: LocalTextConversionUtils.getTranslatedText(locale, LocaleTextEnum.RESCHEDULE_TEXT);
			}
		} else {
			if (BooleanUtils.isTrue(cancelTextAllowed)) {
				return confirmTextAllowed ? LocalTextConversionUtils.getTranslatedText(locale, LocaleTextEnum.CONFIRM_OR_CANCEL_TEXT)
						: LocalTextConversionUtils.getTranslatedText(locale, LocaleTextEnum.CANCEL_TEXT);
			} else {
				return confirmTextAllowed ? LocalTextConversionUtils.getTranslatedText(locale, LocaleTextEnum.CONFIRM_TEXT) : StringUtils.EMPTY;
			}
		}
	}
	
	/**
	 * 
	 * @param scheduleInfo
	 * @return
	 *         return hours value from appointment schedule object
	 *         if scheduling in days return day*24
	 */
	
	public static Integer getHoursFromAppointmentSchedule(AppointmentScheduleInfo scheduleInfo) {
		GroupByTimeEnum groupBy = GroupByTimeEnum.getGroupByTimeEnum(scheduleInfo.getScheduleBy());
		if (groupBy == null) {
			logger.warn("unsupported group by time received {}", scheduleInfo.getScheduled());
			return scheduleInfo.getScheduled();
		}
		switch (groupBy) {
			case HOURS:
				return scheduleInfo.getScheduled();
			case DAYS:
				return scheduleInfo.getScheduled() * Constants.TOTAL_HRS_IN_DAY;
			case WEEKS:
				return scheduleInfo.getScheduled() * Constants.TOTAL_DAYS_IN_WEEK * Constants.TOTAL_HRS_IN_DAY;
			case MONTHS:
				return scheduleInfo.getScheduled() * Constants.TOTAL_DAYS_IN_MONTH * Constants.TOTAL_HRS_IN_DAY;
		}
		return scheduleInfo.getScheduled();
	}
	
	/**
	 * 
	 * @param isAppointmentEnabled
	 * 
	 * @return return true if input is 1 else false
	 */
	public static boolean isAppointmentEnabled(Integer isAppointmentEnabled) {
		return (isAppointmentEnabled != null && Boolean.valueOf(isAppointmentEnabled == 1));
	}
	
	/**
	 * 
	 * @param isAnyAppointmentFeatureEnabled
	 * 
	 * @return return true if any appointment feature is 1 else false
	 */
	public static boolean isAnyAppointmentFeatureEnabled(Integer scheduling, Integer reminder, Integer recall, Integer form) {
		return (CoreUtils.isTrueForInteger(scheduling) || CoreUtils.isTrueForInteger(reminder) || CoreUtils.isTrueForInteger(recall) || CoreUtils.isTrueForInteger(form));
	}
	
	/**
	 * Prepares a list of ZonedDateTime objects representing appointment schedules,
	 * based on the provided list of AppointmentScheduleInfo and starting appointment time.
	 *
	 * @param appointmentScheduleInfo
	 *            List of AppointmentScheduleInfo containing schedule information.
	 * @param zonedAppointmentStartTime
	 *            The starting time of the appointment in a specific time zone.
	 * @return A sorted list of ZonedDateTime objects representing appointment schedules.
	 */
	public static List<ZonedDateTime> prepareZonedDateTimeWiseReminderSchedule(CampaignCondition campaignCondition, ZonedDateTime zonedAppointmentStartTime, BusinessEnterpriseEntity business,
			Integer appointmentId) {
		logger.info("Scheduling zonedDateTime wise appointment reminder for appointmentScheduleInfo : {} and zonedAppointmentStartTime : {} and campaignId : {}",
				campaignCondition.getAppointmentScheduleInfo(), zonedAppointmentStartTime, campaignCondition.getCampaignId());
		if (CollectionUtils.isEmpty(campaignCondition.getAppointmentScheduleInfo())) {
			return null;
		}
		List<ZonedDateTime> scheduleReminderTime = campaignCondition.getAppointmentScheduleInfo().stream()
				.map(appointmentSchedule -> computeReminderScheduleZonedDateTime(appointmentSchedule, zonedAppointmentStartTime, campaignCondition, business)).filter(Objects::nonNull)
				.collect(Collectors.toList());
		
		logger.info("Scheduled zonedDateTime wise appointment reminder List : {} for campaign : {} and appointmentId : {}", scheduleReminderTime, campaignCondition.getCampaignId(), appointmentId);
		// Sort the list of ZonedDateTime objects in natural order (ascending)
		if (CollectionUtils.isNotEmpty(scheduleReminderTime)) {
			Collections.sort(scheduleReminderTime, Comparator.naturalOrder());
		}
		return scheduleReminderTime;
	}
	
	/**
	 * Checks if the request for execution time of an appointment is valid.
	 *
	 * @param appointmentInfo
	 *            Information about the appointment.
	 * @param business
	 *            Information about the business.
	 * @param campaignCondition
	 *            Condition for the campaign.
	 * @return True if it's a valid execution time request, false otherwise.
	 */
	public static boolean isValidExecutionTimeRequest(AppointmentInfoLiteDTO appointmentInfo, BusinessEnterpriseEntity business, CampaignCondition campaignCondition, List<Date> appointmentScheduledDates) {
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			String json = objectMapper.writeValueAsString(campaignCondition);
			campaignCondition = objectMapper.readValue(json, campaignCondition.getClass());
			
		} catch (JsonProcessingException e) {
			logger.info("Exception while parsing json to CampaignCondition object for campaign id {} and appointment id {}", campaignCondition.getCampaignId(), appointmentInfo.getAppointmentId());
			e.printStackTrace();
		}
		long appointmentStartTimeEpochMillis = appointmentInfo.getStartTime(); // appointment external service - 1664517453000
		ZoneId zoneId = DateTimeUtils.getZoneIdFromTimeZoneId(business.getTimezoneId());
		ZonedDateTime zonedAppointmentStartTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(appointmentStartTimeEpochMillis), zoneId);
		// Prepare the list of reminder schedules based on campaign conditions
		List<ZonedDateTime> reminderScheduleList = prepareZonedDateTimeWiseReminderSchedule(campaignCondition, zonedAppointmentStartTime, business, appointmentInfo.getAppointmentId());
		// Convert the reminder schedules to UTC timezone for consistency
		reminderScheduleList = convertDateToSpecifiedTimeZone(reminderScheduleList, ZoneId.of("UTC"));
		zonedAppointmentStartTime = zonedAppointmentStartTime.withZoneSameInstant(ZoneId.of("UTC"));
		// Check if the appointment time and reminder schedules are valid for execution
		return isTimeValidForExecution(zonedAppointmentStartTime, reminderScheduleList, appointmentScheduledDates);
	}
	
	/**
	 * Computes the reminder schedule {@link ZonedDateTime} based on the provided {@link AppointmentScheduleInfo}.
	 *
	 * @param appointmentScheduleInfo
	 *            The information about the appointment schedule.
	 * @param zonedAppointmentStartTime
	 *            The start time of the appointment in the desired time zone.
	 * @param campaignCondition
	 *            The condition for the campaign associated with the appointment.
	 * @param business
	 *            The business entity associated with the appointment.
	 * @return The computed reminder schedule {@link ZonedDateTime}, or null if the schedule type is not recognized.
	 */
	public static ZonedDateTime computeReminderScheduleZonedDateTime(AppointmentScheduleInfo appointmentScheduleInfo, ZonedDateTime zonedAppointmentStartTime, CampaignCondition campaignCondition,
			BusinessEnterpriseEntity business) {
		// Check if the appointment schedule type is either APPOINTMENT_TIME or CUSTOM_TIME
		if (StringUtils.equalsAnyIgnoreCase(appointmentScheduleInfo.getScheduleAt(), ScheduleAtEnum.APPOINTMENT_TIME.getScheduleAt(), ScheduleAtEnum.CUSTOM_TIME.getScheduleAt())) {
			// If true, calculate the appointment schedule date and return it
			return getAppointmentScheduleDate(appointmentScheduleInfo, zonedAppointmentStartTime, campaignCondition, business);
		}
		
		return null;
		// if (StringUtils.equalsAnyIgnoreCase(appointmentScheduleInfo.getScheduleAt(), ScheduleAtEnum.APPOINTMENT_TIME.getScheduleAt(),
		// ScheduleAtEnum.CUSTOM_TIME.getScheduleAt())) {
		// If true, calculate the appointment schedule date and return it
		// return getAppointmentScheduleDate(appointmentScheduleInfo, zonedAppointmentStartTime, campaignCondition, business);
		// }
		
		// return null;
		// >>>>>>> dev-BIRD-702
	}
	
	/**
	 * Gets the appointment schedule date based on the provided schedule information and appointment start time.
	 *
	 * @param appointmentScheduleInfo
	 *            Information about the appointment schedule.
	 * @param zonedAppointmentStartTime
	 *            The scheduled start time of the appointment with time zone information.
	 * @return The calculated appointment schedule date.
	 */
	public static ZonedDateTime getAppointmentScheduleDate(AppointmentScheduleInfo appointmentScheduleInfo, ZonedDateTime zonedAppointmentStartTime, CampaignCondition campaignCondition,
			BusinessEnterpriseEntity business) {
		ZonedDateTime appointmentScheduleTime = zonedAppointmentStartTime.withZoneSameInstant(zonedAppointmentStartTime.getZone());
		GroupByTimeEnum groupBy = GroupByTimeEnum.getGroupByTimeEnum(appointmentScheduleInfo.getScheduleBy());
		
		switch (groupBy) {
			case HOURS:
				// If the grouping is by HOURS, subtract the scheduled hours from the appointment start time
				return appointmentScheduleTime.minusHours(appointmentScheduleInfo.getScheduled());
			
			case DAYS:
				// If the grouping is by DAYS, subtract the scheduled hours multiplied by total hours in a day
				appointmentScheduleTime = appointmentScheduleTime.minusHours(appointmentScheduleInfo.getScheduled() * Constants.TOTAL_HRS_IN_DAY);
				break;
			
			case WEEKS:
				// If the grouping is by WEEKS, subtract the scheduled hours multiplied by total days in a week and total hours in a day
				appointmentScheduleTime = appointmentScheduleTime.minusHours(appointmentScheduleInfo.getScheduled() * Constants.TOTAL_DAYS_IN_WEEK * Constants.TOTAL_HRS_IN_DAY);
				break;
			default:
				return null;
		}
		logger.info("After substraction by hours Scheduled reminder at : {} for scheduleBy : {} and campaign : {}", appointmentScheduleTime, appointmentScheduleInfo.getScheduleBy(),
				campaignCondition.getCampaignId());
		// Calculates the custom appointment reminder time based on the provided schedule information.
		appointmentScheduleTime = getCustomAppointmentReminderTime(appointmentScheduleInfo, appointmentScheduleTime, business.getTimezoneId());
		logger.info("After computation by scheduleAt, scheduled reminder at {} for scheduleAt : {} and campaign ID: {} ", appointmentScheduleTime, appointmentScheduleInfo.getScheduleAt(),
				campaignCondition.getCampaignId());
		
		// For DAYS and WEEKS, check if the resulting scheduled day is within allowed days
		if (isScheduledDayWithinAllowedDays(appointmentScheduleInfo.getAllowedDays(), LocalDate.from(appointmentScheduleTime))) {
			return appointmentScheduleTime;
		} else {
			// If not within allowed days, find the nearest scheduled day to the appointment reminder
			return getNearestScheduledDayToAppointmentReminder(zonedAppointmentStartTime, appointmentScheduleTime, appointmentScheduleInfo, campaignCondition, business);
		}
		
		// return appointmentScheduleTime;
		
	}
	
	/**
	 * Checks if the given appointment start time is valid for execution based on the reminder schedule list.
	 *
	 * @param zonedAppointmentStartTime
	 *            The start time of the appointment in a specific time zone.
	 * @param reminderScheduleList
	 *            List of reminder schedule times in the same time zone as the appointment.
	 * @return True if the appointment start time is valid for execution; false otherwise.
	 */
	public static boolean isTimeValidForExecution(ZonedDateTime zonedAppointmentStartTime, List<ZonedDateTime> reminderScheduleList, List<Date> appointmentScheduledDates) {
		ZonedDateTime currentTime = ZonedDateTime.ofInstant(Instant.now(), ZoneId.of("UTC"));
		Duration durationBeforeAppointment = Duration.between(currentTime, zonedAppointmentStartTime);
		// If the duration is negative, the appointment has already passed, so it's not a valid time for a reminder
		if (durationBeforeAppointment.isNegative())
			return false;
		// Convert the duration to minutes
		long beforeAppointmentMinutes = durationBeforeAppointment.toMinutes();
		long acceptableDelay = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty(APPOINTMENT_REMINDER_ACCEPTABLE_DELAY, 5); // in minutes
		
		for (ZonedDateTime scheduleDay : reminderScheduleList) {
			Duration scheduleTimeDuration = Duration.between(scheduleDay, zonedAppointmentStartTime);
			long scheduleTimeInMinutes = scheduleTimeDuration.toMinutes();
			// Check if the reminder time falls within the acceptable delay
			if (scheduleTimeInMinutes - beforeAppointmentMinutes >= 0 && scheduleTimeInMinutes - beforeAppointmentMinutes <= acceptableDelay) {
				appointmentScheduledDates.add(Date.from(scheduleDay.toInstant()));
				// Valid time for sending a reminder
				return true;
			}
		}
		
		logger.info("Execution dropped: Invalid or passed schedule time. Current Time: {}, Reminder Schedule List: {}", currentTime, reminderScheduleList);
		// No valid reminder time found
		return false;
	}
	
	/**
	 * Calculates the custom appointment reminder time based on the provided schedule information.
	 *
	 * @param appointmentScheduleInfo
	 *            Information about the appointment schedule.
	 * @param appointmentscheduleTime
	 *            The original appointment schedule time.
	 * @return The custom appointment reminder time, or the original schedule time if not a custom time.
	 */
	public static ZonedDateTime getCustomAppointmentReminderTime(AppointmentScheduleInfo appointmentScheduleInfo, ZonedDateTime appointmentscheduleTime, String timezoneId) {
		// Check if the appointment is scheduled at a custom time
		if (StringUtils.equalsIgnoreCase(appointmentScheduleInfo.getScheduleAt(), ScheduleAtEnum.CUSTOM_TIME.getScheduleAt())) {
			// Parse the send time from the appointment schedule info
			LocalTime sendTime = LocalTime.parse(appointmentScheduleInfo.getSendTime(), DateTimeFormatter.ofPattern("h:mm a"));
			// ZonedDateTime with the same date and time but with the custom send time
			return appointmentscheduleTime.with(LocalTime.of(sendTime.getHour(), sendTime.getMinute()));
		}
		// if(StringUtils.equalsIgnoreCase(appointmentScheduleInfo.getScheduleAt(), ScheduleAtEnum.CUSTOM_TIME.getScheduleAt())) {
		// // Parse the send time from the appointment schedule info
		// LocalTime sendTime = LocalTime.parse(appointmentScheduleInfo.getSendTime(), DateTimeFormatter.ofPattern("h:mm a"));
		// // ZonedDateTime with the same date and time but with the custom send time
		// return appointmentscheduleTime.with(LocalTime.of(sendTime.getHour(), sendTime.getMinute()));
		// }
		// Return the original appointment schedule time if it's not a custom time
		return appointmentscheduleTime;
	}
	
	/**
	 * Checks if the scheduled day is within the allowed days specified in the appointment schedule.
	 *
	 * @param appointmentScheduleInfo
	 *            Information about allowed days for appointments.
	 * @param appointmentscheduleTime
	 *            The scheduled date and time.
	 * @return true if the scheduled day is within the allowed days, false otherwise.
	 */
	public static boolean isScheduledDayWithinAllowedDays(List<String> allowedDays, LocalDate appointmentscheduleTime) {
		return allowedDays.stream().anyMatch(day -> day.equalsIgnoreCase(appointmentscheduleTime.getDayOfWeek().toString()));
	}
	
	public static List<ZonedDateTime> convertDateToSpecifiedTimeZone(List<ZonedDateTime> dates, ZoneId timezoneId) {
		if (CollectionUtils.isNotEmpty(dates) && timezoneId != null) {
			return dates.stream().map(dateTime -> dateTime.withZoneSameInstant(timezoneId)).collect(Collectors.toList());
		}
		return Collections.emptyList();
	}
	
	/**
	 * Gets the nearest scheduled day to the appointment reminder based on allowed days.
	 * This method finds the nearest forward and backward days based on the schedule time and allowed days.
	 * If one of the nearest days is null, the second nearest day will be selected as the schedule day.
	 * If both nearest forward and backward days are non-null, it calculates the difference between
	 * the forward day and the schedule day, and the difference between the backward day and the schedule day.
	 * The smaller value will be chosen as the schedule day.
	 *
	 * @param zonedAppointmentStartTime
	 *            The scheduled start time of the appointment.
	 * @param appointmentscheduleTime
	 *            The scheduled time for the appointment.
	 * @param appointmentScheduleInfo
	 *            Information about allowed days for appointments.
	 * @return The nearest scheduled day, or null if none is found.
	 */
	public static ZonedDateTime getNearestScheduledDayToAppointmentReminder(ZonedDateTime zonedAppointmentStartTime, ZonedDateTime appointmentscheduleTime,
			AppointmentScheduleInfo appointmentScheduleInfo, CampaignCondition campaignCondition, BusinessEnterpriseEntity business) {
		logger.info("Get nearest schedule time for given reminder schedule day : {} and campaignId : {}", appointmentscheduleTime, campaignCondition.getCampaignId());
		LocalDate scheduleTime = appointmentscheduleTime.toLocalDate();
		
		// Find the nearest allowed days in both directions
		LocalDate nearestForwardDay = findNearestAllowedDay(appointmentScheduleInfo, scheduleTime, true, zonedAppointmentStartTime, appointmentscheduleTime, business.getTimezoneId());
		LocalDate nearestBackwardDay = findNearestAllowedDay(appointmentScheduleInfo, scheduleTime, false, zonedAppointmentStartTime, appointmentscheduleTime, business.getTimezoneId());
		
		logger.info("Nearest forward day {} and nearest backward day {} for reminder schedule : {}", nearestForwardDay, nearestBackwardDay, appointmentscheduleTime);
		// Compare the results and return the nearest scheduled day
		if (nearestForwardDay != null && nearestBackwardDay == null) {
			// If only a forward day is found (no backward day), return appointmentscheduleTime with the nearest forward day.
			return appointmentscheduleTime.with(nearestForwardDay);
		} else if (nearestForwardDay == null && nearestBackwardDay != null) {
			// If only a backward day is found (no forward day), return appointmentscheduleTime with the nearest backward day.
			return appointmentscheduleTime.with(nearestBackwardDay);
		} else if (nearestForwardDay != null && nearestBackwardDay != null) {
			// If both a forward and a backward day are found, compare the days to find the nearest one.
			long backwardDaysDifference = Math.abs(ChronoUnit.DAYS.between(scheduleTime, nearestBackwardDay));
			long forwardDaysDifference = Math.abs(ChronoUnit.DAYS.between(scheduleTime, nearestForwardDay));
			logger.info("Backward day difference : {}, forward day difference : {}, for schedule time {}", backwardDaysDifference, forwardDaysDifference, scheduleTime);
			
			if (backwardDaysDifference < forwardDaysDifference) {
				// If the backward day is closer to scheduleDay, return appointmentscheduleTime with the nearest backward day.
				return appointmentscheduleTime.with(nearestBackwardDay);
			} else {
				// If the forward day is closer or if they are equidistant to scheduleDay, return appointmentscheduleTime with the nearest forward day.
				return appointmentscheduleTime.with(nearestForwardDay);
			}
		}
		// If no suitable day is found, return null
		return null;
		
	}
	
	/*
	 * @param scheduleTime The reference date for finding the nearest allowed day.
	 * @param isForward Indicates whether to search forward (true) or backward (false) in dates.
	 * @return The nearest allowed day, or null if none is found.
	 */
	public static LocalDate findNearestAllowedDay(AppointmentScheduleInfo appointmentScheduleInfo, LocalDate scheduleDate, boolean isForward, ZonedDateTime appointmentTime, ZonedDateTime appointmentscheduleTime, String timeZoneId) {
		logger.info("Get nearest day by moving forward and backward by given schedule day : {} and isforward flag : {}", scheduleDate, isForward);
	    LocalDate nearestAllowedDay = LocalDate.from(scheduleDate);
	    ZoneId zoneId = DateTimeUtils.getZoneIdFromTimeZoneId(timeZoneId);
	    // Get the current date and time in the specified time zone.
		ZonedDateTime currentTime = ZonedDateTime.now(zoneId);
	
	    long acceptableDelay = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty(APPOINTMENT_REMINDER_ACCEPTABLE_DELAY, 5); // in minutes
		
	    // Add a 5-minute acceptable delay to the temporary schedule time to ensure current time are considered within an acceptable time delay for scheduling reminder.
	    ZonedDateTime tempScheduleTime = appointmentscheduleTime.withZoneSameInstant(appointmentscheduleTime.getZone()).plusMinutes(acceptableDelay);
	    
	    while (!isScheduledDayWithinAllowedDays(appointmentScheduleInfo.getAllowedDays(), nearestAllowedDay)) {
	        nearestAllowedDay = isForward ? nearestAllowedDay.plusDays(1) : nearestAllowedDay.minusDays(1);
	        // If the search loops back to the appointment date, return null to avoid an infinite loop
	        if (isForward && appointmentscheduleTime.with(nearestAllowedDay).compareTo(appointmentTime) >= 0) {
	        	
	        	return null;
	        }
	        // If searching backwards brings us to a date equal current date, return null to avoid an invalid date
	        if (!isForward && tempScheduleTime.with(nearestAllowedDay).compareTo(currentTime) < 0) {
	            return null;
	        }
	    }
	    logger.info("Nearest day of schedule day is : {} for schedule time : {} and isforward flag : {}", nearestAllowedDay, scheduleDate, isForward);
	    return nearestAllowedDay;
	}
	
	/**
	 * Gets the valid appointment reminder schedule date.
	 * 
	 * @param reminderScheduleList
	 *            A list of ZonedDateTime objects representing reminder schedules.
	 * @return The first ZonedDateTime in the future, or null if none are in the future.
	 */
	public static ZonedDateTime getAppointmentReminderNextValidScheduleDate(List<ZonedDateTime> reminderScheduleList) {
		if (CollectionUtils.isNotEmpty(reminderScheduleList)) {
			// Get the current date and time in the specified time zone.
			ZonedDateTime currentTime = ZonedDateTime.now(ZoneId.of("UTC"));
			for (ZonedDateTime scheduleDate : reminderScheduleList) {
				// Compare the schedule date with the current time
				int result = scheduleDate.compareTo(currentTime);
				// If the schedule date is in the future, return it
				if (result > 0) {
					return scheduleDate;
				}
			}
		}
		// Return null if no valid schedule date was found
		return null;
	}
	
	public static boolean isValidAppointmentStatusForExecution(String appointmentStatus) {
		return StringUtils.equalsAnyIgnoreCase(appointmentStatus, AppointmentStatusEnum.BOOKED.getType(), AppointmentStatusEnum.CONFIRMED.getType());
	}

	/**
	 * Gets the campaign scheduling configuration based on review request and appointment information.
	 *
	 * @param reviewRequest     The review request entity.
	 * @param appointmentInfo   The appointment information.
	 * @return                  The campaign scheduling configuration string, or null if not applicable.
	 */
	public static String getSchedulingConfigurationForBeforeAppointmentTriggerRequest(BaseCommunicationEntity reviewRequest, AppointmentInfoLiteDTO appointmentInfo) {
		
		ReviewRequest request = (ReviewRequest) reviewRequest;
		
		ZonedDateTime zonedAppointmentStartTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(appointmentInfo.getStartTime()), ZoneId.of("UTC"));
		ZonedDateTime sentOn = request.getSentOn().toInstant().atZone(ZoneId.of("UTC"));
		
		// Calculate the absolute difference in days between appointment start time and sent on time
		long daysDifference = Math.abs(ChronoUnit.DAYS.between(zonedAppointmentStartTime, sentOn));
		
		StringBuilder scheduleConfig = new StringBuilder();
		
		// If the difference is 0, calculate the absolute difference in hours
		if (daysDifference == 0) {
			daysDifference = Math.abs(ChronoUnit.HOURS.between(zonedAppointmentStartTime, sentOn));
			return scheduleConfig.append(daysDifference).append(" hours before").toString();
		}
		
		// Check if the days difference is divisible by seven
		if (CoreUtils.isgivenValueDivisibleByDivisorValue(daysDifference, 7)) {
			return scheduleConfig.append(daysDifference / 7).append(" weeks before").toString();
		} else {
			return scheduleConfig.append(daysDifference).append(" days before").toString();
		}
		
	}
}
