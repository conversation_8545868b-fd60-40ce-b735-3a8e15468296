package com.birdeye.campaign.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.dto.AccessSettingMigrateDTO;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.dto.CampaignUserAccessDTO;
import com.birdeye.campaign.entity.CampaignUserMapping;
import com.birdeye.campaign.platform.constant.CampaignAccessSettingEnum;
import com.birdeye.campaign.request.AccessSettingsUpdateRequest;
import com.birdeye.campaign.request.CampaignUserAccessSaveRequest;
import com.birdeye.campaign.request.CampaignUsersListAccess;
import com.birdeye.campaign.request.CampaignUserAccessInfoDetails;
import com.birdeye.campaign.request.CampaignUserAccessRequest;

public class CampaignUserAccessUtils {
	
	private CampaignUserAccessUtils() {
		
	}
	
	/**
	 * 
	 * Fetch default access settings for a user
	 * 
	 */
	public static String fetchDefaultAccessSettingForAUser() {
		return CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("campaign.default.access.setting", CampaignAccessSettingEnum.NO_ACCESS.getAccess());
	}
	
	/**
	 * 
	 * Fetch default access settings for an enterprise user
	 * 
	 */
	public static String fetchAccessSettingForEnterpriseUser() {
		return CampaignAccessSettingEnum.EDIT.getAccess();
	}
	
	/**
	 * 
	 * Prepare UserCampaignAccessDTO list from CampaignUserMapping list
	 * 
	 * @param campaignUserMappingList
	 * 
	 */
	public static List<CampaignUserAccessDTO> prepareUserCampaignAccessListFromCampaignUserMapping(List<CampaignUserMapping> campaignUserMappingList) {
		if (CollectionUtils.isEmpty(campaignUserMappingList)) {
			return new ArrayList<>();
		}
		List<CampaignUserAccessDTO> userCampaignAccessDTOList = new ArrayList<>();
		campaignUserMappingList.stream().filter(e -> e != null).forEach(e -> userCampaignAccessDTOList.add(prepareUserCampaignAccessFromCampaignUserMapping(e)));
		return userCampaignAccessDTOList;
	}
	
	/**
	 * 
	 * Prepare UserCampaignAccessDTO object from CampaignUserMapping object
	 * 
	 * @param campaignUserMapping
	 * 
	 */
	public static CampaignUserAccessDTO prepareUserCampaignAccessFromCampaignUserMapping(CampaignUserMapping campaignUserMapping) {
		return (campaignUserMapping == null) ? null
				: new CampaignUserAccessDTO(campaignUserMapping.getCampaignId(), campaignUserMapping.getSplitCampaignId(), campaignUserMapping.getUserId(), campaignUserMapping.getAccess(),
						campaignUserMapping.getAccountId());
	}
	
	/**
	 * 
	 * In case of all the pages other than Campaign Access Setting Page, the access modifier of an owner is given as edit.
	 * This function validates and provides the same.
	 * 
	 * @param access
	 * 
	 */
	public static String fetchAccessModifierForCampaign(String access) {
		if (StringUtils.equalsIgnoreCase(access, CampaignAccessSettingEnum.OWNER.getAccess())) {
			return CampaignAccessSettingEnum.EDIT.getAccess();
		}
		return access;
	}
	
	/**
	 * 
	 * This function
	 * 1. Checks if for the given campaign id access settings are available in the map.
	 * 2. If yes return those else return default access settings.
	 * 
	 * @param campaignIdToCampaignAccess,
	 *            campaignId
	 * 
	 */
	public static List<String> fetchAccessSettingsForCampaignId(Map<Integer, CampaignUserAccessDTO> campaignIdToCampaignAccess, Integer campaignId) {
		List<String> accessList = new ArrayList<>();
		String defaultAccessSetting = fetchDefaultAccessSettingForAUser();
		if (campaignIdToCampaignAccess.containsKey(campaignId)) {
			accessList.add(fetchAccessModifierForCampaign(campaignIdToCampaignAccess.get(campaignId).getAccess()));
		} else {
			accessList.add(defaultAccessSetting);
		}
		return accessList;
	}
	
	/**
	 * 
	 * Checks if the returned DTO contains access settings for an enterprise user.
	 * 
	 * @param userCampaignAccess
	 * 
	 */
	public static Boolean isAccessSettingsForEnterpriseUser(CampaignUserAccessDTO userCampaignAccess) {
		return (userCampaignAccess != null && BooleanUtils.isTrue(userCampaignAccess.getIsEnterpriseUser()));
	}
	
	/**
	 * 
	 * Checks if the returned DTO list contains access settings for an enterprise user.
	 * 
	 * @param userCampaignAccess
	 * 
	 */
	public static Boolean isAccessSettingsListForEnterpriseUser(CachedCollectionWrapper<CampaignUserAccessDTO> userCampaignAccess) {
		return (userCampaignAccess != null && CollectionUtils.isNotEmpty(userCampaignAccess.getElementsList()) && isAccessSettingsForEnterpriseUser(userCampaignAccess.getElementsList().get(0)));
	}
	
	/**
	 * 
	 * Prepare Map for campaignId to Access Settings or SplitCampaignId to Access Settings
	 * 
	 * @param userCampaignAccess
	 * 
	 */
	public static Map<Integer, CampaignUserAccessDTO> prepareIdToCampaignAccessMap(CachedCollectionWrapper<CampaignUserAccessDTO> userCampaignAccessList, String campaignOrSplitCampaign) {
		if (userCampaignAccessList == null || CollectionUtils.isEmpty(userCampaignAccessList.getElementsList())
				|| BooleanUtils.isFalse(StringUtils.equalsAnyIgnoreCase(campaignOrSplitCampaign, Constants.USER_ACCESS_CAMPAIGN, Constants.USER_ACCESS_SPLIT_CAMPAIGN))) {
			return new HashMap<>();
		}
		if (StringUtils.equalsIgnoreCase(campaignOrSplitCampaign, Constants.USER_ACCESS_CAMPAIGN)) {
			return userCampaignAccessList.getElementsList().stream().filter(e -> e.getCampaignId() != null).collect(Collectors.toMap(e -> e.getCampaignId(), e -> e, (oldValue, newValue) -> oldValue));
		}
		return userCampaignAccessList.getElementsList().stream().filter(e -> e.getSplitCampaignId() != null)
				.collect(Collectors.toMap(e -> e.getSplitCampaignId(), e -> e, (oldValue, newValue) -> oldValue));
	}
	
	/**
	 * 
	 * Prepare CampaignUserMapping object from UserCampaignAccessDTO object
	 * 
	 * @param userAccessDTO
	 * 
	 */
	public static CampaignUserMapping prepareCampaignUserMappingFromUserAccessDTO(CampaignUserAccessDTO userAccessDTO) {
		return (userAccessDTO != null)
				? new CampaignUserMapping(userAccessDTO.getAccountId(), userAccessDTO.getCampaignId(), userAccessDTO.getSplitCampaignId(), userAccessDTO.getUserId(), userAccessDTO.getAccess(),
						userAccessDTO.getUserAddedBy())
				: null;
	}
	
	/**
	 * 
	 * Prepare CampaignUserMapping list from UserAccessInfoDetails list
	 * 
	 * @param userAccessList,
	 *            campaignId, splitCampaignId, accountId, userAddedBy
	 * 			
	 */
	public static List<CampaignUserMapping> convertUserAccessInfoToCampaignUserMapping(List<CampaignUserAccessInfoDetails> userAccessList, Integer campaignId, Integer splitCampaignId, Integer accountId,
			Integer userAddedBy) {
		if(CollectionUtils.isEmpty(userAccessList)) {
			return new ArrayList<>();
		}
		return userAccessList.stream().map(userAccess -> new CampaignUserMapping(accountId, campaignId, splitCampaignId, userAccess.getUserId(), userAccess.getPermission(), userAddedBy))
				.collect(Collectors.toList());
	}
	
	/**
	 * 
	 * This functions returns true if user has view permissions
	 * Called in case of ViewRecipients(Split And Normal Automation).
	 * Note : Here isAccessSettingApplicable Flag is not checked because, in case of view recipients if access settings applicable, user permissions list
	 * will never be empty. If list is empty then it means settings were not applicable.
	 * 
	 * @param userPermissions
	 * 
	 */
	public static Boolean isUserHasViewPermission(List<String> userPermissions) {
		return (CollectionUtils.isEmpty(userPermissions) || BooleanUtils.isFalse(StringUtils.equalsIgnoreCase(userPermissions.get(0), CampaignAccessSettingEnum.NO_ACCESS.getAccess())));
	}
	
	/**
	 * 
	 * This functions returns true if user has view permissions based on access settings and access setting flag applicable 
	 * 
	 * @param userPermissions, isAccessSettingApplicable
	 * 
	 */
	public static Boolean isUserHasViewPermission(List<String> userPermissions, Boolean isAccessSettingApplicable) {
		return ((CollectionUtils.isEmpty(userPermissions) && BooleanUtils.isFalse(isAccessSettingApplicable))
				|| (CollectionUtils.isNotEmpty(userPermissions) && BooleanUtils.isFalse(StringUtils.equalsIgnoreCase(userPermissions.get(0), CampaignAccessSettingEnum.NO_ACCESS.getAccess()))));
	}
	
	/**
	 * 
	 * This functions returns true if user has edit permissions based on access settings and access setting flag applicable
	 * 
	 * @param userPermissions, isAccessSettingApplicable
	 * 
	 */
	public static Boolean isUserHasEditPermission(List<String> userPermissions, Boolean isAccessSettingApplicable) {
		return ((CollectionUtils.isEmpty(userPermissions) && BooleanUtils.isFalse(isAccessSettingApplicable)) || (CollectionUtils.isNotEmpty(userPermissions)
				&& StringUtils.equalsAnyIgnoreCase(userPermissions.get(0), CampaignAccessSettingEnum.EDIT.getAccess(), CampaignAccessSettingEnum.OWNER.getAccess())));
	}
	
	/**
	 * 
	 * Checks if campaign count flag should be enabled or disabled
	 * 
	 * @param campaignId,
	 *            splitCampaignId
	 * 
	 */
	public static Boolean hasCampaignCountFlagEnabled(Integer campaignId, Integer splitCampaignId) {
		return ((campaignId != null && campaignId > 0) || (splitCampaignId != null && splitCampaignId > 0));
	}
	
	// Request Validation Functions
	
	/**
	 * 
	 * Checks if the request to fetch access settings is invalid
	 * 
	 * @param CampaignUserAccessRequest
	 * 
	 */
	public static Boolean isInvalidFetchAccessSettingRequest(CampaignUsersListAccess request) {
		return (request == null || request.getUserId() == null || (request.getCampaignId() == null && request.getSplitCampaignId() == null));
	}
	
	public static Boolean isInvalidFetchAccessSettingRequest(CampaignUserAccessRequest request) {
		return (request == null || request.getUserId() == null || (request.getCampaignId() == null && request.getSplitCampaignId() == null));
	}
	
	
	/**
	 * 
	 * Checks if the request to fetch user's all campaign access is invalid
	 * 
	 * @param CampaignUserAccessRequest
	 * 
	 */
	public static Boolean isInvalidFetchUserCampaignsAccessRequest(CampaignUserAccessRequest request) {
		return (request == null || request.getUserId() == null
				|| BooleanUtils.isFalse(StringUtils.equalsAnyIgnoreCase(request.getCampaignCategory(), Constants.USER_ACCESS_CAMPAIGN, Constants.USER_ACCESS_SPLIT_CAMPAIGN)));
	}
	
	/**
	 * 
	 * Checks if the request to update owner is invalid
	 * 
	 * @param CampaignUserAccessRequest
	 * 
	 */
	public static Boolean isInvalidUpdateOwnerRequest(CampaignUserAccessRequest request) {
		return (request == null || request.getOwnerId() == null || (request.getCampaignId() == null && request.getSplitCampaignId() == null) || request.getAccountId() == null
				|| BooleanUtils.isFalse(request.getIsAutomationCreationRequest()));
	}
	
	/**
	 * 
	 * Checks if the request to delete campaign access settings is invalid
	 * 
	 * @param CampaignUserAccessRequest
	 * 
	 */
	public static Boolean isInvalidDeleteCampaignAccessSettingRequest(CampaignUserAccessRequest request) {
		return (request == null || (request.getCampaignId() == null && request.getSplitCampaignId() == null));
	}
	
	/**
	 * 
	 * Checks if the request to fetch owner and its permissible locations is invalid
	 * 
	 * @param CampaignUserAccessRequest
	 * 
	 */
	public static Boolean isInvalidFetchOwnerAndPermissibleLocationsRequest(CampaignUserAccessRequest request) {
		return (request == null || (request.getCampaignId() == null && request.getSplitCampaignId() == null) || request.getAccountId() == null);
	}
	
	/**
	 * 
	 * Checks if the request to fetch variants in case of split automation is valid or not
	 * 
	 * @param splitCampaignId, accountId 
	 * 
	 */
	public static Boolean isInvalidVariantFetchRequest(Integer splitCampaignId, Integer accountId) {
		return (splitCampaignId == null || accountId == null);
	}
	
	// Migration Flow
	
	/**
	 * 
	 * Checks if the request to migrate access settings for existing campaigns for given account is invalid 
	 * 
	 * @param AccessSettingsUpdateRequest
	 * 
	 */
	public static Boolean isInvalidAcessSettingMigrationRequest(AccessSettingsUpdateRequest request) {
		return (request == null || request.getAccountId() == null);
	}
	
	/**
	 * 
	 * Filter out entries from list where owner id is null or whose access settings are already present.
	 * 
	 * @param inputList,
	 *            existingCampaignUserMappingMap, campaignCategory
	 * 
	 */
	public static List<AccessSettingMigrateDTO> filterCampaignAccessDataList(List<AccessSettingMigrateDTO> inputList, Map<Integer, CampaignUserMapping> existingCampaignUserMappingMap) {
		if (CollectionUtils.isEmpty(inputList)) {
			return new ArrayList<>();
		}
		return inputList.stream().filter(e -> e.getOwnerId() != null).filter(e -> BooleanUtils.isFalse(existingCampaignUserMappingMap.containsKey(e.getId()))).collect(Collectors.toList());
	}
	
	/**
	 * 
	 * Prepare Map for campaignId to Campaign User Mapping Object or SplitCampaignId to Campaign User Mapping Object
	 * 
	 * @param existingCampaignUserMappingList,
	 *            category
	 * 
	 */
	public static Map<Integer, CampaignUserMapping> prepareCampaignIdToCampaignUserMappingMap(List<CampaignUserMapping> existingCampaignUserMappingList, String category) {
		if (CollectionUtils.isEmpty(existingCampaignUserMappingList)
				|| BooleanUtils.isFalse(StringUtils.equalsAnyIgnoreCase(category, Constants.USER_ACCESS_CAMPAIGN, Constants.USER_ACCESS_SPLIT_CAMPAIGN))) {
			return new HashMap<>();
		}
		
		if (StringUtils.equalsIgnoreCase(category, Constants.USER_ACCESS_CAMPAIGN)) {
			return existingCampaignUserMappingList.stream().filter(e -> e.getCampaignId() != null).collect(Collectors.toMap(e -> e.getCampaignId(), e -> e, (oldValue, newValue) -> oldValue));
		}
		return existingCampaignUserMappingList.stream().filter(e -> e.getSplitCampaignId() != null).collect(Collectors.toMap(e -> e.getSplitCampaignId(), e -> e, (oldValue, newValue) -> oldValue));
	}
	
	public static boolean isInvalidRequestToSaveCampaignUsers(Integer accountId, CampaignUserAccessSaveRequest usersRequest) {
		if (accountId == null || usersRequest == null || CollectionUtils.isEmpty(usersRequest.getUserAccessList())
				|| (usersRequest.getCampaignId() == null && usersRequest.getSplitCampaignId() == null)) {
			return true;
		}
		return false;
	}
	
	
}
