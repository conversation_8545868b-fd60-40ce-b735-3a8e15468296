package com.birdeye.campaign.utils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.GlobalTemplatesCache;
import com.birdeye.campaign.cache.SystemPropertiesCache;
import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.dto.AccountGlobalTemplateMappingDTO;
import com.birdeye.campaign.dto.BusinessTemplateEntity;
import com.birdeye.campaign.dto.CachedCollectionWrapper;
import com.birdeye.campaign.dto.GlobalTemplateValidationDTO;
import com.birdeye.campaign.dto.GlobalTemplatesDTO;
import com.birdeye.campaign.entity.AccountGlobalTemplateMapping;
import com.birdeye.campaign.entity.BusinessSmsTemplate;
import com.birdeye.campaign.platform.constant.TemplateListingTypeEnum;
import com.birdeye.template.sms.dto.SmsTemplateMessage;

public class GlobalTemplatesUtils {
	private static final Logger logger = LoggerFactory.getLogger(GlobalTemplatesUtils.class);
	
	private static final Integer	ZERO	= 0;
	
	private static final String		GENERAL	= "General";
	
	
	/**
	 * Validate if the flag input is true or not
	 *
	 * 
	 * @param globalTemplateFlag
	 * 
	 */
	public static boolean isGlobalSmsTemplate(Boolean globalTemplateFlag) {
		return (globalTemplateFlag != null && globalTemplateFlag == true);
	}
	
	/**
	 * Validate if the flag input is false or not
	 *
	 * 
	 * @param globalTemplateFlag
	 * 
	 */
	public static Boolean isNotGlobalSmsTemplate(Boolean globalTemplateFlag) {
		return BooleanUtils.isFalse(isGlobalSmsTemplate(globalTemplateFlag));
	}
	
	/**
	 * Get long property from system properties cache, if found else return default value
	 *
	 * 
	 * @param name, defaultValue
	 * 
	 */
	private static Long getLongPropertyFromCache(String name, Long defaultValue) {
		return CacheManager.getInstance().getCache(SystemPropertiesCache.class).getLongProperty(name, defaultValue);
	}
	
	/**
	 * Get string property from system properties cache, if found else return default value
	 *
	 * 
	 * @param name,
	 *            defaultValue
	 * 
	 */
	public static String getStringPropertyFromCache(String name, String defaultValue) {
		return CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(name, defaultValue);
	}
	
	/**
	 * Validate if business is eligible for showing global templates
	 * This function validates to true if the business is created after the allowed time
	 * Allowed time is configurable, and is fetched from db
	 *
	 * 
	 * @param businessCreatedTime
	 * 
	 */
	public static Boolean isValidBusinessBasedOnTime(Long businessCreatedTime) {
		Long allowedTime = getLongPropertyFromCache("global.template.epoch.time.allowed", 0l);
		return (allowedTime < businessCreatedTime) ? true : false;
	}
	
	/**
	 * Prepares BusinessSmsTemplate From smsTemplateMessage
	 *
	 * 
	 * @param enterpriseId,templateType,smsTemplateMessage,userId,resellerId,isDeleted
	 * 
	 */
	public static BusinessSmsTemplate prepareBusinessSmsTemplateForGlobalTemplate(Integer enterpriseId, String templateType, SmsTemplateMessage smsTemplateMessage, Integer userId, Integer resellerId,
			Boolean isDeleted) {
		BusinessSmsTemplate businessSmsTemplate = null;
		businessSmsTemplate = smsTemplateMessage.getBusinessSmsTemplate(userId);
		businessSmsTemplate.setEnterpriseId(enterpriseId);
		businessSmsTemplate.setIsDeleted((BooleanUtils.isFalse(isDeleted) ? Constants.TEMPLATE_IS_DELETED_DEFAULT : Constants.TEMPLATE_IS_DELETED));
		businessSmsTemplate.setType(TemplateValidationUtils.validateTemplateType(templateType));
		businessSmsTemplate.setEnableSentimentCheck(smsTemplateMessage.getIsSentimentCheckEnabled());
		businessSmsTemplate.setCreatedBy(userId);
		businessSmsTemplate.setLocationLevelTemplate(smsTemplateMessage.getLocationLevelTemplate());
		return businessSmsTemplate;
	}
	
	/**
	 * Prepares AccountGlobalTemplateMapping Object
	 *
	 * 
	 * @param enterpriseId,globalTemplateId,smsTemplateId,emailTemplateId
	 * 
	 */
	public static AccountGlobalTemplateMapping prepareAccountGlobalTemplateMappingData(Integer enterpriseId, Integer globalTemplateId, Integer smsTemplateId, Integer emailTemplateId) {
		AccountGlobalTemplateMapping accountGlobalTemplateMapping = new AccountGlobalTemplateMapping(enterpriseId, globalTemplateId, smsTemplateId, emailTemplateId);
		return accountGlobalTemplateMapping;
	}
	
	/**
	 * Prepares HashMap From List
	 *
	 * 
	 * @param mappingList
	 * 
	 */
	public static Map<Integer, AccountGlobalTemplateMappingDTO> prepareHashMapFromList(List<AccountGlobalTemplateMappingDTO> mappingList) {
		return mappingList.stream().collect(Collectors.toMap(obj -> obj.getGlobalTemplateId(), obj -> obj, (oldValue, newValue) -> newValue));
	}
	
	/**
	 * Prepares BusinessTemplateEntity from GlobalTemplate Object based on listing type and source
	 *
	 * 
	 * @param globalTemplate,listingEnum,source
	 * 
	 */
	public static BusinessTemplateEntity prepareBusinessTemplateEntityFromGlobalTemplates(GlobalTemplatesDTO globalTemplate, TemplateListingTypeEnum listingEnum, String source) {
		// Here custom Templates are in scope for Global Templates 1.0 only, this function can be extended for diff template types and listing enum further
		BusinessTemplateEntity businessTemplateEntity = new BusinessTemplateEntity(globalTemplate.getId(), globalTemplate.getTemplateType(), globalTemplate.getTemplateName(),
				globalTemplate.getUpdatedAt(), globalTemplate.getCreatedAt(), globalTemplate.getMessage(), String.valueOf(0), true);
		return businessTemplateEntity;
	}
	
	/**
	 * Prepares BusinessTemplateEntity List
	 *
	 * 
	 * @param globalTemplatesList,listingEnum,source
	 * 
	 */
	public static CachedCollectionWrapper<BusinessTemplateEntity> prepareBusinessTemplateEntityList(List<GlobalTemplatesDTO> globalTemplatesList, TemplateListingTypeEnum listingEnum, String source) {
		List<BusinessTemplateEntity> businessTemplateList = globalTemplatesList.stream().map(obj -> prepareBusinessTemplateEntityFromGlobalTemplates(obj, listingEnum, source)).collect(Collectors.toList());
		CachedCollectionWrapper<BusinessTemplateEntity> wrapper = new CachedCollectionWrapper<BusinessTemplateEntity>();
		wrapper.setElementsList(businessTemplateList);
		return wrapper;
	}
	
	/**
	 * Checks if a local copy of Global Template is already created or not
	 *
	 * 
	 * @param globalTemplateValidateDTO,originalTemplateId
	 * 
	 */
	public static Integer isGlobalTemplateAlreadyCreated(GlobalTemplateValidationDTO globalTemplateValidateDTO, Integer originalTemplateId) {
		return (globalTemplateValidateDTO.getTemplateId() != null && BooleanUtils.isFalse(globalTemplateValidateDTO.getIsGlobalTemplate())) ? globalTemplateValidateDTO.getTemplateId()
				: originalTemplateId;
	}
	
	/**
	 * Validate if the template id given belongs to a global template or not
	 *
	 * 
	 * @param globalTemplateId
	 * 
	 */
	public static boolean validateGlobalTemplate(Integer globalTemplateId) {
		// Step 1 : Lookup global_template table to check for template based on template id.
		GlobalTemplatesDTO globalTemplates = CacheManager.getInstance().getCache(GlobalTemplatesCache.class).getById(globalTemplateId);
		// Step 2 : If no record found then return false else true.
		return globalTemplates == null ? false : true;
	}
	
	/**
	 * Get global template by industry from in memory cache
	 *
	 * 
	 * @param industry
	 * 
	 */
	public static List<GlobalTemplatesDTO> getGlobalTemplateByIndustry(String industry) {	
		return CacheManager.getInstance().getCache(GlobalTemplatesCache.class).getByIndustry(industry);
	}
	
	/**
	 * Get global template list by industry and general industry.
	 *
	 * 
	 * @param industry
	 * 
	 */
	public static List<GlobalTemplatesDTO> getGlobalTemplateList(String industry) {
		// Fetch global template list by industry
		List<GlobalTemplatesDTO> globalTemplateListIndustry = null;
		if (StringUtils.isNotBlank(industry)) {
			globalTemplateListIndustry = getGlobalTemplateByIndustry(StringUtils.lowerCase(industry));
		}
		
		// Fetch global template list for general industry, to be shown in for the businesses, for which no industry is associated with them or nothing found
		// in global templates list
		if (CollectionUtils.isEmpty(globalTemplateListIndustry)) {
			List<GlobalTemplatesDTO> generalGlobalTemplatesList = getGlobalTemplateByIndustry(StringUtils.lowerCase(getStringPropertyFromCache("global.templates.mandatory.industry", GENERAL)));
			return generalGlobalTemplatesList;
		}
		
		return globalTemplateListIndustry;
	}
	
	/**
	 * Get global template by id from in memory cache
	 *
	 * 
	 * @param templateId
	 * 
	 */
	public static GlobalTemplatesDTO getGlobalTemplateById(Integer templateId) {
		return CacheManager.getInstance().getCache(GlobalTemplatesCache.class).getById(templateId);
	}
	
	/**
	 * 
	 * Get allowed global template types from system properties cache
	 * 
	 */
	public static List<String> getAllowedTemplateTypes() {
		return CacheManager.getInstance().getCache(SystemPropertiesCache.class).getCommaSeparatedPropertiesList("global.templates.supported.types", "promotion");
	}
	
//	private static boolean isElementInList(String ele, List<String> targetList) {
//		if (CollectionUtils.isEmpty(targetList)) {
//			return false;
//		}
//		return targetList.stream().anyMatch(target -> StringUtils.equalsIgnoreCase(target, ele));
//	}
	
	/**
	 * Find common elements between two lists
	 *
	 * 
	 * @param listOne,listTwo
	 * 
	 */
	private static List<String> findIntersectionOfTwoLists(List<String> listOne, List<String> listTwo) {
		if (CollectionUtils.isEmpty(listOne) || CollectionUtils.isEmpty(listTwo)) {
			return null;
		}
		return listTwo.stream().filter(target -> listOne.contains(target)).collect(Collectors.toList());
	}
	
	/**
	 * Filter global templates by type
	 *
	 * 
	 * @param globalTemplateList,templateTypes
	 * 
	 */
	public static List<GlobalTemplatesDTO> filterGlobalTemplatesByType(List<GlobalTemplatesDTO> globalTemplateList, List<String> templateTypes) {
		if (CollectionUtils.isEmpty(globalTemplateList)) {
			return null;
		}
		// Currently we have only promotion template type
		return globalTemplateList;
		// return globalTemplateList.stream().filter(gt -> isElementInList(gt.getTemplateType(), templateTypes))
		// .collect(Collectors.toList());
	}
	
	/**
	 * Filter global templates by 
	 * 1. If a template id is present in account global template mapping table, remove from list of global templates
	 * 2. Filter global templates list by source
	 *
	 * 
	 * @param globalTemplatesList,accountGlobalTemplateMappingList,source
	 * 
	 */
	public static List<GlobalTemplatesDTO> filterGlobalTemplates(List<GlobalTemplatesDTO> globalTemplatesList, List<AccountGlobalTemplateMappingDTO> accountGlobalTemplateMappingList, String source) {
		if (CollectionUtils.isEmpty(accountGlobalTemplateMappingList)) {
			return globalTemplatesList;
		}
		
		Map<Integer, AccountGlobalTemplateMappingDTO> templateIdToEntryMap = prepareHashMapFromList(accountGlobalTemplateMappingList);
		
		return globalTemplatesList.stream().filter(gt -> {
			return (BooleanUtils.isFalse(templateIdToEntryMap.containsKey(gt.getId()))) && (StringUtils.equalsIgnoreCase(gt.getSource(), source));
		}).collect(Collectors.toList());
	}
	
	/**
	 * This function does the following : 
	 * 1. If global templates list is empty return smsTemplateMessagesList
	 * 2. If smsTemplateMessages List is empty, then return globalTemplatesList
	 * 3. If both are non-empty, merge and return
	 *
	 * 
	 * @param globalTemplatesList,accountGlobalTemplateMappingList,source
	 * 
	 */
	public static List<BusinessTemplateEntity> addGlobalTemplatesToExistingList(CachedCollectionWrapper<BusinessTemplateEntity> globalTemplates, List<BusinessTemplateEntity> smsTemplatesMessages) {
		if (globalTemplates == null || CollectionUtils.isEmpty(globalTemplates.getElementsList())) {
			return smsTemplatesMessages;
		}
		if (CollectionUtils.isEmpty(smsTemplatesMessages)) {
			return globalTemplates.getElementsList();
		}
		smsTemplatesMessages.addAll(globalTemplates.getElementsList());
		return smsTemplatesMessages;
	}
	
	/**
	 * Validate if cached object is empty or not
	 *
	 * 
	 * @param globalTemplates
	 * 
	 */
	public static Boolean validateCachedObjectNotEmpty(CachedCollectionWrapper<BusinessTemplateEntity> globalTemplates) {
		return (globalTemplates != null && CollectionUtils.isNotEmpty(globalTemplates.getElementsList()));
	}
	
	/**
	 * Based on listing enum and source, sanitize template type list
	 *
	 * 
	 * @param templateType
	 * 
	 */
	public static List<String> sanitizeTemplateTypes(List<String> templateType, TemplateListingTypeEnum listingEnum, String source) {
		// More functionality will be added based upon source and listing enum in the future
		List<String> allowedTemplateTypes = getAllowedTemplateTypes();
		if (CollectionUtils.isEmpty(templateType)) {
			if(listingEnum == TemplateListingTypeEnum.TEMPLATES_TAB_FILTERS) {
				return null;
			}
			return allowedTemplateTypes;
		}
		return findIntersectionOfTwoLists(templateType, allowedTemplateTypes);
	}
	
	/**
	 * Prepare businessSmsTemplate object from globalTemplate Object
	 *
	 * 
	 * @param globalTemplate
	 * 
	 */
	public static BusinessSmsTemplate prepareBusinessSmsTemplate(GlobalTemplatesDTO globalTemplate) {
		BusinessSmsTemplate businessSmsTemplate = new BusinessSmsTemplate();
		businessSmsTemplate.setId(globalTemplate.getId());
		businessSmsTemplate.setType(globalTemplate.getTemplateType());
		businessSmsTemplate.setName(globalTemplate.getTemplateName());
		businessSmsTemplate.setMessageBody(globalTemplate.getMessage());
		businessSmsTemplate.setMmsEnabled(ZERO);
		businessSmsTemplate.setLocationLevelTemplate(ZERO);
		return businessSmsTemplate;
	}
	
	/**
	 * Prepare sms template message from global template
	 *
	 * 
	 * @param templateId
	 * 
	 */
	public static SmsTemplateMessage prepareSmsTemplateMessage(Integer templateId) {
		GlobalTemplatesDTO globalTemplateObject = getGlobalTemplateById(templateId);
		if (globalTemplateObject == null) {
			return null;
		}
		BusinessSmsTemplate businessSmsTemplate = prepareBusinessSmsTemplate(globalTemplateObject);
		return new SmsTemplateMessage(businessSmsTemplate, businessSmsTemplate.getNpsLabels(), businessSmsTemplate.getStarLabels());
	}
	
	/**
	 * Convert string value to integer
	 *
	 * 
	 * @param val
	 * 
	 */
	public static Integer convertStringToInteger(String val) {
		if (StringUtils.isBlank(val)) {
			return null;
		}
		Integer result = null;
		try {
			result = Integer.parseInt(val);
			return result;
		} catch (Exception e) {
			logger.error("convertStringToInteger :: Exception occurred while converting string value {} to integer {}", val, e);
		}
		return null;
	}
	
//	public static void updateBusinessSmsTemplateName(BusinessSmsTemplate businessSmsTemplate, Integer sameNameCount) {
//		String name = businessSmsTemplate.getName();
//		if (0 != sameNameCount) {
//			name = name + "(" + sameNameCount + ")";
//		}
//		businessSmsTemplate.setName(name);
//	}
	
}
