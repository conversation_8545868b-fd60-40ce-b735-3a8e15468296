package com.birdeye.campaign.utils;

import java.lang.reflect.Field;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.routines.EmailValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.birdeye.campaign.cache.CacheManager;
import com.birdeye.campaign.cache.ReferralSourceCache;
import com.birdeye.campaign.dto.BusinessEnterpriseEntity;
import com.birdeye.campaign.entity.ReferralSource;
import com.birdeye.campaign.enums.BusinessAccountTypeEnum;
import com.birdeye.campaign.enums.BusinessActivationStatusEnum;
import com.birdeye.campaign.enums.BusinessTypeEnum;
import com.birdeye.campaign.platform.entity.Location;
import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonemetadata;
import com.google.i18n.phonenumbers.Phonenumber;
import com.google.i18n.phonenumbers.Phonenumber.PhoneNumber;

/**
 ** File: CoreUtils.java Created: 3 Jan 2019 Author: puneetgupta
 **
 ** This code is copyright (c) BirdEye Software India Pvt. Ltd.
 **/
public class CoreUtils {
	
	private static final String					AMERICA_LOS_ANGELES	= "America/Los_Angeles";
	private static final Logger					logger				= LoggerFactory.getLogger(CoreUtils.class);
	private static final Map<String, String>	idByLowerName		= new HashMap<>();
	private static final Map<String, String>	idByLowerId			= new HashMap<>();
	
	private static final DateTimeFormatter		DATE_TIME_FORMATTER	= DateTimeFormatter.ofPattern("uuuu-MM-dd HH:mm:ss");
	
	private static final String EMAIL_REGEX = "(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|\"(?:[\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x21\\x23-\\x5b\\x5d-\\x7f]|\\\\[\\x01-\\x09\\x0b\\x0c\\x0e-\\x7f])*\")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x21-\\x5a\\x53-\\x7f]|\\\\[\\x01-\\x09\\x0b\\x0c\\x0e-\\x7f])+)\\])";
	
	private static final PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance();
	
	static {
		for (String tzID : TimeZone.getAvailableIDs()) {
			TimeZone tz = TimeZone.getTimeZone(tzID);
			idByLowerName.put(tz.getDisplayName().toLowerCase(), tzID);
			idByLowerId.put(tzID.toLowerCase(), tzID);
		}
	}
	
	private CoreUtils() {
		
	}
	public static String getRandom(final int length) {
		
		final String randomString = getRandom();
		return randomString.substring(randomString.length() - length);
	}
	
	public static String getRandom() {
		return UUID.randomUUID().toString();
	}
	
	public static String getCountryCodeForLocation(Location location) {
		String countryCode = null;
		if (location != null && location.getCountryCode() != null) {
			countryCode = location.getCountryCode();
		}
		return countryCode;
	}
	
	public static String formatPhoneNumber(String phoneNumber, String countryCode) {
		return formatPhoneNumber(phoneNumber, countryCode, null);
	}
	
	public static String formatPhoneNumberV2(String phoneNumber, String countryCode) {
		return formatPhoneNumberV2(phoneNumber, countryCode, null);
	}
	
	public static String formatPhoneNumber(String phoneNumber, String countryCode, PhoneNumberCustomFormat customFormat) {
		countryCode = getIsoCountryCode(countryCode);
		if (StringUtils.isBlank(phoneNumber)) {
			return phoneNumber;
		}
		if (!PhoneNoValidator.isValidPhoneNumber(phoneNumber, countryCode)) {
			if ("MX".equalsIgnoreCase(countryCode)) {
				countryCode = "US";
			}
			if (!PhoneNoValidator.isValidPhoneNumber(phoneNumber, countryCode)) {
//				logger.warn("Not a valid phone number : {}", phoneNumber);
				return phoneNumber;
			}
		} else {
//			logger.warn("Not a valid phone number : {}", phoneNumber);
			return phoneNumber;
		}
		return formatNumber(phoneNumber, countryCode, customFormat);
	}
	
	public static String formatPhoneNumberV2(String phoneNumber, String countryCode, PhoneNumberCustomFormat customFormat) {
		countryCode = getIsoCountryCode(countryCode);
		if (StringUtils.isBlank(phoneNumber)) {
			return phoneNumber;
		}
		if (!PhoneNoValidator.isValidPhoneNumber(phoneNumber, countryCode)) {
			if ("MX".equalsIgnoreCase(countryCode)) {
				countryCode = "US";
			}
			if (!PhoneNoValidator.isValidPhoneNumber(phoneNumber, countryCode)) {
//				logger.warn("Not a valid phone number : {}", MaskingUtils.maskAndEncodePhone(phoneNumber));
				return phoneNumber;
			}
		}
		return formatNumber(phoneNumber, countryCode, customFormat);
	}
	
	private static String formatNumber(String number, String code, PhoneNumberCustomFormat customFormat) {
		code = getIsoCountryCode(code);
		PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance();
		try {
			Phonenumber.PhoneNumber phoneNumber = phoneUtil.parse(number, code);
			if (null == customFormat) {
				number = phoneUtil.formatInOriginalFormat(phoneNumber, code);
			} else {
				Phonemetadata.NumberFormat newNumFormat = new Phonemetadata.NumberFormat();
				newNumFormat.setPattern(customFormat.getPattern());
				newNumFormat.setFormat(customFormat.getFormat());
				List<Phonemetadata.NumberFormat> newNumberFormats = new ArrayList<>();
				newNumberFormats.add(newNumFormat);
				StringBuilder phoneNum = new StringBuilder();
				if (customFormat.isCountryCodeRequired()) {
					if (StringUtils.isNotBlank(customFormat.getCountryCodePrefix())) {
						phoneNum.append(customFormat.getCountryCodePrefix());
					}
					phoneNum.append(phoneNumber.getCountryCode());
				}
				phoneNum.append(phoneUtil.formatByPattern(phoneNumber, PhoneNumberUtil.PhoneNumberFormat.E164, newNumberFormats));
				number = phoneNum.toString();
			}
		} catch (NumberParseException e) {
			logger.error("NumberParseException was thrown: {}", e.getMessage());
		}
		return number;
	}
	
	public static String formatPhoneFaxNumber(String phoneNumber) {
		return formatPhoneNumber(phoneNumber, null);
	}
	
	public static String formatPhoneFaxNumber(String phoneNumber, Location location) {
		return formatPhoneFaxNumber(phoneNumber, location, null);
	}
	
	public static String formatPhoneFaxNumber(String phoneNumber, Location location, PhoneNumberCustomFormat customFormat) {
		String countryCode = null;
		if (location != null && StringUtils.isNotBlank(location.getCountryCode())) {
			countryCode = location.getCountryCode();
		}
		return formatPhoneNumber(phoneNumber, countryCode, customFormat);
	}
	
	public static String getIsoCountryCode(String countryCode) {
		if (StringUtils.isBlank(countryCode)) {
			countryCode = "US";
		} else {
			countryCode = countryCode.toUpperCase();
		}
		switch (countryCode) {
			case "UK":
			case "GB":
				countryCode = "GB";
				break;
			case "CA":
				countryCode = "CA";
				break;
			case "AU":
				countryCode = "AU";
				break;
			case "NZ":
				countryCode = "NZ";
				break;
			case "NL":
				countryCode = "NL";
				break;
			case "DE":
				countryCode = "DE";
				break;
			case "MX":
				countryCode = "MX";
				break;
			case "PR":
				countryCode = "PR";
				break;
			case "US":
			default:
				countryCode = "US";
				break;
		}
		return countryCode;
	}
	
	public static List<Integer> parseDelimitedIntegers(String str, String delimiter) {
		if (StringUtils.isBlank(str)) {
			return Collections.emptyList();
		}
		List<Integer> intList = new ArrayList<>();
		String[] strArr = str.split(delimiter);
		for (String val : strArr) {
			intList.add(Integer.parseInt(val));
		}
		return intList;
	}
	
	public static List<String> parseDelimitedStrings(String str, String delimiter) {
		if (StringUtils.isBlank(str)) {
			return Collections.emptyList();
		}
		return Arrays.asList(str.split(delimiter));
	}
	
	public static List<String> parseDelimitedStringsWithDefaultAsEmptyList(String str, String delimiter) {
		if (StringUtils.isBlank(str)) {
			return Collections.emptyList();
		}
		return Arrays.asList(StringUtils.split(str, delimiter));
	}
	
	public static String getCommaSeparatedStringFromIntegerList(List<Integer> input) {
		String result = null;
		if (CollectionUtils.isNotEmpty(input)) {
			result = input.stream().map(st -> st.toString()).collect(Collectors.joining(","));// NOSONAR
		}
		return result;
	}
	
	public static List<Integer> getIntegerListFromCommaSeparatedString(String input) {
		List<Integer> result = null;
		if (StringUtils.isNotEmpty(input)) {
			result = Stream.of(input.split(",")).map(Integer::parseInt).collect(Collectors.toList());
		}
		return result;
	}
	
	/**
	 * wrapper over applySmsTimeFrameWindowToDate to allow default behavior for current date
	 */
	public static int applySmsTimeFrameWindow(String timeZone) {
		return applySmsTimeFrameWindowToDate(timeZone, new Date());
	}
	
	/**
	 * method that returns number of hours to be added to current time so that sms can be sent within business hours (8am-8pm) or next day 8am
	 *
	 * @param date
	 *            : date to apply the time-frame to
	 * @return number of hours that need to be added to current time
	 */
	public static int applySmsTimeFrameWindowToDate(String timeZone, Date date) {
		int incomingHours = 0; // delay to be added to the date
		int totalHoursInDay = 24; // 24 incomingHours in a day
		int startHour = 8; // 8am
		int endHour = 20; // 8pm
		
		// BIRDEYE-17004 : delay in daily digest email due to anomaly in timezone Id
		// returned for timezone string "Central Standard Time"
		String replacedString = timeZone;
		if (("Central Standard Time").equalsIgnoreCase(timeZone) || ("Central Daylight Time").equalsIgnoreCase(timeZone))
			replacedString = "CST";
		
		Calendar cal = convertToBusinessTimeZoneWithDaylight(date, TimeZone.getTimeZone(getTimeZoneID(replacedString)));
		
		int currentHour = cal.get(Calendar.HOUR_OF_DAY);
		logger.info("applySmsTimeFrameWindow: incomingHours={} startHour={} endHour={} currentHour={}", incomingHours, startHour, endHour, currentHour);
		
		// if within startHour(am) to endHour(pm) then no change
		if (currentHour + incomingHours < endHour && currentHour + incomingHours >= startHour) {
			return incomingHours;
		}
		
		// if beyond endHour, then add hours to make it next start startHour
		if (currentHour + incomingHours >= endHour) {
			return totalHoursInDay + startHour - currentHour;
		}
		
		// if within startHour, then no need to add, just reset to startHour
		if (currentHour + incomingHours < startHour) {
			return startHour - currentHour;
		}
		return incomingHours;
	}
	
	public static String getTimeZoneID(String input) {
		String tzID = null;
		if (StringUtils.isNotBlank(input)) {
			if (input.contains("Daylight")) {
				input = StringUtils.replace(input, "Daylight", "Standard");
			}
			
			tzID = idByLowerName.get(input.toLowerCase());
			// Could be a case that instead of display name we get ID
			// e.g. America/New_York instead of Eastern Standard Time
			if (StringUtils.isBlank(tzID)) {
				tzID = idByLowerId.get(input.toLowerCase());
			}
		}
		if (StringUtils.isBlank(tzID)) {
			tzID = "PST";
		}
		return tzID;
	}
	
	public static Calendar convertToUTCTimeZone(Calendar calendar) {
		TimeZone fromTimeZone = calendar.getTimeZone();
		TimeZone toTimeZone = TimeZone.getTimeZone("UTC");
		calendar.setTimeZone(fromTimeZone);
		calendar.add(Calendar.MILLISECOND, fromTimeZone.getRawOffset() * -1);
		if (fromTimeZone.inDaylightTime(calendar.getTime())) {
			calendar.add(Calendar.MILLISECOND, calendar.getTimeZone().getDSTSavings() * -1);
		}
		
		calendar.add(Calendar.MILLISECOND, toTimeZone.getRawOffset());
		if (toTimeZone.inDaylightTime(calendar.getTime())) {
			calendar.add(Calendar.MILLISECOND, toTimeZone.getDSTSavings());
		}
		
		logger.info("ControllerUtil convertToUTCTimeZone : {}", calendar.getTime());
		return calendar;
	}
	
	public static Calendar convertToBusinessTimeZoneWithDaylight(Date date, TimeZone toTimeZone) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		TimeZone fromTimeZone = calendar.getTimeZone();
		logger.info("current system time : {}", calendar.getTime());
		logger.info("fromTimeZone attributes : {}", fromTimeZone);
		logger.info("toTimeZone attributes : {}", toTimeZone);
		if (fromTimeZone.inDaylightTime(calendar.getTime())) {
			calendar.add(Calendar.MILLISECOND, fromTimeZone.getDSTSavings());
		}
		calendar.add(Calendar.MILLISECOND, fromTimeZone.getRawOffset() * -1);
		calendar.add(Calendar.MILLISECOND, toTimeZone.getRawOffset());
		if (toTimeZone.inDaylightTime(calendar.getTime())) {
			calendar.add(Calendar.MILLISECOND, toTimeZone.getDSTSavings());
		}
		return calendar;
	}
	
	public static Integer getDiffInMinute(Calendar calendar, Integer totalEndtime, String timeZone) {
		
		String replacedString = timeZone;
		if (("Central Standard Time").equalsIgnoreCase(timeZone) || ("Central Daylight Time").equalsIgnoreCase(timeZone))
			replacedString = "CST";
		
		Calendar cal = convertToBusinessTimeZoneWithDaylight(calendar.getTime(), TimeZone.getTimeZone(ZoneId.of(replacedString)));
		cal.add(Calendar.SECOND, totalEndtime * 3);
		
		if (cal.get(Calendar.DAY_OF_YEAR) > calendar.get(Calendar.DAY_OF_YEAR) || (cal.get(Calendar.HOUR_OF_DAY) >= 19 && cal.get(Calendar.MINUTE) >= 45)) {
			
			Calendar cal2 = Calendar.getInstance();
			cal2.setTime(cal.getTime());
			cal2.set(Calendar.HOUR_OF_DAY, 19);
			cal2.set(Calendar.MINUTE, 45);
			
			// Get the represented date in milliseconds
			long millis1 = cal2.getTimeInMillis();
			long millis2 = cal.getTimeInMillis();
			
			// Calculate difference in milliseconds
			long diff = millis2 - millis1;
			
			// Calculate difference in minutes
			long diffMinutes = diff / (60 * 1000);
			
			return (int) diffMinutes;
		}
		return 0;
		
	}
	
	/**
	 * Return all fields of class as List
	 * 
	 * @param type
	 * @return
	 */
	public static List<Field> getAllFields(Class<?> type) {
		List<Field> fields = new ArrayList<>();
		for (Class<?> c = type; c != null; c = c.getSuperclass()) {
			fields.addAll(Arrays.asList(c.getDeclaredFields()));
		}
		return fields;
	}
	
	public static boolean isNotValidMMSImageUrl(boolean enableMMS, String mediaUrl, String customImageUrl) {
		return (enableMMS && StringUtils.isBlank(mediaUrl) && StringUtils.isBlank(customImageUrl));
	}
	
	public static Calendar convertToTimeZone(Date date, String timeZoneId) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		return convertToTimeZone(cal, timeZoneId);
	}
	
	public static Calendar convertToTimeZone(Calendar calendar, String timeZoneId) {
		TimeZone fromTimeZone = calendar.getTimeZone();
		TimeZone toTimeZone = TimeZone.getTimeZone(timeZoneId);
		calendar.setTimeZone(fromTimeZone);
		calendar.add(Calendar.MILLISECOND, fromTimeZone.getRawOffset() * -1);
		if (fromTimeZone.inDaylightTime(calendar.getTime())) {
			calendar.add(Calendar.MILLISECOND, calendar.getTimeZone().getDSTSavings() * -1);
		}
		
		calendar.add(Calendar.MILLISECOND, toTimeZone.getRawOffset());
		if (toTimeZone.inDaylightTime(calendar.getTime())) {
			calendar.add(Calendar.MILLISECOND, toTimeZone.getDSTSavings());
		}
		return calendar;
	}
	
	public static String prepareFormattedDatePST(Date date, String format) {
		String formattedDate = null;
		try {
			ZonedDateTime zdt = ZonedDateTime.ofInstant(date.toInstant(), ZoneId.of(AMERICA_LOS_ANGELES));
			formattedDate = DateTimeFormatter.ofPattern(format).format(zdt);
		} catch (Exception e) {
			// no-op
		}
		return formattedDate;
	}
	
	public static String prepareFormattedDateToPST(String date, String format) {
		LocalDateTime localDateTime = LocalDateTime.parse(date, DATE_TIME_FORMATTER);
		Date utcDate = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
		return prepareFormattedDatePST(utcDate, format);
	}
	
	public static ZonedDateTime convertDatePST(Date date) {
		return ZonedDateTime.ofInstant(date.toInstant(), ZoneId.of(AMERICA_LOS_ANGELES));
	}
	
	public static String getPercentileStr(Long count, Long total) {
		Float f = getPercentile(count, total);
		DecimalFormat df = new DecimalFormat("#.##");
		return df.format(f);
	}
	
	public static Float getPercentile(Long count, Long total) {
		if (total != null && total == 0) {
			return 0f;
		}
		return Math.round((count.floatValue() / total.floatValue() * 100) * 10) / 10F;
	}
	
	public static Double getPercentile(Double count, Long total) {
		if (total != null && total == 0) {
			return 0d;
		}
		return Math.round((count.floatValue() / total.floatValue() * 100) * 10) / 10d;
	}
	
	public static Double getPercentile(Double count, Double total) {
		if (total != null && total == 0d) {
			return 0d;
		}
		return Math.round((count.floatValue() / total.floatValue() * 100) * 10) / 10d;
	}
	
	public static double round(double key) {
		return Math.round(key * 100.0) / 100.0;
	}
	
	public static double roundToSingleDigit(double key) {
		return Math.round(key * 10.0) / 10.0;
	}
	
	public static Integer getNullSafeInteger(String strInt) {
		if (StringUtils.isBlank(strInt)) {
			return null;
		}
		Integer intVal = null;
		try {
			intVal = Integer.parseInt(strInt);
		} catch (NumberFormatException e) {
			logger.error("ERROR while parsing value as integer {}", strInt);
			return null;
		}
		return intVal;
	}
	
	public static String encode(String text) {
		return encode(text, null);
	}
	
	public static String encodeWithSpacesEncodedUTF8(String text) {
		String str = encode(text, null);
		str = StringUtils.replace(str, "+", "%20");
		return str;
	}
	
	public static String replaceLineBreaks(String input) {
		input = StringUtils.replace(input, "<br />", " ");
		input = StringUtils.replace(input, "<br>", " ");
		input = StringUtils.replace(input, "\n", " ");
		return input;
	}
	
	public static String encode(String text, String format) {
		String encodedText = text;
		if (StringUtils.isNotBlank(text)) {
			try {
				format = StringUtils.isBlank(format) ? "UTF-8" : format;
				encodedText = URLEncoder.encode(text, format);
			} catch (Exception e) {
				logger.error("Unable to encode text :{},Reason:{}", text, e);
			}
		}
		return encodedText;
	}
	
	public static String decode(String text, String format) {
		String decodedText = text;
		if (StringUtils.isNotBlank(text)) {
			try {
				text = text.replaceAll("%(?![0-9a-fA-F]{2})", "%25");
				format = StringUtils.isBlank(format) ? "UTF-8" : format;
				decodedText = URLDecoder.decode(text, format);
			} catch (Exception e) {
				logger.info("Unable to decode text :{},Reason:{}", text, e);
			}
		}
		return decodedText;
	}
	
	public static String getUserName(String firstName, String lastName, String emailId) {
		String name = null;
		if (StringUtils.isBlank(firstName) && StringUtils.isBlank(lastName) && StringUtils.isNotBlank(emailId) && StringUtils.containsIgnoreCase(emailId, "@")) {
			name = emailId.substring(0, emailId.indexOf("@"));
		} else if (StringUtils.isNotBlank(firstName) || StringUtils.isNotBlank(lastName)) {
			name = firstName;
			if (StringUtils.isNotBlank(lastName)) {
				if (StringUtils.isBlank(name)) {
					name = lastName;
				} else {
					name = name + " " + lastName;
				}
			}
		}
		return name;
	}
	
	public static String getUserName(String userName, String emailId) {
		if(StringUtils.isNotEmpty(userName)) {
			return userName;
		}
		String name = null;
		if (StringUtils.isBlank(userName) && StringUtils.isNotBlank(emailId) && StringUtils.containsIgnoreCase(emailId, "@")) {
			name = emailId.substring(0, emailId.indexOf("@"));
		} 
		return name;
	}
	
	public static String getCustomerName(String firstName, String lastName, String email, String phone) {
		String name = StringUtils.isNotBlank(firstName) ? firstName : "";
		if (StringUtils.isBlank(name) && StringUtils.isNotBlank(lastName)) {
			name = lastName;
		} else {
			name = name + (StringUtils.isNotBlank(lastName) ? (" " + lastName) : "");
		}
		if (StringUtils.isBlank(name) && StringUtils.isNotBlank(email)) {
			if (StringUtils.contains(email, "@")) {
				name = email.substring(0, email.indexOf("@"));
			} else {
				name = email;
			}
		} else if (StringUtils.isBlank(name) && StringUtils.isNotBlank(phone)) {
			name = phone;
		}
		return name;
	}
	
	public static String getCustomerName(String firstName, String lastName) {
		String name = StringUtils.isNotBlank(firstName) ? firstName : "";
		if (StringUtils.isBlank(name) && StringUtils.isNotBlank(lastName)) {
			name = lastName;
		} else {
			name = name + (StringUtils.isNotBlank(lastName) ? (" " + lastName) : "");
		}
		return name;
	}
	
	public static Integer getIntegerValueFromBoolean(Boolean bool) {
		if (bool == null || bool == false) {
			return 0;
		}
		return 1;
	}
	
	public static boolean getBooleanValueFromInteger(Integer intValue) {
		if (intValue == null || intValue == 0) {
			return false;
		}
		return true;
	}
	
	public static String getFormattedBusinessPhoneNumber(String phone, List<String> countryCodes) {
		Boolean formatted = Boolean.FALSE;
		if (countryCodes == null || countryCodes.isEmpty()) {
			countryCodes = new ArrayList<>();
			countryCodes.add("US");
		}
		for (int i = 0; i < countryCodes.size() && !formatted; i++) {
			String countryCode = countryCodes.get(i);
			if (StringUtils.isNotBlank(countryCode)) {
				countryCode = getIsoCountryCode(countryCode);
			}
			// added special check for MX
			if ("MX".equalsIgnoreCase(countryCode) && !PhoneNoValidator.isValidPhoneNumber(phone, countryCode)) {
				countryCode = "US";
			}
			PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance();
			try {
				Phonenumber.PhoneNumber phoneNumber = phoneUtil.parse(phone, countryCode);
				// using international format example: format expected by Twilio is +***********
				phone = phoneUtil.format(phoneNumber, PhoneNumberUtil.PhoneNumberFormat.E164);
				formatted = Boolean.TRUE;
			} catch (NumberParseException exe) {
				logger.error("Error {} while formatting the number", exe.getLocalizedMessage());
			}
		}
		return phone;
	}

	public static boolean isEmailIdValid(String emailId) {
		return EmailValidator.getInstance().isValid(emailId) || isValidEmailAddress(emailId);
	}
	
	private static boolean isValidEmailAddress(String email) {
		
		if (StringUtils.isBlank(email)) {
			return false;
		}
		
		Matcher matcher = Pattern.compile(EMAIL_REGEX).matcher(email);
		return matcher.matches();
		
	}
	
	public static ZonedDateTime convertUTCToPST(Date date) {
		if (date != null) {
			return ZonedDateTime.ofInstant(date.toInstant(), ZoneId.of(AMERICA_LOS_ANGELES));
		}
		return null;
	}
	
	public static String formatZonedDateTimeToMMMddYYYY(ZonedDateTime zdt) {
		if (zdt != null) {
			return DateTimeFormatter.ofPattern("MMM dd, yyyy").format(zdt);
		}
		return null;
	}
	
	public static String formatDateToMMMddYYYY(Date date) {
		return formatZonedDateTimeToMMMddYYYY(convertUTCToPST(date));
	}
	
	public static String formatZonedDateTimeToMMddyyyy(ZonedDateTime zdt) {
		if (zdt != null) {
			return DateTimeFormatter.ofPattern("MM/dd/yyyy").format(zdt);
		}
		return null;
	}
	
	public static String formatDateTommddyyyy(Date date) {
		return formatZonedDateTimeToMMddyyyy(convertUTCToPST(date));
	}
	
	public static String formatUserName(String firstName, String lastName, String emailId) {
		StringBuilder formattedName = new StringBuilder();
		if (StringUtils.isBlank(firstName) && StringUtils.isBlank(lastName)) {
			return emailId;
		}
		
		if (StringUtils.isNoneBlank(firstName)) {
			formattedName.append(Character.toUpperCase(firstName.charAt(0))).append(firstName.substring(1)).append(" ");
		}
		if (StringUtils.isNoneBlank(lastName)) {
			formattedName.append(Character.toUpperCase(lastName.charAt(0))).append(lastName.substring(1));
		}
		return formattedName.toString().trim();
	}
	
	public static boolean isTrueForInteger(Integer val) {
		if (val == null || val.intValue() == 0) {
			return false;
		}
		return true;
	}
	
	public static boolean isTrueForLong(Long val) {
		if (val == null || val == 0) {
			return false;
		}
		return true;
	}
	
	/**
	 * Preparing business address.
	 * 
	 * @param business
	 * @return
	 */
	public static String getBusinessAddress(BusinessEnterpriseEntity business) {
		StringBuilder businessAddress = new StringBuilder();
		if (business != null) {
			if (StringUtils.isNotBlank(business.getAddress1())) {
				businessAddress.append(business.getAddress1().trim());
			}
			if (StringUtils.isNotBlank(business.getAddress2())) {
				businessAddress.append(StringUtils.SPACE).append(business.getAddress2().trim());
			}
			if (StringUtils.isNotBlank(business.getCity())) {
				if (StringUtils.isNotBlank(businessAddress.toString())) {
					businessAddress.append(", ");
				}
				businessAddress.append(business.getCity().trim());
			}
			if (StringUtils.isNotBlank(business.getState())) {
				if (StringUtils.isNotBlank(businessAddress.toString())) {
					businessAddress.append(", ");
				}
				businessAddress.append(business.getState().trim());
			}
			if (StringUtils.isNotBlank(business.getZip())) {
				businessAddress.append(StringUtils.SPACE).append(business.getZip().trim());
			}
		}
		return businessAddress.toString();
	}
	
	public static boolean ifUrlContainsQueryParams(String urlString) {
		if (StringUtils.isEmpty(urlString))
			return false;
		
		URL url;
		try {
			url = new URL(urlString);
		} catch (MalformedURLException e) {
			logger.warn("invalid url {} passed", urlString);
			return false;
		}
		
		if (url != null && url.getQuery() != null)
			return true;
		
		return false;
	}
	
	public static Calendar getUnixZeroUnitDate() {
		Calendar defaultDate = Calendar.getInstance();
		try {
			defaultDate.setTime(new SimpleDateFormat("MM/dd/yyyy HH:mm:ss").parse("01/02/1970 00:00:00"));
		} catch (ParseException e) {
			logger.error("Exception while parsing default zero point unix timestamp while checking survey request reminder generation applicability", e);
			return null;
		}
		return defaultDate;
	}
	
	public static ReferralSource getReferralSourceById(Integer sourceId) {
		return CacheManager.getInstance().getCache(ReferralSourceCache.class).getReferralSource(sourceId);
	}
	
	public static boolean isEmailFailureEvent(String event) {
		return StringUtils.equalsAnyIgnoreCase(event, "unsubscribe", "spamreport", "bounce", "dropped", "group_unsubscribe");
	}
	
	public static boolean isEmailConfigErrorEvent(String event) {
		return StringUtils.equalsAnyIgnoreCase(event, "Bad Email Sender Configuration");
	}
	
	public static boolean isEmailOpenEvent(String event) {
		return StringUtils.equalsAnyIgnoreCase(event, "open");
	}
	
	/**
	 * 
	 * @param business
	 * @return
	 */
	public static boolean isNotValidBusiness(BusinessEnterpriseEntity business) {
		return BusinessActivationStatusEnum.CANCELLED.getStatus().equalsIgnoreCase(business.getActivationStatus())
				|| BusinessActivationStatusEnum.SUSPENDED.getStatus().equalsIgnoreCase(business.getActivationStatus());
	}
	
	/**
	 * 
	 * @param date
	 * @param dateFormat
	 * @return
	 */
	public static LocalDate getValidDate(String date, String dateFormat) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
		try {
			return LocalDate.parse(date, formatter);
		} catch (DateTimeParseException e) {
			logger.error("[getValidDate] : error while parsing date {} using format {}", date, dateFormat);
		}
		return null;
	}
	
	public static String convertToValidIdentifier(String operand) {
		if (operand == null || operand.isEmpty())
			return operand;
		StringBuilder sb = new StringBuilder("$");
		for (char c : operand.toCharArray()) {
			if (!Character.isJavaIdentifierPart(c)) {
				sb.append("_");
			} else {
				sb.append(c);
			}
		}
		return sb.toString();
	}
	
	/**
	 * Get SMB ID or Enterprise ID from location/SMB
	 * 
	 * @param locationOrSmb
	 * @return
	 */
	public static Integer getSmbOrEnterpriseId(BusinessEnterpriseEntity locationOrSmb) {
		if (locationOrSmb.getEnterpriseId() != null) {
			return locationOrSmb.getEnterpriseId();
		}
		return locationOrSmb.getId();
	}
	
	/**
	 * Replace new line character(s) with <br />
	 * tag
	 * 
	 * @param str
	 * @return
	 */
	public static String replaceNewLineWithBRTag(String str) {
		if (StringUtils.isBlank(str)) {
			return str;
		}
		return StringUtils.replace(str, "\n", "<br />");
	}
	
	/**
	 * Generates timestamp in format yyyy-MM-dd HH:mm:ss
	 * 
	 * @param date
	 * @return
	 */
	public static String getDateTimestamp(Date date) {
		if (date == null) {
			return null;
		}
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return dateFormat.format(date);
	}
	
	public static Integer convertLongToInt(Long value) {
		return value.intValue();
	}

	public static String getAliasOrName(BusinessEnterpriseEntity business) {
		String businessName = business.getAlias1();
		if(StringUtils.isBlank(businessName)) {
			businessName = business.getName();
		}
		return businessName;
	}
	
	public static boolean isInactive(BusinessEnterpriseEntity business) {
		if(business.getClosed()!=0 || BusinessActivationStatusEnum.SUSPENDED.getStatus().equalsIgnoreCase(business.getActivationStatus()) || BusinessActivationStatusEnum.CANCELLED.getStatus().equalsIgnoreCase(business.getActivationStatus())) {
			return true;
		}
		return false;
	}
	
	public static boolean isSMB(BusinessEnterpriseEntity business) {
		if(business.getEnterpriseId() ==  null && BusinessTypeEnum.BUSINESS.getBusinessType().equalsIgnoreCase(business.getType())) {
			return true;
		}
		return false;
	}
	
	public static boolean isReseller(BusinessEnterpriseEntity business) {
		if(BusinessTypeEnum.RESELLER.getBusinessType().equals(business.getType())) {
			return true;
		}
		return false;
	}
	
	public static String getStringValue(String value) {
		return StringUtils.isNotBlank(value) ? value : StringUtils.EMPTY;
	}

	/**
	 * Checks if a given number is divisible by a specified divisor.
	 * @param number  The number to be checked for divisibility.
	 * @param divisor The divisor to check against.
	 * @return True if the number is divisible by the divisor, false otherwise.
	 */
	public static boolean isgivenValueDivisibleByDivisorValue(long number, long divisor) {
		return (number % divisor == 0);
	}
	
	/**
	 * Checks if the particular enterprise or smb is under a reseller.
	 * 
	 * @param The account type
	 * 
	 */
	public static boolean isAccountUnderReseller(String accountType) {
		if (StringUtils.equalsAnyIgnoreCase(accountType, BusinessAccountTypeEnum.COBRANDED.getType(), BusinessAccountTypeEnum.WHITELABELED.getType())) {
			return true;
		}
		return false;
	}
	
	public static Integer getIntegerValueFromLong(Long value) {
		if(value==null) {
			return null;
		}
		return Integer.valueOf(String.valueOf(value));
	}
	
	public static String getHyphenSeparatedList(Set<?> list) {
		String key = "";
		
		if (CollectionUtils.isEmpty(list)) {
			return key;
		}
		
		key = list.stream().map(String::valueOf).collect(Collectors.joining("-"));
		
		return key;
	}
	
	public static boolean isValidE164(String phoneNumber) {
		try {
			PhoneNumber parsed = phoneUtil.parse(phoneNumber, null);
			if (!phoneUtil.isValidNumber(parsed)) {
				return false;
			}
			String formatted = phoneUtil.format(parsed, PhoneNumberUtil.PhoneNumberFormat.E164);
			return formatted.equals(phoneNumber);
		} catch (NumberParseException e) {
			return false;
		}
	}
	
}
