package com.birdeye.campaign.utils;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

public final class ModifiedFieldNameMapper {
	
	private static final Map<String, Map<String, String>> MAPPER = new HashMap<>();
	
	private ModifiedFieldNameMapper() {
		throw new IllegalStateException("Utility class. Not meant to be instantized.");
	}
	
	static {
		// Default Mappings For Some Common Fields
		Map<String, String> defaultMappings = new HashMap<String, String>();
		defaultMappings.put("createdAt", StringUtils.EMPTY);
		defaultMappings.put("updatedAt", StringUtils.EMPTY);
		defaultMappings.put("priorityOrder", StringUtils.EMPTY);
		defaultMappings.put("createdById", StringUtils.EMPTY);
		defaultMappings.put("updatedById", StringUtils.EMPTY);
		
		// Campaign Entity Field Name Mappings
		Map<String, String> campaignMappings = new HashMap<>();
		campaignMappings.put("type", "campaignType");
		campaignMappings.put("startAt", "cmpStartAt"); // To do for manual
		campaignMappings.put("endAt", "cmpEndDate");
		campaignMappings.put("name", "campaignName");
		campaignMappings.put("status", "statusId");
		campaignMappings.put("templateId", "emailTemplateId");
		campaignMappings.put("reminderFrequency", "reminderInterval");
		campaignMappings.put("schedule", "scheduled");
		campaignMappings.put("bypassCommRestriction", "overrideCommRestriction");
		campaignMappings.put("ruleExpression", "expression");
		campaignMappings.put("contactSources", "sources");
		campaignMappings.put("triggerRuleExpression", "triggerExpression");
		campaignMappings.put("recurringReminderEventType", "contactEventType");
		campaignMappings.put("executionDateInfo", "dueDateInfo");
		campaignMappings.put("appointmentScheduleInfo", "scheduleInfo");
		
		Map<String, String> appointmentFormCampaignMappings = new HashMap<>();
		appointmentFormCampaignMappings.put("appointmentScheduleInfo", "appointmentScheduleInfo");
		
		Map<String, String> appointmentRecallCampaignMappings = new HashMap<>();
		appointmentRecallCampaignMappings.put("appointmentScheduleInfo", "appointmentScheduleInfo");
		
		Map<String, String> appointmentReminderCampaignMappings = new HashMap<>();
		appointmentReminderCampaignMappings.put("appointmentScheduleInfo", "appointmentScheduleInfo");
		
		// SMS template mappings
		Map<String, String> smsTemplateMappings = new HashMap<>();
		smsTemplateMappings.put("enableSentimentCheck ", "isSentimentCheckEnabled");
		smsTemplateMappings.put("enableContactUs", "contactUsEnabled");
		smsTemplateMappings.put("messageBody", "textMessage");
		smsTemplateMappings.put("unsubscribeText", "defaultUnsubscribeText");
		smsTemplateMappings.put("unsubscribeTextEnabled", "includeUnsubscribeText");
		smsTemplateMappings.put("mmsEnabled", "includeImageWithText");
		smsTemplateMappings.put("nonRecommendReviewPageMessage", "feedbackMessage");
		smsTemplateMappings.put("enableFeedbackMessage", "feedbackCallbackEnabled");
		smsTemplateMappings.put("enableFeedbackCheckbox", "feedbackCheckboxEnabled");
		smsTemplateMappings.put("reviewEnable", "reviewEnabled");
		smsTemplateMappings.put("recommendPageHeading", "reviewHeading");
		smsTemplateMappings.put("recommendPageMessage", "reviewMessage");
		smsTemplateMappings.put("nonRecommendedUrl", "contactUsCustomUrl");
		smsTemplateMappings.put("sentimentCheckType", "cxDataByType");
		smsTemplateMappings.put("starHeading", "cxDataByType");
		smsTemplateMappings.put("starMessage", "cxDataByType");
		smsTemplateMappings.put("starLabels", "cxDataByType");
		smsTemplateMappings.put("question", "cxDataByType");
		smsTemplateMappings.put("writeReviewQuestion", "cxDataByType");
		smsTemplateMappings.put("npsLabels", "cxDataByType");
		smsTemplateMappings.put("sentimentHeading", "cxDataByType");
		smsTemplateMappings.put("sentimentMessage", "cxDataByType");
		smsTemplateMappings.put("positiveLinkLabel", "cxDataByType");
		smsTemplateMappings.put("negativeLinkLabel", "cxDataByType");
		smsTemplateMappings.put("neutralLinkLabel", "cxDataByType");
		smsTemplateMappings.put("showEmoticon", "cxDataByType");
		smsTemplateMappings.put("excludeNeutral", "cxDataByType");
		smsTemplateMappings.put("thankyouHeading", "thankYouHeading");
		smsTemplateMappings.put("thankyouMessage", "thankYouMessage");
		smsTemplateMappings.put("smsCategory", "templateCategory");
		
		// Referral SMS template mappings
		Map<String, String> referralSmsTemplateMappings = new HashMap<>();
		referralSmsTemplateMappings.put("tyPageHeading", "thankYouHeading");
		referralSmsTemplateMappings.put("tyPageMessage", "thankYouMessage");
		referralSmsTemplateMappings.put("tyPageCtaLabel", "thankYouCtaLabel");
		referralSmsTemplateMappings.put("tyPageCtaUrl", "thankYouCtaUrl");
		
		// Promotional SMS template mappings
		Map<String, String> promotionSmsTemplateMappings = new HashMap<>();
		promotionSmsTemplateMappings.put("messageBody", "reviewMessage");
		
		// Email template mappings
		Map<String, String> emailTemplateMappings = new HashMap<>();
		emailTemplateMappings.put("nonRecommendPageMessage", "feedbackMessage");
		emailTemplateMappings.put("feedbackShowCallbackOption", "feedbackCallbackEnabled");
		emailTemplateMappings.put("feedbackDefaultCheckboxChecked", "feedbackCheckboxEnabled");
		emailTemplateMappings.put("feedbackMessage", "feedbackCallbackMessage");
		emailTemplateMappings.put("enableContactUs", "contactUsEnabled");
		emailTemplateMappings.put("nonRecommendedUrl", "contactUsCustomUrl");
		emailTemplateMappings.put("emailCategory", "templateCategory");
		
		// Survey Email template mappings
		Map<String, String> surveyEmailTemplateMapping = new HashMap<>();
		surveyEmailTemplateMapping.put("emailQuestion", "reviewHeading");
		surveyEmailTemplateMapping.put("message", "reviewMessage");
		surveyEmailTemplateMapping.put("positiveButtonLabel", "surveyButtonText");
		
		//Review Email template mappings
		Map<String, String> reviewEmailTemplateMapping = new HashMap<>();
		reviewEmailTemplateMapping.put("message", "reviewMessage");
		reviewEmailTemplateMapping.put("emailQuestion", "reviewHeading");
		
		// Promotional Email template mappings
		Map<String, String> promotionEmailTemplateMapping = new HashMap<>();
		promotionEmailTemplateMapping.put("emailQuestion", "message");
		promotionEmailTemplateMapping.put("message", "reviewMessage");
		
		// Appointment Reminder Email template mappings
		Map<String, String> appointmentReminderEmailTemplateMapping = new HashMap<>();
		appointmentReminderEmailTemplateMapping.put("message", "reviewMessage");
		
		// Appointment Recall Email template mappings
		Map<String, String> appointmentRecallEmailTemplateMapping = new HashMap<>();
		appointmentRecallEmailTemplateMapping.put("emailQuestion", "reviewHeading");
		appointmentRecallEmailTemplateMapping.put("message", "reviewMessage");
		
		//Referral Email template mappings
		Map<String, String> referralEmailTemplateMapping = new HashMap<>();
		referralEmailTemplateMapping.put("message", "referralMessage");
		referralEmailTemplateMapping.put("tyPageHeading ", "thankYouHeading");
		referralEmailTemplateMapping.put("tyPageMessage", "thankYouMessage");
		referralEmailTemplateMapping.put("tyPageCtaLabel", "thankYouCtaLabel");
		referralEmailTemplateMapping.put("tyPageCtaUrl", "thankYouCtaUrl");
		
		// Appointment Form Email template mappings
		Map<String, String> appointmentFormEmailTemplateMapping = new HashMap<>();
		appointmentFormEmailTemplateMapping.put("message", "reviewMessage");
		
		//CX Email template mappings
		Map<String, String> cxEmailTemplateMapping = new HashMap<>();
		cxEmailTemplateMapping.put("sentimentCheckType", "cxDataByType");
		cxEmailTemplateMapping.put("emailQuestion", "cxDataByType");
		cxEmailTemplateMapping.put("message", "cxDataByType");
		cxEmailTemplateMapping.put("npsRatingMin", "cxDataByType");
		cxEmailTemplateMapping.put("npsLabels", "cxDataByType");
		cxEmailTemplateMapping.put("starHeading", "cxDataByType");
		cxEmailTemplateMapping.put("starMessage", "cxDataByType");
		cxEmailTemplateMapping.put("starRatingMin", "cxDataByType");
		cxEmailTemplateMapping.put("starLabels", "cxDataByType");
		cxEmailTemplateMapping.put("sentimentHeading", "cxDataByType");
		cxEmailTemplateMapping.put("sentimentMessage", "cxDataByType");
		cxEmailTemplateMapping.put("positiveButtonLabel", "cxDataByType");
		cxEmailTemplateMapping.put("negativeButtonLabel", "cxDataByType");
		cxEmailTemplateMapping.put("neutralButtonLabel", "cxDataByType");
		cxEmailTemplateMapping.put("showEmoticon", "cxDataByType");
		cxEmailTemplateMapping.put("excludeNeutral", "cxDataByType");
		
		// Store mappings by template type
		MAPPER.put("default", defaultMappings);
		MAPPER.put("campaign", campaignMappings);
		MAPPER.put("campaign_appointment_reminder", appointmentReminderCampaignMappings);
		MAPPER.put("campaign_appointment_recall", appointmentRecallCampaignMappings);
		MAPPER.put("campaign_appointment_form", appointmentFormCampaignMappings);
		MAPPER.put("sms_template", smsTemplateMappings);
		MAPPER.put("sms_template_promotion", promotionSmsTemplateMappings);
		MAPPER.put("sms_template_referral", referralSmsTemplateMappings);
		MAPPER.put("email_template", emailTemplateMappings);
		MAPPER.put("email_template_survey_request", surveyEmailTemplateMapping);
		MAPPER.put("email_template_review_request_new", reviewEmailTemplateMapping);
		MAPPER.put("email_template_promotion", promotionEmailTemplateMapping);
		MAPPER.put("email_template_appointment_reminder", appointmentReminderEmailTemplateMapping);
		MAPPER.put("email_template_appointment_recall", appointmentRecallEmailTemplateMapping);
		MAPPER.put("email_template_referral", referralEmailTemplateMapping);
		MAPPER.put("email_template_appointment_form", appointmentFormEmailTemplateMapping);
		MAPPER.put("email_template_customer_experience", cxEmailTemplateMapping);
	}
	
	/**
	 * Get the mapped value for a field based on type information.
	 *
	 * @param entityType (e.g., "campaign", "sms_template", "email_template")
	 * @param fieldName
	 * @return the mapped value, or the original field name if no mapping exists
	 */
	public static String getMappedValue(String entityType, String fieldName, String category) {
		Map<String, String> fieldMappings = MAPPER.get(StringUtils.join(entityType, "_", category));
		if (fieldMappings != null && fieldMappings.containsKey(fieldName)) {
			return fieldMappings.get(fieldName);
		}
		// Fall back to "#entityType" mapping
		Map<String, String> mappings = MAPPER.get(StringUtils.join(entityType));
		if (mappings != null && mappings.containsKey(fieldName)) {
			return mappings.get(fieldName);
		}
		
		// Fall back to "default" mappings or return the fieldName itself
		return MAPPER.getOrDefault("default", new HashMap<>()).getOrDefault(fieldName, fieldName);
	}
}
