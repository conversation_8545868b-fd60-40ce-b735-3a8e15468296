package com.birdeye.campaign.utils;

import java.util.Date;
import java.util.Optional;

import com.birdeye.campaign.platform.constant.CommunicationCategoryEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.entity.BusinessSmsTemplate;
import com.birdeye.campaign.enums.EmailTemplateTypes;
import com.birdeye.campaign.enums.SentimentCheckTypeEnum;
import com.birdeye.campaign.platform.constant.TemplateTypeEnum;
import com.birdeye.template.sms.dto.SmsTemplateMessage;

public class BusinessTemplateUtils {
	
	private BusinessTemplateUtils() {}

	public static void setSentimentCheckOptions(SmsTemplateMessage smsTemplateMessage,
			BusinessSmsTemplate businessSmsTemplate) {
		businessSmsTemplate.setSentimentCheckType(smsTemplateMessage.getSentimentCheckType());
		businessSmsTemplate.setStarRatingMin(smsTemplateMessage.getStarRatingMin());
		businessSmsTemplate.setNpsRatingMin(smsTemplateMessage.getNpsRatingMin());
		SentimentCheckTypeEnum sentimentCheckTypeEnum = SentimentCheckTypeEnum
				.findByKey(smsTemplateMessage.getSentimentCheckType());
		if (sentimentCheckTypeEnum == null)
			return;
		switch (sentimentCheckTypeEnum) {
		case YES_NO:
			businessSmsTemplate.setShowEmoticon(smsTemplateMessage.isShowEmoticon() ? 1 : 0);
			break;
		case SENTIMENT:
			businessSmsTemplate.setShowEmoticon(smsTemplateMessage.isShowEmoticon() ? 1 : 0);
			break;
		case STAR:
			businessSmsTemplate.setStarLabels(smsTemplateMessage.getStarLabels());
			break;
		case NPS:
			businessSmsTemplate.setNpsLabels(smsTemplateMessage.getNpsLabels());
			break;

		default:
			break;
		}
	}
	
	public static void updateSmsTemplateFields(BusinessSmsTemplate smsTemplate, SmsTemplateMessage smsTemplateMessage,
			Integer userId) {
		if (StringUtils.isNotBlank(smsTemplateMessage.getMessageFrom())) {
			smsTemplate.setMessageFrom(smsTemplateMessage.getMessageFrom());
		}
		if (StringUtils.isNotBlank(smsTemplateMessage.getMessageBody())) {
			smsTemplate.setMessageBody(smsTemplateMessage.getMessageBody());
		}
		if (StringUtils.isNotBlank(smsTemplateMessage.getName())) {
			smsTemplate.setName(smsTemplateMessage.getName());
		}
		if (StringUtils.isNotBlank(smsTemplateMessage.getQuestion())) {
			smsTemplate.setQuestion(smsTemplateMessage.getQuestion());
		}
		if (StringUtils.isNotBlank(smsTemplateMessage.getPositiveLinkLabel())) {
			smsTemplate.setPositiveLinkLabel(smsTemplateMessage.getPositiveLinkLabel());
		}
		if (StringUtils.isNotBlank(smsTemplateMessage.getNegativeLinkLabel())) {
			smsTemplate.setNegativeLinkLabel(smsTemplateMessage.getNegativeLinkLabel());
		}
		if (StringUtils.isNotBlank(smsTemplateMessage.getNeutralLinkLabel())) {
			smsTemplate.setNeutralLinkLabel(smsTemplateMessage.getNeutralLinkLabel());
		}
		smsTemplate.setExcludeNeutral(smsTemplateMessage.getExcludeNeutral());
		smsTemplate.setWriteReviewQuestion(smsTemplateMessage.getWriteReviewQuestion());
		smsTemplate.setCustomImageUrl(smsTemplateMessage.getCustomImageUrl());
		smsTemplate.setNonRecommendReviewPageMessage(smsTemplateMessage.getNonRecommendReviewPageMessage());
		smsTemplate.setNonRecommendedUrl(smsTemplateMessage.getNonRecommendedUrl());
		smsTemplate.setUnsubscribeTextEnabled(smsTemplateMessage.getUnsubscribeTextEnabled());
		smsTemplate.setStarHeading(smsTemplateMessage.getStarHeading());
		smsTemplate.setSentimentHeading(smsTemplateMessage.getSentimentHeading());
		smsTemplate.setStarMessage(smsTemplateMessage.getStarMessage());
		smsTemplate.setSentimentMessage(smsTemplateMessage.getSentimentMessage());
		smsTemplate.setSmsCategory(TemplateUtils.getUnsubscriptionCategoryWithDefault(smsTemplateMessage.getSmsCategory(), smsTemplate.getType()));
		if (StringUtils.isNotBlank(smsTemplateMessage.getUnsubscribeText())) {
			smsTemplate.setUnsubscribeText(smsTemplateMessage.getUnsubscribeText());
		}
		if (EmailTemplateTypes.CUSTOMER_EXPERIENCE.name().equalsIgnoreCase(smsTemplate.getType())) {
			setSentimentCheckOptions(smsTemplateMessage, smsTemplate);
			if (smsTemplateMessage.getIsSentimentCheckEnabled() != null) {
				smsTemplate.setEnableSentimentCheck(smsTemplateMessage.getIsSentimentCheckEnabled());
			}

		} else {
			if (smsTemplateMessage.getIsSentimentCheckEnabled() != null) {
				smsTemplate.setEnableSentimentCheck(smsTemplateMessage.getIsSentimentCheckEnabled());
				if (smsTemplateMessage.getIsSentimentCheckEnabled() == 1) {
					setSentimentCheckOptions(smsTemplateMessage, smsTemplate);
				}
			}
		}

		if (smsTemplateMessage.getMmsEnable() != null) {
			smsTemplate.setMmsEnabled(smsTemplateMessage.getMmsEnable());
		}
		smsTemplate.setUpdatedAt(new Date());
		smsTemplate.setUpdatedBy(userId);
		if (StringUtils.isNotBlank(smsTemplateMessage.getMediaUrl())) {
			smsTemplate.setMediaUrl(smsTemplateMessage.getMediaUrl());
		} else {
			smsTemplate.setMediaUrl(null);
		}
		if (StringUtils.isNotBlank(smsTemplateMessage.getMediaType())) {
			smsTemplate.setMediaType(smsTemplateMessage.getMediaType());
		} else {
			smsTemplate.setMediaType(null);
		}
		if ("review_request".equalsIgnoreCase(smsTemplate.getType())
				|| "survey_request".equalsIgnoreCase(smsTemplate.getType())
				|| EmailTemplateTypes.CUSTOMER_EXPERIENCE.name().equalsIgnoreCase(smsTemplate.getType())) {
			if (smsTemplateMessage.getIsContactUsCheckEnabled() != null) {
				smsTemplate.setEnableContactUs(smsTemplateMessage.getIsContactUsCheckEnabled());
			}

			if (StringUtils.isNotBlank(smsTemplateMessage.getContactUsMessage())) {
				smsTemplate.setContactUsMessage(smsTemplateMessage.getContactUsMessage());
			}
			if (StringUtils.isNotBlank(smsTemplateMessage.getContactUsButtonText())) {
				smsTemplate.setContactUsButtonText(smsTemplateMessage.getContactUsButtonText());
			}
			if (StringUtils.isNotBlank(smsTemplateMessage.getContactButtonTextColor())) {
				smsTemplate.setContactUsButtonTextColor(smsTemplateMessage.getContactButtonTextColor());
			}
			if (StringUtils.isNotBlank(smsTemplateMessage.getContactButtonColor())) {
				smsTemplate.setContactUsButtonColor(smsTemplateMessage.getContactButtonColor());
			}
			if (StringUtils.isNotBlank(smsTemplateMessage.getRecommendPageHeading())) {
				smsTemplate.setRecommendPageHeading(smsTemplateMessage.getRecommendPageHeading());
			}
			if (StringUtils.isNotBlank(smsTemplateMessage.getRecommendPageMessage())) {
				smsTemplate.setRecommendPageMessage(smsTemplateMessage.getRecommendPageMessage());
			}
		}
		
		// set mediaurl List for custom template
		if (smsTemplate.getType().equalsIgnoreCase(TemplateTypeEnum.PROMOTION_SMS.getName())) {
			if (CollectionUtils.isNotEmpty(smsTemplateMessage.getMediaUrls())) {
				smsTemplate.setMediaUrls(String.join(",", smsTemplateMessage.getMediaUrls()));
				smsTemplate.setMmsEnabled(1);
				smsTemplate.setMessageBody(smsTemplateMessage.getMessageBody());
			} else {
				smsTemplate.setMmsEnabled(0);
				smsTemplate.setMediaUrls(null);
			}
			if (StringUtils.isNotBlank(smsTemplateMessage.getRecommendPageMessage())) {
				smsTemplate.setRecommendPageMessage(smsTemplateMessage.getRecommendPageMessage());
			}
		}
		
		if (smsTemplate.getType().equalsIgnoreCase(TemplateTypeEnum.REFERRAL.getName())) {
			if (CollectionUtils.isNotEmpty(smsTemplateMessage.getMediaUrls())) {
				smsTemplate.setMediaUrls(String.join(",", smsTemplateMessage.getMediaUrls()));
				smsTemplate.setMmsEnabled(1);
			} else {
				smsTemplate.setMmsEnabled(0);
				smsTemplate.setMediaUrls(null);
			}
			smsTemplate.setReferralContactOptionEnabled(smsTemplateMessage.getReferralContactOptionEnabled());
			smsTemplate.setReferralQuestion(smsTemplateMessage.getReferralQuestion());
			smsTemplate.setReferralOptions(CollectionUtils.isNotEmpty(smsTemplateMessage.getReferralOptions()) ? 
					String.join(Constants.REFERRAL_OPTIONS_DELIMITER, smsTemplateMessage.getReferralOptions()) : null); // keeping uncommon separator to avoid its matching probability.
		}
		
		//new fields
		smsTemplate.setEnableFeedbackMessage(smsTemplateMessage.getEnableFeedbackMessage());
		smsTemplate.setFeedbackCallbackMessage(smsTemplateMessage.getFeedbackCallbackMessage());
		smsTemplate.setEnableFeedbackCheckbox(smsTemplateMessage.getEnableFeedbackCheckbox());
		smsTemplate.setThankyouMessage(smsTemplateMessage.getThankyouMessage());
		smsTemplate.setThankyouHeading(smsTemplateMessage.getThankyouHeading());
		smsTemplate.setReviewSiteButtonColor(smsTemplateMessage.getReviewSiteButtonColor());
		smsTemplate.setReviewSiteButtonTextColor(smsTemplateMessage.getReviewSiteButtonTextColor());
		smsTemplate.setReviewEnable(smsTemplateMessage.getReviewEnable());
		smsTemplate.setLocationBrandingEnabled(smsTemplateMessage.getLocationBrandingEnabled());
		smsTemplate.setReferralMessage(smsTemplateMessage.getReferralMessage());
		smsTemplate.setRecommendPageHeading(smsTemplateMessage.getRecommendPageHeading());
		smsTemplate.setRecommendPageMessage(smsTemplateMessage.getRecommendPageMessage());
		
		if (StringUtils.equalsIgnoreCase(smsTemplate.getType(), EmailTemplateTypes.APPOINTMENT_REMINDER.name())) {
			smsTemplate.setConfirmButtonEnabled(Optional.ofNullable(smsTemplateMessage.getConfirmButtonEnabled()).orElse(0));
		    smsTemplate.setRescheduleButtonEnabled(Optional.ofNullable(smsTemplateMessage.getRescheduleButtonEnabled()).orElse(0));
		    smsTemplate.setCancelButtonEnabled(Optional.ofNullable(smsTemplateMessage.getCancelButtonEnabled()).orElse(0));

		}
		if (StringUtils.equalsIgnoreCase(smsTemplate.getType(), EmailTemplateTypes.APPOINTMENT_RECALL.name())) {
			smsTemplate.setBookAppointmentButtonEnabled(Optional.ofNullable(smsTemplateMessage.getBookAppointmentButtonEnabled()).orElse(0));
		}
		
		if (smsTemplate.getType().equalsIgnoreCase(TemplateTypeEnum.APPOINTMENT_FORM_SMS.getName())) {
				smsTemplate.setFormUrl(StringUtils.trim(smsTemplateMessage.getFormUrl()));
			if (CollectionUtils.isNotEmpty(smsTemplateMessage.getMediaUrls())) {
				smsTemplate.setMediaUrls(String.join(",", smsTemplateMessage.getMediaUrls()));
				smsTemplate.setMmsEnabled(1);
			} else {
				smsTemplate.setMmsEnabled(0);
				smsTemplate.setMediaUrls(null);
			}
		}
		
		smsTemplate.setLocationLevelTemplate(smsTemplateMessage.getLocationLevelTemplate());
		
	}
	
	/**
	 * returns true if template needs to be created
	 *
	 * @param templateId
	 * @return
	 */
	
	public static boolean isNewTemplate(Integer templateId) {
		return StringUtils.equalsAnyIgnoreCase(String.valueOf(templateId), "0", "null");
	}

}
