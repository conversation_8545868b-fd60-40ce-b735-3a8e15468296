package com.birdeye.campaign.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.birdeye.campaign.constant.Constants;
import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.exception.InValidTimeZoneException;

public class DateTimeUtils {
	private static final String					PACIFIC_STANDARD_TIME					= "Pacific Standard Time";
	private static final String					CENTRAL_STANDARD_TIME					= "Central Standard Time";
	private static final String					EASTERN_STANDARD_TIME					= "Eastern Standard Time";
	private static final String					MOUNTAIN_STANDARD_TIME					= "Mountain Standard Time";
	private static final String					HAWAII_STANDARD_TIME					= "Hawaii Standard Time";
	private static final String					AUSTRALIAN_EASTERN_STANDARD_TIME		= "Australian Eastern Standard Time";
	private static final String					AUSTRALIAN_CENTRAL_STANDARD_TIME		= "Australian Central Standard Time";
	
	private static final String					PACIFIC_STANDARD_TIME_ZONE				= "US/Pacific";							// "America/Los_Angeles";
	private static final String					CENTRAL_STANDARD_TIME_ZONE				= "US/Central";							// America/Chicago
	private static final String					EASTERN_STANDARD_TIME_ZONE				= "US/Eastern";							// America/New_York
	private static final String					MOUNTAIN_STANDARD_TIME_ZONE				= "US/Mountain";						// America/Denver America/Shiprock, Navajo
	private static final String					HAWAII_STANDARD_TIME_ZONE				= "US/Hawaii";							// Pacific/Honolulu Pacific/Johnston
	private static final String					AUSTRALIAN_EASTERN_STANDARD_TIME_ZONE	= "Australia/Sydney";					// Australia/Sydney
	private static final String					AUSTRALIAN_CENTRAL_STANDARD_ZONE_ID		= "Australia/Adelaide";					// South Australia
	
	private static final Map<String, String>	zoneNameTozoneId						= new HashMap<>();
	private static final Map<String, String>	zoneIdToName							= new HashMap<>();
	
	private static int							dndStartHr								= 8;
	
	private static int							dndEndHr								= 20;
	private static final String					GOOGLE_CALENDAR_DATE_FORMAT				= "yyyyMMdd'T'HHmmss'Z'";
	private static final String					OUTLOOK_CALENDAR_DATE_FORMAT			= "yyyy-MM-dd'T'HH: mm:ss";
	
	private static final int					TOTAL_HRS_IN_DAY							= 24;
	
	private static final String			CAMPAIGN_AUDIENCE_DATE_FORMAT		= "MMM dd, yyyy";
	private static final String			CAMPAIGN_AUDIENCE_DATE_TIME_FORMAT	= "MMM dd, yyyy hh:mm a z";
	

	private static final Logger			logger	= LoggerFactory.getLogger(DateTimeUtils.class);

	
	private DateTimeUtils() {
		
	}
	
	public static int getDndStartHr() {
		return dndStartHr;
	}
	
	public static void setDndStartHr(int dndStartHr) {
		DateTimeUtils.dndStartHr = dndStartHr;
	}
	
	public static int getDndEndHr() {
		return dndEndHr;
	}
	
	public static void setDndEndHr(int dndEndHr) {
		DateTimeUtils.dndEndHr = dndEndHr;
	}
	
	static {
		// loading the available time zones id
		for (String availZoneId : ZoneId.getAvailableZoneIds()) {
			// hack as part of British Summer Time for JIRA BIRDEYE-68827
			if ("Europe/London".equalsIgnoreCase(availZoneId)) {
				zoneNameTozoneId.put("British Summer Time".toLowerCase(), availZoneId);
				zoneIdToName.put(availZoneId, "British Summer Time".toLowerCase());
			} else {
				zoneNameTozoneId.put(TimeZone.getTimeZone(availZoneId).getDisplayName().toLowerCase(), availZoneId);
				zoneIdToName.put(availZoneId, TimeZone.getTimeZone(availZoneId).getDisplayName().toLowerCase());
				// only adding the daylight saving applicable timeZone for same time zone.
				ZonedDateTime currentTime = ZonedDateTime.now(ZoneId.of(availZoneId));
				boolean isDST = currentTime.getZone().getRules().isDaylightSavings(currentTime.toInstant());
				if (isDST) {
					zoneNameTozoneId.put(TimeZone.getTimeZone(availZoneId).getDisplayName().toLowerCase(), availZoneId);
				}
			}
			
		}
		overridePopularUSTimeZones();
	}
	
	/**
	 * Refer - https://en.wikipedia.org/wiki/List_of_tz_database_time_zones
	 */
	private static void overridePopularUSTimeZones() {
		zoneNameTozoneId.put(PACIFIC_STANDARD_TIME.toLowerCase(), PACIFIC_STANDARD_TIME_ZONE);
		zoneIdToName.put(PACIFIC_STANDARD_TIME_ZONE, PACIFIC_STANDARD_TIME.toLowerCase());
		
		zoneNameTozoneId.put(CENTRAL_STANDARD_TIME.toLowerCase(), CENTRAL_STANDARD_TIME_ZONE);
		zoneIdToName.put(CENTRAL_STANDARD_TIME_ZONE, CENTRAL_STANDARD_TIME.toLowerCase());
		
		zoneNameTozoneId.put(EASTERN_STANDARD_TIME.toLowerCase(), EASTERN_STANDARD_TIME_ZONE);
		zoneIdToName.put(EASTERN_STANDARD_TIME_ZONE, EASTERN_STANDARD_TIME.toLowerCase());
		
		zoneNameTozoneId.put(MOUNTAIN_STANDARD_TIME.toLowerCase(), MOUNTAIN_STANDARD_TIME_ZONE);
		zoneIdToName.put(MOUNTAIN_STANDARD_TIME_ZONE, MOUNTAIN_STANDARD_TIME.toLowerCase());
		
		zoneNameTozoneId.put(HAWAII_STANDARD_TIME.toLowerCase(), HAWAII_STANDARD_TIME_ZONE);
		zoneIdToName.put(HAWAII_STANDARD_TIME_ZONE, HAWAII_STANDARD_TIME.toLowerCase());
		
		zoneNameTozoneId.put(AUSTRALIAN_EASTERN_STANDARD_TIME.toLowerCase(), AUSTRALIAN_EASTERN_STANDARD_TIME_ZONE);
		zoneIdToName.put(AUSTRALIAN_EASTERN_STANDARD_TIME_ZONE, AUSTRALIAN_EASTERN_STANDARD_TIME.toLowerCase());
		
		zoneNameTozoneId.put(AUSTRALIAN_CENTRAL_STANDARD_TIME.toLowerCase(), AUSTRALIAN_CENTRAL_STANDARD_ZONE_ID);
		zoneIdToName.put(AUSTRALIAN_CENTRAL_STANDARD_ZONE_ID, AUSTRALIAN_CENTRAL_STANDARD_TIME.toLowerCase());
	}
	
	public static int calculateSmsDNDRemainingTimeForATimeZone(String timeZone) {
		// convert the time zone
		timeZone = convertToProperTimeZone(timeZone);
		// get the timezone id
		ZoneId zoneId = getTimeZoneId(timeZone);
		ZonedDateTime currentZonedTime = ZonedDateTime.now(zoneId);
		// we consider the time zone for 24 Hour
		// current hr is in between 0 to 7
		if (currentZonedTime.getHour() < dndStartHr) {
			return dndStartHr - currentZonedTime.getHour();
		}
		// current hr is in between 20 to 23.59
		else if (currentZonedTime.getHour() > (dndEndHr - 1)) {
			return TOTAL_HRS_IN_DAY + dndStartHr - currentZonedTime.getHour();
		}
		return 0;
	}
	
	// BIRDEYEV2-11785 | Support of timezoneId in place of timezone
	public static int calculateSmsDNDRemainingTimeForATimeZoneId(String timeZoneId) {
		
		ZoneId zoneId = getZoneIdFromTimeZoneId(timeZoneId);
		
		ZonedDateTime currentZonedTime = ZonedDateTime.now(zoneId);
		// we consider the time zone for 24 Hour
		// current hr is in between 0 to 7
		if (currentZonedTime.getHour() < dndStartHr) {
			return dndStartHr - currentZonedTime.getHour();
		}
		// current hr is in between 20 to 23.59
		else if (currentZonedTime.getHour() > (dndEndHr - 1)) {
			return TOTAL_HRS_IN_DAY + dndStartHr - currentZonedTime.getHour();
		}
		return 0;
	}
	
	public static ZonedDateTime calculateSmsDNDApplicableTimeForATimeZone(String timeZoneId, int applicableHrForDND) {
		// convert the time zone
		// timeZone = convertToProperTimeZone(timeZone);
		// get the timezone id
		ZoneId zoneId = getZoneIdFromTimeZoneId(timeZoneId);
		return ZonedDateTime.now(zoneId).plusHours(applicableHrForDND);
	}
	
	public static Date getCorrectSmsScheduledDateAfterApplyDNDForATimeZone(String timeZoneId, int applicableHrForDND) {
		ZonedDateTime currentDateInBusinessTimeZone = calculateSmsDNDApplicableTimeForATimeZone(timeZoneId, applicableHrForDND);
		ZonedDateTime currentDateInUTCTimeZone = currentDateInBusinessTimeZone.withZoneSameInstant(ZoneId.of("UTC"));
		return Date.from(currentDateInUTCTimeZone.toInstant());
		
	}
	
	public static void validateBusinessTimeZone(String timeZoneId) {
		// convert the time zone
		//  timeZone = convertToProperTimeZone(timeZone);
		// get the timezone id
		getZoneIdFromTimeZoneId(timeZoneId);
	}
	
	public static ZoneId getTimeZoneId(String timeZone) {
		// check for not a valid time zone
		if (zoneNameTozoneId.get(timeZone.toLowerCase()) == null && zoneIdToName.get(timeZone) == null) {
			throw new InValidTimeZoneException(ErrorCodes.INVALID_TIME_ZONE_ERROR, "Text failed due to invalid business timezone [" + timeZone + "].");
		}
		String zoneId = zoneNameTozoneId.get(timeZone.toLowerCase()) != null ? zoneNameTozoneId.get(timeZone.toLowerCase()) : zoneNameTozoneId.get(zoneIdToName.get(timeZone));
		try {
			return ZoneId.of(zoneId);
		} catch (Exception exe) {
			throw new InValidTimeZoneException(ErrorCodes.INVALID_TIME_ZONE_ERROR, "Text failed due to invalid business timezone [" + timeZone + "].");
		}
	}
	
	public static ZoneId getZoneIdFromTimeZoneId(String timeZoneId) {
		try {
			return StringUtils.isNotBlank(timeZoneId) ? ZoneId.of(timeZoneId) : ZoneId.of(Constants.AMERICA_LOS_ANGELES);
		} catch (Exception exe) {
			throw new InValidTimeZoneException(ErrorCodes.INVALID_TIME_ZONE_ERROR, "Text failed due to invalid business timezoneId [" + timeZoneId + "].");
		}
	}
	
	public static String convertToProperTimeZone(String inputTimeZone) {
		String convertedTimeZone = null;
		if (StringUtils.isBlank(inputTimeZone) || PACIFIC_STANDARD_TIME.equalsIgnoreCase(inputTimeZone) || "Pacific Daylight Time".equalsIgnoreCase(inputTimeZone)
				|| "PST".equalsIgnoreCase(inputTimeZone) || "PDT".equalsIgnoreCase(inputTimeZone)) {
			convertedTimeZone = PACIFIC_STANDARD_TIME;
		} else if (CENTRAL_STANDARD_TIME.equalsIgnoreCase(inputTimeZone) || "Central Daylight Time".equalsIgnoreCase(inputTimeZone) || "CST".equalsIgnoreCase(inputTimeZone)
				|| "CDT".equalsIgnoreCase(inputTimeZone)) {
			convertedTimeZone = CENTRAL_STANDARD_TIME;
		} else if (EASTERN_STANDARD_TIME.equalsIgnoreCase(inputTimeZone) || "Eastern Daylight Time".equalsIgnoreCase(inputTimeZone) || "EST".equalsIgnoreCase(inputTimeZone)
				|| "EDT".equalsIgnoreCase(inputTimeZone)) {
			convertedTimeZone = EASTERN_STANDARD_TIME;
		} else if (MOUNTAIN_STANDARD_TIME.equalsIgnoreCase(inputTimeZone) || "Mountain Daylight Time".equalsIgnoreCase(inputTimeZone) || "MST".equalsIgnoreCase(inputTimeZone)
				|| "MDT".equalsIgnoreCase(inputTimeZone)) {
			convertedTimeZone = MOUNTAIN_STANDARD_TIME;
		} else if (HAWAII_STANDARD_TIME.equalsIgnoreCase(inputTimeZone) || "Hawaii-Aleutian Standard Time".equalsIgnoreCase(inputTimeZone) || "HAST".equalsIgnoreCase(inputTimeZone)
				|| "HADT".equalsIgnoreCase(inputTimeZone)) {
			convertedTimeZone = HAWAII_STANDARD_TIME;
		} else if (AUSTRALIAN_EASTERN_STANDARD_TIME.equalsIgnoreCase(inputTimeZone) || "Australian Eastern Daylight Time".equalsIgnoreCase(inputTimeZone) || "AEST".equalsIgnoreCase(inputTimeZone)
				|| "AEDT".equalsIgnoreCase(inputTimeZone) || "AET".equalsIgnoreCase(inputTimeZone)) {
			convertedTimeZone = AUSTRALIAN_EASTERN_STANDARD_TIME;
		} else if (AUSTRALIAN_CENTRAL_STANDARD_TIME.equalsIgnoreCase(inputTimeZone) || "Australian Central Daylight Time".equalsIgnoreCase(inputTimeZone) || "ACST".equalsIgnoreCase(inputTimeZone)
				|| "ACDT".equalsIgnoreCase(inputTimeZone)) {
			convertedTimeZone = AUSTRALIAN_CENTRAL_STANDARD_TIME;
		} else {
			// other Daylight can be handle as it is
			if (inputTimeZone.contains("Daylight")) {
				convertedTimeZone = StringUtils.replace(inputTimeZone, "Daylight", "Standard");
			} else {
				convertedTimeZone = inputTimeZone;
			}
		}
		return convertedTimeZone;
	}
	
	public static long getCurrentUTCTimeInEpochsMillis() {
		return ZonedDateTime.ofInstant(Instant.now(), ZoneId.of("UTC")).toInstant().toEpochMilli();
	}
	
	public static Date convertStringToDate(String date) {
		try {
			return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(date);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}
	
	// Test Method
	//public static void main(String[] args) {
		// test EST time
		// ZoneId utcZoneId = ZoneId.of("UTC");
		// TimeZone.setDefault(TimeZone.getTimeZone(utcZoneId));
		// String timeZone="Eastern Standard Time";
		// LocalDateTime estDayLightSavingTime = LocalDateTime.now();
		// ZonedDateTime utcZoneDateTime=estDayLightSavingTime.atZone(utcZoneId);
		// ZonedDateTime estZoneDateTime=utcZoneDateTime.withZoneSameInstant(ZoneId.of(zoneNameTozoneId.get(timeZone.toLowerCase())));
		// System.out.println("UTC :: "+utcZoneDateTime+" EST ::"+estZoneDateTime+" SMS Schedule Time in UTC ::
		// "+getCorrectSmsScheduledDateAfterApplyDNDForATimeZone(timeZone, calculateSmsDNDRemainingTimeForATimeZone(timeZone)));
		// // test CST time
		// timeZone="Central Standard Time";
		// ZonedDateTime cstZoneDateTime=utcZoneDateTime.withZoneSameInstant(ZoneId.of(zoneNameTozoneId.get(timeZone.toLowerCase())));
		// System.out.println("UTC :: "+utcZoneDateTime+" CST ::"+cstZoneDateTime+" SMS Schedule Time in UTC ::
		// "+getCorrectSmsScheduledDateAfterApplyDNDForATimeZone(timeZone, calculateSmsDNDRemainingTimeForATimeZone(timeZone)));
		//
		// // test MST time
		//
		// timeZone="Mountain Standard Time";
		// ZonedDateTime mstZoneDateTime=utcZoneDateTime.withZoneSameInstant(ZoneId.of(zoneNameTozoneId.get(timeZone.toLowerCase())));
		// System.out.println("UTC :: "+utcZoneDateTime+" MST ::"+mstZoneDateTime+" SMS Schedule Time in UTC ::
		// "+getCorrectSmsScheduledDateAfterApplyDNDForATimeZone(timeZone, calculateSmsDNDRemainingTimeForATimeZone(timeZone)));
		//
		// //Pacific Standard Time
		//
		// timeZone=PACIFIC_STANDARD_TIME;
		// ZonedDateTime pstZoneDateTime=utcZoneDateTime.withZoneSameInstant(ZoneId.of(zoneNameTozoneId.get(timeZone.toLowerCase())));
		// System.out.println("UTC :: "+utcZoneDateTime+" PST ::"+pstZoneDateTime+" SMS Schedule Time in UTC ::
		// "+getCorrectSmsScheduledDateAfterApplyDNDForATimeZone(timeZone, calculateSmsDNDRemainingTimeForATimeZone(timeZone)));
		//
		// //Greenwich Mean Time
		// timeZone="Greenwich Mean Time";
		// ZonedDateTime greenZoneDateTime=utcZoneDateTime.withZoneSameInstant(ZoneId.of(zoneNameTozoneId.get(timeZone.toLowerCase())));
		// System.out.println("UTC :: "+utcZoneDateTime+" GMT ::"+greenZoneDateTime+" SMS Schedule Time in UTC ::
		// "+getCorrectSmsScheduledDateAfterApplyDNDForATimeZone(timeZone, calculateSmsDNDRemainingTimeForATimeZone(timeZone)));
		//
		// //Alaska Standard Time
		// timeZone="Alaska Standard Time";
		// ZonedDateTime alaskaZoneDateTime=utcZoneDateTime.withZoneSameInstant(ZoneId.of(zoneNameTozoneId.get(timeZone.toLowerCase())));
		// System.out.println("UTC :: "+utcZoneDateTime+" AKST ::"+alaskaZoneDateTime+" SMS Schedule Time in UTC ::
		// "+getCorrectSmsScheduledDateAfterApplyDNDForATimeZone(timeZone, calculateSmsDNDRemainingTimeForATimeZone(timeZone)));
		//
		// //Hawaii Standard Time
		// timeZone="Hawaii Standard Time";
		// ZonedDateTime hawaiiZoneDateTime=utcZoneDateTime.withZoneSameInstant(ZoneId.of(zoneNameTozoneId.get(timeZone.toLowerCase())));
		// System.out.println("UTC :: "+utcZoneDateTime+" HST ::"+hawaiiZoneDateTime+" SMS Schedule Time in UTC ::
		// "+getCorrectSmsScheduledDateAfterApplyDNDForATimeZone(timeZone, calculateSmsDNDRemainingTimeForATimeZone(timeZone)));
		// //Hawaii Standard Time
		// timeZone="Hawaii-Aleutian Standard Time";
		// ZonedDateTime
		// hawaiiAlZoneDateTime=utcZoneDateTime.withZoneSameInstant(ZoneId.of(zoneNameTozoneId.get(convertToProperTimeZone(timeZone.toLowerCase()).toLowerCase())));
		// System.out.println("UTC :: "+utcZoneDateTime+" HST ::"+hawaiiAlZoneDateTime+" SMS Schedule Time in UTC ::
		// "+getCorrectSmsScheduledDateAfterApplyDNDForATimeZone(timeZone, calculateSmsDNDRemainingTimeForATimeZone(timeZone)));
		//
		// // British Summer Time
		// timeZone="British Summer Time";
		// ZonedDateTime
		// britishZoneDateTime=utcZoneDateTime.withZoneSameInstant(ZoneId.of(zoneNameTozoneId.get(convertToProperTimeZone(timeZone.toLowerCase()).toLowerCase())));
		// System.out.println("UTC :: "+utcZoneDateTime+" BST ::"+britishZoneDateTime+" SMS Schedule Time in UTC ::
		// "+getCorrectSmsScheduledDateAfterApplyDNDForATimeZone(timeZone, calculateSmsDNDRemainingTimeForATimeZone(timeZone)));
		//
		// //Test currentTimeInEpochs
		// System.out.println("Epochs in millis" + getCurrentUTCTimeInEpochsMillis());
		// Date d1=new Date(2021,10,1);
		// Date d2=new Date(2021,9,7);
		//
		// System.out.println("New date is " + addDaysToDate(d2,40));
		// System.out.println(" diffenrence in dates is "+ getDatesDifferenceInDays(d1, d2));
//		System.out.println(getShortFormOfBusinessTimezone("US/Central", isDaylightSaving(new Date(1667452781000l))));
//		
//		System.out.println(isDaylightSaving(new Date(1641187181000l)));
//		
//		long time = getEpochTime("2023-01-04 13:18:11","yyyy-MM-dd HH:mm:ss");
//		System.out.println(time);
//		String pst = prepareFormattedDate(time, "MM/dd/yyyy HH:mm:ss", "America/Los_Angeles");
//		System.out.println(pst);
//
//
//	}
	
	public static Date addDaysToDate(Date date) {
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		c.add(Calendar.DATE, 1);
		return c.getTime();
	}
	
	public static Date addDaysToDate(Date date, Integer numberOfDays) {
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		c.add(Calendar.DATE, numberOfDays);
		return c.getTime();
	}
	
	public static LocalDate getLocalDate(Date date) {
		return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
	}
	
	public static Date addMinutesToDate(Date date) {
		Calendar calender = Calendar.getInstance();
		calender.setTime(date);
		calender.add(Calendar.MINUTE, 30);
		return calender.getTime();
	}
	
	/**
	 * Return 1 index quarter
	 * e.g. Jan-Mar is 1
	 */
	public static int getQuarter(Calendar cal) {
		return (cal.get(Calendar.MONTH) / 3) + 1;
	}

	public static Calendar convertToTimeZone(Date date, String timeZoneId) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
		return convertToTimeZone(cal, timeZoneId);
    }
	
	public static Calendar convertToTimeZone(Calendar calendar, String timeZoneId) {
        TimeZone fromTimeZone = calendar.getTimeZone();
        TimeZone toTimeZone = TimeZone.getTimeZone(timeZoneId);
        calendar.setTimeZone(fromTimeZone);
        calendar.add(Calendar.MILLISECOND, fromTimeZone.getRawOffset() * -1);
        if (fromTimeZone.inDaylightTime(calendar.getTime())) {
            calendar.add(Calendar.MILLISECOND, calendar.getTimeZone().getDSTSavings() * -1);
        }

        calendar.add(Calendar.MILLISECOND, toTimeZone.getRawOffset());
        if (toTimeZone.inDaylightTime(calendar.getTime())) {
            calendar.add(Calendar.MILLISECOND, toTimeZone.getDSTSavings());
        }
        return calendar;
    }
	
	public static Integer getDatesDifferenceInDays(Date d1, Date d2) {
		Long differenceInTIme = Math.abs(d2.getTime() - d1.getTime());
		long differenceInDays = TimeUnit.MILLISECONDS.toDays(differenceInTIme);
		return (int) (long) differenceInDays;
	}

	/**
	 * return formatted date for google calendar
	 * @param time
	 * @return
	 */
	public static String getGoogleCalenderDate(Long time) {
		SimpleDateFormat date_format = new SimpleDateFormat(GOOGLE_CALENDAR_DATE_FORMAT);
		return date_format.format(new Date(time));
	}
	/**
	 * 
	 * @param time
	 * @return
	 * return formatted date for outlook calendar  
	 */
	
	public static String getOutlookCalenderDate(Long time) {
		SimpleDateFormat date_format = new SimpleDateFormat(OUTLOOK_CALENDAR_DATE_FORMAT);
		return date_format.format(new Date(time));
	}
	
	
	
	
	public static String getCustomDateFromEpoch(Long time, String dateFormat) {
		SimpleDateFormat dateTime = new SimpleDateFormat(dateFormat);
		return dateTime.format(new Date(time));
	}

	private static boolean isSameDay(ZonedDateTime date1, ZonedDateTime date2) {
		return date1.truncatedTo(ChronoUnit.DAYS).equals(date2.truncatedTo(ChronoUnit.DAYS));
	}
	
	public static String getFormattedDateTimeFromEpoch(Long epochTime, String timeZoneId, String dateFormat) {
		Date appointmaentDate = new Date(epochTime);
		//String timeZone = DateTimeUtils.convertToProperTimeZone(timeZoneId);
		// get the timezone id
		ZoneId zoneId = getZoneIdFromTimeZoneId(timeZoneId);
		ZonedDateTime zdt = ZonedDateTime.ofInstant(appointmaentDate.toInstant(), ZoneId.of(zoneId.getId()));
		return DateTimeFormatter.ofPattern(dateFormat).format(zdt);
		
	}
	
	public static String getFormattedDateTimeFromEpoch(Long epochTime, String timeZoneId, String dateFormat, Locale locale) {
		Date appointmentDate = new Date(epochTime);
		//String timeZone = DateTimeUtils.convertToProperTimeZone(timeZoneId);
		// get the timezone id
		ZoneId zoneId = getZoneIdFromTimeZoneId(timeZoneId);
		ZonedDateTime zdt = ZonedDateTime.ofInstant(appointmentDate.toInstant(), ZoneId.of(zoneId.getId()));
		return DateTimeFormatter.ofPattern(dateFormat, locale).format(zdt);
		
	}
	
	public static boolean isSameDay(Long inputEpochTime, String timeZoneId) {
		Date inputEpochDateTime = new Date(inputEpochTime);
		//String timeZone = convertToProperTimeZone(timeZoneId);
		ZoneId zoneId = getZoneIdFromTimeZoneId(timeZoneId);
		ZonedDateTime zonedInputDateTime = ZonedDateTime.ofInstant(inputEpochDateTime.toInstant(), ZoneId.of(zoneId.getId()));
		ZonedDateTime zonedCurrentTime = ZonedDateTime.ofInstant(Instant.ofEpochSecond(Instant.now().getEpochSecond()), ZoneId.of(zoneId.getId()));
		
		return isSameDay(zonedInputDateTime, zonedCurrentTime);
	}
	
	public static String getDateAfterConversion(Long lastActivityTime, String timeZoneId) {
		return prepareFormattedDate(new Date(lastActivityTime), CAMPAIGN_AUDIENCE_DATE_FORMAT, timeZoneId);
	}
	
	public static String getDateTimeAfterConversion(Long lastActivityTime, String timeZoneId) {
		return prepareFormattedDate(new Date(lastActivityTime), CAMPAIGN_AUDIENCE_DATE_TIME_FORMAT, timeZoneId);
	}
	
	public static String prepareFormattedDate(Date date, String format, String timeZoneId) {
		
		String formattedDate = null;
		try {
			ZonedDateTime zdt = ZonedDateTime.ofInstant(date.toInstant(), ZoneId.of(timeZoneId));
			formattedDate = DateTimeFormatter.ofPattern(format).format(zdt);
		} catch (Exception exp) {
			logger.error("prepareFormattedDate: Exception occured while converting date {} to format {} and timezone {}", date, format, timeZoneId, exp);			
		}
		return formattedDate;
	}
	
	public static boolean isDaylightSaving(Date date, String timeZoneId) {
		TimeZone tz = TimeZone.getTimeZone(timeZoneId);
		return tz.inDaylightTime(date);
	}
	
	public static String getShortFormOfBusinessTimezone(String timeZoneId, boolean isDaylightSaving) {
		return TimeZone.getTimeZone(timeZoneId).getDisplayName(isDaylightSaving, TimeZone.SHORT);
	}
	public static void main(String[] args) {
		System.out.println(ZoneId.of(null));
		TimeZone tz = TimeZone.getTimeZone("America/Cancun");
		System.out.println(tz.inDaylightTime(new Date()));
		System.out.println(ZoneId.of("America/Los_Angeles").getDisplayName(TextStyle.FULL, Locale.US));
		System.out.println(ZoneId.of("America/Cancun").getDisplayName(TextStyle.SHORT, Locale.US));
		System.out.println(TimeZone.getTimeZone("US/Pacific").getDisplayName(true, TimeZone.SHORT));
		System.out.println(TimeZone.getTimeZone("US/Pacific").getDisplayName(false, TimeZone.SHORT));
		System.out.println(TimeZone.getTimeZone("America/Cancun").getDisplayName(true, TimeZone.SHORT));
		System.out.println(TimeZone.getTimeZone("America/Cancun").getDisplayName(false, TimeZone.SHORT));
	}
	
	public static String getTimeZneId(String timeZone) {
		ZoneId zoneId = DateTimeUtils.getTimeZoneId(convertToProperTimeZone(timeZone));
		return zoneId.getId();
	}

	public static long getEpochTime(String date, String format) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
		LocalDateTime ldt = LocalDateTime.parse(date,formatter);
		Instant instant = ldt.atZone(ZoneId.of("UTC")).toInstant();	
		return instant.toEpochMilli();
	}
	
	public static String prepareFormattedDate(long epochMillis, String format, String timeZoneId) {
		
		String formattedDate = null;
		try {
			ZonedDateTime zdt = ZonedDateTime.ofInstant(Instant.ofEpochMilli(epochMillis), ZoneId.of(timeZoneId));
			formattedDate = DateTimeFormatter.ofPattern(format).format(zdt);
		} catch (Exception exp) {
			logger.error("prepareFormattedDate: Exception occured while converting date {} to format {} and timezone {}", epochMillis, format, timeZoneId, exp);			
		}
		return formattedDate;
	}

	
	/**
	 * @param date
	 * This method returns true if dateInMs lies in current year of provided timeZone
	 * @param timeZoneId 
	 * @return
	 */
	public static Boolean checkDateInCurrentYear(Long dateInMs, String timeZoneId) {
		Boolean inCurrentYear = null;
		try {
			Date date = new Date(dateInMs);
			//String timeZone = DateTimeUtils.convertToProperTimeZone(timeZoneId);
			// get the timezone id
			ZoneId zoneId = getZoneIdFromTimeZoneId(timeZoneId);
			ZonedDateTime zdt = ZonedDateTime.ofInstant(date.toInstant(), ZoneId.of(zoneId.getId()));
	
			return zdt.getYear() == ZonedDateTime.now(zoneId).getYear();
		} catch (Exception e) {
			logger.error("checkDateInCurrentYear: Exception occured while checking date in current year");
		}
		return inCurrentYear;
	}
	
	/**
	 * 
	 * @param date
	 * @return
	 */
	public static String convertDateToString(String pattern, Date date) {
		SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
		try {
			return dateFormat.format(date);
		} catch (Exception exception) {
			logger.error("Exception occurred while converting date to string {}", ExceptionUtils.getStackTrace(exception));
		}
		return StringUtils.EMPTY;
	}
	
}
