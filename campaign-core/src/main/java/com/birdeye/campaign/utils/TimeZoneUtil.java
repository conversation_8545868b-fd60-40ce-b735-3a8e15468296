package com.birdeye.campaign.utils;

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */



import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class TimeZoneUtil {
    private static final Map<String, String> idByLowerName = new HashMap<>();
    private static final Map<String, String> idByLowerId = new HashMap<>();
    static{
        for(String tzID : TimeZone.getAvailableIDs()){
            TimeZone tz = TimeZone.getTimeZone(tzID);
            idByLowerName.put(tz.getDisplayName().toLowerCase(), tzID);
            idByLowerId.put(tzID.toLowerCase(), tzID);
            System.out.println(tz.getDisplayName());
        }
    }
    
    private TimeZoneUtil(){}
    
    /**
     * This method returns TimeZone ID for given displayName
     * e.g. it returns PST for Pacific Standard Time
     * @param input
     * @return 
     */
    public static String getTimeZoneID(String input){
        String tzID = null;
        if(StringUtils.isNotBlank(input)){
            if (input.contains("Daylight")) {
                input = StringUtils.replace(input, "Daylight", "Standard");
            }
           
            tzID = idByLowerName.get(input.toLowerCase());
            //Could be a case that instead of display name we get ID
            //e.g. America/New_York instead of Eastern Standard Time
            if(StringUtils.isBlank(tzID)){
                tzID = idByLowerId.get(input.toLowerCase());
            }
        }
        if(StringUtils.isBlank(tzID)){
            tzID = "PST";
        }
        return tzID;
    }
    
    public static void main(String[] args) {
		System.out.println(TimeZoneUtil.validTimezone("Australian Eastern Standard Time (victoria)"));
	}
    
    
    public static boolean validTimezone(String input){
        String tzID = null;
        if(StringUtils.isNotBlank(input)){
            if (input.contains("Daylight")) {
                input = StringUtils.replace(input, "Daylight", "Standard");
            }
            tzID = idByLowerName.get(input.toLowerCase());
            //Could be a case that instead of display name we get ID
            //e.g. America/New_York instead of Eastern Standard Time
            if(StringUtils.isBlank(tzID)){
                tzID = idByLowerId.get(input.toLowerCase());
            }
        }
        return StringUtils.isNotBlank(tzID);
    }
    
    public enum TimeZoneParameter {
        TIMEZONENAME("timeZoneName"),
        TIMEZONEID("timeZoneId");

        private final String name;       
        private TimeZoneParameter(String s) {
            name = s;
        }

        @Override
        public String toString() {
           return this.name;
        }
    }
}
