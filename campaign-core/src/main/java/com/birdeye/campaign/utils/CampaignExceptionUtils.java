package com.birdeye.campaign.utils;

import com.birdeye.campaign.constant.ErrorCodes;
import com.birdeye.campaign.exception.CampaignException;

public class CampaignExceptionUtils {
	
	private CampaignExceptionUtils() {
		
	}
	
	public static boolean equals(CampaignException campaignException, ErrorCodes errorCode) {
		if (campaignException == null || errorCode == null) {
			return false;
		}
		return campaignException.getCode().getValue() == errorCode.getValue();
	}
	
	public static boolean isURLShorteningRetriableException(CampaignException campaignException) {
		return campaignException.getCode().getValue() == ErrorCodes.SHORTEN_URL_RETRIABLE_ERROR.getValue();
	}
	
	public static boolean isAppointmentNotFoundException(CampaignException campaignException) {
		return campaignException.getCode().getValue() == ErrorCodes.NO_VALID_APOINTMENT_FOUND.getValue();
	}
	
}
