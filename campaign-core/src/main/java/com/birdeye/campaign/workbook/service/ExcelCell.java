package com.birdeye.campaign.workbook.service;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 
 * <AUTHOR>
 *
 * Marker annotation to mark a field as excel cell
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
public @interface ExcelCell {
	public String header() default "";
	public int order() default Integer.MAX_VALUE;
	public CellType cellType();
}
