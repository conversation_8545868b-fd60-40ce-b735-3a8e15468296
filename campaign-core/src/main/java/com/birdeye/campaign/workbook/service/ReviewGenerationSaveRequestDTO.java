package com.birdeye.campaign.workbook.service;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ReviewGenerationSaveRequestDTO implements Serializable {
	
	private static final long	serialVersionUID	= -1920267908835984220L;
	
	private Integer				sourceId;
	
	private Integer				businessAggregationId;
	
	private String				reviewGenerationUrl;
	
	private Integer				businessId;
	
	private Integer				enterpriseId;
	
	private Integer				resellerId;
	
	private Integer				defaultForTemplate	= 1;					// default value =1 same as in db
	
	private Integer				reviewGenerationSourceId;
	
	private Integer				createdBy;									// user id of created by
	
	private Integer				updatedBy;									// updated by user id
	
	/**
	 * @return the sourceId
	 */
	public Integer getSourceId() {
		return sourceId;
	}
	
	/**
	 * @param sourceId
	 *            the sourceId to set
	 */
	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}
	
	/**
	 * @return the businessAggregationId
	 */
	public Integer getBusinessAggregationId() {
		return businessAggregationId;
	}
	
	/**
	 * @param businessAggregationId
	 *            the businessAggregationId to set
	 */
	public void setBusinessAggregationId(Integer businessAggregationId) {
		this.businessAggregationId = businessAggregationId;
	}
	
	/**
	 * @return the reviewGenerationUrl
	 */
	public String getReviewGenerationUrl() {
		return reviewGenerationUrl;
	}
	
	/**
	 * @param reviewGenerationUrl
	 *            the reviewGenerationUrl to set
	 */
	public void setReviewGenerationUrl(String reviewGenerationUrl) {
		this.reviewGenerationUrl = reviewGenerationUrl;
	}
	
	/**
	 * @return the businessId
	 */
	public Integer getBusinessId() {
		return businessId;
	}
	
	/**
	 * @param businessId
	 *            the businessId to set
	 */
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	/**
	 * @return the enterpriseId
	 */
	public Integer getEnterpriseId() {
		return enterpriseId;
	}
	
	/**
	 * @param enterpriseId
	 *            the enterpriseId to set
	 */
	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	
	/**
	 * @return the resellerId
	 */
	public Integer getResellerId() {
		return resellerId;
	}
	
	/**
	 * @param resellerId
	 *            the resellerId to set
	 */
	public void setResellerId(Integer resellerId) {
		this.resellerId = resellerId;
	}
	
	/**
	 * @return the defaultForTemplate
	 */
	public Integer getDefaultForTemplate() {
		return defaultForTemplate;
	}
	
	/**
	 * @param defaultForTemplate
	 *            the defaultForTemplate to set
	 */
	public void setDefaultForTemplate(Integer defaultForTemplate) {
		this.defaultForTemplate = defaultForTemplate;
	}

	/**
	 * @return the reviewGenerationSourceId
	 */
	public Integer getReviewGenerationSourceId() {
		return reviewGenerationSourceId;
	}

	/**
	 * @param reviewGenerationSourceId the reviewGenerationSourceId to set
	 */
	public void setReviewGenerationSourceId(Integer reviewGenerationSourceId) {
		this.reviewGenerationSourceId = reviewGenerationSourceId;
	}

	/**
	 * @return the createdBy
	 */
	public Integer getCreatedBy() {
		return createdBy;
	}

	/**
	 * @param createdBy the createdBy to set
	 */
	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	/**
	 * @return the updatedBy
	 */
	public Integer getUpdatedBy() {
		return updatedBy;
	}

	/**
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}
	

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
	}
	
}
