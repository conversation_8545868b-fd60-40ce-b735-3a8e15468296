package com.birdeye.campaign.workbook.service;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.birdeye.campaign.utils.CoreUtils;

/**
 * Utility to create complete excel files
 * <AUTHOR>
 *
 */
public class WorkbookUtil {
	
	
	private static Comparator<CellData> orderComparator = (CellData c1, CellData c2)->c1.getOrder() - c2.getOrder();
	
	private static Comparator<HeaderCell> headerComparator = (HeaderCell c1, HeaderCell c2)->c1.getOrder() - c2.getOrder();

	private WorkbookUtil() {
		
	}

	private static final Logger logger = LoggerFactory.getLogger(WorkbookUtil.class);
	
	/**
	 * Method to create excel file with specified fileName
	 * @param input
	 * @param fileName
	 * @param headerToCustomHeaderMap
	 */
	public static <T> String createExcelFile(List<T> input, String fileName, Map<String, String> headerToCustomHeaderMap){
		logger.info("Received request to create excel with name : {}" , fileName);
		if(StringUtils.isBlank(fileName)){
			throw new IllegalArgumentException("FileName cannot be empty!");
		}
		try {
			WorkbookWriterHelper excelWriter = new WorkbookWriterHelper().initializeHSSFWorkbook(fileName);
	        if(CollectionUtils.isNotEmpty(input)){
	    		List<List<CellData>> data = new ArrayList<>();
	            List<HeaderCell> headers = new ArrayList<>();
	            String sheetName = validateAndPrepareData(input, data, headers,null);
	    		Sheet sheet = excelWriter.createWorkSheet(sheetName);
	            Map<String, Integer> headerToIndexMap = writeHeaders(excelWriter, sheet, headers, headerToCustomHeaderMap , null);
	            writeData(excelWriter, sheet, data, headerToIndexMap , null);
	            autoSizeColumn(sheet, headerToIndexMap.size());
			}
			// write file to given location
			excelWriter.close();
			return excelWriter.getWorkbookFilePath();
		}catch(Exception exception) {
			logger.error("Exception occurred while creating workbook : {}" , fileName , exception);
		}
		return null;
	}
	
	private static <T> String validateAndPrepareData(List<T> input, List<List<CellData>> data, List<HeaderCell> headers,String name) throws IllegalAccessException {
        String sheetName = null;
        if(input!=null){
            for (Object item : input) {
                List<CellData> rowData = new ArrayList<>();
                sheetName = prepareCellData(item, rowData, headers, name);
                data.add(rowData);
            }
        }
        return sheetName;
    }
	
	private static <T> String prepareCellData(T dto, List<CellData> columnList, List<HeaderCell> headers, String name) throws IllegalAccessException {
		Class<?> clazz = dto.getClass();
		String sheetName = null;
		if(clazz.isAnnotationPresent(ExcelData.class)){
			sheetName = StringUtils.isNotBlank(name) ? name: clazz.getAnnotation((ExcelData.class)).name();
			for(Field f: CoreUtils.getAllFields(clazz)){
				f.setAccessible(true);
				if(f.isAnnotationPresent(ExcelCell.class)){
					ExcelCell cell = f.getAnnotation(ExcelCell.class);
					CellType cellType = CellType.valueOf(cell.cellType().name());
					CellData c = new CellData(cell.order(), cell.header(), f.get(dto), cellType);
					columnList.add(c);
					HeaderCell header = new HeaderCell(cell.order(), cell.header());
					if(!headers.contains(header)){
						headers.add(header);
					}
				} else if(f.isAnnotationPresent(DynamicExcelCell.class)) {
					DynamicExcelCell cellGroup = f.getAnnotation(DynamicExcelCell.class);
					if(f.getType().isAssignableFrom(List.class)){
						List<?> cells = (List<?>) f.get(dto);
						if(CollectionUtils.isNotEmpty(cells) && !(cells.get(0) instanceof DynamicCellDto)){
							throw new IllegalArgumentException("Dynamic cells must be of type : "+DynamicCellDto.class.getCanonicalName());
						}
						if(CollectionUtils.isNotEmpty(cells)){
							for (Object obj : cells) {
								DynamicCellDto cell = (DynamicCellDto)obj;
								processDynamicCell(columnList, headers, cellGroup, cell);
							}
						}
					}else if(f.getType().isAssignableFrom(DynamicCellDto.class)){
						DynamicCellDto cell = (DynamicCellDto) f.get(dto);
						if(cell != null){
							processDynamicCell(columnList, headers, cellGroup, cell);
						}
					}
				}
			}
			Collections.sort(columnList, orderComparator);
			Collections.sort(headers, headerComparator);
		}else{
			throw new IllegalArgumentException("Provided data class must be annotated with @ExcelData annotation - "+clazz.getName());
		}
		return sheetName;
	}
	
	private static void processDynamicCell(List<CellData> columnList, List<HeaderCell> headers,
			DynamicExcelCell cellGroup, DynamicCellDto cell) {
		CellType cellType = CellType.valueOf(cell.getCellType().name());
		CellData cellData = new CellData(cellGroup.order(), cell.getHeader(), cell.getValue(), cellType);
		columnList.add(cellData);
		HeaderCell header = new HeaderCell(cellGroup.order(), cell.getHeader());
		if(!headers.contains(header)){
			headers.add(header);
		}
	}
	
	private static Map<String, Integer> writeHeaders(WorkbookWriterHelper workbookWriter, Sheet sheet, List<HeaderCell> headers, Map<String, String> headerToCustomHeaderMap , Integer rowIndex) {
    	if(rowIndex == null) {
    		rowIndex = 0;
    	}
        Row headerRow = sheet.createRow(rowIndex);
        int colIndex = 0;
        Map<String, Integer> headerToIndexMap = new HashMap<>();
        for(HeaderCell header : headers){
        	workbookWriter.createHeaderStyleFont();
            sheet.autoSizeColumn(colIndex);
            String headerName = header.getLabel();
            if (headerToCustomHeaderMap != null && headerToCustomHeaderMap.containsKey(headerName)) {
            	headerName = headerToCustomHeaderMap.get(headerName);
            }
            headerToIndexMap.put(header.getLabel() + header.getOrder(), colIndex);
            workbookWriter.createHdrCell(headerRow, colIndex++, headerName);  
        }
        return headerToIndexMap;
    }
    
    private static int writeData(WorkbookWriterHelper workbookWriter, Sheet sheet, List<List<CellData>> data, Map<String, Integer> headerToIndexMap, Integer rowIndex) {
    	if(rowIndex == null) {
    		rowIndex = 1;
    	}
        logger.info("Writing Excel data");
        for(List<CellData> rowData : data){
            Row row = sheet.createRow(rowIndex++);
            for(CellData cellData : rowData){
            	int headerIndex = headerToIndexMap.get(cellData.getHeader() + cellData.getOrder());
            	workbookWriter.writeDataCell(row, headerIndex, cellData);
            }
        }
        return rowIndex;
    }
    
    private static void autoSizeColumn(Sheet sheet, int size) {
		for(int i = 0; i < size; i++){
			sheet.autoSizeColumn(i);
		}
	}
    
	public static interface RowMapper<T> {
		T mapRow(List<String> cells);
	}
    
    /**
     * Excel Utility to read file.
     * @param fis
     * @return
     */
	public static <T> Map<Integer, T> readXSSFWorkbook(InputStream fis,RowMapper<T> rowMapper) throws IOException {
		XSSFWorkbook workbook = null;
		Map<Integer, T> data = new HashMap<>();
		try {
			workbook = new XSSFWorkbook(fis);
			XSSFSheet sheet = workbook.getSheetAt(0);
			// Process sheet.
			for (int i = sheet.getFirstRowNum(); i <= sheet.getLastRowNum(); i++) {
				XSSFRow row = sheet.getRow(i);
				if (row != null) {
					List<String> cells = new ArrayList<>();
					//Row processing
					for (int j = 0; j < row.getLastCellNum(); j++) {
						XSSFCell cell = row.getCell(j);
						if (cell != null) {
							cells.add(readCellContent(cell));
						} else {
							cells.add(null);
						}
					}
					data.put(i,rowMapper.mapRow(cells));
				}
			}
		} finally {
			if (workbook != null) {
				workbook.close();
			}
		}
		return data;
	}

	private static String readCellContent(Cell cell) {
		String content;
		switch (cell.getCellType()) {
		case STRING:
			content = cell.getStringCellValue();
			break;
		case NUMERIC:
			if (DateUtil.isCellDateFormatted(cell)) {
				content = cell.getDateCellValue() + "";
			} else {
				content = cell.getNumericCellValue() + "";
			}
			break;
		case BOOLEAN:
			content = cell.getBooleanCellValue() + "";
			break;
		case FORMULA:
			content = cell.getCellFormula() + "";
			break;
		default:
			content = "";
		}
		return content;
	}
}
