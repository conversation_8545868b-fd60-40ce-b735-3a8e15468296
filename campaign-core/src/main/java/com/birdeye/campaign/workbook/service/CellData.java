package com.birdeye.campaign.workbook.service;

/**
 * Wrapper object representing a single single of workbook
 * <AUTHOR>
 *
 */
public class CellData{
	
	private int order;
	private String header;
	private Object value;
	private CellType type;
	
	public CellData(int order, String header, Object value, CellType type){
		this.order=order;
		this.header=header;
		this.value = value;
		this.type = type;
	}
	
	public Object getValue() {
		return value;
	}

	public void setValue(Object value) {
		this.value = value;
	}

	public int getOrder() {
		return order;
	}
	public void setOrder(int order) {
		this.order = order;
	}
	public String getHeader() {
		return header;
	}
	public void setHeader(String header) {
		this.header = header;
	}

	public CellType getType() {
		return type;
	}

	public void setType(CellType type) {
		this.type = type;
	}

	@Override
	public String toString() {
		return "CellData [order=" + order + ", header=" + header + ", value=" + value + ", type=" + type + "]";
	}
	
}